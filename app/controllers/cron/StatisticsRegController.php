<?php

use Octopus\PdoEx;

includeBase('CronController');

class StatisticsRegController extends CronController
{
    const HOUR_AGO = 0;
    const OFFSET_HOUR = 1;
    const RATE = 0.55;
    const AVG_MAX = 40;

    /**
     * cron 注册报警脚本
     */
    public function actionIndex()
    {
        // 弃用旧版的注册统计数据
        $this->storeOldStatistics();
    }

    /**
     * User: panj
     * 弃用旧版的注册统计数据
     * @return null
     */
    private function storeOldStatistics()
    {
        $redis = RedisEx::getInstance();
        $regSuccNum = intval($redis->get("regSuccNum"));
        $regOAuthSuccNum = intval($redis->get("regOAuthSuccNum"));
        $redis->set("regSuccNum", 0);
        $redis->set("regOAuthSuccNum", 0);
        $domains = array_keys(Config::get("domainServerIPs"));
        $RSN = array();
        $RFN = array();
        $types = array('username', 'phone', 'email');
        $regDetailNums = array();
        foreach ($domains as $domain)
        {
            $rsnKey = "RSN:" . str_replace(".", "_", $domain);
            $rfnKey = "RFN:" . str_replace(".", "_", $domain);
            $RSN[$domain] = intval($redis->get($rsnKey));
            $RFN[$domain] = intval($redis->get($rfnKey));
            $redis->set($rsnKey, 0);
            $redis->set($rfnKey, 0);
            foreach ($types as $type)
            {
                $regDetailNums[$domain]["succ_num_$type"] = intval($redis->get("$rsnKey:$type"));
                $regDetailNums[$domain]["fail_num_$type"] = intval($redis->get("$rfnKey:$type"));
                $redis->set("$rsnKey:$type", 0);
                $redis->set("$rfnKey:$type", 0);
            }
        }
        $redis->del('referDomain:RSN');
    }

}
