<!DOCTYPE HTML>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=gb2312">
        <meta content="IE=edge, chrome=1" http-equiv="X-UA-Compatible" />
        <title>2345帐号登录</title>
        <link href="/browser2/css/sign_20140219.css" rel="stylesheet" type="text/css"/>
        <script type="text/javascript" src="/browser2/js/login.js"></script>
    </head>
    <body scroll="no" style="overflow:hidden;">
        <div class="main">
            <div class="banner"></div>
            <div class="box">
                <div class="popup" id="popup" style="display:none;"></div>
                <ul id="userlist" style="display:none;outline:none;left:140px;" tabindex="999">
                    <a tabindex="-1" href="#" id="delbtn" style="background:url('/browser2/images/sign_delbtn.png') no-repeat;width:11px;height:11px;position:absolute;top:5px;right:8px;display:none;"></a>
                </ul>
                <form action="" method="post" onsubmit="return form_submit();">
                    <input type="hidden" name="cmd" id="cmd"/>
                    <table class="sign" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td align="right" class="txt1" width="140">2345帐号：</td>
                            <td align="left" width="208">
                                <table class="account" width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td width="80%">
                                            <input tabindex="1" type="text" class="input0" id="username" autocomplete="off" value="" onfocus="select();">
                                        </td>
                                        <td width="20%">
                                            <a tabindex="-1" href="#" id="dropbtn" style="background:url('/browser2/images/sign_dropbtn.png') no-repeat;width:26px;height:30px;display:block;"></a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td align="left" width="132">
                                <a tabindex="-1" href="#" onclick="window.external.RCCoralShowRegDlg();return false;">注册新用户</a>
                            </td>
                        </tr>
                        <tr>
                            <td class="h_sign">&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td align="right" class="txt1">密码：</td>
                            <td align="left">
                                <input tabindex="2" class="input1" type="password" id="password" onfocus="select();">
                            </td>
                            <td align="left">
                                <a tabindex="-1" href="#" onclick="window.external.RCCoralOpenUrl('/find?type=password','');return false;">找回密码</a>
                            </td>
                        </tr>
                        <tr>
                            <td class="h_sign">&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr id="tr_yzm" style="display:{{$pageArray.display}};">
                            <td align="right" class="txt1">验证码：</td>
                            <td align="left">
                                <table class="code" width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td width="28%">
                                            <input{{if $pageArray.display==""}} tabindex="3"{{/if}} class="input2" type="text" maxlength="4" id="check_code" onfocus="select();">
                                        </td>
                                        <td width="24%">
                                            <img id="checkImg" src="/check_code.php" onclick="this.src='/check_code.php?'+new Date();">
                                        </td>
                                        <td width="48%">
                                            <a tabindex="-1" href="#" onclick="$('checkImg').src='/check_code.php?'+new Date();return false;">换一张</a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr id="tr_yzm_tr" style="display:{{$pageArray.display}};">
                            <td class="h_sign">&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td align="left">
                                <table class="txt2" width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td>
                                            <label for="remember">
                                                <input tabindex="-1" checked="checked" class="check1" type="checkbox" id="remember" value="1">
                                                记住密码
                                            </label>
                                        </td>
                                        <td>
                                            <label for="automatic">
                                                <input tabindex="-1" checked="checked" class="check1" type="checkbox" id="automatic" value="1">
                                                下次自动登录
                                            </label>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td class="h_sign">&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>
                                <input tabindex="-1" type="submit" value="登录" style="border:none;cursor:pointer" class="sign_btn"/>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td class="syn_small" colspan="3">
                                <a class="qq" href="#" onclick="cc('login_qq');window.external.RCCoralOpenUrl('{{$smarty.const.LOGIN_HOST}}/browser2/qq','');return false;">用QQ帐号登录</a>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
        </div>
        <script type="text/javascript">
            var xhr=null;
            var dhd=true;
            function initXhr(){
                if(window.XMLHttpRequest){
                    try{
                        xhr = new XMLHttpRequest();
                    }catch(e){
                        xhr=false;
                    }
                }else if(window.ActiveXObject){
                    try{
                        xhr = new ActiveXObject("Msxml2.XMLHTTP");
                    }catch(e){
                        try{
                            xhr=new ActiveXObject("Microsoft.XMLHTTP");
                        }catch(e){
                            xhr=false;
                        }
                    }
                }
            }
            initXhr();
            function sendPost(_url,_para,_callBack,_method){
                _callBack = _callBack||function(){};
                _method = _method||"POST";
                if(xhr){
                    xhr.open(_method,_url,true);
                    xhr.onreadystatechange = _callBack;
                    xhr.setRequestHeader("Content-Type","application/x-www-form-urlencoded;");
                    xhr.send(_para);
                }
            }
            function $(id){return document.getElementById(id);}
            function $t(_tag){var _doc = arguments[1]||document;return _doc.getElementsByTagName(_tag);}
            function $c(_tag){return document.createElement(_tag);}
            function auto_login(user){
                cc("auto_login");
                $("cmd").value = "auto";
                $("username").value = user["username"];
                $("password").value = user["password"];
                $("remember").checked = true;
                $("automatic").checked = true;
                form_submit();
            }
            function show_error(msg){
                $("popup").innerHTML = msg;
                $("popup").style.display = "";
            }
            function form_submit(){
                cc("login_all");
                if ($("username").value =="")
                {
                    $("popup").style.display = "";
                    $("popup").innerHTML = "请输入2345帐号!";
                    $("username").focus();
                    return false;
                }
                if ($("password").value =="")
                {
                    $("popup").style.display = "";
                    $("popup").innerHTML = "请先输入密码!";
                    $("password").focus();
                    return false;
                }
                window.external.RCCoralOnLogin($("username").value,"",1,"","","","","");
                sendPost("/browser2/sign","username="+$("username").value+"&password="+MD5($("password").value)+"&check_code="+$("check_code").value,function(){
                    if(xhr.readyState==4&&xhr.status==200&&xhr.responseText!="NaN"){
                        data = eval("("+xhr.responseText+")");
                        switch(data["sts"]){
                            case -3:
                                cc("login_fail");
                                window.external.RCCoralOnLogin($("username").value,"",3,"帐号不存在，区分大小写","","","","");
                                $("popup").style.display = "";
                                $("popup").innerHTML = "帐号不存在，区分大小写";
                                break;
                            case -1:
                                cc("login_fail");
                                window.external.RCCoralOnLogin($("username").value,"",3,"帐号未激活","","","","");
                                window.external.RCCoralOpenUrl("/active_error.html","");
                                break;
                            case 0:
                                cc("login_success");
                                window.external.RCCoralOnLogin($("username").value,"",0,"",data["uid"],data["sec"],data["passid"],data["token"]);
                                if ($("automatic").checked) {
                                    window.external.RCCoralSetLoginUserInfo($("username").value,$("password").value);
                                    window.external.RCCoralSetIsAutoLogin(true);
                                }else{
                                    if ($("remember").checked) {
                                        window.external.RCCoralSetLoginUserInfo($("username").value,$("password").value);
                                    }else{
                                        window.external.RCCoralSetLoginUserInfo($("username").value,"");
                                    }
                                    window.external.RCCoralSetIsAutoLogin(false);
                                }
                                break;
                            case 1:
                                cc("login_fail");
                                window.external.RCCoralOnLogin($("username").value,"",3,"密码错误，密码区分大小写","","","","");
                                if($("cmd").value=="auto"){
                                }else{
                                    $("popup").style.display = "";
                                    $("popup").innerHTML = "密码错误，密码区分大小写";
                                }
                                break;
                            case 3:
                                cc("login_fail");
                                window.external.RCCoralOnLogin($("username").value,"",3,"验证码输入错误","","","","");
                                $("popup").style.display = "";
                                $("popup").innerHTML = "验证码输入错误";
                                break;
                            case 4:
                                cc("login_fail");
                                window.external.RCCoralOnLogin($("username").value,"",3,"验证码不能为空","","","","");
                                $("popup").style.display = "";
                                $("popup").innerHTML = "验证码不能为空";
                                break;
                            case 1004:
                                cc("login_fail");
                                window.external.RCCoralOnLogin($("username").value,"",3,"登录有误，错误代码1004，请联系客服","","","","");
                                $("popup").style.display = "";
                                $("popup").innerHTML = "登录有误，错误代码1004，请联系客服";
                                break;
                            case 1005:
                                cc("login_fail");
                                window.external.RCCoralOnLogin($("username").value,"",3,"登录有误，错误代码1005，请联系客服","","","","");
                                $("popup").style.display = "";
                                $("popup").innerHTML = "登录有误，错误代码1005，请联系客服";
                                break;
                        }
                        if(data["display"]===""){
                            $("tr_yzm").style.display = "";
                            $("tr_yzm_tr").style.display = "";
                            $('checkImg').src='/check_code.php?'+new Date();
                            window.external.RCCoralSetDlgSize(480,403);
                        }else{
                            $("tr_yzm").style.display = "none";
                            $("tr_yzm_tr").style.display = "none";
                        }
                    }
                });
                return false;
            }
            function loadJs(_url){
                var callback = arguments[1]||function(){};
                var _js = document.createElement("script");
                _js.setAttribute("type", "text/javascript");
                _js.setAttribute("src", _url);
                _js.setAttribute("defer", true);
                if(document.all){
                    _js.onreadystatechange = function(){
                        if(this.readyState == "loaded" || this.readyState == 4 || this.readyState == "complete"){
                            setTimeout(callback,10);
                        }
                    };
                }else{
                    _js.onload = function(){
                        callback();
                    };
                }
                document.getElementsByTagName("head")[0].appendChild(_js);
            }
            function cc(a) {
                var b = arguments,
                web = "ajax62",
                a2,
                i1 = document.cookie.indexOf("uUiD="),
                i2;
                if (b.length > 1) web = b[1];
                if (i1 != -1) {
                    i2 = document.cookie.indexOf(";", i1);
                    a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
                }
                if (!a2) {
                    a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                    document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
                }
                if (a.length > 0) {
                    var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                    loadJs(c);
                }
                return true;
            }
            $("username").focus();
            {{if $pageArray.display == 'none'}}
            window.external.RCCoralSetDlgSize(480,353);
            {{else}}
            window.external.RCCoralSetDlgSize(480,403);
            {{/if}}
            var user = window.external.RCCoralGetLastActiveUserInfo();
            if(user!=""){
                user = eval("("+user+")");
                $("username").value = user["username"];
                $("password").value = user["password"];
                if($("password").value!=""){
                    $("remember").checked = true;
                    if(window.external.RCCoralGetIsAutoLogin()){
                        $("automatic").checked = true;
                    }else{
                        $("automatic").checked = false;
                    }
                }else if($("username").value!=""){
                    $("remember").checked = false;
                    $("automatic").checked = false;
                }
            }
            var users=window.external.RCCoralGetLoginAllUserName();
            if(users!=""){
                users = eval(users);
                for(i in users){
                    var li = $c("li");
                    li.innerHTML = users[i];
                    $("userlist").appendChild(li);
                }
            }
            $("automatic").onclick = function(){
                if(this.checked){
                    $("remember").checked = true;
                }
            };
            $("remember").onclick = function(){
                if(!this.checked){
                    $("automatic").checked = false;
                }
            };
            $("userlist").onmouseover = function(e){
                var event, target;
                event = window.event || e;
                target =event.srcElement || event.target;
                if(target.tagName=="LI"){
                    target.className = "name_h";
                    target.appendChild($("delbtn"));
                    $("delbtn").style.display = "";
                }
            };
            $("userlist").onmouseout = function(e){
                var event, target, relatedTarget;
                event = window.event || e;
                target =event.srcElement || event.target;
                relatedTarget = event.toElement || event.relatedTarget;
                if(target.tagName=="LI"){
                    if(document.all){
                        if(!target.contains(relatedTarget)){
                            target.className = "name";
                            $("delbtn").style.display = "none";
                            $("userlist").appendChild($("delbtn"));
                        }
                    }else{
                        var res = target.compareDocumentPosition(relatedTarget);
                        if(res!=20){
                            target.className = "name";
                            $("delbtn").style.display = "none";
                            $("userlist").appendChild($("delbtn"));
                        }
                    }
                }
            };
            $("userlist").onclick = function(e){
                var event, target;
                event = window.event || e;
                target =event.srcElement || event.target;
                if(target.tagName=="LI"){
                    target.className = "name";
                    $("delbtn").style.display = "none";
                    $("userlist").appendChild($("delbtn"));
                    $("username").value = target.innerHTML.replace(/^\s+|\s+$/g, "");
                    $("password").value = window.external.RCCoralGetLoginUserPwd($("username").value);
                    if($("password").value!=''){
                        $("remember").value = true;
                    }else{
                        $("remember").checked = false;
                        $("automatic").checked = false;
                    }
                    $("userlist").style.display = "none";
                }else{
                    $("userlist").style.display = "";
                }
            };
            $("delbtn").onclick = function(){
                target = this.parentNode;
                $("userlist").appendChild(this);
                if(window.external.RCCoralDelUser(target.innerHTML.replace(/^\s+|\s+$/g, ""))){
                    if($("username").value == target.innerHTML.replace(/^\s+|\s+$/g, "")){
                        $("username").value = "";
                        $("password").value = "";
                    }
                    target.className = "name";
                    this.style.display = "none";
                    $("userlist").removeChild(target);
                    if($t("li",$("userlist")).length == 0){
                        $("userlist").style.display = "none";
                    }
                }else{
                    target.appendChild(this);
                }
            };
            $("dropbtn").onclick = function(){
                if($t("li",$("userlist")).length > 0){
                    $("userlist").style.display = "";
                    return false;
                }
            };
			$("username").onkeydown = function(){
				$("password").value = "";
			};
			$("username").onblur = function(){
				dhd = setTimeout(function(){$("userlist").style.display = "none";},200);
			};
			document.onclick = function(e){
				e = window.event || e;
                sE = e.srcElement || e.target;
				if(sE.id!="userlist"&&sE.id!="delbtn"&&sE.id!="username"&&sE.id!="dropbtn"){
					$("userlist").style.display = "none";
				}else{
					clearTimeout(dhd);
				}
			};
        </script>
    </body>
</html>