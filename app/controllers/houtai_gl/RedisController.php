<?php

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:RedisController.php
 * 摘    要: 用户中心redis后台
 * 作    者:<EMAIL>
 * 修改日期: 2015/11/13
 */
includeRedis("RedminController", 'controller/');

class RedisController extends RedminController
{

    private $pageArray, $authName = '查询限制信息';

    /**
     * 构造方法
     */
    public function __construct()
    {
        parent::__construct();
        $this->checkAuth();
        $this->pageArray['redisConfig'] = $this->app->config['database']['redis'];
        $this->pageArray['redisKeys'] = $this->app->config['redisKeys'];
    }

    /**
     * 验证后台是否有权限访问
     * <AUTHOR>
     */
    private function checkAuth()
    {
        if (!\Service\Security\Server::checkCompanyIp(get_client_ip()))
        {
            showError(403, 403);
        }
        $adminModel = loadModel('admin');
        $row = $adminModel->checkAuth($this->authName, $_COOKIE['admin_user']);
        if (!$row)
        {
            showMessage('没有权限访问此页面', 'back');
        }
    }

    /**
     * redis后台展示
     * <AUTHOR>
     */
    public function actionIndex()
    {
        if (isset(RedminApp::instance()->config['timezone']))
        {
            date_default_timezone_set(RedminApp::instance()->config['timezone']);
        }
        RedminRouter::instance()->route();
    }

    /**
     * 查看redis信息
     * <AUTHOR>
     */
    public function actionInfo()
    {
        $info = $this->db->info();
        $uptimeDays = floor($info['uptime_in_seconds'] / 86400);
        $dbSize = $this->db->dbSize();
        $lastSave = $this->db->lastSave();
        $this->pageArray['info'] = $info;
        $this->pageArray['uptimeDays'] = $uptimeDays;
        $this->pageArray['dbSize'] = $dbSize;
        $this->pageArray['lastSave'] = $lastSave;
        $this->pageArray['showKeys'] = true;
        $this->pageArray['redisKeys'] = false;

        loadView("admin/redmin/info.tpl.html", $this->pageArray);
    }

    /**
     * 查看key过期时间
     * @param $key
     * <AUTHOR>
     */
    public function actionExpire($key)
    {
        $updated = Null;
        $oldttl = $this->db->ttl(urldecode($key));

        $this->pageArray['key'] = urldecode($key);
        $this->pageArray['ttl'] = $oldttl;
        loadView("admin/redmin/ttl.tpl.html", $this->pageArray);
    }

    /**
     * key值展示页
     * @param $key
     * @param int $page
     * <AUTHOR>
     */
    public function actionView($key, $page = 0)
    {
        $this->pageArray['key'] = urldecode($key);
        switch ($this->db->type(urldecode($key)))
        {
            case Redis::REDIS_SET:
                $this->pageArray['members'] = $this->db->sMembers(urldecode($key));
                loadView("admin/redmin/setview.tpl.html", $this->pageArray);
                break;
            case Redis::REDIS_LIST:
                $this->pageArray['page'] = $page;
                $this->pageArray['count'] = $this->db->lLen(urldecode($key));
                $start = $page * 30;
                $this->pageArray['ceil'] = floor($this->pageArray['count'] / 30);
                $this->pageArray['start'] = $start;
                $this->pageArray['values'] = $this->db->lRange(urldecode($key), $start, $start + 29);
                loadView("admin/redmin/listview.tpl.html", $this->pageArray);
                break;
            case Redis::REDIS_ZSET:
                $this->pageArray['count'] = $this->db->zSize(urldecode($key));
                $start = $page * 30;
                $this->pageArray['ceil'] = floor($this->pageArray['count'] / 30);
                $this->pageArray['values'] = $this->db->zRange(urldecode($key), $start, $start + 29, True);
                loadView("admin/redmin/zsetsview.tpl.html", $this->pageArray);
                break;
            case Redis::REDIS_HASH:
                $this->pageArray['members'] = $this->db->hGetAll(urldecode($key));
                loadView("admin/redmin/hashview.tpl.html", $this->pageArray);
                break;
        }
    }

    /**
     * 搜索key
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionSearch()
    {
        includeRedis("RedisModel", "model/");
        if ($this->router->method == RedminRouter::POST)
        {
            $key = $this->inputs->post('key', null);
            $host = $this->inputs->post('host', '');
            $port = $this->inputs->post('port', '');
            $password = $this->inputs->post('password', '');
            if (!empty($host) && !empty($port) && !empty($password) && !$this->isSameRedisConnection($host, $port, $password))
            {
                $this->pageArray['host'] = $host;
                $this->pageArray['port'] = $port;
                $this->pageArray['password'] = $password;
                $this->changeRedisConnection($host, $port, $password);
            }
            $this->pageArray['redisHostArray'] = $this->getRedisHostArray();

            if (isset($key) && trim($key) != '')
            {
                $this->pageArray['keys'] = array($key);
                $this->pageArray['search'] = $key;
                $RedisModel = RedisModel::instance();
                if ($this->db->exists($key))
                {
                    $this->pageArray['searchKey']['findkey'] = $key;
                    $this->pageArray['searchKey']['type'] = $RedisModel->getType($key);
                    $this->pageArray['searchKey']['ttl'] = $RedisModel->getTTL($key);
                    $this->pageArray['searchKey']['refcount'] = $RedisModel->getCount($key);
                    $this->pageArray['searchKey']['idle'] = $RedisModel->getIdleTime($key);
                    $this->pageArray['searchKey']['encoding'] = $RedisModel->getEncoding($key);
                    $this->pageArray['searchKey']['size'] = $RedisModel->getSize($key);
                }
                if ($this->db->type($key) == Redis::REDIS_STRING)
                {
                    $this->pageArray['searchKey']['keyvalue'] = $this->db->get($key);
                }
                //asort($keys);
                loadView('admin/redmin/search.tpl.html', $this->pageArray);
            }
            else
            {
                $this->pageArray['emptykey'] = true;
                loadView('admin/redmin/search.tpl.html', $this->pageArray);
            }
        }
    }

    /**
     * 设置redis LOG_HANDLER_KEY
     * @author：dongx
     * @return void
     */
    public function actionSetLogToRedis()
    {
        $redis = RedisEx::getInstance();
        if (isset($_POST['LOG_HANDLER']))
        {
            $redis->set(LOG_HANDLER_KEY, $_POST['LOG_HANDLER']);
        }
        echo 'ok';
    }

    /**
     * 实例&集群数据比对检测
     *
     * @return null
     */
    public function actionConfirm()
    {
        $checkFile = APPPATH . "/logs/checkResult.txt";
        $list = [];
        if (is_file($checkFile)) {
            $content = file_get_contents($checkFile);
            if (preg_match('/已运行(\d)分钟/', $content, $matches)) {
                $this->pageArray['checking'] = $content;
            } else {
                if (isset($_GET['act']) && $_GET['act'] == 'refresh') {
                    @unlink($checkFile);
                    redirect('http://login.2345.com/houtai_gl/redis/confirm');
                } else {
                    $list = json_decode($content, true);
                    \Common\Utils\Encoding::iteratorArray($list, "utf-8", "gbk");
                }
            }
        }
        unset($this->pageArray['redisConfig']);
        unset($this->pageArray['redisKeys']);
        $this->pageArray['list'] = $list;
        loadView("admin/redmin/confirm.tpl.html", $this->pageArray);
    }
}
