<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：DataFormat.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：06-15, 2016
 */

namespace Common\Utils;

class DataFormat
{
    const EMAIL = "email";
    const PHONE = "phone";
    const USERNAME = "username";

    public static function protectString($str, $type = "")
    {
        if ($type == self::EMAIL)
        {
            $email_pre = explode("@", $str);
            if (strlen($email_pre[0]) > 7)
            {
                $email_pre[0] = substr($email_pre[0], 0, 3) . "****" . substr($email_pre[0], strlen($email_pre[0]) - 3, 3);
            }
            else
            {
                $email_pre[0] = substr($email_pre[0], 0, 3) . "****";
            }
            $str = $email_pre[0] . "@" . $email_pre[1];
        }
        elseif ($type == self::PHONE)
        {
            $str = substr($str, 0, 3) . '****' . substr($str, -4);
        }
        elseif ($type == self::USERNAME)
        {

        }
        else
        {
            if (strlen($str) > 7)
            {
                $str = substr($str, 0, 3) . '****' . substr($str, -4);
            }
        }
        return $str;
    }

}
