<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/3
 * Time: 9:40
 */
includeBase('ExternalController');

class CheckController extends ExternalController
{
    /**
     * 判断手机号码是否已经注册
     * @return string
     */
    public function actionPhoneStatus()
    {
        $this->startSession();
        //校验必填字段及签名
        $necessaryField = array(
            'exId',
            'phone',
            'timestamp',
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check('data', $necessaryField);
        if ($aesDecodeData === false)
        {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        list($exId, $phone) = $this->getParamAndAutoDeny($necessaryField);
        $memberModel = loadModel('member');
        $status = 0;
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(300, "", "请确认您的手机号是否填写正确!");
        }
        else
        {
            $_SESSION['captcha_phone'] = $phone;
            $res = $memberModel->checkPhone($phone, true); //检查是否存在手机号, 包括用户名
            if ($res > 0)
            {
                $status = 1;
            }
        }
        $data = array(
            "status" => $status,
        );
//        $msg = 'phone:' . $phone . "\t" . "status:" . $status;
//        xLog('external/checkPhoneStatus', 'checkPhoneStatus', $msg, 'info');
        $this->codeResponse(200, $data);
    }
}
