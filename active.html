<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
        <title>用户中心-2345网址导航</title>
        <link href="/css/css_20130814.css" rel="stylesheet" type="text/css" />
    </head>
    <body>
        <div class="top">
            <p><a href="http://www.2345.com" target="_blank">2345网址大全</a> | <a href="http://www.duote.com" target="_blank">2345软件大全</a> | <a href="http://haozip.2345.com" target="_blank">2345好压</a> | <a href="http://ie.2345.com" target="_blank">2345王牌浏览器</a></p>
            <a href="http://www.2345.com" target="_blank" class="logo"><img src="/images/logo20141017.png" /></a>
        </div>
        <div class="cnt">
            <div class="cnt_left">
                <h3 class="cnt_left_tit">激活2345帐号</h3>
                <table width="95%" border="0" cellspacing="0" cellpadding="0" class="tab2">
                    <tr>
                        <td>
                            <p><b>我们已经将激活帐号的链接发到您注册时填写的邮箱中，请尽快查收此邮件并激活该帐号。</b></p>
                            <p><b>感谢您一直以来对2345网址大全关注与支持！<span id="email_host"></span></b></p>
                            <p><b id="active_email" style="display: none;">如果您没收到邮件，可<a href="#" onclick="active_email();
                                    return false;" style="color:#2d87cc;text-decoration:underline;">再发一封</a>激活邮件。</b></p>
                            <p style="color:#999;">如果您需要登录帐号，请<a style="text-decoration:underline" href="/login" class="blue">点此返回</a>登录页面。</p>
                            <p><br /></p>
                            <script type="text/javascript">
                                var cookies = document.cookie.split('; ');
                                var emailVal = '';
                                for (i in cookies) {
                                    cookie = cookies[i].split('=');
                                    if (cookie[0] == 'active_email') {
                                        emailVal = decodeURIComponent(cookie[1]);
                                        break;
                                    }
                                }
                                if (emailVal) {
                                    var emailExp = emailVal.split('@');
                                    if (emailExp[1] == 'gmail.com') {
                                        var emailHost = 'http://www.' + emailExp[1];
                                    } else {
                                        var emailHost = 'http://mail.' + emailExp[1];
                                    }
                                    document.getElementById('email_host').innerHTML = '<a href="' + emailHost + '" target="_blank" style="color:#2d87cc;text-decoration:underline;">去邮箱收件>></a>';
                                    document.getElementById('active_email').style.display = '';
                                }
                            </script>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="links">
                                <p style="color:#999;">拥有2345帐号，您可以登录：</p>
                                <a href="http://www.2345.com/" target="_blank"><img src="/images/plogo_01_20140123.png" width="114" height="75"/></a>
                                <a href="http://www.duote.com/" target="_blank"><img src="/images/plogo_02_20140123.png" width="98" height="75"/></a>
                                <a href="http://haozip.2345.com/" target="_blank"><img src="/images/plogo_03.png" width="67" height="75"/></a>
                                <a href="http://ie.2345.com/" target="_blank"><img src="/images/plogo_04_2014.png" width="108" height="75"/></a>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="cnt_right2" style="height:400px;">
                <i><a href="http://gaoji.2345.com/help.php?c=4" class="gray">什么是2345帐号?</a></i>
                <div class="help">
                    <strong style="font-size:14px; font-weight:700; display:block; padding:50px 0 20px; height:20px;">客服电话</strong>
                    <p><img src="/images/tel_400.png" width="198" height="33"/></p>
                </div>
            </div>
        </div>
        <script type="text/javascript">
            var xhr = null;
            function initXhr() {
                if (window.XMLHttpRequest) {
                    try {
                        xhr = new XMLHttpRequest();
                    } catch (e) {
                        xhr = false;
                    }
                } else if (window.ActiveXObject) {
                    try {
                        xhr = new ActiveXObject("Msxml2.XMLHTTP");
                    } catch (e) {
                        try {
                            xhr = new ActiveXObject("Microsoft.XMLHTTP");
                        } catch (e) {
                            xhr = false;
                        }
                    }
                }
            }
            initXhr();
            function sendPost(_url, _para, _callBack, _method) {
                _callBack = _callBack || function() {
                };
                _method = _method || "POST";
                if (xhr) {
                    xhr.open(_method, _url, true);
                    xhr.onreadystatechange = _callBack;
                    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;");
                    xhr.send(_para);
                }
            }
            function active_email() {
                sendPost('/unlock/active', '', function() {
                    if (xhr.readyState == 4 && xhr.status == 200 && xhr.responseText != "NaN") {
                        status = parseInt(xhr.responseText,10);
                        if (status == 1) {
                            document.getElementById('active_email').innerHTML = '邮件发送成功！';
                        } else if (status == 0) {
                            document.getElementById('active_email').innerHTML = '邮件发送失败！';
                        } else if (status == -1) {
                            document.getElementById('active_email').innerHTML = '邮件发送过于频繁！请联系客服！';
                        }
                    }
                });
            }
        </script>
        <div class="foot"><a href="http://www.2345.com/about/about.htm" target="_blank">关于2345</a> | <a href="http://www.2345.com/about/gyhd.htm" target="_blank">公益</a> | <a target="_blank" href="/passport/declare.html">隐私政策</a> | <a href="http://www.2345.net/join_us/" target="_blank">诚聘英才</a>  <span><a href=" http://www.miitbeian.gov.cn/" target="_blank">沪ICP备12023051号-1</a>  诚信上网 规范经营</span></div>
    </body>
</html>