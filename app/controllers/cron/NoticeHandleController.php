<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/6/24
 * Time: 11:40
 */
includeBase('CronController');

class NoticeHandleController extends CronController
{
    /**
     * 通知回调入redis,消费脚本
     * -
     * @return void
     */
    public function actionIndex()
    {
        $memberModel = loadModel('member');
        $noticeHandle = loadAction('NoticeHandle');
        $noticeHandle->startTimer(58);
        while ($noticeHandle->isExpire())
        {
            if ($noticeHandle->getNoticeListLength() <= 0)
            {
                sleep(1);
                continue;
            }
            $i = 0;
            $noticeHandle->setNoticeLogMsg("开始读取队列,队列长度：" . $noticeHandle->getNoticeListLength());
            while ($noticeList = $noticeHandle->getNoticeList($noticeHandle->isExpire()))
            {
                WebLogger\Facade\LoggerFacade::Info('推送排查-时间', ['start' => time()]);
                ++$i;
                $noticeInfo = json_decode($noticeList, true);
                loadAction('Encoding');
                $noticeInfo = EncodingAction::transcoding($noticeInfo, 'gbk');
                if (empty($noticeInfo) || !is_array($noticeInfo))
                {
                    $noticeHandle->setNoticeLogMsg('解析失败:'. $noticeInfo);
                    continue;
                }
                if (!$noticeHandle->checkParams($noticeInfo))
                {
                    $noticeHandle->setNoticeLogMsg('参数校验失败:'. var_export($noticeInfo, 1));
                    continue;
                }
                if (empty($noticeInfo['uid'])) {
                    $uid = $memberModel->getUid($noticeInfo['passid']);
                    if (!empty($uid)) {
                        $noticeInfo['uid'] = $uid;
                    }
                }
                $noticeHandle->handleData($noticeInfo);
                WebLogger\Facade\LoggerFacade::Info('推送排查-时间', ['stop' => time()]);
                //读了1000条了休息下
                if ($i == 1000)
                {
                    $noticeHandle->setNoticeLogMsg("读取队列休息1秒钟，当前剩余队列：" . $noticeHandle->getNoticeListLength());
                    sleep(1);
                }
            }
            $noticeHandle->setNoticeLogMsg("队列读取结束，当前剩余队列：" . $noticeHandle->getNoticeListLength(), true);
            echo $noticeHandle->getNoticeLogMsg();
            sleep(1);
        }
    }

    /**
     * 通知回调失败,重试3次
     * -
     * @return bool|void
     */
    public function actionRepeat()
    {
        loadAction('Encoding');
        $noticeHandle = loadAction('NoticeHandle');
        $noticeHandle->startTimer(58);
        $noticeHandle->setNoticeLogMsg('回调重试开始');
        $noticeNewModel = loadModel('NoticeNew');
        $memberModel = loadModel('member');
        $errorList = $noticeNewModel->getErrorNoticeList(3);
        $noticeHandle->setNoticeLogMsg('当次查询总数:' . count($errorList));
        $i = 0;
        $noTime = 0;
        foreach ($errorList as $noticeError)
        {
            if (!$noticeHandle->isExpire())
            {
                $noticeHandle->setNoticeLogMsg('总共执行回调重试' . $i . '次,未到执行时间' . $noTime . '个');
                $noticeHandle->setNoticeLogMsg('回调重试结束', true);
                echo $noticeHandle->getNoticeLogMsg();
                return;
            }
            if ($noticeError['notice_time'] > time())
            {
                ++$noTime;
                continue;
            }
            ++$i;
            $params = EncodingAction::transcoding(unserialize($noticeError['params']));
            if (empty($params['uid'])) {
                $uid = $memberModel->getUid($params['passid']);
                if (!empty($uid)) {
                    $params['uid'] = $uid;
                }
            }
            $requests[] = $noticeHandle->getSendParams($noticeError['id'], $noticeError['api_url'], $params, $noticeError['retry_times']);
            // todo 有问题 try 应该移出循环
            try
            {
                rollingCurl($requests);
            }
            catch (Exception $e)
            {
                $noticeHandle->setNoticeLogMsg($e->getMessage());
                $noticeHandle->setNoticeLogMsg('回调重试结束', true);
                echo $noticeHandle->getNoticeLogMsg();
                return;
            }
        }
        $noticeHandle->setNoticeLogMsg('总共执行回调重试' . $i . '次,未到执行时间' . $noTime . '个');
        $noticeHandle->setNoticeLogMsg('回调重试结束', true);
        echo $noticeHandle->getNoticeLogMsg();
    }

    /**
     * 通知回调失败3次日志报警
     * -
     * @return bool|void
     */
    public function actionWarn()
    {
        $noticeArr = array();
        $warnMsg = '';
        $noticeNewModel = loadModel('NoticeNew');
        $noticeHandle = loadAction('NoticeHandle');
        $noticeHandle->setNoticeLogMsg("报警扫描开始");
        $runTime = time() - 180;
        //取出3分钟内重试>=3次的数据
        $errorList = $noticeNewModel->getErrorNoticeList(3, 'ge', 0, $runTime);
        $noticeHandle->setNoticeLogMsg("扫描报警总数:" . count($errorList));
        foreach ($errorList as $noticeError)
        {
            $noticeArr[$noticeError['notice_type']][$noticeError['api_url']] += 1;
        }

        foreach ($noticeArr as $notices)
        {
            foreach ($notices as $noticeKey => $noticeInfo)
            {

                if ($noticeKey == 'https://newsapp.2345.com/apiv1/userinfo/userCallback') {
                    continue;
                }
                if ($noticeInfo >= 5)
                {
                    $warnMsg .= $noticeKey . ':回调通知异常' . PHP_EOL;
                }
            }
        }
        $listLength = $noticeHandle->getNoticeListLength();
        if ($listLength >= 100)
        {
            $warnMsg .= "通知回调队列长度：" . $listLength . PHP_EOL;
        }

        if (!empty($warnMsg))
        {
            $title = "[用户中心]通知接口异常";
            $content = str_replace(PHP_EOL, "；\t", $warnMsg);
            $sussMsg = '';
            if (RUNMODE == 'production')
            {
                $ret = sendCcserWarn($title, $content);
                $sussMsg = $ret ? '报警成功' : '报警失败';
            }
            $noticeHandle->setNoticeLogMsg($warnMsg . $sussMsg);
        }
        $noticeHandle->setNoticeLogMsg("报警扫描结束", true);
        echo $noticeHandle->getNoticeLogMsg();
    }

    /**
     * 清除一个小时以前的ID记录
     * -
     * @return void
     */
    public function actionClearSeq()
    {
        $startTime = time();
        $noticeNewModel = loadModel('NoticeNew');
        $noticeHandle = loadAction('NoticeHandle');
        $noticeHandle->setNoticeLogMsg("清理数据开始");
        $msg = $noticeNewModel->clearSeq() !== false ? '删除成功' . PHP_EOL : '删除失败' . PHP_EOL;
        $costTime = time() - $startTime;
        $msg .= '清理时长：' . $costTime . PHP_EOL . "清理结束;";
        $noticeHandle->setNoticeLogMsg($msg, true);
        echo $noticeHandle->getNoticeLogMsg();
    }
}
