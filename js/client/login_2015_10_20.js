document.write('<script type="text/javascript" src="http://login.2345.com/js/client/base.js"></script>');
document.write('<script type="text/javascript" src="http://login.2345.com/js/client/jquery.md5.js"></script>');
var login = 
{
    proxyUrl : '',
    errorLog : '',
    isShowCode : false,
    commitParam : new Object() ,
    locationUrl : '',
    isAjax : false,
    isThirdAjax : false,
    jqueryObj : $ ,
    init : function ()
    {
        login.jqueryObj.ajax({
           url: 'http://login.2345.com/webapi/LoginCheck',
           type: "GET",
           dataType: 'jsonp',
           jsonp: 'jsoncallback',
           data: '',
           async: false,
           success: function (as) {
           }
        });
        
        login.jqueryObj('.changeCode,.codeImg').click(function (){
            login.showCodeImg();
        });
        login.jqueryObj(".g-inputTxt input").focus(function (){
            login.jqueryObj(this).parent().addClass("g-inputTxt-focus");
            login.jqueryObj(this).siblings(".sDes").css("display","none");
        });
        login.jqueryObj(".g-inputTxt input").blur(function (){
            login.jqueryObj(this).parent().removeClass("g-inputTxt-focus g-inputTxt-error");
            if(login.jqueryObj(this).val() == ""){
                login.jqueryObj(this).siblings(".sDes").css("display","block");
            }
        });
        login.jqueryObj(".g-inputTxt input").keypress(function (event){
            var key = event.which;
            if( key == 13){
                login.submitForm();
            }
        });
        
        var exePopupName = this.exeFuncPopupName;
        login.jqueryObj('.loginClose').click(function (){
            if ( typeof exePopupName != 'undefined' )
            {
                exePopupName();
            }
            login.jqueryObj('#jsLoginDiv').hide();
        });
        if (typeof this.locationUrl == 'undefined' )
        {
            this.locationUrl = 'http://'+document.domain;
        }
        var callbackUrl = '&callback=';
        
        switch ( typeof (this.setCookieUrl) )
        {
             case 'string' :
                callbackUrl += this.setCookieUrl;
                break;
             case 'object' :
                 for (var surl in this.setCookieUrl)
                 {
                    callbackUrl += this.setCookieUrl[surl] + ',' ;
                 }
                 break;
            default:
                var callbackUrl = '';
        }

        login.jqueryObj('.thirdPartyLogin').click(function (){
            
            var thirdQqCallLocation = '';
            var thirdWeiboCallLocation = '';
            switch ( typeof (login.thirdLocationUrl) )
            {
                case 'string':
                    thirdWeiboCallLocation = thirdQqCallLocation = login.thirdLocationUrl;
                    break;
                case 'object' :
                    thirdQqCallLocation = typeof login.thirdLocationUrl['qq'] != 'undefined' ? login.thirdLocationUrl['qq'] : login.locationUrl;
                    thirdWeiboCallLocation = typeof login.thirdLocationUrl['weibo']  != 'undefined' ? login.thirdLocationUrl['weibo'] : login.locationUrl;
                    break;
                default:
                    thirdWeiboCallLocation = thirdQqCallLocation = login.locationUrl;
            }
            var type = login.jqueryObj(this).attr('type');
            switch (type)
            {
                case 'qq':
                    var link = 'http://login.2345.com/qq?forward=' + thirdQqCallLocation + callbackUrl;
                    break;
                case 'weibo':
                    var link = 'http://login.2345.com/weibo?forward=' + thirdWeiboCallLocation + callbackUrl;
                    break;
                default:
                    link = '';
                    break;
            }
            if (link == '')
            {
                return false;
            }
            if ( login.isThirdAjax )
            {
                login.thirdExeName(link);
            }
            else
            {
                window.location = link;
            }
            
        });
    },
    checkLoginStatus : function (data)
    {
        if ( data.display )
        {
            login.isShowCode = data.display ;
            login.showCodeImg();
        }
    },
    setLocation : function (url)
    {
        this.locationUrl = url;
    },
    setThirdLocation : function (obj)
    {
        this.thirdLocationUrl = obj;
    },
    setThirdExeFunc : function (funcName)
    {
        this.thirdExeName = funcName;
    },
    setError : function (error)
    {
        this.errorLog = error;
    },
    getLastError : function ()
    {
        return this.errorLog;
    },
    exeFunc : function ( funcName )
    {
        this.exeFuncName = funcName;
    },
    exePopup : function ( popupName )
    {
        this.exeFuncPopupName = popupName;
    },
    //设置提交的参数
    setCommitParam : function ( data )
    {
        this.commitParam =  data;
    },
    //获取提交参数转换成input
    getOtherParamHtml : function ()
    {
        var str = '';
        for(var key in this.commitParam)
        {
            str += '<input type="hidden" value="' + this.commitParam[key] +  '" name="'+ key  +'">';
        }
        return str;
    },
    setLoginConfig : function ()
    {
        
    },
    setProxy : function ( proxyUrl )
    {
       this.proxyUrl = proxyUrl;
    },
    setCookieUrl : function ( url )
    {
        this.setCookieUrl = url;
    },
    isHtml5 : function ()
    {
        if (window.applicationCache)
        {
            return true;
        }
        else
        {
            return false;
        }
    },
    getLoginHtml : function ()
    {
        return '<div class="g-login" id="jsLoginDiv">'+
          '<div class="g-login-th">'+
            '<span class="sMark g-left">2345帐号登录</span>'+
            '<a href="javascript:void(0);" class="g-login-closeBtn g-right loginClose"></a>'+
          '</div>'+
          '<div class="g-login-tb clearfix">'+
           ' <form id="myFormLogin">'+
           
           
           this.getOtherParamHtml()
           
           
             +'<input type="hidden" name="cmd" value="login">'+
             '<input type="hidden"  name="currtime"/>'+
              '<div class="g-login-form">'+
                  '<div class="g-inputTxt">'+
                    '<i class="g-icon-name"></i>'+
                    '<span class="sDes">手机/已验证邮箱/用户名</span>'+
                    '<input type="text" name="username" value="">'+
                  '</div>'+
                  '<div class="g-inputTxt">'+
                    '<i class="g-icon-password"></i>'+
                    '<span class="sDes">密码</span>'+
                    '<input type="hidden" value="" name="password">'+
                    '<input type="password" value="" id="expassword">'+
                  '</div>'+
                  '<div class="g-login-code" style="display:none">'+
                    '<div class="g-inputTxt">'+
                      '<input type="text" value="" name="check_code">'+
                    '</div>'+
                        '<div class="codePic"><span class="sPic g-left">'+
                        '<img src="" class="codeImg" width="88" height="40">'+
                        '</span><a href="javascript:void(0);"  class="g-right changeCode">换一换</a></div>'+
                  '</div>'+
                '<div class="g-error" id="g-error-tips" style="display:none"></div>'+
        
                  '<div class="g-txt"><span class="g-sCheck g-left"><input type="checkbox" checked class="g-checkbox" name="autoLogin">下次自动登录</span><a href="http://login.2345.com/find?type=password" target="_blank" class="g-right forgetPassClass">忘记密码？</a></div>'+
        
                  '<a href="javascript:void(0);"  class="g-btn btn-submit">登&nbsp;&nbsp;&nbsp;&nbsp;录</a>'+
                  '<div class="g-txt g-txt-registration"><span class="g-txt-no-account-txt">还没有2345帐号？</span><a href="http://login.2345.com/reg.php?forward=' + this.locationUrl +'" target="_blank" class="g-txt-login-btn"><em>立刻</em>注册</a></div>'+
					'<div class="g-other-login">'+
                    '<span class="sTit">你也可以用以下方式登录</span>'+
                    '<div class="otherStyle">'+
                      '<a href="javascript:void(0);" class="blueBtn thirdPartyLogin" type="qq"><i class="g-icon-qq"></i>QQ登录</a>'+
                      '<a href="javascript:void(0);" class="redBtn thirdPartyLogin thirdPartyWeibo" type="weibo"><i class="g-icon-sina"></i></a>'+
                    '</div>'+
                  '</div>'+
        
              '</div>'+
            '</form>'+
          '</div>'+
        '</div>';
    },
    showCodeImg: function ()
    {
        d = new Date();
        login.jqueryObj('.codeImg').prop('src','http://login.2345.com/randCaptcha.php?d=' + d.getTime() );
        login.jqueryObj('.g-login-code').show();
    },
    showErrorTips : function (tag,msg)
    {
        var gErrorTips = login.jqueryObj(tag);
        gErrorTips.html(msg);
        gErrorTips.show();
    },
    checkUserAccount : function ()
    {
        var status = false;
        var userNameObj = login.jqueryObj('.g-inputTxt input[name="username"]');
        var username = userNameObj.val();
        if ( username == '' )
        {
            login.showErrorTips('#g-error-tips','用户名不能为空');
            userNameObj.parent().addClass('g-inputTxt-error');
        }
        else if (username.length < 2)
        {
            login.showErrorTips('#g-error-tips','最少2个字符!');
            userNameObj.parent().addClass('g-inputTxt-error');
        }
        else if ( username.replace(/[^\x00-\xff]/g, "**").length > 24 )
        {
            login.showErrorTips('#g-error-tips','请不要超过24个字符!');
            userNameObj.parent().addClass('g-inputTxt-error');
        }
        else
        {
            status = true;
        }
        return status;
    },
    checkUserPass : function ()
    {
        var status = false;
        var password = login.jqueryObj('#expassword').val();
        if ( password == '')
        {
            login.showErrorTips('#g-error-tips','请先输入密码!');
            login.jqueryObj('#expassword').parent().addClass('g-inputTxt-error');
        }
        else
        {
            login.jqueryObj('input[name="password"]').val( MD5 (password) );
            status = true;
        }
        return status
    },
    showLogin : function (tag)
    {
        if ( this.proxyUrl == '')
        {
            login.showErrorTips('body','代理页面不能为空');
            return false;
        }
        login.jqueryObj(tag).append(this.getLoginHtml () );
        this.init();
        if ( this.isHtml5() )
        {
            this.createProxy();
            window.addEventListener("message",function(e){
 			    login.response(e.data);
      		},false);
        }
        login.jqueryObj('.btn-submit').click(function (){
            login.submitForm();
        });
    },
    submitForm : function ()
    {
        if (login.jqueryObj.browser.msie && (login.jqueryObj.browser.version == "6.0") && !login.jqueryObj.support.style) {
            login.jqueryObj('a.btn-submit').attr('href','###');
        }
        if ( ! login.checkUserAccount () )
        {
            return false;
        }
        if ( ! login.checkUserPass() )
        {
            return false;   
        }
        if ( login.isShowCode == true )
        {
            var checkCode = login.jqueryObj('input[name="check_code"]').val();
            if ( checkCode == '')
            {
                login.showErrorTips('#g-error-tips','请输入计算结果');
                return false; 
            } 
        }
        login.jqueryObj('input[name="currtime"]').val( parseInt(new Date().getTime() / 1000)); 
        var getHost = window.location.host;
        var getCurrentUrl = window.location;
        var submitData = login.jqueryObj('#myFormLogin').serialize() + '&domain='+getHost + '&currentUrl='+ getCurrentUrl;
        window.submitData = submitData;
        if ( login.isHtml5() )
        {
            var win = document.getElementById("loginProxy").contentWindow;
            win.postMessage( submitData  ,"http://login.2345.com");
        }
        else
        {
            login.createProxy();
        }
    },
    createProxy : function ()
    {
        var code = new Base64();
        if ( this.isHtml5() )
        {
            login.jqueryObj('body').append('<iframe id="loginProxy" style="display:none" src="http://login.2345.com/proxy.html#'+code.encode(this.proxyUrl)+'"></iframe>');
        }
        else
        {
            login.jqueryObj('body').append('<iframe id="loginProxy" style="display:none" src="'+this.proxyUrl+'#'+code.encode(this.proxyUrl)+'"></iframe>');
        }
    },
    response : function (data)
    {
        data = eval("("+data+")");
        if (data.status != 'P00001')
        {
            if ( typeof(data.location) != "undefined" )
            {
                window.location = data.location;
                return false;
            }
            if ( data.display == true )
            {
                login.isShowCode = true;
                login.showCodeImg();
            }
            login.jqueryObj('input[name="password"]').val('');
            login.showErrorTips('#g-error-tips',data.msg);
        }
        else
        {
            if ( typeof(data.loadPage) == "undefined" )
            {
                data.loadPage = new Array();
            }
            if ( document.domain.slice(-8) != '2345.com')
            {
                var cookieI = eval("("+data.setCookie+")");
                var param =  '?' + cookieI.I ;
                switch ( typeof (this.setCookieUrl) )
                {
                    case 'string' :
                        data.loadPage[data.loadPage.length ] = this.setCookieUrl + param;
                        break;
                    case 'object' :
                        for (var surl in this.setCookieUrl)
                        {
                            data.loadPage[data.loadPage.length ] = this.setCookieUrl[surl] + param ;
                        }
                        break;
                    default:
                        data.loadPage[data.loadPage.length ] = '/user/SetLoginCallBack?' + param ;
                }
            }
            var len = data.loadPage.length;
            var j = 0;
            for (var i in  data.loadPage)
            {
                login.jqueryObj.getScript(data.loadPage[i], function(){
                    ++j;
                    if ( j == len)
                    {
                        login.endRunFunc(data);
                    }
                });
            }
        }
    },
    endRunFunc : function (data)
    {
        if ( typeof this.exeFuncName != 'undefined')
        {
            this.exeFuncName();
        }
        if ( ! this.isAjax )
        {
            login.locationPage(data)
        }
    },
    locationPage : function (data)
    {
        if ( typeof(data.location) != "undefined" )
        {
            window.location = data.location;
        }
        else if ( this.locationUrl != '')
        {
            window.location = this.locationUrl;
        }
        else
        {
            window.location.reload();
        }
    },
    chgWeiboText : function(text)
    {
        login.jqueryObj('.thirdPartyWeibo').html(text + '<i class="g-icon-sina"></i>');
    }
}