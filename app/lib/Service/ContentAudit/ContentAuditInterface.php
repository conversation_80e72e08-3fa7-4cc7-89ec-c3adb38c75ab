<?php


namespace Service\ContentAudit;

/**
 * 内容审核接口
 *
 * PHP version >= 7.0
 *
 * Interface ContentAuditInterface
 * @package Service\ContentAudit
 */
interface ContentAuditInterface
{
    /**
     * 初始化必要的设置
     *
     * @param array $paramA 需要设置的参数
     *
     * @return void
     */
    public function init(array $paramA);

    /**
     *文本审核
     *
     * @param string $content 审核内容
     * @param array  $paramA  请求参数
     *
     * @return array 返回结果
     */
    public function textAudit(string $content, array $paramA = null);

    /**
     * 图片同步审核
     *
     * @param string $imgUrl 可访问url
     * @param array  $paramA 请求参数
     *
     * @return array 返回结果
     */
    public function imageSyncAudit(string $imgUrl, array $paramA = null);

    /**
     * 图片异步审核
     *
     * @param string $imgUrl 可访问url
     * @param array  $paramA 请求参数
     *
     * @return array 返回结果
     */
    public function imageAsyncAudit(string $imgUrl, array $paramA = null);

    /**
     * 视频同步审核
     *
     * @return void
     */
    public function videoSyncAudit();

    /**
     * 视频异步审核
     *
     * @return void
     */
    public function videoAsyncAudit();

    /**
     * 审核回调
     *
     * @param array $paramA 一些必要参数
     *
     * @return  array 返回结果
     */
    public function auditCallBack(array $paramA = null);
}