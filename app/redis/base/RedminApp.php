<?php

final class RedminApp
{
    protected static $_instance = Null;
    protected $_data = Array();

    /**
     * RedminApp constructor.
     */
    protected function __construct()
    {
        $this->_data['config'] = require_once(APPPATH . '/config/RedminConfig.php');
        $this->_data['drivers'] = 'drivers/';
    }

    /**
     * changeRedisHostConfig
     * -
     * @param string $host Host
     * @param int $port Port
     * @param string $password Password
     * @return void
     * <AUTHOR>
     */
    public function changeRedisHostConfig($host = '', $port = '', $password = '')
    {
        if (!empty($host) && !empty($port) && !empty($password))
        {
            $this->_data['config']['database']['redis']['host'] = $host;
            $this->_data['config']['database']['redis']['port'] = $port;
            $this->_data['config']['database']['redis']['password'] = $password;
        }
    }

    /**
     * 实例化
     * @return null|RedminApp
     * <AUTHOR>
     */
    public static function instance()
    {
        if (!self::$_instance)
        {
            self::$_instance = new self;
        }

        return self::$_instance;
    }

    /**
     * 魔术方法
     * @param $key
     * @return null
     * <AUTHOR>
     */
    public function __get($key)
    {
        return isset($this->_data[$key]) ? $this->_data[$key] : Null;
    }
}
