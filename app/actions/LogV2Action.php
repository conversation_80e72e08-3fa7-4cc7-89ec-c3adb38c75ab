<?php
/**
 * Copyright (c) 2022,上海二三四五网络科技有限公司
 * 摘    要: 用户中心-用户操作日志-记录上报
 * 作    者: 朱锋锦
 * 日    期: 2022-03-25
 */

use Service\Amqp\AmqpV2;

class LogV2Action extends Action
{

    const LOG_V2_PLATFORM = 'login_v1';
    const LOG_V2_PLATFORMNAME = '用户中心V1';

    const LOG_V2_OPERATOR_TYPE_ADMIN = 1;
    const LOG_V2_OPERATOR_TYPE_USER = 2;

    /**
     * 功    能: 单条日志上报
     * 作    者: 朱锋锦
     * 日    期: 2022-03-25
     *
     * @param string $strMid mid
     * @param string $strType 日志大类
     * @param string $strSubType 日志小类
     * @param string $arrUserInfo 用户信息，需要包含passid、phone、email、username
     * @param string $strBefore 操作前内容
     * @param string $strAfter 操作前内容
     * @param string $strOperator 操作人员
     * @return bool
     */
    public function report($strMid, $strType, $strSubType, $arrUserInfo, $strBefore = "", $strAfter = "", $strOperator = "")
    {
        $strDomain = '';
        $strRefer = $_SERVER['HTTP_REFERER'];
        if ($strRefer) {
            $strDomain = parse_url($strRefer,  PHP_URL_HOST) ?: '';
        }

        $intOperatorType = self::LOG_V2_OPERATOR_TYPE_ADMIN;
        if (!$strOperator) {
            $strOperator = strval($arrUserInfo['passid'] ?? 0);
            $intOperatorType = self::LOG_V2_OPERATOR_TYPE_USER;
        }

        $arrReport = [
            'platform' => self::LOG_V2_PLATFORM,
            'platformName' => mb_convert_encoding(self::LOG_V2_PLATFORMNAME, 'utf-8', 'gbk'),
            'mid' => strval($strMid),
            'type' => strval($strType),
            'subType' => strval($strSubType),
            'passid' => intval($arrUserInfo['passid'] ?? 0),
            'phone' => strval($arrUserInfo['phone'] ?? ''),
            'email' => strval($arrUserInfo['email'] ?? ''),
            'username' => mb_convert_encoding(strval($arrUserInfo['username'] ?? ''), 'utf-8', 'gbk'),
            'before' => mb_convert_encoding(strval($strBefore), 'utf-8', 'gbk'),
            'after' => mb_convert_encoding(strval($strAfter), 'utf-8', 'gbk'),
            'ip' => get_client_ip(),
            'port' => get_remote_port(),
            'domain' => $strDomain,
            'operatorType' => $intOperatorType,
            'operator' => mb_convert_encoding($strOperator, 'utf-8', 'gbk'),
        ];

        try {
            $strMqConfig = 'service_log';
            $strExchangeName = Config::get('rabbitMq')['service_log_v2_exchange_name'];;
            $strRouteKey = Config::get('rabbitMq')['service_log_v2_route_key'];
            $objAmqp = AmqpV2::getInstance($strMqConfig);
            $strReport = json_encode($arrReport, JSON_UNESCAPED_UNICODE);
            $blnRe = $objAmqp->putDirectMsg($strReport, $strRouteKey, $strExchangeName);
            if (!$blnRe) {
                AmqpV2::delInstance($strMqConfig);
                \WebLogger\Facade\LoggerFacade::error('日志上报写入MQ失败', ['log' => $arrReport]);
                return false;
            }
        } catch (Exception $e) {
            AmqpV2::delInstance($strMqConfig);
            \WebLogger\Facade\LoggerFacade::error('日志上报写入MQ错误', ['log' => $arrReport, 'code' => $e->getCode(), 'msg' => $e->getMessage()]);
            return false;
        }
        return true;
    }
}
