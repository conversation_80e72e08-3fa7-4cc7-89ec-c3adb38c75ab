<?php

use Octopus\Badwords;
use \Common\Validator\Validator;
use Common\Msg\ResponseMsg;

class CheckController extends Controller
{

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis)
        {
            $client_ip = get_client_ip();
            $nowTime = time();
            $redis = RedisEx::getInstance();
            //Check禁止IP规则次数
            define('CIPFLN', $redis->hGet('LimitSetting', 'CIPFLN'));
            //Check禁止IP规则时间
            define('CIPFLT', $redis->hGet('LimitSetting', 'CIPFLT'));
            //Check禁止IP持续时间
            define('CIPFCT', $redis->hGet('LimitSetting', 'CIPFCT'));
            //公司服务端ip数组
            $ip_arr = getAllServerIps($redis);
            if (!in_array($client_ip, $ip_arr))
            {
                $keyname = "checkTimeLog:" . str_replace(".", "_", $client_ip);
                $redis->lPush($keyname, $nowTime);
                $redis->expireAt($keyname, $nowTime + CIPFLT);
                $redis->lTrim($keyname, 0, CIPFLN - 1);
                $forbidenTime = $redis->zScore('checkForbidenIps', str_replace(".", "_", $client_ip));
                if ($forbidenTime && $forbidenTime > ($nowTime - CIPFCT))
                {
                    header("HTTP/1.1 403");
                    exit;
                }
                //取最新的规则次数检查时间日志
                $checkTime_range = $redis->lRange($keyname, 0, CIPFLN - 1);
                //计算日志长度，如果大于规则次数才需要做判断
                $checkLen = count($checkTime_range);
                if ($checkLen >= CIPFLN)
                {
                    //计算最近规则次数检查的时间间隔
                    $timeRange = $checkTime_range[0] - $checkTime_range[CIPFLN - 1];
                    //如果最近规则次数检查的时间间隔小于规则时间，则将IP加入禁止列表
                    if ($timeRange < CIPFLT)
                    {
                        //清理1小时之前的ip
                        $redis->zRemRangeByScore('checkForbidenIps', 0, $nowTime - CIPFCT);
                        //添加禁止IP
                        $redis->zAdd('checkForbidenIps', $nowTime, str_replace(".", "_", $client_ip));
                        if ($redis->exists('checkForbidenPersistentIps:' . str_replace(".", "_", substr($client_ip, 0, strrpos($client_ip, '.')))))
                        {
                            $ppIp = str_replace(".", "_", substr($client_ip, 0, strrpos($client_ip, '.')));
                            $pIps = $redis->sMembers('checkForbidenPersistentIps:' . $ppIp);
                            foreach ($pIps as $pIp)
                            {
                                $redis->zAdd('checkForbidenIps', $nowTime, $ppIp . '_' . $pIp);
                            }
                            $redis->sAdd('checkForbidenPersistentIps:' . $ppIp, substr($client_ip, strrpos($client_ip, '.') + 1));
                        }
                        header("HTTP/1.1 403");
                        exit;
                    }
                }
            }
        }
    }

    /**
     * 默认检查方式
     */
    public function actionIndex()
    {
        $type = $_REQUEST['type'];
        $memberModel = loadModel('member');
        if ($type == 'username')
        {
            $username = iconv('UTF-8', 'GBK', $_POST['username']);
//            $badwords = new Badwords(10);
//            if ($badwords->filter($username))
//            {
//                echo 2;
//            }
//            else
//            {
                echo $memberModel->checkUser($username);
//            }
        }
        elseif ($type == 'email' || $type == 'emailUsername')
        {
            if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL))
            {
                echo 1;
            }
            else
            {
                if ($memberModel->checkEmail($_POST['email']))
                {
                    echo 1;
                }
                else
                {
                    echo 0;
                }
            }
        }
        elseif ($type == 'verifiedemail')
        {
            if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL))
            {
                echo 1;
            }
            else
            {
                echo $memberModel->checkVerifiedEmail($_POST['email']);
            }
        }
        elseif ($type == 'validate')
        {
            session_start();
            $_POST['val'] = iconv('UTF-8', 'GBK', $_POST['val']);
            if (isset($_GET["flg"]) && $_GET["flg"] == "loadjs")
            {
                if (isset($_GET['val']) && $_GET['val'] === '' . $_SESSION['checkIMGCode_new'])
                {
                    echo "checkCallback('validate',1);";
                }
                else
                {
                    echo "checkCallback('validate',0);";
                    unset($_SESSION['checkIMGCode_new']);
                }
            }
            else
            {
                if (isset($_POST['val']) && $_POST['val'] === '' . $_SESSION['checkIMGCode_new'])
                {
                    echo 1;
                }
                else
                {
                    echo 0;
                    unset($_SESSION['checkIMGCode_new']);
                }
            }
        }
        else
        {
            if ($type == 'captcha')
            {
                session_start();
                $_POST['val'] = iconv('UTF-8', 'GBK', $_POST['val']);
                if (isset($_GET["flg"]) && $_GET["flg"] == "loadjs")
                {
                    if (isset($_GET['val']) && $_GET['val'] === '' . $_SESSION['captcha_code'])
                    {
                        echo "checkCallback('validate',1);";
                    }
                    else
                    {
                        echo "checkCallback('validate',0);";
                        unset($_SESSION['captcha_code']);
                    }
                }
                else
                {
                    if (isset($_POST['val']) && $_POST['val'] === '' . $_SESSION['captcha_code'])
                    {
                        echo 1;
                    }
                    else
                    {
                        echo 0;
                        unset($_SESSION['captcha_code']);
                    }
                }
            }
        }
    }

    /**
     * jsonp形式检查
     */
    public function actionJsonp()
    {
//        loadAction('checkIP');
//        if (!CheckIPAction::checkISChinaIP(get_client_ip())) {
//            exit;
//        }
        /* -----接口埋点----- */
        $logPath = APPPATH . "/logs/" . date("Ymd") . "/apiCheckPhone";
        deepMkdir($logPath);
        $logger = new Octopus\Logger("apiCheckPhone");
        $logger->pushHandler(new \Octopus\Logger\Handler\StreamHandler($logPath . "/" . date('H') . ".log"));
        $userClient_ip = get_client_ip();
        $logger->info("\"{$_SERVER['REQUEST_URI']}\" \"HTTP_REFERER:{$_SERVER['HTTP_REFERER']}\" \"HTTP_USER_AGENT:{$_SERVER['HTTP_USER_AGENT']}\" \"ip:$userClient_ip\"");

        $type = $_GET['type'];
        $value = $_GET['value'];
        $isUtf8 = isset($_GET['utf8']);
        if ($isUtf8)
        {
            $value = mb_convert_encoding($value, "GBK", "UTF-8");
        }
        $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
        
        $userClientIp = get_client_ip();
        $regAction = loadAction('reg');
        $isShowCode = $regAction->regCheckShowCode($userClientIp);
        if (!$isShowCode)
        {
            $DateAnalyzeAction = loadAction('DateAnalyze');
            $DateAnalyzeAction->setDateSlice('reg','rename');
        }
        
        if ($type == 'username')
        {
            $memberModel = loadModel('member');
            if (mb_strlen($value, "GBK") < 2)
            {
                echo "$callback(3);";
            }
            else
            {
                // 验证器， 禁词
                $validator = new Validator();
                $validator->addValidator($value, Validator::BAD_WORDS, array("昵称"));
                list($res, $msg) = $validator->validate();
                if ($res != ResponseMsg::SUCCESS)
                {
                    // 存在禁词
                    echo "$callback(2);";
                }
                else
                {
                    $ret = $memberModel->checkUser($value);
                    echo "$callback($ret);";
                }
            }
        }
        elseif ($type == 'email')
        {
            $loginAction = loadAction('login');
            $info = $loginAction->checkAuthCookie();
            if ($info)
            {
                $passid = $info['i'];
            }
            else
            {
                $passid = 0;
            }
            $status = (isset($_GET['status']) && $_GET['status'] == 0) ? 0 : 1;
            $withUsername = (isset($_GET['with']) && $_GET['with'] == 0) ? 0 : 1;
            if (!filter_var($value, FILTER_VALIDATE_EMAIL))
            {
                $ret = 1;
            }
            else
            {
                if ($passid)
                {
                    $emailAction = loadAction('email');
                    $ret = $emailAction->check($passid, $value, $status);
                    $ret = explode(".", $ret);
                    if ($ret[0] == 300)
                    {
                        $ret = $ret[1] + 1;
                    }
                    else
                    {
                        $ret = 0;
                    }
                }
                else
                {
                    $memberModel = loadModel('member');
                    $ret = $memberModel->checkEmail($value, $status, $withUsername);
                }
            }
            echo "$callback($ret);";
        }
        elseif ($type == "phone")
        {
            $loginAction = loadAction('login');
            $info = $loginAction->checkAuthCookie();
            if ($info)
            {
                $passid = $info['i'];
            }
            else
            {
                $passid = 0;
            }
            $withUsername = (isset($_GET['with']) && $_GET['with'] == 0) ? 0 : 1;
            $judgeRegion = (isset($_GET['region']) && $_GET['region'] == 1) ? 1 : 0;
            $regex = Config::get('regex');
            if (!preg_match($regex['phone'], $value))
            {
                $ret = 1;
            }
            else
            {
                if ($passid)
                {
                    $phoneAction = loadAction('phone');
                    $ret = $phoneAction->check($passid, $value);
                    $ret = explode(".", $ret);
                    if ($ret[0] == 300)
                    {
                        $ret = $ret[1] + 1;
                    }
                    else
                    {
                        $ret = 0;
                    }
                }
                else
                {
                    $memberModel = loadModel('member');
                    $ret = $memberModel->checkPhone($value, $withUsername);
                }
            }
            if ($ret == 0 && $judgeRegion)
            {
                $limitTelConfig = include(APPPATH . '/config/limitTelConfig.php');
                if (isset($limitTelConfig[substr($value, 0, 5)]) || isset($limitTelConfig[substr($value, 0, 6)]) || isset($limitTelConfig[substr($value, 0, 7)]))
                {
                    $ret = -1;
                }
            }
            echo "$callback($ret);";
        }
        elseif ($type == 'validate')
        {
            session_start();
            $_GET['val'] = iconv('UTF-8', 'GBK', $_GET['val']);
            if (isset($_GET["flg"]) && $_GET["flg"] == "loadjs")
            {
                $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
                if (isset($_GET['val']) && $_GET['val'] === '' . $_SESSION['checkIMGCode_new'])
                {
                    echo $callback . "({'validate':'1'});";
                }
                else
                {
                    echo $callback . "({'validate':'0'});";
                    unset($_SESSION['checkIMGCode_new']);
                }
            }
            else
            {
                if (isset($_POST['val']) && $_POST['val'] === '' . $_SESSION['checkIMGCode_new'])
                {
                    echo 1;
                }
                else
                {
                    echo 0;
                    unset($_SESSION['checkIMGCode_new']);
                }
            }
        }
    }

}