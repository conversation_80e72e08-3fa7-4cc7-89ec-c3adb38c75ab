<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/11/5
 * Time: 14:04
 */
includeBase('AdminController');
use Service\Encryption\Aes\AesManager;

class LogoffLogController extends AdminController
{
    protected $authName = '账号注销日志';

    /**
     * 注销列表
     * -
     * @return void
     */
    public function actionIndex()
    {
        $pageArray = [];
        $LogOffModel = loadModel('LogOff');

        $queryData = [
            'userType' => !empty($_POST['userType']) ? $_POST['userType'] : '',
            'userName' => !empty($_POST['userName']) ? $_POST['userName'] : '',
        ];

        $pageAction = loadAction('Page');
        $logoffList = $LogOffModel->getLogoffLogList($queryData, $pageAction->returnPageConfig(30));
        foreach ($logoffList as &$val) {
            $val['phoneEncrypt'] = (new AesManager())->aes128cbcEncrypt($val['phone']);
            $val['phone'] = empty($val['phone']) ? $val['phone'] : $this->userlogoHidden($val['phone'], 'phone');
            $val['emailEncrypt'] = (new AesManager())->aes128cbcEncrypt($val['email']);
            $val['email'] = empty($val['email']) ? $val['email'] : $this->userlogoHidden($val['email'], 'email');
            $logoffInfo = json_decode($val['logoffInfo'], true);
            $logoffInfo['phone'] = empty($logoffInfo['phone']) ? $logoffInfo['phone'] : $this->userlogoHidden($logoffInfo['phone'], 'phone');
            $logoffInfo['email'] = empty($logoffInfo['email']) ? $logoffInfo['email'] : $this->userlogoHidden($logoffInfo['email'], 'email');
            $val['logoffInfo'] = json_encode($logoffInfo);
        }
        $pageArray['logoffList'] = $logoffList;

        $queryData['count'] = true;
        $logCount = $LogOffModel->getLogoffLogList($queryData);
        $pageArray['userName'] = $queryData['userName'];
        $pageArray['userType'] = $queryData['userType'];
        $pageArray['showPage'] = $pageAction->showPage($logCount['total'], '/houtai_gl/LogoffLog');
        loadView('admin/logoff_log.tpl.html', $pageArray);
    }
}
