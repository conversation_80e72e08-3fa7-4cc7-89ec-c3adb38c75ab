<!DOCTYPE HTML>
<html>
<head>
    <meta charset="gb2312" />
    <title>2345网址导航用户中心-设置用户名</title>
    <style type="text/css">
        body{ font:12px/1.5 \5FAE\8F6F\96C5\9ED1,\5b8b\4f53;background:#fff; color:#333;}
        html,body,p,dl,dt,dd,table,td,th,input,img,form,div,span,ul,ol,li,h1,h2,h3,h4,h5,h6,select,fieldset,fieldset,input,button,sub,sup,textarea{margin:0;padding:0; }
        table {border-collapse:collapse; border-spacing:0;}
        h1,h2,h3,h4,h5,h6 {font-size:100%; font-weight:normal;}
        iframe,img{ border:0 none;}
        img{ vertical-align:middle;}
        em,i{font-style: normal;}
        ul,li,ol{list-style:none outside none;}
        .clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
        .clearfix{*zoom:1;}
        a{ text-decoration:none;color:#666}
        a:hover{ text-decoration:underline;color:#f30}
        :focus{ outline:0;}
        .clear{ clear:both; overflow:hidden; font-size:0; height:0;line-height:0;}
        .mt10{ margin-top:10px;}
        .mt90{ margin-top:90px;}

        .main { margin: 0 auto; width: 640px;}
        .m-boxA { height:296px; padding: 12px 20px; background-color: #fff; *zoom: 1; }
        .m-boxA-hd { position: relative; height: 38px; line-height: 38px; border-bottom: 1px solid #eeeeee; *zoom: 1; }
        .m-boxA-hd .more { position: absolute; right: 0; color: #338ad1; }
        .m-boxA-hd .more:hover { color: #ff3300; }
        .m-boxA-tit { float: left; margin-top: -2px; font-size: 14px; border-bottom: 2px solid #338ad1; }
        .m-boxA-bd { padding: 12px 0 0; }

        .weiboTxt{ text-align:center; font-size:18px; color:#666; font-family:\5FAE\8F6F\96C5\9ED1, Microsoft YaHei; line-height:46px;}
        .weiboTxt a,.shelvesTxt a{ color:#166cbb;}
        .shelvesTxt{ font-size:18px; color:#666; font-family:\5FAE\8F6F\96C5\9ED1, Microsoft YaHei; margin-top:40px;}
        .shelvesTit{ height:46px;}
        .shelvesTit .m-boxA-tit{ font-size:18px; color:#666; font-family:\5FAE\8F6F\96C5\9ED1, Microsoft YaHei; margin-top:0px; border:0; padding-bottom:5px;}
        .shelvesForm .form-field{ color:#666;}

        /*m-form*/
        .m-form .form-item { position: relative; margin: 15px 0 0 0; padding: 0 0 0 112px; line-height: 32px; font-size: 14px; *zoom: 1; }
        .m-form .form-item-radio .piece { margin-right: 8px; cursor: pointer; color: #666666; }
        .m-form .form-item-radio .ipt_radio { margin-right: 5px; }
        .m-form .form-item-top { padding-left: 0; }
        .m-form .form-field { position: absolute; left: 0; width: 108px; text-align: right; padding-right: 4px; }
        .m-form .form-tips { margin-left: 7px; color: #999999; }
        .m-form .form-tips-error { color: #ff6600; }
        .m-form .form-tips .icon-error { margin-right: 8px; }
        .m-form .yzmCon { display: inline-block; margin-right: 10px; width: 148px; font-family:Arial; font-weight:bold;}
        .m-form .form-pwd-tips { color: #ff3f00; }
        .m-form .form-pwd-tips i { display: inline-block; vertical-align: middle; margin: 0 2px; width: 17px; height: 4px; overflow: hidden; border: 1px solid #ff3f00; }
        .m-form .form-pwd-tips i.on { background-color: #ff3f00; }
        .m-form .form-pwd-tips em { margin-left: 5px; vertical-align: middle; }
        .m-form .form-pwd-tips-2 { color: #ffc600; }
        .m-form .form-pwd-tips-2 i { border-color: #ffc600; }
        .m-form .form-pwd-tips-2 i.on { background-color: #ffc600; }
        .m-form .form-pwd-tips-3 { color: #4dc214; }
        .m-form .form-pwd-tips-3 i { border-color: #4dc214; }
        .m-form .form-pwd-tips-3 i.on { background-color: #4dc214; }
        .m-form .form-item-cur .ipt_radio { background-position: -5px -31px; }
        .m-form .form-item-cur .ipt_txt { color: #333333; border-color: #499fe6; }
        .m-form .goPrev { margin-left: 10px; color: #2371c8; }
        .m-form .goPrev:hover { color: #ff3300; }
        .btn-blueA { display: inline-block; vertical-align: middle; width: 112px; height: 31px; line-height: 31px; background-color: #338ad1; border: 1px solid #186cb1; text-align: center; color: #ffffff; font-size: 14px; cursor: pointer; -webkit-border-radius: 3px; -moz-border-radius: 3px; border-radius: 3px; }
        .btn-blueA:hover { text-decoration: none; color: #ffffff; background-color: #499fe6; border-color: #2a84ce; }

        /*form ele*/
        .ipt_txt { width: 246px; padding: 4px 12px; height: 22px; line-height: 22px; font-size: 12px; border: 1px solid #c6ced6; color: #333333; vertical-align: middle; font-family: \5b8b\4f53; -moz-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; -webkit-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; }
        .ipt_txt.ipt_defa { color: #999999; }
        .ipt_txt.ipt_txt_error { border-color: #ff6600; }
        .ipt_txt.ipt_txt_cur { color: #333333; border-color: #499fe6; }

        .ipt_radio { display: inline-block; vertical-align: middle; width: 16px; height: 16px; overflow: hidden; background-position: -5px -5px; }
        .ipt_radio_cur { background-position: -5px -31px; }

        .m-selectA { display: inline-block; vertical-align: middle; *display: inline; *zoom: 1; border: 1px solid #d9d9d9; width: 200px; background-position: 0 -165px; background-repeat: repeat-x; height: 30px; line-height: 30px; margin-right: 12px; color: #848484; position: relative; width: auto; min-width: 94px; z-index: 3; font-size: 12px; cursor: pointer; }
        .m-selectA .text { min-width: 40px; padding: 0 6px; border-right: 1px solid #ededed; float: left; text-align: center; cursor: pointer; }
        .m-selectA .holder { width: 40px; height: 30px; border-left: 1px solid #fff; overflow: hidden; position: relative; font-size: 0; cursor: pointer; float: left; }
        .m-selectA .option { position: absolute; width: 100%; _width: expression(this.parentNode.offsetWidth-2); background-color: #fbfbfb; height: 155px; border: 1px solid #d9d9d9; margin: -1px 0 0 -1px; top: 31px; left: 0; overflow: auto; }
        .m-selectA .option li { padding: 0 6px; height: 30px; line-height: 30px; overflow: hidden; border-bottom: 1px solid #eee; }
        .m-selectA .option li a { color: #666; display: block; }
        .m-selectA .option li a:hover { color: #ff4800; text-decoration: none; }
        .m-selectA .arrow-btm { width: 13px; height: 7px; background-position: -186px -115px; position: absolute; top: 12px; left: 15px; }

        .icon-ok-big, .icon-error, .ipt_radio {
            background: url(/images/v2/uc_icon.png) no-repeat;
        }

        .icon-ok-big {
            display: inline-block;
            vertical-align: middle;
            width: 46px;
            height: 46px;
            background-position: 0 -103px;
        }

    </style>
    <!--<link rel="stylesheet" type="text/css" href="/css/v3/global.css">-->
    <!--<link rel="stylesheet" type="text/css" href="/css/v3/common.css">-->
    <link rel="stylesheet" type="text/css" href="/css/v2/uc.css">
    <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
</head>
<body style="background:#fff;">

<div class="main">

    {{if $pageArray.patched}}

    <div class="m-boxA mt10">
        <div class="m-boxA-hd shelvesTit">
            <h2 class="m-boxA-tit">温馨提示：完善用户名后，可用该用户名登录</h2>
        </div>

        <div class="retShow">

            <table>
                <tbody>
                <tr>
                    <td>

                        <i class="icon-show icon-ok-big"></i>

                        <div class="retCon">
                            <div class="name">完善用户名成功！</div>
                            <div class="tips" id="forwardTips">
                                3秒后关闭窗口, 请使用用户名登录</a>
                            </div>
                        </div>

                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <script type="text/javascript">
            var times = 2;
            setInterval(function () {
                if (times > 0) {
                    $("#forwardTips").html(times + '秒后关闭窗口, 请使用用户名登录');
                } else {
                    chrome.sync.closeDlg();
                }
                times--;
            }, 1000);
        </script>

    </div>

    {{else}}

    <div class="m-boxA mt10">
        <div class="m-boxA-hd shelvesTit">
            <h2 class="m-boxA-tit">2345帐号不再支持微博登录，请完善用户名和密码后再登录</h2>
        </div>
        <div class="m-boxA-bd">
            <div class="ucfnBox">
                <div class="m-form">
                    <form id="user-form" action="" method="post" onsubmit="return checkAll()">
                        <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
                        <input type="hidden" name="pwd_strength" id="pwd_strength"/>

                        <div class="form-item">
                            <span class="form-field">用户名：</span>
                            <input class="ipt_txt ipt_defa" type="text" autocomplete="off" id="username" name="username"
                                   onblur="checkUser()"/>
                            <span id="msg_username" class="form-tips"></span>
                        </div>
                        <div class="form-item">
                            <span class="form-field">密码：</span>
                            <input class="ipt_txt ipt_defa" type="password" autocomplete="off" id="password"
                                   name="password" onblur="checkPass();"/>
                            <span id="msg_pwdstr" style="display: none;"></span>
                            <span id="msg_password" class="form-tips">6-16个字符（区分大小写）</span>
                        </div>
                        <div class="form-item">
                            <span class="form-field">重复新密码：</span>
                            <input class="ipt_txt ipt_defa" type="password" autocomplete="off" id="repassword"
                                   onblur="checkRepass()"/>
                            <span id="msg_repassword" class="form-tips">最少6个字符</span>
                        </div>
                        <div class="form-item">
                            <a class="btn-blueA" href="#" onclick="$('#user-form').submit();
                                                        return false;">保存</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        {{/if}}

    </div>
</div>


<div style="display: none">
    <script type="text/javascript" src="//web.50bangzh.com/js/userc2345"></script>
</div>

<script type="text/javascript">
    var userErr = 1;

    String.prototype.trim = function () {
        return this.replace(/(^\s*)|(\s*$)/g, "");
    };

    //换算中文字长
    String.prototype.cnSize = function () {
        var arr = this.match(/[^\x00-\xff]/ig);
        return this.length + (arr == null ? 0 : arr.length);
    };

    function checkUser() {
        username = $("#username").val().trim();
        if (username.cnSize() < 2) {
            userErr = 1;
            $("#msg_username").html('<i class="icon-error"></i>最少2个字符');
            $("#msg_username").addClass('form-tips-error');
            $("#msg_username").show();
            return false;
        }
        if (username.cnSize() > 24) {
            userErr = 1;
            $("#msg_username").html('<i class="icon-error"></i>请不要超过24个字符');
            $("#msg_username").addClass('form-tips-error');
            $("#msg_username").show();
            return false;
        }
        if (/[^\u4E00-\u9FA5\w_@\.\-]/.test(username) || /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(username) || /^1[0123456789]\d{9}$/.test(username)) {
            userErr = 1;
            $("#msg_username").html('<i class="icon-error"></i>请输入汉字，字母，数字');
            $("#msg_username").addClass('form-tips-error');
            $("#msg_username").show();
            return false;
        }

        $.ajax({
            url: "/api/check/jsonp",
            data: 'type=username&value=' + username,
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function (response) {
                err = response;
                if (err > 0) {
                    if (err == 1) {
                        $("#msg_username").html('<i class="icon-error"></i>此用户名已被注册，请重新输入');
                    }
                    else if (err == 2) {
                        $("#msg_username").html('<i class="icon-error"></i>这个用户名不适合您，换一个吧');
                    }
                    else if (err == 3) {
                        $("#msg_username").html('<i class="icon-error"></i>最少2个字符');
                    }
                    userErr = 1;
                    $("#msg_username").addClass('form-tips-error');
                    $("#msg_username").show();
                    return false;
                }

                userErr = 0;
                if (username != username.toLowerCase()) {
                    $("#msg_username").html('<i class="icon-error"></i>登录区分大小写，请牢记您的用户名');
                    $("#msg_username").addClass('form-tips-error');
                    $("#msg_username").show();
                }
                else {
                    $("#msg_username").html('<img src="/images/check.jpg" style="margin: 8px 0 0;" />');
                    $("#msg_username").removeClass('form-tips-error');
                    $("#msg_username").show();
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                userErr = 1;
            },
            timeout: 3000
        });
    }

    function checkPass() {
        var pass = $("#password").val();
        if (pass.length < 6) {
            $("#msg_password").html('<i class="icon-error"></i>最少6个字符');
            $("#msg_password").addClass('form-tips-error');
            $("#msg_password").show();
            $('#msg_pwdstr').hide();
            return false;
        }
        if (pass.length > 16) {
            $("#msg_password").html('<i class="icon-error"></i>最多16个字符');
            $("#msg_password").addClass('form-tips-error');
            $("#msg_password").show();
            $('#msg_pwdstr').hide();
            return false;
        }
        if (pass == $('#username').val()) {
            $("#msg_password").html('<i class="icon-error"></i>密码不能与用户名一致，请重新输入');
            $("#msg_password").addClass('form-tips-error');
            $("#msg_password").show();
            $('#msg_pwdstr').hide();
            return false;
        }
        if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false) {
            $("#msg_password").html('<i class="icon-error"></i>您的密码过于简单，请重新输入');
            $("#msg_password").addClass('form-tips-error');
            $("#msg_password").show();
            $('#msg_pwdstr').hide();
            return false;
        }
        var score = passwordGrade(pass);
        if (score <= 8) {
            $('#pwd_strength').val(1);
            $('#msg_password').html('<i class="icon-error"></i>您的密码过于简单，请重新输入');
            $("#msg_password").addClass('form-tips-error');
            $("#msg_password").show();
            $('#msg_pwdstr').attr("class", "form-pwd-tips form-pwd-tips-1").html('<i class="on"></i><i></i><i></i><i></i><em>弱</em>').show();
            return false;
        }
        else if (score >= 9 && score <= 16) {
            $('#pwd_strength').val(2);
            $("#msg_password").hide();
            $('#msg_pwdstr').attr("class", "form-pwd-tips form-pwd-tips-2").html('<i class="on"></i><i class="on"></i><i></i><i></i><em>中</em>').show();
        }
        else if (score >= 17 && score <= 24) {
            $('#pwd_strength').val(3);
            $("#msg_password").hide();
            $('#msg_pwdstr').attr("class", "form-pwd-tips form-pwd-tips-3").html('<i class="on"></i><i class="on"></i><i class="on"></i><i></i><em>强</em>').show();
        }
        else {
            $('#pwd_strength').val(4);
            $("#msg_password").hide();
            $('#msg_pwdstr').attr("class", "form-pwd-tips form-pwd-tips-3").html('<i class="on"></i><i class="on"></i><i class="on"></i><i class="on"></i><em>极强</em>').show();
        }
        $("#msg_password").html('<img src="/images/check.jpg" style="margin: 8px 0 0;" />');
        $("#msg_password").removeClass('form-tips-error');
        $("#msg_password").show();
        return true;
    }

    function checkPassSame(pass) {
        var first = pass.substring(0, 1);
        var exp = new RegExp('^' + first + '+$');
        if (exp.test(pass)) {
            return false;
        }

        if (first == 'a' || first == 'A') {
            f = pass.charCodeAt(0);
            for (i = 1; i < pass.length; i++) {
                tmp = pass.charCodeAt(i);
                if (tmp - f != i) {
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    function passwordGrade(pwd) {
        var score = 0;
        var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
        var repeatCount = 0;
        var prevChar = '';

        //check length
        var len = pwd.length;
        score += len > 18 ? 18 : len;

        //check type
        for (var i = 0, num = regexArr.length; i < num; i++) {
            if (eval('/' + regexArr[i] + '/').test(pwd))
                score += 4;
        }

        //bonus point
        for (var i = 0, num = regexArr.length; i < num; i++) {
            if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
                score += 2;
            if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
                score += 2;
        }

        //deduction
        for (var i = 0, num = pwd.length; i < num; i++) {
            if (pwd.charAt(i) == prevChar)
                repeatCount++;
            else
                prevChar = pwd.charAt(i);
        }
        score -= repeatCount * 1;

        return score;
    }

    function checkRepass() {
        var pass = $("#repassword").val();
        if (pass.length < 6) {
            $("#msg_repassword").html('<i class="icon-error"></i>最少6个字符');
            $("#msg_repassword").addClass('form-tips-error');
            $("#msg_repassword").show();
            return false;
        }
        if (pass.length > 16) {
            $("#msg_repassword").html('<i class="icon-error"></i>最多16个字符');
            $("#msg_repassword").addClass('form-tips-error');
            $("#msg_repassword").show();
            return false;
        }
        if ($("#repassword").val() != $("#password").val()) {
            $("#msg_repassword").html('<i class="icon-error"></i>两次输入密码不一致');
            $("#msg_repassword").addClass('form-tips-error');
            $("#msg_repassword").show();
            return false;
        }
        $("#msg_repassword").html('<img src="/images/check.jpg" style="margin: 8px 0 0;" />');
        $("#msg_repassword").removeClass('form-tips-error');
        $("#msg_repassword").show();
        return true;
    }

    function checkAll() {
        checkUser();
        if (userErr) {
            return false;
        }
        if (!checkPass()) {
            return false;
        }
        if (!checkRepass()) {
            return false;
        }
        return true;
    }

    $(".ipt_txt").bind({
        focus: function () {
            $(this).addClass("ipt_txt_cur");
            $(this).removeClass('ipt_txt_error');
            $(this).parent('.form-item').find(".form-tips-error").hide();
        },
        blur: function () {
            $(this).removeClass("ipt_txt_cur");
        }
    });

</script>

</body>
</html>


