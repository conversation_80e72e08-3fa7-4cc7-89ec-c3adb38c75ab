<?php
/**
 * smsid:1;total:1;success:1;lose:0 成功时返回的样例
 * 失败时返回具体错误
 * 
 * 文件名称：class.ZhengaoSmsChannel2.php
 * 
 * @copyright Copyright (c) 2013，上海二三四五网络科技股份有限公司
 * <AUTHOR> 
 * @group 互联网研发中心/积分开发组
 * @version 1.0
 * @date 2015/4/29
 */

namespace Smschannel;
use Action;
use FuncAction;

class ZhengaoSmsChannel2Action extends SmsChannelAction
{


    /**
     * 发送短信，适用
     * @param string $phone 手机号
     * @param string $msg   短信内容
     * @return RET_SUCC 发送成功    RET_ERR 发送失败
     */
    public function send($phone, $msg)
    {
        $msg_u8 = iconv('GBK','UTF-8', $msg);

        $params = array(
            'loginname'	=> $this->user,
            'password' 	=> $this->pwd,
            'content' 	=> $msg_u8,
            'mobile' 	=> $phone,
            'extNo'		=>	''
        );
        $response = FuncAction::postTimeout($this->apiUrl, $params, 6);
        $result = self::responseStatus($response);
        
        //由于可能存在curl超时的情况，没有返回值，但是发送成功了， 所以这儿判断失败
        if ($result)
        {
            return self::RET_SUCC;
        }
        else
        {
            return self::RET_ERR;
        }
    }


    private static function responseStatus($response)
    {
        if(strpos($response, 'smsid:') !== false)
        {
            $info = array();
            $infoCollects = explode(';', $response);
            foreach($infoCollects as $val)
            {
                list($field, $value) = explode(':', $val);
                $info[$field] = $value;
            }
            if(isset($info['success']) && $info['success'] >= '1')
            {
                return $info;
            }
        }
        return false;
    }



}