<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/11
 * Time: 10:08
 */
namespace Service\Config\Conf;

class ExtAndbyhls
{
    private static $config;

    /**
     * 获取配置信息
     * -
     * @return array
     */
    public static function get()
    {
        if (!empty(self::$config) && is_array(self::$config))
        {
            return self::$config;
        }
        else
        {
            self::$config = [
                "push" => [
                    "sync" => false,  //是否异步推送  可选
                    "callbackUrl" => \dev::getDevDomain("http://h5.2345.com/LyApp/Login"),  //回调推送URL  可选 为空不进行推送
//                    "callbackUrl" => "http://172.16.0.56:8003/s.php"
                ],
                'aesConfig' => [
                    'aesKey' => 'kxjLlrVgI89u9DTz',  //必要参数
                    'aesIv' => 'l4g8t711qKqF8f58',   //必要参数
                ],
                'MultiClientConfig' => [
                    'isMultiClient' => false,
//                    'validTokenNum' => 3,    //不设置默认10个队列长度
                    'loginPrivateKey' => 'C51joSAHqwIOISc9ppjdjlDKBGL9jXHz',   //必要字段
//                    'expired' => 3600 // 20天  不设置默认20天
                ],
                'signKey' => "UyWdWEVctMRp17SkYwMCNa1EEPoCn1fP",  //数据签名密钥  必要参数
            ];
            return self::$config;
        }
    }
}
