<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：AudioVerifyAction.php
 * 摘    要：处理语音发送
 * 作    者：张超
 * 修改日期：2016.01.08
 * */
class AudioVerifyAction extends Action
{
	/**
	 * fs监听esl端口
	 */
	const FS_PORT = '8021';

	/**
	 * fs连接密码
	 */
	const FS_PW = '2345.com';

	/**
	 * 电话送出
	 */
	const STATUS_OK = 200;

	/**
	 * 线路用完
	 */
	const STATUS_CALL_FULL = 400;

	/**
	 * fs服务器连接失败
	 */
	const STATUS_CONNECT_ERR = 500;

	/**
	 * 最大线路数
	 */
	const MAX_CONNECT = 28;

	/**
	 * redis键
	 */
	const DATA_KEY = 'audio_verify';

	/**
	 * fs地址
	 */
	static private $fs_addr = array(
			
			'*************',
			'*************',
			'*************',
			'*************',
			'*************',
			'*************',
		);

	/**
	 * 类实例
	 */
	static public $instance;

	/**
	 * esl实例
	 */
	static private $sock_out;

	/**
	 * redis实例
	 */
	static private $redis;
	
	/*
	private function __construct($config)
	{

		self::$redis = new Redis();
		self::$redis->connect($config['REDIS']['host'], $config['REDIS']['port']);
		self::$redis->auth($config['REDIS']['password']);
		self::$redis->select(6);
	}
	*/

	static public function getInstance()
	{
		if (!isset(self::$instance))
		{
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 * 外呼函数
	 * @param  [string] $phone [被叫手机号]
	 * @return [int]        [状态码]
	 */
	public static function callOut($phone, $from)
	{
		/*
		foreach (self::$fs_addr as $each_addr) {
			$sock = new ESLconnection($each_addr, self::FS_PORT, self::FS_PW);
			self::$sock_out = new ESLconnection($sock->socketDescriptor());

			$curr_fs_status = $this->_currFsStatus();
			if ($curr_fs_status === true)
			{
				break;
			}
		}*/

		$sock = new ESLconnection('sip.2345.cn', self::FS_PORT, self::FS_PW);
		self::$sock_out = new ESLconnection($sock->socketDescriptor());
		$curr_fs_status = self::_currFsStatus();

		if ($curr_fs_status !== true)
		{
			return $curr_fs_status;
		}
		$rand_files = self::_getRandom($phone);
		$ver_code = $rand_files[0];
		// $stime = strtotime(date('Y-m-d') . '09:30');
		// $etime = strtotime(date('Y-m-d') . '23:30');
		$path_str = "pstn_vos_one"; //(time() >= $stime && time() <= $etime) ? 'pstn_yidong_dx' : 'pstn_yidong_dx';
        $bgapi = "originate {codec_string=pcmu,ignore_early_media=true,playback_delimiter=&}sofia/gateway/" . $path_str . "/" . $phone . " &playback(" . $rand_files[1] . ")" . " XML default audio_ver_" .$from. " " . $phone;
		//echo $bgapi;
        $ret = self::$sock_out->bgapi($bgapi);
		if (!isset($ret))
		{
			unset($ver_code);
			//self::_pushIntoList($phone);
			return self::STATUS_CONNECT_ERR;
		}
		return array(self::STATUS_OK, $ver_code);
	}

        /**
         * @desc 通过ESL脚本外呼
         * @date 2015-08-05 10:56:41
         * @name chenmh
         *
         * @param $phone 手机号
         * @param $from 请求来源
         * @param $rand_files 验证码信息 0code 1file
         * @param $codeId 数据库记录ID
         *
         * @return array|bool|int
         */
	public static function callOutByLua($phone, $from, $rand_files, $codeId)
	{
		$ver_code = $rand_files[0];

		$sip = isset($_GET['DEBUG_SIP']) ? '***************' : 'sip.2345.cn';
		$sock = new ESLconnection($sip, self::FS_PORT, self::FS_PW);
		// $sock = new ESLconnection('***************', self::FS_PORT, self::FS_PW);
		self::$sock_out = new ESLconnection($sock->socketDescriptor());
		$curr_fs_status = self::_currFsStatus();

		if ($curr_fs_status !== true)
		{
			return $curr_fs_status;
		}

		$cmdString = "luarun voice.lua {$phone} {$ver_code} audio_ver_{$from} {$codeId}";
		$ret = self::$sock_out->bgapi($cmdString);
		if (!isset($ret))
		{
			unset($ver_code);
			return array(self::STATUS_CONNECT_ERR, "");
		}
		return array(self::STATUS_OK, $ver_code);
	}

	/**
	 * 判断fs服务器状态
	 * @return [mix] [true|int]
	 */
	private static function _currFsStatus()
	{
		$calls_num_str = self::$sock_out->api('show calls count')->getBody();
		if (!isset($calls_num_str) || empty($calls_num_str))
		{
			//$this->_pushIntoList($phone);
			return self::STATUS_CONNECT_ERR;
		}
		/*
		list($num,) = explode($calls_num_str, ' ');
		if ((int)$num >= self::MAX_CONNECT)
		{
			self::_pushIntoList($phone);
			return self::STATUS_CALL_FULL;
		}
		*/
		return true;
	}


	/**
	 * 生成随机数
	 * @return [array] [随机数和拼接后的媒体文件路径]
	 */
	public static function _getRandom($phone)
	{
		$rand = '';
		$files = '/opt/fsfile/sounds/voice/prefix.wav&';
		$redis = self::openRedis(6);
		$vcode = $redis->get($phone);
		if($vcode)
		{
			$vcode = strval($vcode);
		}
		for ($i = 0; $i < 4; $i++) {
			if($vcode)
			{
				$this_one = $vcode[$i];
			}
			else
			{
				$this_one = (string)mt_rand(0, 9);
			}
			$rand .= $this_one;
			$files .= '/opt/fsfile/sounds/voice/' . $this_one . '.wav&';
		}
		
		if(!$vcode)
		{
			$redis->setex($phone, 1800, $rand);
			
		}
		$files .= $files . $files;

		$files = substr($files, 0, -1);

		return array($rand, $files);
	}

	/**
	 * 外呼失败的入队列
	 * @param  [string] $phone [入队号码]
	 */
	private function _pushIntoList($phone)
	{
		self::$redis->rpush(self::DATA_KEY, $phone);
	}

	/**
	 * 从队列取出之前失败号码重试
	 * @return [string] [被叫号码]
	 */
	public function dealList()
	{
		$val = self::$redis->blpop(self::DATA_KEY, 0);
		return $val[1];
	}

	/**
	 *功	能：打开redis连接
	 */
	private static function openRedis($dbnum = 0)
	{
		$redis = new Redis();
		$redis->connect('172.16.16.161', '6379');
		$redis->auth('rc_2345_redis');
		$redis->select($dbnum);
		return $redis;
	}
}