{{include file="header.tpl.html"}}
<style type="text/css">
	* {border: 0;margin: 0;padding: 0;}
	.ui-dialog{text-align: center;background: #fff;border: 0 none;border-collapse: collapse;border-spacing: 0;margin: 0;width: auto;border-radius: 6px;}
	.ui-dialog-header{text-align:left;}
	.ui-dialog-title{height: 36px;line-height: 36px;padding: 0 15px;font-size: 16px;font-weight: bold;}
	.ui-dialog-close{font-size: 24px;margin-right:8px;font-weight: normal;cursor: pointer;display: inline-block;float: right;background:#fff;}
	.ui-dialog-close:hover{color: #f5694b;}
	.ui-dialog-content{padding:14px;line-height:22px;width:480px;text-align: left;}
	.ui-dialog-autofocus{font-size: 14px;width: 100px;height:30px;cursor: pointer;margin-bottom:15px;}
</style>
<div class="wrap">
	<div class="box clearfix">
        <div class="tit clearfix">
			<span class="titleft"></span>
			<span class="titright"></span>
			<h2>记事本</h2></div>
		<div class="boxbd clearfix">
			<div class="module_1 height560">
				<div id="show_jsb" class="set-cnt">
				</div>

				<!--add modify add_jsb start-->
				<div style="DISPLAY: none;opacity: 0.3; FILTER: alpha(opacity=80); LEFT: 0px; WIDTH: 450px; POSITION: absolute; TOP: 0px; HEIGHT: 300px; BACKGROUND-COLOR: #888; z-index:200;" id="divBg"><iframe frameborder="0" src="about:blank" style="position:absolute;z-index:-1;width:100%;height:100%;top:0;left:0;scrolling:no;"></iframe></div>

				<div disabled="" style="DISPLAY: none; FILTER: alpha(opacity=100); LEFT: 1px; WIDTH: 410px; POSITION: absolute; TOP: 1px; HEIGHT: 366px; z-index:999;" id="add_jsb" class="alarm">
					<h2><em id="add_jsb_title">添加记事</em></h2>
					<form target="ajax_fav" onsubmit="return c_add_jsb();" method="post" action="/notebook/jsb_action.php">
						<div class="alarm-bd">
							<p>请输入记事本内容(限500字)：<span class="zs2">还可以输入<font id="j_Cont" class="red">500</font>字</span></p>
							<p><textarea class="alarm-box" style="height: 160px;" id="jCont" name="jCont" cols="" rows=""></textarea></p>
							<p class="edit-tool"><em id="addTime"></em></p>
							<p class="lb"><b>选择分类：</b><a onclick="modifySort(1,'fl01')" href="javascript:void(0);" id="fl01" class="fl01 lb_a">便签</a>    <a class="fl02" id="fl02" onclick="modifySort(2,'fl02')" href="javascript:void(0);">计划</a>     <a class="fl03" id="fl03" onclick="modifySort(3,'fl03')" href="javascript:void(0);">备忘</a>     <a class="fl04" id="fl04" onclick="modifySort(4,'fl04')" href="javascript:void(0);">私密</a>     <a class="fl05" id="fl05" onclick="modifySort(5,'fl05')" href="javascript:void(0);">杂七杂八</a></p>
							<div><input type="hidden" value="1" id="jSort" name="jSort"><input type="hidden" value="" id="jsb_cmd" name="cmd"><input type="hidden" value="" id="add_jsb_id" name="add_jsb_id"><input type="hidden" value="" id="showType" name="showType"><input type="hidden" value="" id="old_cont" name="old_cont"><input type="hidden" value="" id="old_sort" name="old_sort"><input type="submit" class="butn" name="" value="确定">  <input type="button" onclick="closeDivS('add_jsb');" class="butn" name="" value="取消"></div></div>
					</form>
				</div>
				<!--add modify add_jsb end-->

				<!--del alert_ok start-->
				<div disabled="" style="DISPLAY: none; FILTER: alpha(opacity=100); LEFT: 1px; WIDTH: 300px; POSITION: absolute; TOP: 1px; z-index:999;" id="alert_ok" class="alarm">
					<h2><em id="alert_ok_font"></em></h2>
					<div class="alarm-bd">
						<div id="alert_ok_font2"></div><br><input type="button" onclick="getObj('mmForm').submit();" value="确认" name="alert_ok_focus" id="alert_ok_focus">&nbsp;&nbsp;&nbsp;<input type="button" value="取消" onclick="closeDivS('alert_ok');">
					</div>
				</div>
				<!--del alert_ok end-->

				<!--del del_ok start-->
				<div disabled="" style="DISPLAY: none; FILTER: alpha(opacity=100); LEFT: 1px; WIDTH: 300px; POSITION: absolute; TOP: 1px; z-index:999;" id="del_ok" class="alarm">
					<h2><em id="del_ok_font"></em></h2>
					<form target="ajax_fav" name="" action="/notebook/jsb_action.php" method="post">
						<div class="alarm-bd">
							<div id="del_ok_font2"></div>
							<input type="hidden" value="del_jsb" name="cmd" id="del_ok_cmd">
							<input type="hidden" value="" name="del_id" id="del_id">
							<input type="hidden" value="" name="showTypeDel" id="showTypeDel">
							<input type="hidden" value="" name="jSortDel" id="jSortDel">
							<input type="submit" value="确 认" name="ok" id="ok_focus">&nbsp;&nbsp;&nbsp;<input type="button" value="取 消" onclick="closeDivS('del_ok');">
						</div>
					</form>
				</div>
				<!--del del_ok end-->

				<div disabled="" style="DISPLAY: none; FILTER: alpha(opacity=100); LEFT: 1px; WIDTH: 410px; POSITION: absolute; TOP: 1px; HEIGHT: 317px; z-index:999;" id="add_tx" class="alarm">
					<h2><span id="txTitle"></span></h2>
					<form target="ajax_fav" onsubmit="return c_tx_jsb();" method="post" action="/notebook/jsb_action.php">
						<div class="alarm-bd">
							<div class="alarm-tt"><b><a onclick="ch" href="javascript:void(0)">请选择时间：</a></b></div>
							<p id="txSelectTime"></p>
							<p>提醒内容：<span class="zs">还可以输入<font id="tx_Cont" class="red">500</font>字</span></p>
							<p><textarea rows="" cols="" id="txCont" name="txCont" class="alarm-box"></textarea></p>
							<p>提醒方式：<input type="checkbox" id="txfs" checked="checked" value="1">电子邮件  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" disabled="disabled"><font class="gray">手机短信[敬请期待]</font></p>
							<div><input type="hidden" value="" name="cmd" id="tx_cmd"><input type="hidden" value="" name="tx_jsbid" id="tx_jsbid"><input type="hidden" value="" name="tx_id" id="tx_id"><input type="hidden" value="" name="tx_showType" id="tx_showType"><input type="hidden" value="" name="tx_p" id="tx_p"><input type="hidden" value="" name="tx_sort" id="tx_sort"><input type="hidden" value="" name="old_txCont" id="old_txCont"><input type="hidden" value="" name="old_txTime" id="old_txTime"><input type="submit" value="确定" name="" class="butn">  <input type="button" value="取消" onclick="closeDivS('add_tx');" class="butn"></div></div>
					</form>
				</div>
				<iframe style="display:none;" id="ajax_fav" name="ajax_fav"></iframe>
			</div>




		</div>         
	</div>
</div>
<script src="/js/my2011.js?v=1" type="text/javascript"></script>
<script src="/js/notebook.js?v=1" type="text/javascript"></script>
<script type="text/javascript" src="/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="/js/dialog-min.js"></script>
<script>
    jQuery.noConflict();
    jQuery(document).ready(
        function() {
            var d = dialog({
                title: '尊敬的2345的用户',
                content: '<div class="shang-body"><p>您好。由于业务调整，[记事本]模块预计将在2019年3月底下线，请您及时备份数据，给大家造成的不变还请谅解！</p></div>',
                quickClose: false,
                okValue: '我知道了',
                ok: function () {},
                cancelValue: '关闭',
            });
            d.showModal();
        }
    );
</script>
<!-- footer -->
{{include file="footer.tpl.html"}}