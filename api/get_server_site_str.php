<?php
/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 摘　　要：获取云收藏数据接口
 * 作　　者：熊小明
 * 修改日期：2012.12.27
 */

ini_set('display_errors', 'Off');

define("BASE_PATH", realpath(dirname(__FILE__) . "/../") . "/");
require_once BASE_PATH . "/include/init.php";

include_once BASE_PATH . "/config.inc.php";

if (!IS_LOGIN)
{
    die('');
}

if (UID > 0)
{
    $pdo = \Octopus\PdoEx::getInstance(DB_MY_2345, $g_db_config[DB_MY_2345]);
    $sql = "select site from fav_myset WHERE uid = :uid limit 1";
    $info = $pdo->find($sql, array(":uid" => UID));
    $mySite = str_replace(array("\r", "\n"), "", $info['site']);
    $mySite = str_replace(array("\"", "'"), "", $mySite);

    if ($mySite == "null" || $mySite == '')
    {
        $mySite = "T.siteClicks.callback()";
    }
    else
    {
        $mySite = "T.siteClicks.callback('" . $mySite . "')";
    }
    echo $mySite;
}
exit;
