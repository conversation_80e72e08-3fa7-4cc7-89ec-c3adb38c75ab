<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：JvChenSmsChannelAction.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：10/09/2018 15:41
 */

namespace Smschannel;

class JvChenSmsChannelAction extends SmsChannelAction
{
    const RESPONSE_CODE_SUCCESS = 0;
    const RESPONSE_CODE_PARAMS_REQUIRED = 100;
    const RESPONSE_CODE_USERNAME_ERROR = 101;
    const RESPONSE_CODE_PWD_ERROR = 102;
    const RESPONSE_CODE_PHONE_AMONG_LIMIT = 103;
    const RESPONSE_CODE_CONTENT_AMONG_LIMIT = 104;
    const RESPONSE_CODE_INSUFFICIENT_BALANCE = 105;
    const RESPONSE_CODE_IP_VERIFY_ERROR = 107;
    const RESPONSE_CODE_SIGN_ERROR = 109;
    const RESPONSE_CODE_KEYWORD_ERROR = 110;
    const RESPONSE_CODE_ID_ERROR = 113;
    const RESPONSE_CODE_EXPEND_CODE_ERROR = 114;
    const RESPONSE_CODE_SEND_TIME_FORMAT_ERROR = 115;
    const RESPONSE_CODE_INVALID_PHONE = 116;
    const RESPONSE_CODE_ACCOUNT_BANNED = 117;
    const RESPONSE_CODE_NOT_VALID_MSG = 118;
    const RESPONSE_CODE_INVALID_JSON = 119;
    const RESPONSE_CODE_CRM_INFO_ERROR = 120;
    const RESPONSE_CODE_CONTENT_GARBLED_ERROR = 121;
    const RESPONSE_CODE_OTHER_ERROR = 200;
    public static $responseCodeText = [
        self::RESPONSE_CODE_SUCCESS                => '提交成功',
        self::RESPONSE_CODE_PARAMS_REQUIRED        => '缺少必填参数',
        self::RESPONSE_CODE_USERNAME_ERROR         => '用户名错误',
        self::RESPONSE_CODE_PWD_ERROR              => '密码错误',
        self::RESPONSE_CODE_PHONE_AMONG_LIMIT      => '手机号码超限',
        self::RESPONSE_CODE_CONTENT_AMONG_LIMIT    => '内容长度超限',
        self::RESPONSE_CODE_INSUFFICIENT_BALANCE   => '余额不足',
        self::RESPONSE_CODE_IP_VERIFY_ERROR        => 'IP地址校验错误',
        self::RESPONSE_CODE_SIGN_ERROR             => '签名错误',
        self::RESPONSE_CODE_KEYWORD_ERROR          => '关键字错误',
        self::RESPONSE_CODE_ID_ERROR               => '合批ID长度超过限制或含有非数字',
        self::RESPONSE_CODE_EXPEND_CODE_ERROR      => '扩展码错误',
        self::RESPONSE_CODE_SEND_TIME_FORMAT_ERROR => '发送时间格式错误',
        self::RESPONSE_CODE_INVALID_PHONE          => '无效的手机号',
        self::RESPONSE_CODE_ACCOUNT_BANNED         => '账号被禁用',
        self::RESPONSE_CODE_NOT_VALID_MSG          => '没有检测到有效条数',
        self::RESPONSE_CODE_INVALID_JSON           => '无效的一对一JSON格式',
        self::RESPONSE_CODE_CRM_INFO_ERROR         => '获取CRM客户信息错误',
        self::RESPONSE_CODE_CONTENT_GARBLED_ERROR  => '内容包含乱码字符',
        self::RESPONSE_CODE_OTHER_ERROR            => '系统其他错误',
    ];

    /**
     * JvChenSmsChannelAction constructor.
     *
     * @param string $user username
     * @param string $pwd passwd
     * @param string $apiUrl api url
     */
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }

    /**
     * User: panj
     *
     * @param array $phone phone list
     * @param string $msg msg content
     * @param string $sign sign
     *
     * @return int
     */
    public function send($phone, $msg, $sign)
    {
        $phoneStr = $phone;
        $multi = 0;
        if (is_array($phoneStr))
        {
            $phoneStr = implode(",", $phoneStr);
            $multi = 1;
        }
        $msg = $sign . $msg;
        $params = [
            'username'   => $this->user,
            'passwd'     => $this->pwd,
            'phone'      => $phoneStr,
            'msg'        => \EncodingAction::convertEncoding($msg, 'utf-8'),
            'needstatus' => true,
        ];
        $response = \FuncAction::postTimeout($this->apiUrl, $params);
        $response = \EncodingAction::transcoding($response, 'utf-8');
        $resultArr = json_decode($response, true);
        if ($resultArr && $resultArr['respcode'] == self::RESPONSE_CODE_SUCCESS)
        {
            if (!$multi)
            {
                $this->msgId = $resultArr['batchno'];
                $this->OperatorMsg = array(
                    'result'     => self::RET_SUCC,
                    'codeStatus' => 0,
                    'codeDesc'   => '成功',
                );

                return self::RET_SUCC;
            }
            else
            {
                /**@var array $phone */
                foreach ($phone as $phoneLine)
                {
                    $this->msgIdList[$phoneLine] = $resultArr['batchno'];
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_SUCC,
                        'codeStatus' => 0,
                        'codeDesc'   => '成功',
                    );
                }

                return self::RET_SUCC;
            }
        }
        else
        {
            if (!$multi)
            {
                $this->msgId = 0;
                $this->OperatorMsg = array(
                    'result'     => self::RET_ERR,
                    'codeStatus' => - 1,
                    'codeDesc'   => self::$responseCodeText[$resultArr['respcode']],
                );

                return self::RET_ERR;
            }
            else
            {
                /**@var array $phone */
                foreach ($phone as $phoneLine)
                {
                    $this->msgIdList[$phoneLine] = 0;
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_ERR,
                        'codeStatus' => - 1,
                        'codeDesc'   => self::$responseCodeText[$resultArr['respcode']],
                    );
                }

                return self::RET_ERR;
            }
        }
    }

    /**
     * 相同内容多个手机号群发
     *
     * @param array  $phone [phone1,phone2,phone3]
     * @param string $msg   message
     * @param string $sign  签名
     *
     * @return int
     */
    public function multiPhoneSend($phone, $msg, $sign)
    {
        $this->send($phone, $msg, $sign);
    }
}
