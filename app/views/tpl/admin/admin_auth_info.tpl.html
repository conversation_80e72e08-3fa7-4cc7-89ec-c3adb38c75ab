<!DOCTYPE html>
<html>
<head>
<meta charset="gb2312">
<title>弹窗</title>
<link rel="stylesheet" href="/css/auth/styles/global.css">
</head>
<body>
<!--透明遮罩-->
<div class="boxMask">透明遮罩层</div>

<!--添加用户 弹窗 开始-->
<div class="boxMod w740" style="display:block" style="left:0;">
    <div class="boxTop">
        {{if !$pageArray.username}}
            <h2 class="boxTit">添加用户</h2>
        {{else}}
            <h2 class="boxTit">编辑用户</h2>
        {{/if}}
        <a class="BoxClose" href="/houtai_gl/admin_auth">关闭按钮</a>

    </div>
    <div class="tabModA mtb20">
        <div class="mtrbl10">
            <form method="post" action="{{$pageArray.action}}">
            <table class="tabFromMod" cellpadding="0" cellspacing="0">
                {{if !$pageArray.username}}
                <tr>
                    <td class="tabLeftTit">用户名称：</td>
                    <td><input name="username" class="tabInput" type="text" autocomplete="off" /></td>
                </tr>
                {{else}}
                <tr>
                    <td class="tabLeftTit">用户名称：</td>
                    <td>{{$pageArray.username}}</td>
                </tr>
                {{/if}}
                <tr>
                    <td class="tabLeftTit">功能模块：</td>
                    <td>

                        <label class="checkLabel"><input type="checkbox" id='selAll' onClick='selectAll()' />全选</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="登录用户统计" {{if in_array("登录用户统计", $pageArray.auth_list)}}checked{{/if}} />登录用户统计</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="用户登录查询" {{if in_array("用户登录查询", $pageArray.auth_list)}}checked{{/if}} />用户登录查询</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="注册用户统计" {{if in_array("注册用户统计", $pageArray.auth_list)}}checked{{/if}} />注册用户统计</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="用户信息查询" {{if in_array("用户信息查询", $pageArray.auth_list)}}checked{{/if}} />用户信息查询</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="注册用户编辑" {{if in_array("注册用户编辑", $pageArray.auth_list)}}checked{{/if}} />注册用户编辑</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="用户头像审核" {{if in_array("用户头像审核", $pageArray.auth_list)}}checked{{/if}} />用户头像审核</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="昵称查询与编辑" {{if in_array("昵称查询与编辑", $pageArray.auth_list)}}checked{{/if}} />昵称查询与编辑</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="登录注册IP管理" {{if in_array("登录注册IP管理", $pageArray.auth_list)}}checked{{/if}} />登录注册IP管理</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="可疑注册IP" {{if in_array("可疑注册IP", $pageArray.auth_list)}}checked{{/if}} />可疑注册IP</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="注册禁止用户名" {{if in_array("注册禁止用户名", $pageArray.auth_list)}}checked{{/if}} />注册禁止用户名</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="Redis状态" {{if in_array("Redis状态", $pageArray.auth_list)}}checked{{/if}} />Redis状态</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="查询限制信息" {{if in_array("查询限制信息", $pageArray.auth_list)}}checked{{/if}} />查询限制信息</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="最近登录IP日志" {{if in_array("最近登录IP日志", $pageArray.auth_list)}}checked{{/if}} />最近登录IP日志</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="服务器IP管理" {{if in_array("服务器IP管理", $pageArray.auth_list)}}checked{{/if}} />服务器IP管理</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="设置API禁止域名" {{if in_array("设置API禁止域名", $pageArray.auth_list)}}checked{{/if}} />设置API禁止域名</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="权限管理" {{if in_array("权限管理", $pageArray.auth_list)}}checked{{/if}} />权限管理</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="注册防刷统计" {{if in_array("注册防刷统计", $pageArray.auth_list)}}checked{{/if}} />注册防刷统计</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="每天注册比" {{if in_array("每天注册比", $pageArray.auth_list)}}checked{{/if}} />每天注册比</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="管理员操作日志" {{if in_array("管理员操作日志", $pageArray.auth_list)}}checked{{/if}} />管理员操作日志</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="账号注销日志" {{if in_array("账号注销日志", $pageArray.auth_list)}}checked{{/if}} />账号注销日志</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="广告位管理" {{if in_array("广告位管理", $pageArray.auth_list)}}checked{{/if}} />广告位管理</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="联合登录包名签名管理" {{if in_array("联合登录包名签名管理", $pageArray.auth_list)}}checked{{/if}} />联合登录包名签名管理</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="联合登录设置管理" {{if in_array("联合登录设置管理", $pageArray.auth_list)}}checked{{/if}} />联合登录设置管理</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="项目配置列表" {{if in_array("项目配置列表", $pageArray.auth_list)}}checked{{/if}} />项目配置列表</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="通知日志" {{if in_array("通知日志", $pageArray.auth_list)}}checked{{/if}} />通知日志</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="数据迁移管理" {{if in_array("数据迁移管理", $pageArray.auth_list)}}checked{{/if}} />数据迁移管理</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="头像昵称规则配置" {{if in_array("头像昵称规则配置", $pageArray.auth_list)}}checked{{/if}} />头像昵称规则配置</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="编辑头像昵称规则" {{if in_array("编辑头像昵称规则", $pageArray.auth_list)}}checked{{/if}} />编辑头像昵称规则</label>
                        <label class="checkLabel"><input name="auth[]" type="checkbox" value="用户标识展示" {{if in_array("用户标识展示", $pageArray.auth_list)}}checked{{/if}} />用户标识展示</label>
                    </td>

                </tr>
                <tr>
                    <td align="center" colspan="2">
                        {{if $pageArray.username}}
                            <input type="hidden" name="username" value="{{$pageArray.username}}" />
                        {{/if}}
                        <input type="submit" name="submit" value="保存" class="styleBtnBlue tabsave" style="border: 0" />
                    </td>
                </tr>
            </table>
            </form>
        </div>
    </div><!--tabModA end-->
</div><!--boxMod end-->
<!--添加用户 弹窗 结束-->
</body>
</html>
<script src="/js/jquery.min.js"></script>
<script src="/css/auth/boxLocation.js"></script>
<script type="text/javascript">
function selectAll()
{
  var obj = document.getElementsByName("auth[]");
  if (document.getElementById("selAll").checked == false)
  {
	  for(var i=0; i<obj.length; i++)
	  {
		obj[i].checked=false;
	  }
  }
  else
  {
	  for(var i=0; i<obj.length; i++)
	  {
		obj[i].checked=true;
	  }
  }
}
</script>
<!--此js开发人员无须使用 结束-->











