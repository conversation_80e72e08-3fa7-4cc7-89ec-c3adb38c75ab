<?php

includeBase('CronController');
/**
 * 用户注册队列
 */
class RegQueueController extends CronController
{
    private $regQueueKey = REG_QUEUE_REDIS_LIST_KEY;
    private $sleepTimeLength = 5;
    private $maxExecuteTime = 55;

    /**
     * RegQueueController constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * actionIndex
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $startTime = time();
        $logger = $this->getLogger('regQueue');
        $regQueueRedis = loadAction('regQueueRedis');
        $queueEmptyCount = 0;
        $regCount = 0;
        $regSuccessCount = 0;
        $regFailureCount = 0 ;
        $memberModel = loadModel('member');
        $regInfo = array();

        while (time() - $startTime < $this->maxExecuteTime)
        {
            $regQueueLength = $regQueueRedis->regQueueLength();
            if ($regQueueLength <= 0)
            {
                $queueEmptyCount++;
                if ($queueEmptyCount > 10000)
                {
                    $logger->info('注册队列本分钟内第' . $queueEmptyCount . '次长度为0,循环间隔时间为:' . $this->sleepTimeLength);
                    sleep($this->sleepTimeLength);
                }

                continue;
            }
            else
            {
                if ($queueEmptyCount > 0)
                {
                    $queueEmptyCount = 0;
                }
            }

            try
            {
                $regInfo = $regQueueRedis->popRegQueue('');

                if (empty($regInfo) || count($regInfo) == 0)
                {
                    $logger->notice('用户注册数据为空');
                    $regFailureCount++;
                    continue;
                }
                $regCount++;
                if (!isset($regInfo['PMKeyId']) || intval($regInfo['PMKeyId']) <= 0)
                {
                    $logger->notice('passId为空');
                    $regQueueRedis->pushToRegQueue($regInfo['PMKeyId'], $regInfo);
                    $regFailureCount++;
                    continue;
                }

                $regInfoKeyArray = array(
                    'members',
                    'members_info',
                    'members_phone',
                );
                foreach ($regInfoKeyArray as $key)
                {
                    if (!isset($regInfo[$key]) || !is_array($regInfo[$key]) || count($regInfo[$key]) <= 0)
                    {
                        $logger->notice('注册信息中:' . $key . '为空, 注册数据:' . serialize($regInfo));
                        $regQueueRedis->pushToRegQueue($regInfo['PMKeyId'], $regInfo);
                        $regFailureCount++;
                        continue;
                    }
                }

                $regResult = $memberModel->regOtherTableForQueue($regInfo);
                if (!$regResult)
                {
                    $regFailureCount++;
                    $logger->notice('passId:' . $regInfo['passId'] . ',入库失败, 注册数据:' . serialize($regInfo));
                    $regQueueRedis->pushToRegQueue($regInfo['passId'], $regInfo);
                }
                else
                {
                    $regSuccessCount++;
                }
            }
            catch (Error $er)
            {
                $regFailureCount++;
                $logger->notice('注册队列执行注册逻辑时发生Error');
                $regQueueRedis->pushToRegQueue($regInfo['PMKeyId'], $regInfo);
            }
        }

        $regQueueRedis->closeRegQueueRedis();
        PdoEx::delInstance(DB_PASSPORT);
        $logger->info('本分钟注册队列执行结束,共处理[' . $regCount . ']条数据,成功[' . $regSuccessCount . ']条,失败[' . $regFailureCount . ']条');
        exit();
    }
}
