/*M版选择省市区*/
function addressM(input){

	var $name = input;
	var data1=[];
	var data2=[];
	var data3=[];
	//初始化data1;添加省，直辖市份

	for(var j=0;j<jd_address[0].children.length;j++){
		var s=jd_address[0].children[j]
		var names = jd_address[s].name
		data1.push({
			text:names,
			value:s
		})
	}
	//初始化 添加北京地区二级区
	for(var j=0;j<jd_address[1].children.length;j++){
		var s=jd_address[1].children[j]
		var names = jd_address[s].name
		data2.push({
			text:names,
			value:s
		})
	}
	//初始化 添加北京地区中的三级区
	for(var j=0;j<jd_address[72].children.length;j++){
		var s=jd_address[72].children[j]
		var names = jd_address[s].name
		data3.push({
			text:names,
			value:s
		})
	}

	var data = [data1,data2,data3]

	$name.picker({
		data: [data1, data2, data3],
		selectIndex: [0, 1, 2]

	}).on('picker.select', function (e, selectVal, selectIndex) {

		var flag1 = jd_address[data1[selectIndex[0]].value].children.indexOf(selectVal[1])

		var flag2 = jd_address[data2[selectIndex[1]].value].children.indexOf(data3[selectIndex[2]].value)

		if(flag1!=-1 && flag2 !=-1){

			$(this).val(data1[selectIndex[0]].text + ' ' + data2[selectIndex[1]].text + ' ' + data3[selectIndex[2]].text);
		}else if(flag1!=-1 && flag2 ==-1){


			var num = selectVal[1];
			data3=initLi(num,data3)

			$name.picker('refill', data3, 2);

			$(this).val(data1[selectIndex[0]].text + ' ' + data2[selectIndex[1]].text + ' ' + data3[0].text);
		}else if(flag1==-1){

			var num = selectVal[0]
			data2=initLi(num,data2)

			var num2 = data2[0].value
			data3=initLi(num2,data3)

			//重填二级市区
			$name.picker('refill', data2, 1);
			//重填三级县区
			$name.picker('refill', data3, 2);

			$(this).val(data1[selectIndex[0]].text + ' ' +  data2[0].text + ' ' + data3[0].text);

		}


	}).on('picker.change', function (e, index, selectIndex) {

		if(index==0){


			var num = data1[selectIndex].value
			data2=initLi(num,data2)

			var num2 = data2[0].value
			data3=initLi(num2,data3)

			//重填二级市区
			$name.picker('refill', data2, 1);
			//重填三级县区
			$name.picker('refill', data3, 2);
			
		}else if(index==1){

			var num = data2[selectIndex].value
			data3=initLi(num,data3)
			//重填三级县区
			$name.picker('refill', data3, 2);
		}

	})

	$name.on('click', function () {
		$(this).picker('show');
	});
}


function initLi(num,arr){
	arr=[];
	for(var j=0;j<jd_address[num].children.length;j++){
		
		var s=jd_address[num].children[j]

		var names = jd_address[s].name
		arr.push({
			text:names,
			value:s
		})
	}
	return arr;
}