<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/6/6
 * Time: 10:46
 */
use Service\ThirdParty\Auth\WxXcx;

includeBase('CronController');

class WxXcxController extends CronController
{
    /**
     * 微信小程序重载access_token
     * @param string $appid appid
     * @return void
     */
    public function actionIndex($appid)
    {
        $warningAction = loadAction('Warning');
        $warningAction->setRequestInfo(
            [
                'dir_type' => 'xcx_cron',
                'type'     => 'reload_access',
                'path'     => '/cron/WxXcx/index',
                'appid'    => $appid,
            ]
        );
        try {
            $isReload = WxXcx::getWxXcxAccessToken($appid);
            if ($isReload !== false) {
                $warningAction->setRequestInfo(['code' => 200])->flush();
            } else {
                $warningAction->setRequestInfo(['code' => 500])->flush();
            }
        }
        catch (Exception $exception) {
            $warningAction->setRequestInfo(['code' => 500, 'reloadError' => $exception->getMessage()])->flush();
        }
    }
}
