<?php

use Cache\Adapter\Apcu\ApcuCachePool;
use Vault\CacheClientByUserPass;
use Octopus\Logger;
use Octopus\Logger\Handler\StreamHandler;


class TestController extends Controller
{
    public function actionTest()
    {
        return;
        $config = [
            'url' => $_ENV['vault_address'],
            'username' => $_ENV['vault_username'],
            'password' => $_ENV['vault_password'],
        ];
        $cache = new ApcuCachePool();

        $logPath = APPPATH . "/logs/" . date("Ymd") . "/runtime";
        $logger = new Logger("runtime");
        $logger->pushHandler(new StreamHandler($logPath . "/" . date('H') . ".log"));
        $client = new CacheClientByUserPass($config, $cache, $logger);
        $data = $client->getSecrets('ywzt_kv', 'login_secret');
        var_export($data);

    }
}
