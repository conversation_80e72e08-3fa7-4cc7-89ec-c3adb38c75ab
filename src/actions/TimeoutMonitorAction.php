<?php

class TimeoutMonitorAction extends Action
{

    const LOGTIMELIST = 'logtimelist_';

    protected static $logTimeKey = '';

    private static $dateArr = array();

    /**
     * TimeoutMonitorAction constructor.
     */
    public function __construct()
    {
        self::getLogTimeKey();
    }

    /**
     * 获取设置时间key
     *
     * @return null
     */
    public static function getLogTimeKey()
    {
        self::$logTimeKey = self::LOGTIMELIST . date('Y-m-d-H');
    }

    /**
     * 设置开始记录时间
     *
     * @param int    $logId    logId
     * @param string $phone    phone
     * @param array  $timeData time data
     *
     * @return null
     */
    public function setLogStartTime($logId, $phone, $timeData)
    {
        $getRedisAction = \GetRedisAction::getInstance();
        $data = $getRedisAction->redis->hGet(self::$logTimeKey, $phone);
        $now = date('Y-m-d H:i:s');
        if (empty($data))
        {
            $data = array(
                $logId => array(
                    'start'     => $now,
                    'channelId' => $timeData['channelId'],
                    'pid'       => $timeData['pid'],
                ),
            );
        }
        else
        {
            $data = json_decode($data, true);
            $data[$logId] = array(
                "start"     => $now,
                "channelId" => $timeData['channelId'],
                "pid"       => $timeData['pid'],
            );
        }
        self::$dateArr = array(
            'start'     => $now,
            'channelId' => $timeData['channelId'],
            'pid'       => $timeData['pid'],
        );
        $isTrue = $getRedisAction->redis->hSet(self::$logTimeKey, $phone, json_encode($data));
        $cacheDate = 3 * 24 * 60 * 60;
        $getRedisAction->redis->expire(self::$logTimeKey, $cacheDate);
    }

    /**
     * 设置结束时间
     *
     * @param int    $logId log id
     * @param string $phone phone
     *
     * @return null
     */
    public function setLogEndTime($logId, $phone)
    {
        $getRedisAction = \GetRedisAction::getInstance();
        $data = $getRedisAction->redis->hGet(self::$logTimeKey, $phone);
        $data = json_decode($data, true);
        //如果开始时间没有存入  调用程序内的参数
        if (!isset($data[$logId]))
        {
            $data[$logId] = array(
                "start"     => self::$dateArr['start'],
                "channelId" => self::$dateArr['channelId'],
                "pid"       => self::$dateArr['pid'],
            );
        }
        $nowTime = time();
        $data[$logId]['end'] = date('Y-m-d H:i:s', $nowTime);
        $diffDate = $nowTime - strtotime($data[$logId]['start']);
        $data[$logId]['diffDate'] = $diffDate;

        //如果现在的时间大于3了  那么设置结束时间
        if ($diffDate > 10)
        {
            //设置结束时间记录
            $getRedisAction->redis->hSet(self::$logTimeKey, $phone, json_encode($data));
        }
        else
        {
            //小于3s的 原始记录直接删除
            unset($data[$logId]);
            if (count($data) === 0)
            {
                $getRedisAction->redis->hDel(self::$logTimeKey, $phone);
            }
            else
            {
                $getRedisAction->redis->hSet(self::$logTimeKey, $phone, json_encode($data));
            }
        }
    }
}
