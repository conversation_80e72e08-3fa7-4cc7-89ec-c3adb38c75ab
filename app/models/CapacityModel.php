<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/3/13
 * Time: 15:59
 */

use \Service\Capacity\GetRangeTable;
use \Service\Capacity\GetHashTable;
use Common\Confidential\Sensitive;

class CapacityModel extends Model
{
    private $pdo;
    private $mainPdo;

    /**
     * 初始化
     * -
     * AdminModel constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取passport pdo
     * @return \Octopus\PdoEx
     */
    protected function getPdo()
    {
        if (!is_object($this->pdo)) {
            $dbConfig = Config::get("database");
            $this->pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_BACK, $dbConfig[DB_PASSPORT_BACK]);
        }

        return $this->pdo;
    }

    /**
     * 获取新passport pdo
     * @return \Octopus\PdoEx
     */
    protected function getPdoMain()
    {
        if (!is_object($this->mainPdo)) {
            $dbConfig = Config::get("database");
            $this->mainPdo = \Octopus\PdoEx::getInstance(
                DB_PASSPORT_USER,
                $dbConfig[DB_PASSPORT_USER]
            );  //线上测试环境
        }

        return $this->mainPdo;
    }


    /**
     * 删除实例
     * @return void
     */
    public function delInstance()
    {
        PdoEx::delInstance(DB_PASSPORT_BACK);
        PdoEx::delInstance(DB_PASSPORT_USER);
        $this->pdo = null;
        $this->mainPdo = null;
    }


    /**
     * 获取范围内的passid
     * -
     * @param int $startPassId 起始passid
     * @param int $endPassId   结束passid
     * @param int $limit       默认获取条数
     * @return array|bool
     */
    public function getMembersList($startPassId, $endPassId, $limit = 200)
    {
        $limit = (int)$limit;
        $sql = "SELECT * FROM members WHERE id > :startPassId And id<=:endPassId ORDER BY ID ASC LIMIT {$limit}";
        $queryData = [
            ":startPassId" => $startPassId,
            ':endPassId'   => $endPassId,
        ];

        return $this->getPdo()->findAll($sql, $queryData);
    }

    /**
     * 获取用户主信息
     * -
     * @param int $passid 用户唯一ID
     * @return array|bool
     */
    public function getMemberByPassid($passid)
    {
        $sql = "SELECT * FROM members WHERE id = :passid";
        $member = $this->getPdo()->find($sql, [":passid" => $passid]);
        $memberToUtf8 = \Common\Utils\Encoding::transcoding($member, 'gbk', 'utf-8');
        $member = \Common\Utils\Encoding::transcoding($memberToUtf8);

        return $member;
    }

    /**
     * 获取用户信息
     * -
     * @param int $passid 用户唯一ID
     * @return array|bool
     */
    public function getMemberInfoByPassid($passid)
    {
        $sql = "SELECT * FROM members_info WHERE uid = :passid";
        $memberInfo = $this->getPdo()->find($sql, array(":passid" => $passid));
        $memberInfoToUtf8 = \Common\Utils\Encoding::transcoding($memberInfo, 'gbk', 'utf-8');
        $memberInfo = \Common\Utils\Encoding::transcoding($memberInfoToUtf8);

        return $memberInfo;
    }

    /**
     * 获取手机信息
     * -
     * @param int $passid 用户唯一ID
     * @return array|bool
     */
    public function getMembersPhone($passid)
    {
        $sql = "SELECT * FROM members_phone WHERE passid = :passid";

        return $this->getPdo()->find($sql, [":passid" => $passid]);
    }

    /**
     * 获取第三方绑定信息
     * -
     * @param int $passid 用户唯一ID
     * @return array|bool
     */
    public function getMembersBind($passid)
    {
        $sql = "SELECT * FROM members_bind WHERE passid = :passid";
        $membersBind = $this->getPdo()->findAll($sql, [":passid" => $passid]);
        $membersBindToUtf8 = \Common\Utils\Encoding::transcoding($membersBind, 'gbk', 'utf-8');
        $membersBind = \Common\Utils\Encoding::transcoding($membersBindToUtf8);

        return $membersBind;
    }


    /**
     * @param int $startPassId 起始的passid
     * @param int $endPassId   结束的passid
     * @return array|bool
     */
    public function getMembersCountByPassIdLimit($startPassId, $endPassId)
    {
        $sql = "SELECT count(id) as total FROM members WHERE id > :startPassId And id<=:endPassId";
        $queryData = [
            ":startPassId" => $startPassId,
            ':endPassId'   => $endPassId,
        ];

        return $this->getPdo()->find($sql, $queryData);
    }


    /*****************************获取老库数据对比迁移数据数量start**********************************/
    /**
     * @param int $startPassId 起始的passid
     * @param int $endPassId   结束的passid
     * @return array|bool
     */
    public function getMembersInfoCountByPassIdLimit($startPassId, $endPassId)
    {
        $sql = "SELECT count(uid) as total FROM members_info WHERE uid > :startPassId And uid<=:endPassId";
        $queryData = [
            ":startPassId" => $startPassId,
            ':endPassId'   => $endPassId,
        ];

        return $this->getPdo()->find($sql, $queryData);
    }

    /**
     * @param int $startPassId 起始的passid
     * @param int $endPassId   结束的passid
     * @return array|bool
     */
    public function getMembersPhoneCountByPassIdLimit($startPassId, $endPassId)
    {
        $sql = "SELECT count(passid) as total FROM members_phone WHERE passid > :startPassId And passid<=:endPassId";
        $queryData = [
            ":startPassId" => $startPassId,
            ':endPassId'   => $endPassId,
        ];

        return $this->getPdo()->find($sql, $queryData);
    }

    /**
     * @param int $startPassId 起始的passid
     * @param int $endPassId   结束的passid
     * @return array|bool
     */
    public function getMembersBindCountByPassIdLimit($startPassId, $endPassId)
    {
        $sql = "SELECT count(passid) as total FROM members_bind WHERE passid > :startPassId And passid<=:endPassId";
        $queryData = [
            ":startPassId" => $startPassId,
            ':endPassId'   => $endPassId,
        ];

        return $this->getPdo()->find($sql, $queryData);
    }

    /**
     * @param int $startPassId 起始的passid
     * @param int $endPassId   结束的passid
     * @return array|bool
     */
    public function getMembersEmailCountByPassIdLimit($startPassId, $endPassId)
    {
        $sql = "SELECT count(uid) as total FROM members_info WHERE uid > :startPassId And uid<=:endPassId AND email_status = :email_status";
        $queryData = [
            ":startPassId" => $startPassId,
            ':endPassId'   => $endPassId,
            'email_status' => 1,
        ];

        return $this->getPdo()->find($sql, $queryData);
    }

    /*****************************获取老库数据对比迁移数据数量end**********************************/


    /*--------------------------------下面是新库获取数据--------------------------------------*/


    /**
     * 获取新库的用户主信息
     * -
     * @param int $passid 用户唯一ID
     * @return array|bool
     * desc:该函数兼容加解密
     */
    public function getNewMemberByPassid($passid)
    {
        $membersTable = GetRangeTable::Members($passid);
        $sql = "SELECT * FROM {$membersTable} WHERE id = :passid";
        $user = $this->getPdoMain()->find($sql, [":passid" => $passid]);

        if (isset($user['phone'])) {
            $user['phone'] = Sensitive::Decode($user['phone']);
        }
        if (isset($user['email'])) {
            $user['email'] = Sensitive::Decode($user['email']);
        }
        return $user;
    }

    /**
     * 获取新库 用户主信息
     * -
     * @param int $passid 用户唯一ID
     * @return array|bool
     * desc:该函数兼容加解密
     */
    public function getNewMemberInfoByPassid($passid)
    {
        $membersInfoTable = GetRangeTable::MembersInfo($passid);
        $sql = "SELECT * FROM {$membersInfoTable} WHERE passid = :passid";
        $info = $this->getPdoMain()->find($sql, [":passid" => $passid]);
        return DecodeMembersInfo($info);
    }

    /**
     * 获取新库 手机号码对应关系
     * -
     * @param int $phone 手机号码
     * @return array|bool
     * desc:该函数兼容加解密
     */
    public function getNewMembersPhone($phone)
    {
        $membersPhoneTable = GetHashTable::MembersPhone($phone);
        $sql = "SELECT * FROM {$membersPhoneTable} WHERE phone = :phone or phone=:aesPhone";

        $data = $this->getPdoMain()->find($sql, [":phone" => $phone, ":aesPhone" => Sensitive::Encode($phone)]);
        if (isset($data['phone'])) {
            $data['phone'] = Sensitive::Decode($data['phone']);
        }
        return $data;
    }


    /**
     * 获取新库 用户名对应关系
     * -
     * @param string $username 用户名
     * @return array|bool
     */
    public function getNewMembersUsername($username)
    {
        $usernameU8 = \Common\Utils\Encoding::transcoding($username, 'gbk', 'utf-8');
        $membersUsernameTable = GetHashTable::MembersUsername($usernameU8);
        $sql = "SELECT * FROM {$membersUsernameTable} WHERE username = :username";
        $userNameInfo = $this->getPdoMain()->find($sql, [":username" => $username]);

        return $userNameInfo;
    }


    /**
     * 获取新库 邮箱对应关系
     * -
     * @param string $email 邮箱
     * @return array|bool
     * desc:该函数兼容加解密
     */
    public function getNewMembersEmail($email)
    {
        $membersEmailTable = GetHashTable::MembersEmail($email);
        $sql = "SELECT * FROM {$membersEmailTable} WHERE email = :email or email=:aesEmail";
        $emailInfo = $this->getPdoMain()->find($sql, [":email" => $email, ":aesEmail" => Sensitive::Encode($email)]);

        if (isset($emailInfo['email'])) {
            $emailInfo['email'] = Sensitive::Decode($emailInfo['email']);
        }
        return \Common\Utils\Encoding::transcoding($emailInfo);
    }


    /**
     * 获取新库第三方关系
     * @param string $openid 第三方绑定标识
     * @param string $type   第三方类型
     * @return array|bool|string
     */
    public function getNewMembersBind($openid, $type = '')
    {
        $membersBindTable = GetHashTable::MembersBind($openid);
        $sql = "SELECT * FROM {$membersBindTable} WHERE openid = :openid ";
        $param = [":openid" => $openid];
        if (!empty($type)) {
            $sql .= "  AND `type` = :type ";
            $param[":type"] = $type;
        }
        $bindInfo = $this->getPdoMain()->find($sql, $param);

        return \Common\Utils\Encoding::transcoding($bindInfo);
    }

    /**
     * 更新新老库的差异记录
     * -
     * @param array $data 更新信息
     * @return bool
     */
    public function updateRecord($data)
    {
//        $data = \Common\Utils\Encoding::transcoding($data, 'gbk', 'utf-8');
        $this->getPdoMain()->beginTransaction();
        $isTrue = true;
        foreach ($data as $key => $val) {
            $updateData = isset($data[$key]['update']) ? $data[$key]['update'] : [];
            $delData = isset($data[$key]['del']) ? $data[$key]['del'] : [];

            switch ($key) {
                case 'members':
                    //原表内进行更新修改
                    if (!empty($updateData) && is_array($updateData) && $isTrue !== false) {
                        $passid = $updateData['id'];
                        if (empty($passid)) {
                            $this->getPdoMain()->rollBack();

                            return false;
                        }
                        $updateData['openid'] = $updateData['openid'] ?: '{}';  //openid 是json格式，默认值必须是一个正常的json串

                        if (isset($updateData['phone'])) {
                            $updateData['phone'] = Sensitive::Encode($updateData['phone']);
                        }
                        if (isset($updateData['email'])) {
                            $updateData['email'] = Sensitive::Encode($updateData['email']);
                        }
                        $isTrue = $this->getPdoMain()->duplicate(GetRangeTable::Members($passid), $updateData);
                    }
                    break;
                case "membersInfo":
                    //原表内进行更新修改
                    if (!empty($updateData) && is_array($updateData) && $isTrue !== false) {
                        $passid = $updateData['passid'];
                        if (empty($passid)) {
                            $this->getPdoMain()->rollBack();

                            return false;
                        }

                        $updateData = EncodeMembersInfo($updateData);
                        $isTrue = $this->getPdoMain()->duplicate(GetRangeTable::MembersInfo($passid), $updateData);
                    }
                    break;
                case "membersUsername":
                    //hash分表 不一定分到那张表 先删除原先的数据 在添加数据
                    if (!empty($delData) && is_array($delData) && !empty($delData['username']) && $isTrue !== false) {
                        $usernameU8 = \Common\Utils\Encoding::transcoding($delData['username'], 'gbk', 'utf-8');
                        $delNameTable = GetHashTable::MembersUsername($usernameU8);
                        $condition = array(
                            'where'  => 'passid = :passid AND username = :username',
                            'params' => array(
                                ':passid'   => $delData['passid'],
                                ':username' => $delData['username'],
                            ),
                        );
                        $isTrue = $this->getPdoMain()->delete($delNameTable, $condition);  //删除老表数据
                    }

                    if (!empty($updateData) && is_array($updateData) && $isTrue !== false) {
                        $username = \Common\Utils\Encoding::transcoding($updateData['username'], 'gbk', 'utf-8');
                        $newNameTable = GetHashTable::MembersUsername($username);
                        //修改后，还是在当前表， 做更新
                        $isTrue = $this->getPdoMain()->duplicate($newNameTable, $updateData);
                    }
                    break;
                case "membersEmail":
                    if (!empty($delData) && is_array($delData) && !empty($delData['email']) && $isTrue !== false) {
                        $condition = array(
                            'where'  => 'passid = :passid AND (email = :email or email=:aesEmail)',
                            'params' => array(
                                ':passid' => $delData['passid'],
                                ':email'  => $delData['email'],
                                ':aesEmail' => Sensitive::Encode($delData['email']),
                            ),
                        );
                        $delEmailTable = GetHashTable::MembersEmail($delData['email']);
                        $isTrue = $this->getPdoMain()->delete($delEmailTable, $condition);  //删除老表数据
                    }
                    if (!empty($updateData) && is_array($updateData) && $isTrue !== false) {
                        $email = $updateData['email'];

                        if (isset($updateData['email'])) {
                            $updateData['email'] = Sensitive::Encode($updateData['email']);
                        }
                        $isTrue = $this->getPdoMain()->duplicate(GetHashTable::MembersEmail($email), $updateData);
                    }
                    break;
                case "membersPhone":
                    if (!empty($delData) && is_array($delData) && !empty($delData['phone']) && $isTrue !== false) {
                        $condition = array(
                            'where'  => 'passid = :passid AND (phone = :phone or phone=:aesPhone)',
                            'params' => array(
                                ':passid' => $delData['passid'],
                                ':phone'  => $delData['phone'],
                                ':aesPhone' => Sensitive::Encode($delData['phone']),
                            ),
                        );
                        $oldPhoneTable = GetHashTable::MembersPhone($delData['phone']);
                        $isTrue = $this->getPdoMain()->delete($oldPhoneTable, $condition);  //删除老表数据
                    }
                    if (!empty($updateData) && is_array($updateData) && $isTrue !== false) {
                        $newPhoneTable = GetHashTable::MembersPhone($updateData['phone']);
                        if (isset($updateData['phone'])) {
                            $updateData['phone'] = Sensitive::Encode($updateData['phone']);
                        }
                        $isTrue = $this->getPdoMain()->duplicate($newPhoneTable, $updateData);
                    }
                    break;
                case "membersBind":
                    //同步解绑信息
                    if (!empty($delData) && is_array($delData) && $isTrue !== false) {
                        foreach ($delData as $itemDel => $delInfo) {
                            $openid = $delData[$itemDel]['openid'];
                            $type = $delData[$itemDel]['type'];
                            $condition = array(
                                'where'  => 'openid = :openid AND type = :type',
                                'params' => array(
                                    ':openid' => $openid,
                                    ':type'   => $type,
                                ),
                            );
                            $bindTable = GetHashTable::MembersBind($openid);
                            $isTrue = $this->getPdoMain()->delete($bindTable, $condition);
                            if ($isTrue === false) {
                                break;   //如果错了就退出当前循环
                            }
                        }
                    }
                    //更新差异数据
                    if (!empty($updateData) && is_array($updateData) && $isTrue !== false) {
                        foreach ($updateData as $item => $info) {
                            $openid = $updateData[$item]['openid'];
                            $bindTable = GetHashTable::MembersBind($openid);
                            $isTrue = $this->getPdoMain()->duplicate($bindTable, $updateData[$item]);
                            if ($isTrue === false) {
                                break;   //如果错了就退出当前循环
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
            if ($isTrue === false) {
                $this->getPdoMain()->rollBack();

                return false;
            }
        }
        $this->getPdoMain()->commit();

        return true;
    }



    /*******************************新库数据数量比对查询************************************/

    /**
     * 获取新members的总数
     * @param int $startPassId 起始的passid
     * @param int $endPassId   结束的passid
     * @return int
     */
    public function getSyncMembersCountByPassIdLimit($startPassId, $endPassId)
    {
        $members1Table = GetRangeTable::Members($startPassId);
        $members2Table = GetRangeTable::Members($endPassId);
        $count = 0;
        if ($members1Table == $members2Table) {
            $sql = "SELECT count(*) as total FROM {$members1Table} WHERE id > :startPassId AND id<=:endPassId ";
            $members = $this->getPdoMain()->find($sql, [":startPassId" => $startPassId, ':endPassId' => $endPassId]);
            $count = $members['total'];
        } else {
            $sql = "SELECT count(*) as total FROM {$members1Table} WHERE id > :startPassId AND id<=:endPassId ";
            $members = $this->getPdoMain()->find($sql, [":startPassId" => $startPassId, ':endPassId' => $endPassId]);
            $count += $members['total'];
            $sql = "SELECT count(*) as total FROM {$members2Table} WHERE id > :startPassId AND id<=:endPassId ";
            $members2 = $this->getPdoMain()->find($sql, [":startPassId" => $startPassId, ':endPassId' => $endPassId]);
            $count += $members2['total'];
        }

        return $count;
    }

    /**
     * 获取新membersinfo的总数
     * @param int $startPassId 起始的passid
     * @param int $endPassId   结束的passid
     * @return int
     */
    public function getSyncMembersInfoCountByPassIdLimit($startPassId, $endPassId)
    {
        $members1Table = GetRangeTable::MembersInfo($startPassId);
        $members2Table = GetRangeTable::MembersInfo($endPassId);
        $count = 0;
        if ($members1Table == $members2Table) {
            $sql = "SELECT count(*) as total FROM {$members1Table} WHERE passid > :startPassId AND passid<=:endPassId ";
            $members = $this->getPdoMain()->find($sql, [":startPassId" => $startPassId, ':endPassId' => $endPassId]);
            $count = $members['total'];
        } else {
            $sql = "SELECT count(*) as total FROM {$members1Table} WHERE passid > :startPassId AND passid<=:endPassId ";
            $members = $this->getPdoMain()->find($sql, [":startPassId" => $startPassId, ':endPassId' => $endPassId]);
            $count += $members['total'];
            $sql = "SELECT count(*) as total FROM {$members2Table} WHERE passid > :startPassId AND passid<=:endPassId ";
            $members2 = $this->getPdoMain()->find($sql, [":startPassId" => $startPassId, ':endPassId' => $endPassId]);
            $count += $members2['total'];
        }

        return $count;
    }

    /**
     * 获取绑定关系表总数
     * @param string $table       绑定关系表名
     * @param int    $startPassId 起始的passid
     * @param int    $endPassId   结束的passid
     * @return array|bool
     */
    public function getSyncRelationCountByPassIdLimit($table, $startPassId, $endPassId)
    {
        $sql = "SELECT count(*) as total FROM {$table} WHERE passid > :startPassId AND passid<=:endPassId";

        return $this->getPdoMain()->find($sql, [":startPassId" => $startPassId, ':endPassId' => $endPassId]);
    }

    /*******************************新库数据数量比对查询************************************/

    /**
     * 功    能：获取最后一个自增的id
     * 修改日期：2019年6月19日
     *
     * @author: wangchenglong
     * @return int|bool
     */
    public function getLastPassId()
    {
        $sql = 'select id from `members_passId_incr` order by id desc limit 1';
        $row = $this->getPdo()->find($sql);
        if ($row) {
            return $row['id'];
        } else {
            return false;
        }
    }
}
