<?php
/**
 * 每月统计上个月的计费短信数据量
 */

namespace Crontab;

use Admin\TagListModel;
use Controller;
use Admin\SmsTotalModel;
use cron\SmsCronModel;
use Octopus\PdoEx;

class SmsStatController extends Controller
{
    /**
     * SmsStatController constructor.
     */
    public function __construct()
    {
        $dbConfig = \Config::get("database");
        $this->pdo = PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);
        parent::__construct();
    }

    /**
     *
     * @return void
     */
    public function actionSmsStat()
    {
        global $argv;
        $date = isset($argv[3]) ? $argv[3] : date('Y-m-d');
        //获取上月天数
        $projectData = [
            117 => 0,
            120 => 0,
            115 => 0,
            103 => 0,
            108 => 0,
            15 => 0,
        ];
        $tagData = [
            'andsjzs' => 0,
            'andsy' => 0,
            'SJLM' => 0,
            'andsjlm' => 0,
            'iossjlm' => 0,
            'JF' => 0,
            'pcbs' => 0,
            'andsearch' => 0,
            'anddhllq' => 0,
            'ioswzdh' => 0,
            'iosbs' => 0,
            'andbs' => 0,
            'andllqoem' => 0,
            'andtqw' => 0,
            'andbyhls' => 0,
            'ext_andbyhls' => 0,
            'andlyyx' => 0,
        ];
        // 本月第一秒
        $endTime = strtotime(date('Y-m-1 00:00:00', strtotime($date)));
        // 上月第一秒
        $startTime = strtotime(date('Y-m-1 00:00:00', $endTime - 1));
        $redis = \RedisAction::connect();
        for ($dateTmp = $startTime; $dateTmp < $endTime; $dateTmp += 3600 * 24) {
            $dateStr = date('Y-m-d', $dateTmp);
            // 获取获取各个项目当天统计并叠加
            $queryType = 'project';
            $returnStatus = 1;//仅查询回执成功的
            $redisKey = \Config_CacheKey::SMS_ADDUP_CACHE . $queryType . ':' . $returnStatus . ':' . $dateStr;
            if ($redis->exists($redisKey)) {
                $tmp = unserialize($redis->get($redisKey));
            } else {
                $SmsTotalModel = SmsTotalModel::getInstance();
                $queryDate = [
                    'startTime' => $dateStr,
                    'endTime' => date('Y-m-d', strtotime($dateStr . '+1 day')),
                    'returnStatus' => $returnStatus,
                    'smsCount' => 2,// 计费条数
                ];
                $tmp = $SmsTotalModel->getEveryTotal($queryDate);
            }
            if (is_array($tmp)) {
                foreach ($tmp as $item) {
                    if (isset($projectData[$item['titleId']])) {
                        $projectData[$item['titleId']] += $item['smsCount'];
                    }
                }
            }
            //获取用户中心当天细分数据并叠加
            $redisKey = "sms_addup_cache:tag:{$dateStr}";
            if ($redis->exists($redisKey)) {
                $tmp = unserialize($redis->get($redisKey));
            } else {
                $TagListModel = TagListModel::getInstance();
                $queryData = [
                    'startTime' => date('Y-m-d 00:00:00', strtotime($dateStr)),
                    'endTime' => date('Y-m-d 23:59:59', strtotime($dateStr))
                ];
                $tmp = $TagListModel->getTagLogTotal($queryData);
            }
            if (is_array($tmp)) {
                foreach ($tmp as $item) {
                    if (isset($tagData[$item['mid']])) {
                        $tagData[$item['mid']] += $item['total'];
                    }
                }
            }
        }
        $data = [
            "sjzs" => ["pid_list" => [117], "tag_list" => ['andsjzs', 'andsy']],//手机助手
            "sjlm" => ["pid_list" => [120], "tag_list" => ['SJLM', 'andsjlm', 'iossjlm']],//手机联盟
            "jsylm" => ["pid_list" => [115], "tag_list" => ['JF']],//技术员联盟
            "jsllq" => ["pid_list" => [103], "tag_list" => ['pcbs']],//PC加速浏览器
            "sjllq" => ["pid_list" => [], "tag_list" => ['andsearch', 'anddhllq', 'ioswzdh', 'iosbs', 'andbs', 'andllqoem']],//手机浏览器
            "tqw" => ["pid_list" => [], "tag_list" => ['andtqw']],//天气王
            "yxdh" => ["pid_list" => [108, 15], "tag_list" => ['andbyhls', 'ext_andbyhls', 'andlyyx']],//游戏导航
        ];
        $insertData = [
            'id' => null,
            'date' => date('Y-m-01', strtotime($date)),
            "sjzs" => 0,
            "sjlm" => 0,
            "jsylm" => 0,
            "jsllq" => 0,
            "sjllq" => 0,
            "tqw" => 0,
            "yxdh" => 0,
        ];
        foreach ($data as $key => $item) {
            $tmp = 0;
            foreach ($item['pid_list'] as $pid) {
                $tmp += $projectData[$pid];
            }
            foreach ($item['tag_list'] as $tag) {
                $tmp += $tagData[$tag];
            }
            $insertData[$key] = $tmp;
        }
        $dbConfig = \Config::get("database");
        $pdo = PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);
        $res = $pdo->insert('sms_log_stat', $insertData) ? true : false;
    }
}