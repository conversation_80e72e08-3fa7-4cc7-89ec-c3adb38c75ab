<?php
/**
 * 收集时间碎片 计入数据库 统计
 * */
includeBase('CronController');
class WriteDateAnalyzeController extends CronController
{
    const DATEANAKYZLISTKEY = 'DATEANAKYZLISTKEY'; //时间信息队列
    
    const ERRORDATEANAKYZLISTKEY = 'ERRORDATEANAKYZLISTKEY'; //计入数据库失败  时间信息队列
    
    
    /**
     * 计入时间片统计数据
     * */
    public function actionIndex()
    {
        $redis = RedisEx::getInstance();
        $WriteDateAnalyzeModel = loadModel('WriteDateAnalyze');
        $date = date('Y-m-d H:i:s');
        while ($dateAnalyze = $redis->rPop(self::DATEANAKYZLISTKEY))
        {
            $dateAnalyze = (array )json_decode($dateAnalyze);
            $data = array(
                'passid' => $dateAnalyze['passid'],
                'clientIp' => $dateAnalyze['clientIp'],
                'uuid' => $dateAnalyze['uuid'],
                'token' => !empty($dateAnalyze['token']) ? $dateAnalyze['token'] : '',
                'captcha' => !empty($dateAnalyze['captcha']) ? $dateAnalyze['captcha'] : 0,
                'rename' => !empty($dateAnalyze['rename']) ? $dateAnalyze['rename'] : 0,
                'submit' => !empty($dateAnalyze['submit']) ? $dateAnalyze['submit'] : 0,
                'isShowCap' => !empty($dateAnalyze['isShowCap']) ? $dateAnalyze['isShowCap'] : 0
            );
            $isTrue = $WriteDateAnalyzeModel->insertDateAnalyze($data);
            if (!$isTrue)
            {
                $redis->lPush(self::ERRORDATEANAKYZLISTKEY, json_encode($dateAnalyze) );
            }
            //当前时间  减去 脚本执行前的时间,如果执行程序执行了1分钟了,退出去,让下一个crontab来执行
            $now = time() - strtotime($date);
            if ($now > 59 )
            {
                break;
            }
        }

    }


}
