<?php

namespace Admin;
class OperatorConfigModel extends \Model
{

    const PROVIDER_CATE = [
        1 => '点集',
        2 => '大汉三通-网络',
        3 => '大汉三通-车贷',
        4 => '点集-营销',
        5 => '阿里云',
        0 => '其他',
    ]; //服务商分类

    /**
     * 初始数据库
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }


    /**
     * User: panj
     *
     * @param array  $data 参数
     * @param string $orderBy 排序键值
     * @param string $orderByType 排序方式
     *
     * @return array|bool
     */
    public function getOperatorList($data = array(), $orderBy = 'display_order', $orderByType = 'DESC')
    {
        $where = '';
        $paramArr = array();
        $sql = "SELECT * FROM `my_sms_provider` ";

        if (isset($data['id']))
        {
            $where .= " id = :id AND";
            $paramArr[':id'] = $data['id'];
        }
        if (isset($data['status']))
        {
            $where .= " status = :status AND";
            $paramArr[':status'] = $data['status'];
        }
        if ( !empty($data['OperatorName']) )
        {
            $where .= " provider_name like :provider_name AND";
            $paramArr[':provider_name'] = "%{$data['OperatorName']}%";
        }
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $orderByType = is_null($orderByType) ? 'DESC' : $orderByType;
        $orderBy = ' ORDER BY ' . $orderBy. ' ' .$orderByType;
        $sql .= $where;
        $sql .= $orderBy;
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    public function ChangeOperatorConfig($data)
    {
        $this->pdo->beginTransaction();
        $providerData = array(
            'provider_name' => $data['provider_name'],
            'provider_cate' => $data['provider_cate'],
            //'status' => 1,//!empty($data['status']) ? (int)$data['status'] : 0,
            'api_url' => $data['api_url'],
            'username' => $data['username'],
            'password'        => $data['password'],
            'support_mobile'  => !empty($data['support_mobile']) ? (int)$data['support_mobile'] : 0,
            'support_telecom' => !empty($data['support_telecom']) ? (int)$data['support_telecom'] : 0,
            'support_unicom'  => !empty($data['support_unicom']) ? (int)$data['support_unicom'] : 0,
            'description'     => $data['description'],
            'createdate'      => date('Y-m-d H:i:s'),
            'sign' => $data['sign'],
            'sign_type' => $data['sign_type'],
            'action_name' => $data['action_name'],
            'monitor_config' => $data['monitor_config'],
            'timeQuantum' => $data['timeQuantum'],
            'unit_price' => $data['unit_price'],
            );
        if (empty($data['provider_cate'])) {
            $providerData['provider_cate'] = 0;
        }
        if (empty($data['appType']))
        {
            $providerData['appType'] = 1;
        }
        elseif (in_array($data['appType'], array(1, 2)))
        {
            $providerData['appType'] = $data['appType'];
        }
        if ($data['actioType'] === 'add')
        {
            $isTrue = $this->pdo->insert('my_sms_provider', $providerData);
        }
        else
        {
            $condition = array('where' => 'id = :id', 'params' => array(':id' => $data['id']));
            $isTrue = $this->pdo->update('my_sms_provider', $providerData, $condition);
        }
        if ($isTrue !== false )
        {
            $SetRedisAction = SetRedisAction::getInstance();
            $isTrue = $SetRedisAction->setOperatorConfig();
        }
        if ($isTrue !== false)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    public function changeOperatorStatus($data)
    {
        $condition = array('where' => 'id = :id', 'params' => array(':id' => $data['id']));
        $isTrue = $this->pdo->update('my_sms_provider', array('status' => $data['status']), $condition);
        $SetRedisAction = SetRedisAction::getInstance();
        $isTrue = $SetRedisAction->setOperatorConfig();
        return $isTrue !== false ? true : false;
    }

    /**
     * User: panj
     *
     * @param array $data data
     *
     * @return bool
     */
    public function changeDisplayOrder($data)
    {
        if (empty($data) || !is_array($data))
        {
            return false;
        }
        foreach ($data as $id => $item)
        {
            $data[$id] = intval($item);
        }
        $when = '';
        foreach ($data as $id => $order)
        {
            $when .= ' WHEN ' . $id . ' THEN ' . $order . PHP_EOL;
        }
        $sql = " UPDATE my_sms_provider
               SET display_order = CASE id " .
            $when .
            "END 
               WHERE id IN (" . implode(', ', array_keys($data)) . ")";
        $res1 = $this->pdo->query($sql);
        $res2 = SetRedisAction::getInstance()->setOperatorConfig();

        return $res1 && $res2;
    }
}
