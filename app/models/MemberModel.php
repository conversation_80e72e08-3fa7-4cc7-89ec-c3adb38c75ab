<?php

use Common\Utils\Encoding;
use Common\Confidential\Sensitive;
use Octopus\PdoEx;
use Service\Capacity\GetHashTable;
use Service\Capacity\GetRangeTable;
use Service\Report\LSessionReport;

class MemberModel extends Model
{

    private $pdo;
    private $pdoNew;
    private $pdoLog;
    private $pdoMain;

    /**
     * MemberModel constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_PASSPORT, $dbConfig[DB_PASSPORT]);
    }

    /**
     * User: panj
     * Get passport_log pdo
     * @return PdoEx
     */
    protected function getPdoLog()
    {
        if (!is_object($this->pdoLog)) {
            $dbConfig = Config::get("database");
            $this->pdoLog = PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        }
        return $this->pdoLog;
    }

    /**
     *
     * Get passport_user pdo
     * @return PdoEx
     */
    protected function getPdoNew()
    {
        if (!is_object($this->pdoNew)) {
            $dbConfig = Config::get("database");
            $this->pdoNew = PdoEx::getInstance(DB_PASSPORT_USER, $dbConfig[DB_PASSPORT_USER], true);
        }
        return $this->pdoNew;
    }

    /**
     * User: panj
     * Get passport_main pdo
     * @return PdoEx
     */
    protected function getPdoMain()
    {
        if (!is_object($this->pdoMain)) {
            $dbConfig = Config::get("database");
            $this->pdoMain = PdoEx::getInstance(DB_PASSPORT_MAIN, $dbConfig[DB_PASSPORT_MAIN]);
        }
        return $this->pdoMain;
    }

    /**
     * 冻结和解冻2345账号
     * @param int $passid passid
     * @param  int $locked locked
     * <AUTHOR>
     * @return mixed
     */
    public function setMemberLock($passid, $locked = 0)
    {
        $table = GetRangeTable::Members($passid);
        $update = array("locked" => $locked);
        $where = array(
            'where' => "`id` = :passid",
            'params' => array(':passid' => $passid,)
        );
        return $this->getPdoNew()->update($table, $update, $where);
    }

    /**
     * 功  能：新增用户数据
     *
     * @param string $ip $ip
     * @param string $username $username
     * @param string $password $password
     * @param int $pwdStrength $pwdStrength
     * @param string $salt $salt
     * @param int $gid $gid
     * @param int $locked $locked
     * @param int $gender $gender
     * @param string $phone $phone
     * @param string $email $email
     * @param string $oauthType $oauthType
     * @param string $openid $openid
     * @param string $nickname $nickname
     * @return bool
     * desc:该函数兼容加解密
     */
    private function addUser(
        $ip,
        $username,
        $password,
        $pwdStrength,
        $salt,
        $gid,
        $locked,
        $gender = 0,
        $phone = '',
        $email = '',
        $oauthType = '',
        $openid = '',
        $nickname = ''
    ) {
        $passId = $this->generatePassId();
        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        try {
            // members
            $member = array(
                'id' => $passId,
                'username' => $username,
                'password' => $password,
                'salt' => $salt,
                'pwd_strength' => $pwdStrength,
                'uid' => $passId,
                'gid' => $gid,
                'reg_ip' => $ip,
                'reg_time' => date('Y-m-d H:i:s'),
                'login_ip' => $ip,
                'login_time' => date('Y-m-d H:i:s'),
                'locked' => $locked,
            );

            // desc:字段加解密兼容
            if (!empty($email)) {
                $email = Sensitive::Encode($email);
            }
            if (!empty($phone)) {
                $phone = Sensitive::Encode($phone);
            }

            if ($email) {
                $member['email'] = $email;
            }
            $openid && $oauthType && $member['openid'] = json_encode([$oauthType => $openid]);
            $phone && $member['phone'] = $phone;
            $table = GetRangeTable::Members($passId);
            $result = $pdoNew->insert($table, $member, false);
            if (!$result) {
                $pdoNew->rollBack();
                return false;
            }
            // members_username
            $table = Encoding::transcoding($username, 'gbk', 'utf-8');
            $table = GetHashTable::MembersUsername($table);
            $result = $pdoNew->insert($table, [
                'passid' => $passId,
                'username' => $username,
            ], false);
            if (!$result) {
                $pdoNew->rollBack();
                return false;
            }
            // members_info
            $info = array(
                'passid' => $passId,
                'email' => $email,
                'gender' => $gender
            );
            if ($email) {
                $info['email_status'] = 1;
            }
            $table = GetRangeTable::MembersInfo($passId);
            $result = $pdoNew->insert($table, EncodeMembersInfo($info), false);
            if (!$result) {
                $pdoNew->rollBack();
                return false;
            }
            // members_phone
            if ($phone) {
                $insert = array(
                    'passid' => $passId,
                    'phone' => $phone,
                    'last_update' => date('Y-m-d H:i:s'),
                );
                $table = GetHashTable::MembersPhone($phone);
                $result = $pdoNew->insert($table, $insert, false);
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
            }
            // members_email
            if ($email) {
                $table = GetHashTable::MembersEmail($email);
                $result = $pdoNew->insert($table, [
                    'passid' => $passId,
                    'email' => $email,
                ], false);
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
            }
            // members_bind
            if ($openid && $oauthType) {
                $insert = array(
                    'passid' => $passId,
                    'openid' => $openid,
                    'nickname' => $nickname,
                    'type' => $oauthType,
                    'username' => $username,
                    'bindtime' => date('Y-m-d H:i:s'),
                );
                $table = GetHashTable::MembersBind($openid);
                $result = $pdoNew->insert($table, $insert, false);
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
            }
            $pdoNew->commit();
            return $passId;
        } catch (Exception $e) {
            $pdoNew->rollBack();
            return false;
        }
    }

    /**
     * 注册2345帐号
     * @param string $username 用户名
     * @param string $password 密码
     * @param string $email 用户email
     * @param int $pwdStrength 密码强度
     * @param string $ip 用户ip
     * @param int $locked 是否锁定 1|0
     * @param int $passwordType 是否为MD5过的密码 1|0
     * @return boolean|array 注册成功与否
     */
    public function reg2345($username, $password, $email = '', $pwdStrength = 1, $ip = '', $locked = 0, $passwordType = 0)
    {
        $gid = 2;
        $salt = rand(100000, 999999);
        if ($passwordType == 1) {
            $password = md5($password . $salt);
        } else {
            $password = md5(md5($password) . $salt);
        }
        $passid = $this->addUser(
            $ip,
            $username,
            $password,
            $pwdStrength,
            $salt,
            $gid,
            $locked,
            0,
            '',
            $email
        );
        if (!$passid) {
            return false;
        }

        //注册成功记录logv2日志
        $arrUserInfo = [
            'passid' => $passid,
            'username' => $username,
            'email' => $email,
        ];

        //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:PASSID、EMAIL、USERNAME、PASSWORD;
        $objLogV2Action = loadAction('logV2');
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'PASSID', $arrUserInfo, '', $arrUserInfo['passid']);
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'USERNAME', $arrUserInfo, '', $arrUserInfo['username']);
        $email && $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'EMAIL', $arrUserInfo, '', $arrUserInfo['email']);

        $this->storeNewUserId($passid);
        $this->setPasswordCache($passid, $password . '@' . $salt);
        $token = md5($password . $salt);
        return [
            'passid' => $passid,
            'uid' => $passid,
            'username' => $username,
            'gid' => $gid,
            'token' => $token
        ];
    }

    /**
     * 用第三方帐号信息注册2345帐号
     *
     * @param string $oauthType 第三方帐号类型
     * @param string $openid 第三方帐号openid
     * @param string $nickname 第三方帐号昵称
     * @param string $ip 用户ip
     * @param string $email $email
     * @param int $gender $gender
     * @return boolean 注册成功与否
     */
    public function regOAuth($oauthType, $openid, $nickname, $ip = '', $email = '', $gender = 0)
    {
        if ($oauthType == 'qq') {
            $username = getUniqueUsername('qq用户_');
        } elseif ($oauthType == 'youxia') {
            $username = getUniqueUsername('游侠用户_');
        } elseif ($oauthType == 'weixin') {
            $username = getUniqueUsername('微信用户_');
        } else {
            $username = getUniqueUsername('微博用户_');
        }
        $salt = rand(100000, 999999);
        $pwd = mt_rand() . time();
        $password = md5(md5($pwd . MD5KEY) . $salt);
        $gid = 100;
        $passid = $this->addUser(
            $ip,
            $username,
            $password,
            2,
            $salt,
            $gid,
            0,
            $gender,
            '',
            $email,
            $oauthType,
            $openid,
            $nickname
        );
        if (!$passid) {
            return false;
        }

        //注册成功记录logv2日志
        $arrUserInfo = [
            'passid' => $passid,
            'username' => $username,
            'email' => $email,
        ];

        //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:PASSID、EMAIL、USERNAME、PASSWORD;
        $objLogV2Action = loadAction('logV2');
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'PASSID', $arrUserInfo, '', $arrUserInfo['passid']);
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'USERNAME', $arrUserInfo, '', $arrUserInfo['username']);
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'NICKNAME', $arrUserInfo, '', $nickname);
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'OPENID', $arrUserInfo, '', json_encode([$oauthType => $openid]));
        $email && $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'EMAIL', $arrUserInfo, '', $arrUserInfo['email']);
        $gender && $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'GENDER', $arrUserInfo, '', $gender);

        $this->storeNewUserId($passid);
        $this->setPasswordCache($passid, $password . '@' . $salt);
        $token = md5($password . $salt);
        return [
            'passid' => $passid,
            'uid' => $passid,
            'username' => $username,
            'gid' => $gid,
            'token' => $token
        ];
    }

    /**
     * 使用手机号注册2345帐号
     *
     * @param string $phone 用户手机号
     * @param string $password 用户密码
     * @param int $pwdStrength 密码强度
     * @param string $ip 用户ip
     * @param int $gid $gid
     * @return array|bool 注册返回信息
     */
    public function regPhone($phone, $password, $pwdStrength, $ip, $gid = 200)
    {
        $username = getUniqueUsername('手机用户_');
        $salt = rand(100000, 999999);
        $password = md5(md5($password) . $salt);
        $passid = $this->addUser(
            $ip,
            $username,
            $password,
            $pwdStrength,
            $salt,
            $gid,
            0,
            0,
            $phone
        );
        if (!$passid) {
            return false;
        }

        //注册成功记录logv2日志
        $arrUserInfo = [
            'passid' => $passid,
            'phone' => $phone,
            'username' => $username,
        ];

        //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:PASSID、PHONE、USERNAME、PASSWORD;
        $objLogV2Action = loadAction('logV2');
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'PASSID', $arrUserInfo, '', $arrUserInfo['passid']);
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'PHONE', $arrUserInfo, '', $arrUserInfo['phone']);
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'USERNAME', $arrUserInfo, '', $arrUserInfo['username']);

        //将注册的手机号码存储到redis
        $this->saveRegPhoneToRedis($phone, $passid, [1 => $passid], $gid, $username);

        $this->storeNewUserId($passid);
        $this->setPasswordCache($passid, $password . '@' . $salt);
        $token = md5($password . $salt);
        return [
            'passid' => $passid,
            'uid' => $passid,
            'username' => $username,
            'gid' => $gid,
            'token' => $token
        ];
    }

    /**
     * regPhoneToQueue
     * -
     * @param string $phone Phone
     * @param string $password 密码
     * @param int $pwdStrength 密码强度
     * @param string $ip IP
     * @param int $gid GID
     * @return bool|array
     * <AUTHOR>
     */
    public function regPhoneToQueue($phone, $password, $pwdStrength, $ip, $gid = 200)
    {
        $username = getUniqueUsername('手机用户_');
        $salt = rand(100000, 999999);
        $password = md5(md5($password) . $salt);
        $regQueueRedis = loadAction('regQueueRedis');
        $uid = false;
        $passId = false;
        $mUID = false;
        $token = md5($password . $salt);
        $now = time();

        $this->pdo->beginTransaction();
        try {
            //获取passId
            $passId = $this->generatePassId();
            $mUID = array(1 => $passId);
            $this->pdo->commit();
        } catch (Exception $e) {
            $this->pdo->rollBack();
            return false;
        } catch (Error $er) {
            $this->pdo->rollBack();
            return false;
        }

        //将注册的手机号码存储到redis
        $this->saveRegPhoneToRedis($phone, $passId, $mUID, $gid, $username);
        $this->storeNewUserId($passId); //不知道有啥用
        $this->setPasswordCache($passId, $password . '@' . $salt); //不明所以

        $regInfo = array();
        //队列信息1: member表中的其他字段值
        $regInfo['members'] = array(
            'id' => $passId,
            'username' => $username,
            'password' => $password,
            'salt' => $salt,
            'pwd_strength' => $pwdStrength,
            'uid' => $mUID[1],
            'gid' => $gid,
            'reg_ip' => $ip,
            'reg_time' => date('Y-m-d H:i:s', $now),
            'login_ip' => $ip,
            'login_time' => date('Y-m-d H:i:s', $now),
            'locked' => 0,
            'phone' => $phone,
        );
        //队列信息2: members_info表
        $regInfo['members_info'] = array(
            'passid' => $passId,
            'email' => '',
        );
        //队列信息3: members_phone表
        $regInfo['members_phone'] = array(
            'passid' => $passId,
            'phone' => $phone,
            'last_update' => date('Y-m-d H:i:s'),
        );
        $pushResult = $regQueueRedis->pushToRegQueue($passId, $regInfo);
        if ($pushResult === -1) {
            //-1表示redis连不上, 这种情况就写库
            $this->regOtherTableForQueue($regInfo);
        }

        return array(
            'passid' => $passId,
            'uid' => $passId,
            'username' => $username,
            'gid' => $gid,
            'token' => $token
        );
    }

    /**
     * regOtherTableForQueue
     * -
     * @param array $regInfo 注册数据
     * @return bool
     * <AUTHOR>
     */
    public function regOtherTableForQueue($regInfo)
    {
        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        try {
            $members = $regInfo['members'];
            $table = GetRangeTable::Members($members['id']);
            $result = $pdoNew->insert($table, $members, false);
            if (!$result) {
                throw new Exception('members表insert失败,数据:' . serialize($members));
            }
            $table = Encoding::transcoding($members['username'], 'gbk', 'utf-8');
            $table = GetHashTable::MembersUsername($table);
            $result = $pdoNew->insert($table, [
                'passid' => $members['id'],
                'username' => $members['username'],
            ], false);
            if (!$result) {
                throw new Exception('members_username表insert失败');
            }

            $membersInfo = $regInfo['members_info'];
            $table = GetRangeTable::MembersInfo($members['id']);
            $result = $pdoNew->insert($table, EncodeMembersInfo($membersInfo), false);
            if (!$result) {
                throw new Exception('members_info表insert失败,数据:' . serialize($membersInfo));
            }
            $membersPhone = $regInfo['members_phone'];
            $table = GetHashTable::MembersPhone($membersPhone['phone']);

            // desc:字段加解密兼容
            if (isset($membersPhone['phone'])) {
                $membersPhone['phone'] = Sensitive::Encode($membersPhone['phone']);
            }
            $result = $pdoNew->insert($table, $membersPhone, false);
            if (!$result) {
                throw new Exception('members_phone表insert失败,数据:' . serialize($membersPhone));
            }
            $pdoNew->commit();
            return true;
        } catch (Exception $ex) {
            $pdoNew->rollBack();
            return false;
        }
    }

    /**
     * email注册2345帐号
     *
     * @param string $email $email
     * @param string $password $password
     * @param int $pwdStrength $pwdStrength
     * @param string $ip $ip
     * @return array|bool
     */
    public function regEmail($email, $password, $pwdStrength, $ip)
    {
        $username = $email;
        $salt = rand(100000, 999999);
        $password = md5(md5($password) . $salt);
        $gid = 200;
        $passid = $this->addUser(
            $ip,
            $username,
            $password,
            $pwdStrength,
            $salt,
            $gid,
            0,
            0,
            '',
            $email
        );
        if (!$passid) {
            return false;
        }

        //注册成功记录logv2日志
        $arrUserInfo = [
            'passid' => $passid,
            'email' => $email,
            'username' => $username,
        ];

        //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:PASSID、EMAIL、USERNAME、PASSWORD;
        $objLogV2Action = loadAction('logV2');
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'PASSID', $arrUserInfo, '', $arrUserInfo['passid']);
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'EMAIL', $arrUserInfo, '', $arrUserInfo['email']);
        $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'USERNAME', $arrUserInfo, '', $arrUserInfo['username']);

        $this->storeNewUserId($passid);
        $this->setPasswordCache($passid, $password . '@' . $salt);
        $token = md5($password . $salt);
        return [
            'passid' => $passid,
            'uid' => $passid,
            'username' => $username,
            'gid' => $gid,
            'token' => $token
        ];
    }

    /**
     * 帐号登录
     * @param string $username 用户名
     * @param string $password 密码
     * @return array 登录信息
     * desc:该函数兼容加解密
     */
    public function login($username, $password)
    {
        $pdoNew = $this->getPdoNew();
        if ($passId = $this->getPassIdByUsername($username)) {
            $row = $this->getMember($passId, ['id', 'uid', 'username', 'password', 'salt', 'gid', 'locked']);
            if ($row) {
                $nickrow = $pdoNew->find("SELECT nickname FROM members_nickname WHERE passid = :passid", array(":passid" => $passId));
                //未设置用户名,手机,邮箱的用户 用户组为300(第三方注册) || 100(手机快捷注册用户)) 并且使用密码登录的,直接返回登录失败.
                if ($row['gid'] == 100 || $row['gid'] == 300) {
                    return array(-1, 'type' => 'username');  //登录失败
                }
                if ($row['password'] == md5($password . $row['salt'])) {
                    if ($row['locked']) {
                        $table = GetRangeTable::MembersInfo($passId);
                        $row2 = $pdoNew->find("SELECT email FROM {$table} WHERE passid = :passid", array(":passid" => $passId));

                        // desc:字段加解密兼容
                        if (isset($row2['email'])) {
                            $row2['email'] = Sensitive::Decode($row2['email']);
                        }
                        return array(2, 'passId' => $row['id'], 'email' => $row2['email'], 'locked' => $row['locked']);
                    }
                    $this->setPasswordCache($row['id'], $row['password'] . '@' . $row['salt']);
                    $token = md5($row['password'] . $row['salt']);
                    return array(
                        0 => 1,
                        'data' => serialize([1 => (int)$row['uid']]),
                        'passId' => $row['id'],
                        'username' => $row['username'],
                        'gid' => $row['gid'],
                        'token' => $token,
                        'type' => 'username',
                        'nickname' => $nickrow['nickname']
                    );  //登录成功
                } else {
                    return array(-1, 'type' => 'username');  //登录失败
                }
            }
        }
        $regex = Config::get('regex');
        if (preg_match($regex['phone'], $username)) {
            if ($passId = $this->getPassIdByPhone($username, false)) {
                $row = $this->getMember($passId, ['id', 'uid', 'username', 'password', 'salt', 'gid', 'locked']);
                if ($row) {
                    $nickrow = $pdoNew->find('SELECT nickname FROM members_nickname WHERE passid = :passid', array(":passid" => $row['id']));
                    //未设置用户名,手机,邮箱的用户 用户组为300(第三方注册) || 100(手机快捷注册用户)) 并且使用密码登录的,直接返回登录失败.
                    if ($row['gid'] == 100 || $row['gid'] == 300) {
                        return array(-1, 'type' => 'phone');  //登录失败
                    }
                    if ($row['password'] == md5($password . $row['salt'])) {
                        if ($row['locked']) {
                            $table = GetRangeTable::MembersInfo($passId);
                            $row2 = $pdoNew->find("SELECT email FROM {$table} WHERE passid = :passid", array(":passid" => $passId));
                            // desc:字段加解密兼容
                            if (isset($row2['email'])) {
                                $row2['email'] = Sensitive::Decode($row2['email']);
                            }
                            return array(2, 'passId' => $row['id'], 'email' => $row2['email'], 'locked' => $row['locked']);
                        }
                        $this->setPasswordCache($row['id'], $row['password'] . '@' . $row['salt']);
                        $token = md5($row['password'] . $row['salt']);
                        return array(
                            0 => 1,
                            'data' => serialize([1 => (int)$row['uid']]),
                            'passId' => $row['id'],
                            'username' => $row['username'],
                            'gid' => $row['gid'],
                            'token' => $token,
                            'type' => 'phone',
                            'nickname' => $nickrow['nickname']
                        );  //登录成功
                    } else {
                        return array(-1, 'type' => 'phone');  //登录失败
                    }
                }
            }
        }
        if (filter_var($username, FILTER_VALIDATE_EMAIL)) {
            $row = $this->getPassIdByEmail($username);
            if (1 == $row[0]) {
                $passId = $row[1];
                $row = $this->getMember($passId, ['id', 'uid', 'username', 'password', 'salt', 'gid', 'locked']);
                if ($row) {
                    if ($row['gid'] == 100 || $row['gid'] == 300) {
                        return array(-1, 'type' => 'email');  //登录失败
                    }
                    if ($row['password'] == md5($password . $row['salt'])) {
                        if ($row['locked']) {
                            $table2 = GetRangeTable::MembersInfo($passId);
                            $row2 = $pdoNew->find("SELECT email FROM {$table2} WHERE passid = :passid", array(":passid" => $passId));
                            // desc:字段加解密兼容
                            if (isset($row2['email'])) {
                                $row2['email'] = Sensitive::Decode($row2['email']);
                            }
                            return array(2, 'passId' => $row['id'], 'email' => $row2['email'], 'locked' => $row['locked']);
                        }
                        $nickrow = $pdoNew->find('SELECT nickname FROM members_nickname WHERE passid = :passid', array(":passid" => $passId));
                        $this->setPasswordCache($row['id'], $row['password'] . '@' . $row['salt']);
                        $token = md5($row['password'] . $row['salt']);
                        return array(
                            0 => 1,
                            'data' => serialize([1 => (int)$row['uid']]),
                            'passId' => $row['id'],
                            'username' => $row['username'],
                            'gid' => $row['gid'],
                            'token' => $token,
                            'type' => 'email',
                            'nickname' => $nickrow['nickname']
                        );  //登录成功
                    } else {
                        return array(-1, 'type' => 'email');  //登录失败
                    }
                }
            }
        }
        return array(-2, 'type' => 'username');  //登录失败
    }

    /**
     * 解锁用户
     * @param int $passid 用户passid
     * @return int 成功与否 1|0
     */
    public function unlockUser($passid)
    {
        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        try {
            $data = array('locked' => 0,);
            $condition = array(
                'where' => '`id` = :passid and locked = :lock_status',
                'params' => array(
                    ':passid' => $passid,
                    ':lock_status' => 1,
                )
            );
            $table = GetRangeTable::Members($passid);
            $rs = $pdoNew->update($table, $data, $condition);
            if (!$rs) {
                $pdoNew->rollBack();
                return false;
            }

            $data2 = array('email_status' => 1,);
            $table = GetRangeTable::MembersInfo($passid);
            $result = $pdoNew->update($table, $data2, array(
                'where' => "`passid` = :passid  and email_status = :emai_status",
                'params' => array(
                    ':passid' => $passid,
                    ':emai_status' => 0,
                )
            ));
            if (!$result) {
                $pdoNew->rollBack();
                return false;
            }
            $pdoNew->commit();
        } catch (Exception $e) {
            $pdoNew->rollBack();
            return false;
        }
        if ($rs && $result) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 功  能：
     *
     * @param int $passid $passid
     * @return void
     */
    private function storeNewUserId($passid)
    {
        $expireTime = 60;
        $nowTime = time();
        $redis = RedisEx::getInstance();

        $redis->zAdd('newRegId', $nowTime, $passid);
        $redis->zRemRangeByScore('newRegId', 0, $nowTime - $expireTime);
        $redis->expire('newRegId', $expireTime);
    }

    /**
     * @param int $passid $passid
     *
     * @return bool
     */
    private function isNewUserId($passid)
    {
        $redis = RedisEx::getInstance();
        $addTime = $redis->zScore('newRegId', $passid);
        return !!$addTime;
    }


    /**
     * 读取用户信息
     * @param int $passid 用户passid
     * @param array $columns 增加指定查询字段
     * @return array 用户信息
     * desc:该函数兼容加解密
     */
    public function read($passid, $columns = [])
    {
        $useWritableDB = false;
        if ($this->isNewUserId($passid)) {
            $useWritableDB = true;
        }
        $queryData = [
            'm.username',
            'm.uid AS m_uid',
            'm.gid',
            'm.id',
            'm.reg_ip',
            'm.reg_time',
            'm.login_ip',
            'm.login_time',
            'm.locked',
            'm.openid',
            'mi.name',
            'mi.gender',
            'mi.bday',
            'mi.qq',
            'mi.area',
            'mi.email',
            'mi.email_status',
            'm.phone',
            'm.pwd_strength',
        ];
        if (!empty($columns) && is_array($columns)) {
            $queryData = array_merge($queryData, $columns);
        }
        $row = $this->getMoreUserInfo($passid, $queryData, $useWritableDB);
        if ($row) {
            $row["uid"] = (int)$row['m_uid'];
            $row['m_uid'] = serialize([1 => (int)$row['m_uid']]);
            if (strpos($row['email'], "@bookguest.2345.com") !== false) {
                $row['email'] = "";
                $row['email_status'] = 0;
            }
            $row['phone_redundancy'] = '';  //暗库已经干掉了， 添加此赋值是为了兼容对外接口
        }
        return $row;
    }

    /**
     * 读取用户信息比read多修改邮箱时间和修改手机时间
     * @param int $passid 用户passid
     * @return array 用户信息
     * desc:该函数兼容加解密
     */
    public function readMore($passid)
    {
        $row = $this->getMoreUserInfo($passid, [
            'm.username',
            'm.phone',
            'm.uid AS m_uid',
            'm.gid',
            'm.id',
            'm.reg_ip',
            'm.reg_time',
            'm.login_ip',
            'm.login_time',
            'mi.name',
            'mi.gender',
            'mi.bday',
            'mi.qq',
            'mi.area',
            'mi.email',
            'mi.email_status',
        ]);
        if ($row) {
            $pdoNew = $this->getPdoNew();
            if (strpos($row['email'], "@bookguest.2345.com") !== false) {
                $row['email'] = "";
                $row['email_status'] = 0;
            }
            $row['m_uid'] = serialize([1 => (int)$row['m_uid']]);
            $row['phone_redundancy'] = '';  //暗库已经干掉了， 添加此赋值是为了兼容对外接口

            $table = GetHashTable::MembersPhone($row['phone']);
            $sql = "SELECT last_update FROM {$table} WHERE phone=:phone or phone=:aesPhone";
            $phoneInfo = $pdoNew->find($sql, [":phone" => $row['phone'], ":aesPhone" => Sensitive::Encode($row['phone'])]);
            if ($phoneInfo) {
                $row['last_update'] = $phoneInfo['last_update'];
            } else {
                $row['last_update'] = null;
            }
            $sql = "SELECT op_time FROM members_info_log WHERE uid=:passid";
            $infoLog = $this->getPdoLog()->find($sql, [":passid" => $passid]);
            if ($infoLog) {
                $row['op_time'] = $infoLog['op_time'];
            } else {
                $row['op_time'] = null;
            }
        }
        return $row;
    }

    /**
     * 服务器获取用户绑定信息， 手机号，邮箱，qq，微信
     *
     * @param int $passid 用户passid
     *
     * @return array 绑定 用户信息
     * desc:该函数兼容加解密
     */
    public function bindInfo($passid)
    {
        $row = $this->getMoreUserInfo($passid, [
            'm.id as passid',
            'm.phone',
            'm.openid',
            'mi.email',
            'mi.email_status',
        ]);
        if ($row) {
            $pdoNew = $this->getPdoNew();
            if (strpos($row['email'], "@bookguest.2345.com") !== false) {
                $row['email'] = "";
                $row['email_status'] = 0;
            }
            $row['phone_bindtime'] = null;
            if (!empty($row['phone'])) {
                $table = GetHashTable::MembersPhone($row['phone']);
                $sql = "SELECT last_update  FROM {$table} WHERE phone=:phone or phone=:aesPhone";
                $phoneInfo = $pdoNew->find($sql, [":phone" => $row['phone'], ":aesPhone" => Sensitive::Encode($row['phone'])]);
                if ($phoneInfo) {
                    $row['phone_bindtime'] = $phoneInfo['last_update'];
                } else {
                    $row['phone_bindtime'] = null;
                }
                $row['phone_bindtime'] = strtotime($row['phone_bindtime']);
                $row['phone_bindtime'] = ($row['phone_bindtime'] < 0) ? 0 : $row['phone_bindtime'];
            }
            $openidList = json_decode($row['openid'], true);
            unset($row['openid']);
            $openidList = is_array($openidList) ? $openidList : [];
            $bindRows = $this->getNewMemberBindByPassIdList($openidList);
            if ($bindRows) {
                $row['bind'] = array();
                foreach ($bindRows as $bindRow) {
                    $tmp = [
                        'type' => $bindRow['type'],
                        'nickname' => $bindRow['nickname'],
                        'bindTime' => $bindRow['bindtime'],
                    ];
                    if (isset($_POST['mid']) && $_POST['mid'] == 'H5') {
                        $tmp['openid'] = $bindRow['openid'];
                    }
                    $row['bind'][$bindRow['type']] = $tmp;
                }
            }
        }

        return $row;
    }

    /**
     * to do 可能下线, 暂时不动
     *
     * regIp 下所有用户信息
     * @param string $regIp ip
     * @param int $page page
     * @param int $pageSize pageSize
     *
     * @return array|bool
     */
    public function getListByRegIp($regIp, $page, $pageSize)
    {
        $sql = "SELECT * FROM members WHERE reg_ip = :reg_ip limit " . $pageSize . " offset " . ($page - 1) * $pageSize;
        $row = $this->pdo->findAll(
            $sql,
            array(":reg_ip" => $regIp,)
        );
        return $row;
    }

    /**
     * to do 可能下线, 暂时不动
     *
     * ip 总数
     *
     * @param string $regIp ip
     *
     * @return array|bool
     */
    public function getCountByRegIp($regIp)
    {
        $sql = "SELECT count(1) as count FROM members WHERE reg_ip = :reg_ip";
        $row = $this->pdo->find($sql, array(":reg_ip" => $regIp));

        return ($row && isset($row['count'])) ? $row['count'] : 0;
    }

    /**
     * 功  能：获取member表数据
     *
     * @param int $passid 用户passid
     * @param array $columns $columns
     * @param bool $masterDb 是否使用主库
     * @return array|bool
     * desc:该函数兼容加解密
     */
    public function getMember($passid, $columns = [], $masterDb = false)
    {
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $columns = empty($columns) ? '*' : implode(',', $columns);
        $sql = "SELECT {$columns} " .
            "FROM {$table} " .
            "WHERE id = :passid";
        $data = $pdoNew->find($sql, [":passid" => $passid], $masterDb);

        // desc:字段加解密兼容
        if (isset($data['email'])) {
            $data['email'] = Sensitive::Decode($data['email']);
        }
        if (isset($data['phone'])) {
            $data['phone'] = Sensitive::Decode($data['phone']);
        }
        return $data;
    }

    /**
     * 读取用户UID
     * @param int $passid 用户passid
     * @return int $uid
     */
    public function getUid($passid)
    {
        $row = $this->getMember($passid, ['uid']);
        if ($row) {
            return (int)$row["uid"];
        }
    }

    /**
     * 查询用户注册和登录时间
     * @param int $passid 用户passid
     * @return array 用户注册和登录时间数组
     */
    public function readRegLoginTime($passid)
    {
        return $this->getMember($passid, ['username', 'reg_time', 'login_time']);
    }

    /**
     * 编辑用户信息
     * @param int $passid 用户passid
     * @param array $data 需要编辑的信息
     * @return boolean 编辑成功与否
     * desc:该函数兼容加解密
     */
    public function edit($passid, $data)
    {
        $table = GetRangeTable::MembersInfo($passid);
        foreach ($data as $column => $value) {
            $data[$column] = $value;
        }

        $data = EncodeMembersInfo($data);

        $condition = array(
            'where' => '`passid` = :passid',
            'params' => array(':passid' => $passid,)
        );
        $pdoNew = $this->getPdoNew();

        $memberRedis = loadAction('memberRedis');
        $memberRedis->delMemberCache($passid);

        return $pdoNew->update($table, $data, $condition);
    }

    /**
     * 重置密码
     * @param int $passid 用户passid
     * @param string $password 用户密码
     * @param int $pwd_strength 密码强度
     * @param string $ip $ip
     * @return void
     */
    public function setPassword($passid, $password, $pwd_strength, $ip)
    {
        $pdoNew = $this->getPdoNew();
        $row = $this->getMember($passid, ['openid', 'password', 'salt'], true);
        if ($row) {
            if ($row["password"] == md5(md5("123456") . $row["salt"])) {
                $this->updatePassword($passid, $password, $pwd_strength, $ip);

                $nickname = [
                    'weibo' => 'wbuser',
                    'qq' => 'qzuser',
                    'weixin' => 'wxuser',
                ];
                $openidList = json_decode($row['openid'], true);
                $openidList = is_array($openidList) ? $openidList : [];
                foreach ($openidList as $type => $openid) {
                    $table = GetHashTable::MembersBind($openid);
                    $pdoNew->update(
                        $table,
                        ["nickname" => isset($nickname[$type]) ? $nickname[$type] : ""],
                        [
                            'where' => "`passid` = :passid and type = :type",
                            'params' => [
                                ':passid' => $passid,
                                ':type' => $type,
                            ]
                        ]
                    );
                }
            }
        }
    }

    /**
     * 修复旧的第三方帐号
     * @param int $passid $passid
     * @param string $oauthType $oauthType
     * @return boolean|string
     */
    public function repairOldOAuthUser($passid, $oauthType)
    {
        if ($oauthType == 'qq') {
            $username = getUniqueUsername("qq用户_");
        } elseif ($oauthType == 'youxia') {
            $username = getUniqueUsername("游侠用户_");
        } elseif ($oauthType == 'weixin') {
            $username = getUniqueUsername("微信用户_");
        } else {
            $username = getUniqueUsername('微博用户_');
        }
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $row = $pdoNew->find("SELECT username, gid, openid FROM {$table} WHERE id = :passid", array(":passid" => $passid), true);
        if ($row) {
            if ($row['gid'] == 2 || ($oauthType == 'qq' && strpos($row["username"], "#qq#") !== false) || ($oauthType == 'weibo' && strpos($row["username"], "#weibo#") !== false)) {
                $pdoNew->beginTransaction();
                try {
                    $update = array(
                        "username" => $username,
                        'gid' => 100,
                    );
                    $result = $pdoNew->update($table, $update, array(
                        'where' => "`id` = :passid",
                        'params' => array(':passid' => $passid,)
                    ));
                    if (!$result) {
                        $pdoNew->rollBack();
                        return false;
                    }
                    $openidList = @json_decode($row['openid'], true);
                    if ($openidList) {
                        $update = array(
                            "username" => $username
                        );
                        foreach ($openidList as $openid) {
                            $table = GetHashTable::MembersBind($openid);
                            $result = $pdoNew->update($table, $update, array(
                                'where' => "`passid` = :passid",
                                'params' => array(':passid' => $passid,)
                            ));
                            if (!$result) {
                                $pdoNew->rollBack();
                                return false;
                            }
                        }
                    }
                    $pdoNew->commit();
                    return $username;
                } catch (Exception $e) {
                    $pdoNew->rollBack();
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 第三方帐号注册后补全用户信息
     * @param int $passid 用户passid
     * @param string $username 用户名
     * @param string $password 密码
     * @param int $pwd_strength 密码强度
     * @param string $email 用户email
     * @return boolean 补全成功与否
     */
    public function setUser($passid, $username, $password, $pwd_strength, $email = '')
    {
        $memberRedis = loadAction('memberRedis');
        $memberRedis->delMemberCache($passid);

        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $row = $pdoNew->find("SELECT openid, username, gid, phone, email FROM {$table} WHERE id = :passid", array(":passid" => $passid), true);

        if (isset($row['phone'])) {
            $row['phone'] = Sensitive::Decode($row['phone']);
        }
        if (isset($row['email'])) {
            $row['email'] = Sensitive::Decode($row['email']);
        }

        if ($row) {
            if ($row['gid'] == 100 || $row['gid'] == 300 || strpos($row["username"], "#qq#") !== false || strpos($row["username"], "#weibo#") !== false) {
                $pdoNew->beginTransaction();
                try {
                    if ($row['gid'] == 300) {
                        $gid = 201;
                    } else {
                        $gid = 101;
                    }
                    $salt = rand(100000, 999999);
                    $update = array(
                        "username" => $username,
                        'password' => md5(md5($password) . $salt),
                        'gid' => $gid,
                        'salt' => $salt,
                        'pwd_strength' => $pwd_strength,
                    );
                    if ($email) {
                        $update['email'] = Sensitive::Encode($email);
                    }
                    $result = $pdoNew->update($table, $update, array(
                        'where' => "`id` = :passid",
                        'params' => array(':passid' => $passid,)
                    ));
                    if (!$result) {
                        $pdoNew->rollBack();
                        return false;
                    }
                    if ($email) {
                        $update = array(
                            "email" => Sensitive::Encode($email),
                            "email_status" => 1,
                        );
                        $table = GetRangeTable::MembersInfo($passid);
                        $result = $pdoNew->update($table, $update, array(
                            'where' => "`passid` = :passid",
                            'params' => array(':passid' => $passid,)
                        ));
                        if (!$result) {
                            $pdoNew->rollBack();
                            return false;
                        }
                        $table = GetHashTable::MembersEmail($email);
                        $result = $pdoNew->insert($table, [
                            'passid' => $passid,
                            'email' => Sensitive::Encode($email),
                        ], false);
                        if (!$result) {
                            $pdoNew->rollBack();
                            return false;
                        }
                    }
                    if ($row['gid'] != 300) {
                        $update = array("username" => $username,);
                        $openidList = json_decode($row['openid'], true);
                        $openidList = is_array($openidList) ? $openidList : [];
                        foreach ($openidList as $type => $openid) {
                            $table = GetHashTable::MembersBind($openid);
                            $result = $pdoNew->update($table, $update, array(
                                'where' => "`passid` = :passid",
                                'params' => array(':passid' => $passid,),
                            ));
                            if (!$result) {
                                $pdoNew->rollBack();
                                return false;
                            }
                        }
                    }
                    $table = Encoding::transcoding($row['username'], 'gbk', 'utf-8');
                    $table = GetHashTable::MembersUsername($table);
                    $result = $pdoNew->delete($table, [
                        'where' => "username=:username",
                        'params' => [':username' => $row['username']]
                    ]);
                    if (!$result) {
                        $pdoNew->rollBack();
                        return false;
                    }
                    $table = Encoding::transcoding($username, 'gbk', 'utf-8');
                    $table = GetHashTable::MembersUsername($table);
                    $result = $pdoNew->insert($table, [
                        'passid' => $passid,
                        'username' => $username,
                    ], false);
                    if (!$result) {
                        $pdoNew->rollBack();
                        return false;
                    }
                    $pdoNew->commit();

                    //修改成功记录logv2日志
                    $row['passid'] = $passid;

                    //日志行为打点:用户信息修改; type:USER_INFO_MODIFY; sub_type:EMAIL、USERNAME、PASSWORD;
                    $objLogV2Action = loadAction('logV2');
                    $objLogV2Action->report('ALL', 'USER_INFO_MODIFY', 'USERNAME', $row, $row['username'], $username);
                    $objLogV2Action->report('ALL', 'USER_INFO_MODIFY', 'PASSWORD', $row, '', '');
                    $email && $objLogV2Action->report('ALL', ($row['email'] ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD'), 'EMAIL', $row, $row['email'], $email);

                    return true;
                } catch (Exception $e) {
                    $pdoNew->rollBack();
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 重置密码
     * @param int $passid 用户passid
     * @param string $password 用户密码
     * @param int $pwd_strength 密码强度
     * @return bool
     * desc:该函数兼容加解密
     */
    public function setFirstPassword($passid, $password, $pwd_strength)
    {
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $row = $pdoNew->find("SELECT username, gid, phone FROM {$table} WHERE id = :passid", array(":passid" => $passid), true);

        if (isset($row['phone'])) {
            $row['phone'] = Sensitive::Decode($row['phone']);
        }

        if ($row) {
            //清理缓存
            $memberRedis = loadAction('memberRedis');
            $memberRedis->delUsernameCache($row['username']);
            $memberRedis->delMemberCache($passid);
            $memberRedis->delPhoneCache($row['phone']);

            if ($row['gid'] == 300) {
                $pdoNew->beginTransaction();
                try {
                    $salt = rand(100000, 999999);
                    $update = array(
                        'password' => md5(md5($password) . $salt),
                        'gid' => 200,
                        'salt' => $salt,
                        'pwd_strength' => $pwd_strength,
                    );

                    $result = $pdoNew->update($table, $update, array(
                        'where' => "`id` = :passid",
                        'params' => array(':passid' => $passid,)
                    ));
                    if (!$result) {
                        $pdoNew->rollBack();
                        return false;
                    }
                    $pdoNew->commit();
                    return true;
                } catch (Exception $e) {
                    $pdoNew->rollBack();
                    return false;
                }
            }
        }
        return false;
    }

    /**
     * 重置用户修改用户名权限
     *
     * @param int $passid $passid
     *
     * @return bool
     */
    public function allowSetUsername($passid)
    {
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $pdoNew->beginTransaction();
        $row = $pdoNew->find("SELECT gid FROM {$table} WHERE id = :passid FOR UPDATE ", array(":passid" => $passid), true);
        if ($row) {
            if ($row["gid"] == 201 || $row["gid"] == 301) {
                $update = array('gid' => 200,);
                $result = $pdoNew->update($table, $update, array(
                    'where' => "`id` = :passid",
                    'params' => array(':passid' => $passid,)
                ));
                if ($result) {
                    $pdoNew->commit();
                    return true;
                }
            }
        }
        $pdoNew->rollBack();
        return false;
    }

    /**
     * 补全用户名
     * @param int $passid $passid
     * @param string $username $username
     * @param boolean $ignoreGid $ignoreGid
     * @return boolean
     */
    public function setUsername($passid, $username, $ignoreGid = false)
    {
        $memberRedis = loadAction('memberRedis');
        $memberRedis->delMemberCache($passid);

        if ($row = $this->getMember($passid, ['username', 'gid', 'phone', 'email'], true)) {
            $pdoNew = $this->getPdoNew();
            if ($ignoreGid || $row['gid'] == 200) {
                $pdoNew->beginTransaction();
                $update = array(
                    "username" => $username,
                    "gid" => 201,
                );
                if ($ignoreGid) {
                    $update['gid'] = 200;
                }
                $table = GetRangeTable::Members($passid);
                $result = $pdoNew->update($table, $update, array(
                    'where' => "`id` = :passid",
                    'params' => array(':passid' => $passid,)
                ));
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
                $table = Encoding::transcoding($row['username'], 'gbk', 'utf-8');
                $table = GetHashTable::MembersUsername($table);
                $result = $pdoNew->delete($table, [
                    'where' => "username=:username",
                    'params' => [':username' => $row['username']]
                ]);
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
                $table = Encoding::transcoding($username, 'gbk', 'utf-8');
                $table = GetHashTable::MembersUsername($table);
                $result = $pdoNew->insert($table, [
                    'passid' => $passid,
                    'username' => $username,
                ], false);
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
                $pdoNew->commit();

                //修改成功记录logv2日志
                $row['passid'] = $passid;

                //日志行为打点:用户信息修改; type:USER_INFO_MODIFY; sub_type:EMAIL、USERNAME、PASSWORD;
                $objLogV2Action = loadAction('logV2');
                $objLogV2Action->report('ALL', 'USER_INFO_MODIFY', 'USERNAME', $row, $row['username'], $username);

                return true;
            }
        }
        return false;
    }

    /**
     * 修改密码
     * @param int $passid 用户passid
     * @param string $oldPass 旧密码
     * @param string $newPass 新密码
     * @param int $pwdStrength 密码强度
     * @param string $ip 用户ip
     * @return boolean 修改成功与否
     */
    public function chgPassword($passid, $oldPass, $newPass, $pwdStrength = 1, $ip = '0.0.0.0')
    {
        $row = $this->getMember($passid, ['password', 'salt'], true);
        if ($row && md5(md5($oldPass) . $row['salt']) == $row['password']) {
            $this->updatePassword($passid, $newPass, $pwdStrength, $ip);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取用户修改密码日志
     * @param int $passid 用户passid
     * @return array 日志列表
     */
    public function chgPwdLog($passid)
    {
        if ($passid == 18607234) {
            return array();
        }
        $logTable = 'edit_pwd_log_' . (($passid % 20) + 1);
        $sql = "SELECT ip, time FROM $logTable WHERE passid = :passid ORDER BY id DESC LIMIT 1";
        return $this->getPdoLog()->find($sql, array(":passid" => $passid));
    }

    /**
     * 检查用户名是否允许注册或是否已被注册
     * @param string $username 用户名
     * @return int 状态码
     */
    public function checkUser($username)
    {
        $banPres = array(
            'qq用户_',
            '微博用户_',
            '手机用户_',
            '邮箱用户_',
            '游侠用户_',
        );
        foreach ($banPres as $banPre) {
            if (strpos($username, $banPre) === 0) {
                return 2;
            }
        }
        if ($this->getPassIdByUsername($username)) {
            return 1;
        }
        return 0;
    }

    /**
     * 检查昵称是否允许更改
     * @param string $nickname 昵称
     * @return int 状态码
     */
    public function checkNickname($nickname)
    {
        $row = $this->getPdoNew()->find("SELECT nickname FROM members_nickname WHERE nickname = :nickname LIMIT 1", array(":nickname" => $nickname));
        if ($row) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     *
     * @param int $passid $passid
     * @return array
     */
    public function getNicknameByPassid($passid)
    {
        $row = $this->getPdoNew()->find("SELECT nickname, addtime, ip FROM members_nickname WHERE passid = :passid LIMIT 1", array(":passid" => $passid));
        return $row;
    }

    /**
     *
     * @param string $nickname $nickname
     * @return mixed
     * <AUTHOR>
     */
    public function getPassidByNickname($nickname)
    {
        $row = $this->getPdoNew()->find("SELECT passid FROM members_nickname WHERE nickname = :nickname LIMIT 1", array(":nickname" => $nickname));
        return $row;
    }

    /**
     * 设置用户昵称
     *
     * @param int $passid $passid
     * @param string $nickname $nickname
     * @param string $addTime $addTime
     * @param string $ip $ip
     * @return bool
     */
    public function setNickname($passid, $nickname, $addTime, $ip)
    {
        $pdoNew = $this->getPdoNew();
        $row = $this->getMember($passid, ['gid'], true);
        if ($row) {
            $pdoNew->beginTransaction();
            try {
                $insert = array(
                    'passid' => $passid,
                    'nickname' => $nickname,
                    'addtime' => $addTime,
                    'ip' => $ip
                );
                $result = $pdoNew->insert('members_nickname', $insert, false);
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
                $pdoNew->commit();
                return true;
            } catch (Exception $e) {
                $pdoNew->rollBack();
                return false;
            }
        }
        return false;
    }

    /**
     * 查询手机号是否已经注册
     * @param string $phone 手机号
     * @param int $withUsername $withUsername
     * @return int 是否存在（1|0）
     * desc:该函数兼容加解密
     */
    public function checkPhone($phone, $withUsername = 1)
    {
        if ($this->isUserInfoRedis) {
            return $this->checkPhoneUseRedis($phone, $withUsername);
        } else {
            if ($withUsername) {
                if ($this->getPassIdByUsername($phone)) {
                    return 2;
                }
            }
            if ($this->getPassIdByPhone($phone, false)) {
                return 3;
            }
            return 0;
        }
    }

    /**
     * 查询手机号是否已经注册
     * @param string $phone 手机号
     * @param int $withUsername $withUsername
     * @return int 是否存在（1|0）
     */
    public function checkPhoneUseRedis($phone, $withUsername = 1)
    {
        $memberRedis = loadAction('memberRedis');
        $existsThisPhone = $memberRedis->hashHExists($phone, REG_PHONE_INFO_REDIS_KEY, $phone);

        if ($existsThisPhone === true || $existsThisPhone === 1) {
            $memberRedis->closeUserDataRedis();
            return 3;
        } else {
            $return = 0;
            $passid = 0;
            if ($withUsername && $passid = $this->getPassIdByUsername($phone)) {
                $return = 2;
            }
            if ($return == 0 && $passid = $this->getPassIdByPhone($phone, false)) {
                $return = 3;
            }
            if ($passid) {
                $row = $this->getMember($passid, ['id', 'username', 'gid', 'uid']);
                if ($row) {
                    $row['uid'] = serialize([1 => (int)$row['uid']]);
                    $this->saveRegPhoneToRedis($phone, $row['id'], $row['uid'], $row['gid'], $row['username']);
                }
            }
            return $return;
        }
    }

    /**
     * 检查用户email是否已经被注册
     * @param string $email 用户email
     * @param int $status $status
     * @param int $withUsername $withUsername
     * @return int 状态码
     * desc:该函数兼容加解密
     */
    public function checkEmail($email, $status = 1, $withUsername = 1)
    {
        unset($status);
        $email = strtolower($email);
        if ($withUsername) {
            if ($this->getPassIdByUsername($email)) {
                return 2;
            }
        }
        $row = $this->getPassIdByEmail($email);
        if (1 == $row[0]) {
            return 3;
        }
        return 0;
    }

    /**
     * 检查email是否被验证
     * @param string $email 用户email
     * @param bool $needPassId 是否返回passid
     * @return int 状态码 1|0
     */
    public function checkVerifiedEmail($email, $needPassId = false)
    {
        $email = strtolower($email);
        $row = $this->getPassIdByEmail($email);
        if (1 == $row[0]) {
            if ($needPassId) {
                return $row[1];
            }
            return 1;
        }
        return 0;
    }

    /**
     * 根据email获取passid
     * @param string $email email
     * @param int $status 验证状态
     * @return array passid
     * desc:该函数兼容加解密
     */
    public function getPassIdByEmail($email, $status = 1)
    {
        unset($status);
        $pdoNew = $this->getPdoNew();
        $table = GetHashTable::MembersEmail($email);
        $result = $pdoNew->find("SELECT passid FROM {$table} WHERE email = :email or email=:aesEmail LIMIT 1", array(":email" => $email, ":aesEmail" => Sensitive::Encode($email)));
        if ($result) {
            return array(1, $result['passid']);
        } else {
            return array(0);
        }
    }

    /**
     * 根据用户手机号获取passid
     * @param string $phone 手机号
     * @param bool $cache 是否强制使用缓存
     * @return int passid
     * desc:该函数兼容加解密
     */
    public function getPassIdByPhone($phone, $cache = true)
    {
        if ($cache && $this->isUserInfoRedis) {
            return $this->getPassIdByPhoneUseRedis($phone);
        }
        $pdoNew = $this->getPdoNew();
        $table = GetHashTable::MembersPhone($phone);
        $sql = "SELECT passid " .
            "FROM {$table} " .
            "WHERE phone = :phone or phone=:aesPhone";
        $row = $pdoNew->find($sql, array(":phone" => $phone, ':aesPhone' => Sensitive::Encode($phone)));
        if ($row) {
            return $row['passid'];
        } else {
            return false;
        }
    }

    /**
     * 根据用户手机号获取passid
     * @param string $phone 手机号
     * @return int passid
     */
    public function getPassIdByPhoneUseRedis($phone)
    {
        $memberRedis = loadAction('memberRedis');
        $passId = $memberRedis->hashGet($phone, REG_PHONE_INFO_REDIS_KEY, $phone, 'passId');
        if (empty($passId)) {
            if ($passId = $this->getPassIdByPhone($phone, false)) {
                if ($row = $this->getMember($passId, ['id', 'uid', 'gid', 'username'])) {
                    $row['uid'] = serialize([1 => (int)$row['uid']]);
                    $this->saveRegPhoneToRedis($phone, $row['id'], $row['uid'], $row['gid'], $row['username']);
                }
                return $passId;
            }
            return false;
        } else {
            $memberRedis->closeUserDataRedis();
            return $passId;
        }
    }

    /**
     * 功能：根据用户passid获取手机号, 可主库获取
     * 作者：<EMAIL>
     * 日期：2015.03.19
     * @param int $passid $passid
     * @param bool $isWriteTable $isWriteTable
     * @return bool
     * desc:该函数兼容加解密
     */
    public function getPhoneByPassid($passid, $isWriteTable = false)
    {
        $row = $this->getMember($passid, ['phone'], $isWriteTable);
        if ($row) {
            return $row['phone'] ? $row['phone'] : false;
        } else {
            return false;
        }
    }

    /**
     * 功能：根据用户passid获取登陆类型
     * 作者：<EMAIL>
     * 日期：2015.03.19
     * @param int $passid $passid
     * @return bool
     */
    public function getGidByPassid($passid)
    {
        $row = $this->getMember($passid, ['gid']);
        if ($row) {
            return $row['gid'];
        } else {
            return false;
        }
    }

    /**
     * 修改用户名
     *
     * @param int $passid 用户passid
     * @param string $username 用户名
     *
     * @return boolean
     */
    public function updateUsername($passid, $username)
    {
        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        try {
            $table = GetRangeTable::Members($passid);
            $sql = "SELECT uid, username FROM {$table} WHERE id = :passid";
            $row = $pdoNew->find($sql, array(":passid" => $passid));
            $update = array('username' => $username,);
            $condition = array(
                'where' => 'id = :passid',
                'params' => array(':passid' => $passid,),
            );
            $table = GetRangeTable::Members($passid);
            $res = $pdoNew->update($table, $update, $condition);
            if ($res !== false && $row) {
                // 更新username表
                $table = Encoding::transcoding($row['username'], 'gbk', 'utf-8');
                $table = GetHashTable::MembersUsername($table);
                $result = $pdoNew->delete($table, [
                    'where' => 'username = :username',
                    'params' => [':username' => $row['username']]
                ]);
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
                $table = Encoding::transcoding($username, 'gbk', 'utf-8');
                $table = GetHashTable::MembersUsername($table);
                $result = $pdoNew->insert($table, [
                    'passid' => $passid,
                    'username' => $username
                ], false);
                if (!$result) {
                    $pdoNew->rollBack();
                    return false;
                }
                $pdoNew->commit();
                //  修改用户名通知
                noticeToChange($passid, 'changeUsername', array(
                    'passid' => $passid,
                    'uid' => (int)$row['uid'],
                    'type' => 'username',
                    'value' => $username,
                ));
                return true;
            }
            $pdoNew->rollBack();
            return false;
        } catch (\Exception $e) {
            $pdoNew->rollBack();
            return false;
        }
    }

    /**
     * 修改密码
     *
     * @param int $passid 用户passid
     * @param string $password 密码
     * @param int $pwdStrength 密码强度
     * @param string $ip ip
     *
     * @return boolean
     */
    public function updatePassword($passid, $password, $pwdStrength = 1, $ip = '0.0.0.0')
    {
        $pdoNew = $this->getPdoNew();
        $salt = rand(100000, 999999);
        $update = array(
            'salt' => $salt,
            'password' => md5(md5($password) . $salt),
            'pwd_strength' => $pwdStrength,
        );
        $table = GetRangeTable::Members($passid);
        $row = $pdoNew->find("SELECT uid, gid FROM {$table} WHERE id = :passid", array(":passid" => $passid), true);
        if ($row['gid'] % 100 == 0) {
            $update['gid'] = 200;
        }
        $condition = array(
            'where' => 'id = :passid',
            'params' => array(':passid' => $passid,)
        );
        $status = $pdoNew->update($table, $update, $condition);
        //插入修改密码日志
        $logTable = 'edit_pwd_log_' . (($passid % 20) + 1);
        $insert = array(
            'passid' => $passid,
            'ip' => $ip,
            'time' => time(),
        );
        $res = $this->getPdoLog()->insert($logTable, $insert, false);
        $redis = RedisEx::getInstance();
        $sessionIds = $redis->zRange("LoginSession:$passid", 0, -1);
        $redis->del("LoginSession:$passid");
        //修改密码之后，设置key，青鸟获取到之后退出登陆
        $redis->set("redis:cache:member:update:password:passid:$passid", 1, 2592000);
//        LSessionReport::Log('del', "LoginSession:$passid", "", time());
        foreach ($sessionIds as $sessionId) {
            $redis->zRem("LoginSessions", $sessionId);
        }
        //清理缓存
        $memberRedis = loadAction('memberRedis');
        $memberRedis->delMemberCache($passid);

        noticeToChange($passid, 'changePassword', array(
            'passid' => $passid,
            'uid' => (int)$row['uid'],
            'type' => 'password',
            'value' => "",
        ));

        $this->setPasswordCache($passid, $update["password"] . '@' . $update["salt"]);
        return $status;
    }

    /**
     * 功  能：根据用户名查询passid
     *
     * @param string $username $username
     * @return bool
     */
    public function getPassIdByUsername($username)
    {
        $pdoNew = $this->getPdoNew();
        $table = Encoding::transcoding($username, 'gbk', 'utf-8');
        $table = GetHashTable::MembersUsername($table);
        $sql = "SELECT passid " .
            "FROM {$table} " .
            "WHERE username = :username " .
            "LIMIT 1";
        $row = $pdoNew->find($sql, [":username" => $username]);
        if ($row) {
            return $row['passid'];
        } else {
            return false;
        }
    }

    /**
     * 根据用户email查询用户名
     * @param string $email 用户email
     * @return array 用户名
     */
    public function getMemberFromEmail($email)
    {
        $row = $this->getPassIdByEmail($email);
        if (1 == $row[0]) {
            return $this->getMember($row[1], ['username']);
        }
        return false;
    }

    /**
     * 设置登录日志，写入缓存队列
     * @param int $passid 用户passid
     * @param string $time 用户登录时间
     * @param string $ip 用户登录ip
     * @return boolean 成功与否
     */
    public function setLogin($passid, $time, $ip)
    {
        try {
            $data = array(
                "passid" => $passid,
                "time" => $time,
                "ip" => $ip,
            );
            return \RedisEx::getInstance()->lPush("log:setLogin", serialize($data));
        } catch (\Exception $e) {
            return $this->setLoginDb($passid, $time, $ip);
        }
    }

    /**
     * 设置登录日志，写入数据库
     * <AUTHOR>
     * @DateTime 2018-05-23T13:53:31+0800
     * @param    [type]                   $passid [description]
     * @param    [type]                   $time   [description]
     * @param    [type]                   $ip     [description]
     * @return [type] [<description>]
     */
    public function setLoginDb($passid, $time, $ip)
    {
        $update = array(
            'login_time' => $time,
            'login_ip' => $ip,
        );
        $condition = array(
            'where' => 'id = :passid',
            'params' => array(':passid' => $passid,)
        );
        try {
            $pdoNew = $this->getPdoNew();
            $table = GetRangeTable::Members($passid);
            return $pdoNew->update($table, $update, $condition);
        } catch (\Exception $e) {
            return false;
        }
    }


    /**
     * 记录用户信息修改日志（TO DO：目前只记录没查询）
     * @param int $passid 用户passid
     * @param array $info 日志信息
     * @return boolean 记录成功与否
     * desc:该函数兼容加解密
     */
    public function memberInfoLog($passid, $info)
    {
        $info['uid'] = $passid;

        if (isset($info['email'])) {
            $info['email'] = Sensitive::Encode($info['email']);
        }
        if (isset($info['email_old'])) {
            $info['email_old'] = Sensitive::Encode($info['email_old']);
        }

        return $this->getPdoLog()->insert('members_info_log', $info, false);
    }

    /**
     * 添加完善用户名日志
     *
     * @param int $passid $passid
     * @param string $log $log
     * @return bool|int
     */
    public function patchUserLog($passid, $log)
    {
        $log['passid'] = $passid;
        $res = $this->getPdoLog()->insert('members_log', $log, false);
        return $res;
    }

    /**
     *
     * @param int $passid $passid
     * @param string $log $log
     * @return bool|int
     * <AUTHOR>
     */
    public function patchNicknameLog($passid, $log)
    {
        $log['passid'] = $passid;
        return $this->getPdoLog()->insert('members_nickname_log', $log, false);
    }

    /**
     * 记录可疑登录用户
     * @param int $passid 用户passid
     * @param int $uid 用户uid
     * @param string $username 用户名
     * @param string $ip 登录ip
     * @param string $ua 用户ua
     * @param string $domain 服务器域名
     * @param string $logDate 记录时间
     * @return boolean 记录成功与否
     */
    public function logSuspectLogin($passid, $uid, $username, $ip, $ua, $domain, $logDate)
    {
        $varArray = array(
            'passid' => $passid,
            'uid' => $uid,
            'username' => $username,
            'ip' => $ip,
            'ua' => $ua,
            'domain' => $domain,
            'log_date' => $logDate,
        );
        return $this->pdo->insert('login_suspect_log', $varArray, false);
    }

    /**
     * 功  能：记录登录日志
     *
     * @param string $domain $domain
     * @param string $serverIp $serverIp
     * @param string $ip $ip
     * @param string $username $username
     * @param string $password $password
     * @param int $status $status
     * @return bool|int
     */
    public function loginInfoLog($domain, $serverIp, $ip, $username, $password, $status)
    {
        $varArray = array(
            'domain' => $domain,
            'server' => $serverIp,
            'ip' => $ip,
            'username' => $username,
            'password' => $password,
            'status' => $status,
        );
        return $this->pdo->insert('login_info_log', $varArray, false);
    }

    /**
     * 功  能：记录找回密码日志
     *
     * @param string $ip $ip
     * @param string $ua $ua
     * @param string $passid $passid
     * @param string $from $from
     * @param string $value $value
     * @param int $code $code
     * @return bool|int
     */
    public function logFind($ip, $ua, $passid, $from, $value, $code)
    {
        $varArray = array(
            'ip' => $ip,
            'ua' => $ua,
            'passid' => $passid,
            'from' => $from,
            'value' => Sensitive::Encode($value),
            'code' => $code,
        );
        return $this->getPdoLog()->insert('find_log', $varArray, false);
    }

    /**
     * 根据用户名查询passid
     * @param string $username 用户名
     * @param boolean $fuzzy 是否模糊搜索
     * @return array 用户passid数组
     */
    public function GetIdsByUsername($username, $fuzzy = false)
    {
        unset($fuzzy);
        if ($result = $this->getPassIdByUsername($username)) {
            return [$result];
        }
        return [];
    }

    /**
     * 改造后只能搜索到验证的邮箱信息
     *
     * 根据email查询用户信息
     * @param string $email 用户email
     * @return array 用户信息列表
     */
    public function getUserByEmail($email)
    {
        $row = $this->getPassIdByEmail($email);
        if (1 != $row[0]) {
            return [];
        }
        $data = $this->getMember($row[1], ['username']);
        return [
            [
                'username' => $data['username'],
                'uid' => $row[1],
                'email' => $email,
            ],
        ];
    }

    /**
     * 根据passid查询用户名
     * @param int $passid 用户passid
     * @return array 用户名
     */
    public function getUsernameByPassid($passid)
    {
        return $this->getMember($passid, ['username'], true);
    }

    /**
     * 根据passid查询邮箱
     * @param int $passid 用户passid
     * @return array 邮箱
     */
    public function getEmailByPassId($passid)
    {
        $row = $this->getUserInfoByPassId($passid, ['email', 'email_status'], true);
        if ($row && strpos($row['email'], "@bookguest.2345.com") !== false) {
            $row['email'] = "";
            $row['email_status'] = 0;
        }
        return $row;
    }

    /**
     * 检查用户弱密码
     * @param int $passid 用户passid
     * @return int 是否弱 1|0
     */
    public function checkWeakPassword($passid)
    {
        $row = $this->getMember($passid, ['password', 'salt']);
        if ($row && $row["password"] == md5(md5("123456") . $row["salt"])) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 获取用户token
     * @param int $passid 用户passid
     * @param bool $enforceUseCache $enforceUseCache
     * @return string token用户浏览器自动登录等
     */
    public function getToken($passid, $enforceUseCache = false)
    {
        $pwdInfo = $this->getPasswordInfo($passid, $enforceUseCache);
        if ($pwdInfo) {
            return md5($pwdInfo['password'] . $pwdInfo['salt']);
        } else {
            return false;
        }
    }

    /**
     * 获取用户密码
     * @param int $passid 用户passid
     * @param bool $enforceUseCache $enforceUseCache
     * @return string 用户密码
     */
    public function getPasswordInfo($passid, $enforceUseCache = false)
    {
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis) {
            $redis = RedisEx::getInstance();
            $pwdCache = $redis->get("pwd:$passid");
            if ($pwdCache) {
                $this->setPasswordCache($passid, $pwdCache);
                $pwdCache = explode("@", $pwdCache);
                return array('password' => $pwdCache[0], 'salt' => $pwdCache[1]);
            } elseif ($enforceUseCache) {
                return false;
            }
        }
        $row = $this->getMember($passid, ['password', 'salt']);
        if ($row) {
            $this->setPasswordCache($passid, $row["password"] . '@' . $row["salt"]);
            return $row;
        } else {
            return false;
        }
    }

    /**
     * 设置密码缓存
     * @param int $passid 用户passid
     * @param string $pwdCache 密码缓存
     * @return void
     */
    private function setPasswordCache($passid, $pwdCache)
    {
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis) {
            $redis = RedisEx::getInstance();
            $redis->setex("pwd:$passid", 604800, $pwdCache);
        }
    }

    /**
     * 设置头像状态
     * @param int $passid $passid
     * @param string $path $path
     * @param int $status $status
     * @return int
     * <AUTHOR>
     */
    public function avatarState($passid, $path, $status)
    {
        $data = array(
            'passid' => $passid,
            "path" => $path,
            "uptime" => date("Y-m-d"),
            "status" => $status
        );
        $row = $this->getPdoNew()->duplicate("avatar", $data);
        if ($row) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * @param int $days 天数
     *
     * @return array|bool 头像
     */
    public function latestUnCheckAvatar($days = 4)
    {
        $sql = "select uptime from avatar where status = :status group by uptime order by uptime desc limit $days";
        $condition = array(":status" => 0);
        $row = $this->getPdoNew()->findAll($sql, $condition);
        if (!empty($row)) {
            $endTime = $row[0]['uptime'];
            $startTime = $row[count($row) - 1]['uptime'];
            $sql = "SELECT passid, path, uptime FROM avatar WHERE status = :status AND uptime >= :startTime AND uptime <= :endTime";
            $condition = array(":status" => 0, ":startTime" => $startTime, "endTime" => $endTime);

            return $this->getPdoNew()->findAll($sql, $condition);
        } else {
            return array();
        }
    }

    /**
     * uncheck avatar
     *
     * @param string $startTime startTime
     * @param string $endTime endTime
     *
     * @return array|boolean
     */
    public function uncheckAvatar($startTime, $endTime)
    {
        $sql = "SELECT passid, path, uptime FROM avatar WHERE status = :status AND uptime >= :startTime AND uptime <= :endTime";
        $condition = array(":status" => 0, ":startTime" => $startTime, "endTime" => $endTime);
        $row = $this->getPdoNew()->findAll($sql, $condition);
        return $row;
    }

    /**
     * 查找已经审核的图片
     * @param string $startTime $startTime
     * @param string $endTime $endTime
     * @return array|bool
     * <AUTHOR>
     */
    public function vettedAvatar($startTime, $endTime)
    {
        $sql = "SELECT passid, path, uptime FROM avatar WHERE status = :status AND uptime >= :startTime AND uptime <= :endTime";
        $condition = array(":status" => 1, ":startTime" => $startTime, "endTime" => $endTime);
        $row = $this->getPdoNew()->findAll($sql, $condition);
        return $row;
    }

    /**
     * 更新用户头像审核状态
     * @param array $checklist $checklist
     * @return bool|int
     * <AUTHOR>
     */
    public function vetAvatar($checklist)
    {
        foreach ($checklist as $passid) {
            $data = array("status" => 1);
            $condition = array("where" => "passid= :passid", "params" => array(":passid" => $passid));
            $ret = $this->getPdoNew()->update("avatar", $data, $condition);
        }
        return $ret;
    }

    /**
     * 删除用户上传头像
     * @param array $deletelist $deletelist
     * @return bool|int
     * <AUTHOR>
     */
    public function deleteAvatar($deletelist)
    {
        foreach ($deletelist as $passid) {
            $data = array("status" => 2);
            $condition = array("where" => "passid= :passid", "params" => array(":passid" => $passid));
            $ret = $this->getPdoNew()->update("avatar", $data, $condition);
        }
        return $ret;
    }

    /**
     * 用户上传头像是否已删除
     * @param int $passid $passid
     * @return bool|mixed
     * <AUTHOR>
     */
    public function isDelete($passid)
    {
        $sql = "SELECT status,uptime FROM avatar WHERE passid = :passid";
        $condition = array(":passid" => $passid);
        $avatarStatus = $this->getPdoNew()->find($sql, $condition);
        return $avatarStatus;
    }

    /**
     * 更改头像为关闭提醒状态
     * @param int $passid $passid
     * <AUTHOR>
     * @return void
     */
    public function closeAvatar($passid)
    {
        $data = array("status" => 3);
        $condition = array("where" => "passid= :passid", "params" => array(":passid" => $passid));
        $ret = $this->getPdoNew()->update("avatar", $data, $condition);
    }

    /**
     * 根据passid查询用户信息
     * @param int $passid 用户passid
     * @param array $columns $columns
     * @param bool $masterDb $masterDb
     * @return array 用户名
     * desc:该函数兼容加解密
     */
    public function getUserInfoByPassId($passid, $columns = [], $masterDb = false)
    {
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::MembersInfo($passid);
        $columns = empty($columns) ? '*' : implode(',', $columns);
        $sql = "SELECT {$columns} " .
            "FROM {$table} " .
            "WHERE passid = :passid";
        $params = array(":passid" => $passid);
        $return = $pdoNew->find($sql, $params, $masterDb);
        if ($return) {

            // desc:字段加解密兼容
            if (isset($return['email'])) {
                $return['email'] = Sensitive::Decode($return['email']);
            }
            if (isset($return['phone'])) {
                $return['phone'] = Sensitive::Decode($return['phone']);
            }

            if (isset($return['passid'])) {
                $return = array_merge(['uid' => $return['passid']], $return);
                unset($return['passid']);
            }
            return $return;
        }
        return false;
    }

    /**
     * 功  能：获取member表 member_info表数据
     *
     * @param int $passId 用户passid
     * @param array $columns $columns
     * @param bool $masterDb 是否使用主库
     * @return array|bool
     * desc:该函数兼容加解密
     */
    private function getMoreUserInfo($passId, $columns = [], $masterDb = false)
    {
        if (empty($columns)) {
            return false;
        }
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passId);
        $tableInfo = GetRangeTable::MembersInfo($passId);
        $sql = "SELECT " . implode(',', $columns) . " " .
            "FROM {$table} m LEFT JOIN {$tableInfo} mi ON `m`.`id` = `mi`.`passid` " .
            "WHERE m.id = :id";
        $data = $pdoNew->find($sql, [':id' => $passId], $masterDb);

        // desc:字段加解密兼容
        if (isset($data['email'])) {
            $data['email'] = Sensitive::Decode($data['email']);
        }
        if (isset($data['phone'])) {
            $data['phone'] = Sensitive::Decode($data['phone']);
        }
        return $data;
    }

    public function getMultiUserInfo($passid)
    {
        $columns = [
            'm.username',
            'm.uid AS m_uid',
            'm.gid',
            'm.id',
            'm.reg_ip',
            'm.reg_time',
            'm.login_ip',
            'm.login_time',
            'm.locked',
            'm.phone',
            'm.pwd_strength',
            'mi.name',
            'mi.gender',
            'mi.bday',
            'mi.qq',
            'mi.area',
            'mi.email',
            'mi.email_status',
        ];
        $passidTable = [];
        if (! is_array($passid)) {
            $table = GetRangeTable::Members($passid);
            $tableInfo = GetRangeTable::MembersInfo($passid);
            $passidTable["{$table}||{$tableInfo}"] = [$passid];
        } else {
            foreach ($passid as $item) {
                $table = GetRangeTable::Members($item);
                $tableInfo = GetRangeTable::MembersInfo($item);
                if (isset($passidTable["{$table}||{$tableInfo}"])) {
                    $passidTable["{$table}||{$tableInfo}"][] = $item;
                } else {
                    $passidTable["{$table}||{$tableInfo}"] = [$item];
                }
            }
        }
        $pdoNew = $this->getPdoNew();
        $ret = [];
        foreach ($passidTable as $table => $passidArr) {
            list($from, $join) = explode('||', $table);
            //拼接sql
            $condition = [];
            foreach ($passidArr as $key => $value) {
                $tmp = ":passid{$key}";
                $condition[$tmp] = $value;
            }
            $sql = "SELECT "
                . implode(',', $columns) . " FROM {$from} m LEFT JOIN {$join} mi ON `m`.`id` = `mi`.`passid` WHERE m.id in (" 
                . implode(',', array_keys($condition)) . ")";
            $result = $pdoNew->findAll($sql, $condition);
            if ($result) {
                $ret = array_merge($ret, $result);
            }

        }
        return $ret;
    }

    /**
     * 功  能：
     *
     * @param int $passid $passid
     * @return array|bool
     */
    public function getMoreUserInfoByPassid($passid)
    {
        return $this->getMoreUserInfo($passid, [
            'mi.name',
            'm.username',
            'mi.gender',
            'mi.bday',
            'mi.email',
            'mi.msn',
            'mi.qq',
            'mi.address',
            'mi.zip',
            'mi.city',
            'mi.phone',
            'mi.tel1',
            'mi.tel2',
            'mi.tel3',
            'mi.area',
        ]);
    }

    /**
     * generatePassId
     * -
     * @return bool|int
     * <AUTHOR>
     */
    public function generatePassId()
    {
        $passId = false;
        $pdoNew = $this->getPdoNew();
        $result = $pdoNew->insert('members_passId_incr', array('cTime' => date('Y-m-d H:i:s')));
        if ($result) {
            $passId = intval($pdoNew->lastInsertId());
        }
        return $passId;
    }

    /**
     * generatePassIdTest
     * -
     * @return bool|int
     * <AUTHOR>
     */
    public function generatePassIdTest()
    {
        $passId = false;
        $result = $this->pdo->insert('members_passId_incr_test', array('cTime' => date('Y-m-d H:i:s')));
        if ($result) {
            $passId = intval($this->pdo->lastInsertId());
        }
        return $passId;
    }

    /**
     * generateUIDTest
     * to do 可能下线, 暂时不动
     * -
     * @param string $username UserName
     * @return void
     * <AUTHOR>
     */
    public function generateUIDTest($username)
    {
        $varArray = array(
            'username' => $username,
            'password' => md5($username),
            'email' => '',
            'regip' => '127.0.0.1',
            'regdate' => time(),
        );
        $this->pdo->insert('bbs9991.cdb_members_test', $varArray, false);
    }

    /**
     * isStartSetPassId
     * -
     * @return bool
     * <AUTHOR>
     */
    private function isStartSetPassId()
    {
        return true;
    }

    /**
     * 根据时间删除PassId,删除一个时间段前的数据(不得小于1小时)
     * -
     * @param int $time 时间长度
     * @return bool|int
     * <AUTHOR>
     */
    public function delPassIdByTime($time)
    {
        if (empty($time) || $time < 3600) {
            return false;
        }

        $endTime = date('Y-m-d H:i:s', time() - $time);
        $condition = array(
            "where" => "cTime < :cTime",
            "params" => array(":cTime" => $endTime)
        );

        $result = $this->getPdoNew()->delete('members_passId_incr', $condition);
        return $result;
    }

    /**
     * to do to do 可能下线, 暂时不动
     *
     * getMembersCount
     * -
     * @param int $id Id
     * @return array|bool
     * <AUTHOR>
     */
    public function getMembersCount($id = 0)
    {
        $sql = "select count(id) cn from members";
        $param = array();
        if ($id > 0) {
            $sql .= " where id <= :id";
            $param = array("id" => $id);
        }
        $findResult = $this->pdo->find($sql, $param);
        if (!empty($findResult) && count($findResult) > 0) {
            return $findResult['cn'];
        }
        return false;
    }

    /**
     * to do 可能下线, 暂时不动
     *
     * getUserListByPage
     * -
     * @param int $pageIndex pageIndex(0开始)
     * @param int $pageSize 每页个数
     * @param int $offset 开始位置
     * @param int $normalPageSize 常规页数(非最后一页)
     * @return array|bool
     * <AUTHOR>
     */
    public function getUserListByPage($pageIndex, $pageSize, $offset, $normalPageSize)
    {
        $sql = "SELECT 
m.id as passId,
m.username, 
m.uid AS m_uid, 
m.gid, 
m.id, 
m.reg_ip, 
m.reg_time, 
m.login_ip, 
m.login_time, 
m.locked, 
i.name, 
i.gender, 
i.bday, 
i.qq, 
i.area, 
i.email, 
i.email_status, 
p.phone, 
m.pwd_strength
FROM members m
LEFT JOIN members_info AS i ON m.id = `i`.`uid`
LEFT JOIN members_phone AS p ON m.id = `p`.`passid`
ORDER BY m.id";
        $sql .= " LIMIT " . ($offset + $normalPageSize * $pageIndex) . "," . $pageSize;
        $row = $this->pdo->findAll($sql, array());
        return $row;
    }

    /**
     * to do 可能下线, 暂时不动
     * getMembersPhoneCount
     * -
     * @param string $endTime 截止时间
     * @return bool
     * <AUTHOR>
     */
    public function getMembersPhoneCount($endTime)
    {
        $sql = "select count(passid) cn from members_phone";
        $param = array();
        if (!empty($endTime)) {
            $sql .= " where last_update <= :endTime";
            $param = array("endTime" => $endTime);
        }
        $findResult = $this->pdo->find($sql, $param);
        if (!empty($findResult) && count($findResult) > 0) {
            return $findResult['cn'];
        }
        return false;
    }

    /**
     * to do 可能下线, 暂时不动
     * getRegPhoneListByPage
     * -
     * @param int $pageIndex pageIndex(0开始)
     * @param int $pageSize 每页个数
     * @param int $offset 开始位置
     * @param int $normalPageSize 常规页数(非最后一页)
     * @return array|bool
     * <AUTHOR>
     */
    public function getRegPhoneListByPage($pageIndex, $pageSize, $offset, $normalPageSize)
    {
        $sql = "SELECT p.passid AS passId, p.phone, m.uid AS m_uid, m.gid,m.username
FROM members_phone p
LEFT JOIN members m ON `p`.`passid` = `m`.`id`
ORDER BY passid";
        $sql .= " LIMIT " . ($offset + $normalPageSize * $pageIndex) . "," . $pageSize;
        $row = $this->pdo->findAll($sql, array());
        return $row;
    }

    /**
     * to do 可能下线, 暂时不动
     * getRegPhoneListByPage
     * -
     * @param int $pageSize 每页个数
     * @param int $startPassId 从这个Id开始查询
     * @return array|bool
     * <AUTHOR>
     */
    public function getRegPhoneListByPageNew($pageSize, $startPassId)
    {
        $sql = "SELECT p.passid AS passId, p.phone, m.uid AS m_uid, m.gid,m.username
FROM members_phone p
LEFT JOIN members m ON `p`.`passid` = `m`.`id`
WHERE passId > :startPassId
ORDER BY passid";
        $sql .= " LIMIT 0," . $pageSize;
        $row = $this->pdo->findAll($sql, array(':startPassId' => $startPassId));
        return $row;
    }

    /**
     * saveRegPhoneToRedis
     * -
     * @param string $phone Phone
     * @param string $passId PassId
     * @param string $mUID MUID
     * @param string $gid GID
     * @param string $userName $userName
     * @return bool
     * <AUTHOR>
     */
    public function saveRegPhoneToRedis($phone, $passId, $mUID, $gid, $userName)
    {
        if (empty($phone) || empty($passId) || empty($mUID) || empty($gid) || empty($userName)) {
            return false;
        }
        if (!is_array($mUID)) {
            $mUID = unserialize($mUID);
        }
        $saveInfo = $this->getSaveInfoMemberRedis($passId, $mUID, $gid, $userName);

        $memberRedis = loadAction('memberRedis');
        $memberRedis->hashSet($phone, REG_PHONE_INFO_REDIS_KEY, $phone, serialize($saveInfo));
        $memberRedis->closeUserDataRedis();
    }

    /**
     * getSaveInfoMemberRedis
     * -
     * @param string $passId PassId
     * @param string $mUID uid
     * @param int $gid GID
     * @param string $userName UserName
     * @return mixed
     * <AUTHOR>
     */
    public function getSaveInfoMemberRedis($passId, $mUID, $gid, $userName)
    {
        $saveInfo['passId'] = $passId; //passId作为hashKey
        $saveInfo['mUID'] = $mUID;
        $saveInfo['gid'] = $gid;
        $saveInfo['userName'] = $userName;
        return $saveInfo;
    }

    /**
     * delRegPhoneToRedis
     * -
     * @param string $phone Phone
     * @return void
     * <AUTHOR>
     */
    public function delRegPhoneToRedis($phone)
    {
        $memberRedis = loadAction('memberRedis');
        $memberRedis->hashDel($phone, REG_PHONE_INFO_REDIS_KEY, $phone);
        $memberRedis->closeUserDataRedis();
    }

    /**
     * 功  能：批量获取昵称
     *
     * @param array $passIdList $passIdList
     * @return array|bool
     */
    public function getNicknameByPassIdList($passIdList)
    {
        if (!$passIdList) {
            return [];
        }
        $passIdParams = [];
        foreach ($passIdList as $key => $passId) {
            $passIdParams[":id$key"] = $passId;
        }
        $sql = "SELECT * FROM members_nickname WHERE passid IN (" . implode(",", array_keys($passIdParams)) . ")";
        return $this->getPdoNew()->findAll($sql, $passIdParams);
    }

    /**
     * 批量获取新库的用户主信息
     * -
     * @param array $passIdList 用户唯一ID
     * @param array $columns 需要的字段
     * @return array|bool
     * desc:该函数兼容加解密
     */
    public function getNewMemberByPassIdList($passIdList, $columns = [])
    {
        $return = [];
        $tables = self::getMemberMultiTable($passIdList);
        if (!$tables) {
            return [];
        }
        $pdoNew = $this->getPdoNew();
        $columns = $columns ? implode(',', $columns) : '*';
        foreach ($tables as $table => $passIds) {
            $passIdParams = [];
            foreach ($passIds as $key => $passId) {
                $passIdParams[":id$key"] = $passId;
            }
            $sql = "SELECT {$columns} FROM {$table} WHERE id IN (" . implode(",", array_keys($passIdParams)) . ")";
            $tmp = $pdoNew->findAll($sql, $passIdParams);
            if ($tmp) {
                $return = array_merge($return, $tmp);
            }
        }

        foreach ($return as $key => $val) {
           if (isset($val['phone'])) {
               $return[$key]['phone'] = Sensitive::Decode($val['phone']);
           }
            if (isset($val['email'])) {
                $return[$key]['email'] = Sensitive::Decode($val['email']);
            }
        }
        return $return;
    }

    /**
     * 批量获取新库 用户主信息
     * -
     * @param array $passIdList 用户唯一ID
     * @return array|bool
     * desc:该函数兼容加解密
     */
    public function getNewMemberInfoByPassIdList($passIdList)
    {
        $return = [];
        $tables = self::getMemberInfoMultiTable($passIdList);
        if (!$tables) {
            return [];
        }
        $pdoNew = $this->getPdoNew();
        foreach ($tables as $table => $passIds) {
            $passIdParams = [];
            foreach ($passIds as $key => $passId) {
                $passIdParams[":id$key"] = $passId;
            }
            $sql = "SELECT * FROM {$table} WHERE passid IN (" . implode(",", array_keys($passIdParams)) . ")";
            $tmp = $pdoNew->findAll($sql, $passIdParams);
            if ($tmp) {
                $return = array_merge($return, $tmp);
            }
        }

        foreach ($return as $key => $val) {
            $return[$key] = DecodeMembersInfo($val);
        }
        return $return;
    }

    /**
     * 批量获取新库 用户绑定信息
     * -
     * @param array $openidList 用户唯一ID
     * @return array|bool
     */
    public function getNewMemberBindByPassIdList($openidList)
    {
        $return = [];
        $tables = self::getBindMultiTable($openidList);
        if (!$tables) {
            return [];
        }
        $pdoNew = $this->getPdoNew();
        foreach ($tables as $table => $passIds) {
            $passIdParams = [];
            foreach ($passIds as $key => $passId) {
                $passIdParams[":id$key"] = $passId;
            }
            $sql = "SELECT * FROM {$table} WHERE openid IN (" . implode(",", array_keys($passIdParams)) . ")";
            $tmp = $pdoNew->findAll($sql, $passIdParams);
            if ($tmp) {
                foreach ($tmp as $key => $item) {
                    $tmp[$key]['bindtime'] = strtotime($item['bindtime']);
                }
                $return = array_merge($return, $tmp);
            }
        }
        return $return;
    }

    /**
     * 批量获取新库 用户手机信息
     * -
     * @param array $phoneList 用户唯一ID
     * @return array|bool
     * desc:该函数兼容加解密
     */
    public function getNewMemberPhoneByPassIdList($phoneList)
    {
        $return = [];
        $tables = self::getPhoneMultiTable($phoneList);
        if (!$tables) {
            return [];
        }
        $pdoNew = $this->getPdoNew();
        foreach ($tables as $table => $passIds) {
            $passIdParams = [];
            foreach ($passIds as $key => $passId) {
                $passIdParams[":id$key"] = $passId;
            }
            $sql = "SELECT * FROM {$table} WHERE phone IN (" . implode(",", array_keys($passIdParams)) . ")";
            $tmp = $pdoNew->findAll($sql, $passIdParams);
            if ($tmp) {
                foreach ($tmp as $key => $item) {
                    $tmp[$key]['last_update'] = strtotime($item['last_update']);
                }
                $return = array_merge($return, $tmp);
            }
        }

        foreach ($return as $key => $val) {
            if (isset($val['phone'])) {
                $return[$key]['phone'] = Sensitive::Decode($val['phone']);
            }
        }

        return $return;
    }

    /**
     * 功  能：更新email
     *
     * @param int $passId passId
     * @param string $email $email
     * @return bool
     */
    public function editEmail($passId, $email)
    {
        $memberRedis = loadAction('memberRedis');
        $memberRedis->delMemberCache($passId);
        
        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        $table = GetRangeTable::Members($passId);
        // 删除旧的邮箱
        $sql = "SELECT email FROM {$table} WHERE id=:id";
        $row = $pdoNew->find($sql, [':id' => $passId]);

        if (isset($row['email'])) {
            $row['email'] = Sensitive::Decode($row['email']);
        }
        if ($row && $row['email']) {
            $tableEmail = GetHashTable::MembersEmail($row['email']);
            $result = $pdoNew->delete($tableEmail, [
                'where' => 'email = :email or email=:aesEmail',
                'params' => [':email' => $row['email'], ':aesEmail' => Sensitive::Encode($row['email'])]
            ]);
            if (!$result) {
                $pdoNew->rollBack();
                return false;
            }
        }
        $result = $pdoNew->update($table, ['email' => Sensitive::Encode($email)], [
            'where' => 'id = :id',
            'params' => [':id' => $passId]
        ]);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $result = $pdoNew->update(
            GetRangeTable::MembersInfo($passId),
            [
                "email" => Sensitive::Encode($email),
                'email_status' => empty($email) ? 0 : 1,
            ],
            [
                "where" => "passid = :passid",
                "params" => [":passid" => $passId]
            ]
        );
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        if (empty($email)) {
            $pdoNew->commit();
            return true;
        }

        $tableEmail = GetHashTable::MembersEmail($email);
        $insert = array(
            'passid' => $passId,
            'email' => Sensitive::Encode($email),
        );
        $result = $pdoNew->insert($tableEmail, $insert, false);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $pdoNew->commit();
        return true;
    }

    /**
     * 功  能：批量获取表名
     *
     * @param array $openidList $openidList
     * @return array
     */
    public static function getBindMultiTable($openidList)
    {
        $tables = [];
        foreach ($openidList as $openid) {
            $table = GetHashTable::MembersBind($openid);
            if ($table) {
                $tables[$table][] = $openid;
            }
        }
        return $tables;
    }

    /**
     * 功  能：批量获取表名
     *
     * @param array $phoneList $phoneList
     * @return array
     */
    public static function getPhoneMultiTable($phoneList)
    {
        $tables = [];
        foreach ($phoneList as $phone) {
            $table = GetHashTable::MembersPhone($phone);
            if ($table) {
                $tables[$table][] = $phone;
            }
        }
        return $tables;
    }

    /**
     * 功  能：批量获取表名
     *
     * @param array $passIdList passIdList
     * @return array
     */
    public static function getMemberMultiTable($passIdList)
    {
        $tables = [];
        foreach ($passIdList as $passId) {
            $table = GetRangeTable::Members($passId);
            if ($table) {
                $tables[$table][] = $passId;
            }
        }
        return $tables;
    }

    /**
     * 功  能：批量获取表名
     *
     * @param array $passIdList passIdList
     * @return array
     */
    public static function getMemberInfoMultiTable($passIdList)
    {
        $tables = [];
        foreach ($passIdList as $passId) {
            $table = GetRangeTable::MembersInfo($passId);
            if ($table) {
                $tables[$table][] = $passId;
            }
        }
        return $tables;
    }

    /**
     * 功  能：根据用户名获取信息
     *
     * @param string $username username
     * @return bool
     */
    public function getUserInfoByUsername($username)
    {
        $passId = $this->getPassIdByUsername($username);
        if (!$passId) {
            return false;
        }
        return $this->getBaseUserInfo($passId);
    }

    /**
     * 功  能：
     *
     * @param string $email $email
     * @return array|bool
     */
    public function getUserInfoByEmail($email)
    {
        $passId = $this->getPassIdByEmail($email);
        if (1 != $passId[0]) {
            return false;
        }
        return $this->getBaseUserInfo($passId[1]);
    }

    /**
     * 功  能：
     *
     * @param string $phone $phone
     * @return array|bool
     */
    public function getUserInfoByPhone($phone)
    {
        $passId = $this->getPassIdByPhone($phone);
        if (!$passId) {
            return false;
        }
        return $this->getBaseUserInfo($passId);
    }

    /**
     * 功  能：
     *
     * @param int $passId $passId
     * @return array
     */
    public function getBaseUserInfo($passId)
    {
        return $this->getMoreUserInfo($passId, [
            'm.id',
            'm.username',
            'm.reg_ip',
            'm.reg_time',
            'm.login_ip',
            'm.login_time',
            'm.phone',
            'm.uid',
            'm.locked',
            'mi.email',
            'mi.email_status',
        ]);
    }

    /**
     * 功  能：批量冻结账号
     *
     * @param array $passIdList passIdList
     * @return bool|int
     */
    public function multiLock($passIdList)
    {
        $tables = self::getMemberMultiTable($passIdList);
        if (!$tables) {
            return false;
        }
        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        foreach ($tables as $table => $passIds) {
            $passIdParams = [];
            foreach ($passIds as $key => $passId) {
                $passIdParams[":id$key"] = $passId;
            }

            $res = $pdoNew->update(
                $table,
                ['locked' => 2],
                [
                    'where' => ' id in (' . implode(",", array_keys($passIdParams)) . ")",
                    'params' => $passIdParams,
                ]
            );
            if ($res === false) {
                $pdoNew->rollBack();
                return false;
            }
        }
        $pdoNew->commit();
        return true;
    }

    /**
     * 功  能：按照用户名冻结用户
     *
     * @param string $username $username
     * @return bool
     */
    public function lockByUsername($username)
    {
        $passId = $this->getPassIdByUsername($username);
        if (!$passId) {
            return false;
        }

        return $this->getPdoNew()->update(
            GetRangeTable::Members($passId),
            ['locked' => 2],
            ['where' => 'id = :id', 'params' => [':id' => $passId]]
        );
    }

    /**
     * 功  能：获取列表
     *
     * @param int $page $page
     * @param int $size $size
     * @param string $startTime $startTime
     * @param string $endTime $endTime
     * @param string $ip $ip
     * @return array
     */
    public function getUserList($page, $size, $startTime = '', $endTime = '', $ip = '')
    {
        $max = $this->getMaxId();
        if (!$max) {
            return [];
        }

        $list = [];
        $total = 0;
        $checkOffset = true;
        $checkDate = false;
        $limit = $size;
        $pdoNew = $this->getPdoNew();
        $max = GetRangeTable::Get($max) + 1;
        $offset = ($page - 1) * $limit;//总的偏移
        for ($i = 1; $i <= $max; $i++) {
            $table = "members";
            $tableInfo = "members_info";
            if ($i > 1) {
                $table .= "_" . $i;
                $tableInfo .= "_" . $i;
            }
            $where = [];
            $whereStr = "WHERE 1 ";
            if ($endTime) {
                $whereStr .= "AND m.reg_time >= :start_time ";
                $where[':start_time'] = $startTime;
            }
            if ($endTime) {
                $whereStr .= "AND m.reg_time <= :end_time ";
                $where[':end_time'] = $endTime;
            }
            if ($ip) {
                $whereStr .= "AND m.reg_ip=:ip ";
                $where[':ip'] = $ip;
            }
            // 定位起始位置
            $sql = "SELECT count(*) total FROM {$table} m " . $whereStr;
            $tmp = $pdoNew->find($sql, $where);
            $tmp = $tmp['total'];
            $total += $tmp;
            if ($checkOffset) {
                if ($tmp < $offset) {
                    $offset -= $tmp;
                    continue;
                } else {
                    $checkOffset = false;
                }
            }
            //获取数据
            if (!$checkDate) {
                $sql = "SELECT m.id, m.username, m.reg_ip, m.reg_time, m.login_ip, m.login_time, m.phone, m.uid, m.locked, mi.email, mi.email_status " .
                    "FROM {$table} m LEFT JOIN {$tableInfo} mi ON `m`.`id`=`mi`.`passid` " . $whereStr .
                    "ORDER BY m.id " .
                    "LIMIT {$offset}, {$size}";
                $tmp = $pdoNew->findAll($sql, $where);
                $tmp && $list = array_merge($list, $tmp);
                if (count($list) >= $limit) {
                    $checkDate = true;
                }
                $offset = 0;
                $size = $limit - count($list);
            }
            if ($endTime) {
                $currentMaxTime = $pdoNew->find("SELECT reg_time FROM {$table} ORDER BY id desc LIMIT 1");
                if ($currentMaxTime['reg_time'] > $endTime) {
                    break;
                }
            }
        }
        return ['total' => $total, 'list' => $list];
    }

    /**
     * 功  能：获取绑定列表
     *
     * @param int $startId $startId
     * @param int $size $size
     * @return array
     */
    public function getBindListById($startId, $size)
    {
        $max = $this->getMaxId();
        if (!$max) {
            return [];
        }
        $list = [];
        $openidTable = [];
        $limit = $size;
        $pdoNew = $this->getPdoNew();
        // 获取有绑定信息的用户
        $max = GetRangeTable::Get($max) + 1;
        for ($i = 1; $i <= $max; $i++) {
            $table = "members";
            $i > 1 && $table .= "_" . $i;
            $sql = "SELECT id, username, openid " .
                "FROM {$table} " .
                "WHERE id > :startId AND LENGTH(openid)>:openid " .
                "ORDER BY id LIMIT {$size}";
            $tmp = $pdoNew->findAll($sql, [
                ':startId' => $startId,
                ':openid' => 2,
            ]);
            if ($tmp) {
                foreach ($tmp as $item) {
                    $openidList = json_decode($item['openid'], true);
                    if (!$openidList) {
                        continue;
                    }
                    foreach ($openidList as $type => $openid) {
                        $table = GetHashTable::MembersBind($openid);
                        $openidTable[$table][] = $openid;
                        $key = $item['id'] . "_" . $openid;
                        $list[$key] = [
                            'passid' => $item['id'],
                            'username' => $item['username'],
                        ];
                    }
                }
            }
            if (count($list) >= $limit) {
                break;
            }
            $size = $limit - count($list);
        }
        // 获取绑定信息
        foreach ($openidTable as $table => $openIds) {
            $openIdParams = [];
            foreach ($openIds as $key => $openId) {
                $openIdParams[":id$key"] = $openId;
            }
            $sql = "SELECT passid, openid, type, nickname, bindtime " .
                "FROM {$table} " .
                "WHERE openid IN (" . implode(",", array_keys($openIdParams)) . ")";
            $tmp = $pdoNew->findAll($sql, $openIdParams);
            foreach ($tmp as $item) {
                $key = $item['passid'] . "_" . $item['openid'];
                if (isset($list[$key])) {
                    $list[$key] = array_merge($list[$key], [
                        'type' => $item['type'],
                        'openid' => $item['openid'],
                        'nickname' => $item['nickname'],
                        'bindtime' => strtotime($item['bindtime']),
                    ]);
                }
            }
        }
        return array_slice($list, 0, $limit);
    }

    /**
     * 功  能：获取用户列表
     *
     * @param int $startId $startId
     * @param int $size $size
     * @return array
     * desc:该函数兼容加解密
     */
    public function getUserListById($startId, $size)
    {
        $max = $this->getMaxId();
        if (!$max) {
            return [];
        }

        $list = [];
        $passidList = [];
        $limit = $size;
        $pdoNew = $this->getPdoNew();
        $max = GetRangeTable::Get($max) + 1;
        // 获取有绑定信息的用户
        for ($i = 1; $i <= $max; $i++) {
            $table = "members";
            $tableInfo = "members_info";
            if ($i > 1) {
                $table .= "_" . $i;
                $tableInfo .= "_" . $i;
            }
            $sql = "SELECT m.id,m.username,mi.gender,m.id as avatar,m.phone,mi.email,m.reg_time,m.reg_ip " .
                "FROM {$table} m LEFT JOIN {$tableInfo} mi ON `m`.`id`=`mi`.`passid` " .
                "WHERE id > :startId " .
                "ORDER BY id LIMIT {$size}";
            $tmp = $pdoNew->findAll($sql, [':startId' => $startId,]);
            if ($tmp) {
                foreach ($tmp as $item) {
                    $passidList[] = $item['id'];
                    $list[$item['id']] = [
                        'id' => $item['id'],
                        'username' => $item['username'],
                        'gender' => $item['gender'],
                        'avatar' => $item['avatar'],
                        'phone' => Sensitive::Decode($item['phone']),
                        'email' => Sensitive::Decode($item['email']),
                        'reg_time' => $item['reg_time'],
                        'reg_ip' => $item['reg_ip'],
                        'nickname' => '',
                    ];
                }
            }
            if (count($list) >= $limit) {
                break;
            }
            $size = $limit - count($list);
        }
        // 获取昵称
        $passIdParams = [];
        foreach ($passidList as $key => $passid) {
            $passIdParams[":id$key"] = $passid;
        }
        $sql = "SELECT passid, nickname " .
            "FROM members_nickname " .
            "WHERE passid IN (" . implode(",", array_keys($passIdParams)) . ")";
        $tmp = $pdoNew->findAll($sql, $passIdParams);
        foreach ($tmp as $item) {
            if (isset($list[$item['passid']])) {
                $list[$item['passid']]['nickname'] = $item['nickname'];
            }
        }
        return array_slice($list, 0, $limit);
    }

    /**
     * 功  能：获取最大的Passid
     *
     * @return bool
     */
    private function getMaxId()
    {
        $pdoNew = $this->getPdoNew();
        $sql = "SELECT id FROM members_passId_incr ORDER BY id desc LIMIT 1";
        $data = $pdoNew->find($sql);
        if (!$data) {
            return false;
        }
        return $data['id'];
    }

    /**
     * 释放用户信息库链接
     * @return void
     */
    public function delPdoNewInstance()
    {
        \Octopus\PdoEx::delInstance(DB_PASSPORT_USER);
        $this->pdoNew = null;
    }
}
