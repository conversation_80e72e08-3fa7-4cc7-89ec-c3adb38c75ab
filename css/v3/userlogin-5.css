
/* 头部样式 */
.logoPic {
    float: left;
}

.sTxt {
    color: #666;
    float: left;
    font-size: 18px;
    height: 64px;
    line-height: 64px;
    overflow: hidden;
    padding: 7px 0 0 10px;
}


/* 中间主要模块 */
.login_wrap {
    height: 502px;
    background: #e8f7fc;
    border-top: 1px solid #e7e7e7;
}

.login_center {
    width: 1000px;
    height: 100%;
    margin: 0 auto;
    zoom: 1;
}

.imgSlider {
    _display: inline;
    position: relative;
    width: 438px;
    overflow: hidden;
    float: left;
    margin-left: 91px;
    margin-top: 72px;
    zoom: 1;
}

.swi_img {
    width: 438px;
    height: 324px;
    position: relative;
    overflow: hidden;
    zoom: 1;
}

.imgSlider li {
    zoom: 1;
    margin: 0 auto;
    opacity: 0;
    filter: alpha(opacity:0);
    position: 'absolute';
    top: 0;
    left: 0
}

.imgSlider li.active {
    display: block;
    opacity: 1;
    filter: alpha(opacity:100);
}

.imgSlider li a {
    display: block;
    border-radius: 20px;
}

.imgSlider li a img {
    width: 438px;
    height: 324px;
}

.sliderListNum {
    overflow: hidden;
    position: relative;
    margin: 11px 0 0 180px;
    margin: 0 0 0 180px;
    position: relative;
    margin-top: -4px;
    z-index: 999;
}

.sliderListNum a {
    float: left;
    margin-left: 11px;
    width: 11px;
    height: 11px;
    _height: 10px;
    display: block;
    border-bottom: 0 none;
    text-decoration: none;
    text-decoration: none;
    background: url(../../images/v3/login-slider.png) no-repeat left top;
    background-position: 0 0;
}

.sliderListNum a.active {
    background-position: 0 -19px;
    text-decoration: none;
}




/* 登陆框位置css */
.login_box {
    width: 346px;
    border-radius: 4px;
    float: right;
    zoom: 1;
    margin-left: 0;
    margin-right: 0;
    overflow: hidden;
    margin-top: 20px;
    box-shadow: 3px 3px 7px rgba(0, 131, 255, .1), -3px 3px 7px rgba(0, 131, 255, .1)
}

.g-login {
    width: 346px;
    box-sizing: border-box;
    /* height: 440px; */
    border: none;
    margin-right: 6px;
    background-color: #fff;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 6px 24px 0 24px;
}

.posti-wrap {
    width: 298px;
    height: 54px;
}

.g-login .g-login-th {
    border-bottom: none;
    background: #fff;
    overflow: visible;
    line-height: none;
    font-size: 18px;
    line-height: 54px;
    position: relative;
}

.g-login-th::before {
    content: "";
    position: absolute;
    width: 100%;
    height: 1px;
    background-color: #e7e7e7;
    bottom: 0;
    left: 0;
}

.sMark {
    float: left;
    width: 50%;
    text-align: center;
    cursor: pointer;
    position: relative;
    color: #333333;
}

.sMark.active {
    font-weight: bold;
}

.sMark.active::after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 3px;
    background-color: #0083ff;
    bottom: 1px;
    left: 0;
}

/* 快速登陆 */
.quick-login {
    overflow: hidden;
    position: relative;
    *zoom: 1;
    width: 300px;
}

.q-outTxt {
    width: 267px;
    border: 1px solid #f2d361;
    border-radius: 2px;
    overflow: hidden;
    background: #fcf9d7;
    position: absolute;
    left: 0px;
    top: 12px;
    padding: 4px 2px 4px 27px;
    z-index: 99;
}

.q-outTxt p {
    line-height: 18px;
    color: #555;
}

.q-outTxt i {
    width: 14px;
    height: 14px;
    display: block;
    background: url(../../images/v3/userlogin-2/tips.png) no-repeat;
    position: absolute;
    left: 7px;
    top: 7px;
    overflow: hidden;
    text-indent: -9999px;
}

.quick-tip {
    width: 100%;
    height: 48px;
    text-align: center;
    line-height: 48px;
    color: #707070;
    padding-top: 12px;
    font-size: 14px;
}

.q-picwrap {
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.q-picwrap>a {
    display: block;
    width: 96px;
    height: 96px;
    padding: 12px 0;
    margin: 0 auto;
}

.q-picwrap img {
    display: block;
    border-radius: 4px;
}


.q-picwrap>p {
    height: 16px;
    line-height: 16px;
    text-align: center;
    font-size: 14px;
    color: #333333;
    margin-bottom: 32px;
}

.service-agreement {
    text-align: center;
    font-size: 0;
    padding-bottom: 28px;
    margin-top: 14px;
}

.text-center {
    text-align: center;
}

.sCheck {
    color: #7a7a7a;
    font-size: 12px;
}

.sCheck a {
    color: #2294ff;
    font-size: 12px;
}

.g-other-login {
    width: 150%;
    margin-left: -24px;
    padding-left: 24px;
    height: 50px;
    line-height: 50px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background-color: #f9f9f9;
}

.g-other-login .sTit {
    color: #666;
    display: block;
    float: left;
}

.g-other-login .otherStyle {
    float: left;
    overflow: hidden;
    padding-left: 12px;
}

.g-other-login .otherStyle a {
    float: left;
    margin-right: 16px;
    font-size: 12px;
}

.g-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    vertical-align: middle;
    background-image: url(../images/g-login_0213.png);
    background-repeat: no-repeat;
    background-size: 100px 169px;
    margin-right: 4px;
}

.g-icon-qq {
    width: 18px;
    background-position: -30px -54px;
}

.g-icon-phone {
    background-position: -52px -77px;
}

.g-icon-weixin {
    background-position: -54px -54px;
}

.g-icon-username {
    background-position: -78px -54px;
}



/* 表单中样式  */
.form-item {
    width: 298px;
    height: 48px;
    line-height: 48px;
    padding-top: 12px;
    font-size: 14px;
    border-bottom: 1px solid #e7e7e7;
    position: relative;
}

.form-item input {
    display: block;
    height: 47px;
    width: 100%;
    border: none;
    outline: none;
    font-size: 14px;
    line-height: 47px;
    position: absolute;
    box-sizing: border-box;
    background-color: transparent;
    width: 298px;
}



.form-item input::-webkit-input-placeholder {
    color: #707070;
}

.form-item input::-ms-input-placeholder {
    color: #707070 !important;
}

.form-item input::-moz-placeholder {
    color: #707070;
}

.form-item input:-ms-input-placeholder {
    color: #707070;
}

.form-item input::placeholder {
    color: #707070;
}


.form-item .g-input {
    float: left;
    width: 200px;
    height: 48px;
}


.form-item .error-tip {
    float: left;
    width: 98px;
    height: 48px;
    font-size: 14px;
    line-height: 48px;
    color: #ff0000;
    text-align: right;
    overflow: hidden;
    position: relative;
    z-index: 2;
    background-color: transparent;
}

.g-code-input {
    float: left;
    width: 90px;
    height: 47px;
}
.g-code-input input{
    line-height: 47px;
}

.g-code-image {
    background-color: #fff;
    float: right;
    width: 110px;
    text-align: right;
    position: relative;
    z-index: 2;
}

.g-code-image>a {
    color: #0083ff;
    font-size: 14px;
}

.g-code-image img {
    width: 60px;
    height: 30px;
}

.g-register {
    width: 298px;
    padding-top: 16px;
    padding-bottom: 28px;
    height: 14px;
    line-height: 14px;
}

.g-register a {
    float: left;
    color: #0083ff;
    font-size: 14px;
}

.g-register a.last {
    float: right;
}


.g-button .error-tip {
    display: none;
    font-size: 14px;
    height: 14px;
    line-height: 14px;
    text-align: center;
    color: #ff0000;
    margin-top: 10px;
}

.g-login-button {
    width: 100%;
    height: 36px;
    outline: none;
    border: none;
    border-radius: 4px;
    background-color: #0083ff;
    font-size: 14px;
    color: #fff;
    cursor: pointer;
}

.timer-tip {
    font-size: 14px;
    color: #999999;
}

.send-code {
    font-size: 14px;
    color: #0083ff;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* footer样式 */
.footer {
    border-top: 1px solid #eee;
    height: 55px;
    overflow: hidden;
    background-color: #fff;
    min-width: 1000px;
    line-height: 56px;
    text-align: center;
    color: #999;
    font-size: 14px;
    font-family: Arial, 微软雅黑, 宋体;
}

.footerCon {
    width: 1000px;
    margin: 0 auto;
}

.footer em {
    color: #dcd4dc;
    padding: 0 10px;
}

.footer a {
    color: #999;
}

.footer a:hover {
    color: #f30;
}

a:hover {
    color: inherit;
}
.mb28{
    margin-bottom: 28px;
}