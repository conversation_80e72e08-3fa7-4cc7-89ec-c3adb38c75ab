<?php

class AutoLoginController extends Controller
{

    public function actionIndex()
    {
        header("Cache-Control: no-cache, must-revalidate"); // HTTP/1.1
        header("Expires: Sat, 26 Jul 1997 05:00:00 GMT"); // Date in the past
        $passid = intval($_POST["passid"]);
        if ($passid && $_POST["access"])
        {
            $enforceUseCache = false;
            $isUseRedis = Config::get('isUseRedis');
            if ($isUseRedis && defined('VIPFLN'))
            {
                $clientIp = get_client_ip();
                $redis = RedisEx::getInstance();
                $keyname = "visitTimeLog:" . str_replace(".", "_", $clientIp);
                $visitTimeRange = $redis->lRange($keyname, 0, VIPFLN - 1);
                $visitLen = sizeof($visitTimeRange);
                if ($visitLen > (VIPFLN - 1))
                {
                    $timeRange = $visitTimeRange[0] - $visitTimeRange[VIPFLN - 1];
                    if ($timeRange == 0)
                    {
                        $enforceUseCache = true;
                    }
                }
            }
            $memberModel = loadModel('member');
            $token = $memberModel->getToken($passid, $enforceUseCache);
            if ($token && md5($token . TOKENKEY) == $_POST["access"])
            {
                $uid = $memberModel->getUid($passid);
                echo com_json_encode(array("status" => true, "code" => 200, "data" => "", "u_sec" => get_sec_browser($uid), "msg" => "", "version" => ""));
                return;
            }
            else if ($enforceUseCache)
            {
                echo com_json_encode(array("status" => false, "code" => 404, "data" => "", "u_sec" => "", "msg" => "帐号验证已失效，请重新登录！", "version" => ""));
                return;
            }
        }
        echo com_json_encode(array("status" => false, "code" => 403, "data" => "", "u_sec" => "", "msg" => "自动登录帐号、密码验证失败，请重新输入！", "version" => ""));
    }

}

//自定义json_encode
function com_json_encode($arr)
{
    if (!is_assoc($arr))
    {
        foreach ($arr as $key => $val)
        {
            if (is_array($val))
            {
                $val = com_json_encode($val);
                $arr[$key] = $val;
            }
            else
            {
                $arr[$key] = var_export($val, true);
            }
        }
        return "[" . implode(",", $arr) . "]";
    }
    else
    {
        foreach ($arr as $key => $val)
        {
            if (is_array($val))
            {
                $val = com_json_encode($val);
                $arr[$key] = var_export($key, true) . ":" . $val;
            }
            else
            {
                $arr[$key] = var_export($key, true) . ":" . var_export($val, true);
            }
        }
        return "{" . implode(",", $arr) . "}";
    }
}

//判断数组类型是自然数组还是键值数组
function is_assoc($arr)
{
    return array_keys($arr) !== range(0, count($arr) - 1);
}