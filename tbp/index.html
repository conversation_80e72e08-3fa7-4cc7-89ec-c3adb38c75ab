<!DOCTYPE html>
<div>
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=Edge">
		<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,viewport-fit=cover,user-scalable=0;">
		<title>认证</title>
		
				<script>
					(function(doc, win) {
									var docEl = doc.documentElement,
											resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
											recalc = function() {
													var clientWidth = docEl.clientWidth;
													if (!clientWidth) return;						
													docEl.style.fontSize =clientWidth>1080?'80px': 100 * (clientWidth / 750) + 'px';
											};
									if (!doc.addEventListener) return;
									win.addEventListener(resizeEvt, recalc, false);
									doc.addEventListener('DOMContentLoaded', recalc, false);
							})(document, window);
				</script>
		
    </head>
	<style>
		html, body, p, dl, dt, dd, table, td, th, input, img, form, div, span, ul, ol, li, h1, h2, h3, h4, h5, h6, select, fieldset, fieldset, input, button, sub, sup, textarea {
			margin: 0;
			padding: 0;
		}
		body{
			width:100%;
			color: #333;
			font-size: 0.28rem;
		}
		input, textarea, select {
			background-color: #ffffff;
		}
		@media only screen and (min-width: 1080px) {     
			.content-box{
				width:50%;
				margin:0 auto;
			} 
			.btn-area{
				position: relative;
				margin-top: 2rem;
			}
		} 
		@media only screen and (max-width: 1060px) {  
			.content-box{
				height: 100%;
			}
		.btn-area{
			position: absolute !important;
			width: 80%;
			left: 10%;
			bottom: 1%;
		}
	}
		.header-cont{
			height:40%;
			background:#1872ff;
			border-bottom-right-radius:0.5rem;
			border-bottom-left-radius:0.5rem;
			padding:0.4rem;
		}
		.header-cont p{
			color: #fff;
			font-weight: bold;
			font-size: 0.36rem;
			line-height: 2;
		}
		.transit-main{
			/* height: 618px; */
			width: 80%;
			margin: 0 auto;
			padding: 0.8rem 0;
		}
		a {
			text-decoration: none;
		}
		.btn-blueA{
			background: #1890ff;
			padding: 0.16rem 0.2rem;
			color: #fff;
			border-radius: 0.5rem;
			cursor: pointer;
			text-align: center;
			border: 0.01rem solid #1890ff;
			font-size: 0.24rem;
		}
		.plain-btn{
			background: #fff;
			color: #1890ff;
			cursor: inherit;
		}
		.step2Txt{
			font-size: 0.24rem;
    	color: #aaa;
			margin: 038rem 0 0.20rem 0;
		}
		.step3Txt{
			text-align: center;
			font-size: 0.18rem;
			color: #666;
			line-height: 0.46rem;
		}
		.mt90{
			margin-top: 0.9rem;
		}
		.txt-item{
			line-height: 0.42rem;
		}
		#step2PassArea{
			margin-left: 56px;
		}
		.form-item{
			padding: 0.3rem 0;
			position: relative;
		}
		.form-field{
			color: #666;
		}
		
		.ipt_txt{
			color: #999999;
			padding: 0rem 0.12rem;
			height: 0.6rem;
			border: 0.01rem solid #c6ced6;
			vertical-align: middle;
			font-size: 0.2rem;
			outline:none;
		}
		.ipt_txt_error{
			border-color: red;
		}
		.form-tips-error{
			position:absolute;
			display: none;
			color: red;
			font-size: 0.2rem;
			padding-left: 1.2rem;
			padding-top:0.06rem;
		}
	</style>
<body>

		<div class="content-box">
			<div class="transit-head">
				<div class="header-cont">
					<p>亲爱的2345用户</p>
					<p>欢迎您使用信息加强认证服务~</p>
				</div>
			</div>
			<div class="captcha-box"></div>
			<div class="transit-main">
					<div class="main main_position">
						
						
						<div class="m-boxA step_2_class" style="display: none">
						
							<div id="step2CodeArea">
								<div class="form-item">
									<span class="form-field accountTxt">2345账号：</span>
								</div>
								<div class="form-item">
									<span class="form-field phoneTxt">手机号：</span>
									<button class="btn-blueA confirmCode" style="margin-left: 0.8rem;border-radius: 0.1rem;">发送验证码</button>
								</div>
								<div class="form-item" style="position: relative;padding-bottom:0">
									<span class="form-field">验证码：</span>
									<input type="text" class="ipt_txt" style="width:1.84rem" value="" placeholder="请输入验证码">
								</div>
								<div class="form-tips-error"></div>
								<div class="form-item btn-area">
									<button class="btn-blueA phoneBindClass" style="width: 100%;">认证</button>
									<div style="color: #999999;text-align: center;font-size: 0.12rem;
									margin-top: 0.3rem;">客服联系电话：************</div>
								</div>
								
							</div>
						</div>
						<div class="step_erro_class form-item btn-area">
							<div style="color: #999999;text-align: center;font-size: 0.12rem;
							margin-top: 0.3rem;">客服联系电话：************</div>
						</div>
						
				
						
						<div class="m-boxA step_3_class" style="display: none">
								<p class="step3Txt mt90">认证成功</p>
						</div>
						
				
				 </div>
				
		
			</div>
		</div>	

	
<script src="auth.js"></script></body>

</div>