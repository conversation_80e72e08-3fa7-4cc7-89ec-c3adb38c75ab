<?php
/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 摘    要:GetNicknameByPassidController.php
 * 作    者:<EMAIL>
 * 修改日期: 2015/10/13
 */
class GetNicknameByPassidController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }

        $passid = intval($_POST['passid']);
        $memberModel = loadModel('member');
        $info = $memberModel->getNicknameByPassid($passid);
        echo serialize($info);
    }

}
