<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/7/23
 * Time: 15:00
 */

class dev
{
    private static $instances = null;
    private static $devRootDomain = ".2345.cn";
    protected static $devTag = "";
    protected static $runMode = "";

    /**
     * 初始化
     * @param string $runMode 运行环境
     *                        dev constructor.
     * @return void
     */
    private function __construct($runMode)
    {
        self::$runMode = $runMode;
        if (self::isDevelopment()) {
            if (!empty($_ENV['RUNMODEST'])) {
                self::$devTag = $_ENV['RUNMODEST'];
            } else {
                self::$devTag = self::parasDomain();
            }
        }
    }

    /**
     * 解析域名
     * @param string $domain
     * @return string
     */
    public static function parasDomain($domain = "")
    {
        $requestDomain = self::getRequestDomain();
        if (!empty($domain)) {
            $requestDomain = $domain;
        }
        if (strpos($requestDomain, self::$devRootDomain) !== false) {
            $domainArr = explode(".", str_replace(self::$devRootDomain, "", $requestDomain));
            return $domainArr[count($domainArr) - 1];
        }
        if (strpos($domain, 'qnchrome.com') !== false) {
            return $domain;
        }
        if (strpos($domain, 'passport.2345.com') !== false) {
            return $domain;
        }
        if (strpos($domain, 'passport.2345.cc') !== false) {
            return $domain;
        }
        if (strpos($domain, 'login.2345.com') !== false) {
            return $domain;
        }
        if (strpos($domain, 'my.2345.com') !== false) {
            return $domain;
        }
        return "";
    }

    /**
     * 是否是开发环境
     * @return bool
     */
    public static function isDevelopment()
    {
        if (self::$runMode == "development") {
            return true;
        }

        return false;
    }

    /**
     * 实例化
     * @param string $runMode 运行环境
     * @return dev|null
     */
    public static function getInstance($runMode = "")
    {
        if (empty(self::$instances)) {
            self::$instances = new static($runMode);
        }

        return self::$instances;
    }

    /**
     * 获取请求的域名
     * @return string
     */
    public static function getRequestDomain()
    {
        return $_SERVER['HTTP_HOST'];
    }

    /**
     * 获取测试环境标记
     * @return string
     */
    public static function getDevTag()
    {
        return self::$devTag;
    }

    /**
     * 标准st环境获取域名
     * @param string $domain 域名
     * @param string $devTag 运行环境
     * @return string
     */
    public static function getDevDomain($domain, $devTag = "")
    {
        // if (empty($devTag)) {
        //     $devTag = self::$devTag;
        // }
        // if (self::isDevelopment() && $_REQUEST['mid'] != 'pcbs') {
        //     if (empty($devTag)) {
        //         return $domain;
        //     }
        //     if (!preg_match("/^http|https/", $domain)) {
        //         $domain = "http://" . $domain;
        //     }
        //     $parseDomain = parse_url($domain, PHP_URL_HOST);
        //     $response = file_get_contents("http://convert-st-domain.st1.2345.cn/api/change?domain={$parseDomain}&devTag={$devTag}");
        //     $responseArr = json_decode($response, true);
        //     if (!empty($responseArr["data"])) {
        //         return str_replace($parseDomain, $responseArr["data"], $domain);
        //     }

        //     return $domain;
        // }

        return $domain;
    }

    private function __clone()
    {
        // TODO: Implement __clone() method.
    }
}