<?php

/**
 * 获取短信回执
 */

namespace Api;

use Controller;

class CallbackStatusController extends Controller
{
    /**
     * 批量查询回执状态
     * @return bool
     */
    public function actionGetStatus()
    {
        $logIds = $this->Filter->post('logIds'); //竖线分割

        $getRedisAction = \GetRedisAction::getInstance();
        $isLegal = $getRedisAction->isIpWhiteList(get_client_ip());
        if ($isLegal !== true)
        {
            return \MsgAction::failedResponse(-11, \SmsAction::$errStr[-11]);
        }

        $logIds = explode("|", $logIds);
        if (count($logIds) > 200)
        {
            return \MsgAction::failedResponse(-1, "logId数量最多200个");
        }

        $smsLogModel = \SmsInterface\SmsLogModel::getInstance();
        $logStatus = $smsLogModel->getChannelCallbackStatusByLogIds($logIds);
        $result = array();
        if ($logStatus)
        {
            foreach ($logStatus as $key => $val)
            {
                $phone = substr($val['phone'], -11, 11);
                if (isset($val['message']))
                {
                    preg_match("/'Status' => '([^']*)'/", $val['message'], $matches);
                    $callbackStatus = $matches[1];
                }
                elseif ($val['callback_status'] === '1')
                {
                    $callbackStatus = 'DELIVRD';
                }
                elseif ($val['callback_status'] === '0')
                {
                    $callbackStatus = 'FAILED';
                }
                elseif ($val['callback_status'] === null)
                {
                    $callbackStatus = 'UNKNOWN';
                }
                else
                {
                    $callbackStatus = 'NULL';
                }
                $tmp = array(
                    "logId"          => $val['log_id'],
                    "addTime"        => $val['callback_time'],
                    "status"         => $val['callback_status'],
                    "phone"          => $phone,
                    "callbackStatus" => $callbackStatus,
                );
                $result[$val['log_id']] = $tmp;
            }
        }
        if ($result)
        {
            return \MsgAction::successResponse("成功", $result);
        }

        return \MsgAction::failedResponse(-2, "无查询结果, 请检查参数!");
    }
}
