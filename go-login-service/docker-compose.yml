version: '3.8'

services:
  login-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - HTTP_HOST=login.2345.com
      - RUNMODE=production
    volumes:
      - ./.env:/root/.env:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: nginx reverse proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - login-service
    restart: unless-stopped
