<?php

use Octopus\PdoEx;
use Service\Capacity\GetHashTable;
use Service\Capacity\GetRangeTable;

class OauthModel extends Model
{

    private $pdo;
    private $pdoNew;

    /**
     * OauthModel constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_PASSPORT, $dbConfig[DB_PASSPORT]);
    }

    /**
     *
     * Get passport_user pdo
     * @return PdoEx
     */
    protected function getPdoNew()
    {
        if (!is_object($this->pdoNew)) {
            $dbConfig = Config::get("database");
            $this->pdoNew = PdoEx::getInstance(DB_PASSPORT_USER, $dbConfig[DB_PASSPORT_USER], true);
        }
        return $this->pdoNew;
    }

    /**
     * 绑定第三方帐号
     * @param string $type 第三方帐号类型
     * @param int $passid 用户passid
     * @param string $openid 用户第三方帐号openid
     * @param string $nickname 用户第三方帐号昵称
     * @param string $username 用户名
     * @return boolean 绑定成功与否
     */
    public function setBind($type, $passid, $openid, $nickname, $username)
    {
        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        $table = GetHashTable::MembersBind($openid);
        $insert = array(
            'passid' => $passid,
            'openid' => $openid,
            'nickname' => $nickname,
            'type' => $type,
            'username' => $username,
            'bindtime' => date('Y-m-d H:i:s', time()),
        );
        $result = $pdoNew->insert($table, $insert, false);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $table = GetRangeTable::Members($passid);
        $row = $pdoNew->find("SELECT openid FROM {$table} WHERE id=:id", [':id' => $passid]);
        if (!$row) {
            $pdoNew->rollBack();
            return false;
        }
        $openidList = @json_decode($row['openid'], true);
        if (!$openidList) {
            $openidList = [];
        }
        $openidList[$type] = $openid;
        $result = $pdoNew->update($table, ['openid' => json_encode($openidList)], [
            'where' => 'id = :id',
            'params' => [':id' => $passid]
        ]);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $pdoNew->commit();
        //删除缓存
        RedisEx::getInstance()->del("redis:cache:service:members:info:passid:$passid");
        return true;
    }

    /**
     * 根据第三方信息获取用户绑定信息
     * @param string $openid 第三方帐号openid
     * @param string $type 第三方帐号类型
     * @return array 用户绑定信息
     */
    public function getBind($openid, $type)
    {
        $table = GetHashTable::MembersBind($openid);
        $return = $this->getPdoNew()->find(
            "SELECT * FROM $table WHERE openid = :openid and type = :type",
            [":openid" => $openid, ':type' => $type],
            true
        );
        if ($return) {
            $return['bindtime'] = (string)strtotime($return['bindtime']);
            return $return;
        }

        return false;
    }

    /**
     * 根据passid获取用户第三方帐号所有绑定信息
     * @param int $passid 用户passid
     * @return array 返回用户第三方绑定信息数组
     */
    public function getBindByPassid($passid)
    {
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $sql = "SELECT openid FROM {$table} WHERE id = :passid";
        $row = $pdoNew->find($sql, array(":passid" => $passid));
        if (!$row) {
            return false;
        }
        $openidList = json_decode($row['openid'], true);
        if (!$openidList) {
            return false;
        }

        return loadModel('member')->getNewMemberBindByPassIdList($openidList);
    }

    /**
     * 解除第三方帐号绑定
     * @param string $type 第三方帐号类型
     * @param int $passid 用户passid
     * @param string $code 解绑代码
     * @return boolean 解除成功与否
     */
    public function delBind($type, $passid, $code)
    {
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $sql = "SELECT openid FROM {$table} WHERE id = :passid";
        $row = $pdoNew->find($sql, array(":passid" => $passid));
        if (!$row) {
            return false;
        }
        $openidList = json_decode($row['openid'], true);
        if (!$openidList || !isset($openidList[$type])) {
            return false;
        }
        $pdoNew->beginTransaction();
        $openidListTmp = $openidList;
        unset($openidListTmp[$type]);
        $result = $pdoNew->update(
            $table,
            ['openid' => json_encode($openidListTmp)],
            [
                'where' => 'id = :id',
                'params' => [':id' => $passid]
            ]
        );
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $table = GetHashTable::MembersBind($openidList[$type]);
        $condition = array(
            'where' => 'passid = :passid and type = :type and unbindcode = :code',
            'params' => array(
                ':passid' => $passid,
                ':type' => $type,
                ':code' => $code,
            )
        );
        $result = $pdoNew->delete($table, $condition);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $pdoNew->commit();
        //删除缓存
        RedisEx::getInstance()->del("redis:cache:service:members:info:passid:$passid");
        xLog('changeUInfo', 'changeUInfo', $passid . ' unBindQQ');
        return true;
    }

    /**
     * 设置第三方帐号解绑代码
     * @param string $type 第三方帐号类型
     * @param int $passid 用户passid
     * @param string $code 解绑代码
     * @return boolean 设置成功与否
     */
    public function setUnbindCode($type, $passid, $code)
    {
        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $sql = "SELECT openid FROM {$table} WHERE id = :passid";
        $row = $pdoNew->find($sql, array(":passid" => $passid));
        if (!$row) {
            return false;
        }
        $openidList = json_decode($row['openid'], true);
        if (!$openidList || !isset($openidList[$type])) {
            return false;
        }
        $table = GetHashTable::MembersBind($openidList[$type]);
        $condition = array(
            'where' => 'passid = :passid and type = :type',
            'params' => array(
                ':passid' => $passid,
                ':type' => $type,
            )
        );
        return $pdoNew->update($table, array("unbindcode" => $code), $condition);
    }

}
