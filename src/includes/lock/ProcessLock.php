<?php
/**
 * Copyright (c) 2013,上海瑞创网络科技股份有限公司
 * 文件名称：ProcessLock.php
 * 摘    要：进程锁功能
 * 作    者：胡小明
 * 修改日期：2013.12.03
 */
class ProcessLock
{
	public $LOCK;
	public function __construct()
	{
	}
	/*进程锁*/
	public function getLock($lfile)
	{
		$lock =  APPPATH . '/logs';
		if(!is_dir($lock))
		{
			mkdir($lock);
		}
		if(!is_dir("$lock/flock"))
		{
			mkdir("$lock/flock");
		}
		$fileName = "$lock/flock/$lfile";
		$this->LOCK = fopen ($fileName, "w+");
		return flock($this->LOCK, LOCK_EX|LOCK_NB);
	}
	/*解锁*/
	public function unLock()
	{
		fclose($this->LOCK);
		$this->LOCK = null;
	}
	
	public function __destruct() 
	{
		if($this->LOCK)
		{
			$this->unLock();
		}
   	}
}
?>