<?php

namespace Common\Confidential;

/**
 * 敏感数据脱密公共函数
 *
 */
class Sensitive
{
    const PADSTR = '*';

    /**
     * 配置到.env 可以不传参数
     */
    protected static function defaultConfig($options)
    {
        return [
            "aes_key" => $options['aes_key'] ?? ($_ENV['AES_KEY'] ?? Sensitive::$aesKey),
            "aes_iv" => $options['aes_iv'] ?? ($_ENV['AES_IV'] ?? '0123456789012345'),
            "prefix" => $options['prefix'] ?? ($_ENV['AES_PREFIX'] ?? '{AES-1}'),
        ];
    }

    /**
     * 获取加密数据
     * param $options
     * [
     *   "aes_key" => 'adas',
     *   "aes_iv" => 'aaa',
     *   "prefix" => '{AES_1}',
     * ]
     */
    public static function Decode($value, $options = [])
    {
        if (self::IsEmptyString($value)) {
            return $value;
        }
        $config = self::defaultConfig($options);
        $client = new Crypt($config['aes_key'], $config['aes_iv']);
        $prefix = $config['prefix'];
        if (self::startsWith($value, $prefix)) {
            $value = $client->decrypt(self::after($value, $prefix));
        }
        return $value;
    }

    /**
     * 进行数据加密
     * param $options
     * [
     *   "aes_key" => 'adas',
     *   "aes_iv" => 'aaa',
     *   "prefix" => '{AES_1}',
     * ]
     */
    public static function Encode($value, $options = [])
    {
        if (self::IsEmptyString($value)) {
            return $value;
        }
        $config = self::defaultConfig($options);
        $client = new Crypt($config['aes_key'], $config['aes_iv']);
        $prefix = $config['prefix'];
        $value = $client->encrypt($value);
        return $prefix . $value;
    }

    public static function IDCard($idNo)
    {
        if (self::IsEmptyString($idNo)) {
            return $idNo;
        }
        return static::PadUtf8($idNo, 4, strlen($idNo));
    }

    public static function Phone($phone)
    {
        if (self::IsEmptyString($phone)) {
            return $phone;
        }
        return static::PadUtf8($phone, 3, 7);
    }

    // 前缀保留前2后2位
    public static function Email($email)
    {
        if (self::IsEmptyString($email)) {
            return $email;
        }
        // 获取@符号位置
        $index = strpos($email, '@');
        if ($index === false) {
            return $email;
        }
        if ($index == 4) {
            return static::PadUtf8($email, 2, $index);
        }
        return static::PadUtf8($email, 2, $index - 2);
    }

    // 银行卡号、支付宝账号保留开头4位和末尾4位
    public static function Account($account)
    {
        if (self::IsEmptyString($account)) {
            return $account;
        }
        return static::PadUtf8($account, 4, strlen($account) - 4);
    }

    // 地址脱敏，包含中文字符需要特殊处理
    public static function Address($address)
    {
        if (self::IsEmptyString($address)) {
            return $address;
        }
        return static::PadRight($address, 7, 10);
    }

    protected static function PadRight($str, $start, $num)
    {
        return mb_substr($str, 0, $start) . str_repeat(static::PADSTR, $num);
    }

    private static function IsEmptyString($value): bool
    {
        if (empty($value)) {
            return true;
        }
        return false;
    }

    protected static function startsWith($haystack, $needles)
    {
        if (!is_iterable($needles)) {
            $needles = [$needles];
        }
        foreach ($needles as $needle) {
            if ((string)$needle !== '' && substr($haystack, 0, strlen($needle)) === (string)$needle) {
                return true;
            }
        }
        return false;
    }

    protected static function after($subject, $search)
    {
        return $search === '' ? $subject : array_reverse(explode($search, $subject, 2))[0];
    }

    protected static function PadUtf8($str, $start, $end)
    {
        if ($start > $end || $start < 0 || $end > strlen($str)) {
            return $str;
        }
        return mb_substr($str, 0, $start) . str_repeat(static::PADSTR, $end - $start) . mb_substr($str, $end);
    }
}
