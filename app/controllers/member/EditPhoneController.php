<?php

class EditPhoneController extends Controller
{

    private $pageArray, $phone, $forward;

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $memberModel = loadModel("member");
        $userinfo = $memberModel->read(PASSID);
        $this->pageArray['tabIndex'] = 5;
        $this->pageArray['page'] = "phone";
        $this->pageArray['nav'] = "editphone";
        $this->pageArray['open'] = ($_GET['page'] == "open") ? true : false;
        $this->pageArray['phonetype'] = ($userinfo['phone']) ? "edit" : "bind";
        $this->pageArray['emailtype'] = ($userinfo['email'] && $userinfo['email_status']) ? "edit" : "bind";
        if (!$userinfo["phone"] && !$userinfo["phone_redundancy"])
        {
            redirect('/member/bindPhone');
        }
        if ($userinfo["phone"])
        {
            $this->phone = $userinfo["phone"];
            $this->mode = 'simple';
        }
        else
        {
            $this->phone = $userinfo["phone_redundancy"];
            $this->mode = 'complex';
        }
        $this->pageArray['phone'] = substr($this->phone, 0, 3) . '****' . substr($this->phone, -4);
        $this->forward = getForwardUrl();
        $this->pageArray['forward'] = $this->forward;
        $this->pageArray['title'] = "修改手机-2345用户中心";
    }

    /**
     * 默认方法
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $mid = $_GET["mid"] ?? "";

        $midStr = ($mid == "" ? '' : "&mid=" . $mid);
        $refer = getReferDomain();
        if ($refer && $refer != 'login.2345.com')
        {
            setcookie("refer_domain", $refer, 0, "/");
        }
        $url = "/member/editPhone/step2?from=phone" . $midStr;
        if ($this->pageArray['open'])
        {
            $url .= "&page=open";
        }
        $url .= ($this->forward) ? "&forward=" . urlencode($this->forward) : "";
        redirect($url);
    }

    /**
     * wap修改手机验证
     * <AUTHOR>
     */
    public function actionStep2()
    {
        $from = 'phone';
        $this->pageArray['from'] = $from;
        $this->pageArray['step'] = 2;
        $this->pageArray['mode'] = $this->mode;
        $this->pageArray['open'] = ($_GET['page'] == "open") ? true : false;
        if (IsMobile())
        {
            loadView('member/m/edit_phone_wap.tpl.html', $this->pageArray);
        }
        else
        {
            loadView('member/edit_phone.tpl.html', $this->pageArray);
        }
    }

    /**
     * wap检测手机验证
     * <AUTHOR>
     */
    public function actionStep3()
    {
        $from = 'phone';
        $phone = $this->phone;
        $code = $_POST['code'];
        $checkcode = md5($phone . trim($code));
        $phoneAction = loadAction("phone");
        $status = $phoneAction->checkVerifyCode("login", PASSID, $checkcode);
        if (IsMobile())
        {
            if ($status)
            {
                $this->pageArray['from'] = $from;
                $this->pageArray['code'] = $code;
                $this->pageArray['step'] = 3;
                $this->pageArray['tabIndex'] = 2;
                loadView('member/m/edit_phone_wap.tpl.html', $this->pageArray);
            }
            else
            {
                showMessage("验证码错误！", 'back');
            }
            return;
        }
        $flag = $status ? 200 : 400;
        echo $flag;
    }

    /**
     * wap手机修改
     * <AUTHOR>
     */
    public function actionStep4()
    {
        $memberModel = loadModel("member");
        $userInfo = $memberModel->read(PASSID);
        $userInfo['passid'] = PASSID;
        $objLogV2Action = loadAction('logV2');

        $editcode = $_POST['code2'];
        $encryptAction = loadAction('Encrypt');
        $_POST['phone'] = $encryptAction::aes128cbcHexDecrypt($_POST['phone']);
        $phone = $_POST['phone'];
        $checkcode = md5($phone . $editcode);
        $phoneAction = loadAction("phone");
        $status = $phoneAction->checkEditCode("login", PASSID, $checkcode);
        if ($status)
        {
            if ($this->mode == 'simple')
            {
                $row = $phoneAction->get(PASSID);
                $phoneOld = $row['phone'];
                $result = $phoneAction->edit(PASSID, $phone);
                if ($result == '200.0')
                {
                    //日志行为打点:用户信息添加或修改; type:USER_INFO_ADD; sub_type:PHONE;
                    $objLogV2Action->report('ALL', ($userInfo['phone'] ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD'), 'PHONE', $userInfo, $userInfo['phone'], $phone);

                    $this->log($phoneOld, $phone);
                }
            }
            else
            {
                $result = $phoneAction->bindSimple(PASSID, $phone);
                if ($result == '200.0')
                {
                    //日志行为打点:用户信息添加或修改; type:USER_INFO_ADD; sub_type:PHONE;
                    $objLogV2Action->report('ALL', ($userInfo['phone'] ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD'), 'PHONE', $userInfo, $userInfo['phone'], $phone);

                    $this->log('', $phone);
                }
            }
            if (IsMobile())
            {
                $this->pageArray['step'] = 4;
                $this->pageArray['tabIndex'] = 2;
                loadView('member/m/edit_phone_wap.tpl.html', $this->pageArray);
                return;
            }
            echo substr($result, 0, 3);
        }
        else
        {
            if (IsMobile())
            {
                showMessage("验证码错误！", 'back');
            }
            else
            {
                echo "400";
            }
        }
    }

    /**
     * 发送验证码
     * <AUTHOR>
     */
    public function actionSendCode()
    {
        $phoneAction = loadAction("phone");
        if (isset($_POST['phone']))
        {
            $encryptAction = loadAction('Encrypt');
            $_POST['phone'] = $encryptAction::aes128cbcHexDecrypt($_POST['phone']);
            $phone = $_POST['phone'];
            $verifycode = $_POST['verifycode'];
            $status = $phoneAction->sendEditCode("login", PASSID, $phone, $verifycode, 5, 159);
        }
        else
        {
            $status = $phoneAction->sendVerifyCode("login", PASSID, 5, 154);
        }
        echo $status;
    }

    /**
     * 检测验证码
     * <AUTHOR>
     */
    public function actionCheckCode()
    {
        $code = $_POST['code'];
        $phoneAction = loadAction("phone");
        if (isset($_POST['phone']))
        {
            $encryptAction = loadAction('Encrypt');
            $_POST['phone'] = $encryptAction::aes128cbcHexDecrypt($_POST['phone']);
            $phone = $_POST['phone'];
            $checkcode = md5($phone . $code);
            $regex = Config::get('regex');
            if (preg_match($regex['phone'], $phone))
            {
                $status = $phoneAction->checkEditCode("login", PASSID, $checkcode);
            }
            else
            {
                echo "300";
                return;
            }
        }
        else
        {
            $phone = $this->phone;
            $checkcode = md5($phone . $code);
            $status = $phoneAction->checkVerifyCode("login", PASSID, $checkcode);
        }
        $flag = $status ? "200" : "400";
        echo $flag;
    }

    /**
     *
     * @param $phoneOld
     * @param $phone
     * <AUTHOR>
     */
    private function log($phoneOld, $phone)
    {
        xLog('changeUInfo', 'changeUInfo', PASSID . ' resetPhone');
        $clientIp = get_client_ip();
        $refer = $_COOKIE['refer_domain'] ? $_COOKIE['refer_domain'] : '';
        $from = 'login.2345.com';
        $phoneModel = loadModel('phone');
        $phoneModel->log(PASSID, array('phone_old' => $phoneOld, 'phone' => $phone, 'op_time' => time(), 'op_ip' => $clientIp, 'op_domain' => "login.2345.com", 'op_refer' => $refer, 'op_from' => $from));
    }

}
