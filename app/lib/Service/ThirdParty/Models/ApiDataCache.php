<?php
/**
 * Copyright (c)  上海二三四五网络科技有限公司
 * 文件名称：ApiDataCache.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：05-22, 2017
 */


namespace Service\ThirdParty\Models;

class ApiDataCache
{

    private $redis;

    const EXPIRE_TIME = 600;

    /**
     * ApiDataCache constructor.
     */
    public function __construct()
    {
        $this->redis = \RedisEx::getInstance();
    }

    /**
     * @return string
     */
    private function getUserCenterGameKey()
    {
        $key = "API_DATA:USER_CENTER_Game";

        return $key;
    }

    /**
     * @param array $data data
     *
     * @return mixed
     */
    public function setUserCenterGames($data)
    {
        $data = json_encode($data);
        $key = $this->getUserCenterGameKey();
        $res = $this->redis->set($key, $data);
        $this->redis->expire($key, self::EXPIRE_TIME);

        return $res;
    }

    /**
     * @return mixed
     */
    public function getUserCenterGames()
    {
        $key = $this->getUserCenterGameKey();
        $res = $this->redis->get($key);
        if ($res)
        {
            return json_decode($res, true);
        }

        return $res;
    }

    /**
     * @return string
     */
    private function getUserCenterVideoKey()
    {
        $key = "API_DATA:USER_CENTER_Video";

        return $key;
    }

    /**
     * @param array $data data
     *
     * @return mixed
     */
    public function setUserCenterVideo($data)
    {
        $data = json_encode($data);
        $key = $this->getUserCenterVideoKey();
        $res = $this->redis->set($key, $data);
        $this->redis->expire($key, self::EXPIRE_TIME);

        return $res;
    }

    /**
     * @return mixed
     */
    public function getUserCenterVideo()
    {
        $key = $this->getUserCenterVideoKey();
        $res = $this->redis->get($key);
        if ($res)
        {
            return json_decode($res, true);
        }

        return $res;
    }

}
