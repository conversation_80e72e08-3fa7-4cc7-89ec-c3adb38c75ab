<?php

/**
 * 获取运营商状态码 及 上行回复内容
 * */

namespace Crontab;

use Controller;
use Monitor\DataMonitorAction;
use Service\Func\ServiceFunc;
use Service\Func\SmsFunc;
use SmsInterface\SmsLogModel;
use WebLogger\Facade\LoggerFacade;

class ChannelCallbackStatusController extends Controller
{
    /**
     * 提取短信渠道商 发送状态 已废弃
     * 替换成为 业务数据 报警
     *
     * @return boolean
     */
    const AVG_MAX = 14;

    const CALLBACK_ERROR_MNP = 'F132200'; //短信回执，携号转网错误 Mobile number portability

    /**
     * 监控
     *
     * @return bool
     */
    public function actionIndex()
    {
        // 提交、发送、回执成功率监控报警
        if (date("i") % 5 == 0 || RUNMODE === 'development')
        {
            $this->mailMonitor();
        }

        return true;
    }

    private function mailMonitor()
    {
        $redisAction = \RedisAction::connect();
        $redisKeyDump = 'sms:monitor:dump:' . date('Ymd');
        $dumpExist = $redisAction->exists($redisKeyDump) ? true : false;
        //初始化数据类
        $dataMonitor = \Monitor\DataMonitorAction::getInstance();
        $curTime = time();
        // 通道
        $operatorConfigModel = \Admin\OperatorConfigModel::getInstance();
        $operatorList = $operatorConfigModel->getOperatorList();
        // 类型
        $smsTypeModel = \Admin\SmsTypeModel::getInstance();
        $smsTypeList = $smsTypeModel->getSmsTypeList();
        $data = $grayData = $expireList = array();
        foreach ($operatorList as $operator)
        {
            if ($operator['status'] != 1)
            {
                continue;
            }
            $monitorConfig = $operator['monitor_config'];
            $monitorConfig = json_decode($monitorConfig, true);
            if (!$monitorConfig)
            {
                continue;
            }
            $stepTime = $monitorConfig['stepTime'];
            $offsetTime = $monitorConfig['offsetTime'];
            $callbackLimitRate = $monitorConfig['callbackRate'];

            $curHour = date("G");
            if ($curHour >= 23 || $curHour < 7)
            {
                $stepTime = $stepTime * 3;
                if ($callbackLimitRate >= 0.80)
                {
                    $callbackLimitRate = $callbackLimitRate - 0.05;
                }
            }
            $endTime = date("Y-m-d H:i:s", $curTime - $offsetTime);
            $startTime = date("Y-m-d H:i:s", $curTime - $offsetTime - $stepTime);
            if ($callbackLimitRate <= 0)
            {
                continue;
            }
            if ($callbackLimitRate > 1)
            {
                $callbackLimitRate = 0.90;
            }
            list($rate, $success, $failed, $noCallback, $sql) = $dataMonitor->getCallbackRate($operator['id'], $endTime, $startTime);
            if ($rate < $callbackLimitRate)
            {
                $redisKey = "sms:monitor:operatorRepeat:{$operator['id']}";
                $line = <<<MAIL
通道：{$operator['id']}-{$operator['provider_name']}<br/>
时间段：{$startTime}至{$endTime}
<br/>===================<br/>
成功数：{$success}<br/>
失败数：{$failed}<br/>
无回执：{$noCallback}
<br/>===================<br/>
回执成功率：{$rate} = {$success} / ({$success} + {$failed} + {$noCallback})<br/>
回执成功率限制：{$callbackLimitRate}
<br/>===================<br/>
sql: {$sql}<br/>
MAIL;
                if ($redisAction->exists($redisKey))
                {
                    $expireList[] = $redisKey;
                    $grayData[] = "[重复报警]<br/>\n" . $line;
                }
                else
                {
                    $redisAction->setex($redisKey, 1200, 1);
                    $data[] = $line;
                }
            }
        }

        foreach ($smsTypeList as $smsType)
        {
            $monitorConfig = $smsType['monitor_config'];
            $monitorConfig = json_decode($monitorConfig, true);
            if (!$monitorConfig)
            {
                continue;
            }
            $stepTime = $monitorConfig['stepTime'];//监控跨度
            $offsetTime = $monitorConfig['offsetTime'];//等待回执时间
            $commitLimitRate = $monitorConfig['commitLimitRate'];//提交服务商成功率限制
            $receiveLimitRate = $monitorConfig['receiveLimitRate'];//项目提交成功率限制
            //夜间监控比例修改
            /*$curHour = date("G");
            if ($curHour >= 23 || $curHour < 7)
            {
                $stepTime = $stepTime * 3;
                if ($commitLimitRate >= 0.80)
                {
                    $commitLimitRate = $commitLimitRate - 0.05;
                }
                if ($receiveLimitRate >= 0.80)
                {
                    $receiveLimitRate = $receiveLimitRate - 0.05;
                }
            }*/
            $endTime = date("Y-m-d H:i:00", $curTime - $offsetTime);
            $startTime = date("Y-m-d H:i:00", $curTime - $offsetTime - $stepTime);
            if ($commitLimitRate <= 0 && $receiveLimitRate <= 0)
            {
                continue;
            }
            if ($commitLimitRate > 1)
            {
                $commitLimitRate = 0.90;
            }
            if ($receiveLimitRate > 1)
            {
                $receiveLimitRate = 0.90;
            }
            list ($rate, $receiveRate, $commitRate, $success, $failCommit, $failReceive, $sql) = $dataMonitor->getSendRate(
                $smsType['id'],
                $endTime,
                $startTime
            );

            if ($commitRate < $commitLimitRate || $receiveRate < $receiveLimitRate)
            {
                $redisKey = "sms:monitor:typeRepeat:{$smsType['id']}";
                $line = <<<MAIL
类型：{$smsType['id']}-{$smsType['sms_name']}<br/>
时间段：{$startTime}至{$endTime}
<br/>===================<br/>
总成功数：{$success}<br/>
项目提交失败数：{$failReceive}<br/>
提交服务商失败数：{$failCommit}
<br/>===================<br/>
总成功率：{$rate} = {$success} / ({$success} + {$failCommit} + $failReceive)<br/>
项目提交成功率：$receiveRate = ({$success} + {$failCommit}) / ({$success} + {$failReceive} + {$failCommit})<br/>
项目提交成功率限制：{$receiveLimitRate}<br/>
提交服务商成功率：{$commitRate} = {$success} / ({$success} + {$failCommit})<br/>
提交服务商成功率限制：{$commitLimitRate}
<br/>===================<br/>
sql: {$sql}<br/>
MAIL;
                if(preg_match("/验证码/",$smsType['sms_name'])){
                    $line .= "NEED_SEND";
                }
                if ($redisAction->exists($redisKey))
                {
                    $expireList[] = $redisKey;
                    $grayData[] = "[重复报警]<br/>\n" . $line;
                }
                else
                {
                    $redisAction->setex($redisKey, 1200, 1);
                    $data[] = $line;
                }
            }
        }
        $data = array_filter($data);

        $mailBody = "";
        if (count($data) !== 0) {
            foreach ($data as $key => $item) {
                if (preg_match("/NEED_SEND$/", $item)) {
                    $data[$key] = substr($item, 0, -9);
                    $mailBody = $data[$key] . "<br/><br/>";
                }
            }
            if ($mailBody && count($grayData) !== 0) {
                $mailBodyTmp = '';
                foreach ($grayData as $key => $item) {
                    if (preg_match("/NEED_SEND$/", $item)) {
                        $grayData[$key] = substr($item, 0, -9);
                        $mailBodyTmp = $grayData[$key] . "<br/><br/>";
                    }
                }
                if ($mailBodyTmp) {
                    $mailBody .= "<div style='color:slategray'>以下为重复报警消息：<br/><br/>";
                    $mailBody .= $mailBodyTmp;
                    $mailBody .= "</div>";
                }
                unset($mailBodyTmp);
            }
            foreach ($expireList as $expireKey) {
                $redisAction->expire($expireKey, 1200);
            }
        }
        if (RUNMODE != 'development' && $mailBody ) {
            require_once(SRCPATH . '/includes/monitor/SendMessage.php');
            $monitor = new \SendMessage("smsp.2345.net");
            $title = mb_convert_encoding("短信平台业务成功率报警", "UTF-8", "GBK");
            $mailBody = mb_convert_encoding($mailBody, "UTF-8", "GBK");
            $monitor->sendError($title, $mailBody);
        }

        $redisAction->lPush('sms:monitor:dump:' . date('Ymd'), ...$grayData);
        $redisAction->lPush('sms:monitor:dump:' . date('Ymd'), ...$data);
        if (!$dumpExist)
        {
            $redisAction->expireAt('sms:monitor:dump:' . date('Ymd'), strtotime(date('Y-m-d') . ' +5days'));
        }
    }

    /**
     * 发送量监控报警
     * @return bool
     */
    private function sendDataMonitor()
    {
        $endTime = date("Y-m-d H:i:59", time() - 60);
        $startTime = date("Y-m-d H:i:01", strtotime($endTime) - 3600);

        $redisAction = \RedisAction::connect();
        $rateChannel = $redisAction->hGet(\Config_CacheKey::MONITOR_LIMIT_RATE, "rateChannel");
        $rateProject = $redisAction->hGet(\Config_CacheKey::MONITOR_LIMIT_RATE, "rateProject");
        if (!$rateChannel)
        {
            $rateChannel = 0.9;
        }
        if (!$rateProject)
        {
            $rateProject = 0.9;
        }

        $dataMonitor = \Monitor\DataMonitorAction::getInstance();

        // 通道
        $operatorConfigModel = \Admin\OperatorConfigModel::getInstance();
        $operatorList = $operatorConfigModel->getOperatorList();
        $operators = array();
        foreach ($operatorList as $key => $value)
        {
            if ($value['status'] == 1)
            {
                $operators[$value['id']] = $value["provider_name"];
            }
        }

        // 项目
        $projectModel = \Admin\ProjectSecondModel::getInstance();
        $projectList = $projectModel->getProjectList();
        $projects = array();
        foreach ($projectList as $projectInfo)
        {
            if ($projectInfo['status'] == 1)
            {
                $projects[$projectInfo['pid']] = $projectInfo['project_name'];
            }
        }

        $channelData = array();
        foreach ($operators as $channel => $channelName)
        {
            /** @see DataMonitorAction::checkChannelWarning */
            $res = $dataMonitor->checkChannelWarning("current", $channel, 1, $startTime, $endTime, $rateChannel);
            if (isset($res['flag']) && $res['flag'] == false)
            {
                $res['source'] = $operators[$res['source']];
                $res['status'] = "成功";
                $channelData[] = $res;
            }
            $cmp = \Monitor\MonitorAction::CMP_RIGHT;
            $res = $dataMonitor->checkChannelWarning("current", $channel, 0, $startTime, $endTime, $rateChannel, $cmp);
            if (isset($res['flag']) && $res['flag'] == false)
            {
                $res['source'] = $operators[$res['source']];
                $res['status'] = "失败";
                $channelData[] = $res;
            }
        }

        $projectData = array();
        foreach ($projects as $pid => $projectName)
        {
            /** @see DataMonitorAction::checkProjectWarning */
            $res = $dataMonitor->checkProjectWarning("current", $pid, 1, $startTime, $endTime, $rateProject);
            if (isset($res['flag']) && $res['flag'] == false)
            {
                $res['source'] = $projects[$res['source']];
                $res['status'] = "成功";
                $projectData[] = $res;
            }
            $cmp = \Monitor\MonitorAction::CMP_RIGHT;
            $res = $dataMonitor->checkProjectWarning("current", $pid, 0, $startTime, $endTime, $rateProject, $cmp);
            if (isset($res['flag']) && $res['flag'] == false)
            {
                $res['source'] = $projects[$res['source']];
                $res['status'] = "失败";
                $projectData[] = $res;
            }
        }

        //todo 记录报警的项目和时间，放到redis， 后台页面获取前台读取
        $channelData = array_filter($channelData);
        $projectData = array_filter($projectData);
        if (!empty($channelData) || !empty($projectData))
        {
            $emailContent = $this->formatMsg($channelData, "通道");
            $emailContent .= $this->formatMsg($projectData, "项目");

            if ($emailContent)
            {
                $emailSubject = "通道|项目发送量报警";
                $this->sendEmail($emailSubject, $emailContent);
            }
        }

        return true;
    }

    /**
     * 格式化
     *
     * @param array  $originData origin data
     * @param string $type       type
     *
     * @return string
     */
    private function formatMsg($originData, $type = "")
    {
        $msg = "";

        foreach ($originData as $key => $data)
        {
            if ($data["avg"] > self::AVG_MAX && $data["flag"] == false)
            {
                $msg .= "告警消息:" . ($data["newValue"] > $data["forecastValue"] ? "值大于预测范围" : "值小于预测范围") . "<BR>";
                $msg .= "类型:" . $type . " - " . $data["status"] . "<BR><BR>";
                $msg .= "来源:" . $data['source'] . "<BR>";
                $msg .= "取样时间:" . $data['startTime'] . " ~ " . $data['endTime'] . "<BR>";
                $msg .= "取样点:[" . implode(",", $data["origin"]) . "]<BR><BR>";
                $msg .= "预测值:" . $data["forecastValue"] . "<BR>";
                $msg .= "预测比例:" . $data['rate'] . "<BR>";
                $msg .= "预测值范围:[" . $data["min"] . "," . $data["max"] . "]<BR>";
                $msg .= "实际值:" . $data["newValue"] . "<BR>";
                $msg .= "函数: y=" . $data["slope"] . "x + " . $data["offset"] . " <BR> <BR> <BR>";
            }
        }

        return $msg;
    }

    /**
     * send email
     *
     * @param string $mailSubject subject
     * @param string $mailBody    content
     * @param bool   $test        test
     *
     * @return bool
     */
    private function sendEmail($mailSubject, $mailBody, $test = false)
    {
        $mailSubject = "【短信平台报警】" . $mailSubject;
        //smtp配置
        $smtpConfig = array(
//            'server'   => '58.211.78.142',
            'server'   => 'smtp.exmail.qq.com',
            'port'     => 25,
            'username' => '<EMAIL>',
            'password' => 'PdC8afe9',
            'email'    => '<EMAIL>',
        );
        if (RUNMODE === 'development' || $test)
        {
            $smtpemailto = '<EMAIL>';
        }
        else
        {
            $smtpemailto = '<EMAIL>,<EMAIL>,<EMAIL>';
        }
        $smtpemailcc = '';
        $mailtype = "HTML";
        require_once(SRCPATH . '/includes/mail/Smtp.php');
        $smtp = new \Smtp;

        $smtp->init($smtpConfig['server'], $smtpConfig['port'], $smtpConfig['username'], $smtpConfig['password']);
        $smtp->sendmail($smtpemailto, $smtpConfig['email'], $mailSubject, $mailBody, $mailtype, $smtpemailcc);

        return true;
    }

    /**
     * 提取短信渠道商 发送状态 采用新的存储队列和处理方法
     *
     * @return boolean
     */
    public function actionNewProcessor()
    {
        $redisAction = \RedisAction::connect();
        $redisAction->set("ProcessorResponseStart", date("Y-m-d H:i:s"));
        $startTimeStamp = time();
        $items = array();
        $callbackDataKeys = array();
        $originData = array();
        while ($redisAction->exists(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW))
        {
            $data = $redisAction->rPop(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW);
            $data = json_decode($data, true);
            if (!empty($data))
            {
                //携号转网错误回执，日志记录
                if ($data['Status'] && $data['Status'] == self::CALLBACK_ERROR_MNP) {
                    LoggerFacade::info('短信发送回执异常(' . $data['Status'] . ')', $data);
                }

                $mobile = $data['Mobile'];
                if (strlen($data['Mobile']) > 11)
                {
                    $mobile = substr($data['Mobile'], -11, 11);
                }
                $key = "CHANNEL_DATA:" . $data['Msg_Id'] . ':' . $mobile;
                $logId = $redisAction->get($key);
                if (empty($logId))
                {
                    $key = "CHANNEL_DATA:" . $data['Msg_Id'] . ':00' . $data['Mobile'];
                    $logId = $redisAction->get($key);
                    $mobile = $data['Mobile'];
                }
                if (!empty($logId))
                {
                    $status = ($data['Status'] === 'DELIVRD') ? true : false;
                    $channelData = array(
                        'log_id'   => $logId,
                        'phone'    => $mobile,
                        'status'   => $status,
                        'add_time' => date('Y-m-d H:i:s'),
                        'message'  => var_export(\EncodingAction::iteratorArray($data, "GBK"), true),
                    );
                    if (isset($data['TimeStamp']))
                    {
                        $channelData['add_time'] = $data['TimeStamp'];
                    }

                    if (isset($data['RecvTime']))
                    {
                        $channelData['add_time'] = $data['RecvTime'];
                    }
                    
                    if (!$status)
                    {
                        $logInfo = SmsLogModel::getInstance()->getLogInfo($logId);
                        SmsFunc::Monitor($data['Status'], $data['Mobile'], $logId, $logInfo);
                    }

                    //如果是swoole进来的
                    $isReturn = ServiceFunc::logIdSendThroughSwoole($logId, $key, $channelData, $data);
                    if ($isReturn)
                    {
                        continue;
                    }
                    array_push($items, $channelData);
                    array_push($callbackDataKeys, $key);
                    array_push($originData, $data);
                }
            }
            if (count($items) == 10)
            {
                $this->addChannelCallbackStatusMulti($items, $callbackDataKeys, $originData);
                $items = array();
                $callbackDataKeys = array();
                $originData = array();
            }
            //当前时间 减去 脚本开始时间,如果执行程序执行了50秒,退出去,让下一个crontab来执行
            $timeElapsed = time() - $startTimeStamp;
            if ($timeElapsed > 50)
            {
                break;
            }
        }
        //循环结束，如果数组里还有未处理的数量小于10的条目，处理完剩下的
        if (!empty($items))
        {
            $this->addChannelCallbackStatusMulti($items, $callbackDataKeys, $originData);
        }
        $redisAction->set("ProcessorResponseEnd", date("Y-m-d H:i:s"));
        echo 'end';

        return true;
    }

    /**
     * 获取用户回复内容
     *
     * @return bool
     */
    public function actionGetSmsResponse()
    {
        $redisAction = \RedisAction::connect();
        $startTimeStamp = time();
        while ($redisAction->lLen('channelCallbackResponse') > 0)
        {
            $oriData = $redisAction->rPop('channelCallbackResponse');
            $data = json_decode($oriData, true);
            if (!empty($data) && is_array($data))
            {
                $smsLogModel = \SmsInterface\SmsLogModel::getInstance();
                $channelData = array(
                    'msgId'      => $data['msgId'],
                    'phone'      => $data['phone'],
                    'is_unsub'   => $data['is_unsub'],
                    'providerId' => $data['providerId'],
                    'message'    => \EncodingAction::transcoding($data['message']),
                );
                if (isset($data['add_time']))
                {
                    $channelData['add_time'] = $data['add_time'];
                }
                if (strpos($data['providerId'], 'GuoDu') >= 0)
                {
                    $redisAction->lPush('callbackResponsePush', $oriData);
                }
                $isTrue = $smsLogModel->addSmsReceiptInfo($channelData);
                if ($isTrue)
                {
                    echo 'success';
                }
                else
                {
                    echo 'fail';
                }
            }
            $timeElapsed = time() - $startTimeStamp;
            if ($timeElapsed > 50)
            {
                break;
            }
        }
        echo 'end';

        return true;
    }

    /**
     * 推送用户上行内容至贷款王
     *
     * @return void
     */
    public function actionPushSmsResponse()
    {
        $redisAction = \RedisAction::connect();
        $postOut = array();
        $smsLogModel = \SmsInterface\SmsLogModel::getInstance();
        while ($redisAction->lLen('callbackResponsePush') > 0)
        {
            $data = $redisAction->rPop('callbackResponsePush');
            $data = json_decode($data, true);
            if (!empty($data) && is_array($data))
            {
                $logIds = $smsLogModel->getRecentLogIdByPhone($data['phone']);
                $recentSms = array();
                foreach ($logIds as $logId)
                {
                    $recentSms[] = $logId['log_id'];
                }
                if (isset($data['add_time']))
                {
                    $data['add_time'] = date('Y-m-d H:i:s');
                }
                if ($data['message'] === '')
                {
                    continue;
                }
                if (strpos($data['providerId'], 'GuoDu') >= 0)
                {
                    $postOut[] = array(
                        'responseId' => $data['msgId'],
                        'source'     => $data['providerId'],
                        'phone'      => $data['phone'],
                        'timeStamp'  => strtotime($data['add_time']),
                        'message'    => $data['message'],
                        'recentSms'  => $recentSms,
                    );
                }
            }
        }
        $smsLogModel->close();
        $postCount = count($postOut);
        if ($postCount === 0)
        {
            return;
        }
        LoggerFacade::info("Ready to Push {$postCount} Items.");
        $dkwUrl = "https://managerdaikuan.2345.com/oms/moduleservice/campaign/debitAmountService/processSmsResult";
        if (RUNMODE === 'development')
        {
            $dkwUrl = "http://t3-managerdaikuan.2345.com/oms/moduleservice/campaign/debitAmountService/processSmsResult";
        }
        $plain = json_encode($postOut);
        $content = base64_encode($plain);
        $key = '1D6JFKZTQZUI2LTB1YACNQ3G3OBOMO2S';
        $sign = md5($plain . $key);
        $result = \FuncAction::postTimeout($dkwUrl, array('content' => $content, 'sign' => $sign), 10, false, false);
        $resultObj = json_decode($result, true);
        if ($resultObj !== false && $resultObj['errorCode'] == 0)
        {
            LoggerFacade::info("Pushed {$postCount} Items.");
        }
        else
        {
            LoggerFacade::info("error push items Pushing {$postCount} Items. Return: {$result}");
        }
        LoggerFacade::info("Dump: {$plain}");
    }

    /**
     * 接收运行商返回的状态 及 上行回复内容
     *
     * @return bool
     */
    public function actionAcceptSmsResponseInfo()
    {
        $redisAction = \RedisAction::connect();
        $redisAction->set("SmsResponseTimeStart", date("Y-m-d H:i:s"));

        $channelAction = \Crontab\ChannelAction::getInstance();
        $channelAction->run(0);
        $channelAction->run(2);
        $channelAction->run(3);
        $channelAction->run(4);
        $redisAction->set("SmsResponseTimeEnd", date("Y-m-d H:i:s"));
        sleep(2);

        $channelAction->run(0);
        $channelAction->run(2);
        $channelAction->run(3);
        $channelAction->run(4);
        $redisAction->set("SmsResponseTimeEnd2", date("Y-m-d H:i:s"));
        sleep(2);

        $channelAction->run(0);
        $channelAction->run(2);
        $channelAction->run(3);
        $channelAction->run(4);
        $redisAction->set("SmsResponseTimeEnd3", date("Y-m-d H:i:s"));
        sleep(2);

        $channelAction->run(0);
        $channelAction->run(2);
        $channelAction->run(3);
        $channelAction->run(4);
        $redisAction->set("SmsResponseTimeEnd4", date("Y-m-d H:i:s"));
        echo 'end';

        return true;
    }

    /**
     * @param array $items            回执内容
     * @param array $callbackDataKeys 字段
     * @param array $originData       原始内容
     *
     * @return boolean
     */
    protected function addChannelCallbackStatusMulti($items, $callbackDataKeys, $originData)
    {
        $redisAction = \RedisAction::connect();
        $smsAction = \SmsAction::getInstance();
        $isTrue = $smsAction::addChannelCallbackStatusMulti($items);

        if ($isTrue)//如果成功，则从redis中删除对应关系的key， 并置空items()
        {
            foreach ($callbackDataKeys as $dataKey)
            {
                $redisAction->del($dataKey);
            }
        }
        else //如果是失败，重新push到队列里
        {
            foreach ($originData as $item)
            {
                $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($item));
            }
        }

        return true;
    }

    /**
     * 功    能：每日清除三天前缓存
     * 修改日期：2019年6月11日
     *
     * @author: wangchenglong
     * @return bool
     */
    public function actionClearSmsProjectBindLog()
    {
        $SmsProjectBindLogId = \SmsProjectBindLogIdAction::getInstance();
        $result = $SmsProjectBindLogId->clearSmsProjectBindLog();
        if ($result) {
            echo 'OK';
            return true;
        } else {
            echo 'failed';
            return false;
        }
    }
}
