document.write('<script type="text/javascript" src="//passport.2345.com/js/client/base.js"></script>');
document.write('<script type="text/javascript" src="//passport.2345.com/js/client/jquery.md5.js"></script>');
document.write('<script type="text/javascript" src="//cstaticdun.126.net/load.min.js' + '?' + parseInt((new Date()).getTime() / (60 * 1000), 10) + '"></script>');
if (console === undefined) {
    var console = {
        log: function ($str) {
        },
        debug: function ($str) {
        }
    }
}

function responseszgyym(statusCode, unf, token, captchaType, captchaId) {
    login.szgyym(statusCode, unf, token, captchaType, captchaId);
}

function isWeiXinBrowser() {
    var ua = window.navigator.userAgent.toLowerCase();
    return ua.match(/MicroMessenger/i) == 'micromessenger';
}

function isH5GameAppBrowser() {
    var ua = window.navigator.userAgent.toLowerCase();
    return ua.match(/2345h5game_android_app/i) == '2345h5game_android_app';
}

function isMobileBrowser() {
    var ua = window.navigator.userAgent.toLowerCase();
    var bIsAndroid = ua.match(/android/i) == "android";
    var u = window.navigator.userAgent;
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    return bIsAndroid || isiOS;
}

var login = {
    proxyUrl: '',
    errorLog: '',
    isShowCode: false,
    isGame: false,
    isMicro: false,
    isQqShow: true,
    isWeixinShow: false,
    commitParam: new Object(),
    locationUrl: '',
    wxAppid: 'wx9858d355cbe24ab4',
    isAjax: false,
    isThirdAjax: false,
    jqueryObj: $,
    oldDomain: document.domain,
    unf: 'username',
    showCodeFirst: false,
    logining: false,
    endCallLfaction: '',
    captchaIns: null,
    captchaType: 'bind',
    captchaId: 'f8582f417b2b44dc859a715e8f75a0e1',
    init: function (ctrlSwitchA) {
        this.loadFiles(ctrlSwitchA);
        login.jqueryObj('.g-login .changeCode,.codeImg').click(function () {
            login.showCodeImg();
        });
        login.jqueryObj(".g-login .g-inputTxt input").focus(function () {
            login.jqueryObj(this).parent().addClass("g-inputTxt-focus");
            login.jqueryObj(this).siblings(".sDes").css("display", "none");
        });
        login.jqueryObj(".g-login .g-inputTxt input").blur(function () {
            login.jqueryObj(this).parent().removeClass("g-inputTxt-focus g-inputTxt-error");
            if (login.jqueryObj(this).val() == "") {
                login.jqueryObj(this).siblings(".sDes").css("display", "block");
            }
        });

        if (!login.isPlaceholder()) {
            login.jqueryObj(function () {
                setTimeout(function () {
                    login.jqueryObj(".g-login .g-inputTxt .judgmentValue").each(function () {
                        if ($(this).val() != "") {
                            $(this).parent().find(".sDes").hide();
                        }
                    });
                }, 200);
            });
        }

        var exePopupName = this.exeFuncPopupName;
        login.jqueryObj('.g-login .loginClose').click(function () {
            if (typeof exePopupName != 'undefined') {
                exePopupName();
            }
            login.jqueryObj('#jsLoginDiv').hide();
        });
        if (typeof this.locationUrl == 'undefined') {
            this.locationUrl = 'http://' + document.domain;
        }
        var callbackUrl = '&callback=';

        switch (typeof (this.setCookieUrl)) {
            case 'string' :
                callbackUrl += this.setCookieUrl;
                break;
            case 'object' :
                for (var surl in this.setCookieUrl) {
                    callbackUrl += this.setCookieUrl[surl] + ',';
                }
                break;
            default:
                callbackUrl = '';
        }

        login.jqueryObj('.g-login .thirdPartyLogin').click(function () {
            var thirdQqCallLocation = login.getThirdLocationUrl("qq");
            var thirdWeixinCallLocation = login.getThirdLocationUrl("weixin");

            var type = login.jqueryObj(this).attr('type');
            var link = '';
            switch (type) {
                case 'qq':
                    link = '//login.2345.com/qq?forward=' + thirdQqCallLocation + callbackUrl;
                    break;
                case 'weixin':
                    if (isWeiXinBrowser()) {
                        link = '//passport.2345.com/weixin/mp/' + login.wxAppid + '?forward=' + thirdWeixinCallLocation + callbackUrl;
                    } else if (isH5GameAppBrowser()) {
                        login.launchWeixinClient(login.wxAppid, thirdWeixinCallLocation + callbackUrl);

                        return;
                    } else {
                        link = '//passport.2345.com/weixin?forward=' + thirdWeixinCallLocation + callbackUrl;
                    }
                    break;
                case 'register':
                    link = login.jqueryObj(this).attr("href");
                    break;
                default:
                    link = '';
                    break;
            }
            if (link == '') {
                return false;
            }
            if (login.isThirdAjax) {
                login.thirdExeName(link);
            } else {
                login.jqueryObj(this).attr("href", link);
                window.location = link;
            }

        });
        login.lFaction('', '');
    },

    /**
     * 控制策略的请求函数,会在两种场景下请求:
     * 1.页面初加载; 2.登录错误;
     * 1.页面加载时: 请求会判断IP等规则返回是否需要显示验证码等
     * 2.登录错误时: 需要更新token、随机用户名等字段
     * @param loginError 是否是登录错误返回的,登录错误场景该变量值为'loginError'
     * @param data 登录失败时,返回的数据
     */
    lFaction: function(loginError, data) {
        var lei = loginError,
            bd = data,
            isInit = loginError == 'loginError' ? 0 : 1,
            rvn = Math.floor(Math.random() * 1000),
            protocal = document.location.protocol;
        $.ajax({
            url: protocal + "//passport.2345.com/webapi/NELogin/faction?init=" + isInit + "&v="+rvn,
            data: '',
            async: false,
            dataType: 'jsonp',
            success: function (lfData) {
                var JlfData = eval("(" + lfData + ")");
                /**
                 * status: 状态码,预留字段,目前只可能有一个值P10086
                 * unf: 每次刷新的随机用户名字段
                 * token: 每次刷新的token
                 * isInit: 是否是初次加载
                 * 初次加载时,是否显示验证码由isInit决定,登录错误是否显示验证码由接口返回值决定
                 */
                responseszgyym(JlfData.status, JlfData.unf, JlfData.token, JlfData.type, JlfData.captchaId);

                if (lei != '' && bd) {
                    login.refreshCaptch(bd);
                }

                login.captchaIns && login.captchaIns.refresh()

                try {
                    !login.captchaIns && initNECaptcha({
                        element: '#captcha_div',
                        captchaId: login.captchaId,
                        mode: login.captchaType, // 仅智能无感知验证码时，mode 才能设置为 bind
                        width: '210px',
                        popupStyles: {
                            capBarHeight: 30,
                            capPadding: 1
                        },
                        appendTo: '#jsLoginDiv',
                        onVerify: function(err, data){
                            // 用户验证码验证成功后，进行实际的提交行为
                            if (!err) {
                                login.submitForm();
                            } else {
                                login.cc('NECaptcha_verify_fail');
                            }
                        }
                    }, function onload (instance) {
                        // 初始化成功
                        login.captchaIns = instance;
                        // 登录按钮点击事件
                        login.jqueryObj('.g-login .btn-submit').click(function () {
                            if (!login.checkParams()) {
                                return false;
                            }

                            try {
                                if (login.captchaType === 'popup') {
                                    login.captchaIns && login.captchaIns.popUp();
                                } else {
                                    login.captchaIns && login.captchaIns.verify();
                                }
                            } catch(e) {
                                login.showErrorTips('#g-error-tips', "网络开小差，请刷新重试");
                            }

                        });
                    }, function onerror (err) {
                        login.handleNeInitError();
                    });
                } catch (e) {
                    login.handleNeInitError();
                }


                setTimeout(login.removeSubmitDisabled, 500);
            },
            error: function (jqXHR, textStatus, errorThrown) {
                login.removeSubmitDisabled();
                console.log(jqXHR.status);
            },
            timeout: 3000
        });
    },

    handleNeInitError: function () {
        // 如果易盾初始化失败，跳过前端易盾验证
        login.jqueryObj('.g-login .btn-submit').click(function () {
            login.checkParams && login.submitForm();
        });
        login.cc('NECaptcha_init_fail');
    },

    checkParams: function () {
        if (!login.checkUserAccount()) {
            return false;
        }
        if (!login.checkUserPass()) {
            return false;
        }
        return true;
    },

    getThirdLocationUrl: function (type) {
        var url = '';
        switch (typeof (this.thirdLocationUrl)) {
            case 'string':
                url = this.thirdLocationUrl;
                break;
            case 'object' :
                if (type === "qq") {
                    url = (typeof this.thirdLocationUrl['qq']) !== 'undefined' ? this.thirdLocationUrl['qq'] : this.locationUrl;
                }
                else if (type === "weixin") {
                    url = (typeof this.thirdLocationUrl['weixin']) !== 'undefined' ? this.thirdLocationUrl['weixin'] : this.locationUrl;
                }
                break;
            default:
                url = this.locationUrl;
        }

        return url;
    },

    launchWeixinClient: function (wxAppid, forward) {
        if (typeof(WXCallback) == "object") {
            $.ajax({
                url: "//passport.2345.com/weixin/h5App/" + wxAppid,
                data: 'forward=' + forward,
                async: false,
                dataType: 'jsonp',
                success: function (response) {
                    WXCallback.launchWX(response);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR.status);
                },
                timeout: 3000
            });
        }
    },

    //微信登录
    setWeixinQrImg: function (wxQrImgId, cssUrl, style) {
        !function (a, b) {
            function d(a) {
                var e, c = b.createElement("iframe"),
                    d = "https://open.weixin.qq.com/connect/qrconnect?appid=" + a.appid + "&scope=" + a.scope + "&redirect_uri=" + a.redirect_uri + "&state=" + a.state + "&login_type=jssdk";
                d += a.style ? "&style=" + a.style : "";
                d += a.href ? "&href=" + a.href : "";
                c.src = d;
                c.frameBorder = "0";
                c.allowTransparency = "true";
                c.scrolling = "no";
                e = b.getElementById(a.id);
                var width = e.getAttribute("data-width"), height = e.getAttribute("data-height");
                c.width = width ? width : "150px", c.height = height ? height : "140px";
                e.innerHTML = "", e.appendChild(c)
            }

            a.WxLogin = d
        }(window, document);

        // 参考https://passport.2345.com/css/wx-qr-login.css
        if(!cssUrl) {
            cssUrl = "";
        }

        $.ajax({
            url: "//passport.2345.com/weixin/jsLoginQr/",
            data: 'forward=' + this.getThirdLocationUrl("weixin"),
            async: false,
            dataType: 'jsonp',
            success: function (response) {
                if (response) {
                    var obj = new WxLogin({
                        id: wxQrImgId,
                        appid: response.data.appid,
                        scope: 'snsapi_login',
                        redirect_uri: response.data.redirectUri,
                        state: response.data.state,
                        style: style,
                        href: cssUrl
                    });
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(jqXHR.status);
            },
            timeout: 3000
        });
    },

    loadFiles: function (ctrlSwitchA) {
        if (typeof(ctrlSwitchA) != "object" || ctrlSwitchA == null) {
            var ctrlSwitchA = {
                "isPop": false
            };
        }
        if (typeof(ctrlSwitchA["isPop"]) == "undefined" ? false : !ctrlSwitchA["isPop"]) {
            login.jqueryObj(".g-login .loginClose").hide();
        }
    },
    checkLoginStatus: function (data) {
        if (data.display) {
            login.isShowCode = data.display;
            if (!login.isMicro) {
                login.showCodeImg();
            }
        }
    },
    setWxAppid: function (wxAppid) {
        this.wxAppid = wxAppid;
    },
    setLocation: function (url) {
        this.locationUrl = url;
    },
    setGameProject: function () {
        this.isGame = true;
    },
    setMicro: function (flag) {
        this.isMicro = flag;
    },
    setQqShow: function (flag) {
        this.isQqShow = flag;
    },
    setWeixinShow: function (flag) {
        this.isWeixinShow = flag;
    },
    setThirdLocation: function (obj) {
        this.thirdLocationUrl = obj;
    },
    setThirdExeFunc: function (funcName) {
        this.thirdExeName = funcName;
    },
    setError: function (error) {
        this.errorLog = error;
    },
    getLastError: function () {
        return this.errorLog;
    },
    exeFunc: function (funcName) {
        this.exeFuncName = funcName;
    },
    exePopup: function (popupName) {
        this.exeFuncPopupName = popupName;
    },
    //设置提交的参数
    setCommitParam: function (data) {
        this.commitParam = data;
    },
    //获取提交参数转换成input
    getOtherParamHtml: function () {
        var str = '';
        for (var key in this.commitParam) {
            str += '<input type="hidden" value="' + this.commitParam[key] + '" name="' + key + '">';
        }
        return str;
    },
    setLoginConfig: function () {

    },
    setDomain: function (doamainName) {
        this.getDomainName = doamainName;
    },
    setProxy: function (proxyUrl) {
        this.proxyUrl = proxyUrl;
    },
    setCookieUrl: function (url) {
        this.setCookieUrl = url;
    },
    isHtml5: function () {
        if (window.applicationCache) {
            return true;
        } else {
            return false;
        }
    },
    getLoginHtml: function () {
        return '<div class="g-login" id="jsLoginDiv">' +
            '<div class="g-login-th">' +
            '<span class="sMark g-left">2345\u5e10\u53f7\u767b\u5f55</span>' +
            '<a href="javascript:void(0);" class="g-login-closeBtn g-right loginClose"></a>' +
            '</div>' +
            '<div class="g-login-tb clearfix">' +
            ' <form id="myFormLogin">' +
            this.getOtherParamHtml()


            + '<input type="hidden" name="cmd" value="login">' +
            '<input type="hidden"  name="currtime"/>' +
            '<input type="hidden"  name="flToken"/>' +
            '<div class="g-login-form">' +
            '<div class="g-inputTxt">' +
            '<i class="g-icon-name"></i>' +
            '<span class="sDes">\u624B\u673A/\u5DF2\u9A8C\u8BC1\u90AE\u7BB1/\u7528\u6237\u540D</span>' +
            '<input class="judgmentValue" type="text" name="username" value="">' +
            '</div>' +
            '<div class="g-inputTxt">' +
            '<i class="g-icon-password"></i>' +
            '<span class="sDes">\u5bc6\u7801</span>' +
            '<input type="hidden" value="" name="password">' +
            '<input class="judgmentValue" type="password" value="" id="expassword">' +
            '</div>' +
            '<div id="captcha_div"></div>' +
            '<div class="g-error" id="g-error-tips" style="display:none"></div>' +
            '<div class="g-txt"><span class="g-sCheck g-left"><input type="checkbox" checked class="g-checkbox" name="autoLogin" id="autoLogin"><label for="autoLogin">\u4e0b\u6b21\u81ea\u52a8\u767b\u5f55</label></span><a href="//passport.2345.com/find?type=password" target="_blank" class="g-right forgetPassClass">\u627E\u56DE\u5BC6\u7801</a></div>' +
            '<input  type="button" class="g-btn btn-submit" value="\u767b&nbsp;&nbsp;&nbsp;&nbsp;\u5f55">' +
            '<div class="g-other-login">' +
            '<span class="sTit">\u4f60\u4e5f\u53ef\u4ee5\u7528\u4ee5\u4e0b\u65b9\u5f0f\u767b\u5f55</span>' +
            '<div class="otherStyle">' +
            (login.isQqShow ? '<a href="javascript:void(0);" class="blueBtn thirdPartyLogin" type="qq"><i class="g-icon-qq"></i><em class="qqTxthide">QQ登录</em></a>' : "") +
            (login.isWeixinShow? '<a href="javascript:void(0);" class="blueBtn thirdPartyLogin login-weixin" type="weixinJs"><i class="g-icon-weixin"></i><em class="qqTxthide">微信登录</em></a>' : '' ) +
            (login.isMicro ? '' : '<a href="//passport.2345.com/reg.php?forward=' + this.locationUrl + '" target="_blank"  class="blueBtn thirdPartyLogin" type="register"><i class="g-icon-reg"></i><em class="qqTxthide">注册</em></a>') +
            '</div>' +
            (isWeiXinBrowser() || isH5GameAppBrowser() ?
                    '<div class="otherStyle">' +
                    '<a href="javascript:void(0);" class="blueBtn thirdPartyLogin" type="weixin"><i class="g-icon-weixin"></i><em class="qqTxthide">微信登录</em></a>' +
                    '</div>'
                    : ''
            )
            +
            '</div>' +
            '</div>' +
            '</form>' +
            '</div>' +
            '</div>';
    },
    showErrorTips: function (tag, msg) {
        var gErrorTips = login.jqueryObj(tag);
        gErrorTips.html(msg);
        gErrorTips.show();
    },
    hideErrorTips: function (tag) {
        var gErrorTips = login.jqueryObj(tag);
        gErrorTips.html("");
        gErrorTips.hide();
    },
    checkUserAccount: function () {
        var status = false;
        var unfS = '.g-login .g-inputTxt input[name="' + login.unf + '"]';
        var userNameObj = login.jqueryObj(unfS);
        var username = userNameObj.val();
        if (username == '') {
            login.showErrorTips('#g-error-tips', '\u7528\u6237\u540d\u4e0d\u80fd\u4e3a\u7a7a');
            userNameObj.parent().addClass('g-inputTxt-error');
        } else if (username.length < 2) {
            login.showErrorTips('#g-error-tips', '\u6700\u5c112\u4e2a\u5b57\u7b26!');
            userNameObj.parent().addClass('g-inputTxt-error');
        } else if (username.replace(/[^\x00-\xff]/g, "**").length > 24) {
            login.showErrorTips('#g-error-tips', '\u8bf7\u4e0d\u8981\u8d85\u8fc724\u4e2a\u5b57\u7b26!');
            userNameObj.parent().addClass('g-inputTxt-error');
        } else {
            status = true;
        }
        return status;
    },
    checkUserPass: function () {
        var status = false;
        var password = login.jqueryObj('#expassword').val();
        if (password == '') {
            login.showErrorTips('#g-error-tips', '\u8bf7\u5148\u8f93\u5165\u5bc6\u7801!');
            login.jqueryObj('#expassword').parent().addClass('g-inputTxt-error');
        } else {
            password = MD5(password);
            var mdpwd = password.split("").reverse().join("");
            login.jqueryObj('.g-login input[name="password"]').val(mdpwd);
            status = true;
        }
        return status
    },
    showLogin: function (tag, ctrlSwitchA) {
        if (this.proxyUrl == '') {
            login.showErrorTips('body', '\u4ee3\u7406\u9875\u9762\u4e0d\u80fd\u4e3a\u7a7a');
            return false;
        }
        login.jqueryObj(tag).append(this.getLoginHtml());
        this.init(ctrlSwitchA);
        if (this.isHtml5()) {
            this.createProxy();
            window.addEventListener("message", function (e) {
                login.response(e.data);
            }, false);
        }
    },
    submitForm: function () {
        if (login.jqueryObj.browser.msie && (login.jqueryObj.browser.version == "6.0") && !login.jqueryObj.support.style) {
            login.jqueryObj('.g-login a.btn-submit').attr('href', '###');
        }
        login.jqueryObj('.g-login input[name="currtime"]').val(parseInt(new Date().getTime() / 1000));
        var getHost = window.location.host;
        var getCurrentUrl = window.location;
        var tval = login.jqueryObj('input[name="flToken"]').val();
        login.jqueryObj('input[name="flToken"]').attr('value', MD5(tval));
        var submitData = login.jqueryObj('#myFormLogin').serialize() + '&domain=' + getHost + '&currentUrl=' + getCurrentUrl;
        window.submitData = submitData;

        if (login.logining) {
            return;
        }
        login.logining = true;
        login.jqueryObj('.btn-submit').attr('disabled', "true");

        if (login.isHtml5()) {
            var win = document.getElementById("loginProxy").contentWindow;
            var protocal = document.location.protocol;
            win.postMessage(submitData, protocal + "//passport.2345.com");
        } else {
            login.createProxy();
        }
    },
    createProxy: function () {
        var code = new Base64();
        if (this.isHtml5()) {
            login.jqueryObj('body').append('<iframe id="loginProxy" style="display:none" src="//passport.2345.com/proxy_ne.html#' + code.encode(this.proxyUrl) + '"></iframe>');
        } else {
            var proxyHash = code.encode(this.proxyUrl);
            var param = '';
            if (this.oldDomain != document.domain) {
                this.getDomainName = document.domain;
            }
            if (typeof this.getDomainName != 'undefined') {
                proxyHash = code.fencode(this.proxyUrl + '?domain=' + this.getDomainName);
                param = '?domain=' + this.getDomainName;
            }
            login.jqueryObj('body').append('<iframe id="loginProxy" style="display:none" src="' + this.proxyUrl + param + '#' + proxyHash + '"></iframe>');
        }
    },
    /**
     * 函数名是随便写的一串字符没有特殊意义
     * @param statusCode 状态码
     * @param unf 随机用户名字段
     * @param token 随机token
     * @param isInit 是初次加载还是登陆错误刷新
     */
    szgyym: function(statusCode, unf, token, captchaType, captchaId) {
        if (statusCode == 'P10086') {
            login.unf = unf;
            login.captchaType = captchaType;
            login.captchaId = captchaId;
            var userNameObj = login.jqueryObj('.judgmentValue');
            login.jqueryObj(userNameObj[0]).attr('name', login.unf);
            login.jqueryObj('input[name="flToken"]').attr('value', token);
        }
    },
    removeSubmitDisabled: function() {
        login.logining = false;
        login.jqueryObj('.btn-submit').removeAttr('disabled');
    },
    response: function (retdata) {
        data = eval("(" + retdata + ")");
        try {
            data.status;
        }
        catch (error) {
            login.removeSubmitDisabled();
            retdata;
            return false;
        }

        if (data.status !== 'P00001' && new RegExp('^P[0-9]{5}$').test(data.status)) {
            login.lFaction('loginError', data);
        } else if (data.status === 'P00001') {
            login.captchaIns = null;
            if (typeof (data.loadPage) == "undefined") {
                data.loadPage = new Array();
            }
            if (document.domain.slice(-8) != '2345.com') {
                var cookieI = eval("(" + data.setCookie + ")");
                var param = '?' + cookieI.I;
                switch (typeof (this.setCookieUrl)) {
                    case 'string' :
                        data.loadPage[data.loadPage.length] = this.setCookieUrl + param;
                        break;
                    case 'object' :
                        for (var surl in this.setCookieUrl) {
                            data.loadPage[data.loadPage.length] = this.setCookieUrl[surl] + param;
                        }
                        break;
                    default:
                        data.loadPage[data.loadPage.length] = '/user/SetLoginCallBack?' + param;
                }
            }
            if (data.loadPage.length == 0) {
                login.endRunFunc(data);
            }
            else {
                var len = data.loadPage.length;
                var j = 0;
                for (var i in  data.loadPage) {
                    login.jqueryObj.getScript(data.loadPage[i], function () {
                        ++j;
                        if (j == len) {
                            login.endRunFunc(data);
                        }
                    });
                }
            }
        }
    },
    refreshCaptch: function (data) {
        if (typeof (data.location) != "undefined") {
            window.location = data.location;
            return false;
        }

        //只要有错误,则清空密码
        login.jqueryObj('.g-login input[name="check_code"]').val("");
        if (!login.isMicro || data.status != 'P00004') {
            login.jqueryObj('.g-login input[name="password"]').val('');
            login.jqueryObj("#expassword").val('');
        }
        login.showErrorTips('#g-error-tips', data.msg);
        login.captchaIns && login.captchaIns.refresh()
    },
    endRunFunc: function (data) {
        setTimeout(login.removeSubmitDisabled, 500);
        if (typeof this.exeFuncName != 'undefined') {
            this.exeFuncName();
        }
        if (!this.isAjax) {
            login.locationPage(data)
        }
    },
    locationPage: function (data) {
        if (typeof (data.location) != "undefined") {
            window.location = data.location;
        } else if (this.locationUrl != '') {
            window.location = this.locationUrl;
        } else {
            window.location.reload();
        }
    },
    chgWeiboText: function (text) {
        //微博登录已废除,该函数保留只是为了兼容旧版本
    },
    isPlaceholder: function () {
        var input = login.jqueryObj(".g-login input").get(0);
        return "placeholder" in input;
    },
    cc: function (a) {
        var b = arguments,
            web = "ajax54",
            a2,
            i1 = document.cookie.indexOf("uUiD="),
            i2;
        if (b.length > 1)
            web = b[1];
        if (i1 != -1) {
            i2 = document.cookie.indexOf(";", i1);
            a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
        }
        if (!a2) {
            a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
            document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
        }
        if (a.length > 0) {
            var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
            $.getScript(c)
        }
        return true;
    }
};