<?php

/**
 *
 * Copyright (c) 2016,上海二三四五网络科技股份有限公司
 * 文件名称：Http.php
 * 摘    要：http工具类
 * 作    者：谢志新 <EMAIL>
 * 修改日期：2016.5.15
 *
 */

namespace Common\Utils;

use WebLogger\Facade\LoggerFacade;

class Http
{

    private static $timeout = 30;

    private static function curlReturn($errorCode, $content)
    {
        return array($errorCode, $content);
    }

    private static function setOption($ch, array $options = null)
    {
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        if (!empty($options) && isset($options["cookie"]) && is_array($options["cookie"]))
        {
            $cookieArr = array();
            foreach ($options["cookie"] as $key => $value)
            {
                $cookieArr[] = "$key=$value";
            }
            $cookie = implode("; ", $cookieArr);
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }
        $timeout = self::$timeout;
        if (isset($options["timeout"]))
        {
            $timeout = $options["timeout"];
        }
        if ($timeout < self::$timeout)
        {
            $timeout = self::$timeout;
        }
        if (isset($options["ua"]))
        {
            curl_setopt($ch, CURLOPT_USERAGENT, $options["ua"]);
        }
        if (isset($options['header'])) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $options['header']);
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    }

    public static function get($url, $params, array $options = null)
    {
        if (!empty($params))
        {
            $p1 = array();
            foreach ($params as $k => $v)
            {
                $p1[] = $k . "=" . urlencode($v);
            }
            $url .= "?" . implode("&", $p1);
        }
        $ch = curl_init($url);
        if (empty($options))
        {
            $options = array();
        }
        self::setOption($ch, $options);
        $content = curl_exec($ch);
        $errorCode = curl_errno($ch);
        curl_close($ch);
        return self::curlReturn($errorCode, $content);
    }

    public static function post($url, $params, array $options = null)
    {
        $ch = curl_init();
        self::setOption($ch, $options);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        if (empty($params)) {
            curl_setopt($ch, CURLOPT_POST, false);
        }
        $params = is_array($params) ? http_build_query($params) : $params;
        curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
        $current = mSecTime();
        $content = curl_exec($ch);
        $errorCode = curl_errno($ch);
        $curlInfo = curl_getinfo($ch);
        curl_close($ch);
        LoggerFacade::info("刷新缓存", [
            "type" => "http_request",
            "path" => $url,
            "exec_time" => mSecTime() - $current,
            "code" => strpos($content, '"code":200') === false ? 0 : 1,
            "http_code" => $curlInfo["http_code"],
        ]);
        return self::curlReturn($errorCode, $content);

    }

    public static function getUserIp()
    {
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && $_SERVER['HTTP_X_FORWARDED_FOR'])
        {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        }
        else
        {
            $ip = $_SERVER["REMOTE_ADDR"];
        }
        return $ip;
    }

    /**
     * 判断是否是微信浏览器访问
     *
     * @return bool
     */
    public static function isWeiXinBrowser()
    {
        return !!preg_match('#^Mozilla/5.0.*MicroMessenger#i', $_SERVER['HTTP_USER_AGENT']);
    }

    /**
     * 判断是否为页游h5游戏包装app的WebView
     *
     * @return bool
     */
    public static function isH5AppWebView()
    {
        return !!preg_match('#2345h5game_android_app#i', $_SERVER['HTTP_USER_AGENT']);
    }

    /**
     * 判断是否为google浏览器
     *
     * @return bool
     */
    public static function isChrome()
    {
        $agent = strtolower($_SERVER['HTTP_USER_AGENT']);
        return strpos($agent, 'chrome') || strpos($agent, 'google');
    }

    /**
     * 判断是否为ajax请求
     *
     * @return bool
     */
    public static function isAjax()
    {
        return (isset($_SERVER["HTTP_X_REQUESTED_WITH"]) && strtolower($_SERVER["HTTP_X_REQUESTED_WITH"]) === "xmlhttprequest") ? true : false;
    }

}
