{{include file = "member/m/head.tpl.html"}}
{{if $pageArray.info.email_status}}
<article class="g-user-bd">
    <div class="g-user-result bind-sec">
        <h1><i></i>绑定邮箱成功</h1>
        <p id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a></p>
    </div>
</article>
{{include file = "public/footer_m.tpl.html"}}
<script type="text/javascript">
var times = 3;
setInterval(function() {
    if (times > 0) {
        $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
    } else {
        window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
    }
    times--;
}, 1000);
</script>
{{else}}
<article class="g-user-bd">
    <header>
        <h1 class="g-user-title">绑定邮箱</h1>
    </header>
    <form id="bind-form" action="" method="post" onsubmit="return checkAll();" class="g-user-form">
    <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
        <div class="form-item">
            <input type="text" name='email' id='email' class="form-ipt" placeholder="请输入邮箱" value="{{$pageArray.info.email}}">
            <p class="form-tip-error" id="sendMsg" style="display: none;"></p>
        </div>
        <div class="form-item">
            <div class="form-vcode">
                <input type="tel" name="code" id="code" class="form-ipt" placeholder="请输入验证码">
                <a id="sendBtn" class="btn-getvcode" href="#">发送验证码</a>
                <a id="sendingBtn" class="btn-getvcode btn-disabled" style = "display :none">重新发送(60)</a>
            </div>
            <p class="form-tip-error" id="checkMsg" style="display: none;"><i></i>验证码错误</p>
        </div>
        <div class="btn-group">
            <a class="btn-confirm">下一步</a>
        </div>
    </form>
    <footer class="g-user-help">
        <p>如果您的邮箱无法收到验证码:
            <br>请检查邮箱地址是否正确。
            <br>请检查你的邮件垃圾箱。
            <br>请在邮箱设置中将“2345.com”添加到白名单后再点击。
            <br>若仍未收到,请稍后重新点击发送。</p>
    </footer>
</article>
{{include file = "public/footer_m.tpl.html"}}
<script type="text/javascript">
$("#email").focus();
var emailErr = 1;

function checkAndSend() {
    var email = $("#email").val();
    if (!email) {
        emailErr = 1;
        return false;
    }
    if (!/^[_\.0-9a-z-]+@([0-9a-z][0-9a-z-]+\.)+[a-z]{2,4}$/.test(email)) {
        emailErr = 1;
        $("#sendMsg").html('<i></i>请输入正确的邮箱地址！');
        $("#sendMsg").parent().addClass('form-item-error');
        $("#sendMsg").show();
        return false;
    }
    $.ajax({
        url: "/api/check/jsonp",
        data: 'type=email&value=' + email,
        async: false,
        dataType: 'jsonp',
        jsonp: 'callback',
        success: function(response) {
            err = response;
            if (err == 1) {
                emailErr = 1;
                $("#sendMsg").html('<i></i>请输入正确的邮箱地址！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            } else if (err == 2) {
                emailErr = 1;
                $("#sendMsg").html('<i></i>此邮箱已被绑定，请重新输入！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            } else if (err == 3) {
                emailErr = 1;
                $("#sendMsg").html('<i></i>此邮箱已被绑定，请重新输入！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            emailErr = 0;
            sendCode();
        },
        error: function(jqXHR, textStatus, errorThrown) {
            emailErr = 1;
        },
        timeout: 3000
    });
}
$('#sendBtn').on('click',function(e){
    checkAndSend();
    e.preventDefault();
})
function sendCode() {
    var email = $("#email").val();
    if (email == '') {
        $("#sendMsg").html('<i></i>请先输入邮箱地址！');
        $("#sendMsg").parent().addClass('form-item-error');
        $("#sendMsg").show();
        return false;
    }
    if (emailErr == 1) {
        return false;
    }
    $.post("/member/bindEmail/sendCode", {
        'email': AES.encrypt(email)
    }, function(data) {
        if (data == '300') {
            $("#sendMsg").html('<i></i>请输入正确的邮箱地址！');
            $("#sendMsg").parent().addClass('form-item-error');
            $("#sendMsg").show();
        } else if (data == '400') {
            $("#sendMsg").html('<i></i>验证的太频繁了，请稍后再试！');
            $("#sendMsg").parent().addClass('form-item-error');
            $("#sendMsg").show();
        } else if (data == '500') {
            $("#sendMsg").html('<i></i>服务器忙，请稍后再试！');
            $("#sendMsg").parent().addClass('form-item-error');
            $("#sendMsg").show();
        } else {
            var emailExp = email.split('@');
            if (emailExp[1] == 'gmail.com') {
                var emailHost = 'http://www.' + emailExp[1];
            } else {
                var emailHost = 'http://mail.' + emailExp[1];
            }
            $('#sendMsg').html('验证邮件已发送');
            $("#sendMsg").show();
        }
    });
    $("#sendBtn").text('重新发送');
    showSendMsg(60);
}

function showSendMsg(times) {
    $("#sendBtn").hide();
    $("#sendingBtn").text('重新发送（' + times + '）');
    $("#sendingBtn").show();
    if (times > 1) {
        times--;
        setTimeout(function() {
            showSendMsg(times);
        }, 1000);
    } else {
        $("#sendingBtn").hide();
        $("#sendBtn").show();
    }
}

function checkAll() {
    var email = $("#email").val();
    if (!email) {
        emailErr = 1;
        $("#sendMsg").html('<i></i>请先输入邮箱地址！');
        $("#sendMsg").parent().addClass('form-item-error');
        $("#sendMsg").show();
        return false;
    }
    if (!/^[_\.0-9a-z-]+@([0-9a-z][0-9a-z-]+\.)+[a-z]{2,4}$/.test(email)) {
        emailErr = 1;
        $("#sendMsg").html('<i></i>请输入正确的邮箱地址！');
        $("#sendMsg").parent().addClass('form-item-error');
        $("#sendMsg").show();
        return false;
    }
    else
    {
        emailErr = 0;
    }
    var codeErr = true;
    var email = $("#email").val();
    var code = $("#code").val();
    if (emailErr == 1) {
        return false;
    }
    if (!code) {
        $("#checkMsg").html('<i></i>请输入验证码！');
        $("#checkMsg").parent().addClass('form-item-error');
        $("#checkMsg").show();
        return false;
    }
    $.ajax({
        type: "POST",
        url: "/member/bindEmail/checkCode",
        async: false,
        data: "email=" + AES.encrypt(email) + "&code=" + code,
        success: function(data) {
            if (data == '200') {
                codeErr = false;
            } else if (data == '300') {
                $("#checkMsg").html('<i></i>请输入正确的邮箱地址！');
                $("#checkMsg").parent().addClass('form-item-error');
                $("#checkMsg").show();
            } else if (data == '400') {
                $("#checkMsg").html('<i></i>验证码错误！');
                $("#checkMsg").parent().addClass('form-item-error');
                $("#checkMsg").show();
            }
        }
    });
    if (codeErr === false) {
        $("#email").val(AES.encrypt(email));
    }
    return !codeErr;
}
$(".form-ipt").bind({
    focus: function() {
        $(this).closest(".form-item").removeClass("form-item-error");
        $(this).closest(".form-item").find(".form-tip-error").hide();
    },
});
$(".btn-confirm").on("click", function(e)
    {
        $("#bind-form").submit();
        e.preventDefault();
});
</script>
<script type="text/javascript">
document.body.addEventListener('touchstart', function() {});
</script>
{{/if}}
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
</body>

</html>
