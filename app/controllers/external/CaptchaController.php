<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/3
 * Time: 9:36
 */

class CaptchaController extends Controller
{
    /**
     * 生成图片验证码
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $this->startSession();
        $exId = \Common\Utils\Url::getStringParam('exId');
        $externalArr = \Service\Config\ExternalConfig::get($exId);
        if (empty($externalArr))
        {
            $this->codeResponse("400", "加载失败");
        }
        $captchaAction = loadAction("captcha");
        $code = $captchaAction->generate($exId);
        $_SESSION["captcha_code"] = implode("", $code);
        $captchaAction->show($exId);
    }
}
