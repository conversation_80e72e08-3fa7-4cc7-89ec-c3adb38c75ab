<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：AccountStat.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：07-13, 2016
 */

namespace Service\AccountStat\Consts;

class AccountStat
{

    public static function getAllType()
    {
        return self::$types;
    }

    public static function getAllClientType()
    {
        return self::$clientTypes;
    }

    public static function getAllAccountType()
    {
        return self::$accountType;
    }

    public static function getAllStatus()
    {
        return self::$status;
    }

    public static function getTypeName($type)
    {
        return self::$types[$type];
    }

    public static function getClientTypeName($type)
    {
        return self::$clientTypes[$type];
    }

    public static function getAccountTypeName($type)
    {
        return self::$accountType[$type];
    }

    public static function getStatusName($status)
    {
        return self::$status[$status];
    }

    /**
     * 根据MID获取来源
     * -
     * @param string $mid 用户中心项目唯一标识
     * @return mixed
     */
    public static function getSourceByMid($mid)
    {
        //todo 待 getSourceByMid 上线之后再来处理 v.2345.com  -> kan.2345.com
        $midSource = array(
            "XS" => "book.2345.com",
            "ZS" => "zhuzhou.2345.com",
            "YY" => "wan.2345.com",
            "CP" => "caipiao.2345.com",
            "YS" => "v.2345.com",
            "JF" => "jifen.yl234.com",
            "SJLM" => "shouji.2345.com",
            "XQ" => QKL_ZYXQ_DOMAIN,
            "SRF" => "my.pinyin.2345.com",
        );
        $name = $mid;
        if (isset($midSource[strtoupper($mid)]))
        {
            $name = $midSource[$mid];
        }
        return $name;
    }

    const TP_LOGIN = 1;
    const TP_REG = 2;
    const TP_AUTO_LOGIN = 3;

    const CTP_WEB = 1;
    const CTP_APP = 2;
    const CTP_XCX = 3;

    const AC_TP_PHONE = 1;
    const AC_TP_EMAIL = 2;
    const AC_TP_USERNAME = 3;
    const AC_TP_OAUTH_QQ = 4;
    const AC_TP_OAUTH_WEIXIN = 5;
    const AC_TP_OAUTH_CHINA_MOBILE = 6;
    const AC_TP_UNION_LOGIN = 7;
    const AC_TP_SHANYAN = 8;
    const AC_TP_OAUTH_XCX = 9;

    const STATUS_SUCC = 1;
    const STATUS_FAIL = 0;

    private static $types = array(
        self::TP_LOGIN => "登录",
        self::TP_REG => "注册",
    );

    private static $clientTypes = array(
        self::CTP_WEB => "网页端",
        self::CTP_APP => "移动端",
    );

    private static $accountType = array(
        self::AC_TP_PHONE => "手机",
        self::AC_TP_EMAIL => "邮箱",
        self::AC_TP_USERNAME => "用户名",
        self::AC_TP_OAUTH_QQ => "QQ登录",
        self::AC_TP_OAUTH_WEIXIN => "微信登录",
        self::AC_TP_OAUTH_CHINA_MOBILE => "中国移动统一认证",
        self::AC_TP_UNION_LOGIN => "联合登录",
        self::AC_TP_SHANYAN => "闪验",
    );

    private static $status = array(
        self::STATUS_SUCC => "成功",
        self::STATUS_FAIL => "失败",
    );

    private static $oauthTypes = array(
        "qq" => self::AC_TP_OAUTH_QQ,
        "weixin" => self::AC_TP_OAUTH_WEIXIN,
    );

    public static function getOauth($oauthType)
    {
        $name = $oauthType;
        if (isset(self::$oauthTypes[$oauthType]))
        {
            $name = self::$oauthTypes[$oauthType];
        }
        return $name;
    }

    public static function getDomainList($clientType)
    {
        $domainList = array();
        $domainGroups = self::getGroupList($clientType);
        foreach ($domainGroups as $group => $domain)
        {
            $domainList = array_merge($domainList, $domain);
        }
        return $domainList;
    }

    const GTP_LOGIN = 'login';
    const GTP_WANG_ZHI_DAO_HANG = 'www';
    const GTP_JIFEN = 'jifen';
    const GTP_BUY = 'buy';
    const GTP_BBS = 'bbs';
    const GTP_MY = 'my';
    const GTP_M = 'm';
    const GTP_BOOK = 'book';
    const GTP_DIANHUA = 'dianhua';
    const GTP_YUN = 'yun';
    const GTP_V = 'v';
    const GTP_IE = 'ie';
    const GTP_WAN = 'wan';
    const GTP_HUODONG = 'huodong';
    const GTP_CAIPIAO = 'caipiao';
    const GTP_ZHUSHOU = 'zhushou';
    const GTP_SHOU_YOU = 'shouyou';
    const GTP_DAY = 'day';
    const GTP_DAO_HANG_BROWSER = 'www_browser';
    //const GTP_QU_KAUI_LIAN = 'qkl';
    const GTP_ZY_XQ = 'xq'; //章鱼兴修 -- 这里只是一个定义,可随意命名
    const GTP_TEST = 'test';
    const GTP_SRF = 'SRF'; //输入法
    const GTP_GSQ = 'GSQ'; //购省钱
    const GTP_WNL = 'WNL';   //万年历
    const GTP_TIAN_QI_WANG = 'tqw';   //天气王
    const GTP_XING_QIU_LIAN_MENG = 'xqlm';   //兴修连梦
    const GTP_LE_YU_YOU_XI = 'lyyx';
    const GTP_AND_SEARCH = 'andsearch';
    const GTP_AND_BYHLS = 'andbyhls';
    const GTP_EXT_AND_BYHLS = 'ext_andbyhls';
    const GTP_AND_GWB = 'andgwb';
    const GTP_AND_HTT = "andhtt";
    const GTP_WPYDLM = "wpydlm";
    const GTP_TQW_FZ = "tqwfz"; //天气王辅助版2
    const GTP_AND_KXGY = "andkxgy"; //开心果园
    const GTP_AND_ZSDAIPAIBAO = "andzsdaipaibao"; //手机助手-带牌包
    const GTP_AND_ZSPUBAIBAO = "andzspubaibao"; //手机助手-普白包
    const GTP_AND_ZSSHEGNPAIBAO = "andzsshengpaibao"; //手机助手-深白包
    const GTP_AND_KXBBZ = "andkxbbz";   //开心步步赚
    const GTP_AND_ZONE2345APP = "ZONE2345APP";   //来说APP
    const GTP_AND_LSXCXAPP = "LSXCXAPP";  //来说小程序
    const GTP_AND_KXNCAPP = "KXNCAPP";  //开心农场
    const GTP_AND_ZONEMINIAPP = "ZONEMINIAPP";   //可映小程序
    const GTP_AND_KXYTAPP = "KXYTAPP"; //开心鱼塘
    const GTP_AND_JBZAPP = "JBZAPP";  //计步赚
    const GTP_AND_KYYJAPP = "KYYJAPP";  //可映影集小程序
    const GTP_AND_KXGYAPP = "2345KXGYAPP";  //2345开心果园
    const GTP_AND_PJZYQLDS = "PJZYQLDS";  //章鱼清理大师

    /**
     * 用于显示后台统计父标题
     * @var array $webGroupTypes
     * @static
     * @access
     */
    private static $webGroupTypes = array(
        'all' => '全部',
        self::GTP_WANG_ZHI_DAO_HANG => '网址导航',
        self::GTP_LOGIN => '用户中心',
        self::GTP_JIFEN => '积分',
        //self::GTP_BUY => '购物',
        self::GTP_BBS => '论坛',
        self::GTP_MY => 'MY',
        self::GTP_M => 'M2345',
        //self::GTP_BOOK => '阅读王',
        //self::GTP_DIANHUA => '电话',
        self::GTP_YUN => 'YUN',
        self::GTP_V => '影视',
        self::GTP_IE => '浏览器',
        self::GTP_WAN => '游戏',
        self::GTP_HUODONG => '活动',
        //self::GTP_CAIPIAO => '彩票',
        self::GTP_ZHUSHOU => '手机助手',
        //self::GTP_DAY => '每日一乐',
        self::GTP_ZY_XQ => '章鱼兴修',
        self::GTP_TEST => '测试',
        self::GTP_SRF => '输入法',
        self::GTP_GSQ => '购省钱',
        self::GTP_WPYDLM => '王牌移动联盟',
    );

    const SRC_IE_BROWSER = 'browser';
    const SRC_IE_BROWSER2 = 'browser2';
    const SRC_IE_BROWSER3 = 'chrome.2345.com';
    const SRC_IE_CHROME = 'chrome';

    /**
     * 用于后台统计子标题(例如: 章鱼兴修(xq) - [zyxq.2345.com; www.235.org])
     * @var array $webDomainGroups
     * @static
     * @access
     */
    private static $webDomainGroups = array(
        "all" => array(
            "all" => "all",
        ),

        self::GTP_LOGIN => array(
            'login.2345.com' => '帐号中心', //passport login my
            'api.2345.com' => 'api',
        ),

        self::GTP_WANG_ZHI_DAO_HANG => array(
            'www.2345.com' => '2345主站', //跳转至passport forward
            'gaoji.2345.com' => '高级版',
            'my.ie.2345.com' => '收藏夹',
        ),

        //self::GTP_DAY => array(
        //    'day.2345.com' => '每日一乐',
        //),

        // 拥有快捷登录和服务器api 方式
        self::GTP_JIFEN => array(
            'jifen.yl234.com' => '积分',
            'jifen.2345.com' => '积分',
            'm.jifen.2345.com' => '积分M版',
            'shouji.2345.cn' => '手机联盟(别名)',
        ),

        self::GTP_BBS => array(
            'bbs.2345.cn' => '论坛',
        ),

        // js登录框, m版跳转passport forward
        //self::GTP_BOOK => array(
        //    'book.2345.com' => '小说',
        //    'book.2345.com/m' => '小说M版',
        //),

        self::GTP_V => array(
            'kan.2345.com' => '影视kan',
            'dianying.2345.com' => '电影',
            'tv.2345.com' => '电视剧',
            'dongman.2345.com' => '动漫',
            //以下几个都是跳转到v.2345.com/m/ 登录的
            //'dianying.2345.com/m' => '电影M版',
            //'waptv.2345.com' => '电视剧M版',
            //'dongman.2345.com/m' => '动漫M版',
        ),

        // 王牌浏览器 5.0  服务器api
        self::GTP_IE => array(
            'ie.2345.com' => '加速浏览器',
        ),

        // 都是用game.2345.com
        self::GTP_WAN => array(
            'game.2345.com' => '页游',
            'xiaoyouxi.2345.com' => '小游戏',
        ),

        // 跳转到passport forward
        //self::GTP_CAIPIAO => array(
        //    'caipiao.2345.com' => '彩票',
        //),

        self::GTP_ZHUSHOU => array(
            'zhushou.2345.com' => '手机助手',
        ),
        self::GTP_ZY_XQ => array(
            'www.2345.org' => '章鱼兴修',
        ),
        self::GTP_SRF => array(
            'my.pinyin.2345.com' => '输入法',
        ),
        self::GTP_TEST => array(
            'passport.2345.org' => '测试专用',
        ),
        self::GTP_WPYDLM => array(
            'wpydlm' => '王牌移动联盟H5',
        ),
    );

    const GTP_XIAO_SHUO = "xiao_shuo";
    const GTP_MOBILE_BROWSER = "m_browser";
    const GTP_BROWSER_WANG_PAI = "wp_browser";
    const GTP_YING_SHI = "ying_shi";
    const GTP_KUAI_XIU = "kuai_xiu";
    const GTP_LIAN_MENG = "lian_meng";
    const GTP_PASSPORT = "passport";


    const SRC_IE_M_BROWSER = 'm_browser';

    /**
     * 用于后台移动端统计父标题
     * @var array $appGroupTypes
     * @static
     * @access
     */
    private static $appGroupTypes = array(
        'all' => '全部',
        'mobile' => '移动端',
        self::GTP_PASSPORT => '用户中心SDK',
        //self::GTP_XIAO_SHUO => "阅读王",
        self::GTP_MOBILE_BROWSER => "手机浏览器",
        self::GTP_BROWSER_WANG_PAI => "王牌浏览器",
        //self::GTP_YING_SHI => "影视",
        //self::GTP_KUAI_XIU => "快修王",
        self::GTP_LIAN_MENG => "手机联盟",
        self::GTP_ZHUSHOU => '手机助手',
        self::GTP_SHOU_YOU => '手游',
        self::GTP_DAO_HANG_BROWSER => '网址导航',
        self::GTP_ZY_XQ => '章鱼兴修',
        self::GTP_GSQ => '购省钱',
        self::GTP_WNL => '万年历',
        self::GTP_XING_QIU_LIAN_MENG => '兴修连梦',
        self::GTP_TIAN_QI_WANG => '天气王',
        self::GTP_LE_YU_YOU_XI => '游戏联运',
        self::GTP_AND_SEARCH => '搜索',
        self::GTP_AND_BYHLS => '捕鱼欢乐颂',
        self::GTP_EXT_AND_BYHLS => '捕鱼欢乐颂-外',
        self::GTP_AND_GWB => '购物宝',
        self::GTP_AND_HTT => '好头条',
        self::GTP_WPYDLM => '王牌移动联盟',
        self::GTP_TQW_FZ => '天气王辅助版2',
        self::GTP_AND_KXGY => '开心果园',
        self::GTP_AND_ZSDAIPAIBAO => '手机助手-带牌包',
        self::GTP_AND_ZSPUBAIBAO => '手机助手-普白包',
        self::GTP_AND_ZSSHEGNPAIBAO => '手机助手-深白包',
        self::GTP_AND_KXBBZ         => "开心步步赚",
        self::GTP_AND_ZONE2345APP   => "来说app",
        self::GTP_AND_LSXCXAPP      => "来说小程序",
        self::GTP_AND_KXNCAPP       => "开心农场",
        self::GTP_AND_ZONEMINIAPP => "可映小程序",
        self::GTP_AND_KXYTAPP => "开心鱼塘",
        self::GTP_AND_JBZAPP => "计步赚",
        self::GTP_AND_KYYJAPP => "可映影集小程序",
        self::GTP_AND_KXGYAPP => '2345开心果园',
        self::GTP_AND_PJZYQLDS => '章鱼清理大师',
    );

    /**
     * 用于后台移动端统计子标题(例如: 章鱼兴修 - [安卓,ios])
     * @var array $appDomainGroups
     * @static
     * @access
     */
    private static $appDomainGroups = array(
        "all" => array(
            "all" => "全部",
        ),
        "mobile" => array(
            "mobile" => "其他",
        ),
        self::GTP_PASSPORT => array(
            "iossdktest" => "iPhone",
            "andsdktest" => "安卓",
        ),
        self::GTP_MOBILE_BROWSER => array(
            "iosbs" => "iPhone",
            "andbs" => "安卓",
            'andllqoem' => "oem"
        ),
        self::GTP_DAO_HANG_BROWSER => array(
            "ioswzdh" => "iPhone",
            "anddhllq" => "安卓",
        ),
        self::GTP_BROWSER_WANG_PAI => array(
            "pcbs" => "PC",
        ),
        self::GTP_LIAN_MENG => array(
            "iossjlm" => "iPhone",
            "andsjlm" => "安卓",
        ),
        self::GTP_ZHUSHOU => array(
            "andsjzs" => "安卓",
        ),
        self::GTP_SHOU_YOU => array(
            "andsy" => "安卓",
        ),
        self::GTP_ZY_XQ => array(
            "andzyxq" => "安卓",
            "ioszyxq" => "IOS",
        ),
        self::GTP_GSQ => array(
            "andgsq" => "安卓",
            'iosgsq' => 'IOS',
        ),
        self::GTP_WNL => array(
            "andwnl" => "安卓",
        ),
        self::GTP_TIAN_QI_WANG => array(
            "andtqw" => "安卓",
            'iostqw' => 'IOS',
        ),
        self::GTP_XING_QIU_LIAN_MENG => array(
            "andxqlm" => "安卓",
            'iosxqlm' => 'IOS',
        ),
        self::GTP_LE_YU_YOU_XI => array(
            "andlyyx" => "安卓",
        ),
        self::GTP_AND_SEARCH => array(
            'andsearch' => '安卓'
        ),
        self::GTP_AND_BYHLS => array(
            'andbyhls' => '安卓'
        ),
        self::GTP_EXT_AND_BYHLS => array(
            'ext_andbyhls' => '安卓'
        ),
        self::GTP_AND_GWB => array(
            'andgwb' => '安卓'
        ),
        self::GTP_AND_HTT => array(
            'andhtt' => '安卓'
        ),
        self::GTP_WPYDLM => array(
            'andwpydlm' => '安卓',
            'ioswpydlm' => 'ios',
        ),
        self::GTP_TQW_FZ => array(
            "andtqwfz" => "安卓"
        ),
        self::GTP_AND_KXGY => array(
            "andkxgy" => "安卓"
        ),
        self::GTP_AND_ZSDAIPAIBAO => array(
            "andzsdaipaibao" => "安卓"
        ),
        self::GTP_AND_ZSPUBAIBAO => array(
            "andzspubaibao" => "安卓"
        ),
        self::GTP_AND_ZSSHEGNPAIBAO => array(
            "andzsshengpaibao" => "安卓"
        ),
        self::GTP_AND_KXBBZ       => array(
            'andkxbbz' => '安卓',
        ),
        self::GTP_AND_ZONE2345APP => array(
            'ZONE2345APP' => '安卓',
        ),
        self::GTP_AND_LSXCXAPP    => array(
            'LSXCXAPP' => '安卓',
        ),
        self::GTP_AND_KXNCAPP     => array(
            'KXNCAPP' => '安卓',
        ),
        self::GTP_AND_ZONEMINIAPP => array(
            'ZONEMINIAPP' => '安卓',
        ),
        self::GTP_AND_KXYTAPP => array(
            'KXYTAPP' => '安卓',
        ),
        self::GTP_AND_JBZAPP => array(
            'JBZAPP' => '安卓',
        ),
        self::GTP_AND_KYYJAPP => array(
            'KYYJAPP' => '安卓',
        ),
        self::GTP_AND_KXGYAPP => array(
            '2345KXGYAPP' => '安卓',
        ),
        self::GTP_AND_PJZYQLDS => array(
            'PJZYQLDS' => '安卓',
        ),
    );

    public static function getGroupList($clientType)
    {
        $domainGroup = array();
        if ($clientType == self::CTP_WEB)
        {
            $domains = self::$webDomainGroups;
            foreach ($domains as $key => $val)
            {
                $name = self::$webGroupTypes[$key];
                $domainGroup[$name] = $val;
            }
        }
        else
        {
            $domains = self::$appDomainGroups;
            foreach ($domains as $key => $val)
            {
                $name = self::$appGroupTypes[$key];
                $domainGroup[$name] = $val;
            }
        }
        return $domainGroup;
    }

    public static function getOriginGroupList($clientType)
    {
        if ($clientType == self::CTP_WEB)
        {
            $domains = self::$webDomainGroups;
        }
        else
        {
            $domains = self::$appDomainGroups;
        }
        return $domains;
    }

    public static function getGroupType($clientType, $source)
    {
        $groupType = "";
        $domainGroup = self::getOriginGroupList($clientType);
        foreach ($domainGroup as $key => $val)
        {
            if (array_key_exists($source, $val))
            {
                $groupType = $key;
                break;
            }
        }
        return $groupType;
    }

    public static function getGroupName($clientType, $groupType)
    {
        $name = "";
        if ($clientType == self::CTP_WEB)
        {
            $domainGroupTypes = self::$webGroupTypes;
        }
        else
        {
            $domainGroupTypes = self::$appGroupTypes;
        }
        if (isset($domainGroupTypes[$groupType]))
        {
            $name = $domainGroupTypes[$groupType];
        }
        return $name;
    }


}
