<?php

/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：AccountStatController.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：01-12, 2017
 */
class AccountStatController extends Controller
{

    /**
     * AccountStatController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            $this->codeResponse(- 401, "", "限制IP，无访问权限！");
        }
    }

    /**
     * 每日数据统计
     *
     * @return bool
     */
    public function actionDailyReg()
    {
        $domain = $_POST['domain'];
        $group = $_POST['group'];
        $startTime = date("Y-m-d 00:00:01", strtotime($_POST['startTime']));
        $endTime = date("Y-m-d 23:59:59", strtotime($_POST['endTime']));
        $data = array();
        if (empty($domain))
        {
            $this->codeResponse(- 1, $data, "domain参数错误, 请检查");

            return false;
        }
        if (empty($group))
        {
            $this->codeResponse(- 1, $data, "group参数错误, 请检查");

            return false;
        }
        if (strtotime($endTime) - strtotime($startTime) > ********)//86400 * 366)
        {
            $this->codeResponse(- 2, $data, "时间跨度大于366天, 请检查");

            return false;
        }

        // todo 只为 game.2345.com 提供数据
        if ($group != "game.2345.com")
        {
            $this->codeResponse(- 3, $data, "获取数据失败, 请重试");

            return true;
        }

        //todo
        $groupType = \Service\AccountStat\Consts\AccountStat::GTP_WAN;

        $accountStat = new \Service\AccountStat\Models\AccountStat();
        $res = $accountStat->getRegAccountStatSummaryByGroupType($groupType, $startTime, $endTime);

        foreach ($res as $key => $val)
        {
            $cTime = date("Y-m-d", strtotime($val["ctime"]));
            $amount = $val["amount"];
            $data[$cTime] = intval($amount);
        }

        if (!empty($data))
        {
            $this->codeResponse(1, $data, "");

            return true;
        }
        $this->codeResponse(- 3, $data, "获取数据失败, 请重试");

        return false;
    }

}
