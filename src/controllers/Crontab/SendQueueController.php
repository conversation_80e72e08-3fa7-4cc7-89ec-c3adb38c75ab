<?php

namespace Crontab;

use Controller;
use Octopus\PdoEx;

class SendQueueController extends Controller
{
    private $pdo;
    private $redis;

    /**
     * SmsCronController constructor.
     */
    public function __construct()
    {
        $dbConfig = \Config::get("database");
        $this->redis = \RedisAction::connect();
        $this->pdo = PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);
        parent::__construct();
    }

    /**
     * 进程关闭方法：http://smsp.2345.net/Admin/TestData/SetRedis?cmd=del&val[0]=sms:queue:run
     * 进程启动方法：http://smsp.2345.net/Admin/TestData/SetRedis?cmd=set&val[0]=sms:queue:run&val[1]=2
     * cron脚本入口
     * @return void
     */
    public function actionRun($machineID)
    {
        if (!isset($machineID))
        {
            $machineID = 0;
        }
        $myPid = getmypid();
        //每100条积压短信开一个进程
        $itemPerProcess = 100;
        //当前已有的进程列表
        $existPids = $this->redis->keys('sms:queue:pid:*');
        //当前积压短信数量
        $queueLen = $this->redis->lLen('sms:queue:send');
        //基础进程数量
        $basicProcess = $this->redis->get('sms:queue:run');
        //此刻本进程还未在redis注册
        if (count($existPids) > 100 || count($existPids) > floor($queueLen / $itemPerProcess) + $basicProcess - 1)
        {
            exit;
        }
        $waitTimeStamp = null;
        while (1)
        {
            if (!$this->redis->exists('sms:queue:run'))
            {
                $this->redis->del("sms:queue:pid:{$myPid}:*");
                exit;
            }
            $this->redis->setex("sms:queue:pid:{$myPid}:{$machineID}", 60, $myPid);
            $data = unserialize($this->redis->rPop('sms:queue:send'));
            if ($data)
            {
                $waitTimeStamp = time();
                switch ($data[1]['send_id'])
                {
                    case 11:
                        $this->normalSend($data);
                        break;
                    default:
                        break;
                }
            }
            $basicProcess = $this->redis->get('sms:queue:run');
            $existPids = $this->redis->keys('sms:queue:pid:*');
            $queueLen = $this->redis->lLen('sms:queue:send');
            //自动收缩，此时计入本进程
            if (count($existPids) > floor($queueLen / $itemPerProcess) + $basicProcess)
            {
                $existPidNums = array_map(function ($v) {
                    $pidArray = explode(':', $v);
                    return (int)$pidArray[3];
                }, $existPids);
                if (min($existPidNums) === $myPid)
                {
                    $this->redis->del("sms:queue:pid:{$myPid}:{$machineID}");
                    exit;
                }
                unset($existPidNums);
            }
            if ($queueLen === 0)
            {
                if ($waitTimeStamp !== null && time() - $waitTimeStamp > 25)
                {
                    $this->pdo->closeInstance();
                    $waitTimeStamp = null;
                }
                sleep(5);
            }
        }
    }

    private function normalSend($data)
    {
        $requiredParamList = $data[0];
        $additionalParamList = $data[1];
        $arrParam = array_merge($requiredParamList, $additionalParamList);
        if ($arrParam['mid'] === 'TEST')
        {
            usleep(200 * 1000);//200ms
            return true;
        }
        $apiSmsSenderAction = \ApiSmsSenderAction::getInstance();
        $apiSmsSenderAction->setSubmitData($arrParam);
        $isLegal = $apiSmsSenderAction->checkParams($data[0]);
        $getRedisAction = \GetRedisAction::getInstance();
        if ($isLegal === true)
        {
            $getRedisAction->incrMonitor(count($requiredParamList['phone']), 'queueSend', $requiredParamList['smsType'], $requiredParamList['pid']);
            $insertData = array();
            $sessionId = uniqid();
            $column = array(
                "phone",
                "text",
                "sessionid",
                "type",
                "send_time",
                "send_status",
                "send_response",
                "passid",
                "pid",
                'business_id',
                'client_ip',
                'server_ip',
            );
            foreach ($requiredParamList['phone'] as $phone)
            {
                $insertData[$phone] = array(
                    $phone,
                    $requiredParamList['msg'],
                    $sessionId,
                    $requiredParamList['smsType'],
                    $additionalParamList['sendTime'],
                    -22,
                    '(队列)等待发送',
                    $additionalParamList['passid'],
                    $requiredParamList['pid'],
                    $additionalParamList['positionId'],
                    $requiredParamList['clientIp'],
                    $additionalParamList['serverIp'],
                );
            }
            $smsModel = \SmsModel::getInstance();
            $phoneLogIds = $smsModel->setSmsLogs($insertData, $column);
            if ($phoneLogIds)
            {
                $arrParam['phoneLogIds'] = $phoneLogIds;
                $ret = $apiSmsSenderAction->apiSend($arrParam);
                return $this->ticket2logid($data['ticketId'], $ret);
            }
            else
            {
                return $this->ticket2logid($data['ticketId'], array('status' => -21, 'msg' => \SmsAction::$errStr[-21]));
            }
        }
        else
        {
            $errorData = array(
                'status' => $isLegal['status'],
                'msg'    => $isLegal['msg'],
            );
            return $this->ticket2logid($data['ticketId'], $errorData);
        }
    }

    public function ticket2logid($ticketId, $result)
    {
        $this->redis->setex('sms:queue:ticket:' . $ticketId, 86400 * 2, json_encode(\EncodingAction::transcoding($result, 'utf-8')));
        return $result;
    }
}
