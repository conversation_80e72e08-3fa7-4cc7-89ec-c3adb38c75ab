<html xmlns:o="urn:schemas-microsoft-com:office:office"  xmlns:x="urn:schemas-microsoft-com:office:excel"  xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta http-equiv="expires" content="Mon, 06 Jan 1999 00:00:01 GMT">
    <meta http-equiv=Content-Type content="text/html; charset=gb2312">
    <!--[if gte mso 9]><xml> <x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name></x:Name><x:WorksheetOptions><x:DisplayGridlines/>
</x:WorksheetOptions></x:ExcelWorksheet> </x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
</head>
<body>
<table border="1" cellspacing="0" cellpadding="0" width="100%">
    <tr style="text-align: center;">
        <th width="10%" rowspan="2">日期</th>
        {{foreach item=domains key=groupKey from=$pageArray.domainGroups name=domainGroups}}
        <th{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:greenyellow"{{/if}} colspan="{{$domains|@count}}">{{$groupKey}}</th>
        {{/foreach}}
    </tr>
    <tr style="text-align: center;">
        {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
        {{foreach item=domain_name key=domain from=$domains}}
        <th{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:greenyellow"{{/if}}><span title="{{$domain}}">{{if $domain_name}}{{$domain_name}}{{else}}{{$domain}}{{/if}}</span><input type="button" onclick="setSeries([{name: '{{$pageArray.domains.$domain}}正常登录', color: '#0000FF', data: [{{$pageArray.charts.$domain.common_num}}]}, {name: '{{$pageArray.domains.$domain}}异常登录', color: '#FF0000', data: [{{$pageArray.charts.$domain.uncommon_num}}]}]);" value="图表"/></th>
        {{/foreach}}
        {{/foreach}}
    </tr>
    {{foreach item=item key=date from=$pageArray.data}}
    <tr>
        <td rowspan="2">{{$date}}</td>
        {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
        {{foreach item=domain_name key=domain from=$domains}}
        <td{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:greenyellow"{{/if}}><span style="color:blue;">{{$item.$domain.common_num}}</span></td>
        {{/foreach}}
        {{/foreach}}
    </tr>
    <tr>
        {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
        {{foreach item=domain_name key=domain from=$domains}}
        <td{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:greenyellow"{{/if}}><span style="color:red;">{{$item.$domain.uncommon_num}}</span></td>
        {{/foreach}}
        {{/foreach}}
    </tr>
    {{/foreach}}
</table>
</body>
</html>