<?php

/**
 * Copyright (c) 2015,上海二三四五网络科技股份有限公司
 * 文件名称：PhoneController.php
 * 摘    要：客户端手机号接口
 * 作    者：张小虎
 * 修改日期：2015.03.13
 */

use \Common\Utils\Url;

class PhoneController extends Controller
{

    private $version = 1;

    /**
     * PhoneController constructor.
     * -
     * @return void
     */
    public function __construct()
    {
        $version = intval($_GET['v']);
        if ($version > 0)
        {
            $this->version = $version;
        }
        parent::__construct();
    }

    /**
     * 记录输入手机号验证码错误日志
     * -
     * @param string $mid mid
     * @param int $phone 手机号码
     * @param int $code code
     * @return void
     */
    private function checkSmslog($mid, $phone, $code)
    {
        if (!in_array($mid, ['andbs', 'andxqlm']))
        {
            return;
        }
        $checkCode = md5($phone . $code);
        if ($this->version > 1)
        {
            $sid = session_id();
            if (!$sid)
            {
                session_start();
                $sid = session_id();
            }
            $checkCode = md5($sid . $checkCode);
        }
        $codeKey = "PL:code:$mid:$phone";
        $redis = RedisEx::getInstance();
        $storeCode = $redis->get($codeKey);
        $msg = 'mid:' . $mid . ' phone:' . $phone . ' code:' . $code . ' version:' . $this->version . ' storeCode:' . $storeCode . ' checkCode:' . $checkCode;
        xLog('phoneCodeCheckLog', 'phoneCodeCheckLog', $msg, 'info');
    }

    /**
     * 请求log
     * -
     * @return void;
     */
    private function requestLog()
    {
        $scheme = $_SERVER['REQUEST_SCHEME']; //协议
        $domain = $_SERVER['HTTP_HOST']; //域名/主机
        $requestUri = $_SERVER['REQUEST_URI']; //请求参数
        $currentUrl = $scheme . "://" . $domain . $requestUri;
        $data = array_merge($_POST, $_GET);
        $msg = $currentUrl . ' ' . urldecode(http_build_query($data));
        xLog('phoneRequestLog', 'phoneRequestLog', $msg, 'info');
    }

    /**
     * 根据mid获取短信类型
     * -
     * @param string $mid mid
     * @return int
     */
    private function getSmsTypeBymid($mid)
    {
        switch ($mid)
        {
            case 'andxqlm':
                //兴修连梦
                $smsType = 178;
                break;
            case 'andwnl':
                //万年历
                $smsType = 179;
                break;
            case 'andgsq':
                //购省钱
                $smsType = 180;
                break;
            case 'andhtt':
                //好头条
                $smsType = '234';
                break;
            default:
                $smsType = 7;
        }
        return $smsType;
    }

    /**
     * 发送登录验证码,成功发送5次显示图形验证码
     * -
     * @return string
     */
    public function actionSendLoginPhoneCode()
    {
        session_start();
        $aes = new \Service\Encryption\Aes\AesManager();
        $data = \Common\Utils\Url::getStringParam('data');
        if (empty($data))
        {
            $this->codeResponse(406, "", "参数不能为空");
        }
        $aesDecodeData = $aes->aes128cbcHexDecrypt($data);
        if (empty($aesDecodeData))
        {
            $this->codeResponse(407, "", "校验失败");
        }
        loadAction('Encoding');
        $aesDecodeData = EncodingAction::transcoding(json_decode($aesDecodeData, true), 'gbk');
        //校验必填字段及签名
        $necessaryField = array(
            'mid',
            'phone',
            'timestamp',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );
        foreach ($necessaryField as $necessaryInfo)
        {
            $_REQUEST[$necessaryInfo] = $aesDecodeData[$necessaryInfo];
        }
        list($mid ,$phone) = $this->getParamAndAutoDeny($necessaryField);
        $checkCode = $aesDecodeData['checkCode'];
        $ShowCaptchaAction = loadAction('ShowCaptcha');
        $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($mid, $phone, $checkCode);
        if ($showCaptcha['code'] != 200)
        {
            return $this->echoResponse($showCaptcha);
        }
        $sessionVerify = $this->version > 1 ? true : false;
        $phoneAction = loadAction("phone");
        $smsFilterConfig = [
            'mNum' => 15,
            'hNum' => 15,
            'dNum' => 50
        ];
        $smsType = $this->getSmsTypeBymid($mid);
        $retCode = $phoneAction->sendLoginCode($mid, $phone, $smsType, 453, $sessionVerify, $smsFilterConfig);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 400:
                $this->codeResponse(4100, "", "发送频繁，请稍后再试！");
                break;
            case 403:
                $this->codeResponse(4103, "", "该手机号禁止使用！");
                break;
            case 404:
                $this->codeResponse(4104, "", "该手机号不存在！");
                break;
            case 500:
            default:
                $this->codeResponse(5000, "", "服务器忙，请稍后再试！");
                break;
        }
    }

    /**
     * 手机号短信登录  配合actionSendLoginPhoneCode  发送登录验证码,成功发送5次显示图形验证码 使用
     * -
     * @return string
     */
    public function actionLoginByPhone()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];
        $verifyCode = $_POST['verify_code'];
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->showError300();
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        $phoneAction = loadAction("phone");

        $sessionVerify = $this->version > 1 ? true : false;
        //*********** 章鱼兴修   ***********购省钱APP iOS
        if (in_array($phone, array('***********', '***********', '***********', '18200432762')) && $verifyCode == '123456')
        {
            //章鱼兴修IOS过审指定手机号,6月初删掉,删除前联系 王小威、汪丽娜、李志
            //下面一行纯粹为了代码不报警
            $sessionVerify = $this->version > 1 ? true : false;
        }
        else
        {
            $timeKey = "PL:time:$mid:$phone";
            $codeKey = "PL:code:$mid:$phone";
            $redis = RedisEx::getInstance();
            $checkTime = $redis->get($timeKey);
            if ($checkTime > 2)
            {
                $redis->del($codeKey);
                $this->codeResponse(4110, '', "短信验证码已失效，请重新获取");
            }
            if (!$phoneAction->checkLoginCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify, true))
            {
                $this->checkSmslog($mid, $phone, $verifyCode);
                $this->showError303();
            }
        }
        $this->requestLog();
        $memberModel = loadModel('member');
        $passid = $memberModel->getPassidByPhone($phone);
        if (!$passid)
        {
            //手机号,未绑定手机号, 但存在用户名
            $passid = $memberModel->getPassidByUsername($phone);
        }

        $user = [];
        if (!empty($passid))
        {
            $user = $memberModel->read($passid);
            $isProhibitLogin = $phoneAction->prohibitUsernameAsPhoneDiffBindPhoneLogin($user, $phone);
            if ($isProhibitLogin)
            {
                $this->codeResponse(400, '', "登录失败！");
            }
        }

        //收集注册登录信息
        $source = Url::getStringParam("mid", "mobile", Url::POST);
        $st = !!$passid;
        $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        \Service\AccountStat\AccountStat::collect($type, $clientType, $phone, $st, $source);

        if ($passid)
        {
//            $user = $memberModel->read($passid);

            $phoneAction = loadAction("phone");
            $newUserName = $phoneAction->repairUsernameWithPhoneNum($phone, $user, "clientapi");
            if ($newUserName)
            {
                $username = $newUserName;
            }
            else
            {
                $username = $user['username'];
            }

            $user["m_uid"] = unserialize($user['m_uid']);
            $uid = $user['m_uid']['1'];
            $userMod = $user['gid'] % 100 == 0 ? $user['gid'] : 0;
            $loginAction = loadAction("login");
            $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
            $this->codeResponse(200, ['I' => $cookie['I']], "登录成功！");
        }
        else
        {
            $this->codeResponse(400, '', "登录失败！");
        }
    }

    /**
     * 注册短信验证码
     * -
     * @param string $method quick 是否是快捷注册
     * @return string
     */
    public function actionSendRegPhoneCode($method)
    {
        session_start();
        $aes = new \Service\Encryption\Aes\AesManager();
        $data = \Common\Utils\Url::getStringParam('data');
        if (empty($data))
        {
            $this->codeResponse(406, "", "参数不能为空");
        }
        $aesDecodeData = $aes->aes128cbcHexDecrypt($data);
        if (empty($aesDecodeData))
        {
            $this->codeResponse(407, "", "校验失败");
        }
        loadAction('Encoding');
        $aesDecodeData = EncodingAction::transcoding(json_decode($aesDecodeData, true), 'gbk');
        //校验必填字段及签名
        $necessaryField = array(
            'mid',
            'phone',
            'timestamp',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );
        foreach ($necessaryField as $necessaryInfo)
        {
            $_REQUEST[$necessaryInfo] = $aesDecodeData[$necessaryInfo];
        }
        list($mid ,$phone) = $this->getParamAndAutoDeny($necessaryField);
        $checkCode = $aesDecodeData['checkCode'];

        $ShowCaptchaAction = loadAction('ShowCaptcha');
        $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($mid, $phone, $checkCode);
        if ($showCaptcha['code'] != 200)
        {
            return $this->echoResponse($showCaptcha);
        }

        $phoneAction = loadAction("phone");
        $positionId = ($method == "quick") ? 454 : 163;
        $sessionVerify = $this->version > 1 ? true : false;

        $smsFilterConfig = [
            'mNum' => 15,
            'hNum' => 15,
            'dNum' => 50
        ];
        $smsType = $this->getSmsTypeBymid($mid);
        $retCode = $phoneAction->sendRegCode($mid, $phone, $smsType, $positionId, $sessionVerify, $smsFilterConfig);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 301:
                $this->showError301();
                break;
            case 302:
                $this->showError302();
                break;
            case 400:
                $this->codeResponse(4100, "", "发送频繁，请稍后再试！");
                break;
            case 500:
            default:
                $this->codeResponse(5000, "", "服务器忙，请稍后再试！");
                break;
        }
    }

    /**
     * 手机号短信注册  配合actionSendRegPhoneCode  发送登录验证码,成功发送5次显示图形验证码 使用
     * -
     * @return string
     */
    public function actionQuickRegByPhone()
    {
        $phone = \Common\Utils\Url::getStringParam("phone");
        $mid = \Common\Utils\Url::getStringParam("mid");
        $verifyCode = \Common\Utils\Url::getStringParam("verify_code");

        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(300, "", "请确认您的手机号是否填写正确!");
        }

        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(501, "", "项目标识不正确!");
        }

        $sessionVerify = $this->version > 1 ? true : false;

        $phoneAction = loadAction("phone");
        $timeKey = "PL:time:$mid:$phone";
        $codeKey = "PL:code:$mid:$phone";
        $redis = RedisEx::getInstance();
        $checkTime = $redis->get($timeKey);
        if ($checkTime > 2)
        {
            $redis->del($codeKey);
            $this->codeResponse(4110, '', "验证码已失效");
        }
        if (!$phoneAction->checkRegCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify, true))
        {
            $this->checkSmslog($mid, $phone, $verifyCode);
            $this->codeResponse(400, "", "短信验证失败!");
        }
        $this->requestLog();
        $userClientIp = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $pwd = mt_rand() . time();
        $password = substr(md5($pwd . MD5KEY), 0, 16);

        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        $regAction = loadAction('reg');

        $gid = 300;
        // 客户端 注册
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        $source = \Common\Utils\Url::getStringParam("mid", "mobile", \Common\Utils\Url::POST);
        $result = $regAction->regPhone($phone, $password, $pwdStrength, $userClientIp, "login.2345.com", $gid, $source, $clientType);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            $this->codeResponse(403, "", 'errorCode:' . $msgs[$states[0]][$states[1]]);
        }
        elseif ($states[0] == 300)
        {
            $this->codeResponse($states[0] + $states[1], "", $msgs[$states[0]][$states[1]]);
        }
        elseif ($states[0] == 200)
        {
            $cookie = $result[1]['cookie'];
            $data = array(
                'I' => $cookie['I'],
            );
            $this->codeResponse(200, $data, "注册成功！");
        }
        else
        {
            $this->codeResponse(500, "", "禁止注册！");
        }
    }

    /**
     * 发送注册验证码
     * -
     * @param string $method 发送短信接口类型
     * @return string
     */
    public function actionSendRegCode($method = "")
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        session_start();
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];

        $midArr = Config::get("mid");
        $lockPro = Config::get("lockpro");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        if ($lockPro && array_key_exists($mid, $lockPro))
        {
            if ((empty($_SESSION['captcha_phone']) || $_SESSION['captcha_phone'] != $phone) && (empty($_POST['captcha_code']) || empty($_SESSION['captcha_code']) || strtoupper($_POST['captcha_code']) != $_SESSION['captcha_code']))
            {
                unset($_SESSION['captcha_phone']);
                unset($_SESSION['captcha_code']);
                $this->showError304();
            }
            unset($_SESSION['captcha_code']);
        }
        $phoneAction = loadAction("phone");
        $positionId = ($method == "quick") ? 199 : 163;

        $sessionVerify = $this->version > 1 ? true : false;

        $retCode = $phoneAction->sendRegCode($mid, $phone, 8, $positionId, $sessionVerify);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 301:
                $this->showError301();
                break;
            case 302:
                $this->showError302();
                break;
            case 400:
                $this->showError400();
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * actionWebSendRegCode
     * 跟上面的区别:1.支持jsonp;2.GET POST均可;3.上面那个判断图片验证码有bug
     * 所以可用于客户端内嵌登录网页的形式(比如输入法、浏览器等)
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionWebSendRegCode()
    {
        session_start();
        $mid = Url::getStringParam('mid');
        $phone = Url::getStringParam('phone');
        $checkCode = Url::getStringParam('check_code');

        $midArr = Config::get("mid");
        $lockPro = Config::get("lockpro");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(501, "", "项目标识不正确！");
        }

        $loginAction = loadAction('login');
        $isCheck = $loginAction->IsCheckCaptcha($mid, $phone, true);
        if ($isCheck && $lockPro && array_key_exists($mid, $lockPro))
        {
            if (empty($phone) || empty($_SESSION['captcha_phone']) || empty($_SESSION['captcha_code']) || empty($checkCode))
            {
                unset($_SESSION['captcha_phone']);
                unset($_SESSION['captcha_code']);
                $this->codeResponse(304, "", "请输入正确的图片验证码！");
            }
            if ($_SESSION['captcha_phone'] != $phone || strtoupper($checkCode) != $_SESSION['captcha_code'])
            {
                unset($_SESSION['captcha_phone']);
                unset($_SESSION['captcha_code']);
                $this->codeResponse(304, "", "请输入正确的图片验证码！");
            }
            unset($_SESSION['captcha_code']);
        }
        $phoneAction = loadAction("phone");
        $positionId = 199;
        $sessionVerify = $this->version > 1 ? true : false;
        $retCode = $phoneAction->sendRegCode($mid, $phone, 8, $positionId, $sessionVerify);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->codeResponse(200, "", "发送成功！");
                break;
            case 300:
                $this->codeResponse(300, "", "请输入正确的手机号码！");
                break;
            case 301:
                $this->codeResponse(301, "", "该手机号已被其他用户占用！");
                break;
            case 302:
                $this->codeResponse(302, "", "该手机号已被其他用户绑定！");
                break;
            case 400:
                $this->codeResponse(400, "", "发送频繁，请稍后再试！");
                break;
            case 500:
            default:
                $this->codeResponse(500, "", "服务器忙，请稍后再试！");
                break;
        }
    }

    /**
     * 验证注册验证码
     */
    public function actionCheckRegCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];
        $verifyCode = $_POST['verify_code'];
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->showError300();
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }

        $sessionVerify = $this->version > 1 ? true : false;

        $phoneAction = loadAction("phone");
        if ($phoneAction->checkRegCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify))
        {
            $this->show200('验证成功！');
        }
        else
        {
            die(json_encode(array(
                'code' => 400,
                'data' => '',
                'msg' => mb_convert_encoding('验证失败！', 'UTF-8', 'GBK')
            )));
        }
    }

    /**
     * 发送登录验证码
     */
    public function actionSendLoginCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        session_start();
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];
        $midArr = Config::get("mid");
        $lockPro = Config::get("lockpro");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        if ($lockPro && array_key_exists($mid, $lockPro))
        {
            if ((empty($_SESSION['captcha_phone']) || $_SESSION['captcha_phone'] != $phone) && (empty($_POST['captcha_code']) || empty($_SESSION['captcha_code']) || strtoupper($_POST['captcha_code']) !== $_SESSION['captcha_code']))
            {
                unset($_SESSION['captcha_phone']);
                unset($_SESSION['captcha_code']);
                $this->showError304();
            }
            unset($_SESSION['captcha_code']);
        }

        $sessionVerify = $this->version > 1 ? true : false;

        $phoneAction = loadAction("phone");
        $retCode = $phoneAction->sendLoginCode($mid, $phone, 8, 162, $sessionVerify);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 400:
                $this->showError400();
                break;
            case 403:
                $this->showError403();
                break;
            case 404:
                die(json_encode(array(
                    'code' => 404,
                    'data' => '',
                    'msg' => mb_convert_encoding('该手机号不存在！', 'UTF-8', 'GBK')
                )));
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 发送登录验证码
     * 对于内嵌网页的客户端登录,就用这个,其他函数可以在更新SDK的时候直接引用这个action
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionWebSendLoginCode()
    {
        session_start();
        $mid = \Common\Utils\Url::getStringParam('mid');
        $phone = \Common\Utils\Url::getStringParam('phone');
        $checkCode = Url::getStringParam('check_code');
        $midArr = Config::get("mid");
        $lockPro = Config::get("lockpro");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(501, "", "项目标识不正确!");
        }

        $loginAction = loadAction('login');
        $isCheck = $loginAction->IsCheckCaptcha($mid, $phone, true);
        if ($isCheck && $lockPro && array_key_exists($mid, $lockPro))
        {
            if (empty($phone) || empty($_SESSION['captcha_phone']) || empty($_SESSION['captcha_code']) || empty($checkCode))
            {
                unset($_SESSION['captcha_phone']);
                unset($_SESSION['captcha_code']);
                $this->codeResponse(304, "", "请输入正确的图片验证码!");
            }
            if ($_SESSION['captcha_phone'] != $phone || strtoupper($checkCode) != $_SESSION['captcha_code'])
            {
                unset($_SESSION['captcha_phone']);
                unset($_SESSION['captcha_code']);
                $this->codeResponse(304, "", "请输入正确的图片验证码!");
            }
            unset($_SESSION['captcha_code']);
        }

        $sessionVerify = $this->version > 1 ? true : false;
        $positionId = 200;
        $phoneAction = loadAction("phone");
        $retCode = $phoneAction->sendLoginCode($mid, $phone, 8, $positionId, $sessionVerify);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->codeResponse(200, "", "发送成功");
                break;
            case 300:
                $this->codeResponse(300, "", "请输入正确的手机号码");
                break;
            case 400:
                $this->codeResponse(400, "", "发送频繁，请稍后再试！");
                break;
            case 403:
                $this->codeResponse(403, "", "该手机号禁止使用！");
                break;
            case 404:
                $this->codeResponse(404, "", "该手机号不存在");
                break;
            case 500:
            default:
                $this->codeResponse(500, "", "服务器忙，请稍后再试！");
                break;
        }
    }

    /**
     * 发送验证用验证码
     */
    public function actionSendVerifyCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo)
        {
            $this->showError401();
        }
        $passid = $authInfo['i'];
        $phoneAction = loadAction("phone");
        $retCode = $phoneAction->sendVerifyCode($mid, $passid, 5, 157);
        switch ($retCode)
        {
            case 200:
                $this->show200();
                break;
            case 400:
                $this->showError400();
                break;
            case 404:
                $this->showError404();
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 验证短信验证码
     */
    public function actionCheckVerifyCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $verifyCode = $_POST['verify_code'];
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo)
        {
            $this->showError401();
        }
        $passid = $authInfo['i'];
        $phoneAction = loadAction("phone");
        $phoneInfo = $phoneAction->getComplex($passid);
        if (!$phoneInfo)
        {
            $this->showError404();
        }
        $phone = $phoneInfo['phone'];
        if ($phoneAction->checkVerifyCode($mid, $passid, md5($phone . $verifyCode)))
        {
            $this->show200('验证成功！');
        }
        else
        {
            die(json_encode(array(
                'code' => 400,
                'data' => '',
                'msg' => mb_convert_encoding('验证失败！', 'UTF-8', 'GBK')
            )));
        }
    }

    /**
     * 发送绑定验证码
     */
    public function actionSendBindCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo)
        {
            $this->showError401();
        }
        $passid = $authInfo['i'];
        $phoneAction = loadAction("phone");
        $retCode = $phoneAction->sendBindCode($mid, $passid, $phone, 5, 151);
        switch ($retCode)
        {
            case 200:
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 301:
                $this->showError301();
                break;
            case 302:
                $this->showError302();
                break;
            case 400:
                $this->showError400();
                break;
            case 402:
                $this->showError402();
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 发送修改验证码
     */
    public function actionSendEditCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];
        $verifyCode = $_POST['verify_code'];
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo)
        {
            $this->showError401();
        }
        $passid = $authInfo['i'];
        $phoneAction = loadAction("phone");
        $retCode = $phoneAction->sendEditCode($mid, $passid, $phone, $verifyCode, 5, 160);
        switch ($retCode)
        {
            case 200:
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 301:
                $this->showError301();
                break;
            case 302:
                $this->showError302();
                break;
            case 303:
                $this->showError303();
                break;
            case 400:
                $this->showError400();
                break;
            case 404:
                $this->showError404();
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 发送通用验证码
     */
    public function actionSendCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $action = $_POST['action'];
        switch ($action)
        {
            case 'verify':
                $this->actionSendVerifyCode();
                break;
            case 'bind':
                $this->actionSendBindCode();
                break;
            case 'edit':
                $this->actionSendEditCode();
                break;
            default:
                $this->actionSendVerifyCode();
                break;
        }
    }

    /**
     * 手机号短信登录
     */
    public function actionLogin()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];
        $verifyCode = $_POST['verify_code'];
        $warningAction = loadAction('Warning');
        $warningAction->setRequestInfo([
            'dir_type' => 'w_default',
            'type' => 'login',
            'path' => true,
            'account_type' => 'phone',
            'source' => $mid,
            'passid' => ''
        ]);

        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(300, '', '请输入正确的手机号码！');
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(501, '', '项目标识不正确！');
        }
        $phoneAction = loadAction("phone");

        $sessionVerify = $this->version > 1 ? true : false;
        //*********** 章鱼兴修   ***********购省钱APP iOS
        if (in_array($phone, array('***********', '***********', '***********')) && $verifyCode == '123456')
        {
            //章鱼兴修IOS过审指定手机号,6月初删掉,删除前联系 王小威、汪丽娜、李志
            //下面一行纯粹为了代码不报警
            $sessionVerify = $this->version > 1 ? true : false;
        }
        else
        {
            if (!$phoneAction->checkLoginCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify, true))
            {
                $this->checkSmslog($mid, $phone, $verifyCode);
                $this->codeResponse(303, '', '手机短信验证码错误！');
            }
        }
        $this->requestLog();
        $memberModel = loadModel('member');
        $passid = $memberModel->getPassidByPhone($phone);
        if (!$passid)
        {
            //手机号,未绑定手机号, 但存在用户名
            $passid = $memberModel->getPassidByUsername($phone);
        }
        $user = [];
        if (!empty($passid))
        {
            $user = $memberModel->read($passid);
            $isProhibitLogin = $phoneAction->prohibitUsernameAsPhoneDiffBindPhoneLogin($user, $phone);
            if ($isProhibitLogin)
            {
                $this->codeResponse(400, '', '登录失败！');
            }
        }

        //收集注册登录信息
        $source = Url::getStringParam("mid", "mobile", Url::POST);
        $st = !!$passid;
        $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        \Service\AccountStat\AccountStat::collect($type, $clientType, $phone, $st, $source);

        if ($passid)
        {
//            $user = $memberModel->read($passid);

            $phoneAction = loadAction("phone");
            $newUserName = $phoneAction->repairUsernameWithPhoneNum($phone, $user, "clientapi");
            if ($newUserName)
            {
                $username = $newUserName;
            }
            else
            {
                $username = $user['username'];
            }

            $user["m_uid"] = unserialize($user['m_uid']);
            $uid = $user['m_uid']['1'];
            $userMod = $user['gid'] % 100 == 0 ? $user['gid'] : 0;
            $loginAction = loadAction("login");
            $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
            $warningAction->setRequestInfo(['passid' => $passid]);
            $this->codeResponse(200, [
                'I' => $cookie['I']
            ], '登录成功！');
        }
        else
        {
            $this->codeResponse(400, '', '登录失败！');
        }
    }

    /**
     * 手机号短信登录
     */
    public function actionWebLogin()
    {
        $mid = Url::getStringParam('mid', '');
        $phone = Url::getStringParam('phone', '');
        $verifyCode = Url::getStringParam('verify_code', '');
        $warningAction = loadAction('Warning');
        $warningAction->setRequestInfo([
            'dir_type' => 'w_default',
            'type' => 'login',
            'path' => true,
            'account_type' => 'phone',
            'source' => $mid,
            'passid' => ''
        ]);

        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(300, "", "请输入正确的手机号码！");
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(501, "", "项目标识不正确！");
        }
        $phoneAction = loadAction("phone");

        $sessionVerify = $this->version > 1 ? true : false;
        if (!$phoneAction->checkLoginCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify, true))
        {
            $this->checkSmslog($mid, $phone, $verifyCode);
            $this->codeResponse(303, "", "手机短信验证码错误！");
        }
        $this->requestLog();
        $memberModel = loadModel('member');
        $passid = $memberModel->getPassidByPhone($phone);
        if (!$passid)
        {
            //手机号,未绑定手机号, 但存在用户名
            $passid = $memberModel->getPassidByUsername($phone);
        }

        $user = [];
        if (!empty($passid))
        {
            $user = $memberModel->read($passid);
            if ($user['locked'] == 2) {
                $this->codeResponse(400, "", "此账号已被冻结，请联系客服", ********);
            }
            $isProhibitLogin = $phoneAction->prohibitUsernameAsPhoneDiffBindPhoneLogin($user, $phone);
            if ($isProhibitLogin)
            {
                $this->codeResponse(400, '', '登录失败！');
            }
        }

        //收集注册登录信息
        $source = Url::getStringParam("mid", "mobile", Url::POST);
        $st = !!$passid;
        $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        \Service\AccountStat\AccountStat::collect($type, $clientType, $phone, $st, $source);

        if ($passid)
        {
//            $user = $memberModel->read($passid);

            $phoneAction = loadAction("phone");
            $newUserName = $phoneAction->repairUsernameWithPhoneNum($phone, $user, "clientapi");
            if ($newUserName)
            {
                $username = $newUserName;
            }
            else
            {
                $username = $user['username'];
            }

            $user["m_uid"] = unserialize($user['m_uid']);
            $uid = $user['m_uid']['1'];
            $userMod = $user['gid'] % 100 == 0 ? $user['gid'] : 0;
            $loginAction = loadAction("login");
            $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
            $response = array(
                'I' => $cookie['I']
            );
            if ($mid == 'pcbs')
            {
                $paramsEncodeKey = Config::get("paramsEncodeKey");
                $response['phone'] = \Common\Utils\Security::encodeString($paramsEncodeKey["default"], $phone);
            }
            $warningAction->setRequestInfo(['passid' => $passid]);
            $this->codeResponse(200, $response, "登录成功！");
        }
        else
        {
            $this->codeResponse(400, "", "登录失败！");
        }
    }

    /**
     * 验证并注册
     */
    public function actionQuickReg()
    {
        $phone = \Common\Utils\Url::getStringParam("phone");
        $mid = \Common\Utils\Url::getStringParam("mid");
        $verifyCode = \Common\Utils\Url::getStringParam("verify_code");

        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(300, "", "请确认您的手机号是否填写正确!");
        }

        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(501, "", "项目标识不正确!");
        }

        $sessionVerify = $this->version > 1 ? true : false;

        $phoneAction = loadAction("phone");
        if (!$phoneAction->checkRegCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify, true))
        {
            $this->checkSmslog($mid, $phone, $verifyCode);
            $this->codeResponse(400, "", "短信验证失败!");
        }
        $this->requestLog();
        $userClientIp = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $pwd = mt_rand() . time();
        $password = substr(md5($pwd . MD5KEY), 0, 16);

        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        $regAction = loadAction('reg');

        $gid = 300;
        // 客户端 注册
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        $source = \Common\Utils\Url::getStringParam("mid", "mobile", \Common\Utils\Url::POST);
        $result = $regAction->regPhone($phone, $password, $pwdStrength, $userClientIp, "login.2345.com", $gid, $source, $clientType);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            $this->codeResponse(403, "", 'error:' . $msgs[$states[0]][$states[1]]);
        }
        elseif ($states[0] == 300)
        {
            $this->codeResponse($states[0] + $states[1], "", $msgs[$states[0]][$states[1]]);
        }
        elseif ($states[0] == 200)
        {
            $cookie = $result[1]['cookie'];
            $data = array(
                'I' => $cookie['I'],
            );
            $this->codeResponse(200, $data, "注册成功！");
        }
        else
        {
            $this->codeResponse(500, "", "禁止注册！");
        }
    }

    /**
     * 绑定手机
     */
    public function actionBind()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];
        $verifyCode = $_POST['verify_code'];
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->showError300();
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo)
        {
            $this->showError401();
        }
        $passid = $authInfo['i'];
        $phoneAction = loadAction("phone");
        if (!$phoneAction->checkBindCode($mid, $passid, md5($phone . $verifyCode)))
        {
            $this->showError303();
        }
        $retCode = $phoneAction->bind($passid, $phone);
        switch ($retCode)
        {
            case '200.0':
                $this->log($passid, '', $phone);
                $this->show200('绑定成功！');
                break;
            case '300.0':
                $this->showError300();
                break;
            case '300.1':
                $this->showError301();
                break;
            case '300.2':
                $this->showError302();
                break;
            case '300.3':
                $this->showError402();
                break;
            case '500.0':
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 修改手机
     */
    public function actionEdit()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST['mid'];
        $phone = $_POST['phone'];
        $verifyCode = $_POST['verify_code'];
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->showError300();
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo)
        {
            $this->showError401();
        }
        $passid = $authInfo['i'];
        $phoneAction = loadAction("phone");
        if (!$phoneAction->checkEditCode($mid, $passid, md5($phone . $verifyCode)))
        {
            $this->showError303();
        }
        $memberModel = loadModel("member");
        $userinfo = $memberModel->read($passid);
        if ($userinfo["phone"])
        {
            $phoneOld = $userinfo["phone"];
            $retCode = $phoneAction->edit($passid, $phone);
        }
        else
        {
            $phoneOld = $userinfo["phone_redundancy"];
            $retCode = $phoneAction->bindSimple($passid, $phone);
        }
        switch ($retCode)
        {
            case '200.0':
                $this->log($passid, $phoneOld, $phone);
                $this->show200('修改成功！');
                break;
            case '300.0':
                $this->showError300();
                break;
            case '300.1':
                $this->showError301();
                break;
            case '300.2':
                $this->showError302();
                break;
            case '500.0':
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 返回成功信息
     */
    private function show200($msg = '发送成功！')
    {
        die(json_encode(array(
            'code' => 200,
            'data' => '',
            'msg' => mb_convert_encoding($msg, 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示300错
     */
    private function showError300()
    {
        die(json_encode(array(
            'code' => 300,
            'data' => '',
            'msg' => mb_convert_encoding('请输入正确的手机号码！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示301错
     */
    private function showError301()
    {
        die(json_encode(array(
            'code' => 301,
            'data' => '',
            'msg' => mb_convert_encoding('该手机号已被其他用户占用！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示302错
     */
    private function showError302()
    {
        die(json_encode(array(
            'code' => 302,
            'data' => '',
            'msg' => mb_convert_encoding('该手机号已被其他用户绑定！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示303错
     */
    private function showError303()
    {
        die(json_encode(array(
            'code' => 303,
            'data' => '',
            'msg' => mb_convert_encoding('手机短信验证码错误！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示304错
     */
    private function showError304()
    {
        die(json_encode(array(
            'code' => 304,
            'data' => '',
            'msg' => mb_convert_encoding('请输入正确的图片验证码！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示400错
     */
    private function showError400()
    {
        die(json_encode(array(
            'code' => 400,
            'data' => '',
            'msg' => mb_convert_encoding('发送频繁，请稍后再试！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示401错
     */
    private function showError401()
    {
        die(json_encode(array(
            'code' => 401,
            'data' => '',
            'msg' => mb_convert_encoding('身份验证失效，请重新登录！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示402错
     */
    private function showError402()
    {
        die(json_encode(array(
            'code' => 402,
            'data' => '',
            'msg' => mb_convert_encoding('已绑定过手机号！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示403错
     */
    private function showError403()
    {
        die(json_encode(array(
            'code' => 403,
            'data' => '',
            'msg' => mb_convert_encoding('该手机号禁止使用！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示404错
     */
    private function showError404()
    {
        die(json_encode(array(
            'code' => 404,
            'data' => '',
            'msg' => mb_convert_encoding('你的帐号未绑定手机！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示500错
     */
    private function showError500()
    {
        die(json_encode(array(
            'code' => 500,
            'data' => '',
            'msg' => mb_convert_encoding('服务器忙，请稍后再试！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 显示501错
     */
    private function showError501()
    {
        die(json_encode(array(
            'code' => 501,
            'data' => '',
            'msg' => mb_convert_encoding('项目标识不正确！', 'UTF-8', 'GBK')
        )));
    }

    /**
     * 添加手机修改日志
     * @param int $passid
     * @param string $phoneOld
     * @param string $phone
     */
    private function log($passid, $phoneOld, $phone, $from = 'phone')
    {
        $clientIp = get_client_ip();
        $domain = 'login.2345.com';
        $referDomain = getReferDomain();
        $phoneModel = loadModel('phone');
        $phoneModel->log($passid, array('phone_old' => $phoneOld, 'phone' => $phone, 'op_time' => time(), 'op_ip' => $clientIp, 'op_domain' => $domain, 'op_refer' => $referDomain, 'op_from' => $from));
    }

}
