<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/6/1
 * Time: 11:31
 */

namespace Service\ThirdParty\Auth;

use Common\Utils\Encoding;
use Service\AccountStat\Consts\AccountStat;
use Service\UserBase\TipsMap;

class WxXcx
{
    protected static $OK = 0;
    protected static $IllegalAesKey = - 41001;
    protected static $IllegalIv = - 41002;
    protected static $IllegalBuffer = - 41003;
    protected static $DecodeBase64Error = - 41004;
    protected static $appId = "";

    private static $redis = null;

    /**
     * 获取redis链接
     * @return \redis|null
     */
    protected static function getRedis()
    {
        if (!is_object(self::$redis)) {
            self::$redis = \RedisEx::getInstance();
        }

        return self::$redis;
    }

    /**
     * 用户登录的session key
     * @param string $openid openid
     * @return string
     */
    public static function loginRedisSessionKey($openid)
    {
        return "xcxUser:openid:" . $openid;
    }

    /**
     * 记录微信小程序生成的sessionkey
     * @param string $openid     openid
     * @param string $sessionKey sessionkey
     * @param int    $expire     有效期
     * @return bool
     */
    public static function setLoginSessionKey($openid, $sessionKey, $expire = 86400)
    {
        $key = self::loginRedisSessionKey($openid);
        $isSet = self::getRedis()->set($key, $sessionKey);
        self::getRedis()->expire($key, $expire);

        return $isSet;
    }

    /**
     * 设置APPid
     * @param string $appId appid
     * @return void
     */
    public static function setAppId($appId)
    {
        self::$appId = $appId;
    }

    /**
     * 获取appId
     * @return string
     */
    public static function getAppId()
    {
        return self::$appId;
    }

    /**
     * 获取小程序用户的sessionkey
     * @param string $openid 小程序的openid
     * @return bool|string
     */
    public static function getLoginSessionKey($openid)
    {
        $sessionInfo = self::getRedis()->get(self::loginRedisSessionKey($openid));
        if (empty($sessionInfo)) {
            $xcxMode = loadModel('Xcx');
            $openInfo = $xcxMode->getBindOpenid($openid);
            if (!empty($openInfo['sessionKey'])) {
                $isSet = self::setLoginSessionKey($openid, $openInfo['sessionKey']);
                if ($isSet === false) {
                    $warningAction = loadAction('Warning');
                    $warningAction->setRequestInfo(['setRedisSessionKeyError' => TipsMap::ERROR_XCX_SET_REDIS_SESSION_KEY['code']]);
                }
                $sessionInfo = $openInfo['sessionKey'];
            } else {
                $sessionInfo = '';
            }
        }

        return $sessionInfo;
    }


    /**
     * 清除关系列表
     * @param string $type        清除类型
     * @param bool   $isBindPhone 是否绑定手机好
     * @param array  $openidInfo  openid信息
     * @return array
     */
    public static function clearOpenidMapList($type, $isBindPhone, $openidInfo)
    {
        $xcxMode = loadModel('Xcx');
        $isDel = $xcxMode->delOpenIdUnionIdInfo($openidInfo['passid'], $openidInfo['openid'], $openidInfo['unionid']);
        if ($isDel === false) {
            //删除失败
            return [TipsMap::ERROR_XCX_DEL_OPENID_BIND, ""];
        }
        $outData = [
            'isBindPhone' => $isBindPhone,
        ];
        $outMsg = [];
        switch ($type) {
            case 'wxUnbind':
                $outMsg = TipsMap::ERROR_XCX_UBINDWEIXIN;
                break;
            case 'changeWxBind':
                $outMsg = TipsMap::ERROR_XCV_CHANGE_WEIXIN_BIND;
                break;
            case 'logout':
                $outMsg = TipsMap::ERROR_GET_USER_INFO_FAILED;
                break;
            case 'noUnionId':
                $outMsg = TipsMap::ERROR_XCX_NO_BINDUNIONID;
                break;
            default:
                break;
        }

        return [$outMsg, $outData];
    }


    /**
     * 小程序登录操作
     * @param string $mid       项目标识
     * @param array  $userInfo  用户信息
     * @param string $openid    openid
     * @param string $unionId   unionid
     * @param bool   $isLoadLog 是否记录日志
     * @return array
     */
    public static function doLogin($mid, $userInfo, $openid, $unionId, $isLoadLog = true)
    {
        if (empty($userInfo)) {
            // 没有用户信息
            return [TipsMap::ERROR_GET_USER_INFO_FAILED, ""];
        }
        $memberModel = loadModel('member');
        $loginAction = loadAction('login');
        $passid = $userInfo['id'];
        \Service\AccountStat\AccountStat::collect(AccountStat::TP_LOGIN, AccountStat::CTP_XCX, AccountStat::AC_TP_OAUTH_XCX, true, $mid);
        //微信绑定 手机号码绑定 直接登录
        $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
        $uid = $userInfo['uid'];
        $username = $userInfo['username'];
        $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;
        $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
        $paramsEncodeKey = \Config::get("paramsEncodeKey");
        $sData = json_encode(
            [
                'openid'  => $openid,
                'unionId' => $unionId,
                'passId'  => $passid,
                'cookieI' => $cookie['I'],
                'time'    => time(),
            ]
        );
        $outputData = [
            'sData' => \Common\Utils\Security::encodeString($paramsEncodeKey["xcx"], $sData),
        ];
        if ($isLoadLog) {
            $warningAction = loadAction('Warning');
            $warningAction->setRequestInfo(
                [
                    'passid'     => $passid,
                    'openid'     => $openid,
                    'unionId'    => $unionId,
                    'login_time' => time(),
                ]
            );
        }

        return [TipsMap::SUCCESS_LOGIN, $outputData];
    }

    /**
     * 更新sessionkey
     * @param int    $passid     passid
     * @param string $pushOpenid openid
     * @param string $unionId    unionid
     * @param string $sessionKey sessionkey
     * @return bool
     */
    public static function setOpenidUnionIdMap($passid, $pushOpenid, $unionId, $sessionKey)
    {
        $xcxMode = loadModel('Xcx');
        $openidInfo = $xcxMode->getBindOpenid($pushOpenid);
        if (empty($openidInfo)) {
            $appId = self::getAppId();
            $isSet = $xcxMode->addOpenIdUnionIdInfo($passid, $pushOpenid, $unionId, $sessionKey, $appId);
        } else {
            if ($sessionKey == $openidInfo['sessionKey']) {
                $isSet = true;
            } else {
                $isSet = $xcxMode->updateOpenIdUnionIdInfo($passid, $pushOpenid, $unionId, $sessionKey);
            }
        }

        return $isSet;
    }

    /**
     * @param string $mid        mid
     * @param int    $passid     passid
     * @param string $pushOpenid pushOpenid
     * @param string $unionId    unionId
     * @param string $nickname 昵称
     * @param string $avatarUrl 头像
     * @param string $sessionKey
     * @param string $phone
     * @param bool   $isLoadLog  是否加载日志
     * @return array
     */
    public static function bindOpenidToLogin($mid, $passid, $pushOpenid, $unionId, $nickname, $avatarUrl, $sessionKey = '', $phone = "", $isLoadLog = true)
    {
        $memberModel = loadModel('member');
        $xcxMode = loadModel('Xcx');
        //之前有在APP上进行了微信登录 , 插入关系 并进行登录操作
        $userInfo = $memberModel->read($passid);
        if (empty($userInfo)) {
            // 没有用户信息
            return [TipsMap::ERROR_GET_USER_INFO_FAILED, ""];
        }
        $isSet = true;
        if (!empty($phone)) {
            // 有手机号码 就进行绑定操作
            $phoneAction = loadAction("phone");
            $retCode = $phoneAction->bind($passid, $phone);
            if ($retCode != "200.0") {
                // 已绑定过手机号
                return [TipsMap::ERROR_XCX_PHONEISBIND_FAILED, ""];
            }
        }
        $isSet = self::setOpenidUnionIdMap($passid, $pushOpenid, $unionId, $sessionKey);
        if ($isSet !== false) {
            $userBaseAction = loadAction('UserBase');
            $result = [
                'nickname'     => Encoding::transcoding($nickname),
                'nicknameUtf8' => $nickname,
                'figureurl'    => $avatarUrl,
            ];
            $userBaseAction->updateThirdAvatarNicknameInfo($mid, $userInfo['id'], $result, 'xcx');

            return WxXcx::doLogin($mid, $userInfo, $pushOpenid, $unionId, $isLoadLog);
        } else {
            return TipsMap::ERROR_XCV_MAKE_OPENID_MAP;
        }
    }

    /**
     * 第三方注册且openid绑定unionid
     * @param string $mid        项目标识
     * @param string $openid     openid
     * @param string $unionId    unionid
     * @param string $nickname 昵称
     * @param string $avatarUrl 头像
     * @param string $sessionKey sessionkey
     * @param string $phone      绑定的手机号码
     * @return array
     */
    public static function doRegBindOpenid($mid, $openid, $unionId, $nickname, $avatarUrl, $sessionKey = "", $phone = "")
    {
        $memberModel = loadModel('member');
        $xcxMode = loadModel('Xcx');
        $nicknameGbk = Encoding::transcoding($nickname);
        $regResult = $memberModel->regOAuth('weixin', $unionId, $nicknameGbk, get_client_ip());
        if ($regResult === false) {
            //注册失败
            \Service\AccountStat\AccountStat::collect(AccountStat::TP_REG, AccountStat::CTP_APP, AccountStat::AC_TP_OAUTH_XCX, false, $mid);

            return [TipsMap::ERROR_THIRD_REG_FAILED, ""];
        } else {
            $passid = $regResult['passid'];
            \Service\AccountStat\AccountStat::collect(AccountStat::TP_REG, AccountStat::CTP_APP, AccountStat::AC_TP_OAUTH_XCX, true, $mid);
            $warningAction = loadAction('Warning');
            $warningAction->setRequestInfo(
                [
                    'passid'     => $passid,
                    'openid'     => $openid,
                    'unionId'    => $unionId,
                    'reg_time' => time(),
                ]
            );
            //注册成功 绑定openid和unionid关系
            return self::bindOpenidToLogin($mid, $passid, $openid, $unionId, $nickname, $avatarUrl, $sessionKey, $phone, false);
        }
    }

    /**
     * 手机账号绑定小程序账号
     * @param string $mid         项目标识
     * @param int    $phonePassId passid
     * @param string $pushOpenid  openid
     * @param string $unionId     unionid
     * @param string $nickname    昵称
     * @param string $avatarUrl   头像
     * @param string $sessionKey
     * @return array
     */
    public static function bindWxXcxToPhone($mid, $phonePassId, $pushOpenid, $unionId, $nickname, $avatarUrl, $sessionKey = "")
    {
        $oauthModel = loadModel('oauth');
        $memberModel = loadModel('member');
        $xcxMode = loadModel('Xcx');
        $userInfo = $memberModel->read($phonePassId);
        if (empty($userInfo)) {
            // 没有用户信息
            return [TipsMap::ERROR_GET_USER_INFO_FAILED, ""];
        }
        $username = $userInfo['username'];
        $nicknameGbk = Encoding::transcoding($nickname);
        $isSet = $oauthModel->setBind('weixin', $phonePassId, $unionId, $nicknameGbk, $username);
        if ($isSet === false) {
            return [TipsMap::ERROR_XCX_BIND_FAILED, ""];
        } else {
            //进行关联关系操作
            $isSet = self::setOpenidUnionIdMap($phonePassId, $pushOpenid, $unionId, $sessionKey);
            if ($isSet === false) {
                //openid 和 unionid 绑定失败
                return [TipsMap::ERROR_XCV_MAKE_OPENID_MAP, ""];
            } else {
                $userBaseAction = loadAction('UserBase');
                $result = [
                    'nickname'     => $nicknameGbk,
                    'nicknameUtf8' => $nickname,
                    'figureurl'    => $avatarUrl,
                ];
                $userBaseAction->updateThirdAvatarNicknameInfo($mid, $userInfo['id'], $result, 'xcx');

                return WxXcx::doLogin($mid, $userInfo, $pushOpenid, $unionId);
            }
        }
    }


    /**
     * 解码微信小程序的用户信息
     * @param string $appid         小程序appid
     * @param string $sessionKey    sessionkey
     * @param string $encryptedData 加密串
     * @param string $iv            偏移量
     * @return array
     */
    public static function getDecodeWxUserInfo($appid, $sessionKey, $encryptedData, $iv)
    {
        $data = [];
        self::decryptData($appid, $sessionKey, $encryptedData, $iv, $data);

        return $data;
    }


    /**
     * 检验数据的真实性，并且获取解密后的明文.
     * @param $encryptedData string 加密的用户数据
     * @param $iv            string 与用户数据一同返回的初始向量
     * @param $data          string 解密后的原文
     * @return int 成功0，失败返回对应的错误码
     */
    public static function decryptData($appid, $sessionKey, $encryptedData, $iv, &$data)
    {
        if (strlen($sessionKey) != 24) {
            return self::$IllegalAesKey;
        }
        $aesKey = base64_decode($sessionKey);
        if (strlen($iv) != 24) {
            return self::$IllegalIv;
        }
        $aesIV = base64_decode($iv);

        $aesCipher = base64_decode($encryptedData);

        $result = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);

        $dataObj = json_decode($result);
        if ($dataObj == null) {
            return self::$IllegalBuffer;
        }
        if ($dataObj->watermark->appid != $appid) {
            return self::$IllegalBuffer;
        }
        $data = $result;

        return self::$OK;
    }


    /**
     * 获取微信小程序access_token key
     * @param string $appid appid
     * @return string
     */
    public static function getWxXcxAccessTokenKey($appid)
    {
        return "xcxApp:" . $appid;
    }

    /**
     * 进程锁
     * @param int $expire 有效期
     * @return bool
     */
    private static function getLock($expire = 60)
    {
        return self::getRedis()->set("getAccessToken:lock", time(), ['nx', 'ex' => $expire]);
    }

    /**
     * 删除进程锁
     * @return mixed 成功：1，失败：0
     */
    protected static function delProcessLock()
    {
        return self::getRedis()->del("getAccessToken:lock");
    }

    /**
     * 获取access_token
     * @param string $appid appid
     * @param int $reloadTime 重载时间
     * @return bool|string
     */
    public static function getWxXcxAccessToken($appid, $reloadTime = 1800)
    {
        $accessTokenInfo = [];
        $isReloadAccessToken = false;
        $warningAction = loadAction('Warning');
        $appInfo = self::getRedis()->get(self::getWxXcxAccessTokenKey($appid));
        if (empty($appInfo)) {
            $xcxMode = loadModel('Xcx');
            $appInfo = $xcxMode->getWxXcxAccessToken($appid);
        } else {
            $appInfo = json_decode($appInfo, true);
        }
        if (!empty($appInfo['access_token']) && !empty($appInfo['expires_in'])) {
            if ($appInfo['expires_in'] - time() < $reloadTime) {
                //有效期小于1800s的时候，重新刷新access_token
                $warningAction->setRequestInfo(['reloadType' => 1, 'getLockType' => 1]);
                if (self::getLock()) {
                    $warningAction->setRequestInfo(['getLockStatus' => 1]);
                    $accessTokenInfo = self::getWxXcxApiAccessToken($appid);
                    $isReloadAccessToken = true;
                } else {
                    $warningAction->setRequestInfo(['getLockStatus' => 0]);

                    return $appInfo['access_token'];
                }
            } else {
                //返回redis 或数据库里面的值
                $warningAction->setRequestInfo(['reloadType' => 0,]);
                return $appInfo['access_token'];
            }
        } else {
            //数据库或redis没有数据的时候重新获取access_token
            $warningAction->setRequestInfo(['reloadType' => 2, 'getLockType' => 2]);
            if (self::getLock()) {
                $warningAction->setRequestInfo(['getLockStatus' => 1]);
                $accessTokenInfo = self::getWxXcxApiAccessToken($appid);
                $warningAction->setRequestInfo(['isReload' => true,]);
                $isReloadAccessToken = true;
            } else {
                $warningAction->setRequestInfo(['getLockStatus' => 0]);

                return false;
            }
        }
        if ($isReloadAccessToken) {
            $accessTokenInfo = json_decode($accessTokenInfo, true);
            $expiresIn = $accessTokenInfo['expires_in'] + time();
            $isSet = self::setWxXcxAccessToken($appid, $accessTokenInfo['access_token'], $expiresIn, 7200);
            if ($isSet !== false) {
                return $accessTokenInfo['access_token'];
            } else {
                return false;
            }
        }
    }


    /**
     * 设置微信小程序access_token
     * @param string $appid       appid
     * @param string $accessToken accessToken
     * @param int    $expireIn    有效期
     * @param int    $expire      redis的有效期
     * @return bool
     */
    public static function setWxXcxAccessToken($appid, $accessToken, $expireIn, $expire)
    {
        $key = self::getWxXcxAccessTokenKey($appid);
        $token = json_encode(
            [
                'access_token' => $accessToken,
                'expires_in'   => $expireIn,
            ]
        );
        $isSet = self::getRedis()->set($key, $token);
        self::getRedis()->expire($key, $expire);
        if ($isSet !== false) {
            $xcxMode = loadModel('Xcx');
            $updateData = [
                'appid'        => $appid,
                'access_token' => $accessToken,
                'expires_in'   => date('Y-m-d H:i:s', $expireIn),
            ];
            $isUpdate = $xcxMode->setWxXcxAccessToken($updateData);
            if ($isUpdate === false) {
                $warningAction = loadAction('Warning');
                $warningAction->setRequestInfo(["reloadAccessTokenError" => TipsMap::ERROR_XCX_RELOAD_ACCESS_TOKEN_FAILED['code']]);
            }
            self::delProcessLock();

            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取微信小程序后台API
     * @param string $appid appid
     * @return bool|\returns|string
     */
    public static function getWxXcxApiAccessToken($appid)
    {
        $oauthConfig = \Config::get('oauth');
        $oauthConfig = $oauthConfig['wx_xcx'];
        if (!isset($oauthConfig[$appid])) {
            return false;
        }
        $secret = $oauthConfig[$appid]['appkey'];
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appid}&secret={$secret}";
        $curlOption = [
            CURLOPT_CONNECTTIMEOUT => 1,
            CURLOPT_TIMEOUT        => 3,
        ];

        return http_get($url, [], $curlOption);
    }

    /**
     * 登录请求小程序进行授权
     * @param string $appid 小程序的appid
     * @param string $code  小程序生成的临时code码
     * @return bool|\returns|string
     */
    public static function getSessionKey($appid, $code)
    {
        $oauthConfig = \Config::get('oauth');
        $oauthConfig = $oauthConfig['wx_xcx'];
        if (!isset($oauthConfig[$appid])) {
            return false;
        }
        $secret = $oauthConfig[$appid]['appkey'];
        $url = 'https://api.weixin.qq.com/sns/jscode2session?appid=' . $appid . '&secret=' . $secret . '&js_code=' . $code . '&grant_type=authorization_code';
        $curlOption = [
            CURLOPT_CONNECTTIMEOUT => 1,
            CURLOPT_TIMEOUT        => 3,
        ];

        return http_get($url, [], $curlOption);
    }

}