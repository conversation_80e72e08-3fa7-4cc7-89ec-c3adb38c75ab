<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：AccountStatController.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：07-18, 2016
 *
 * actionDay Minute Detail(不展示数据表,只展示api数据)
 * actionDaySource MinuteSource DetailSource(不展示数据表,只展示api数据)
 *
 * 功能很相近,只是返回值, 不带Source 返回了成功是失败, 带了Source 只返回成功
 *
 * 每个方法带了export = 1 表示导出  export =2 表示 在页面中打印即将导出的内容.
 * 可以尝试由于六个精简到三个通用方法. 甚至1个方法.
 */

includeBase('AdminController');

class AccountStatController extends AdminController
{

    /**
     * AccountStatController constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * @param string $typeField 图表类型
     * @param string $clientTypeField 客户端来源
     * @return string mixed
     */
    public function actionDay($typeField = "login", $clientTypeField = "web")
    {
        if ($typeField == "login")
        {
            $title = '每日登录次数表';
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        }
        else
        {
            $title = '每日注册次数表';
            $typeField = "reg";
            $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
        }
        if ($clientTypeField == "web")
        {
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        }
        else
        {
            $clientTypeField = "app";
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        }

        if (strtotime($_GET['startTime']) === false || strtotime($_GET['endTime']) === false)
        {
            $startTime = date('Y-m-d 00:00:00', strtotime('-30 day'));
            $endTime = date('Y-m-d 23:59:59', strtotime('-1 day'));
        }
        else
        {
            $startTime = date('Y-m-d', strtotime($_GET['startTime'])) . ' 00:00:00';
            $endTime = date('Y-m-d', strtotime($_GET['endTime'])) .' 23:59:59';
        }
        $totalTime = strtotime($endTime) - strtotime($startTime);  //查询总天数
        $restrictTime = 364 * 86400;
        //如果时间跨度大于1年,则默认为1年
        if ($totalTime > $restrictTime)
        {
            $startTime = date('Y-m-d 00:00:00', strtotime($endTime) - $restrictTime);
        }
        list($days, $formatDays) = $this->getDaysList($startTime, $endTime);
        $accountStatService = new \Service\AccountStat\AccountStat();
        $res = $accountStatService->getAccountStatSummaryDay($type, $clientType, $startTime, $endTime);
        $data = array();
        foreach (array_reverse($formatDays, true) as $k => $v)
        {
            //如果小于查询起始时间或者大于查询截止日期就退出
            if (strtotime($v) < strtotime($startTime) || strtotime($v) > strtotime($endTime))
            {
                continue;
            }
            $data[$v] = array();
        }
        foreach ($res as $key => $val)
        {
            $day = $val["day"];
            $source = $val["source"];
            $status = $val["status"];
            $amount = $val["amount"];
            $statusKey = ($status == \Service\AccountStat\Consts\AccountStat::STATUS_SUCC) ? "succ" : "fail";
            if (\Service\AccountStat\Consts\AccountStat::getGroupType($clientType, $source))
            {
                $data[$day][$statusKey][$source] = $amount;
                @$data[$day][$statusKey]["all"] += $amount;
            }
        }
        $domainGroups = \Service\AccountStat\Consts\AccountStat::getGroupList($clientType);
        $pageArray = array(
            'title' => $title,
            'typeField' => $typeField,
            'clientTypeField' => $clientTypeField,
            'type' => $type,
            'clientType' => $clientType,

            'data' => $data,
            'domainGroups' => $domainGroups,
            'startTime' => date('Y-m-d', strtotime($startTime)),
            'endTime' => date('Y-m-d', strtotime($endTime))
        );
        $export = \Common\Utils\Url::getStringParam("export");
        if ($export)
        {
            if ($export == 1)
            {
                header("Content-type:application/vnd.ms-excel");
                header("Content-Disposition:filename=" . $title . date("YmdHis") . ".xls");
            }
            loadView('admin/account_stat/day_export.tpl.html', $pageArray);
        }
        else
        {
            $this->show('admin/account_stat/day.tpl.html', $pageArray);
        }
    }

    public function  actionMinute($typeField = "login", $clientTypeField = "web")
    {
        if ($typeField == "login")
        {
            $title = '每3分钟登录次数表';
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        }
        else
        {
            $title = '每3分钟注册次数表';
            $typeField = "reg";
            $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
        }
        if ($clientTypeField == "web")
        {
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        }
        else
        {
            $clientTypeField = "app";
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        }

        if (strtotime($_GET['startTime']) === false || strtotime($_GET['endTime']) === false)
        {
            $startTime = date('Y-m-d H:i:s', strtotime('-2 hours'));
            $endTime = date('Y-m-d H:i:s');
        }
        else
        {
            $startTime = $_GET['startTime'];
            $endTime = $_GET['endTime'];
        }
        if ((int)$_GET['gap'] <= 120 && (int)$_GET['gap'] >= 1)
        {
            $gap = (int)$_GET['gap'];
        }
        else
        {
            $gap = 3;
        }

        list($minutes, $formatMinutes) = $this->getMinuteArray($gap);
        $accountStatService = new \Service\AccountStat\AccountStat();
        $res = $accountStatService->getAccountStatForMinute($type, $clientType, $startTime, $endTime);
        $data = array();
        foreach (array_reverse($formatMinutes, true) as $k => $v)
        {
            $data[$v] = array();
        }
        foreach ($res as $key => $val)
        {
            $time = $val["time1"] . sprintf("%02d", $val["time2"]);
            $source = $val["source"];
            $status = $val["status"];
            $amount = $val["amount"];
            $statusKey = ($status == \Service\AccountStat\Consts\AccountStat::STATUS_SUCC) ? "succ" : "fail";
            if (\Service\AccountStat\Consts\AccountStat::getGroupType($clientType, $source))
            {
                $data[$time][$statusKey][$source] = $amount;
                @$data[$time][$statusKey]["all"] += $amount;
            }
        }
        $domainGroups = \Service\AccountStat\Consts\AccountStat::getGroupList($clientType);
        $pageArray = array(
            'title' => $title,
            'typeField' => $typeField,
            'clientTypeField' => $clientTypeField,
            'type' => $type,
            'clientType' => $clientType,
            'data' => $data,
            'domainGroups' => $domainGroups,
        );
        $export = \Common\Utils\Url::getStringParam("export");
        if ($export)
        {
            if ($export == 1)
            {
                header("Content-type:application/vnd.ms-excel");
                header("Content-Disposition:filename=" . $title . date("YmdHis") . ".xls");
            }
            loadView('admin/account_stat/minute_export.tpl.html', $pageArray);
        }
        else
        {
            $this->show('admin/account_stat/minute.tpl.html', $pageArray);
        }
    }

    public function  actionDetail($typeField = "login", $clientTypeField = "web")
    {
        if ($typeField == "login")
        {
            $title = '每日登录详情次数表';
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        }
        else
        {
            $title = '每日注册详情次数表';
            $typeField = "reg";
            $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
        }
        if ($clientTypeField == "web")
        {
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        }
        else
        {
            $clientTypeField = "app";
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        }

        $data = array();
        $domainGroups = \Service\AccountStat\Consts\AccountStat::getGroupList($clientType);
        $pageArray = array(
            'title' => $title,
            'typeField' => $typeField,
            'clientTypeField' => $clientTypeField,
            'type' => $type,
            'clientType' => $clientType,
            'data' => $data,
            'domainGroups' => $domainGroups,
        );
        $this->show('admin/account_stat/detail.tpl.html', $pageArray);
    }

    /**
     * @param string $typeField 图表类型
     * @param string $clientTypeField 客户端来源
     * @return string
     */
    public function actionDaySource($typeField = "login", $clientTypeField = "web")
    {
        if ($typeField == "login")
        {
            $title = '每日登录次数表';
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        }
        else
        {
            $title = '每日注册次数表';
            $typeField = "reg";
            $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
        }
        if ($clientTypeField == "web")
        {
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        }
        else
        {
            $clientTypeField = "app";
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        }

        if (strtotime($_GET['startTime']) === false || strtotime($_GET['endTime']) === false)
        {
            $startTime = date('Y-m-d 00:00:00', strtotime('-30 day'));
            $endTime = date('Y-m-d 23:59:59', strtotime('-1 day'));
        }
        else
        {
            $startTime = date('Y-m-d', strtotime($_GET['startTime'])) . ' 00:00:00';
            $endTime = date('Y-m-d', strtotime($_GET['endTime'])) .' 23:59:59';
        }
        $totalTime = strtotime($endTime) - strtotime($startTime);  //查询总天数
        $restrictTime = 364 * 86400;
        //如果时间跨度大于1年,则默认为1年
        if ($totalTime > $restrictTime)
        {
            $startTime = date('Y-m-d 00:00:00', strtotime($endTime) - $restrictTime);
        }
        list($days, $formatDays) = $this->getDaysList($startTime, $endTime);
        $accountStatService = new \Service\AccountStat\AccountStat();
        $res = $accountStatService->getAccountStatSummaryDay($type, $clientType, $startTime, $endTime);

        $data = array();
        foreach (array_reverse($formatDays, true) as $k => $v)
        {
            //如果小于查询起始时间或者大于查询截止日期就退出
            if (strtotime($v) < strtotime($startTime) || strtotime($v) > strtotime($endTime))
            {
                continue;
            }
            $data[$v] = array();
        }
        foreach ($res as $key => $val)
        {
            $day = $val["day"];
            $source = $val["source"];
            $status = $val["status"];
            $amount = $val["amount"];
            if ($status == \Service\AccountStat\Consts\AccountStat::STATUS_SUCC)
            {
                if (\Service\AccountStat\Consts\AccountStat::getGroupType($clientType, $source))
                {
                    $data[$day]["succ"][$source] = $amount;
                    @$data[$day]["succ"]["all"] += $amount;
                }
            }
        }
        $domainGroups = \Service\AccountStat\Consts\AccountStat::getGroupList($clientType);
        $pageArray = array(
            'title' => $title,
            'typeField' => $typeField,
            'clientTypeField' => $clientTypeField,
            'type' => $type,
            'clientType' => $clientType,
            'domainGroups' => $domainGroups,
            'data' => $data,
            'startTime' => date('Y-m-d', strtotime($startTime)),
            'endTime' => date('Y-m-d', strtotime($endTime))
        );
        $export = \Common\Utils\Url::getStringParam("export");
        if ($export)
        {
            if ($export == 1)
            {
                header("Content-type:application/vnd.ms-excel");
                header("Content-Disposition:filename=" . $title . date("YmdHis") . ".xls");
            }
            loadView('admin/account_stat/day_success_export.tpl.html', $pageArray);
        }
        else
        {
            $this->show('admin/account_stat/day_source.tpl.html', $pageArray);
        }
    }

    public function  actionMinuteSource($typeField = "login", $clientTypeField = "web")
    {
        if ($typeField == "login")
        {
            $title = '每3分钟登录次数表';
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        }
        else
        {
            $title = '每3分钟注册次数表';
            $typeField = "reg";
            $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
        }
        if ($clientTypeField == "web")
        {
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        }
        else
        {
            $clientTypeField = "app";
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        }
        $startTime = date('Y-m-d H:i:s', strtotime('-2 hours'));
        $endTime = date('Y-m-d H:i:s');

        list($minutes, $formatMinutes) = $this->getMinuteArray();
        $accountStatService = new \Service\AccountStat\AccountStat();
        $res = $accountStatService->getAccountStatForMinute($type, $clientType, $startTime, $endTime);

        $data = array();
        foreach (array_reverse($formatMinutes, true) as $k => $v)
        {
            $data[$v] = array();
        }
        foreach ($res as $key => $val)
        {
            $time = $val["time1"] . sprintf("%02d", $val["time2"]);
            $source = $val["source"];
            $status = $val["status"];
            $amount = $val["amount"];
            if ($status == \Service\AccountStat\Consts\AccountStat::STATUS_SUCC)
            {
                if (\Service\AccountStat\Consts\AccountStat::getGroupType($clientType, $source))
                {
                    $data[$time]["succ"][$source] = $amount;
                    @$data[$time]["succ"]["all"] += $amount;
                }
            }
        }

        $domainGroups = \Service\AccountStat\Consts\AccountStat::getGroupList($clientType);
        $pageArray = array(
            'title' => $title,
            'typeField' => $typeField,
            'clientTypeField' => $clientTypeField,
            'type' => $type,
            'clientType' => $clientType,
            'domainGroups' => $domainGroups,
            'data' => $data,
        );
        $export = \Common\Utils\Url::getStringParam("export");
        if ($export)
        {
            if ($export == 1)
            {
                header("Content-type:application/vnd.ms-excel");
                header("Content-Disposition:filename=" . $title . date("YmdHis") . ".xls");
            }
            loadView('admin/account_stat/day_success_export.tpl.html', $pageArray);
        }
        else
        {
            $this->show('admin/account_stat/minute_source.tpl.html', $pageArray);
        }
    }

    public function  actionDetailSource($typeField = "login", $clientTypeField = "web")
    {
        if ($typeField == "login")
        {
            $title = '每日登录详情次数表';
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        }
        else
        {
            $title = '每日注册详情次数表';
            $typeField = "reg";
            $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
        }
        if ($clientTypeField == "web")
        {
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        }
        else
        {
            $clientTypeField = "app";
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        }

        $data = array();
        $domainGroups = \Service\AccountStat\Consts\AccountStat::getGroupList($clientType);
        $pageArray = array(
            'title' => $title,
            'typeField' => $typeField,
            'clientTypeField' => $clientTypeField,
            'type' => $type,
            'clientType' => $clientType,
            'data' => $data,
            'domainGroups' => $domainGroups,
        );
        $this->show('admin/account_stat/detail_source.tpl.html', $pageArray);
    }

    private function getMinuteArray($gap = 3)
    {
        $min = date("i");
        $minutes = array();
        $formatMinutes = array();
        for ($i = 40;$i > 0;$i--)
        {
            $key = date("Hi", strtotime("-" . ($gap * ($i - 1) + $min % 3) . " minutes"));
            $key2 = date("Y-m-d H:i", strtotime("-" . ($gap * ($i - 1) + $min % 3) . " minutes"));

            $minutes[$key] = 0;
            $formatMinutes[$key] = $key2;
        }
        return array($minutes, $formatMinutes);
    }

    private function getDaysArray($gap = 1)
    {
        $days = array();
        $formatDays = array();
        for ($i = 30;$i > 0;$i = $i - $gap)
        {
            $key = date('md', strtotime("-$i day"));
            $key2 = date('Y-m-d', strtotime("-$i day"));
            $days[$key] = 0;
            $formatDays[] = $key2;
        }
        return array($days, $formatDays);
    }

    /**
     * @param string $startTime 起始时间
     * @param string  $endTime 结束时间
     * @return array
     */
    private function getDaysList($startTime, $endTime)
    {
        $dateList = range(strtotime($startTime), strtotime($endTime), 86400);
        $days = array();
        $formatDays = array();
        foreach ($dateList as $dateInfo)
        {
            $days[date('md', $dateInfo)] = 0;
            $formatDays[] = date('Y-m-d', $dateInfo);
        }
        return array($days,$formatDays);
    }

    public function actionMonitor()
    {
        $pageArray = array(
            'title' => "登录/注册每三分钟数报警",
        );
        $this->show('admin/account_stat/monitor.tpl.html', $pageArray);
    }

    /**
     * actionAuthorizeLogin
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionAuthorizeLogin()
    {
        $pageArray = array();
        $this->show('admin/account_stat/authorize.tpl.html', $pageArray);
    }
}
