{{include file = "admin/redmin/layout.tpl.html"}}
{{include file = "admin/redmin/generalmodals.tpl.html"}}
<div class="alert alert-warning">
    差集 = （集群数据 - 实例数据）/ 实例数据
    <br>差集比小于 5% 为pass
</div>
<span class="span12">
    <h5><i class="icon-bar-chart"></i> Redis Info
        {{if $pageArray.checking}}
        【{{$pageArray.checking}}】
        {{else}}
        【<a href="?act=refresh">重新检测</a>】
        {{/if}}
    </h5>
    <table class="table table-striped">
        <tr>
            <th>
                检查项目：
            </th>
            <th>
                实例数据：
            </th>
            <th>
                集群数据：
            </th>
            <th>
                差集：
            </th>
            <th>
                检测状态：
            </th>
        </tr>
        {{foreach from = $pageArray.list key = member item = value}}
        <tr>
            <td>
                {{$value.item}}
            </td>
            <td>
                {{$value.ins}}
            </td>
            <td>
                {{$value.clu}}
            </td>
            <td>
                {{$value.cacuRes}}
            </td>
            <td>
                {{if $value.status eq 'pass'}}
                <span class="label label-success">Pass</span>
                {{else}}
                <span class="label label-warning">Warning</span>
                {{/if}}
            </td>
        </tr>
        {{/foreach}}
    </table>
</span>
{{include file = "admin/redmin/footer.tpl.html"}}
