<?php
/**
 * 文件名称：GuoDuSmsChannelAction.php
 * @copyright Copyright (c) 2017，上海二三四五网络科技股份有限公司
 * <AUTHOR>
 * @group     互联网研发中心/平台开发组
 * @date      2017/12/08
 */

namespace Smschannel;

use FuncAction;
use Service\Swoole\Tool\Log;

class GuoDuSmsChannelAction extends SmsChannelAction
{
    /**
     * 发送短信，单条
     * @param array|string $phone 手机号
     * @param string       $msg   短信内容
     * @param string       $sign  签名
     * @return int
     */
    public function send($phone, $msg, $sign)
    {
        $phoneStr = $phone;
        $multi = 0;
        if (is_array($phoneStr))
        {
            $phoneStr = implode(",", $phoneStr);
            $multi = 1;
        }
        //该通道具有直接传输GBK编码的接口
        $params = array(
            'OperID'    => $this->user,//用户名
            'OperPass'  => $this->pwd,//密码
            'DesMobile' => $phoneStr,//手机号
        );
        $query = http_build_query($params);
        $content = urlencode($sign . $msg);
        $finalUrl = "{$this->apiUrl}?{$query}&Content={$content}";
        $resultStr = FuncAction::get($finalUrl, 4);
        $str = str_replace(array(' ', "\n"), '', $resultStr);
        $patternCode = '/<code>(.*)<\/code>/';
        preg_match($patternCode, $str, $code);
        $patternMsgid = '/<message><desmobile>(.*)<\/desmobile><msgid>(.*)<\/msgid><\/message>/U';
        preg_match_all($patternMsgid, $str, $msgid);


        switch ($code[1])
        {
            case '01':
            case '03':
                if (!$multi)
                {
                    $this->msgId = $msgid[2][0];
                    $this->OperatorMsg = array(
                        'result'     => self::RET_SUCC,
                        'codeStatus' => 0,
                        'codeDesc'   => '成功',
                    );
                    return self::RET_SUCC;
                }
                else
                {
                    foreach ($msgid[1] as $index => $phoneLine)
                    {
                        $this->msgIdList[$phoneLine] = $msgid[2][$index];
                        $this->operatorMsgList[$phoneLine] = array(
                            'result'     => self::RET_SUCC,
                            'codeStatus' => 0,
                            'codeDesc'   => '成功',
                        );
                    }
                    return self::RET_SUCC;
                }
                break;
            default:
                if (!$multi)
                {
                    $this->msgId = 0;
                    $this->OperatorMsg = array(
                        'result'     => self::RET_ERR,
                        'codeStatus' => -1,
                        'codeDesc'   => $code[1],
                    );
                    return self::RET_ERR;
                }
                else
                {
                    foreach ($phone as $phoneLine)
                    {
                        $this->msgIdList[$phoneLine] = 0;
                        $this->operatorMsgList[$phoneLine] = array(
                            'result'     => self::RET_ERR,
                            'codeStatus' => -1,
                            'codeDesc'   => $code[1],
                        );
                    }
                    return self::RET_ERR;
                }
                break;
        }
    }

    /**
     * 相同内容多个手机号群发
     * @param array  $phone [phone1,phone2,phone3]
     * @param string $msg   message
     * @param string $sign  签名
     * @return int
     */
    public function multiPhoneSend($phone, $msg, $sign)
    {
        $this->send($phone, $msg, $sign);
    }

    /**
     * 获取 callback status 需要的配置文件, 和设定回调方法
     * @param array $channelConfig channelConfig
     * @return array
     */
    public static function callbackStatusConfig($channelConfig)
    {
        $params = array(
            'OperID'   => $channelConfig['username'],//用户名
            'OperPass' => $channelConfig['password'],//密码
        );
        $api = $channelConfig['api_url'];
        $query = http_build_query($params);
        $finalUrl = "{$api}?{$query}";

        return array(
            'url'           => $finalUrl,
            'data'          => '',
            'account'       => $channelConfig['username'],
            'action'        => 'get',
            'callbackClass' => '\\Smschannel\\GuoDuSmsChannelAction',
            'callbackFunc'  => 'callbackStatus',
            'callback'      => '',
        );
    }

    /**
     * 获取 callback status, 是回调方法
     * @param array $data data
     * @return boolean
     */
    public static function callbackStatus($data)
    {
        \WebLogger\Facade\LoggerFacade::info('callbackStatus', (array)$data);
        $str = str_replace(array(' ', "\n"), '', $data);
        $patternCode = '/<Category>(.*)<\/Category>/';
        preg_match($patternCode, $str, $code);
        $patternReport = '/<Report><SMSID>(.*)<\/SMSID><Type>.*<\/Type><DesMobile>(.*)<\/DesMobile><SendTime>(.*)<\/SendTime><RecvTime>(.*)<\/RecvTime><Status>(.*)<\/Status><Other>.*<\/Other><\/Report>/U';
        preg_match_all($patternReport, $str, $report);
        $res = true;
        $redisAction = \RedisAction::connect();
        foreach ($report[4] as $index => $status)
        {
            if ($report[1][$index] == 'null')
            {
                continue;
            }
            $dataInfo = array(
                'Mobile'    => $report[2][$index],//手机号码
                'Msg_Id'    => $report[1][$index],//订单号码
                'SendTime'  => $report[3][$index],//发送时间
                'RecvTime'  => $report[4][$index],//到达时间
                'TimeStamp' => preg_replace('((\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2}))', '$1-$2-$3 $4:$5:$6', $report[4][$index]),//到达时间格式化
            );
            if ($report[5][$index] === '0')
            {
                $dataInfo['Status'] = 'DELIVRD';
            }
            else
            {
                $dataInfo['Status'] = $report[5][$index];
            }
            $res &= $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($dataInfo));
        }
        return $res;
    }


    /**
     * 获取 callback status 需要的配置文件, 和设定回调方法
     * @param array $channelConfig channelConfig
     * @return array
     */
    public static function responseSMSConfig($channelConfig)
    {
        $params = array(
            'OperID'   => $channelConfig['username'],//用户名
            'OperPass' => $channelConfig['password'],//密码
        );
        $api = $channelConfig['api_url'];
        $query = http_build_query($params);
        $finalUrl = "{$api}?{$query}";

        return array(
            'url'           => $finalUrl,
            'data'          => '',
            'account'       => $channelConfig['username'],
            'action'        => 'get',
            'callbackClass' => '\\Smschannel\\GuoDuSmsChannelAction',
            'callbackFunc'  => 'responseSMS',
            'callback'      => '',
        );
    }

    /**
     * 获取 callback status, 是回调方法
     * @param string $data data
     * @return boolean
     */
    public static function responseSMS($data, $account = null)
    {
        $res = true;
        $redisAction = \RedisAction::connect();
        $lineArr = explode('|;|', $data);
        foreach ($lineArr as $line)
        {
            $msg = explode('|^|', $line);
            if (empty($msg[1]) || $msg[1] == 'null')
            {
                continue;
            }
            $upSmsInfo = array(
                'phone'      => $msg[1],//手机号码
                'msgId'      => time() . rand(100, 999),
                'is_unsub'   => false,
                'providerId' => 'GuoDu',
                'message'    => mb_convert_encoding($msg[2], 'UTF-8', 'GBK'),//信息内容
                'add_time'   => $msg[3],//收到的时间
            );
            if ($account)
            {
                $upSmsInfo['providerId'] .= "-{$account}";
            }
            if ($upSmsInfo['message'] === 'T' || $upSmsInfo['message'] === 'TD')
            {
                $upSmsInfo['is_unsub'] = true;
            }
            $res &= $redisAction->lPush('channelCallbackResponse', json_encode($upSmsInfo));
        }
        return $res;
    }
}
