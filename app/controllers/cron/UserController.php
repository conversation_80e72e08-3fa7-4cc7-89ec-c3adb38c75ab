<?php

includeBase('CronController');

/**
 * 用户帐号操作
 */
class UserController extends CronController
{
    /**
     * 功  能：
     * @return void
     */
    public function actionIndex()
    {
    }

    /**
     * 导出可疑用户信息
     * @return void
     */
    public function actionExport()
    {
        $usernames = file(APPPATH . "/files/username.csv");
        $users = array();
        $memberModel = loadModel('member');
        $phoneModel = loadModel('phone');
        foreach ($usernames as $username) {
            $username = trim($username);
            if ($username) {
                $row = $memberModel->getUserInfoByUsername($username);
                if ($row) {
                    $rowPhone = $phoneModel->getByPhone($row['phone']);
                    if ($rowPhone) {
                        $row['phone_time'] = strtotime($rowPhone['last_update']);
                    }
                    unset($row['uid'], $row['locked'], $row['email'], $row['email_status']);
                    $users[$row['id']] = "\"" . implode("\",\"", $row) . "\"";
                }
            }
        }
        file_put_contents(APPPATH . "/files/users.csv", implode("\n", $users));
    }
}
