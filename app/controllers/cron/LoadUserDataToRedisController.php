<?php

use Octopus\Logger\Handler\StreamHandler;
use Octopus\Logger;

includeBase('CronController');
/**
 * Class LoadUserDataToRedisController
 * 57数据库 root 的host修改由 172.1% 修改为 %
 */
class LoadUserDataToRedisController extends CronController
{
    //redis数量
    private $partCount = 3;
    //总进程数
    private $totalProcess = 10;
    //执行间歇步长
    private $sleepCount = 100000;
    //间歇时间
    private $sleepTime = 2;
    //每页查询数量
    private $pageSize = 5000;
    //用户数据hash_key
    private $userDataHashKey = 'dlx:memberInfo:shard';
    //最后处理的passId
    private $lastPassIdHashKey = 'dlx:memberInfo:lastPassId';
    //进程锁
    private $processLockHashKey = 'dlx:memberInfo:processLock';
    //最大处理时间
    private $maxExecuteTime = 3600 * 4;
    //config
    private $config;

    /**
     * LoadUserDataToRedisController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->config = Config::get('redis');
    }

    /**
     * actionIndex
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        global $cronArg;
        set_time_limit(0);
        $startTime = time();

        //1.获取参数(1:进程;2:是否全部重新加载)
        $process = isset($cronArg[0]) ? intval($cronArg[0]) : 1;
        if ($process <= 0 || $process > $this->totalProcess)
        {
            echo '参数错误,停止执行';
            exit();
        }

        //2.添加进程锁
        $redis = RedisEx::getInstance(); //初始化业务数据redis
        if ($redis->hExists($this->processLockHashKey, 'process_' . $process) && $redis->hGet($this->processLockHashKey, 'process_' . $process) == 1)
        {
            echo '进程[' . $process . ']已被锁,停止执行';
            exit();
        }
        //添加进程锁
        $redis->hSet($this->processLockHashKey, 'process_' . $process, 1);

        //3.获取日志
        $logger = $this->getLogger("loadUserDataRedis", "loadUserDataToRedis_" . $process);
        $logger->info('进程[' . $process . ']开始执行..');

        //4.查询总数,分配每个进程执行数量 (总数需要找出一个过去的ID,或者过去的某个时间点)
        $memberModel = loadModel('member');
        $endMemebersId = 400220865;
        $totalCount = $memberModel->getMembersCount($endMemebersId);
        $everyProcessTotal = floor($totalCount / $this->totalProcess);
        $logger->info('members表一共有[' . $totalCount . ']条数据处理(id<' . $endMemebersId . '), 当前进程分配到[' . $everyProcessTotal . ']条数据..');

        if ($everyProcessTotal <= 0)
        {
            $str = '当前进程分配到的数据为0,停止执行..';
            $logger->info($str);
            echo $str;
            exit();
        }

        //5.执行变量 初始化
        $loadedCount = 0; //共加载
        $successCount = 0; //成功数量
        $failedCount = 0;  //失败数量
        $lastPassId = 0; //最后处理的Id

        if ($process == $this->totalProcess)
        {
            $processCount = $totalCount - ($everyProcessTotal * ($this->totalProcess - 1));
        }
        else
        {
            $processCount = $everyProcessTotal; //当前进程需要处理的数据条数,默认是分配的所有数据
        }

        $userDataRedisArray = $this->initUserDataRedis(); //初始化用户数据redis连接
        if (!$userDataRedisArray || count($userDataRedisArray) < 3)
        {
            $redis->hDel($this->processLockHashKey, 'process_' . $process);
            $str = '用户数据redis链接失败, 停止执行';
            $logger->info($str);
            $this->clearConnection($userDataRedisArray);
            exit();
        }

        //7.计算出总页数
        if ($this->pageSize > $processCount)
        {
            $pageCount = 1;
            $this->pageSize = $processCount;
        }
        else
        {
            $pageCount = ceil($processCount / $this->pageSize); //总页数
        }

        if ($process == $this->totalProcess)
        {
            $offset = ($process - 1) * $everyProcessTotal;
        }
        else
        {
            $offset = ($process - 1) * $processCount; //进程处理数据的起始位置
        }

        $logger->info('总页数:['. $pageCount . '], 起始位置:[' . $offset . ']..');

        //8.分页处理
        for ($i = 0; $i < $pageCount; $i++)
        {
            $logger->info('当前处理页:[' . ($i + 1) . ']..');
            try
            {
                if (!$redis)
                {
                    $redis = RedisEx::getInstance();
                }
                if (time() - $startTime > $this->maxExecuteTime)
                {
                    $this->clearConnection();
                    $redis->hDel($this->processLockHashKey, 'process_' . $process);
                    $str = '当前进程脚本执行时间已超过最大时间限制:' . $this->maxExecuteTime . ', 停止执行';
                    $logger->info($str);
                    unset($userDataRedisArray);
                    exit();
                }
                if ($i == ($pageCount - 1))
                {
                    $tmpPageSize = $processCount - (($pageCount - 1) * $this->pageSize);
                }
                else
                {
                    $tmpPageSize = $this->pageSize;
                }
                $memberList = $memberModel->getUserListByPage($i, $tmpPageSize, $offset, $this->pageSize);
                foreach ($memberList as $key => $memberInfo)
                {
                    $lastPassId = $memberInfo['passId']; //passId作为hashKey
                    $info = serialize($memberInfo); //用户信息序列化后存储
                    $hashIndex = calcHashRedis($lastPassId); //计算hashKey

                    if (isset($userDataRedisArray[$hashIndex]) && $userDataRedisArray[$hashIndex])
                    {
                        $udRedis = $userDataRedisArray[$hashIndex]; //3台redis
                    }
                    else
                    {
                        $userDataRedisArray = $this->initUserDataRedis();
                        if (!$userDataRedisArray || count($userDataRedisArray) < 3)
                        {
                            $redis->hDel($this->processLockHashKey, 'process_' . $process);
                            $str = '用户数据redis链接失败, 停止执行';
                            $logger->info($str);
                            $this->clearConnection($userDataRedisArray);
                            exit();
                        }
                        $udRedis = $userDataRedisArray[$hashIndex]; //3台redis
                    }

                    $setResult = $udRedis->hSet($this->userDataHashKey, $lastPassId, $info);

                    $loadedCount++;
                    if (!$setResult)
                    {
                        $failedCount++;
                    }
                    else
                    {
                        $successCount++;
                    }
                }

                if ($loadedCount >= $this->sleepCount)
                {
                    $logger->info('当前进程执行了[' . $loadedCount . ']条数据了,休息一下..');
                    $this->clearConnection($userDataRedisArray);
                    unset($userDataRedisArray);
                    sleep($this->sleepTime);
                }
            }
            catch (Exception $ex)
            {
                echo $ex;
                $redis->hSet($this->lastPassIdHashKey, 'process' . $process, $lastPassId);
                $logger->info('当前进程执行到id:[' . $lastPassId . ']后出现 Error..');
                $this->clearConnection($userDataRedisArray);
                unset($userDataRedisArray);
                break;
            }
            catch (Error $er)
            {
                echo $er;
                $redis->hSet($this->lastPassIdHashKey, 'process' . $process, $lastPassId);
                $logger->info('当前进程执行到id:[' . $lastPassId . ']后出现 Error..');
                $this->clearConnection($userDataRedisArray);
                unset($userDataRedisArray);
                break;
            }
            finally
            {
                $this->clearConnection($userDataRedisArray);
                unset($userDataRedisArray);
            }
        }
        //删除进程锁
        $redis->hDel($this->processLockHashKey, 'process_' . $process);

        $redis->hSet($this->lastPassIdHashKey, 'process' . $process, $lastPassId);
        $finishStr = sprintf("执行结束,共执行[%s]条,成功[%s]条,失败[%s]条.", $loadedCount, $successCount, $failedCount);
        $logger->info($finishStr);
        echo $finishStr;
    }

    /**
     * clearConnection
     * -
     * @param array $userDataRedisArray userDataRedisArray
     * @return void
     * <AUTHOR>
     */
    private function clearConnection($userDataRedisArray)
    {
        PdoEx::delInstance(DB_PASSPORT);
        RedisEx::delInstance();
        foreach ($userDataRedisArray as $redisConnection)
        {
            $redisConnection->close();
        }
    }

    /**
     * initUserDataRedis
     * -
     * @return array
     * <AUTHOR>
     */
    private function initUserDataRedis()
    {
        $userDataRedis = array();
        $dlxRedis = $this->config['dlx_members'];
        foreach ($dlxRedis as $key => $redisInfo)
        {
            if (!empty($redisInfo['master']))
            {
                $tmpRedis = new Redis();
                $tmpRedis->connect($redisInfo['master']['host'], $redisInfo['master']['port'], 3);
                $tmpRedis->auth($redisInfo['master']['auth']);
                if ($tmpRedis)
                {
                    $userDataRedis[$key] = $tmpRedis;
                }
            }
        }
        return $userDataRedis;
    }
}
