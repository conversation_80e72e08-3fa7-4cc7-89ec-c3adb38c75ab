<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="gbk">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>头像昵称配置</title>
    <!-- Bootstrap -->
    <link href="/css/UnionLogin/bootstrap.min.css" rel="stylesheet">
    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="/js/houtai_gl/unionLogin/html5shiv.min.js"></script>
    <script src="/js/houtai_gl/unionLogin/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<div class="container">
    <!--面包屑导航 开始-->
    <div class="location">位置：<a href="#" target="_blank">头像昵称管理</a>  &gt; <span>生态列表</span></div>
    <!--面包屑导航 结束-->
    <div id="app" style="display: none;">

        <form class="form-inline">
            <div class="form-group">
                <label >关键字</label>
                <input type="text" v-model="keywords"  id="keywords" class="form-control"  placeholder="请输入关键字">
            </div>
            <button type="button" class="btn btn-primary" @click="search" >搜索</button>
            <a  href="/houtai_gl/UnionLogin/EditGroupConfig" class="btn btn-success text-right" role="button">新增生态</a>
        </form>

        <template id="list">
            <div class="row" style="margin: 10px">
<!--                <div class="text-left">-->
<!--                    <a href="/houtai_gl/UnionLogin/EditGroupConfig" class="btn btn-success" role="button">新增生态</a>-->
<!--                </div>-->
            </div>
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-hover">
                        <tr>
                            <th>生态名称</th>
                            <th>关联应用</th>
                            <th>第三方头像</th>
                            <th>头像审核规则</th>
                            <th>昵称默认文案</th>
                            <th>昵称校验规则</th>
                            <th style="text-align: center">操作</th>
                        </tr>
                        <template v-for="(item, index) in list">
                            <tr>
                                <td><{item.groupName}></td>
                                <td>
                                    <p v-for="mid,i in item.midList"><{mid}></p>
                                </td>
                                <td>
                                    <p v-if="item.fromTriplicate == 1">可获取</p>
                                    <p v-else>不获取</p>
                                </td>
                                <td>
                                    <p v-if="item.auditFirst == 1">先审后发</p>
                                    <p v-else>先发后审</p>
                                </td>
                                <td><{item.tips}></td>
                                <td>
                                    <p v-if="item.duplication ==  1">允许重复</p>
                                    <p v-else>不允许重复</p>


                                    <p v-if="item.specialChar == 1">允许特殊字符</p>
                                    <p v-else>不允许特殊字符</p>


                                    <p v-if="item.allowMidSpace == 1">允许中间空格</p>
                                    <p v-else>不允许中间空格</p>
                                    <p>长度限制</p>

                                </td>
                                <td style="text-align: center">
                                    <a :href="`/houtai_gl/UnionLogin/EditGroupConfig?groupName=${item.groupName}`" class="btn btn-link">编辑</a>
                                </td>
                            </tr>
                        </template>
                    </table>
                </div>
            </div>
        </template>
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="/js/houtai_gl/unionLogin/jquery.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="/js/houtai_gl/unionLogin/bootstrap.min.js"></script>
<script src="/js/houtai_gl/layer/layer.js"></script>
<script src="/js/houtai_gl/vue/vue{{if $smarty.const.RUNMODE eq 'production'}}.min{{/if}}.js"></script>
<script>
    var mids = JSON.parse('{{$pageArray.mid}}');
    var app = new Vue({
        el: '#app',
        delimiters: ["<{", "}>"],
        data: {
            id: 0,
            list: [],
            mids: mids,
            keywords:""
        },
        methods: {
            init: function () {
                var me = this;
                me.pullList();
            },
            pullList: function () {
                var me = this;
                $.ajax({
                    url: '/houtai_gl/GroupConfigApi',
                    type: 'post',
                    success: function (response) {
                        var status = response.code;
                        if (status == 200) {
                            me.list = response.data;
                        }
                    }
                })
            },
            search: function () {
                var me = this;
                $.ajax({
                    url: '/houtai_gl/GroupConfigApi',
                    type: 'get',
                    data: {"keywords" : me.keywords},
                    success: function (response) {
                        var status = response.code;
                        if (status == 200) {
                            me.list = response.data;
                        }
                    }
                })
            },
        },
        mounted: function () {
            var me = this;
            $('#app').show();
            me.init();
        }
    })
</script>
</body>
</html>