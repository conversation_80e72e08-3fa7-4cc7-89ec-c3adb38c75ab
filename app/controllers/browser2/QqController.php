<?php

use Service\Avatar\Avatar;

class QqController extends Controller
{
    /**
     * 功  能：
     * @return void
     */
    public function actionIndex()
    {
        if (isset($_GET['forward'])) {
            $forward = getForwardUrl();
            setcookie('oauth_forward', $forward, 0, '/');
        }
        redirect(LOGIN_HOST . '/oauth/qq?callback=/browser2/qq/callback');
    }

    /**
     * 功  能：
     * @return void
     */
    public function actionCallback()
    {
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis) {
            $redis = RedisEx::getInstance();
        }
        setcookie('oauth_state', null, time() - 3600, '/');
        setcookie('redirect_uri', null, time() - 3600, '/');
        setcookie('oauth_forward', null, time() - 3600, '/');
        if (empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state']) {
            redirect('/');
        }
        $oauthAction = loadAction('oauth');
        $result = $oauthAction->qqCallback($_REQUEST['code'], $_COOKIE["redirect_uri"]);
        if ($result) {
            $openid = $result['openid'];
            $nickname = $result['nickname'];
            $figureurl = $result['figureurl'];
            $gender = $result['gender'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'qq');
            $source = \Service\AccountStat\Consts\AccountStat::SRC_IE_BROWSER2;
            if ($return["passid"] > 0) {
                $passid = $return["passid"];
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $result = $memberModel->read($passid);
                if ($result['gender'] == 0 && $gender != 0) {
                    $memberModel->edit($passid, array('gender' => $gender));
                }
                $result["token"] = $memberModel->getToken($passid);
                $username = $result['username'];
                $nickname = $username;
                $bind = unserialize($result["m_uid"]);
                $uid = $bind['1'];
                if (strpos($username, "#qq#") !== false) {
                    $usernameOld = $username;
                    $username = $memberModel->repairOldOAuthUser($passid, 'qq');
                    if ($username) {
                        $nickname = $username;
                        $memberModel->patchUserLog($passid, array(
                            'username_old' => $usernameOld,
                            'username' => $username,
                            'op_time' => time(),
                            'op_ip' => get_client_ip(),
                            'op_domain' => 'login.2345.com'
                        ));
                        noticeToChange($passid, 'changeUsername', array(
                            'passid' => $passid,
                            'uid' => $uid,
                            'type' => 'username',
                            'value' => $username
                        ));
                    } else {
                        $username = $usernameOld;
                        $nickname = explode("#qq#", $username);
                        $nickname = $nickname[0];
                    }
                }
            } else {
                $nickname = str_replace(array("#qq#", "#weibo#"), "", $nickname);
                $nickname = trim($nickname);
                if (!$nickname) {
                    $nickname = "qzuser_" . substr(md5(microtime()), 0, 6);
                }
                $clientIp = get_client_ip();
                $result = $memberModel->regOAuth('qq', $openid, $nickname, $clientIp, '', $gender);
                if (!$result) {
                    $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
                    $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
                    $accountType = \Service\AccountStat\Consts\AccountStat::AC_TP_OAUTH_QQ;
                    \Service\AccountStat\AccountStat::collect($type, $clientType, $accountType, 0, $source);

                    closeWindow();
                }
                $passid = $result["passid"];
                $uid = $result["uid"];
                $username = $result['username'];
                $nickname = $username;
                if ($isUseRedis) {
                    $redis->incr("regOAuthSuccNum");
                    $redis->incr('RSN:qq_browser_login_2345_com');
                }
            }

            $st = 1;
            if ($return["passid"] > 0) {
                $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
            } else {
                $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
            }
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
            $accountType = \Service\AccountStat\Consts\AccountStat::AC_TP_OAUTH_QQ;
            \Service\AccountStat\AccountStat::collect($type, $clientType, $accountType, $st, $source);

            $sec = get_sec_browser($uid);
            $token = $result["token"];
            if ($isUseRedis) {
                $redis->incr('LSN:qq_browser_login_2345_com');
            }
            echo '<script type="text/javascript">window.external.RCCoralOnLogin("qq","' . $username . '","' . $nickname . '",0,"","' . $uid . '","' . $sec . '","' . $passid . '","' . $token . '");window.external.RCCoralCheckIsFristLogin();</script>';
            if ($_COOKIE['oauth_forward']) {
                echo '<script type="text/javascript">window.location = "' . str_replace("\"", "'", $_COOKIE['oauth_forward']) . '";</script>';
            } else {
                echo '<script type="text/javascript">window.location = "' . LOGIN_HOST . '/browser2/qq/success";</script>';
            }
        } else {
            if ($isUseRedis) {
                $redis->incr('LFN:qq_browser_login_2345_com');
            }
            closeWindow();
        }
    }

    /**
     * 功  能：
     * @return void
     */
    public function actionSuccess()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST["act"]) && $_POST["act"] == "done") {
            closeWindow();
        } else {
            $pageArray["type"] = "qq";
            loadView("browser2/success.tpl.html", $pageArray);
        }
    }
}
