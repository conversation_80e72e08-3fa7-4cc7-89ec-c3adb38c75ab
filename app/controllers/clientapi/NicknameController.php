<?php
use Common\Utils\Encoding;
use Service\Encryption\Sign\SignManager;
use Service\UserBase\Rules;
use Service\UserBase\TipsMap;

includeBase('NController');

/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/4/29
 * Time: 17:19
 */
class NicknameController extends NController
{

    private $authInfo = [];

    /**
     * NicknameController constructor.
     * @return void
     */
    public function __construct()
    {
        $loginAction = loadAction('login');
        $this->authInfo = $loginAction->checkAuthCookie('i');
        if (!$this->authInfo) {
            $this->codeResponse("403", "", "未登录，请先进行登录");
        }
    }


    /**
     * 获取头像昵称接口
     * @version
     */
    public function actionGet()
    {
        $necessaryField = self::getNeedField(
            [
                'mid',
                SignManager::$paramSignName,
            ]
        );
        list($mid) = $this->getParamAndAutoDeny($necessaryField);
        $passId = $this->authInfo['i'];
        $userBaseAction = loadAction('UserBase');
        $warningAction = loadAction('Warning');
        $warningAction->setRequestInfo(
            [
                'dir_type'    => 'r_nickname',
                'type'        => 'query',
                'path'        => '/clientapi/nickname/get',
                'source'      => $mid,
                'modifyError' => '',
                'passid'      => $passId,
            ]
        );
        try {
            $groupName = Rules::getGroupNameByMid($mid);
            if (!empty($groupName)) {
                $userBaseModel = loadModel('UserBase');
                $userInfo = $userBaseModel->getAvatarNicknameInfoByPassId($mid, $passId, '', true);
                list($nickname) = Rules::getAvatarNicknameByRule($mid, $passId, 1, $userInfo, 'nickname', 'big', true);
                $nicknameSource = !empty($userInfo['nickname_source']) ? $userInfo['nickname_source'] : 'default';
                $warningAction->setRequestInfo(['ecotype' => 1, 'nicknameSource' => $nicknameSource]);
                $output = [
                    'nickname' => $nickname,
                ];
                $this->codeResponse(200, $output, "获取成功");
            } else {
                $warningAction->setRequestInfo(['ecotype' => 2]);
                $memberModel = loadModel('member');
                $info = $memberModel->getNicknameByPassid($passId);
                $output = [
                    'nickname' => Encoding::transcoding($info['nickname'], 'gbk', 'utf-8'),
                ];
                $this->codeResponse(200, $output, "获取成功");
            }
        }
        catch (Exception $exception) {
            $warningAction->setRequestInfo(['queryError' => $exception->getMessage()]);
            $this->codeResponse(TipsMap::ERRORNICKNAMEMODIFYFAILED['code'], "", TipsMap::ERRORNICKNAMEMODIFYFAILED['msg']);
        }
    }

    /**
     * 修改昵称接口
     * @return void
     * @throws Exception
     */
    public function actionModify()
    {
        $needField = self::getNeedField(
            [
                'mid',
                'nickname',
                SignManager::$paramSignName,
            ]
        );
        list($mid, $nickname) = $this->getParamAndAutoDeny($needField);
        $errorMsg = [];
        $warningAction = loadAction('Warning');
        $warningAction->setRequestInfo(
            [
                'dir_type'    => 'w_nickname',
                'type'        => 'update',
                'path'        => '/clientapi/nickname/Modify',
                'source'      => $mid,
                'modifyError' => '',
                'passid'      => $this->authInfo['i'],
            ]
        );
        try {
            $groupName = Rules::getGroupNameByMid($mid);
            $nickname = trim(base64_decode($nickname));

            $passid = $this->authInfo['i'];
            $memberModel = loadModel('member');
            $userInfo = $memberModel->read($passid);
            $userInfo['passid'] = $passid;
            $objLogV2Action = loadAction('logV2');

            if (!empty($groupName)) {
                $warningAction->setRequestInfo(['ecotype' => 1]);
                if (Rules::checkNickname($mid, $nickname, $errorMsg)) {
                    $userBaseModel = loadModel('UserBase');

                    $arrOldNickName = $userBaseModel->getAvatarNicknameInfoByPassId($mid, $passid, '', true);
                    list($strOldNickName) = Rules::getAvatarNicknameByRule($mid, $passid, 1, $arrOldNickName, 'nickname', 'big', true);
                    $strOldNickName = Encoding::transcoding($strOldNickName, 'utf-8', 'gbk');

                    $isUpdate = $userBaseModel->setNicknameInfo($mid, $this->authInfo['i'], $nickname, $errorMsg, [], 'upload');
                    if ($isUpdate) {
                        // 更新昵称
                        $objLogV2Action->report($mid, $strOldNickName ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD', 'NICKNAME', $userInfo, $strOldNickName, Encoding::transcoding($nickname));
                        $this->codeResponse("200", "", "昵称更新成功");
                    } else {
                        $this->codeResponse($errorMsg["code"], "", $errorMsg['msg']);
                    }
                } else {
                    $this->codeResponse($errorMsg["code"], "", $errorMsg['msg']);
                }
            } else {
                $warningAction->setRequestInfo(['ecotype' => 2]);
                if (Rules::checkOldNickname($mid, $nickname, $errorMsg)) {
                    $nickname = Encoding::transcoding($nickname);
                    $userBaseModel = loadModel('UserBase');

                    $arrOldNickName = $memberModel->getNicknameByPassid($passid);
                    $strOldNickName = $arrOldNickName['nickname'];

                    $isUpdate = $userBaseModel->setOldNicknameInfo($mid, $this->authInfo['i'], $nickname, $errorMsg);
                    if ($isUpdate) {
                        // 更新昵称
                        $objLogV2Action->report($mid, $strOldNickName ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD', 'NICKNAME', $userInfo, $strOldNickName, $nickname);
                        $this->codeResponse("200", "", "昵称更新成功");
                    } else {
                        $this->codeResponse($errorMsg["code"], "", $errorMsg['msg']);
                    }
                } else {
                    $this->codeResponse($errorMsg["code"], "", $errorMsg['msg']);
                }
            }
        }
        catch (Exception $exception) {
            $warningAction->setRequestInfo(['modifyError' => $exception->getMessage()]);
            $this->codeResponse(TipsMap::ERRORNICKNAMEMODIFYFAILED['code'], "", TipsMap::ERRORNICKNAMEMODIFYFAILED['msg']);
        }
    }
}
