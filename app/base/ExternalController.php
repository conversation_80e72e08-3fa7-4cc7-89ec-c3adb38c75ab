<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/5
 * Time: 14:11
 */

class ExternalController extends Controller
{
    /**
     * ExternalController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        if (RUNMODE != 'development' && !HTTPS)
        {
            $this->codeResponse(4000, "", '请求拒绝');
        }
    }

    /**
     * uParamFilter
     * -
     * @param array $params Params
     * @return array|bool
     * <AUTHOR>
     */
    protected function uParamFilter($params)
    {
        static $uFilter;
        $returnArray = array();
        $filterResult = true;
        if (!$uFilter)
        {
            $uFilter = new \Common\Utils\UFilter();
        }
        foreach ($params as $name => $value)
        {
            $methodName = $name . 'Filter';
            if (method_exists($uFilter, $methodName))
            {
                if ($name == \Service\Encryption\Sign\SignManager::$paramSignName)
                {
                    $fValue = \Common\Validator\Params::checkSign($value, $params);
                }
                else
                {
                    $fValue = $uFilter->$methodName($value);
                }

                if ($fValue === null)
                {
                    $filterResult = false;
                    break;
                }
                else
                {
                    $returnArray[] = $fValue;
                }
            }
            else
            {
                $returnArray[] = $value;
            }
        }
        if ($filterResult)
        {
            return $returnArray;
        }
        else
        {
            return false;
        }
    }

}
