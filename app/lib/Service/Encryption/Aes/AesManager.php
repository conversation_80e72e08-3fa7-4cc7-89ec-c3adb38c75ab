<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/7/2
 * Time: 19:18
 */

namespace Service\Encryption\Aes;

use WebLogger\Facade\LoggerFacade;

class AesManager
{
    private $key = "owjLyPGHmBY7a1qq";

    private $iv = "UcXc0UyMASBX3Ylc";

    /**
     * AesManager constructor.
     * -
     * @param string $key  密钥
     * @param string $iv  偏移量
     * @return void
     */
    public function __construct($key = '', $iv = '')
    {
        if (!empty($key))
        {
            $this->key = $key;
        }
        if (!empty($iv))
        {
            $this->iv = $iv;
        }
    }

    /**
     * pkcs7补码
     * -
     * @param string $string  明文
     * @param int $blocksize Blocksize , 以 byte 为单位
     * @return String
     */
    private function addPkcs7Padding($string, $blocksize = 32)
    {
        !is_string($string) && $string = null;
        $len = strlen($string); //取得字符串长度
        $pad = $blocksize - ($len % $blocksize); //取得补码的长度
        $string .= str_repeat(chr($pad), $pad); //用ASCII码为补码长度的字符， 补足最后一段
        return $string;
    }

    /**
     * 加密然后base64转码
     * -
     * @param String $str  明文
     * @return String
     */
    public function aes256cbcEncrypt($str)
    {
        return base64_encode(openssl_encrypt($str, 'aes-256-cbc', $this->key, 1, $this->iv));
    }

    /**
     * 除去pkcs7 padding
     * -
     * @param String $string 解密后的结果
     * @return String
     */
    private function stripPkcs7Padding($string)
    {
        $slast = ord(substr($string, -1));
        $slastc = chr($slast);
        $pcheck = substr($string, - $slast);
        if (preg_match("/$slastc{".$slast."}/", $string))
        {
            $string = substr($string, 0, strlen($string) - $slast);
            return $string;
        }
        else
        {
            return false;
        }
    }
    /**
     * 解密
     * -
     * @param String $encryptedText 二进制的密文
     * @return String
     */
    public function aes256cbcDecrypt($encryptedText)
    {
        $encryptedText = base64_decode($encryptedText);
        return openssl_decrypt($encryptedText, 'aes-256-cbc', $this->key, 1, $this->iv);
    }

    /**
     * 解密
     * -
     * @param String $encryptedText 二进制的密文
     * @return String
     */
    public function aes128cbcDecrypt($encryptedText)
    {
        $encryptedText = base64_decode($encryptedText);
        return openssl_decrypt($encryptedText, 'aes-128-cbc', $this->key, 1, $this->iv);
    }

    /**
     * 十六进制转换字符串
     * @param string $hex 十六进制
     * @return string
     */
    public function hexToStr($hex)
    {
        $string = "";
        for ($i = 0; $i < strlen($hex) - 1; $i += 2)
        {
            $string .= chr(hexdec($hex[$i] . $hex[$i + 1]));
        }
        return  $string;
    }

    /**
     * 字符串转十六进制
     * -
     * @param string $string 字符串转十六进制
     * @return string
     */
    public function strToHex($string)
    {
        $hex = "";
        $tmp = "";
        for ($i = 0; $i < strlen($string); $i++)
        {
            $tmp = dechex(ord($string[$i]));
            $hex .= strlen($tmp) == 1 ? "0" . $tmp : $tmp;
        }
        $hex = strtoupper($hex);
        return $hex;
    }

    /**
     * 解密
     * -
     * @param String $encryptedText 二进制的密文
     * @return String
     */
    public function aes128cbcHexDecrypt($encryptedText)
    {
        $str = $this->hexToStr($encryptedText);
        return openssl_decrypt($str, 'aes-128-cbc', $this->key, 1, $this->iv);
    }

    /**
     * 加密
     * -
     * @param String $str 加密字符串
     * @return String
     */
    public function aes128cbcEncrypt($str)
    {
        $base = openssl_encrypt($str, 'aes-128-cbc', $this->key, 1, $this->iv);
        return $this->strToHex($base);
    }

    /**
     * 解密
     * -
     * @param String $encryptedText base64的密文
     * @return String|bool
     */
    public function aes128cbcBase64Decrypt($encryptedText)
    {
        return openssl_decrypt(base64_decode($encryptedText), 'aes-128-cbc', $this->key, 1, $this->iv);
    }
}
