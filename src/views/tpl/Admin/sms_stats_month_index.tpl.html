<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信通道-月度短信对账</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信通道</a>  >  <span>月度短信对账</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="/Admin/SmsStatsMonth/Index">月度短信对账</a>
    </div>
    <!--Tab菜单 结束-->

    <!--日期表单 开始-->
    <div class="funList mt20 clearfix">
        <form action="/Admin/SmsStatsMonth/Index" method="post" id="queryForm">
            <div class="clearfix">
                <div class="dataWrap clearfix">
                    <em>账单月份：</em>
                    <div class="dataList w130">
                        <div class="getTime"><input type="text" value="{{$pageArray.show_month}}" autocomplete="off" name="show_month" onclick="WdatePicker({dateFmt: 'yyyy-MM', minDate: '{{$pageArray.min_month}}', maxDate: '{{$pageArray.max_month}}'})"/><i>日历icon</i></div>
                    </div>
                </div>
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 querySubmit">查询</a>
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml20 exportData">导出</a>
                {{if $pageArray.max_month == $pageArray.show_month && $pageArray.can_check}}
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml20 checkData">校对</a>
                {{/if}}
            </div>
        </form>
    </div>
    <!--日期表单 结束-->

    <!--分割 开始-->
    <div class="mt20" style="height:30px;background-color:#d6e4f7;color:#4c6180;font-weight: bold;vertical-align: inherit;line-height:  30px;padding-left: 10px;">
        对账配置
    </div>
    <!--分割 结束-->

    <!--配置列表 开始-->
    <div class="contentWrap" style="overflow-x: scroll;">
        <table class="tabMod forFixed" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
            <tr>
                <th>月份</th>
                <th>服务商</th>
                <th>结算条数</th>
                <th>结算单价</th>
                <th>结算总价</th>
                <th>操作人员</th>
                <th>操作时间</th>
            </tr>
            {{foreach from=$pageArray.provider key=cate item=name}}
            <tr>
                <td>{{$pageArray.month}}</td>
                <td>{{$cate}}-{{$name}}</td>
                {{if isset($pageArray.config.$cate)}}
                <td>{{$pageArray.config.$cate['settle_count']}}</td>
                <td>{{$pageArray.config.$cate['price']}}</td>
                <td>{{$pageArray.config.$cate['cost']}}</td>
                <td>{{$pageArray.config.$cate['operator']}}</td>
                <td>{{$pageArray.config.$cate['mtime']}}</td>
                {{else}}
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                {{/if}}
            </tr>
            {{/foreach}}
        </table>
    </div>
    <!--配置列表 end-->

    {{foreach from=$pageArray.data key=cate item=list}}
    {{if $list}}
    <!--分割 开始-->
    <div class="mt20" style="height:30px;background-color:#d6e4f7;color:#4c6180;font-weight: bold;vertical-align: inherit;line-height:  30px;padding-left: 10px;">
        {{$cate}}-{{if isset($pageArray.provider[$cate])}}{{$pageArray.provider[$cate]}}{{else}}未知服务商{{/if}}&nbsp;&nbsp;数据
    </div>
    <!--分割 结束-->

    <!--数据列表 开始-->
    <div class="contentWrap" style="overflow-x: scroll;">
        <table class="tabMod forFixed" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
            <tr>
                <th>月份</th>
                <th>OA项目</th>
                <th>服务商</th>
                <th>发送条数</th>
                <th>计费条数</th>
                <th>结算条数</th>
                <th>结算单价</th>
                <th>结算总价</th>
                <th>校对状态</th>
                <th>备注</th>
                <th>操作人员</th>
                <th>操作时间</th>
            </tr>
            {{foreach from=$list item=info}}
            <tr>
                <td>{{$info.month}}</td>
                <td>{{$info.oa_project_id}}-{{$info.oa_project_name}}</td>
                <td>{{if isset($pageArray.provider[$info.provider_cate])}}{{$pageArray.provider[$info.provider_cate]}}{{else}}未知服务商{{/if}}</td>
                <td>{{$info.send_count}}</td>
                <td>{{$info.charge_count}}</td>
                <td>{{$info.settle_count}}</td>
                <td>{{$info.price}}</td>
                <td>{{$info.cost}}</td>
                <td>{{if $info.status == 1}}<font color="green"><b>校对完成</b></font>{{else if $info.status == 0}}<font color="red"><b>未校对</b></font>{{else}}<font color="#8b0000"><b>未知</b></font>{{/if}}</td>
                <td>{{$info.remark}}</td>
                <td>{{$info.operator}}</td>
                <td>{{$info.mtime}}</td>
            </tr>
            {{/foreach}}
            <tr>
                <td></td>
                <td>汇总</td>
                <td></td>
                <td>{{$pageArray.total[$cate]['send_count']}}</td>
                <td>{{$pageArray.total[$cate]['charge_count']}}</td>
                <td>{{$pageArray.total[$cate]['settle_count']}}</td>
                <td>{{$pageArray.total[$cate]['price']}}</td>
                <td>{{$pageArray.total[$cate]['cost']}}</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </table>
    </div>
    <!--数据列表 end-->
    {{/if}}
    {{/foreach}}

</div>
<script src="/style/js/jquery-1.8.3.min.js"></script>
<script src="/style/js/tableFixed.js"></script>
<script>
    $('.querySubmit').click(function () {
        $(this).html('正在查询');
        $(this).css('cursor','not-allowed');
        $(this).css('background','#acc0dc');
        $('#queryForm').submit();
    });

    $('.exportData').click(function () {
        var link = '/Admin/SmsStatsMonth/Index?act=export&' + $('#queryForm').serialize();
        window.open(link);
    });

    $('.checkData').click(function () {
        var link = '/Admin/SmsStatsMonth/Check?' + $('#queryForm').serialize();
        window.location.href = link;
    });

</script>
</body>
</html>
