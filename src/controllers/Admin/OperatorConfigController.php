<?php

namespace Admin;

use Controller;

class OperatorConfigController extends Controller
{
    public function actionIndex()
    {
        $pageArray = array();
        $orderBy = $this->Filter->getPost('orderBy');
        $orderByType = $this->Filter->getPost('type');
        if (!in_array(strtolower($orderBy), ['id', 'display_order']))
        {
            $orderBy = 'display_order';
        }

        if (!in_array(strtolower($orderByType), ['asc', 'desc']))
        {
            $orderByType = 'desc';
        }
        $OperatorConfigModel = OperatorConfigModel::getInstance();
        $pageArray['OperatorName'] = $this->Filter->getPost('OperatorName');
        $OperatorName = !empty($pageArray['OperatorName']) ? \EncodingAction::transcoding($pageArray['OperatorName']) : '';
        $queryData = array(
            'OperatorName' => $OperatorName,
        );
        $pageArray['provider_cate_list'] = OperatorConfigModel::PROVIDER_CATE;
        $pageArray['operatorList'] = $OperatorConfigModel->getOperatorList($queryData, $orderBy, $orderByType);
        $pageArray['orderBy'] = $orderBy;
        $pageArray['orderByType'] = $orderByType;
        loadView('Admin/operator_config_index.tpl.html', $pageArray);
    }

    public function actionChangeOperatorConfig()
    {
        $operatorForm = $this->Filter->getPost('operatorForm');
        $operatorForm['actioType'] = $this->Filter->getPost('actioType');
        if (empty($operatorForm['provider_name']) || empty($operatorForm['action_name']) || empty($operatorForm['actioType']))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        if (!in_array($operatorForm['actioType'], array('add', 'edit')))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '类型不正确'));
        }
        $operatorForm['provider_name'] = \EncodingAction::transcoding($operatorForm['provider_name']);
        $operatorForm['description'] = \EncodingAction::transcoding($operatorForm['description']);
        $operatorForm['sign'] = \EncodingAction::transcoding($operatorForm['sign']);
        $monitor_config = json_decode($operatorForm['monitor_config'], true);
        if (!isset($monitor_config['callbackRate'], $monitor_config['stepTime'], $monitor_config['offsetTime']))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '预警信息必须是合法的json'));
        }
        preg_match('/^([0-2][\d]:[0-5][\d])([+-])([0-2][\d]:[0-5][\d])$/', $operatorForm['timeQuantum'], $timeMatch);
        if (!isset($timeMatch[0]))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '可用时间段必须符合格式'));
        }

        if (!empty($operatorForm['unit_price']) && !is_numeric($operatorForm['unit_price']))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '单价必须是小数格式'));
        }

        $OperatorConfigModel = OperatorConfigModel::getInstance();
        $isTrue = $OperatorConfigModel->ChangeOperatorConfig($operatorForm);
        if ($isTrue)
        {
            $redisKey = \Config_CacheKey::BI_DATA_SMS_PROVIDER_ACCOUNT;
            $redis = \RedisAction::connect();
            $redis->del($redisKey);
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '设置成功'));
        }
        else
        {
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '设置失败'));
        }
    }

    public function actionGetOperatorConfigInfo()
    {
        $id = (int)$this->Filter->getPost('id');
        if (empty($id))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        $OperatorConfigModel = OperatorConfigModel::getInstance();
        $OperatorInfo = $OperatorConfigModel->getOperatorList(array('id' => $id, 'row' => true));
        return \MsgAction::returnJsonData(array('status' => 'P00001', 'data' => $OperatorInfo));
    }

    public function actionChangeOperatorStatus()
    {
        $id = (int)$this->Filter->getPost('id');
        $operatorStatus = (int)$this->Filter->getPost('operatorStatus');
        $operatorStatus = !empty($operatorStatus) ? $operatorStatus : 0;
        if (empty($id))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        $OperatorConfigModel = OperatorConfigModel::getInstance();
        $OperatorInfo = $OperatorConfigModel->getOperatorList(array('id' => $id, 'row' => true));
        if (empty($OperatorInfo))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00004', 'msg' => '不存在的配置'));
        }
        $isTrue = $OperatorConfigModel->changeOperatorStatus(array('status' => $operatorStatus, 'id' => $id));
        if ($isTrue)
        {
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '设置成功'));
        }
        else
        {
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '设置失败'));
        }
    }

    /**
     * User: panj
     * @return null
     */
    public function actionChangeDisplayOrder()
    {
        $displayOrder = $this->Filter->getPost('displayOrder');
        if (empty($displayOrder))
        {
            return \MsgAction::returnJsonData(
                [
                    'status' => 'P00002',
                    'msg'    => '缺少参数',
                ]
            );
        }
        parse_str($displayOrder, $orderArray);
        if (!isset($orderArray['displayOrder']) || empty($orderArray['displayOrder']))
        {
            return \MsgAction::returnJsonData(
                [
                    'status' => 'P00002',
                    'msg'    => '缺少参数',
                ]
            );
        }
        $orderArray = $orderArray['displayOrder'];
        $OperatorConfigModel = OperatorConfigModel::getInstance();
        $isTrue = $OperatorConfigModel->changeDisplayOrder($orderArray);
        $response = $isTrue ? [
            'status' => 'P00001',
            'msg'    => '设置成功',
        ] : [
            'status' => 'P00001',
            'msg'    => '设置失败',
        ];

        return \MsgAction::returnJsonData($response);
    }
}
