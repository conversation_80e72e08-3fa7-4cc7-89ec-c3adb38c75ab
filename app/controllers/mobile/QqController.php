<?php

use \Common\Utils\Url;
use Service\Avatar\Avatar;

class QqController extends Controller
{

    public function actionRemote()
    {
        $isUseRedis = Config::get("isUseRedis");
        $appid = preg_replace('/[^\w_\.\$]/', '', $_POST['appid']);
        if ($isUseRedis)
        {
            $redis = RedisEx::getInstance();
        }
        if (!isset($_POST["access_token"]))
        {
            echo json_encode(array(
                'sts' => 0,
            ));
            return;
        }
        $oauthAction = loadAction('oauth');
        $result = $oauthAction->qqRemote($_POST["access_token"], $appid);
        if ($result)
        {
            $openid = $result['openid'];
            $nickname = $result['nickname'];
            $figureurl = $result['figureurl'];
            $gender = $result['gender'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'qq');
            $source = Url::getStringParam("mid", "mobile", Url::POST);
            if ($return["passid"] > 0)
            {
                $passid = $return["passid"];
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $result = $memberModel->read($passid);
                if ($result['gender'] == 0 && $gender != 0)
                {
                    $memberModel->edit($passid, array('gender' => $gender));
                }
                $result["token"] = $memberModel->getToken($passid);
                $result["m_uid"] = unserialize($result['m_uid']);
                $result['uid'] = $result['m_uid']['1'];
                $result['passid'] = $result['id'];
                $uid = $result['uid'];
                $username = $result['username'];
                if (strpos($username, "#qq#") !== false)
                {
                    $usernameOld = $username;
                    $username = $memberModel->repairOldOAuthUser($passid, 'qq');
                    if ($username)
                    {
                        $memberModel->patchUserLog($passid, array('username_old' => $usernameOld, 'username' => $username, 'op_time' => time(), 'op_ip' => get_client_ip(), 'op_domain' => 'login.2345.com'));
                        noticeToChange($passid, 'changeUsername', array(
                            'passid' => $passid,
                            'uid' => $uid,
                            'type' => 'username',
                            'value' => $username
                        ));
                    }
                    else
                    {
                        $username = explode("#qq#", $usernameOld);
                        $username = $username[0];
                    }
                }
            }
            else
            {
                $nickname = str_replace(array("#qq#", "#weibo#"), "", $nickname);
                $nickname = trim($nickname);
                if (!$nickname)
                {
                    $nickname = "qzuser_" . substr(md5(microtime()), 0, 6);
                }
                $clientIp = get_client_ip();
                $result = $memberModel->regOAuth('qq', $openid, $nickname, $clientIp, '', $gender);
                if (!$result)
                {
                    $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
                    $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
                    $accountType = \Service\AccountStat\Consts\AccountStat::AC_TP_OAUTH_QQ;
                    \Service\AccountStat\AccountStat::collect($type, $clientType, $accountType, 0, $source);

                    echo json_encode(array(
                        'sts' => 0,
                    ));
                    exit;
                }
                $username = $result['username'];
                $passid = $result['passid'];
                if ($isUseRedis)
                {
                    $redis->incr("regOAuthSuccNum");
                    $redis->incr('RSN:qq_mobile_login_2345_com');
                }
            }
            $token = $result["token"];
            if ($isUseRedis)
            {
                $redis->incr('LSN:qq_mobile_login_2345_com');
            }

            $st = 1;
            if ($return["passid"] > 0)
            {
                $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
            }
            else
            {
                $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
            }
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
            $accountType = \Service\AccountStat\Consts\AccountStat::AC_TP_OAUTH_QQ;
            \Service\AccountStat\AccountStat::collect($type, $clientType, $accountType, $st, $source);

            echo json_encode(array(
                'sts' => 1,
                'username' => mb_convert_encoding($username, "UTF-8", "GBK"),
                'nickname' => mb_convert_encoding($nickname, "UTF-8", "GBK"),
                'uid' => $result["uid"],
                'sec' => get_sec_browser($result["uid"]),
                'passid' => $result["passid"],
                'token' => $token,
            ));
        }
        else
        {
            if ($isUseRedis)
            {
                $redis->incr('LFN:qq_mobile_login_2345_com');
            }

            $source = Url::getStringParam("mid", "mobile", Url::POST);
            $st = 0;
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
            $accountType = \Service\AccountStat\Consts\AccountStat::AC_TP_OAUTH_QQ;
            \Service\AccountStat\AccountStat::collect($type, $clientType, $accountType, $st, $source);

            echo json_encode(array(
                'sts' => 0,
            ));
        }
    }

}