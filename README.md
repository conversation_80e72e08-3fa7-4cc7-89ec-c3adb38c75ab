#octopus框架开发指南 ------++++

##简介 ---+++---------

octopus是一个简单、 快速以及高度可扩展的框架。octopus需要PHP5.3以上版本，且拥抱了当下Web应用程序开发中最出色的操作和实践。
octopus应用遵循 模型-视图-控制器（model-view-controller (MVC)）设计模式。 在MVC中，Models代表数据、业务逻辑，以及数据校验规则。
Views包含用户界面元素，如文本、图片和表单。Controllers用来管理模型和视图之间的通信，处理动作和请求。在本框架中，还包含Actions，
用于处理一些复杂的动作。  

##目录

 * [框架目录结构](#directory)
 * [安装/部署](#install)
 * [配置](#config)
 * [路由控制](#router)
 * [控制器](#controller)
 * [模型](#model)
 * [数据库](#database)
 * [视图](#view)
 * [动作](#action)
 * [钩子](#hook)
 * [子系统](#module)
 * [公共函数](#function)
 * [输入过滤](#filter)
 * [日志处理](#log)
 * [公共资源](#asset)
 * [命令行](#console)

###<a name="directory"></a>框架目录结构

    octopus|
        --|app                   项目全局管理应用程序组件库，协调访问请求的对象
          --|classes             全局基类类库
            --|Action.php        动作类基类
            --|BaseClass.php     顶层祖先类
            --|Config.php        配置文件操作类
            --|Controller.php    控制器基类
            --|Hook.php          钩子基类
            --|Model.php         数据与逻辑模型基类
            --|Octopus.php       框架操作类
          --|config              配置文件
            --|config.php
            --|database.php      数据库配置
            --|redis.php         Redis服务器配置
          --|includes
            --|functions.php     公共函数库
          --|logs
            --|debug.log         debug模式下的操作日志
            --|index.html
          --|Bootstrap.php       框架启动文件
        --|src
          --|actions             动作与逻辑层
            --|CrawlAction.php
          --|controllers         控制器层
            --|DefaultController.php
            --|NewsController.php
          --|hooks               系统钩子
          --|models              数据与业务逻辑层
            --|DefaultModel.php
            --|NewsModel.php
          --|modules             子系统
            --|Demo
              --|actions         子系统动作层
              --|controllers     子系统控制器层
              --|models          子系统模型层
              --|views           子系统模板目录
                --|tpl
                --|tpl_c
          --|views                 视图层
            --|tpl                 模板目录
              --|add.tpl.html
              --|edit.tpl.html
              --|index.tpl.html
              ...
            --|tpl_c               编译后的静态文件目录
        --|vendor                框架核心类库
        --|web                   项目web入口，包含图片、脚本、样式等静态文件
          --|scripts
          --|css
          --|images
          --|index.php         项目入口程序
        --|composer.json       composer安装配置文件
        --|README.md           帮助文档

###<a name="install"></a>安装/部署

####安装
你可以通过两种方式来安装`octopus`框架：  

* 通过[composer](http://getcomposer.org/)  
* 通过下载一个所需文件以及`octopus`框架文件的应用模板  

推荐前者方式，这样只需要一条极其简单的命令就可以安装新的`octopus`框架了。  

#####通过composer安装

> Composer 是PHP中用来管理依赖（dependency）关系的工具。你可以在自己的项目中声明所依赖
> 的外部工具库（libraries），composer会帮你安装这些依赖的库文件。

了解什么是composer，推荐使用composer来安装octopus。从这里下载composer：[http://getcomposer.org/](http://getcomposer.org/)， 或者直接运行下述命令：  

    curl -s http://getcomposer.org/installer | php  

通过composer安装，需要首先绑定本机**hosts**文件，绑定内容如下：  

    ***********  packagist.2345.com

之后打开命令行，cd到相应目录后运行如下指令：

    composer create-project octopus/octopus dev.octopus.com 1.0.4 --repository-url=http://packagist.2345.com/repo/private/

#####通过应用模板方式安装  
通过下载框架模板程序，进行相应部署即可。  

####服务器部署

Apache重写规则：  
打开`apache/conf/httpd.conf`文件，修改

    LoadModule rewrite_module modules/mod_rewrite.so
    # Virtual hosts
    Include conf/extra/httpd-vhosts.conf
打开行前的注释的`#`号，开启重写模块以及虚机模块。
打开`apache/conf/extra/httpd-vhosts.conf`，新增

    <VirtualHost *:80>
        ServerName octopus.dev.com
        DocumentRoot "D:/www/octopus/web"
        <Directory "D:/www/octopus/web">
            RewriteEngine on
            RewriteCond %{REQUEST_FILENAME} !-f
            RewriteCond %{REQUEST_FILENAME} !-d
            RewriteRule ^(.*)$ index.php/$1 [L]
        </Directory>
        ErrorLog "d:/wamp/logs/octopus-dev-error.log"
        CustomLog "d:/wamp/logs/octopus-dev-access.log" common
    </VirtualHost>
以上虚机中配置了重写规则，可以在浏览中输入`http://octopus.dev.com/index.php/default`动作时候直接跳过`index.php`
，直接输入`http://octopus.dev.com/default`即可。

###<a name="config"></a>配置

####默认配置文件

打开文件`octopus/app/config/config.php`

    <?php

    /*
    * 基础配置文件
    */
    define('RUNMODE', 'development');//运行环境development、testing、production
    $config = array();
    include APPPATH . '/config/database.php';
    include APPPATH . '/config/redis.php';
    return $config;

####数据库配置

**创建示例**:

打开文件`octopus/app/config/database.php`

    <?php
    /**
     * 数据库配置文件
     */
    $config['database'] = array(
      'database_name' => array( //数据库名
        'master' => array(
          'host' => '127.0.0.1',
          'port' => '3306',
        ),
        'slave' => array(
          'host' => '127.0.0.1',
          'port' => '3306',
        ),
        'username' => 'root',
        'password' => '888888',
        'charset' => 'gbk',
      ),
    );

####Redis配置

**创建示例**:

打开文件`octopus/app/config/database.php`

    <?php
    /**
     * redis配置文件
     */
     $config['redis'] = array(
         'default' => array(
            'master' => array(
                'host' => '127.0.0.1',
                'port' => '6379',
            ),
            'slave' => array(
                'host' => '127.0.0.1',
                'port' => '6379',
            ),
            'auth' => 'rc_redis',
        ),
      );

###<a name="router"></a>路由控制

####完整链接示例

    http://[domain]/[namespace]/[controller]/[action]/[params]

####默认控制器&默认方法

    访问：http://[domain]/
    默认控制器DefaultController 默认方法actionIndex
    文件目录 /src/controllers/DefaultController.php
    执行方法 public function actionIndex();

####指定控制器&默认方法

    访问：http://[domain]/test
    控制器TestController 默认方法actionIndex
    文件目录 /src/controllers/TestController.php
    执行方法 public function actionIndex();

####指定控制器&指定方法

    访问：http://[domain]/test/hello
    控制器TestController 方法actionHello
    文件目录 /src/controllers/TestController.php
    执行方法 public function actionHello();

####指定控制器&指定方法&指定参数

    访问：http://[domain]/test/hello/world
    控制器TestController 方法actionHello
    文件目录 /src/controllers/TestController.php
    执行方法 public function actionHello("world");

####指定命名空间&指定控制器&指定方法

    访问：http://[domain]/foo/test/hello
    命名空间Foo 控制器TestController 方法actionHello
    文件目录 /src/controllers/Foo/TestController.php
    代码实例
    <?php

    namespace Foo;

    use Controller;

    class TestController extends Controller
    {

        public function actionHello()
        {
            ...
        }

    }

    执行方法 public function actionHello();

    命名空间支持三层

    示例访问：http://[domain]/foo/bar/test/hello
    命名空间Foo 控制器TestController 方法actionHello
    文件目录 /src/controllers/Foo/Bar/TestController.php
    代码实例
    <?php

    namespace Foo\Bar;

    use Controller;

    class TestController extends Controller
    {

        public function actionHello()
        {
            ...
        }

    }

    执行方法 public function actionHello();

    使用到命名空间时注意引用类的层级关系，适当使用use关键字

####指定子系统&指定控制器&指定方法

    示例：在配置文件config.php中配置子系统

    $config['modules'] = array(
        'Demo' => array()
    );

    示例访问：http://[domain]/Demo/test/hello
    命名空间Demo 控制器TestController 方法actionHello
    文件目录 /src/modules/Demo/controllers/TestController.php

    代码实例
    <?php

    namespace Demo;

    use Controller;

    class TestController extends Controller
    {

        public function actionHello()
        {
            ...
        }

    }

###<a name="controller"></a>控制器

类声明： `class DefaultController extends Controller`，控制器程序全部首字母大写，以`Controller`结尾，并且继承自`Controller`基类，文件名和类名保持一致；

类内方法定义：`public function actionIndex()`，注意方法名一定要加`action`前缀，应用驼峰命名法规则，出于安全考虑，仅对用户可以直接访问的控制器类内的方法如此，其他类内方法均正常命名。

**创建示例**:

打开文件`octopus/src/controllers/DefaultController.php`

      <?php
        /**
         * Copyright (c) 2013,上海二三四五网络科技股份有限公司
         * 文件名称：DefaultController.php
         * 摘    要：默认控制器类（例子）
         * 作    者：张小虎
         * 修改日期：2013.10.12
         */
        class DefaultController extends Controller
        {
            public function actionIndex()
            {
                echo "Hello world.";
            }
        }

###<a name="model"></a>模型

类声明：`class NewsModel extends Model`，模型层程序全部首字母大写，以`Model`结尾，并且继承自`Model`基类，文件名和类名保持一致。

**创建示例**:

打开文件`octopus/src/models/DefaultModel.php`

    <?php
    /**
     * Copyright (c) 2013,上海二三四五网络科技股份有限公司
     * 文件名称：DefaultModel.php
     * 摘    要：默认模型类（例子）
     * 作    者：张小虎
     * 修改日期：2013.10.12
     */
    class DefaultModel extends Model
    {

        protected $dbName = 'database_name';
        protected $tableName = 'table_name';
        protected $pkName = 'id';

        /**
         * 简易搜索
         */
        public function search($words)
        {
            $sql = "SELECT * FROM {$this->tableName} WHERE `content` LIKE :content";
            $params = array(
                ':content' => "%$words%";
            );
            return $this->pdo->findAll($sql, $params);
        }

        /**
         * 打个招呼
         * <AUTHOR>
         * @return string 测试字符串
         */
        public function hello()
        {
            return 'Hello world from model.';
        }
    }

**调用示例**:

      $defaultModel = DefaultModel::getInstance();     //模型对象建立
      $msg = $defaultModel->hello();                              //调用模型方法

###<a name="database"></a>数据库操作

####实例化

    $dbConf = Config::get("database");
    $pdo = PdoEx::getInstance("database_name", $dbConf["database_name"]);


####添加数据

    $insert = array(
        "username" => "abc123",
        "age" => "11",
    );
    $pdo->insert("tableName", $insert);

####删除数据

    $condition = array(
        "where" => "id = :id",
        "params" => array(
            ":id" => 3
        ),
    );
    $pdo->delete("tableName", $condition);

####修改数据

    $update = array(
        'age' => 15
    );
    $condition = array(
        "where" => "id = :id",
        "params" => array(
            ":id" => 5
        ),
    );
    $pdo->update("tableName", $update, $condition);

####查询数据

#####查询单条

    $sql = "select * from tableName where id = :id";
    $params = array(
        ":id" => 6
    );
    $row = $pdo->find($sql, $params);

#####查询多条

    $sql = "select * from tableName where age = :age";
    $params = array(
        ":age" => 16
    );
    $result = $pdo->findAll($sql, $params);

#####分页查询

    $limit = 10;
    $offset = 100;
    $sql = "select * from tableName where age = :age";
    $params = array(
        ":age" => 16
    );
    $result = $pdo->findList($sql, $params, $limit, $offset);

###<a name="view"></a>视图

模板语法为Smarty语法，详细见[http://www.yiibai.com/smarty/](http://www.yiibai.com/smarty/)。

**创建示例**:

打开文件`octopus/src/views/tpl/default.index.tpl.html`

    <!DOCTYPE html>
    <head>
      <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
      <title>默认视图模板</title>
      <link type="text/css" href="/css/style.css" rel="stylesheet">
      <script type="text/javascript" src="/scripts/jquery.js"></script>
      </head>
    <body>
      <h1>{{$pageArray.msg}}</h1>
    </body>
    </html>

**调用示例**:

打开文件`octopus/src/controllers/DefaultController.php`

      <?php
        /**
         * Copyright (c) 2013,上海二三四五网络科技股份有限公司
         * 文件名称：DefaultController.php
         * 摘    要：默认控制器类（例子）
         * 作    者：张小虎
         * 修改日期：2013.10.12
         */
        class DefaultController extends Controller
        {
            public function actionIndex()
            {
                $defaultModel = DefaultModel::getInstance();                      //模型对象建立
                $msg = $defaultModel->hello();                                               //调用模型方法
                loadView('default.index.tpl.html', array('msg' => $msg));   //调用视图模板
             }
        }

###<a name="action"></a>动作

类声明：`class DefaultAction extends Action` ，Action程序全部继承自`Action`基类，内部逻辑可以自己制定，文件名和类名保持一致。

**创建示例**:

打开文件`octopus/src/actions/DefaultAction.php`

    <?php
    /**
     * Copyright (c) 2013,上海二三四五网络科技股份有限公司
     * 文件名称：DefaultAction.php
     * 摘    要：默认动作类（例子）
     * 作    者：张小虎
     * 修改日期：2013.10.12
     */
    class DefaultAction extends Action
    {
        public function replaceWord($msg)
        {
            $retStr = str_replace("model", "<span style='color:red;'>action</span>", $msg);
            return $retStr;
        }
    }

**调用示例**:

打开文件`octopus/src/controllers/DefaultController.php`

      <?php
        /**
         * Copyright (c) 2013,上海二三四五网络科技股份有限公司
         * 文件名称：DefaultController.php
         * 摘    要：默认控制器类（例子）
         * 作    者：张小虎
         * 修改日期：2013.10.12
         */
        class DefaultController extends Controller
        {
            public function actionIndex()
            {
                $defaultModel = DefaultModel::getInstance();                      //模型对象建立
                $msg = $defaultModel->hello();                                               //调用模型方法
                $defaultAction = DefaultAction::getInstance();                      //动作对象建立
                $msg = $defaultAction->replaceWord($msg);                       //调用动作方法
                loadView('default.index.tpl.html', array('msg' => $msg));   //调用视图模板
             }
        }

###<a name="hook"></a>钩子

钩子功能可以在不修改系统核心文件的基础上来改变或增加系统的核心运行功能。 配置示例：

    $config['hooks'] = array(
        'pre_bootstrap' => array(
            array(
                'class' => 'BenchmarkHook',
                'method' => 'mark',
                'params' => array(
                    'total_execution_time_start'
                )
            ),
        ),
        'post_bootstrap' => array(
            array(
                'class' => 'BenchmarkHook',
                'method' => 'mark',
                'params' => array(
                    'total_execution_time_end'
                )
            ),
            array(
                'class' => 'BenchmarkHook',
                'method' => 'elapsedTime',
                'params' => array(
                    'total_execution_time_start',
                    'total_execution_time_end'
                )
            ),
            array(
                'class' => 'BenchmarkHook',
                'method' => 'memoryUsage',
            ),
        ),
    );

以上是一个计算程序执行时间和占用内存数的例子，文件路径`/src/hooks/BenchmarkHook.php`

###<a name="module"></a>子系统

当系统越来越大，控制器、模型、动作类越来越多，为了方便维护，可以按功能区分建立子系统
下面介绍创建一个网站后台，配置如下：

    $config['modules'] = array(
        'Houtai' => array()
    );

目录结构如下：

    --|src
        --|modules             子系统
            --|Houtai
                --|actions         子系统动作层
                --|controllers     子系统控制器层
                --|models          子系统模型层
                --|views           子系统模板目录
                    --|tpl
                    --|tpl_c

注意点：
> 子系统里面所有的控制器、模型、动作类都要添加命名空间 namespace Houtai;

###<a name="function"></a>公共函数

统一存放在文件`octopus/app/includes/functions.php`中.

###<a name="filter"></a>输入过滤

    use Octopus\Filter;
    ...
    $filter = new Filter();
    $string = $filter->xssClean($string);//过滤字符串
    $_GET = $filter->get(null, true);//获取过滤后的$_GET
    $_GET["test"] = $filter->get("test", true);//获取过滤后的$_GET["test"]
    $_POST = $filter->post(null, true);//获取过滤后的$_POST
    $_POST["test"] = $filter->post("test", true);//获取过滤后的$_POST["test"]
    $_COOKIE = $filter->cookie(null, true);//同上类似
    $_SERVER = $filter->server(null, true);//同上类似
    $filter->filterAll();//过滤以上所有，影响较大，不推荐

###<a name="log"></a>日志处理

    当 RUNMODE == 'development' 时开启了debug日志 /app/logs/debug.log
    自定义日志：
    use Octopus\Logger\Handler\StreamHandler;
    use Octopus\Logger\Registry;
    use Octopus\Logger;
    ...
    $logger = new Logger("mylog");
    $logger->pushHandler(new StreamHandler(APPPATH . "/logs/mylog.log"));
    $logger->debug($message, $contextArr);//记录调试日志
    $logger->info($message, $contextArr);//记录信息日志
    $logger->notice($message, $contextArr);//记录通知日志
    $logger->warning($message, $contextArr);//记录警告日志
    $logger->error($message, $contextArr);//记录错误日志
    $logger->critical($message, $contextArr);//记录危险日志
    $logger->alert($message, $contextArr);//记录警报日志
    $logger->emergency($message, $contextArr);//记录紧急日志
    ...
    Registry::addLogger($logger);//注册日志处理类
    ...
    $logger = Registry::mylog();//取出日志处理类

###<a name="asset"></a>公共资源

js、css、image等公共资源文件分别存放在`octopus/web/scripts`、`octopus/web/css`、`octopus/web/images`下.

###<a name="console"></a>命令行

命令行调用脚本的方式

**创建示例**:

打开文件`octopus/console/index.sh`

    #!/bin/sh
    date +%Y-%m-%d\ %H:%M:%S >> index.log
    php index.php DefaultController actionIndex >> index.log
    date +%Y-%m-%d\ %H:%M:%S >> index2.log
