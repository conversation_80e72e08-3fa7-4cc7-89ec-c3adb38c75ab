@charset "utf-8";
/* CSS Document */

.left{ float:left;}
.mt20{ margin-top:20px;}
.ml10{ margin-left:10px;}
.ml30{ margin-left:30px;}
.ml54{ margin-left:54px;}



.tx_warp{ padding:20px;}
.top_cz{ height:30px;}
.a_sty{ padding:0 15px; height:24px; line-height:24px; color:#666; font-size:14px; margin-top:3px;}
.a_sty:hover{ color:#338ad1;}
.cur_a{ background:#049ae6; color:#fff}
.cur_a:hover{ color:#fff;}
.em_sty{ line-height:30px;}
.btn-search{ padding:0 20px; line-height:28px; background:#f5f5f5; border:1px solid #d9d9d9; border-radius:2px; color:#666;}
.btn-search:hover{ background:#e8f3ff; color:#338ad1; border-color:#73bbe5;}
.time_sty{ width:91px; padding-left:20px; padding-right:30px; height:28px; border:1px solid #dedede; border-radius:2px; position:relative;}
.time_sty input{ width:100%; height:28px; line-height:28px; border:0 none; font-size:12px; color:#666; float:left;}
.time_sty i{ width:16px; height:17px; display:block; position:absolute; right:11px; top:6px; background:url(/images/other/touxiang.png) left top; overflow:hidden;}

.tx_listWarp{ border-top:1px solid #ededed;}
.tx_list{ overflow:hidden;}
.time_title{ font-size:18px; font-weight:bold; margin-top:28px;}
.user_list{ margin-top:8px; margin-left:-10px;}
.user_list li{ width:80px; float:left; vertical-align:top; margin-left:30px; display:inline; margin-top:17px; position:relative;}
.user_list li img{ width:76px; height:76px; border:2px solid #e6e6e6; overflow:hidden; display:block; cursor:pointer;}
.user_list li .curr_userpic{ border-color:#73bbe5;}
.user_list li em{ font-family:Arial; font-size:12px; display:block; line-height:18px; height:18px; overflow:hidden;}
.close-pic{ width:20px; height:20px; display:block; position:absolute; top:-5px; right:-7px; background:url(/images/other/touxiang.png) left -25px; display:none;}
.user_list .more_li{ border:none;}
.user_list .more_li a{ color:#666; text-align:center; line-height:80px; display:block;}
.user_list .more_li a:hover{ color:#338ad1;}
.cz-btnWarp{ margin-top:13px;}
.cz-btnWarp a{ float:left; display:inline; padding:0 10px; line-height:28px; margin-left:20px; margin-right:20px; border:1px solid #d9d9d9; border-radius:2px; color:#666; background:#f5f5f5;}
.cz-btnWarp a:hover{ background:#e8f3ff; color:#338ad1; border-color:#73bbe5;}





























