!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.echarts={})}(this,function(t){"use strict";function e(t,e){"createCanvas"===t&&(hp=null),sp[t]=e}function n(t){if(null==t||"object"!=typeof t)return t;var e=t,i=tp.call(t);if("[object Array]"===i){if(!z(t)){e=[];for(var r=0,o=t.length;r<o;r++)e[r]=n(t[r])}}else if(Jf[i]){if(!z(t)){var a=t.constructor;if(t.constructor.from)e=a.from(t);else{e=new a(t.length);for(var r=0,o=t.length;r<o;r++)e[r]=n(t[r])}}}else if(!Qf[i]&&!z(t)&&!M(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=n(t[s]))}return e}function i(t,e,r){if(!w(e)||!w(t))return r?n(e):t;for(var o in e)if(e.hasOwnProperty(o)){var a=t[o],s=e[o];!w(s)||!w(a)||y(s)||y(a)||M(s)||M(a)||b(s)||b(a)||z(s)||z(a)?!r&&o in t||(t[o]=n(e[o],!0)):i(a,s,r)}return t}function r(t,e){for(var n=t[0],r=1,o=t.length;r<o;r++)n=i(n,t[r],e);return n}function o(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function a(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}function s(){return hp||(hp=lp().getContext("2d")),hp}function l(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function h(t,e){function n(){}var i=t.prototype;n.prototype=e.prototype,t.prototype=new n;for(var r in i)t.prototype[r]=i[r];t.prototype.constructor=t,t.superClass=e}function u(t,e,n){a(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,n)}function c(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function d(t,e,n){if(t&&e)if(t.forEach&&t.forEach===np)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function f(t,e,n){if(t&&e){if(t.map&&t.map===op)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}}function p(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===ap)return t.reduce(e,n,i);for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function g(t,e,n){if(t&&e){if(t.filter&&t.filter===ip)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function m(t,e){var n=rp.call(arguments,2);return function(){return t.apply(e,n.concat(rp.call(arguments)))}}function v(t){var e=rp.call(arguments,1);return function(){return t.apply(this,e.concat(rp.call(arguments)))}}function y(t){return"[object Array]"===tp.call(t)}function x(t){return"function"==typeof t}function _(t){return"[object String]"===tp.call(t)}function w(t){var e=typeof t;return"function"===e||!!t&&"object"==e}function b(t){return!!Qf[tp.call(t)]}function S(t){return!!Jf[tp.call(t)]}function M(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function I(t){return t!==t}function T(t){for(var e=0,n=arguments.length;e<n;e++)if(null!=arguments[e])return arguments[e]}function C(t,e){return null!=t?t:e}function D(t,e,n){return null!=t?t:null!=e?e:n}function A(){return Function.call.apply(rp,arguments)}function k(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function P(t,e){if(!t)throw new Error(e)}function L(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}function O(t){t[up]=!0}function z(t){return t[up]}function E(t){function e(t,e){n?i.set(t,e):i.set(e,t)}var n=y(t),i=this;t instanceof E?t.each(e):t&&d(t,e)}function N(t){return new E(t)}function R(){}function B(t,e){var n=new dp(2);return null==t&&(t=0),null==e&&(e=0),n[0]=t,n[1]=e,n}function V(t,e){return t[0]=e[0],t[1]=e[1],t}function F(t){var e=new dp(2);return e[0]=t[0],e[1]=t[1],e}function H(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function G(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function W(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Z(t){return Math.sqrt(U(t))}function U(t){return t[0]*t[0]+t[1]*t[1]}function X(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function j(t,e){var n=Z(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Y(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}function q(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}function $(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function K(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function Q(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}function J(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}function tt(t,e){return{target:t,topTarget:e&&e.topTarget}}function et(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which}}function nt(){}function it(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return!i||_p}return!1}function rt(){var t=new Sp(6);return ot(t),t}function ot(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function at(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function st(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function lt(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function ht(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],h=Math.sin(n),u=Math.cos(n);return t[0]=i*u+a*h,t[1]=-i*h+a*u,t[2]=r*u+s*h,t[3]=-r*h+u*s,t[4]=u*o+h*l,t[5]=u*l-h*o,t}function ut(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function ct(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}function dt(t){return t>Tp||t<-Tp}function ft(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}function pt(t){return(t=Math.round(t))<0?0:t>255?255:t}function gt(t){return(t=Math.round(t))<0?0:t>360?360:t}function mt(t){return t<0?0:t>1?1:t}function vt(t){return pt(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function yt(t){return mt(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function xt(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function _t(t,e,n){return t+(e-t)*n}function wt(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function bt(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function St(t,e){Bp&&bt(Bp,e),Bp=Rp.put(t,Bp||e.slice())}function Mt(t,e){if(t){e=e||[];var n=Rp.get(t);if(n)return bt(e,n);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Np)return bt(e,Np[i]),St(t,e),e;if("#"!==i.charAt(0)){var r=i.indexOf("("),o=i.indexOf(")");if(-1!==r&&o+1===i.length){var a=i.substr(0,r),s=i.substr(r+1,o-(r+1)).split(","),l=1;switch(a){case"rgba":if(4!==s.length)return void wt(e,0,0,0,1);l=yt(s.pop());case"rgb":return 3!==s.length?void wt(e,0,0,0,1):(wt(e,vt(s[0]),vt(s[1]),vt(s[2]),l),St(t,e),e);case"hsla":return 4!==s.length?void wt(e,0,0,0,1):(s[3]=yt(s[3]),It(s,e),St(t,e),e);case"hsl":return 3!==s.length?void wt(e,0,0,0,1):(It(s,e),St(t,e),e);default:return}}wt(e,0,0,0,1)}else{if(4===i.length)return(h=parseInt(i.substr(1),16))>=0&&h<=4095?(wt(e,(3840&h)>>4|(3840&h)>>8,240&h|(240&h)>>4,15&h|(15&h)<<4,1),St(t,e),e):void wt(e,0,0,0,1);if(7===i.length){var h=parseInt(i.substr(1),16);return h>=0&&h<=16777215?(wt(e,(16711680&h)>>16,(65280&h)>>8,255&h,1),St(t,e),e):void wt(e,0,0,0,1)}}}}function It(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=yt(t[1]),r=yt(t[2]),o=r<=.5?r*(i+1):r+i-r*i,a=2*r-o;return e=e||[],wt(e,pt(255*xt(a,o,n+1/3)),pt(255*xt(a,o,n)),pt(255*xt(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Tt(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,h=(s+a)/2;if(0===l)e=0,n=0;else{n=h<.5?l/(s+a):l/(2-s-a);var u=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-o)/6+l/2)/l;i===s?e=d-c:r===s?e=1/3+u-d:o===s&&(e=2/3+c-u),e<0&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,h];return null!=t[3]&&f.push(t[3]),f}}function Ct(t,e){var n=Mt(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:t[i]<0&&(n[i]=0);return Lt(n,4===n.length?"rgba":"rgb")}}function Dt(t){var e=Mt(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function At(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=pt(_t(a[0],s[0],l)),n[1]=pt(_t(a[1],s[1],l)),n[2]=pt(_t(a[2],s[2],l)),n[3]=mt(_t(a[3],s[3],l)),n}}function kt(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=Mt(e[r]),s=Mt(e[o]),l=i-r,h=Lt([pt(_t(a[0],s[0],l)),pt(_t(a[1],s[1],l)),pt(_t(a[2],s[2],l)),mt(_t(a[3],s[3],l))],"rgba");return n?{color:h,leftIndex:r,rightIndex:o,value:i}:h}}function Pt(t,e){if((t=Mt(t))&&null!=e)return t[3]=mt(e),Lt(t,"rgba")}function Lt(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}function Ot(t,e){return t[e]}function zt(t,e,n){t[e]=n}function Et(t,e,n){return(e-t)*n+t}function Nt(t,e,n){return n>.5?e:t}function Rt(t,e,n,i,r){var o=t.length;if(1==r)for(s=0;s<o;s++)i[s]=Et(t[s],e[s],n);else for(var a=o&&t[0].length,s=0;s<o;s++)for(var l=0;l<a;l++)i[s][l]=Et(t[s][l],e[s][l],n)}function Bt(t,e,n){var i=t.length,r=e.length;if(i!==r)if(i>r)t.length=r;else for(a=i;a<r;a++)t.push(1===n?e[a]:Gp.call(e[a]));for(var o=t[0]&&t[0].length,a=0;a<t.length;a++)if(1===n)isNaN(t[a])&&(t[a]=e[a]);else for(var s=0;s<o;s++)isNaN(t[a][s])&&(t[a][s]=e[a][s])}function Vt(t,e,n){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===n){for(o=0;o<i;o++)if(t[o]!==e[o])return!1}else for(var r=t[0].length,o=0;o<i;o++)for(var a=0;a<r;a++)if(t[o][a]!==e[o][a])return!1;return!0}function Ft(t,e,n,i,r,o,a,s,l){var h=t.length;if(1==l)for(c=0;c<h;c++)s[c]=Ht(t[c],e[c],n[c],i[c],r,o,a);else for(var u=t[0].length,c=0;c<h;c++)for(var d=0;d<u;d++)s[c][d]=Ht(t[c][d],e[c][d],n[c][d],i[c][d],r,o,a)}function Ht(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function Gt(t){if(c(t)){var e=t.length;if(c(t[0])){for(var n=[],i=0;i<e;i++)n.push(Gp.call(t[i]));return n}return Gp.call(t)}return t}function Wt(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function Zt(t){var e=t[t.length-1].value;return c(e&&e[0])?2:1}function Ut(t,e,n,i,r,o){var a=t._getter,s=t._setter,l="spline"===e,h=i.length;if(h){var u,d=c(i[0].value),f=!1,p=!1,g=d?Zt(i):0;i.sort(function(t,e){return t.time-e.time}),u=i[h-1].time;for(var m=[],v=[],y=i[0].value,x=!0,_=0;_<h;_++){m.push(i[_].time/u);var w=i[_].value;if(d&&Vt(w,y,g)||!d&&w===y||(x=!1),y=w,"string"==typeof w){var b=Mt(w);b?(w=b,f=!0):p=!0}v.push(w)}if(o||!x){for(var S=v[h-1],_=0;_<h-1;_++)d?Bt(v[_],S,g):!isNaN(v[_])||isNaN(S)||p||f||(v[_]=S);d&&Bt(a(t._target,r),S,g);var M,I,T,C,D,A,k=0,P=0;if(f)var L=[0,0,0,0];var O=new ft({target:t._target,life:u,loop:t._loop,delay:t._delay,onframe:function(t,e){var n;if(e<0)n=0;else if(e<P){for(n=M=Math.min(k+1,h-1);n>=0&&!(m[n]<=e);n--);n=Math.min(n,h-2)}else{for(n=k;n<h&&!(m[n]>e);n++);n=Math.min(n-1,h-2)}k=n,P=e;var i=m[n+1]-m[n];if(0!==i)if(I=(e-m[n])/i,l)if(C=v[n],T=v[0===n?n:n-1],D=v[n>h-2?h-1:n+1],A=v[n>h-3?h-1:n+2],d)Ft(T,C,D,A,I,I*I,I*I*I,a(t,r),g);else{if(f)o=Ft(T,C,D,A,I,I*I,I*I*I,L,1),o=Wt(L);else{if(p)return Nt(C,D,I);o=Ht(T,C,D,A,I,I*I,I*I*I)}s(t,r,o)}else if(d)Rt(v[n],v[n+1],I,a(t,r),g);else{var o;if(f)Rt(v[n],v[n+1],I,L,1),o=Wt(L);else{if(p)return Nt(v[n],v[n+1],I);o=Et(v[n],v[n+1],I)}s(t,r,o)}},ondestroy:n});return e&&"spline"!==e&&(O.easing=e),O}}}function Xt(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}function jt(t){for(var e=0;t>=tg;)e|=1&t,t>>=1;return t+e}function Yt(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;qt(t,e,r)}else for(;r<n&&i(t[r],t[r-1])>=0;)r++;return r-e}function qt(t,e,n){for(n--;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}function $t(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=o+1;var h=i-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;h>0;)t[s+h]=t[s+h-1],h--}t[s]=a}}function Kt(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])>0){for(s=i-r;l<s&&o(t,e[n+r+l])>0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var h=a;a=r-l,l=r-h}for(a++;a<l;){var u=a+(l-a>>>1);o(t,e[n+u])>0?a=u+1:l=u}return l}function Qt(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var h=a;a=r-l,l=r-h}else{for(s=i-r;l<s&&o(t,e[n+r+l])>=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;a<l;){var u=a+(l-a>>>1);o(t,e[n+u])<0?l=u:a=u+1}return l}function Jt(t,e){function n(n){var s=o[n],h=a[n],u=o[n+1],c=a[n+1];a[n]=h+c,n===l-3&&(o[n+1]=o[n+2],a[n+1]=a[n+2]),l--;var d=Qt(t[u],t,s,h,0,e);s+=d,0!==(h-=d)&&0!==(c=Kt(t[s+h-1],t,u,c,c-1,e))&&(h<=c?i(s,h,u,c):r(s,h,u,c))}function i(n,i,r,o){var a=0;for(a=0;a<i;a++)h[a]=t[n+a];var l=0,u=r,c=n;if(t[c++]=t[u++],0!=--o)if(1!==i){for(var d,f,p,g=s;;){d=0,f=0,p=!1;do{if(e(t[u],h[l])<0){if(t[c++]=t[u++],f++,d=0,0==--o){p=!0;break}}else if(t[c++]=h[l++],d++,f=0,1==--i){p=!0;break}}while((d|f)<g);if(p)break;do{if(0!==(d=Qt(t[u],h,l,i,0,e))){for(a=0;a<d;a++)t[c+a]=h[l+a];if(c+=d,l+=d,(i-=d)<=1){p=!0;break}}if(t[c++]=t[u++],0==--o){p=!0;break}if(0!==(f=Kt(h[l],t,u,o,0,e))){for(a=0;a<f;a++)t[c+a]=t[u+a];if(c+=f,u+=f,0===(o-=f)){p=!0;break}}if(t[c++]=h[l++],1==--i){p=!0;break}g--}while(d>=eg||f>=eg);if(p)break;g<0&&(g=0),g+=2}if((s=g)<1&&(s=1),1===i){for(a=0;a<o;a++)t[c+a]=t[u+a];t[c+o]=h[l]}else{if(0===i)throw new Error;for(a=0;a<i;a++)t[c+a]=h[l+a]}}else{for(a=0;a<o;a++)t[c+a]=t[u+a];t[c+o]=h[l]}else for(a=0;a<i;a++)t[c+a]=h[l+a]}function r(n,i,r,o){var a=0;for(a=0;a<o;a++)h[a]=t[r+a];var l=n+i-1,u=o-1,c=r+o-1,d=0,f=0;if(t[c--]=t[l--],0!=--i)if(1!==o){for(var p=s;;){var g=0,m=0,v=!1;do{if(e(h[u],t[l])<0){if(t[c--]=t[l--],g++,m=0,0==--i){v=!0;break}}else if(t[c--]=h[u--],m++,g=0,1==--o){v=!0;break}}while((g|m)<p);if(v)break;do{if(0!=(g=i-Qt(h[u],t,n,i,i-1,e))){for(i-=g,f=(c-=g)+1,d=(l-=g)+1,a=g-1;a>=0;a--)t[f+a]=t[d+a];if(0===i){v=!0;break}}if(t[c--]=h[u--],1==--o){v=!0;break}if(0!=(m=o-Kt(t[l],h,0,o,o-1,e))){for(o-=m,f=(c-=m)+1,d=(u-=m)+1,a=0;a<m;a++)t[f+a]=h[d+a];if(o<=1){v=!0;break}}if(t[c--]=t[l--],0==--i){v=!0;break}p--}while(g>=eg||m>=eg);if(v)break;p<0&&(p=0),p+=2}if((s=p)<1&&(s=1),1===o){for(f=(c-=i)+1,d=(l-=i)+1,a=i-1;a>=0;a--)t[f+a]=t[d+a];t[c]=h[u]}else{if(0===o)throw new Error;for(d=c-(o-1),a=0;a<o;a++)t[d+a]=h[a]}}else{for(f=(c-=i)+1,d=(l-=i)+1,a=i-1;a>=0;a--)t[f+a]=t[d+a];t[c]=h[u]}else for(d=c-(o-1),a=0;a<o;a++)t[d+a]=h[a]}var o,a,s=eg,l=0,h=[];o=[],a=[],this.mergeRuns=function(){for(;l>1;){var t=l-2;if(t>=1&&a[t-1]<=a[t]+a[t+1]||t>=2&&a[t-2]<=a[t]+a[t-1])a[t-1]<a[t+1]&&t--;else if(a[t]>a[t+1])break;n(t)}},this.forceMergeRuns=function(){for(;l>1;){var t=l-2;t>0&&a[t-1]<a[t+1]&&t--,n(t)}},this.pushRun=function(t,e){o[l]=t,a[l]=e,l+=1}}function te(t,e,n,i){n||(n=0),i||(i=t.length);var r=i-n;if(!(r<2)){var o=0;if(r<tg)return o=Yt(t,n,i,e),void $t(t,n,i,n+o,e);var a=new Jt(t,e),s=jt(r);do{if((o=Yt(t,n,i,e))<s){var l=r;l>s&&(l=s),$t(t,n,n+l,n+o,e),o=l}a.pushRun(n,o),a.mergeRuns(),r-=o,n+=o}while(0!==r);a.forceMergeRuns()}}function ee(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function ne(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=isNaN(i)?0:i,r=isNaN(r)?1:r,o=isNaN(o)?0:o,a=isNaN(a)?0:a,t.createLinearGradient(i,o,r,a)}function ie(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*r+n.y,l*=o),t.createRadialGradient(a,s,0,a,s,l)}function re(){return!1}function oe(t,e,n){var i=lp(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left=0,a.top=0,a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}function ae(t){if("string"==typeof t){var e=fg.get(t);return e&&e.image}return t}function se(t,e,n,i,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=fg.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?!he(e=o.image)&&o.pending.push(a):(!e&&(e=new Image),e.onload=le,fg.put(t,e.__cachedImgObj={image:e,pending:[a]}),e.src=e.__zrImageSrc=t),e}return t}return e}function le(){var t=this.__cachedImgObj;this.onload=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function he(t){return t&&t.width&&t.height}function ue(t,e){var n=t+":"+(e=e||yg);if(pg[n])return pg[n];for(var i=(t+"").split("\n"),r=0,o=0,a=i.length;o<a;o++)r=Math.max(be(i[o],e).width,r);return gg>mg&&(gg=0,pg={}),gg++,pg[n]=r,r}function ce(t,e,n,i,r,o,a){return o?fe(t,e,n,i,r,o,a):de(t,e,n,i,r,a)}function de(t,e,n,i,r,o){var a=Se(t,e,r,o),s=ue(t,e);r&&(s+=r[1]+r[3]);var l=a.outerHeight,h=new Xt(pe(0,s,n),ge(0,l,i),s,l);return h.lineHeight=a.lineHeight,h}function fe(t,e,n,i,r,o,a){var s=Me(t,{rich:o,truncate:a,font:e,textAlign:n,textPadding:r}),l=s.outerWidth,h=s.outerHeight;return new Xt(pe(0,l,n),ge(0,h,i),l,h)}function pe(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function ge(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function me(t,e,n){var i=e.x,r=e.y,o=e.height,a=e.width,s=o/2,l="left",h="top";switch(t){case"left":i-=n,r+=s,l="right",h="middle";break;case"right":i+=n+a,r+=s,h="middle";break;case"top":i+=a/2,r-=n,l="center",h="bottom";break;case"bottom":i+=a/2,r+=o+n,l="center";break;case"inside":i+=a/2,r+=s,l="center",h="middle";break;case"insideLeft":i+=n,r+=s,h="middle";break;case"insideRight":i+=a-n,r+=s,l="right",h="middle";break;case"insideTop":i+=a/2,r+=n,l="center";break;case"insideBottom":i+=a/2,r+=o-n,l="center",h="bottom";break;case"insideTopLeft":i+=n,r+=n;break;case"insideTopRight":i+=a-n,r+=n,l="right";break;case"insideBottomLeft":i+=n,r+=o-n,h="bottom";break;case"insideBottomRight":i+=a-n,r+=o-n,l="right",h="bottom"}return{x:i,y:r,textAlign:l,textVerticalAlign:h}}function ve(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=ye(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=xe(o[a],r);return o.join("\n")}function ye(t,e,n,i){(i=o({},i)).font=e;var n=C(n,"...");i.maxIterations=C(i.maxIterations,2);var r=i.minChar=C(i.minChar,0);i.cnCharWidth=ue("国",e);var a=i.ascCharWidth=ue("a",e);i.placeholder=C(i.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;l<r&&s>=a;l++)s-=a;var h=ue(n);return h>s&&(n="",h=0),s=t-h,i.ellipsis=n,i.ellipsisWidth=h,i.contentWidth=s,i.containerWidth=t,i}function xe(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=ue(t,i);if(o<=n)return t;for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?_e(t,r,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*r/o):0;o=ue(t=t.substr(0,s),i)}return""===t&&(t=e.placeholder),t}function _e(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}function we(t){return ue("国",t)}function be(t,e){return xg.measureText(t,e)}function Se(t,e,n,i){null!=t&&(t+="");var r=we(e),o=t?t.split("\n"):[],a=o.length*r,s=a;if(n&&(s+=n[0]+n[2]),t&&i){var l=i.outerHeight,h=i.outerWidth;if(null!=l&&s>l)t="",o=[];else if(null!=h)for(var u=ye(h-(n?n[1]+n[3]:0),e,i.ellipsis,{minChar:i.minChar,placeholder:i.placeholder}),c=0,d=o.length;c<d;c++)o[c]=xe(o[c],u)}return{lines:o,height:a,outerHeight:s,lineHeight:r}}function Me(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;for(var i,r=vg.lastIndex=0;null!=(i=vg.exec(t));){var o=i.index;o>r&&Ie(n,t.substring(r,o)),Ie(n,i[2],i[1]),r=vg.lastIndex}r<t.length&&Ie(n,t.substring(r,t.length));var a=n.lines,s=0,l=0,h=[],u=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;u&&(null!=d&&(d-=u[1]+u[3]),null!=f&&(f-=u[0]+u[2]));for(k=0;k<a.length;k++){for(var p=a[k],g=0,m=0,v=0;v<p.tokens.length;v++){var y=(P=p.tokens[v]).styleName&&e.rich[P.styleName]||{},x=P.textPadding=y.textPadding,_=P.font=y.font||e.font,w=P.textHeight=C(y.textHeight,we(_));if(x&&(w+=x[0]+x[2]),P.height=w,P.lineHeight=D(y.textLineHeight,e.textLineHeight,w),P.textAlign=y&&y.textAlign||e.textAlign,P.textVerticalAlign=y&&y.textVerticalAlign||"middle",null!=f&&s+P.lineHeight>f)return{lines:[],width:0,height:0};P.textWidth=ue(P.text,_);var b=y.textWidth,S=null==b||"auto"===b;if("string"==typeof b&&"%"===b.charAt(b.length-1))P.percentWidth=b,h.push(P),b=0;else{if(S){b=P.textWidth;var M=y.textBackgroundColor,I=M&&M.image;I&&he(I=ae(I))&&(b=Math.max(b,I.width*w/I.height))}var T=x?x[1]+x[3]:0;b+=T;var A=null!=d?d-m:null;null!=A&&A<b&&(!S||A<T?(P.text="",P.textWidth=b=0):(P.text=ve(P.text,A-T,_,c.ellipsis,{minChar:c.minChar}),P.textWidth=ue(P.text,_),b=P.textWidth+T))}m+=P.width=b,y&&(g=Math.max(g,P.lineHeight))}p.width=m,p.lineHeight=g,s+=g,l=Math.max(l,m)}n.outerWidth=n.width=C(e.textWidth,l),n.outerHeight=n.height=C(e.textHeight,s),u&&(n.outerWidth+=u[1]+u[3],n.outerHeight+=u[0]+u[2]);for(var k=0;k<h.length;k++){var P=h[k],L=P.percentWidth;P.width=parseInt(L,10)/100*l}return n}function Ie(t,e,n){for(var i=""===e,r=e.split("\n"),o=t.lines,a=0;a<r.length;a++){var s=r[a],l={styleName:n,text:s,isLineHolder:!s&&!i};if(a)o.push({tokens:[l]});else{var h=(o[o.length-1]||(o[0]={tokens:[]})).tokens,u=h.length;1===u&&h[0].isLineHolder?h[0]=l:(s||!u||i)&&h.push(l)}}}function Te(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&L(e)||t.textFont||t.font}function Ce(t,e){var n,i,r,o,a=e.x,s=e.y,l=e.width,h=e.height,u=e.r;l<0&&(a+=l,l=-l),h<0&&(s+=h,h=-h),"number"==typeof u?n=i=r=o=u:u instanceof Array?1===u.length?n=i=r=o=u[0]:2===u.length?(n=r=u[0],i=o=u[1]):3===u.length?(n=u[0],i=o=u[1],r=u[2]):(n=u[0],i=u[1],r=u[2],o=u[3]):n=i=r=o=0;var c;n+i>l&&(n*=l/(c=n+i),i*=l/c),r+o>l&&(r*=l/(c=r+o),o*=l/c),i+r>h&&(i*=h/(c=i+r),r*=h/c),n+o>h&&(n*=h/(c=n+o),o*=h/c),t.moveTo(a+n,s),t.lineTo(a+l-i,s),0!==i&&t.arc(a+l-i,s+i,i,-Math.PI/2,0),t.lineTo(a+l,s+h-r),0!==r&&t.arc(a+l-r,s+h-r,r,0,Math.PI/2),t.lineTo(a+o,s+h),0!==o&&t.arc(a+o,s+h-o,o,Math.PI/2,Math.PI),t.lineTo(a,s+n),0!==n&&t.arc(a+n,s+n,n,Math.PI,1.5*Math.PI)}function De(t){return Ae(t),d(t.rich,Ae),t}function Ae(t){if(t){t.font=Te(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||_g[e]?e:"left";var n=t.textVerticalAlign||t.textBaseline;"center"===n&&(n="middle"),t.textVerticalAlign=null==n||wg[n]?n:"top",t.textPadding&&(t.textPadding=k(t.textPadding))}}function ke(t,e,n,i,r){i.rich?Le(t,e,n,i,r):Pe(t,e,n,i,r)}function Pe(t,e,n,i,r){var o=Fe(e,"font",i.font||yg),a=i.textPadding,s=t.__textCotentBlock;s&&!t.__dirty||(s=t.__textCotentBlock=Se(n,o,a,i.truncate));var l=s.outerHeight,h=s.lines,u=s.lineHeight,c=Ve(l,i,r),d=c.baseX,f=c.baseY,p=c.textAlign,g=c.textVerticalAlign;ze(e,i,r,d,f);var m=ge(f,l,g),v=d,y=m,x=Ne(i);if(x||a){var _=ue(n,o);a&&(_+=a[1]+a[3]);var w=pe(d,_,p);x&&Re(t,e,i,w,m,_,l),a&&(v=Ze(d,p,a),y+=a[0])}Fe(e,"textAlign",p||"left"),Fe(e,"textBaseline","middle"),Fe(e,"shadowBlur",i.textShadowBlur||0),Fe(e,"shadowColor",i.textShadowColor||"transparent"),Fe(e,"shadowOffsetX",i.textShadowOffsetX||0),Fe(e,"shadowOffsetY",i.textShadowOffsetY||0),y+=u/2;var b=i.textStrokeWidth,S=He(i.textStroke,b),M=Ge(i.textFill);S&&(Fe(e,"lineWidth",b),Fe(e,"strokeStyle",S)),M&&Fe(e,"fillStyle",M);for(var I=0;I<h.length;I++)S&&e.strokeText(h[I],v,y),M&&e.fillText(h[I],v,y),y+=u}function Le(t,e,n,i,r){var o=t.__textCotentBlock;o&&!t.__dirty||(o=t.__textCotentBlock=Me(n,i)),Oe(t,e,o,i,r)}function Oe(t,e,n,i,r){var o=n.width,a=n.outerWidth,s=n.outerHeight,l=i.textPadding,h=Ve(s,i,r),u=h.baseX,c=h.baseY,d=h.textAlign,f=h.textVerticalAlign;ze(e,i,r,u,c);var p=pe(u,a,d),g=ge(c,s,f),m=p,v=g;l&&(m+=l[3],v+=l[0]);var y=m+o;Ne(i)&&Re(t,e,i,p,g,a,s);for(var x=0;x<n.lines.length;x++){for(var _,w=n.lines[x],b=w.tokens,S=b.length,M=w.lineHeight,I=w.width,T=0,C=m,D=y,A=S-1;T<S&&(!(_=b[T]).textAlign||"left"===_.textAlign);)Ee(t,e,_,i,M,v,C,"left"),I-=_.width,C+=_.width,T++;for(;A>=0&&"right"===(_=b[A]).textAlign;)Ee(t,e,_,i,M,v,D,"right"),I-=_.width,D-=_.width,A--;for(C+=(o-(C-m)-(y-D)-I)/2;T<=A;)Ee(t,e,_=b[T],i,M,v,C+_.width/2,"center"),C+=_.width,T++;v+=M}}function ze(t,e,n,i,r){if(n&&e.textRotation){var o=e.textOrigin;"center"===o?(i=n.width/2+n.x,r=n.height/2+n.y):o&&(i=o[0]+n.x,r=o[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r)}}function Ee(t,e,n,i,r,o,a,s){var l=i.rich[n.styleName]||{},h=n.textVerticalAlign,u=o+r/2;"top"===h?u=o+n.height/2:"bottom"===h&&(u=o+r-n.height/2),!n.isLineHolder&&Ne(l)&&Re(t,e,l,"right"===s?a-n.width:"center"===s?a-n.width/2:a,u-n.height/2,n.width,n.height);var c=n.textPadding;c&&(a=Ze(a,s,c),u-=n.height/2-c[2]-n.textHeight/2),Fe(e,"shadowBlur",D(l.textShadowBlur,i.textShadowBlur,0)),Fe(e,"shadowColor",l.textShadowColor||i.textShadowColor||"transparent"),Fe(e,"shadowOffsetX",D(l.textShadowOffsetX,i.textShadowOffsetX,0)),Fe(e,"shadowOffsetY",D(l.textShadowOffsetY,i.textShadowOffsetY,0)),Fe(e,"textAlign",s),Fe(e,"textBaseline","middle"),Fe(e,"font",n.font||yg);var d=He(l.textStroke||i.textStroke,p),f=Ge(l.textFill||i.textFill),p=C(l.textStrokeWidth,i.textStrokeWidth);d&&(Fe(e,"lineWidth",p),Fe(e,"strokeStyle",d),e.strokeText(n.text,a,u)),f&&(Fe(e,"fillStyle",f),e.fillText(n.text,a,u))}function Ne(t){return t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor}function Re(t,e,n,i,r,o,a){var s=n.textBackgroundColor,l=n.textBorderWidth,h=n.textBorderColor,u=_(s);if(Fe(e,"shadowBlur",n.textBoxShadowBlur||0),Fe(e,"shadowColor",n.textBoxShadowColor||"transparent"),Fe(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),Fe(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),u||l&&h){e.beginPath();var c=n.textBorderRadius;c?Ce(e,{x:i,y:r,width:o,height:a,r:c}):e.rect(i,r,o,a),e.closePath()}if(u)Fe(e,"fillStyle",s),e.fill();else if(w(s)){var d=s.image;(d=se(d,null,t,Be,s))&&he(d)&&e.drawImage(d,i,r,o,a)}l&&h&&(Fe(e,"lineWidth",l),Fe(e,"strokeStyle",h),e.stroke())}function Be(t,e){e.image=t}function Ve(t,e,n){var i=e.x||0,r=e.y||0,o=e.textAlign,a=e.textVerticalAlign;if(n){var s=e.textPosition;if(s instanceof Array)i=n.x+We(s[0],n.width),r=n.y+We(s[1],n.height);else{var l=me(s,n,e.textDistance);i=l.x,r=l.y,o=o||l.textAlign,a=a||l.textVerticalAlign}var h=e.textOffset;h&&(i+=h[0],r+=h[1])}return{baseX:i,baseY:r,textAlign:o,textVerticalAlign:a}}function Fe(t,e,n){return t[e]=rg(t,e,n),t[e]}function He(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ge(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function We(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Ze(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Ue(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Xe(t){t=t||{},qp.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new ag(t.style,this),this._rect=null,this.__clipPaths=[]}function je(t){Xe.call(this,t)}function Ye(t){return parseInt(t,10)}function qe(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}function $e(t,e,n){return Mg.copy(t.getBoundingRect()),t.transform&&Mg.applyTransform(t.transform),Ig.width=e,Ig.height=n,!Mg.intersect(Ig)}function Ke(t,e){if(t==e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0}function Qe(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}function Je(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}function tn(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function en(t,e,n,i){return n=n||{},i||!Kf.canvasSupported?nn(t,e,n):Kf.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):nn(t,e,n),n}function nn(t,e,n){var i=tn(t);n.zrX=e.clientX-i.left,n.zrY=e.clientY-i.top}function rn(t,e,n){if(null!=(e=e||window.event).zrX)return e;var i=e.type;if(i&&i.indexOf("touch")>=0){var r="touchend"!=i?e.targetTouches[0]:e.changedTouches[0];r&&en(t,r,e,n)}else en(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var o=e.button;return null==e.which&&void 0!==o&&Dg.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function on(t,e,n){Cg?t.addEventListener(e,n):t.attachEvent("on"+e,n)}function an(t,e,n){Cg?t.removeEventListener(e,n):t.detachEvent("on"+e,n)}function sn(t){return t.which>1}function ln(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}function hn(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}function un(t){return"mousewheel"===t&&Kf.browser.firefox?"DOMMouseScroll":t}function cn(t,e,n){var i=t._gestureMgr;"start"===n&&i.clear();var r=i.recognize(e,t.handler.findHover(e.zrX,e.zrY,null).target,t.dom);if("end"===n&&i.clear(),r){var o=r.type;e.gestureEvent=o,t.handler.dispatchToElement({target:r.target},o,r.event)}}function dn(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function fn(t){var e=t.pointerType;return"pen"===e||"touch"===e}function pn(t){function e(t,e){return function(){if(!e._touching)return t.apply(e,arguments)}}d(zg,function(e){t._handlers[e]=m(Rg[e],t)}),d(Ng,function(e){t._handlers[e]=m(Rg[e],t)}),d(Og,function(n){t._handlers[n]=e(Rg[n],t)})}function gn(t){function e(e,n){d(e,function(e){on(t,un(e),n._handlers[e])},n)}xp.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._gestureMgr=new Pg,this._handlers={},pn(this),Kf.pointerEventsSupported?e(Ng,this):(Kf.touchEventsSupported&&e(zg,this),e(Og,this))}function mn(t,e){var n=new Gg(qf(),t,e);return Hg[n.id]=n,n}function vn(t,e){Fg[t]=e}function yn(t){delete Hg[t]}function xn(t){return t instanceof Array?t:null==t?[]:[t]}function _n(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}function wn(t){return!Ug(t)||Xg(t)||t instanceof Date?t:t.value}function bn(t){return Ug(t)&&!(t instanceof Array)}function Sn(t,e){e=(e||[]).slice();var n=f(t||[],function(t,e){return{exist:t}});return Zg(e,function(t,i){if(Ug(t)){for(r=0;r<n.length;r++)if(!n[r].option&&null!=t.id&&n[r].exist.id===t.id+"")return n[r].option=t,void(e[i]=null);for(var r=0;r<n.length;r++){var o=n[r].exist;if(!(n[r].option||null!=o.id&&null!=t.id||null==t.name||Tn(t)||Tn(o)||o.name!==t.name+""))return n[r].option=t,void(e[i]=null)}}}),Zg(e,function(t,e){if(Ug(t)){for(var i=0;i<n.length;i++){var r=n[i].exist;if(!n[i].option&&!Tn(r)&&null==t.id){n[i].option=t;break}}i>=n.length&&n.push({option:t})}}),n}function Mn(t){var e=N();Zg(t,function(t,n){var i=t.exist;i&&e.set(i.id,t)}),Zg(t,function(t,n){var i=t.option;P(!i||null==i.id||!e.get(i.id)||e.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&e.set(i.id,t),!t.keyInfo&&(t.keyInfo={})}),Zg(t,function(t,n){var i=t.exist,r=t.option,o=t.keyInfo;if(Ug(r)){if(o.name=null!=r.name?r.name+"":i?i.name:jg+n,i)o.id=i.id;else if(null!=r.id)o.id=r.id+"";else{var a=0;do{o.id="\0"+o.name+"\0"+a++}while(e.get(o.id))}e.set(o.id,t)}})}function In(t){var e=t.name;return!(!e||!e.indexOf(jg))}function Tn(t){return Ug(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")}function Cn(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?y(e.dataIndex)?f(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?y(e.name)?f(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0}function Dn(){var t="__\0ec_inner_"+qg+++"_"+Math.random().toFixed(5);return function(e){return e[t]||(e[t]={})}}function An(t,e,n){if(_(e)){var i={};i[e+"Index"]=0,e=i}var r=n&&n.defaultMainType;!r||kn(e,r+"Index")||kn(e,r+"Id")||kn(e,r+"Name")||(e[r+"Index"]=0);var o={};return Zg(e,function(i,r){var i=e[r];if("dataIndex"!==r&&"dataIndexInside"!==r){var a=r.match(/^(\w+)(Index|Id|Name)$/)||[],s=a[1],h=(a[2]||"").toLowerCase();if(!(!s||!h||null==i||"index"===h&&"none"===i||n&&n.includeMainTypes&&l(n.includeMainTypes,s)<0)){var u={mainType:s};"index"===h&&"all"===i||(u[h]=i);var c=t.queryComponents(u);o[s+"Models"]=c,o[s+"Model"]=c[0]}}else o[r]=i}),o}function kn(t,e){return t&&t.hasOwnProperty(e)}function Pn(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function Ln(t,e){return t.getAttribute?t.getAttribute(e):t[e]}function On(t){var e={main:"",sub:""};return t&&(t=t.split($g),e.main=t[0]||"",e.sub=t[1]||""),e}function zn(t){P(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function En(t,e){t.$constructor=t,t.extend=function(t){var e=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return o(n.prototype,t),n.extend=this.extend,n.superCall=Rn,n.superApply=Bn,h(n,this),n.superClass=e,n}}function Nn(t){var e=["__\0is_clz",Qg++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,t.isInstance=function(t){return!(!t||!t[e])}}function Rn(t,e){var n=A(arguments,2);return this.superClass.prototype[e].apply(t,n)}function Bn(t,e,n){return this.superClass.prototype[e].apply(t,n)}function Vn(t,e){function n(t){var e=i[t.main];return e&&e[Kg]||((e=i[t.main]={})[Kg]=!0),e}e=e||{};var i={};if(t.registerClass=function(t,e){return e&&(zn(e),(e=On(e)).sub?e.sub!==Kg&&(n(e)[e.sub]=t):i[e.main]=t),t},t.getClass=function(t,e,n){var r=i[t];if(r&&r[Kg]&&(r=e?r[e]:null),n&&!r)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){t=On(t);var e=[],n=i[t.main];return n&&n[Kg]?d(n,function(t,n){n!==Kg&&e.push(t)}):e.push(n),e},t.hasClass=function(t){return t=On(t),!!i[t.main]},t.getAllClassMainTypes=function(){var t=[];return d(i,function(e,n){t.push(n)}),t},t.hasSubTypes=function(t){t=On(t);var e=i[t.main];return e&&e[Kg]},t.parseClassType=On,e.registerWhenExtend){var r=t.extend;r&&(t.extend=function(e){var n=r.call(this,e);return t.registerClass(n,e.type)})}return t}function Fn(t){return t>-am&&t<am}function Hn(t){return t>am||t<-am}function Gn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function Wn(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function Zn(t,e,n,i,r,o){var a=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),h=t-r,u=s*s-3*a*l,c=s*l-9*a*h,d=l*l-3*s*h,f=0;if(Fn(u)&&Fn(c))Fn(s)?o[0]=0:(M=-l/s)>=0&&M<=1&&(o[f++]=M);else{var p=c*c-4*u*d;if(Fn(p)){var g=c/u,m=-g/2;(M=-s/a+g)>=0&&M<=1&&(o[f++]=M),m>=0&&m<=1&&(o[f++]=m)}else if(p>0){var v=om(p),y=u*s+1.5*a*(-c+v),x=u*s+1.5*a*(-c-v);(M=(-s-((y=y<0?-rm(-y,hm):rm(y,hm))+(x=x<0?-rm(-x,hm):rm(x,hm))))/(3*a))>=0&&M<=1&&(o[f++]=M)}else{var _=(2*u*s-3*a*c)/(2*om(u*u*u)),w=Math.acos(_)/3,b=om(u),S=Math.cos(w),M=(-s-2*b*S)/(3*a),m=(-s+b*(S+lm*Math.sin(w)))/(3*a),I=(-s+b*(S-lm*Math.sin(w)))/(3*a);M>=0&&M<=1&&(o[f++]=M),m>=0&&m<=1&&(o[f++]=m),I>=0&&I<=1&&(o[f++]=I)}}return f}function Un(t,e,n,i,r){var o=6*n-12*e+6*t,a=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(Fn(a))Hn(o)&&(c=-s/o)>=0&&c<=1&&(r[l++]=c);else{var h=o*o-4*a*s;if(Fn(h))r[0]=-o/(2*a);else if(h>0){var u=om(h),c=(-o+u)/(2*a),d=(-o-u)/(2*a);c>=0&&c<=1&&(r[l++]=c),d>=0&&d<=1&&(r[l++]=d)}}return l}function Xn(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,h=(s-a)*r+a,u=(l-s)*r+s,c=(u-h)*r+h;o[0]=t,o[1]=a,o[2]=h,o[3]=c,o[4]=c,o[5]=u,o[6]=l,o[7]=i}function jn(t,e,n,i,r,o,a,s,l,h,u){var c,d,f,p,g,m=.005,v=1/0;um[0]=l,um[1]=h;for(var y=0;y<1;y+=.05)cm[0]=Gn(t,n,r,a,y),cm[1]=Gn(e,i,o,s,y),(p=mp(um,cm))<v&&(c=y,v=p);v=1/0;for(var x=0;x<32&&!(m<sm);x++)d=c-m,f=c+m,cm[0]=Gn(t,n,r,a,d),cm[1]=Gn(e,i,o,s,d),p=mp(cm,um),d>=0&&p<v?(c=d,v=p):(dm[0]=Gn(t,n,r,a,f),dm[1]=Gn(e,i,o,s,f),g=mp(dm,um),f<=1&&g<v?(c=f,v=g):m*=.5);return u&&(u[0]=Gn(t,n,r,a,c),u[1]=Gn(e,i,o,s,c)),om(v)}function Yn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function qn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function $n(t,e,n,i,r){var o=t-2*e+n,a=2*(e-t),s=t-i,l=0;if(Fn(o))Hn(a)&&(c=-s/a)>=0&&c<=1&&(r[l++]=c);else{var h=a*a-4*o*s;if(Fn(h))(c=-a/(2*o))>=0&&c<=1&&(r[l++]=c);else if(h>0){var u=om(h),c=(-a+u)/(2*o),d=(-a-u)/(2*o);c>=0&&c<=1&&(r[l++]=c),d>=0&&d<=1&&(r[l++]=d)}}return l}function Kn(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function Qn(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function Jn(t,e,n,i,r,o,a,s,l){var h,u=.005,c=1/0;um[0]=a,um[1]=s;for(var d=0;d<1;d+=.05)cm[0]=Yn(t,n,r,d),cm[1]=Yn(e,i,o,d),(m=mp(um,cm))<c&&(h=d,c=m);c=1/0;for(var f=0;f<32&&!(u<sm);f++){var p=h-u,g=h+u;cm[0]=Yn(t,n,r,p),cm[1]=Yn(e,i,o,p);var m=mp(cm,um);if(p>=0&&m<c)h=p,c=m;else{dm[0]=Yn(t,n,r,g),dm[1]=Yn(e,i,o,g);var v=mp(dm,um);g<=1&&v<c?(h=g,c=v):u*=.5}}return l&&(l[0]=Yn(t,n,r,h),l[1]=Yn(e,i,o,h)),om(c)}function ti(t,e,n){if(0!==t.length){var i,r=t[0],o=r[0],a=r[0],s=r[1],l=r[1];for(i=1;i<t.length;i++)r=t[i],o=fm(o,r[0]),a=pm(a,r[0]),s=fm(s,r[1]),l=pm(l,r[1]);e[0]=o,e[1]=s,n[0]=a,n[1]=l}}function ei(t,e,n,i,r,o){r[0]=fm(t,n),r[1]=fm(e,i),o[0]=pm(t,n),o[1]=pm(e,i)}function ni(t,e,n,i,r,o,a,s,l,h){var u,c=Un,d=Gn,f=c(t,n,r,a,wm);for(l[0]=1/0,l[1]=1/0,h[0]=-1/0,h[1]=-1/0,u=0;u<f;u++){var p=d(t,n,r,a,wm[u]);l[0]=fm(p,l[0]),h[0]=pm(p,h[0])}for(f=c(e,i,o,s,bm),u=0;u<f;u++){var g=d(e,i,o,s,bm[u]);l[1]=fm(g,l[1]),h[1]=pm(g,h[1])}l[0]=fm(t,l[0]),h[0]=pm(t,h[0]),l[0]=fm(a,l[0]),h[0]=pm(a,h[0]),l[1]=fm(e,l[1]),h[1]=pm(e,h[1]),l[1]=fm(s,l[1]),h[1]=pm(s,h[1])}function ii(t,e,n,i,r,o,a,s){var l=Kn,h=Yn,u=pm(fm(l(t,n,r),1),0),c=pm(fm(l(e,i,o),1),0),d=h(t,n,r,u),f=h(e,i,o,c);a[0]=fm(t,r,d),a[1]=fm(e,o,f),s[0]=pm(t,r,d),s[1]=pm(e,o,f)}function ri(t,e,n,i,r,o,a,s,l){var h=K,u=Q,c=Math.abs(r-o);if(c%vm<1e-4&&c>1e-4)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(ym[0]=mm(r)*n+t,ym[1]=gm(r)*i+e,xm[0]=mm(o)*n+t,xm[1]=gm(o)*i+e,h(s,ym,xm),u(l,ym,xm),(r%=vm)<0&&(r+=vm),(o%=vm)<0&&(o+=vm),r>o&&!a?o+=vm:r<o&&a&&(r+=vm),a){var d=o;o=r,r=d}for(var f=0;f<o;f+=Math.PI/2)f>r&&(_m[0]=mm(f)*n+t,_m[1]=gm(f)*i+e,h(s,_m,s),u(l,_m,l))}function oi(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0,h=t;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>n+s||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;var u=(l=(e-i)/(t-n))*o-a+(h=(t*i-n*e)/(t-n));return u*u/(l*l+1)<=s/2*s/2}function ai(t,e,n,i,r,o,a,s,l,h,u){if(0===l)return!1;var c=l;return!(u>e+c&&u>i+c&&u>o+c&&u>s+c||u<e-c&&u<i-c&&u<o-c&&u<s-c||h>t+c&&h>n+c&&h>r+c&&h>a+c||h<t-c&&h<n-c&&h<r-c&&h<a-c)&&jn(t,e,n,i,r,o,a,s,h,u,null)<=c/2}function si(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var h=a;return!(l>e+h&&l>i+h&&l>o+h||l<e-h&&l<i-h&&l<o-h||s>t+h&&s>n+h&&s>r+h||s<t-h&&s<n-h&&s<r-h)&&Jn(t,e,n,i,r,o,s,l,null)<=h/2}function li(t){return(t%=Nm)<0&&(t+=Nm),t}function hi(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var h=a;s-=t,l-=e;var u=Math.sqrt(s*s+l*l);if(u-h>n||u+h<n)return!1;if(Math.abs(i-r)%Rm<1e-4)return!0;if(o){var c=i;i=li(r),r=li(c)}else i=li(i),r=li(r);i>r&&(r+=Rm);var d=Math.atan2(l,s);return d<0&&(d+=Rm),d>=i&&d<=r||d+Rm>=i&&d+Rm<=r}function ui(t,e,n,i,r,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=i<e?1:-1,s=(o-e)/(i-e);return 1!==s&&0!==s||(a=i<e?.5:-.5),s*(n-t)+t>r?a:0}function ci(t,e){return Math.abs(t-e)<Fm}function di(){var t=Gm[0];Gm[0]=Gm[1],Gm[1]=t}function fi(t,e,n,i,r,o,a,s,l,h){if(h>e&&h>i&&h>o&&h>s||h<e&&h<i&&h<o&&h<s)return 0;var u=Zn(e,i,o,s,h,Hm);if(0===u)return 0;for(var c,d,f=0,p=-1,g=0;g<u;g++){var m=Hm[g],v=0===m||1===m?.5:1;Gn(t,n,r,a,m)<l||(p<0&&(p=Un(e,i,o,s,Gm),Gm[1]<Gm[0]&&p>1&&di(),c=Gn(e,i,o,s,Gm[0]),p>1&&(d=Gn(e,i,o,s,Gm[1]))),2==p?m<Gm[0]?f+=c<e?v:-v:m<Gm[1]?f+=d<c?v:-v:f+=s<d?v:-v:m<Gm[0]?f+=c<e?v:-v:f+=s<c?v:-v)}return f}function pi(t,e,n,i,r,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var l=$n(e,i,o,s,Hm);if(0===l)return 0;var h=Kn(e,i,o);if(h>=0&&h<=1){for(var u=0,c=Yn(e,i,o,h),d=0;d<l;d++){f=0===Hm[d]||1===Hm[d]?.5:1;(p=Yn(t,n,r,Hm[d]))<a||(Hm[d]<h?u+=c<e?f:-f:u+=o<c?f:-f)}return u}var f=0===Hm[0]||1===Hm[0]?.5:1,p=Yn(t,n,r,Hm[0]);return p<a?0:o<e?f:-f}function gi(t,e,n,i,r,o,a,s){if((s-=e)>n||s<-n)return 0;h=Math.sqrt(n*n-s*s);Hm[0]=-h,Hm[1]=h;var l=Math.abs(i-r);if(l<1e-4)return 0;if(l%Vm<1e-4){i=0,r=Vm;p=o?1:-1;return a>=Hm[0]+t&&a<=Hm[1]+t?p:0}if(o){var h=i;i=li(r),r=li(h)}else i=li(i),r=li(r);i>r&&(r+=Vm);for(var u=0,c=0;c<2;c++){var d=Hm[c];if(d+t>a){var f=Math.atan2(s,d),p=o?1:-1;f<0&&(f=Vm+f),(f>=i&&f<=r||f+Vm>=i&&f+Vm<=r)&&(f>Math.PI/2&&f<1.5*Math.PI&&(p=-p),u+=p)}}return u}function mi(t,e,n,i,r){for(var o=0,a=0,s=0,l=0,h=0,u=0;u<t.length;){var c=t[u++];switch(c===Bm.M&&u>1&&(n||(o+=ui(a,s,l,h,i,r))),1==u&&(l=a=t[u],h=s=t[u+1]),c){case Bm.M:a=l=t[u++],s=h=t[u++];break;case Bm.L:if(n){if(oi(a,s,t[u],t[u+1],e,i,r))return!0}else o+=ui(a,s,t[u],t[u+1],i,r)||0;a=t[u++],s=t[u++];break;case Bm.C:if(n){if(ai(a,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],e,i,r))return!0}else o+=fi(a,s,t[u++],t[u++],t[u++],t[u++],t[u],t[u+1],i,r)||0;a=t[u++],s=t[u++];break;case Bm.Q:if(n){if(si(a,s,t[u++],t[u++],t[u],t[u+1],e,i,r))return!0}else o+=pi(a,s,t[u++],t[u++],t[u],t[u+1],i,r)||0;a=t[u++],s=t[u++];break;case Bm.A:var d=t[u++],f=t[u++],p=t[u++],g=t[u++],m=t[u++],v=t[u++],y=(t[u++],1-t[u++]),x=Math.cos(m)*p+d,_=Math.sin(m)*g+f;u>1?o+=ui(a,s,x,_,i,r):(l=x,h=_);var w=(i-d)*g/p+d;if(n){if(hi(d,f,g,m,m+v,y,e,w,r))return!0}else o+=gi(d,f,g,m,m+v,y,w,r);a=Math.cos(m+v)*p+d,s=Math.sin(m+v)*g+f;break;case Bm.R:l=a=t[u++],h=s=t[u++];var x=l+t[u++],_=h+t[u++];if(n){if(oi(l,h,x,h,e,i,r)||oi(x,h,x,_,e,i,r)||oi(x,_,l,_,e,i,r)||oi(l,_,l,h,e,i,r))return!0}else o+=ui(x,h,x,_,i,r),o+=ui(l,_,l,h,i,r);break;case Bm.Z:if(n){if(oi(a,s,l,h,e,i,r))return!0}else o+=ui(a,s,l,h,i,r);a=l,s=h}}return n||ci(s,h)||(o+=ui(a,s,l,h,i,r)||0),0!==o}function vi(t,e,n){return mi(t,0,!1,e,n)}function yi(t,e,n,i){return mi(t,e,!0,n,i)}function xi(t){Xe.call(this,t),this.path=null}function _i(t,e,n,i,r,o,a,s,l,h,u){var c=l*(ev/180),d=tv(c)*(t-n)/2+Jm(c)*(e-i)/2,f=-1*Jm(c)*(t-n)/2+tv(c)*(e-i)/2,p=d*d/(a*a)+f*f/(s*s);p>1&&(a*=Qm(p),s*=Qm(p));var g=(r===o?-1:1)*Qm((a*a*(s*s)-a*a*(f*f)-s*s*(d*d))/(a*a*(f*f)+s*s*(d*d)))||0,m=g*a*f/s,v=g*-s*d/a,y=(t+n)/2+tv(c)*m-Jm(c)*v,x=(e+i)/2+Jm(c)*m+tv(c)*v,_=rv([1,0],[(d-m)/a,(f-v)/s]),w=[(d-m)/a,(f-v)/s],b=[(-1*d-m)/a,(-1*f-v)/s],S=rv(w,b);iv(w,b)<=-1&&(S=ev),iv(w,b)>=1&&(S=0),0===o&&S>0&&(S-=2*ev),1===o&&S<0&&(S+=2*ev),u.addData(h,y,x,a,s,_,S,c,o)}function wi(t){if(!t)return[];var e,n=t.replace(/-/g," -").replace(/  /g," ").replace(/ /g,",").replace(/,,/g,",");for(e=0;e<Km.length;e++)n=n.replace(new RegExp(Km[e],"g"),"|"+Km[e]);var i,r=n.split("|"),o=0,a=0,s=new Em,l=Em.CMD;for(e=1;e<r.length;e++){var h,u=r[e],c=u.charAt(0),d=0,f=u.slice(1).replace(/e,-/g,"e-").split(",");f.length>0&&""===f[0]&&f.shift();for(var p=0;p<f.length;p++)f[p]=parseFloat(f[p]);for(;d<f.length&&!isNaN(f[d])&&!isNaN(f[0]);){var g,m,v,y,x,_,w,b=o,S=a;switch(c){case"l":o+=f[d++],a+=f[d++],h=l.L,s.addData(h,o,a);break;case"L":o=f[d++],a=f[d++],h=l.L,s.addData(h,o,a);break;case"m":o+=f[d++],a+=f[d++],h=l.M,s.addData(h,o,a),c="l";break;case"M":o=f[d++],a=f[d++],h=l.M,s.addData(h,o,a),c="L";break;case"h":o+=f[d++],h=l.L,s.addData(h,o,a);break;case"H":o=f[d++],h=l.L,s.addData(h,o,a);break;case"v":a+=f[d++],h=l.L,s.addData(h,o,a);break;case"V":a=f[d++],h=l.L,s.addData(h,o,a);break;case"C":h=l.C,s.addData(h,f[d++],f[d++],f[d++],f[d++],f[d++],f[d++]),o=f[d-2],a=f[d-1];break;case"c":h=l.C,s.addData(h,f[d++]+o,f[d++]+a,f[d++]+o,f[d++]+a,f[d++]+o,f[d++]+a),o+=f[d-2],a+=f[d-1];break;case"S":g=o,m=a;var M=s.len(),I=s.data;i===l.C&&(g+=o-I[M-4],m+=a-I[M-3]),h=l.C,b=f[d++],S=f[d++],o=f[d++],a=f[d++],s.addData(h,g,m,b,S,o,a);break;case"s":g=o,m=a;var M=s.len(),I=s.data;i===l.C&&(g+=o-I[M-4],m+=a-I[M-3]),h=l.C,b=o+f[d++],S=a+f[d++],o+=f[d++],a+=f[d++],s.addData(h,g,m,b,S,o,a);break;case"Q":b=f[d++],S=f[d++],o=f[d++],a=f[d++],h=l.Q,s.addData(h,b,S,o,a);break;case"q":b=f[d++]+o,S=f[d++]+a,o+=f[d++],a+=f[d++],h=l.Q,s.addData(h,b,S,o,a);break;case"T":g=o,m=a;var M=s.len(),I=s.data;i===l.Q&&(g+=o-I[M-4],m+=a-I[M-3]),o=f[d++],a=f[d++],h=l.Q,s.addData(h,g,m,o,a);break;case"t":g=o,m=a;var M=s.len(),I=s.data;i===l.Q&&(g+=o-I[M-4],m+=a-I[M-3]),o+=f[d++],a+=f[d++],h=l.Q,s.addData(h,g,m,o,a);break;case"A":v=f[d++],y=f[d++],x=f[d++],_=f[d++],w=f[d++],_i(b=o,S=a,o=f[d++],a=f[d++],_,w,v,y,x,h=l.A,s);break;case"a":v=f[d++],y=f[d++],x=f[d++],_=f[d++],w=f[d++],_i(b=o,S=a,o+=f[d++],a+=f[d++],_,w,v,y,x,h=l.A,s)}}"z"!==c&&"Z"!==c||(h=l.Z,s.addData(h)),i=h}return s.toStatic(),s}function bi(t,e){var n=wi(t);return e=e||{},e.buildPath=function(t){if(t.setData)t.setData(n.data),(e=t.getContext())&&t.rebuildPath(e);else{var e=t;n.rebuildPath(e)}},e.applyTransform=function(t){$m(n,t),this.dirty(!0)},e}function Si(t,e){return new xi(bi(t,e))}function Mi(t,e){return xi.extend(bi(t,e))}function Ii(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function Ti(t,e,n){var i=e.points,r=e.smooth;if(i&&i.length>=2){if(r&&"spline"!==r){var o=dv(i,r,n,e.smoothConstraint);t.moveTo(i[0][0],i[0][1]);for(var a=i.length,s=0;s<(n?a:a-1);s++){var l=o[2*s],h=o[2*s+1],u=i[(s+1)%a];t.bezierCurveTo(l[0],l[1],h[0],h[1],u[0],u[1])}}else{"spline"===r&&(i=cv(i,n)),t.moveTo(i[0][0],i[0][1]);for(var s=1,c=i.length;s<c;s++)t.lineTo(i[s][0],i[s][1])}n&&t.closePath()}}function Ci(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?Wn:Gn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?Wn:Gn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?qn:Yn)(t.x1,t.cpx1,t.x2,e),(n?qn:Yn)(t.y1,t.cpy1,t.y2,e)]}function Di(t){Xe.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}function Ai(t){return xi.extend(t)}function ki(t,e,n,i){var r=Si(t,e),o=r.getBoundingRect();return n&&("center"===i&&(n=Li(n,o)),Oi(r,n)),r}function Pi(t,e,n){var i=new je({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(Li(e,r))}}});return i}function Li(t,e){var n,i=e.width/e.height,r=t.height*i;return n=r<=t.width?t.height:(r=t.width)/i,{x:t.x+t.width/2-r/2,y:t.y+t.height/2-n/2,width:r,height:n}}function Oi(t,e){if(t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}function zi(t){var e=t.shape,n=t.style.lineWidth;return Iv(2*e.x1)===Iv(2*e.x2)&&(e.x1=e.x2=Ni(e.x1,n,!0)),Iv(2*e.y1)===Iv(2*e.y2)&&(e.y1=e.y2=Ni(e.y1,n,!0)),t}function Ei(t){var e=t.shape,n=t.style.lineWidth,i=e.x,r=e.y,o=e.width,a=e.height;return e.x=Ni(e.x,n,!0),e.y=Ni(e.y,n,!0),e.width=Math.max(Ni(i+o,n,!1)-e.x,0===o?0:1),e.height=Math.max(Ni(r+a,n,!1)-e.y,0===a?0:1),t}function Ni(t,e,n){var i=Iv(2*t);return(i+Iv(e))%2==0?i/2:(i+(n?1:-1))/2}function Ri(t){return null!=t&&"none"!=t}function Bi(t){return"string"==typeof t?Ct(t,-.1):t}function Vi(t){if(t.__hoverStlDirty){var e=t.style.stroke,n=t.style.fill,i=t.__hoverStl;i.fill=i.fill||(Ri(n)?Bi(n):null),i.stroke=i.stroke||(Ri(e)?Bi(e):null);var r={};for(var o in i)null!=i[o]&&(r[o]=t.style[o]);t.__normalStl=r,t.__hoverStlDirty=!1}}function Fi(t){if(!t.__isHover){if(Vi(t),t.useHoverLayer)t.__zr&&t.__zr.addHover(t,t.__hoverStl);else{var e=t.style,n=e.insideRollbackOpt;n&&ir(e),e.extendFrom(t.__hoverStl),n&&(nr(e,e.insideOriginalTextPosition,n),null==e.textFill&&(e.textFill=n.autoColor)),t.dirty(!1),t.z2+=1}t.__isHover=!0}}function Hi(t){if(t.__isHover){var e=t.__normalStl;t.useHoverLayer?t.__zr&&t.__zr.removeHover(t):(e&&t.setStyle(e),t.z2-=1),t.__isHover=!1}}function Gi(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&Fi(t)}):Fi(t)}function Wi(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&Hi(t)}):Hi(t)}function Zi(t,e){t.__hoverStl=t.hoverStyle||e||{},t.__hoverStlDirty=!0,t.__isHover&&Vi(t)}function Ui(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&Gi(this)}function Xi(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&Wi(this)}function ji(){this.__isEmphasis=!0,Gi(this)}function Yi(){this.__isEmphasis=!1,Wi(this)}function qi(t,e,n){t.__hoverSilentOnTouch=n&&n.hoverSilentOnTouch,"group"===t.type?t.traverse(function(t){"group"!==t.type&&Zi(t,e)}):Zi(t,e),t.on("mouseover",Ui).on("mouseout",Xi),t.on("emphasis",ji).on("normal",Yi)}function $i(t,e,n,i,r,o,a){var s,l=(r=r||Dv).labelFetcher,h=r.labelDataIndex,u=r.labelDimIndex,c=n.getShallow("show"),d=i.getShallow("show");(c||d)&&(l&&(s=l.getFormattedLabel(h,"normal",null,u)),null==s&&(s=x(r.defaultText)?r.defaultText(h,r):r.defaultText));var f=c?s:null,p=d?C(l?l.getFormattedLabel(h,"emphasis",null,u):null,s):null;null==f&&null==p||(Ki(t,n,o,r),Ki(e,i,a,r,!0)),t.text=f,e.text=p}function Ki(t,e,n,i,r){return Qi(t,e,i,r),n&&o(t,n),t.host&&t.host.dirty&&t.host.dirty(!1),t}function Qi(t,e,n,i){if((n=n||Dv).isRectText){var r=e.getShallow("position")||(i?null:"inside");"outside"===r&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset");var o=e.getShallow("rotate");null!=o&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=C(e.getShallow("distance"),i?null:5)}var a,s=e.ecModel,l=s&&s.option.textStyle,h=Ji(e);if(h){a={};for(var u in h)if(h.hasOwnProperty(u)){var c=e.getModel(["rich",u]);tr(a[u]={},c,l,n,i)}}return t.rich=a,tr(t,e,l,n,i,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function Ji(t){for(var e;t&&t!==t.ecModel;){var n=(t.option||Dv).rich;if(n){e=e||{};for(var i in n)n.hasOwnProperty(i)&&(e[i]=1)}t=t.parentModel}return e}function tr(t,e,n,i,r,o){if(n=!r&&n||Dv,t.textFill=er(e.getShallow("color"),i)||n.color,t.textStroke=er(e.getShallow("textBorderColor"),i)||n.textBorderColor,t.textStrokeWidth=C(e.getShallow("textBorderWidth"),n.textBorderWidth),!r){if(o){var a=t.textPosition;t.insideRollback=nr(t,a,i),t.insideOriginalTextPosition=a,t.insideRollbackOpt=i}null==t.textFill&&(t.textFill=i.autoColor)}t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&i.disableBox||(t.textBackgroundColor=er(e.getShallow("backgroundColor"),i),t.textPadding=e.getShallow("padding"),t.textBorderColor=er(e.getShallow("borderColor"),i),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function er(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function nr(t,e,n){var i,r=n.useInsideStyle;return null==t.textFill&&!1!==r&&(!0===r||n.isRectText&&e&&"string"==typeof e&&e.indexOf("inside")>=0)&&(i={textFill:null,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth},t.textFill="#fff",null==t.textStroke&&(t.textStroke=n.autoColor,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),i}function ir(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth)}function rr(t,e){var n=e||e.getModel("textStyle");return L([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}function or(t,e,n,i,r,o){if("function"==typeof r&&(o=r,r=null),i&&i.isAnimationEnabled()){var a=t?"Update":"",s=i.getShallow("animationDuration"+a),l=i.getShallow("animationEasing"+a),h=i.getShallow("animationDelay"+a);"function"==typeof h&&(h=h(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"==typeof s&&(s=s(r)),s>0?e.animateTo(n,s,h||0,l,o,!!o):(e.stopAnimation(),e.attr(n),o&&o())}else e.stopAnimation(),e.attr(n),o&&o()}function ar(t,e,n,i,r){or(!0,t,e,n,i,r)}function sr(t,e,n,i,r){or(!1,t,e,n,i,r)}function lr(t,e){for(var n=ot([]);t&&t!==e;)st(n,t.getLocalTransform(),n),t=t.parent;return n}function hr(t,e,n){return e&&!c(e)&&(e=Cp.getLocalTransform(e)),n&&(e=ct([],e)),$([],t,e)}function ur(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return o=hr(o,e,n),Math.abs(o[0])>Math.abs(o[1])?o[0]>0?"right":"left":o[1]>0?"bottom":"top"}function cr(t,e,n,i){function r(t){var e={position:F(t.position),rotation:t.rotation};return t.shape&&(e.shape=o({},t.shape)),e}if(t&&e){var a=function(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var i=r(t);t.attr(r(e)),ar(t,i,n,t.dataIndex)}}})}}function dr(t,e){return f(t,function(t){var n=t[0];n=Tv(n,e.x),n=Cv(n,e.x+e.width);var i=t[1];return i=Tv(i,e.y),i=Cv(i,e.y+e.height),[n,i]})}function fr(t,e,n){var i=(e=o({rectHover:!0},e)).style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),a(i,n),new je(e)):ki(t.replace("path://",""),e,n,"center")}function pr(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function gr(t,e,n){for(var i=0;i<e.length&&(!e[i]||null!=(t=t&&"object"==typeof t?t[e[i]]:null));i++);return null==t&&n&&(t=n.get(e)),t}function mr(t,e){var n=Ev(t).getParent;return n?n.call(t,e):t.parentModel}function vr(t){return[t||"",Nv++,Math.random().toFixed(5)].join("_")}function yr(t){return t.replace(/^\s+/,"").replace(/\s+$/,"")}function xr(t,e,n,i){var r=e[1]-e[0],o=n[1]-n[0];if(0===r)return 0===o?n[0]:(n[0]+n[1])/2;if(i)if(r>0){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*o+n[0]}function _r(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?yr(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function wr(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function br(t){return t.sort(function(t,e){return t-e}),t}function Sr(t){if(t=+t,isNaN(t))return 0;for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n}function Mr(t){var e=t.toString(),n=e.indexOf("e");if(n>0){var i=+e.slice(n+1);return i<0?-i:0}var r=e.indexOf(".");return r<0?0:e.length-1-r}function Ir(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function Tr(t,e,n){if(!t[e])return 0;var i=p(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var r=Math.pow(10,n),o=f(t,function(t){return(isNaN(t)?0:t)/i*r*100}),a=100*r,s=f(o,function(t){return Math.floor(t)}),l=p(s,function(t,e){return t+e},0),h=f(o,function(t,e){return t-s[e]});l<a;){for(var u=Number.NEGATIVE_INFINITY,c=null,d=0,g=h.length;d<g;++d)h[d]>u&&(u=h[d],c=d);++s[c],h[c]=0,++l}return s[e]/r}function Cr(t){var e=2*Math.PI;return(t%e+e)%e}function Dr(t){return t>-Rv&&t<Rv}function Ar(t){if(t instanceof Date)return t;if("string"==typeof t){var e=Bv.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return null==t?new Date(NaN):new Date(Math.round(t))}function kr(t){return Math.pow(10,Pr(t))}function Pr(t){return Math.floor(Math.log(t)/Math.LN10)}function Lr(t,e){var n,i=Pr(t),r=Math.pow(10,i),o=t/r;return n=e?o<1.5?1:o<2.5?2:o<4?3:o<7?5:10:o<1?1:o<2?2:o<3?3:o<5?5:10,t=n*r,i>=-20?+t.toFixed(i<0?-i:0):t}function Or(t){return isNaN(t)?"-":(t=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:"")}function zr(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}function Er(t){return String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}function Nr(t,e,n){y(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=Hv[o];t=t.replace(Gv(a),Gv(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var h=e[s][r[l]];t=t.replace(Gv(Hv[l],s),n?Er(h):h)}return t}function Rr(t,e){var n=(t=_(t)?{color:t,extraCssText:e}:t||{}).color,i=t.type,e=t.extraCssText;return n?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+Er(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+Er(n)+";"+(e||"")+'"></span>':""}function Br(t,e){return t+="","0000".substr(0,e-t.length)+t}function Vr(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=Ar(e),r=n?"UTC":"",o=i["get"+r+"FullYear"](),a=i["get"+r+"Month"]()+1,s=i["get"+r+"Date"](),l=i["get"+r+"Hours"](),h=i["get"+r+"Minutes"](),u=i["get"+r+"Seconds"](),c=i["get"+r+"Milliseconds"]();return t=t.replace("MM",Br(a,2)).replace("M",a).replace("yyyy",o).replace("yy",o%100).replace("dd",Br(s,2)).replace("d",s).replace("hh",Br(l,2)).replace("h",l).replace("mm",Br(h,2)).replace("m",h).replace("ss",Br(u,2)).replace("s",u).replace("SSS",Br(c,3))}function Fr(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}function Hr(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,h){var u,c,d=l.position,f=l.getBoundingRect(),p=e.childAt(h+1),g=p&&p.getBoundingRect();if("horizontal"===t){var m=f.width+(g?-g.x+f.x:0);(u=o+m)>i||l.newline?(o=0,u=m,a+=s+n,s=f.height):s=Math.max(s,f.height)}else{var v=f.height+(g?-g.y+f.y:0);(c=a+v)>r||l.newline?(o+=s+n,a=0,c=v,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=o,d[1]=a,"horizontal"===t?o=u+n:a=c+n)})}function Gr(t,e,n){n=Fv(n||0);var i=e.width,r=e.height,o=_r(t.left,i),a=_r(t.top,r),s=_r(t.right,i),l=_r(t.bottom,r),h=_r(t.width,i),u=_r(t.height,r),c=n[2]+n[0],d=n[1]+n[3],f=t.aspect;switch(isNaN(h)&&(h=i-s-d-o),isNaN(u)&&(u=r-l-c-a),null!=f&&(isNaN(h)&&isNaN(u)&&(f>i/r?h=.8*i:u=.8*r),isNaN(h)&&(h=f*u),isNaN(u)&&(u=h/f)),isNaN(o)&&(o=i-s-h-d),isNaN(a)&&(a=r-l-u-c),t.left||t.right){case"center":o=i/2-h/2-n[3];break;case"right":o=i-h-d}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-c}o=o||0,a=a||0,isNaN(h)&&(h=i-d-o-(s||0)),isNaN(u)&&(u=r-c-a-(l||0));var p=new Xt(o+n[3],a+n[0],h,u);return p.margin=n,p}function Wr(t,e,n,i,r){var o=!r||!r.hv||r.hv[0],s=!r||!r.hv||r.hv[1],l=r&&r.boundingMode||"all";if(o||s){var h;if("raw"===l)h="group"===t.type?new Xt(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(h=t.getBoundingRect(),t.needLocalTransform()){var u=t.getLocalTransform();(h=h.clone()).applyTransform(u)}e=Gr(a({width:h.width,height:h.height},e),n,i);var c=t.position,d=o?e.x-h.x:0,f=s?e.y-h.y:0;t.attr("position","raw"===l?[d,f]:[c[0]+d,c[1]+f])}}function Zr(t,e,n){function i(n,i){var a={},l=0,h={},u=0;if(Xv(n,function(e){h[e]=t[e]}),Xv(n,function(t){r(e,t)&&(a[t]=h[t]=e[t]),o(a,t)&&l++,o(h,t)&&u++}),s[i])return o(e,n[1])?h[n[2]]=null:o(e,n[2])&&(h[n[1]]=null),h;if(2!==u&&l){if(l>=2)return a;for(var c=0;c<n.length;c++){var d=n[c];if(!r(a,d)&&r(t,d)){a[d]=t[d];break}}return a}return h}function r(t,e){return t.hasOwnProperty(e)}function o(t,e){return null!=t[e]&&"auto"!==t[e]}function a(t,e,n){Xv(t,function(t){e[t]=n[t]})}!w(n)&&(n={});var s=n.ignoreSize;!y(s)&&(s=[s,s]);var l=i(Yv[0],0),h=i(Yv[1],1);a(Yv[0],t,l),a(Yv[1],t,h)}function Ur(t){return Xr({},t)}function Xr(t,e){return e&&t&&Xv(jv,function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t}function jr(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}function Yr(t){var e=t.get("coordinateSystem"),n={coordSysName:e,coordSysDims:[],axisMap:N(),categoryAxisMap:N()},i=iy[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}function qr(t){return"category"===t.get("type")}function $r(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===sy?{}:[]),this.sourceFormat=t.sourceFormat||ly,this.seriesLayoutBy=t.seriesLayoutBy||uy,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&N(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}function Kr(t){var e=t.option.source,n=ly;if(S(e))n=hy;else if(y(e))for(var i=0,r=e.length;i<r;i++){var o=e[i];if(null!=o){if(y(o)){n=oy;break}if(w(o)){n=ay;break}}}else if(w(e)){for(var a in e)if(e.hasOwnProperty(a)&&c(e[a])){n=sy;break}}else if(null!=e)throw new Error("Invalid data");dy(t).sourceFormat=n}function Qr(t){return dy(t).source}function Jr(t){dy(t).datasetMap=N()}function to(t){var e=t.option,n=e.data,i=S(n)?hy:ry,r=!1,o=e.seriesLayoutBy,a=e.sourceHeader,s=e.dimensions,l=ao(t);if(l){var h=l.option;n=h.source,i=dy(l).sourceFormat,r=!0,o=o||h.seriesLayoutBy,null==a&&(a=h.sourceHeader),s=s||h.dimensions}var u=eo(n,i,o,a,s),c=e.encode;!c&&l&&(c=oo(t,l,n,i,o,u)),dy(t).source=new $r({data:n,fromDataset:r,seriesLayoutBy:o,sourceFormat:i,dimensionsDefine:u.dimensionsDefine,startIndex:u.startIndex,dimensionsDetectCount:u.dimensionsDetectCount,encodeDefine:c})}function eo(t,e,n,i,r){if(!t)return{dimensionsDefine:no(r)};var o,a,s;if(e===oy)"auto"===i||null==i?io(function(t){null!=t&&"-"!==t&&(_(t)?null==a&&(a=1):a=0)},n,t,10):a=i?1:0,r||1!==a||(r=[],io(function(t,e){r[e]=null!=t?t:""},n,t)),o=r?r.length:n===cy?t.length:t[0]?t[0].length:null;else if(e===ay)r||(r=ro(t),s=!0);else if(e===sy)r||(r=[],s=!0,d(t,function(t,e){r.push(e)}));else if(e===ry){var l=wn(t[0]);o=y(l)&&l.length||1}var h;return s&&d(r,function(t,e){"name"===(w(t)?t.name:t)&&(h=e)}),{startIndex:a,dimensionsDefine:no(r),dimensionsDetectCount:o,potentialNameDimIndex:h}}function no(t){if(t){var e=N();return f(t,function(t,n){if(null==(t=o({},w(t)?t:{name:t})).name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var i=e.get(t.name);return i?t.name+="-"+i.count++:e.set(t.name,{count:1}),t})}}function io(t,e,n,i){if(null==i&&(i=1/0),e===cy)for(o=0;o<n.length&&o<i;o++)t(n[o]?n[o][0]:null,o);else for(var r=n[0]||[],o=0;o<r.length&&o<i;o++)t(r[o],o)}function ro(t){for(var e,n=0;n<t.length&&!(e=t[n++]););if(e){var i=[];return d(e,function(t,e){i.push(e)}),i}}function oo(t,e,n,i,r,o){var a=Yr(t),s={},l=[],h=[],u=t.subType,c=N(["pie","map","funnel"]),f=N(["line","bar","pictorialBar","scatter","effectScatter","candlestick","boxplot"]);if(a&&null!=f.get(u)){var p=t.ecModel,g=dy(p).datasetMap,m=e.uid+"_"+r,v=g.get(m)||g.set(m,{categoryWayDim:1,valueWayDim:0});d(a.coordSysDims,function(t){if(null==a.firstCategoryDimIndex){e=v.valueWayDim++;s[t]=e,h.push(e)}else if(a.categoryAxisMap.get(t))s[t]=0,l.push(0);else{var e=v.categoryWayDim++;s[t]=e,h.push(e)}})}else if(null!=c.get(u)){for(var y,x=0;x<5&&null==y;x++)lo(n,i,r,o.dimensionsDefine,o.startIndex,x)||(y=x);if(null!=y){s.value=y;var _=o.potentialNameDimIndex||Math.max(y-1,0);h.push(_),l.push(_)}}return l.length&&(s.itemName=l),h.length&&(s.seriesName=h),s}function ao(t){var e=t.option;if(!e.data)return t.ecModel.getComponent("dataset",e.datasetIndex||0)}function so(t,e){return lo(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function lo(t,e,n,i,r,o){function a(t){return(null==t||!isFinite(t)||""===t)&&(!(!_(t)||"-"===t)||void 0)}var s;if(S(t))return!1;var l;if(i&&(l=w(l=i[o])?l.name:l),e===oy)if(n===cy){for(var h=t[o],u=0;u<(h||[]).length&&u<5;u++)if(null!=(s=a(h[r+u])))return s}else for(u=0;u<t.length&&u<5;u++){var c=t[r+u];if(c&&null!=(s=a(c[o])))return s}else if(e===ay){if(!l)return;for(u=0;u<t.length&&u<5;u++)if((d=t[u])&&null!=(s=a(d[l])))return s}else if(e===sy){if(!l)return;if(!(h=t[l])||S(h))return!1;for(u=0;u<h.length&&u<5;u++)if(null!=(s=a(h[u])))return s}else if(e===ry)for(u=0;u<t.length&&u<5;u++){var d=t[u],f=wn(d);if(!y(f))return!1;if(null!=(s=a(f[o])))return s}return!1}function ho(t,e){if(e){var n=e.seiresIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}function uo(t,e){var r=t.color&&!t.colorLayer;d(e,function(e,o){"colorLayer"===o&&r||Qv.hasClass(o)||("object"==typeof e?t[o]=t[o]?i(t[o],e,!1):n(e):null==t[o]&&(t[o]=e))})}function co(t){t=t,this.option={},this.option[fy]=1,this._componentsMap=N({series:[]}),this._seriesIndices,this._seriesIndicesMap,uo(t,this._theme.option),i(t,ty,!1),this.mergeOption(t)}function fo(t,e){y(e)||(e=e?[e]:[]);var n={};return d(e,function(e){n[e]=(t.get(e)||[]).slice()}),n}function po(t,e,n){return e.type?e.type:n?n.subType:Qv.determineSubType(t,e)}function go(t,e){t._seriesIndicesMap=N(t._seriesIndices=f(e,function(t){return t.componentIndex})||[])}function mo(t,e){return e.hasOwnProperty("subType")?g(t,function(t){return t.subType===e.subType}):t}function vo(t){d(gy,function(e){this[e]=m(t[e],t)},this)}function yo(){this._coordinateSystems=[]}function xo(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function _o(t,e,n){var i,r,o=[],a=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},o=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;vy(l,function(t){t&&t.option&&(t.query?a.push(t):i||(i=t))})}return r||(r=t),r.timeline||(r.timeline=s),vy([r].concat(o).concat(f(a,function(t){return t.option})),function(t){vy(e,function(e){e(t,n)})}),{baseOption:r,timelineOptions:o,mediaDefault:i,mediaList:a}}function wo(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return d(t,function(t,e){var n=e.match(wy);if(n&&n[1]&&n[2]){var o=n[1],a=n[2].toLowerCase();bo(i[a],t,o)||(r=!1)}}),r}function bo(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e}function So(t,e){return t.join(",")===e.join(",")}function Mo(t,e){vy(e=e||{},function(e,n){if(null!=e){var i=t[n];if(Qv.hasClass(n)){e=xn(e);var r=Sn(i=xn(i),e);t[n]=xy(r,function(t){return t.option&&t.exist?_y(t.exist,t.option,!0):t.exist||t.option})}else t[n]=_y(i,e,!0)}})}function Io(t){var e=t&&t.itemStyle;if(e)for(var n=0,r=My.length;n<r;n++){var o=My[n],a=e.normal,s=e.emphasis;a&&a[o]&&(t[o]=t[o]||{},t[o].normal?i(t[o].normal,a[o]):t[o].normal=a[o],a[o]=null),s&&s[o]&&(t[o]=t[o]||{},t[o].emphasis?i(t[o].emphasis,s[o]):t[o].emphasis=s[o],s[o]=null)}}function To(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,a(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r)}}function Co(t){To(t,"itemStyle"),To(t,"lineStyle"),To(t,"areaStyle"),To(t,"label"),To(t,"labelLine"),To(t,"upperLabel"),To(t,"edgeLabel")}function Do(t,e){var n=Sy(t)&&t[e],i=Sy(n)&&n.textStyle;if(i)for(var r=0,o=Yg.length;r<o;r++){var e=Yg[r];i.hasOwnProperty(e)&&(n[e]=i[e])}}function Ao(t){t&&(Co(t),Do(t,"label"),t.emphasis&&Do(t.emphasis,"label"))}function ko(t){if(Sy(t)){Io(t),Co(t),Do(t,"label"),Do(t,"upperLabel"),Do(t,"edgeLabel"),t.emphasis&&(Do(t.emphasis,"label"),Do(t.emphasis,"upperLabel"),Do(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(Io(e),Ao(e));var n=t.markLine;n&&(Io(n),Ao(n));var i=t.markArea;i&&Ao(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!S(o))for(s=0;s<o.length;s++)Ao(o[s]);d(t.categories,function(t){Co(t)})}if(r&&!S(r))for(s=0;s<r.length;s++)Ao(r[s]);if((e=t.markPoint)&&e.data)for(var a=e.data,s=0;s<a.length;s++)Ao(a[s]);if((n=t.markLine)&&n.data)for(var l=n.data,s=0;s<l.length;s++)y(l[s])?(Ao(l[s][0]),Ao(l[s][1])):Ao(l[s]);"gauge"===t.type?(Do(t,"axisLabel"),Do(t,"title"),Do(t,"detail")):"treemap"===t.type&&(To(t.breadcrumb,"itemStyle"),d(t.levels,function(t){Co(t)}))}}function Po(t){return y(t)?t:t?[t]:[]}function Lo(t){return(y(t)?t[0]:t)||{}}function Oo(t,e){e=e.split(",");for(var n=t,i=0;i<e.length&&null!=(n=n&&n[e[i]]);i++);return n}function zo(t,e,n,i){e=e.split(",");for(var r,o=t,a=0;a<e.length-1;a++)null==o[r=e[a]]&&(o[r]={}),o=o[r];(i||null==o[e[a]])&&(o[e[a]]=n)}function Eo(t){d(Ty,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}function No(t){d(t,function(e,n){var i=[],r=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,l=a.map(o,function(o,l,h){var u=a.get(e.stackedDimension,h);if(isNaN(u))return r;var c,d;s?d=a.getRawIndex(h):c=a.get(e.stackedByDimension,h);for(var f=NaN,p=n-1;p>=0;p--){var g=t[p];if(s||(d=g.data.rawIndexOf(g.stackedByDimension,c)),d>=0){var m=g.data.getByRawIndex(g.stackResultDimension,d);if(u>=0&&m>0||u<=0&&m<0){u+=m,f=m;break}}}return i[0]=u,i[1]=f,i});a.hostModel.setData(l),e.data=l})}function Ro(t,e){$r.isInstance(t)||(t=$r.seriesDataToSource(t)),this._source=t;var n=this._data=t.data,i=t.sourceFormat;i===hy&&(this._offset=0,this._dimSize=e,this._data=n),o(this,ky[i===oy?i+"_"+t.seriesLayoutBy:i])}function Bo(){return this._data.length}function Vo(t){return this._data[t]}function Fo(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}function Ho(t,e,n,i){return null!=n?t[n]:t}function Go(t,e,n,i){return Wo(t[i],this._dimensionInfos[e])}function Wo(t,e){var n=e&&e.type;if("ordinal"===n){var i=e&&e.ordinalMeta;return i?i.parseAndCollect(t):t}return"time"===n&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+Ar(t)),null==t||""===t?NaN:+t}function Zo(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r,o,a=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(n);return s&&(r=s.name,o=s.index),Py[a](i,e,o,r)}}}function Uo(t,e,n){if(t){var i=t.getProvider().getSource().sourceFormat;if(i===ry||i===ay){var r=t.getRawDataItem(e);return i!==ry||w(r)||(r=null),r?r[n]:void 0}}}function Xo(t){return new jo(t)}function jo(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}function Yo(t,e){t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null;var n,i;!e&&t._reset&&(n=t._reset(t.context))&&n.progress&&(i=n.forceFirstProgress,n=n.progress),t._progress=n;var r=t._downstream;return r&&r.dirty(),i}function qo(t){var e=t.name;In(t)||(t.name=$o(t)||e)}function $o(t){var e=t.getRawData(),n=[];return d(e.mapDimension("seriesName",!0),function(t){var i=e.getDimensionInfo(t);i.displayName&&n.push(i.displayName)}),n.join(" ")}function Ko(t){return t.model.getRawData().count()}function Qo(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),Jo}function Jo(t,e){t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function ta(t,e){d(t.CHANGABLE_METHODS,function(n){t.wrapMethod(n,v(ea,e))})}function ea(t){var e=na(t);e&&e.setOutputEnd(this.count())}function na(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}function ia(){this.group=new Jp,this.uid=vr("viewChart"),this.renderTask=Xo({plan:aa,reset:sa}),this.renderTask.context={view:this}}function ra(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var n=0;n<t.childCount();n++)ra(t.childAt(n),e)}function oa(t,e,n){var i=Cn(t,e);null!=i?d(xn(i),function(e){ra(t.getItemGraphicEl(e),n)}):t.eachItemGraphicEl(function(t){ra(t,n)})}function aa(t){return Gy(t.model)}function sa(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.canProgressiveRender,a=t.view,s=r&&Hy(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),Zy[l]}function la(t,e,n){function i(){u=(new Date).getTime(),c=null,t.apply(a,s||[])}var r,o,a,s,l,h=0,u=0,c=null;e=e||0;var d=function(){r=(new Date).getTime(),a=this,s=arguments;var t=l||e,d=l||n;l=null,o=r-(d?h:u)-t,clearTimeout(c),d?c=setTimeout(i,t):o>=0?i():c=setTimeout(i,-o),h=r};return d.clear=function(){c&&(clearTimeout(c),c=null)},d.debounceNextCall=function(t){l=t},d}function ha(t,e,n,i){var r=t[e];if(r){var o=r[Uy]||r,a=r[jy];if(r[Xy]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=la(o,n,"debounce"===i))[Uy]=o,r[jy]=i,r[Xy]=n}return r}}function ua(t,e){var n=t[e];n&&n[Uy]&&(t[e]=n[Uy])}function ca(t,e,n,i){this.ecInstance=t,this.api=e,this.unfinished,this._dataProcessorHandlers=n.slice(),this._visualHandlers=i.slice(),this._stageTaskMap=N()}function da(t,e,n,i,r){function o(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}r=r||{};var a;d(e,function(e,s){if(!r.visualType||r.visualType===e.visualType){var l=t._stageTaskMap.get(e.uid),h=l.seriesTaskMap,u=l.overallTask;if(u){var c,d=u.agentStubMap;d.each(function(t){o(r,t)&&(t.dirty(),c=!0)}),c&&u.dirty(),Jy(u,i);var f=t.getPerformArgs(u,r.block);d.each(function(t){t.perform(f)}),a|=u.perform(f)}else h&&h.each(function(s,l){o(r,s)&&s.dirty();var h=t.getPerformArgs(s,r.block);h.skip=!e.performRawSeries&&n.isSeriesFiltered(s.context.model),Jy(s,i),a|=s.perform(h)})}}),t.unfinished|=a}function fa(t,e,n,i,r){function o(n){var o=n.uid,s=a.get(o)||a.set(o,Xo({plan:xa,reset:_a,count:ba}));s.context={model:n,ecModel:i,api:r,useClearVisual:e.isVisual&&!e.isLayout,plan:e.plan,reset:e.reset,scheduler:t},Sa(t,n,s)}var a=n.seriesTaskMap||(n.seriesTaskMap=N()),s=e.seriesType,l=e.getTargetSeries;e.createOnAllSeries?i.eachRawSeries(o):s?i.eachRawSeriesByType(s,o):l&&l(i,r).each(o);var h=t._pipelineMap;a.each(function(t,e){h.get(e)||(t.dispose(),a.removeKey(e))})}function pa(t,e,n,i,r){function o(e){var n=e.uid,i=s.get(n)||s.set(n,Xo({reset:ma,onDirty:ya}));i.context={model:e,overallProgress:u,isOverallFilter:c},i.agent=a,i.__block=u,Sa(t,e,i)}var a=n.overallTask=n.overallTask||Xo({reset:ga});a.context={ecModel:i,api:r,overallReset:e.overallReset,scheduler:t};var s=a.agentStubMap=a.agentStubMap||N(),l=e.seriesType,h=e.getTargetSeries,u=!0,c=e.isOverallFilter;l?i.eachRawSeriesByType(l,o):h?h(i,r).each(o):(u=!1,d(i.getSeries(),o));var f=t._pipelineMap;s.each(function(t,e){f.get(e)||(t.dispose(),s.removeKey(e))})}function ga(t){t.overallReset(t.ecModel,t.api,t.payload)}function ma(t,e){return t.overallProgress&&va}function va(){this.agent.dirty(),this.getDownstream().dirty()}function ya(){this.agent&&this.agent.dirty()}function xa(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function _a(t){if(t.useClearVisual&&t.data.clearAllVisual(),(t.resetDefines=xn(t.reset(t.model,t.ecModel,t.api,t.payload))).length)return wa}function wa(t,e){for(var n=e.data,i=e.resetDefines,r=0;r<i.length;r++){var o=i[r];if(o&&o.dataEach)for(var a=t.start;a<t.end;a++)o.dataEach(n,a);else o&&o.progress&&o.progress(t,n)}}function ba(t){return t.data.count()}function Sa(t,e,n){var i=e.uid,r=t._pipelineMap.get(i);!r.head&&(r.head=n),r.tail&&r.tail.pipe(n),r.tail=n,n.__idxInPipeline=r.count++,n.__pipeline=r}function Ma(t){tx=null;try{t(ex,nx)}catch(t){}return tx}function Ia(t,e){for(var n in e.prototype)t[n]=R}function Ta(t){return function(e,n,i){e=e&&e.toLowerCase(),xp.prototype[t].call(this,e,n,i)}}function Ca(){xp.call(this)}function Da(t,e,i){function r(t,e){return t.__prio-e.__prio}i=i||{},"string"==typeof e&&(e=kx[e]),this.id,this.group,this._dom=t;var o=this._zr=mn(t,{renderer:i.renderer||"canvas",devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height});this._throttledZrFlush=la(m(o.flush,o),17),(e=n(e))&&Dy(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new yo;var a=this._api=Xa(this);te(Ax,r),te(Tx,r),this._scheduler=new ca(this,a,Tx,Ax),xp.call(this),this._messageCenter=new Ca,this._initEvents(),this.resize=m(this.resize,this),this._pendingActions=[],o.animation.on("frame",this._onframe,this),Na(o,this),O(this)}function Aa(t,e,n){var i,r=this._model,o=this._coordSysMgr.getCoordinateSystems();e=An(r,e);for(var a=0;a<o.length;a++){var s=o[a];if(s[t]&&null!=(i=s[t](r,e,n)))return i}}function ka(t){var e=t._model,n=t._scheduler;n.restorePipelines(e),n.prepareStageTasks(),Ra(t,"component",e,n),Ra(t,"chart",e,n),n.plan()}function Pa(t,e,n,i,r){function o(i){i&&i.__alive&&i[e]&&i[e](i.__model,a,t._api,n)}var a=t._model;if(i){var s={};s[i+"Id"]=n[i+"Id"],s[i+"Index"]=n[i+"Index"],s[i+"Name"]=n[i+"Name"];var l={mainType:i,query:s};r&&(l.subType=r),a&&a.eachComponent(l,function(e,n){o(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])},t)}else lx(t._componentsViews.concat(t._chartsViews),o)}function La(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})}function Oa(t,e){var n=t.type,i=t.escapeConnect,r=Mx[n],s=r.actionInfo,l=(s.update||"update").split(":"),h=l.pop();l=null!=l[0]&&cx(l[0]),this[vx]=!0;var u=[t],c=!1;t.batch&&(c=!0,u=f(t.batch,function(e){return e=a(o({},e),t),e.batch=null,e}));var d,p=[],g="highlight"===n||"downplay"===n;lx(u,function(t){d=r.action(t,this._model,this._api),(d=d||o({},t)).type=s.event||d.type,p.push(d),g?Pa(this,h,t,"series"):l&&Pa(this,h,t,l.main,l.sub)},this),"none"===h||g||l||(this[yx]?(ka(this),bx.update.call(this,t),this[yx]=!1):bx[h].call(this,t)),d=c?{type:s.event||n,escapeConnect:i,batch:p}:p[0],this[vx]=!1,!e&&this._messageCenter.trigger(d.type,d)}function za(t){for(var e=this._pendingActions;e.length;){var n=e.shift();Oa.call(this,n,t)}}function Ea(t){!t&&this.trigger("updated")}function Na(t,e){t.on("rendered",function(){e.trigger("rendered"),!t.animation.isFinished()||e[yx]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")})}function Ra(t,e,n,i){function r(t){var e="_ec_"+t.id+"_"+t.type,r=s[e];if(!r){var u=cx(t.type);(r=new(o?By.getClass(u.main,u.sub):ia.getClass(u.sub))).init(n,h),s[e]=r,a.push(r),l.add(r.group)}t.__viewId=r.__id=e,r.__alive=!0,r.__model=t,r.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!o&&i.prepareView(r,t,n,h)}for(var o="component"===e,a=o?t._componentsViews:t._chartsViews,s=o?t._componentsMap:t._chartsMap,l=t._zr,h=t._api,u=0;u<a.length;u++)a[u].__alive=!1;o?n.eachComponent(function(t,e){"series"!==t&&r(e)}):n.eachSeries(r);for(u=0;u<a.length;){var c=a[u];c.__alive?u++:(!o&&c.renderTask.dispose(),l.remove(c.group),c.dispose(n,h),a.splice(u,1),delete s[c.__id],c.__id=c.group.__ecComponentInfo=null)}}function Ba(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function Va(t,e,n,i){Fa(t,e,n,i),lx(t._chartsViews,function(t){t.__alive=!1}),Ha(t,e,n,i),lx(t._chartsViews,function(t){t.__alive||t.remove(e,n)})}function Fa(t,e,n,i,r){lx(r||t._componentsViews,function(t){var r=t.__model;t.render(r,e,n,i),Ua(r,t)})}function Ha(t,e,n,i,r){var o,a=t._scheduler;e.eachSeries(function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var s=n.renderTask;a.updatePayload(s,i),r&&r.get(e.uid)&&s.dirty(),o|=s.perform(a.getPerformArgs(s)),n.group.silent=!!e.get("silent"),Ua(e,n),Za(e,n)}),a.unfinished|=o,Wa(t._zr,e),$y(t._zr.dom,e)}function Ga(t,e){lx(Dx,function(n){n(t,e)})}function Wa(t,e){var n=t.storage,i=0;n.traverse(function(t){t.isGroup||i++}),i>e.get("hoverLayerThreshold")&&!Kf.node&&n.traverse(function(t){t.isGroup||(t.useHoverLayer=!0)})}function Za(t,e){var n=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.style.blend!==n&&t.setStyle("blend",n),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",n)})})}function Ua(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))})}function Xa(t){var e=t._coordSysMgr;return o(new vo(t),{getCoordinateSystems:m(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}}})}function ja(t){function e(t,e){for(var i=0;i<t.length;i++)t[i][n]=e}var n="__connectUpdateStatus";lx(Ix,function(i,r){t._messageCenter.on(r,function(i){if(Ox[t.group]&&0!==t[n]){if(i&&i.escapeConnect)return;var r=t.makeActionFromEvent(i),o=[];lx(Lx,function(e){e!==t&&e.group===t.group&&o.push(e)}),e(o,0),lx(o,function(t){1!==t[n]&&t.dispatchAction(r)}),e(o,2)}})})}function Ya(t){Ox[t]=!1}function qa(t){return Lx[Ln(t,Nx)]}function $a(t,e){kx[t]=e}function Ka(t){Cx.push(t)}function Qa(t,e){ns(Tx,t,e,fx)}function Ja(t,e,n){"function"==typeof e&&(n=e,e="");var i=ux(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,sx(xx.test(i)&&xx.test(e)),Mx[i]||(Mx[i]={action:n,actionInfo:t}),Ix[e]=i}function ts(t,e){ns(Ax,t,e,px,"layout")}function es(t,e){ns(Ax,t,e,gx,"visual")}function ns(t,e,n,i,r){(hx(e)||ux(e))&&(n=e,e=i);var o=ca.wrapStageHandler(n,r);return o.__prio=e,o.__raw=n,t.push(o),o}function is(t,e){Px[t]=e}function rs(t){return Qv.extend(t)}function os(t){return By.extend(t)}function as(t){return Ry.extend(t)}function ss(t){return ia.extend(t)}function ls(t){return t}function hs(t,e,n,i,r){this._old=t,this._new=e,this._oldKeyGetter=n||ls,this._newKeyGetter=i||ls,this.context=r}function us(t,e,n,i,r){for(var o=0;o<t.length;o++){var a="_ec_"+r[i](t[o],o),s=e[a];null==s?(n.push(a),e[a]=o):(s.length||(e[a]=s=[s]),s.push(o))}}function cs(t){var e={},n=e.encode={},i=N(),r=[];d(t.dimensions,function(e){var o=t.getDimensionInfo(e),a=o.coordDim;if(a){var s=n[a];n.hasOwnProperty(a)||(s=n[a]=[]),s[o.coordDimIndex]=e,o.isExtraCoord||(i.set(a,1),fs(o.type)&&(r[0]=e))}Fx.each(function(t,e){var i=n[e];n.hasOwnProperty(e)||(i=n[e]=[]);var r=o.otherDims[e];null!=r&&!1!==r&&(i[r]=o.name)})});var o=[],a={};i.each(function(t,e){var i=n[e];a[e]=i[0],o=o.concat(i)}),e.dataDimsOnCoord=o,e.encodeFirstDimNotExtra=a;var s=n.label;s&&s.length&&(r=s.slice());var l=r.slice(),h=n.tooltip;return h&&h.length&&(l=h.slice()),n.defaultedLabel=r,n.defaultedTooltip=l,e}function ds(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function fs(t){return!("ordinal"===t||"time"===t)}function ps(t){return t._rawCount>65535?Ux:Xx}function gs(t){var e=t.constructor;return e===Array?t.slice():new e(t)}function ms(t,e){d(jx.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods}function vs(t){var e=t._invertedIndicesMap;d(e,function(n,i){var r=t._dimensionInfos[i].ordinalMeta;if(r){n=e[i]=new Ux(r.categories.length);for(o=0;o<n.length;o++)n[o]=NaN;for(var o=0;o<t._count;o++)n[t.get(i,o)]=o}})}function ys(t){return t}function xs(t){return t<this._count&&t>=0?this._indices[t]:-1}function _s(t,e){var n=t._idList[e];return null==n&&(n=t._getIdFromStore(e)),null==n&&(n=Wx+e),n}function ws(t){return y(t)||(t=[t]),t}function bs(t,e){var n=t.dimensions,i=new Yx(f(n,t.getDimensionInfo,t),t.hostModel);ms(i,t);for(var r=i._storage={},a=t._storage,s=o({},t._rawExtent),h=0;h<n.length;h++){var u=n[h];a[u]&&(l(e,u)>=0?(r[u]=Ss(a[u]),s[u]=Ms()):r[u]=a[u])}return i}function Ss(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=gs(t[n]);return e}function Ms(){return[1/0,-1/0]}function Is(t,e,i){function r(t,e,n){null!=Fx.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,u.set(e,!0))}$r.isInstance(e)||(e=$r.seriesDataToSource(e)),i=i||{},t=(t||[]).slice();for(var s=(i.dimsDef||[]).slice(),l=N(i.encodeDef),h=N(),u=N(),c=[],f=Ts(e,t,s,i.dimCount),p=0;p<f;p++){var g=s[p]=o({},w(s[p])?s[p]:{name:s[p]}),m=g.name,v=c[p]={otherDims:{}};null!=m&&null==h.get(m)&&(v.name=v.displayName=m,h.set(m,p)),null!=g.type&&(v.type=g.type),null!=g.displayName&&(v.displayName=g.displayName)}l.each(function(t,e){t=xn(t).slice();var n=l.set(e,[]);d(t,function(t,i){_(t)&&(t=h.get(t)),null!=t&&t<f&&(n[i]=t,r(c[t],e,i))})});var y=0;d(t,function(t,e){var i,t,o,s;if(_(t))i=t,t={};else{i=t.name;var h=t.ordinalMeta;t.ordinalMeta=null,(t=n(t)).ordinalMeta=h,o=t.dimsDef,s=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null}var u=xn(l.get(i));if(!u.length)for(var f=0;f<(o&&o.length||1);f++){for(;y<c.length&&null!=c[y].coordDim;)y++;y<c.length&&u.push(y++)}d(u,function(e,n){var l=c[e];r(a(l,t),i,n),null==l.name&&o&&(l.name=l.displayName=o[n]),s&&a(l.otherDims,s)})});var x=i.generateCoord,b=i.generateCoordCount,S=null!=b;b=x?b||1:0;for(var M=x||"value",I=0;I<f;I++)null==(v=c[I]=c[I]||{}).coordDim&&(v.coordDim=Cs(M,u,S),v.coordDimIndex=0,(!x||b<=0)&&(v.isExtraCoord=!0),b--),null==v.name&&(v.name=Cs(v.coordDim,h)),null==v.type&&so(e,I,v.name)&&(v.type="ordinal");return c}function Ts(t,e,n,i){var r=Math.max(t.dimensionsDetectCount||1,e.length,n.length,i||0);return d(e,function(t){var e=t.dimsDef;e&&(r=Math.max(r,e.length))}),r}function Cs(t,e,n){if(n||null!=e.get(t)){for(var i=0;null!=e.get(t+i);)i++;t+=i}return e.set(t,!0),t}function Ds(t,e,n){var i,r,o,a,s=(n=n||{}).byIndex,l=n.stackedCoordDimension,h=!(!t||!t.get("stack"));if(d(e,function(t,n){_(t)&&(e[n]=t={name:t}),h&&!t.isExtraCoord&&(s||i||!t.ordinalMeta||(i=t),r||"ordinal"===t.type||"time"===t.type||l&&l!==t.coordDim||(r=t))}),r&&(s||i)){o="__\0ecstackresult",a="__\0ecstackedover",i&&(i.createInvertedIndices=!0);var u=r.coordDim,c=r.type,f=0;d(e,function(t){t.coordDim===u&&f++}),e.push({name:o,coordDim:u,coordDimIndex:f,type:c,isExtraCoord:!0,isCalculationCoord:!0}),f++,e.push({name:a,coordDim:a,coordDimIndex:f,type:c,isExtraCoord:!0,isCalculationCoord:!0})}return{stackedDimension:r&&r.name,stackedByDimension:i&&i.name,isStackedByIndex:s,stackedOverDimension:a,stackResultDimension:o}}function As(t,e,n){return e&&e===t.getCalculationInfo("stackedDimension")&&(null!=n?n===t.getCalculationInfo("stackedByDimension"):t.getCalculationInfo("isStackedByIndex"))}function ks(t,e,n){n=n||{},$r.isInstance(t)||(t=$r.seriesDataToSource(t));var i,r=e.get("coordinateSystem"),o=yo.get(r),a=Yr(e);a&&(i=f(a.coordSysDims,function(t){var e={name:t},n=a.axisMap.get(t);if(n){var i=n.get("type");e.type=ds(i)}return e})),i||(i=o&&(o.getDimensionsInfo?o.getDimensionsInfo():o.dimensions.slice())||["x","y"]);var s,l,h=Kx(t,{coordDimensions:i,generateCoord:n.generateCoord});a&&d(h,function(t,e){var n=t.coordDim,i=a.categoryAxisMap.get(n);i&&(null==s&&(s=e),t.ordinalMeta=i.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(h[s].otherDims.itemName=0);var u=Ds(e,h),c=new Yx(h,e);c.setCalculationInfo(u);var p=null!=s&&Ps(t)?function(t,e,n,i){return i===s?n:this.defaultDimValueGetter(t,e,n,i)}:null;return c.hasItemOption=!1,c.initData(t,null,p),c}function Ps(t){if(t.sourceFormat===ry){var e=Ls(t.data||[]);return null!=e&&!y(wn(e))}}function Ls(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function Os(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function zs(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}function Es(t){return t._map||(t._map=N(t.categories))}function Ns(t){return w(t)&&null!=t.value?t.value:t+""}function Rs(t,e,n,i){var r={},o=t[1]-t[0],a=r.interval=Lr(o/e,!0);null!=n&&a<n&&(a=r.interval=n),null!=i&&a>i&&(a=r.interval=i);var s=r.intervalPrecision=Bs(a);return Fs(r.niceTickExtent=[e_(Math.ceil(t[0]/a)*a,s),e_(Math.floor(t[1]/a)*a,s)],t),r}function Bs(t){return Mr(t)+2}function Vs(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Fs(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),Vs(t,0,e),Vs(t,1,e),t[0]>t[1]&&(t[0]=t[1])}function Hs(t,e,n,i){var r=[];if(!t)return r;e[0]<n[0]&&r.push(e[0]);for(var o=n[0];o<=n[1]&&(r.push(o),(o=e_(o+t,i))!==r[r.length-1]);)if(r.length>1e4)return[];return e[1]>(r.length?r[r.length-1]:n[1])&&r.push(e[1]),r}function Gs(t){return t.get("stack")||r_+t.seriesIndex}function Ws(t){return t.dim+t.index}function Zs(t,e){return Us(f(t,function(t){var e=t.getData(),n=t.coordinateSystem.getBaseAxis(),i=n.getExtent(),r="category"===n.type?n.getBandWidth():Math.abs(i[1]-i[0])/e.count();return{bandWidth:r,barWidth:_r(t.get("barWidth"),r),barMaxWidth:_r(t.get("barMaxWidth"),r),barGap:t.get("barGap"),barCategoryGap:t.get("barCategoryGap"),axisKey:Ws(n),stackId:Gs(t)}}),e)}function Us(t,e){var n={};d(t,function(t,e){var i=t.axisKey,r=t.bandWidth,o=n[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},a=o.stacks;n[i]=o;var s=t.stackId;a[s]||o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!a[s].width&&(a[s].width=l,l=Math.min(o.remainedWidth,l),o.remainedWidth-=l);var h=t.barMaxWidth;h&&(a[s].maxWidth=h);var u=t.barGap;null!=u&&(o.gap=u);var c=t.barCategoryGap;null!=c&&(o.categoryGap=c)});var i={};return d(n,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,o=_r(t.categoryGap,r),a=_r(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,h=(s-o)/(l+(l-1)*a);h=Math.max(h,0),d(n,function(t,e){var n=t.maxWidth;n&&n<h&&(n=Math.min(n,s),t.width&&(n=Math.min(n,t.width)),s-=n,t.width=n,l--)}),h=(s-o)/(l+(l-1)*a),h=Math.max(h,0);var u,c=0;d(n,function(t,e){t.width||(t.width=h),u=t,c+=t.width*(1+a)}),u&&(c-=u.width*a);var f=-c/2;d(n,function(t,n){i[e][n]=i[e][n]||{offset:f,width:t.width},f+=t.width*(1+a)})}),i}function Xs(t,e){return p_(t,f_(e))}function js(t,e){var n,i,r,o=t.type,a=e.getMin(),s=e.getMax(),l=null!=a,h=null!=s,u=t.getExtent();"ordinal"===o?n=e.getCategories().length:(y(i=e.get("boundaryGap"))||(i=[i||0,i||0]),"boolean"==typeof i[0]&&(i=[0,0]),i[0]=_r(i[0],1),i[1]=_r(i[1],1),r=u[1]-u[0]||Math.abs(u[0])),null==a&&(a="ordinal"===o?n?0:NaN:u[0]-i[0]*r),null==s&&(s="ordinal"===o?n?n-1:NaN:u[1]+i[1]*r),"dataMin"===a?a=u[0]:"function"==typeof a&&(a=a({min:u[0],max:u[1]})),"dataMax"===s?s=u[1]:"function"==typeof s&&(s=s({min:u[0],max:u[1]})),(null==a||!isFinite(a))&&(a=NaN),(null==s||!isFinite(s))&&(s=NaN),t.setBlank(I(a)||I(s)),e.getNeedCrossZero()&&(a>0&&s>0&&!l&&(a=0),a<0&&s<0&&!h&&(s=0));var c=e.ecModel;if(c&&"time"===o){var d,f=[];if(c.eachSeriesByType("bar",function(t){t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type&&(f.push(t),d|=t.getBaseAxis()===e.axis)}),d){var p=Ys(a,s,e,f);a=p.min,s=p.max}}return[a,s]}function Ys(t,e,n,i){var r=n.axis.getExtent(),o=r[1]-r[0],a=Zs(i)[n.axis.dim+n.axis.index];if(void 0===a)return{min:t,max:e};var s=1/0;d(a,function(t){s=Math.min(t.offset,s)});var l=-1/0;d(a,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var h=s+l,u=e-t,c=u/(1-(s+l)/o)-u;return e+=c*(l/h),t-=c*(s/h),{min:t,max:e}}function qs(t,e){var n=js(t,e),i=null!=e.getMin(),r=null!=e.getMax(),o=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var a=t.type;t.setExtent(n[0],n[1]),t.niceExtent({splitNumber:o,fixMin:i,fixMax:r,minInterval:"interval"===a||"time"===a?e.get("minInterval"):null,maxInterval:"interval"===a||"time"===a?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)}function $s(t,e){if(e=e||t.get("type"))switch(e){case"category":return new t_(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new i_;default:return(Os.getClass(e)||i_).create(t)}}function Ks(t,e,n,i,r){var o,a=0,s=0,l=(i-r)/180*Math.PI,h=1;e.length>40&&(h=Math.floor(e.length/40));for(var u=0;u<t.length;u+=h){var c=t[u],d=ce(e[u],n,"center","top");d.x+=c*Math.cos(l),d.y+=c*Math.sin(l),d.width*=1.3,d.height*=1.3,o?o.intersect(d)?(s++,a=Math.max(a,s)):(o.union(d),s=0):o=d.clone()}return 0===a&&h>1?h:(a+1)*h-1}function Qs(t,e){var n=t.scale,i=n.getTicksLabels(),r=n.getTicks();return"string"==typeof e?(e=function(t){return function(e){return t.replace("{value}",null!=e?e:"")}}(e),f(i,e)):"function"==typeof e?f(r,function(n,i){return e(Js(t,n),i)},this):i}function Js(t,e){return"category"===t.type?t.scale.getLabel(e):e}function tl(t,e){if("image"!==this.type){var n=this.style,i=this.shape;i&&"line"===i.symbolType?n.stroke=t:this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff"):(n.fill&&(n.fill=t),n.stroke&&(n.stroke=t)),this.dirty(!1)}}function el(t,e,n,i,r,o,a){var s=0===t.indexOf("empty");s&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var l;return l=0===t.indexOf("image://")?Pi(t.slice(8),new Xt(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?ki(t.slice(7),{},new Xt(e,n,i,r),a?"center":"cover"):new C_({shape:{symbolType:t,x:e,y:n,width:i,height:r}}),l.__isEmptyBrush=s,l.setColor=tl,l.setColor(o),l}function nl(t,e){return Math.abs(t-e)<k_}function il(t,e,n){var i=0,r=t[0];if(!r)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=ui(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return nl(r[0],s[0])&&nl(r[1],s[1])||(i+=ui(r[0],r[1],s[0],s[1],e,n)),0!==i}function rl(t,e,n){if(this.name=t,this.geometries=e,n)n=[n[0],n[1]];else{var i=this.getBoundingRect();n=[i.x+i.width/2,i.y+i.height/2]}this.center=n}function ol(t){if(!t.UTF8Encoding)return t;var e=t.UTF8Scale;null==e&&(e=1024);for(var n=t.features,i=0;i<n.length;i++)for(var r=n[i].geometry,o=r.coordinates,a=r.encodeOffsets,s=0;s<o.length;s++){var l=o[s];if("Polygon"===r.type)o[s]=al(l,a[s],e);else if("MultiPolygon"===r.type)for(var h=0;h<l.length;h++){var u=l[h];l[h]=al(u,a[s][h],e)}}return t.UTF8Encoding=!1,t}function al(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),r=s+=r,o=l+=o,i.push([s/n,l/n])}return i}function sl(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}function ll(t,e){var n=t.mapDimension("defaultedLabel",!0),i=n.length;if(1===i)return Zo(t,e,n[0]);if(i){for(var r=[],o=0;o<n.length;o++){var a=Zo(t,e,n[o]);r.push(a)}return r.join(" ")}}function hl(t,e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array?n.slice():[+n,+n]}function ul(t){return[t[0]/2,t[1]/2]}function cl(t,e,n){Jp.call(this),this.updateData(t,e,n)}function dl(t,e){this.parent.drift(t,e)}function fl(t){this.group=new Jp,this._symbolCtor=t||cl}function pl(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(i.isIgnore&&i.isIgnore(n))&&!(i.clipShape&&!i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function gl(t){return null==t||w(t)||(t={isIgnore:t}),t||{}}function ml(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}function vl(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),o=yl(r,n),a=i.dim,s=r.dim,l=e.mapDimension(s),h=e.mapDimension(a),u="x"===s||"radius"===s?1:0,c=As(e,l,h);return{dataDimsForPoint:f(t.dimensions,function(t){return e.mapDimension(t)}),valueStart:o,valueAxisDim:s,baseAxisDim:a,stacked:c,valueDim:l,baseDim:h,baseDataOffset:u,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function yl(t,e){var n=0,i=t.scale.getExtent();return"start"===e?n=i[0]:"end"===e?n=i[1]:i[0]>0?n=i[0]:i[1]<0&&(n=i[1]),n}function xl(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}function _l(t,e){var n=[];return e.diff(t).add(function(t){n.push({cmd:"+",idx:t})}).update(function(t,e){n.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){n.push({cmd:"-",idx:t})}).execute(),n}function wl(t){return isNaN(t[0])||isNaN(t[1])}function bl(t,e,n,i,r,o,a,s,l,h,u){return null==h?Sl(e,"x")?Ml(t,e,n,i,r,o,a,s,l,"x",u):Sl(e,"y")?Ml(t,e,n,i,r,o,a,s,l,"y",u):Il.apply(this,arguments):"none"!==h&&Sl(e,h)?Ml.apply(this,arguments):Il.apply(this,arguments)}function Sl(t,e){if(t.length<=1)return!0;for(var n="x"===e?0:1,i=t[0][n],r=0,o=1;o<t.length;++o){var a=t[o][n]-i;if(!isNaN(a)&&!isNaN(r)&&0!==a&&0!==r&&a>=0!=r>=0)return!1;isNaN(a)||0===a||(r=a,i=t[o][n])}return!0}function Ml(t,e,n,i,r,o,a,s,l,h,u){for(var c=0,d=n,f=0;f<i;f++){var p=e[d];if(d>=r||d<0)break;if(wl(p)){if(u){d+=o;continue}break}if(d===n)t[o>0?"moveTo":"lineTo"](p[0],p[1]);else if(l>0){var g=e[c],m="y"===h?1:0,v=(p[m]-g[m])*l;j_(q_,g),q_[m]=g[m]+v,j_($_,p),$_[m]=p[m]-v,t.bezierCurveTo(q_[0],q_[1],$_[0],$_[1],p[0],p[1])}else t.lineTo(p[0],p[1]);c=d,d+=o}return f}function Il(t,e,n,i,r,o,a,s,l,h,u){for(var c=0,d=n,f=0;f<i;f++){var p=e[d];if(d>=r||d<0)break;if(wl(p)){if(u){d+=o;continue}break}if(d===n)t[o>0?"moveTo":"lineTo"](p[0],p[1]),j_(q_,p);else if(l>0){var g=d+o,m=e[g];if(u)for(;m&&wl(e[g]);)m=e[g+=o];var v=.5,y=e[c];if(!(m=e[g])||wl(m))j_($_,p);else{wl(m)&&!u&&(m=p),W(Y_,m,y);var x,_;if("x"===h||"y"===h){var w="x"===h?0:1;x=Math.abs(p[w]-y[w]),_=Math.abs(p[w]-m[w])}else x=gp(p,y),_=gp(p,m);X_($_,p,Y_,-l*(1-(v=_/(_+x))))}Z_(q_,q_,s),U_(q_,q_,a),Z_($_,$_,s),U_($_,$_,a),t.bezierCurveTo(q_[0],q_[1],$_[0],$_[1],p[0],p[1]),X_(q_,p,Y_,l*v)}else t.lineTo(p[0],p[1]);c=d,d+=o}return f}function Tl(t,e){var n=[1/0,1/0],i=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var o=t[r];o[0]<n[0]&&(n[0]=o[0]),o[1]<n[1]&&(n[1]=o[1]),o[0]>i[0]&&(i[0]=o[0]),o[1]>i[1]&&(i[1]=o[1])}return{min:e?n:i,max:e?i:n}}function Cl(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],r=e[n];if(i[0]!==r[0]||i[1]!==r[1])return}return!0}}function Dl(t){return"number"==typeof t?t:t?.5:0}function Al(t){var e=t.getGlobalExtent();if(t.onBand){var n=t.getBandWidth()/2-1,i=e[1]>e[0]?1:-1;e[0]+=i*n,e[1]-=i*n}return e}function kl(t,e,n){if(!n.valueDim)return[];for(var i=[],r=0,o=e.count();r<o;r++)i.push(xl(n,t,e,r));return i}function Pl(t,e,n){var i=Al(t.getAxis("x")),r=Al(t.getAxis("y")),o=t.getBaseAxis().isHorizontal(),a=Math.min(i[0],i[1]),s=Math.min(r[0],r[1]),l=Math.max(i[0],i[1])-a,h=Math.max(r[0],r[1])-s,u=n.get("lineStyle.width")||2,c=n.get("clipOverflow")?u/2:Math.max(l,h);o?(s-=c,h+=2*c):(a-=c,l+=2*c);var d=new gv({shape:{x:a,y:s,width:l,height:h}});return e&&(d.shape[o?"width":"height"]=0,sr(d,{shape:{width:l,height:h}},n)),d}function Ll(t,e,n){var i=t.getAngleAxis(),r=t.getRadiusAxis().getExtent(),o=i.getExtent(),a=Math.PI/180,s=new hv({shape:{cx:t.cx,cy:t.cy,r0:r[0],r:r[1],startAngle:-o[0]*a,endAngle:-o[1]*a,clockwise:i.inverse}});return e&&(s.shape.endAngle=-o[0]*a,sr(s,{shape:{endAngle:-o[1]*a}},n)),s}function Ol(t,e,n){return"polar"===t.type?Ll(t,e,n):Pl(t,e,n)}function zl(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,o=[],a=0;a<t.length-1;a++){var s=t[a+1],l=t[a];o.push(l);var h=[];switch(n){case"end":h[r]=s[r],h[1-r]=l[1-r],o.push(h);break;case"middle":var u=(l[r]+s[r])/2,c=[];h[r]=c[r]=u,h[1-r]=l[1-r],c[1-r]=s[1-r],o.push(h),o.push(c);break;default:h[r]=l[r],h[1-r]=s[1-r],o.push(h)}}return t[a]&&o.push(t[a]),o}function El(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count()&&"cartesian2d"===e.type){for(var i,r,o=n.length-1;o>=0;o--){var a=n[o].dimension,s=t.dimensions[a],l=t.getDimensionInfo(s);if("x"===(i=l&&l.coordDim)||"y"===i){r=n[o];break}}if(r){var h=e.getAxis(i),u=f(r.stops,function(t){return{coord:h.toGlobalCoord(h.dataToCoord(t.value)),color:t.color}}),c=u.length,p=r.outerColors.slice();c&&u[0].coord>u[c-1].coord&&(u.reverse(),p.reverse());var g=u[0].coord-10,m=u[c-1].coord+10,v=m-g;if(v<.001)return"transparent";d(u,function(t){t.offset=(t.coord-g)/v}),u.push({offset:c?u[c-1].offset:.5,color:p[1]||"transparent"}),u.unshift({offset:c?u[0].offset:.5,color:p[0]||"transparent"});var y=new bv(0,0,0,0,u,!0);return y[i]=g,y[i+"2"]=m,y}}}function Nl(t){return this._axes[t]}function Rl(t){iw.call(this,t)}function Bl(t,e){return e.type||(e.data?"category":"value")}function Vl(t,e,n){return t.getCoordSysModel()===e}function Fl(t,e){var n=e*Math.PI/180,i=t.plain(),r=i.width,o=i.height,a=r*Math.cos(n)+o*Math.sin(n),s=r*Math.sin(n)+o*Math.cos(n);return new Xt(i.x,i.y,a,s)}function Hl(t){var e,n=t.model,i=n.get("axisLabel.show")?n.getFormattedLabels():[],r=n.getModel("axisLabel"),o=1,a=i.length;a>40&&(o=Math.ceil(a/40));for(var s=0;s<a;s+=o)if(!t.isLabelIgnored(s)){var l=Fl(r.getTextRect(i[s]),r.get("rotate")||0);e?e.union(l):e=l}return e}function Gl(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}function Wl(t,e,n){var i=t[e];if(n.onZero){var r=n.onZeroAxisIndex;if(null==r){for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];if(a&&!Zl(a)){r=+o;break}}null==r&&(n.onZero=!1),n.onZeroAxisIndex=r}else(a=i[r])&&Zl(a)&&(n.onZero=!1)}}function Zl(t){return"category"===t.type||"time"===t.type||!dw(t)}function Ul(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}function Xl(t,e){return f(gw,function(e){return t.getReferringComponents(e)[0]})}function jl(t){return"cartesian2d"===t.get("coordinateSystem")}function Yl(t){var e={componentType:t.mainType};return e[t.mainType+"Index"]=t.componentIndex,e}function ql(t,e,n,i){var r,o,a=Cr(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;return Dr(a-mw/2)?(o=l?"bottom":"top",r="center"):Dr(a-1.5*mw)?(o=l?"top":"bottom",r="center"):(o="middle",r=a<1.5*mw&&a>mw/2?l?"left":"right":l?"right":"left"),{rotation:a,textAlign:r,textVerticalAlign:o}}function $l(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)}function Kl(t,e,n){var i=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],n=n||[];var o=e[0],a=e[1],s=e[e.length-1],l=e[e.length-2],h=n[0],u=n[1],c=n[n.length-1],d=n[n.length-2];!1===i?(Ql(o),Ql(h)):Jl(o,a)&&(i?(Ql(a),Ql(u)):(Ql(o),Ql(h))),!1===r?(Ql(s),Ql(c)):Jl(l,s)&&(r?(Ql(l),Ql(d)):(Ql(s),Ql(c)))}function Ql(t){t&&(t.ignore=!0)}function Jl(t,e,n){var i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r){var o=ot([]);return ht(o,o,-t.rotation),i.applyTransform(st([],o,t.getLocalTransform())),r.applyTransform(st([],o,e.getLocalTransform())),i.intersect(r)}}function th(t){return"middle"===t||"center"===t}function eh(t,e,n){var i=e.axis;if(e.get("axisTick.show")&&!i.scale.isBlank()){for(var r=e.getModel("axisTick"),o=r.getModel("lineStyle"),s=r.get("length"),l=ww(r,n.labelInterval),h=i.getTicksCoords(r.get("alignWithLabel")),u=i.scale.getTicks(),c=e.get("axisLabel.showMinLabel"),d=e.get("axisLabel.showMaxLabel"),f=[],p=[],g=t._transform,m=[],v=h.length,y=0;y<v;y++)if(!_w(i,y,l,v,c,d)){var x=h[y];f[0]=x,f[1]=0,p[0]=x,p[1]=n.tickDirection*s,g&&($(f,f,g),$(p,p,g));var _=new mv(zi({anid:"tick_"+u[y],shape:{x1:f[0],y1:f[1],x2:p[0],y2:p[1]},style:a(o.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),z2:2,silent:!0}));t.group.add(_),m.push(_)}return m}}function nh(t,e,n){var i=e.axis;if(T(n.axisLabelShow,e.get("axisLabel.show"))&&!i.scale.isBlank()){var r=e.getModel("axisLabel"),o=r.get("margin"),a=i.scale.getTicks(),s=e.getFormattedLabels(),l=(T(n.labelRotate,r.get("rotate"))||0)*mw/180,h=xw(n.rotation,l,n.labelDirection),u=e.getCategories(),c=[],f=$l(e),p=e.get("triggerEvent"),g=e.get("axisLabel.showMinLabel"),m=e.get("axisLabel.showMaxLabel");return d(a,function(l,d){if(!_w(i,d,n.labelInterval,a.length,g,m)){var v=r;u&&u[l]&&u[l].textStyle&&(v=new pr(u[l].textStyle,r,e.ecModel));var y=v.getTextColor()||e.get("axisLine.lineStyle.color"),x=[i.dataToCoord(l),n.labelOffset+n.labelDirection*o],_=i.scale.getLabel(l),w=new ov({anid:"label_"+l,position:x,rotation:h.rotation,silent:f,z2:10});Ki(w.style,v,{text:s[d],textAlign:v.getShallow("align",!0)||h.textAlign,textVerticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||h.textVerticalAlign,textFill:"function"==typeof y?y("category"===i.type?_:"value"===i.type?l+"":l,d):y}),p&&(w.eventData=Yl(e),w.eventData.targetType="axisLabel",w.eventData.value=_),t._dumbGroup.add(w),w.updateTransform(),c.push(w),t.group.add(w),w.decomposeTransform()}}),c}}function ih(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return rh(n,t,e),n.seriesInvolved&&ah(n,t),n}function rh(t,e,n){var i=e.getComponent("tooltip"),r=e.getComponent("axisPointer"),o=r.get("link",!0)||[],a=[];bw(n.getCoordinateSystems(),function(n){function s(i,s,l){var c=l.model.getModel("axisPointer",r),d=c.get("show");if(d&&("auto"!==d||i||dh(c))){null==s&&(s=c.get("triggerTooltip"));var f=(c=i?oh(l,u,r,e,i,s):c).get("snap"),p=fh(l.model),g=s||f||"category"===l.type,m=t.axesInfo[p]={key:p,axis:l,coordSys:n,axisPointerModel:c,triggerTooltip:s,involveSeries:g,snap:f,useHandle:dh(c),seriesModels:[]};h[p]=m,t.seriesInvolved|=g;var v=sh(o,l);if(null!=v){var y=a[v]||(a[v]={axesInfo:{}});y.axesInfo[p]=m,y.mapper=o[v].mapper,m.linkGroup=y}}}if(n.axisPointerEnabled){var l=fh(n.model),h=t.coordSysAxesInfo[l]={};t.coordSysMap[l]=n;var u=n.model.getModel("tooltip",i);if(bw(n.getAxes(),Sw(s,!1,null)),n.getTooltipAxes&&i&&u.get("show")){var c="axis"===u.get("trigger"),d="cross"===u.get("axisPointer.type"),f=n.getTooltipAxes(u.get("axisPointer.axis"));(c||d)&&bw(f.baseAxes,Sw(s,!d||"cross",c)),d&&bw(f.otherAxes,Sw(s,"cross",!1))}}})}function oh(t,e,i,r,o,s){var l=e.getModel("axisPointer"),h={};bw(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){h[t]=n(l.get(t))}),h.snap="category"!==t.type&&!!s,"cross"===l.get("type")&&(h.type="line");var u=h.label||(h.label={});if(null==u.show&&(u.show=!1),"cross"===o){var c=l.get("label.show");if(u.show=null==c||c,!s){var d=h.lineStyle=l.get("crossStyle");d&&a(u,d.textStyle)}}return t.model.getModel("axisPointer",new pr(h,i,r))}function ah(t,e){e.eachSeries(function(e){var n=e.coordinateSystem,i=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);n&&"none"!==i&&!1!==i&&"item"!==i&&!1!==r&&!1!==e.get("axisPointer.show",!0)&&bw(t.coordSysAxesInfo[fh(n.model)],function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function sh(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(lh(o[i+"AxisId"],n.id)||lh(o[i+"AxisIndex"],n.componentIndex)||lh(o[i+"AxisName"],n.name))return r}}function lh(t,e){return"all"===t||y(t)&&l(t,e)>=0||t===e}function hh(t){var e=uh(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=dh(n);null==o&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function uh(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[fh(t)]}function ch(t){var e=uh(t);return e&&e.axisPointerModel}function dh(t){return!!t.get("handle.show")}function fh(t){return t.type+"||"+t.id}function ph(t,e,n,i,r,o){var a=Mw.getAxisPointerClass(t.axisPointerClass);if(a){var s=ch(e);s?(t._axisPointer||(t._axisPointer=new a)).render(e,s,i,o):gh(t,i)}}function gh(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}function mh(t,e,n){n=n||{};var i=t.coordinateSystem,r=e.axis,o={},a=r.position,s=r.onZero?"onZero":a,l=r.dim,h=i.getRect(),u=[h.x,h.x+h.width,h.y,h.y+h.height],c={left:0,right:1,top:0,bottom:1,onZero:2},d=e.get("offset")||0,f="x"===l?[u[2]-d,u[3]+d]:[u[0]-d,u[1]+d];if(r.onZero){var p=i.getAxis("x"===l?"y":"x",r.onZeroAxisIndex),g=p.toGlobalCoord(p.dataToCoord(0));f[c.onZero]=Math.max(Math.min(g,f[1]),f[0])}o.position=["y"===l?f[c[s]]:u[0],"x"===l?f[c[s]]:u[3]],o.rotation=Math.PI/2*("x"===l?0:1);var m={top:-1,bottom:1,left:-1,right:1};o.labelDirection=o.tickDirection=o.nameDirection=m[a],o.labelOffset=r.onZero?f[c[a]]-f[c.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),T(n.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var v=e.get("axisLabel.rotate");return o.labelRotate="top"===s?-v:v,o.labelInterval=r.getLabelInterval(),o.z2=1,o}function vh(t,e,n,i,r,o,a){$i(t,e,n.getModel("label"),n.getModel("emphasis.label"),{labelFetcher:r,labelDataIndex:o,defaultText:ll(r.getData(),o),isRectText:!0,autoColor:i}),yh(t),yh(e)}function yh(t,e){"outside"===t.textPosition&&(t.textPosition=e)}function xh(t,e,n){n.style.text=null,ar(n,{shape:{width:0}},e,t,function(){n.parent&&n.parent.remove(n)})}function _h(t,e,n){n.style.text=null,ar(n,{shape:{r:n.shape.r0}},e,t,function(){n.parent&&n.parent.remove(n)})}function wh(t,e,n,i,r,o,s,l){var h=e.getItemVisual(n,"color"),u=e.getItemVisual(n,"opacity"),c=i.getModel("itemStyle"),d=i.getModel("emphasis.itemStyle").getBarItemStyle();l||t.setShape("r",c.get("barBorderRadius")||0),t.useStyle(a({fill:h,opacity:u},c.getBarItemStyle()));var f=i.getShallow("cursor");f&&t.attr("cursor",f);var p=s?r.height>0?"bottom":"top":r.width>0?"left":"right";l||vh(t.style,d,i,h,o,n,p),qi(t,d)}function bh(t,e){var n=t.get(Ow)||0;return Math.min(n,Math.abs(e.width),Math.abs(e.height))}function Sh(t,e,n,i){var r=e.getData(),o=this.dataIndex,a=r.getName(o),s=e.get("selectedOffset");i.dispatchAction({type:"pieToggleSelect",from:t,name:a,seriesId:e.id}),r.each(function(t){Mh(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,n)})}function Mh(t,e,n,i,r){var o=(e.startAngle+e.endAngle)/2,a=Math.cos(o),s=Math.sin(o),l=n?i:0,h=[a*l,s*l];r?t.animate().when(200,{position:h}).start("bounceOut"):t.attr("position",h)}function Ih(t,e){function n(){o.ignore=o.hoverIgnore,a.ignore=a.hoverIgnore}function i(){o.ignore=o.normalIgnore,a.ignore=a.normalIgnore}Jp.call(this);var r=new hv({z2:2}),o=new pv,a=new ov;this.add(r),this.add(o),this.add(a),this.updateData(t,e,!0),this.on("emphasis",n).on("normal",i).on("mouseover",n).on("mouseout",i)}function Th(t,e,n,i,r,o,a){function s(e,n){for(var i=e;i>=0&&(t[i].y-=n,!(i>0&&t[i].y>t[i-1].y+t[i-1].height));i--);}function l(t,e,n,i,r,o){for(var a=e?Number.MAX_VALUE:0,s=0,l=t.length;s<l;s++)if("center"!==t[s].position){var h=Math.abs(t[s].y-i),u=t[s].len,c=t[s].len2,d=h<r+u?Math.sqrt((r+u+c)*(r+u+c)-h*h):Math.abs(t[s].x-n);e&&d>=a&&(d=a-10),!e&&d<=a&&(d=a+10),t[s].x=n+d*o,a=d}}t.sort(function(t,e){return t.y-e.y});for(var h,u=0,c=t.length,d=[],f=[],p=0;p<c;p++)(h=t[p].y-u)<0&&function(e,n,i,r){for(var o=e;o<n;o++)if(t[o].y+=i,o>e&&o+1<n&&t[o+1].y>t[o].y+t[o].height)return void s(o,i/2);s(n-1,i/2)}(p,c,-h),u=t[p].y+t[p].height;a-u<0&&s(c-1,u-a);for(p=0;p<c;p++)t[p].y>=n?f.push(t[p]):d.push(t[p]);l(d,!1,e,n,i,r),l(f,!0,e,n,i,r)}function Ch(t,e,n,i,r,o){for(var a=[],s=[],l=0;l<t.length;l++)t[l].x<e?a.push(t[l]):s.push(t[l]);Th(s,e,n,i,1,r,o),Th(a,e,n,i,-1,r,o);for(l=0;l<t.length;l++){var h=t[l].linePoints;if(h){var u=h[1][0]-h[2][0];t[l].x<e?h[2][0]=t[l].x+3:h[2][0]=t[l].x-3,h[1][1]=h[2][1]=t[l].y,h[1][0]=h[2][0]+u}}}function Dh(){this.group=new Jp}function Ah(t,e,n,i){var r=n.type,o=new(0,Av[r.charAt(0).toUpperCase()+r.slice(1)])(n);e.add(o),i.set(t,o),o.__ecGraphicId=t}function kh(t,e){var n=t&&t.parent;n&&("group"===t.type&&t.traverse(function(t){kh(t,e)}),e.removeKey(t.__ecGraphicId),n.remove(t))}function Ph(t){return t=o({},t),d(["id","parentId","$action","hv","bounding"].concat(jv),function(e){delete t[e]}),t}function Lh(t,e){var n;return d(e,function(e){null!=t[e]&&"auto"!==t[e]&&(n=!0)}),n}function Oh(t,e){var n=t.exist;if(e.id=t.keyInfo.id,!e.type&&n&&(e.type=n.type),null==e.parentId){var i=e.parentOption;i?e.parentId=i.id:n&&(e.parentId=n.parentId)}e.parentOption=null}function zh(t,e,n){var r=o({},n),a=t[e],s=n.$action||"merge";"merge"===s?a?(i(a,r,!0),Zr(a,r,{ignoreSize:!0}),Xr(n,a)):t[e]=r:"replace"===s?t[e]=r:"remove"===s&&a&&(t[e]=null)}function Eh(t,e){t&&(t.hv=e.hv=[Lh(e,["left","right"]),Lh(e,["top","bottom"])],"group"===t.type&&(null==t.width&&(t.width=e.width=0),null==t.height&&(t.height=e.height=0)))}function Nh(t,e,n,i,r){var a=t.axis;if(!a.scale.isBlank()&&a.containData(e))if(t.involveSeries){var s=Rh(e,t),l=s.payloadBatch,h=s.snapToValue;l[0]&&null==r.seriesIndex&&o(r,l[0]),!i&&t.snap&&a.containData(h)&&null!=h&&(e=h),n.showPointer(t,e,l,r),n.showTooltip(t,s,h)}else n.showPointer(t,e)}function Rh(t,e){var n=e.axis,i=n.dim,r=t,o=[],a=Number.MAX_VALUE,s=-1;return jw(e.seriesModels,function(e,l){var h,u,c=e.getData().mapDimension(i,!0);if(e.getAxisTooltipData){var d=e.getAxisTooltipData(c,t,n);u=d.dataIndices,h=d.nestestValue}else{if(!(u=e.getData().indicesOfNearest(c[0],t,"category"===n.type?.5:null)).length)return;h=e.getData().get(c[0],u[0])}if(null!=h&&isFinite(h)){var f=t-h,p=Math.abs(f);p<=a&&((p<a||f>=0&&s<0)&&(a=p,s=f,r=h,o.length=0),jw(u,function(t){o.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})}))}}),{payloadBatch:o,snapToValue:r}}function Bh(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function Vh(t,e,n,i){var r=n.payloadBatch,o=e.axis,a=o.model,s=e.axisPointerModel;if(e.triggerTooltip&&r.length){var l=e.coordSys.model,h=fh(l),u=t.map[h];u||(u=t.map[h]={coordSysId:l.id,coordSysIndex:l.componentIndex,coordSysType:l.type,coordSysMainType:l.mainType,dataByAxis:[]},t.list.push(u)),u.dataByAxis.push({axisDim:o.dim,axisIndex:a.componentIndex,axisType:a.type,axisId:a.id,value:i,valueLabelOpt:{precision:s.get("label.precision"),formatter:s.get("label.formatter")},seriesDataIndices:r.slice()})}}function Fh(t,e,n){var i=n.axesInfo=[];jw(e,function(e,n){var r=e.axisPointerModel.option,o=t[n];o?(!e.useHandle&&(r.status="show"),r.value=o.value,r.seriesDataIndices=(o.payloadBatch||[]).slice()):!e.useHandle&&(r.status="hide"),"show"===r.status&&i.push({axisDim:e.axis.dim,axisIndex:e.axis.model.componentIndex,value:r.value})})}function Hh(t,e,n,i){if(!Uh(e)&&t.list.length){var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}else i({type:"hideTip"})}function Gh(t,e,n){var i=n.getZr(),r=qw(i).axisPointerLastHighlights||{},o=qw(i).axisPointerLastHighlights={};jw(t,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&jw(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;o[e]=t})});var a=[],s=[];d(r,function(t,e){!o[e]&&s.push(t)}),d(o,function(t,e){!r[e]&&a.push(t)}),s.length&&n.dispatchAction({type:"downplay",escapeConnect:!0,batch:s}),a.length&&n.dispatchAction({type:"highlight",escapeConnect:!0,batch:a})}function Wh(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}function Zh(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function Uh(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}function Xh(t,e,n){if(!Kf.node){var i=e.getZr();$w(i).records||($w(i).records={}),jh(i,e),($w(i).records[t]||($w(i).records[t]={})).handler=n}}function jh(t,e){function n(n,i){t.on(n,function(n){var r=Kh(e);Kw($w(t).records,function(t){t&&i(t,n,r.dispatchAction)}),Yh(r.pendings,e)})}$w(t).initialized||($w(t).initialized=!0,n("click",v($h,"click")),n("mousemove",v($h,"mousemove")),n("globalout",qh))}function Yh(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]),n&&(n.dispatchAction=null,e.dispatchAction(n))}function qh(t,e,n){t.handler("leave",null,n)}function $h(t,e,n,i){e.handler(t,n,i)}function Kh(t){var e={showTip:[],hideTip:[]},n=function(i){var r=e[i.type];r?r.push(i):(i.dispatchAction=n,t.dispatchAction(i))};return{dispatchAction:n,pendings:e}}function Qh(t,e){if(!Kf.node){var n=e.getZr();($w(n).records||{})[t]&&($w(n).records[t]=null)}}function Jh(){}function tu(t,e,n,i){eu(Jw(n).lastProp,i)||(Jw(n).lastProp=i,e?ar(n,i,t):(n.stopAnimation(),n.attr(i)))}function eu(t,e){if(w(t)&&w(e)){var n=!0;return d(e,function(e,i){n=n&&eu(t[i],e)}),!!n}return t===e}function nu(t,e){t[e.get("label.show")?"show":"hide"]()}function iu(t){return{position:t.position.slice(),rotation:t.rotation||0}}function ru(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function ou(t){var e,n=t.get("type"),i=t.getModel(n+"Style");return"line"===n?(e=i.getLineStyle()).fill=null:"shadow"===n&&((e=i.getAreaStyle()).stroke=null),e}function au(t,e,n,i,r){var o=lu(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),a=n.getModel("label"),s=Fv(a.get("padding")||0),l=a.getFont(),h=ce(o,l),u=r.position,c=h.width+s[1]+s[3],d=h.height+s[0]+s[2],f=r.align;"right"===f&&(u[0]-=c),"center"===f&&(u[0]-=c/2);var p=r.verticalAlign;"bottom"===p&&(u[1]-=d),"middle"===p&&(u[1]-=d/2),su(u,c,d,i);var g=a.get("backgroundColor");g&&"auto"!==g||(g=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:c,height:d,r:a.get("borderRadius")},position:u.slice(),style:{text:o,textFont:l,textFill:a.getTextColor(),textPosition:"inside",fill:g,stroke:a.get("borderColor")||"transparent",lineWidth:a.get("borderWidth")||0,shadowBlur:a.get("shadowBlur"),shadowColor:a.get("shadowColor"),shadowOffsetX:a.get("shadowOffsetX"),shadowOffsetY:a.get("shadowOffsetY")},z2:10}}function su(t,e,n,i){var r=i.getWidth(),o=i.getHeight();t[0]=Math.min(t[0]+e,r)-e,t[1]=Math.min(t[1]+n,o)-n,t[0]=Math.max(t[0],0),t[1]=Math.max(t[1],0)}function lu(t,e,n,i,r){var o=e.scale.getLabel(t,{precision:r.precision}),a=r.formatter;if(a){var s={value:Js(e,t),seriesData:[]};d(i,function(t){var e=n.getSeriesByIndex(t.seriesIndex),i=t.dataIndexInside,r=e&&e.getDataParams(i);r&&s.seriesData.push(r)}),_(a)?o=a.replace("{value}",o):x(a)&&(o=a(s))}return o}function hu(t,e,n){var i=rt();return ht(i,i,n.rotation),lt(i,i,n.position),hr([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}function uu(t,e,n,i,r,o){var a=vw.innerTextLayout(n.rotation,0,n.labelDirection);n.labelMargin=r.get("label.margin"),au(e,i,r,o,{position:hu(i.axis,t,n),align:a.textAlign,verticalAlign:a.textVerticalAlign})}function cu(t,e,n){return n=n||0,{x1:t[n],y1:t[1-n],x2:e[n],y2:e[1-n]}}function du(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}function fu(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}function pu(t){return"x"===t.dim?0:1}function gu(t){var e="left "+t+"s cubic-bezier(0.23, 1, 0.32, 1),top "+t+"s cubic-bezier(0.23, 1, 0.32, 1)";return f(ab,function(t){return t+"transition:"+e}).join(";")}function mu(t){var e=[],n=t.get("fontSize"),i=t.getTextColor();return i&&e.push("color:"+i),e.push("font:"+t.getFont()),n&&e.push("line-height:"+Math.round(3*n/2)+"px"),rb(["decoration","align"],function(n){var i=t.get(n);i&&e.push("text-"+n+":"+i)}),e.join(";")}function vu(t){var e=[],n=t.get("transitionDuration"),i=t.get("backgroundColor"),r=t.getModel("textStyle"),o=t.get("padding");return n&&e.push(gu(n)),i&&(Kf.canvasSupported?e.push("background-Color:"+i):(e.push("background-Color:#"+Dt(i)),e.push("filter:alpha(opacity=70)"))),rb(["width","color","radius"],function(n){var i="border-"+n,r=ob(i),o=t.get(r);null!=o&&e.push(i+":"+o+("color"===n?"":"px"))}),e.push(mu(r)),null!=o&&e.push("padding:"+Fv(o).join("px ")+"px"),e.join(";")+";"}function yu(t,e){if(Kf.wxa)return null;var n=document.createElement("div"),i=this._zr=e.getZr();this.el=n,this._x=e.getWidth()/2,this._y=e.getHeight()/2,t.appendChild(n),this._container=t,this._show=!1,this._hideTimeout;var r=this;n.onmouseenter=function(){r._enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},n.onmousemove=function(e){if(e=e||window.event,!r._enterable){var n=i.handler;rn(t,e,!0),n.dispatch("mousemove",e)}},n.onmouseleave=function(){r._enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}function xu(t){for(var e=t.pop();t.length;){var n=t.pop();n&&(pr.isInstance(n)&&(n=n.get("tooltip",!0)),"string"==typeof n&&(n={formatter:n}),e=new pr(n,e,e.ecModel))}return e}function _u(t,e){return t.dispatchAction||m(e.dispatchAction,e)}function wu(t,e,n,i,r,o,a){var s=Su(n),l=s.width,h=s.height;return null!=o&&(t+l+o>i?t-=l+o:t+=o),null!=a&&(e+h+a>r?e-=h+a:e+=a),[t,e]}function bu(t,e,n,i,r){var o=Su(n),a=o.width,s=o.height;return t=Math.min(t+a,i)-a,e=Math.min(e+s,r)-s,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function Su(t){var e=t.clientWidth,n=t.clientHeight;if(document.defaultView&&document.defaultView.getComputedStyle){var i=document.defaultView.getComputedStyle(t);i&&(e+=parseInt(i.paddingLeft,10)+parseInt(i.paddingRight,10)+parseInt(i.borderLeftWidth,10)+parseInt(i.borderRightWidth,10),n+=parseInt(i.paddingTop,10)+parseInt(i.paddingBottom,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10))}return{width:e,height:n}}function Mu(t,e,n){var i=n[0],r=n[1],o=0,a=0,s=e.width,l=e.height;switch(t){case"inside":o=e.x+s/2-i/2,a=e.y+l/2-r/2;break;case"top":o=e.x+s/2-i/2,a=e.y-r-5;break;case"bottom":o=e.x+s/2-i/2,a=e.y+l+5;break;case"left":o=e.x-i-5,a=e.y+l/2-r/2;break;case"right":o=e.x+s+5,a=e.y+l/2-r/2}return[o,a]}function Iu(t){return"center"===t||"middle"===t}function Tu(t,e,n){var i,r={},o="toggleSelected"===t;return n.eachComponent("legend",function(n){o&&null!=i?n[i?"select":"unSelect"](e.name):(n[t](e.name),i=n.isSelected(e.name)),d(n.getData(),function(t){var e=t.get("name");if("\n"!==e&&""!==e){var i=n.isSelected(e);r.hasOwnProperty(e)?r[e]=r[e]&&i:r[e]=i}})}),{name:e.name,selected:r}}function Cu(t,e,n){var i=e.getBoxLayoutParams(),r=e.get("padding"),o={width:n.getWidth(),height:n.getHeight()},a=Gr(i,o,r);qv(e.get("orient"),t,e.get("itemGap"),a.width,a.height),Wr(t,i,o,r)}function Du(t,e){var n=Fv(e.get("padding")),i=e.getItemStyle(["color","opacity"]);return i.fill=e.get("backgroundColor"),t=new gv({shape:{x:t.x-n[3],y:t.y-n[0],width:t.width+n[1]+n[3],height:t.height+n[0]+n[2],r:e.get("borderRadius")},style:i,silent:!0,z2:-1})}function Au(t,e){e.dispatchAction({type:"legendToggleSelect",name:t})}function ku(t,e,n){var i=n.getZr().storage.getDisplayList()[0];i&&i.useHoverLayer||t.get("legendHoverLink")&&n.dispatchAction({type:"highlight",seriesName:t.name,name:e})}function Pu(t,e,n){var i=n.getZr().storage.getDisplayList()[0];i&&i.useHoverLayer||t.get("legendHoverLink")&&n.dispatchAction({type:"downplay",seriesName:t.name,name:e})}function Lu(t,e,n){var i=[1,1];i[t.getOrient().index]=0,Zr(e,n,{type:"box",ignoreSize:i})}function Ou(t){_n(t,"label",["show"])}function zu(t){return!(isNaN(parseFloat(t.x))&&isNaN(parseFloat(t.y)))}function Eu(t){return!isNaN(parseFloat(t.x))&&!isNaN(parseFloat(t.y))}function Nu(t,e,n,i,r,o){var a=[],s=As(e,i,n)?e.getCalculationInfo("stackResultDimension"):i,l=Gu(e,s,t),h=e.indicesOfNearest(s,l)[0];a[r]=e.get(n,h),a[o]=e.get(i,h);var u=Sr(e.get(i,h));return(u=Math.min(u,20))>=0&&(a[o]=+a[o].toFixed(u)),a}function Ru(t,e){var i=t.getData(),r=t.coordinateSystem;if(e&&!Eu(e)&&!y(e.coord)&&r){var o=r.dimensions,a=Bu(e,i,r,t);if((e=n(e)).type&&Tb[e.type]&&a.baseAxis&&a.valueAxis){var s=Mb(o,a.baseAxis.dim),l=Mb(o,a.valueAxis.dim);e.coord=Tb[e.type](i,a.baseDataDim,a.valueDataDim,s,l),e.value=e.coord[l]}else{for(var h=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis],u=0;u<2;u++)Tb[h[u]]&&(h[u]=Gu(i,i.mapDimension(o[u]),h[u]));e.coord=h}}return e}function Bu(t,e,n,i){var r={};return null!=t.valueIndex||null!=t.valueDim?(r.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=n.getAxis(Vu(i,r.valueDataDim)),r.baseAxis=n.getOtherAxis(r.valueAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim)):(r.baseAxis=i.getBaseAxis(),r.valueAxis=n.getOtherAxis(r.baseAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim),r.valueDataDim=e.mapDimension(r.valueAxis.dim)),r}function Vu(t,e){var n=t.getData(),i=n.dimensions;e=n.getDimension(e);for(var r=0;r<i.length;r++){var o=n.getDimensionInfo(i[r]);if(o.name===e)return o.coordDim}}function Fu(t,e){return!(t&&t.containData&&e.coord&&!zu(e))||t.containData(e.coord)}function Hu(t,e,n,i){return i<2?t.coord&&t.coord[i]:t.value}function Gu(t,e,n){if("average"===n){var i=0,r=0;return t.each(e,function(t,e){isNaN(t)||(i+=t,r++)}),i/r}return t.getDataExtent(e,!0)["max"===n?1:0]}function Wu(t,e,n){var i=e.coordinateSystem;t.each(function(r){var o,a=t.getItemModel(r),s=_r(a.get("x"),n.getWidth()),l=_r(a.get("y"),n.getHeight());if(isNaN(s)||isNaN(l)){if(e.getMarkerPosition)o=e.getMarkerPosition(t.getValues(t.dimensions,r));else if(i){var h=t.get(i.dimensions[0],r),u=t.get(i.dimensions[1],r);o=i.dataToPoint([h,u])}}else o=[s,l];isNaN(s)||(o[0]=s),isNaN(l)||(o[1]=l),t.setItemLayout(r,o)})}function Zu(t,e,n){var i;i=t?f(t&&t.dimensions,function(t){return a({name:t},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{})}):[{name:"value",type:"float"}];var r=new Yx(i,n),o=f(n.get("data"),v(Ru,e));return t&&(o=g(o,v(Fu,t))),r.initData(o,null,t?Hu:function(t){return t.value}),r}function Uu(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}function Xu(t){return"_"+t+"Type"}function ju(t,e,n){var i=e.getItemVisual(n,"color"),r=e.getItemVisual(n,t),o=e.getItemVisual(n,t+"Size");if(r&&"none"!==r){y(o)||(o=[o,o]);var a=el(r,-o[0]/2,-o[1]/2,o[0],o[1],i);return a.name=t,a}}function Yu(t){var e=new kb({name:"line"});return qu(e.shape,t),e}function qu(t,e){var n=e[0],i=e[1],r=e[2];t.x1=n[0],t.y1=n[1],t.x2=i[0],t.y2=i[1],t.percent=1,r?(t.cpx1=r[0],t.cpy1=r[1]):(t.cpx1=NaN,t.cpy1=NaN)}function $u(t,e,n){Jp.call(this),this._createLine(t,e,n)}function Ku(t){this._ctor=t||$u,this.group=new Jp}function Qu(t,e,n,i){if(nc(e.getItemLayout(n))){var r=new t._ctor(e,n,i);e.setItemGraphicEl(n,r),t.group.add(r)}}function Ju(t,e,n,i,r,o){var a=e.getItemGraphicEl(i);nc(n.getItemLayout(r))?(a?a.updateData(n,r,o):a=new t._ctor(n,r,o),n.setItemGraphicEl(r,a),t.group.add(a)):t.group.remove(a)}function tc(t){var e=t.hostModel;return{lineStyle:e.getModel("lineStyle").getLineStyle(),hoverLineStyle:e.getModel("emphasis.lineStyle").getLineStyle(),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label")}}function ec(t){return isNaN(t[0])||isNaN(t[1])}function nc(t){return!ec(t[0])&&!ec(t[1])}function ic(t){return!isNaN(t)&&!isFinite(t)}function rc(t,e,n,i){var r=1-t,o=i.dimensions[t];return ic(e[r])&&ic(n[r])&&e[t]===n[t]&&i.getAxis(o).containData(e[t])}function oc(t,e){if("cartesian2d"===t.type){var n=e[0].coord,i=e[1].coord;if(n&&i&&(rc(1,n,i,t)||rc(0,n,i,t)))return!0}return Fu(t,e[0])&&Fu(t,e[1])}function ac(t,e,n,i,r){var o,a=i.coordinateSystem,s=t.getItemModel(e),l=_r(s.get("x"),r.getWidth()),h=_r(s.get("y"),r.getHeight());if(isNaN(l)||isNaN(h)){if(i.getMarkerPosition)o=i.getMarkerPosition(t.getValues(t.dimensions,e));else{var u=a.dimensions,c=t.get(u[0],e),d=t.get(u[1],e);o=a.dataToPoint([c,d])}if("cartesian2d"===a.type){var f=a.getAxis("x"),p=a.getAxis("y"),u=a.dimensions;ic(t.get(u[0],e))?o[0]=f.toGlobalCoord(f.getExtent()[n?0:1]):ic(t.get(u[1],e))&&(o[1]=p.toGlobalCoord(p.getExtent()[n?0:1]))}isNaN(l)||(o[0]=l),isNaN(h)||(o[1]=h)}else o=[l,h];t.setItemLayout(e,o)}function sc(t,e,n){var i;i=t?f(t&&t.dimensions,function(t){return a({name:t},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{})}):[{name:"value",type:"float"}];var r=new Yx(i,n),o=new Yx(i,n),s=new Yx([],n),l=f(n.get("data"),v(zb,e,t,n));t&&(l=g(l,v(oc,t)));var h=t?Hu:function(t){return t.value};return r.initData(f(l,function(t){return t[0]}),null,h),o.initData(f(l,function(t){return t[1]}),null,h),s.initData(f(l,function(t){return t[2]})),s.hasItemOption=!0,{from:r,to:o,line:s}}function lc(t){return!isNaN(t)&&!isFinite(t)}function hc(t,e,n,i){var r=1-t;return lc(e[r])&&lc(n[r])}function uc(t,e){var n=e.coord[0],i=e.coord[1];return!("cartesian2d"!==t.type||!n||!i||!hc(1,n,i,t)&&!hc(0,n,i,t))||(Fu(t,{coord:n,x:e.x0,y:e.y0})||Fu(t,{coord:i,x:e.x1,y:e.y1}))}function cc(t,e,n,i,r){var o,a=i.coordinateSystem,s=t.getItemModel(e),l=_r(s.get(n[0]),r.getWidth()),h=_r(s.get(n[1]),r.getHeight());if(isNaN(l)||isNaN(h)){if(i.getMarkerPosition)o=i.getMarkerPosition(t.getValues(n,e));else{var u=[f=t.get(n[0],e),p=t.get(n[1],e)];a.clampData&&a.clampData(u,u),o=a.dataToPoint(u,!0)}if("cartesian2d"===a.type){var c=a.getAxis("x"),d=a.getAxis("y"),f=t.get(n[0],e),p=t.get(n[1],e);lc(f)?o[0]=c.toGlobalCoord(c.getExtent()["x0"===n[0]?0:1]):lc(p)&&(o[1]=d.toGlobalCoord(d.getExtent()["y0"===n[1]?0:1]))}isNaN(l)||(o[0]=l),isNaN(h)||(o[1]=h)}else o=[l,h];return o}function dc(t,e,n){var i,r,o=["x0","y0","x1","y1"];t?(i=f(t&&t.dimensions,function(t){var n=e.getData();return a({name:t},n.getDimensionInfo(n.mapDimension(t))||{})}),r=new Yx(f(o,function(t,e){return{name:t,type:i[e%2].type}}),n)):r=new Yx(i=[{name:"value",type:"float"}],n);var s=f(n.get("data"),v(Eb,e,t,n));t&&(s=g(s,v(uc,t)));var l=t?function(t,e,n,i){return t.coord[Math.floor(i/2)][i%2]}:function(t){return t.value};return r.initData(s,null,l),r.hasItemOption=!0,r}function fc(t){return l(Rb,t)>=0}function pc(t,e,n){function i(t,e){return l(e.nodes,t)>=0}function r(t,i){var r=!1;return e(function(e){d(n(t,e)||[],function(t){i.records[e.name][t]&&(r=!0)})}),r}function o(t,i){i.nodes.push(t),e(function(e){d(n(t,e)||[],function(t){i.records[e.name][t]=!0})})}return function(n){var a={nodes:[],records:{}};if(e(function(t){a.records[t.name]={}}),!n)return a;o(n,a);var s;do{s=!1,t(function(t){!i(t,a)&&r(t,a)&&(o(t,a),s=!0)})}while(s);return a}}function gc(t,e,n){var i=[1/0,-1/0];return Vb(n,function(t){var n=t.getData();n&&Vb(n.mapDimension(e,!0),function(t){var e=n.getApproximateExtent(t);e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1])})}),i[1]<i[0]&&(i=[NaN,NaN]),mc(t,i),i}function mc(t,e){var n=t.getAxisModel(),i=n.getMin(!0),r="category"===n.get("type"),o=r&&n.getCategories().length;null!=i&&"dataMin"!==i&&"function"!=typeof i?e[0]=i:r&&(e[0]=o>0?0:NaN);var a=n.getMax(!0);return null!=a&&"dataMax"!==a&&"function"!=typeof a?e[1]=a:r&&(e[1]=o>0?o-1:NaN),n.get("scale",!0)||(e[0]>0&&(e[0]=0),e[1]<0&&(e[1]=0)),e}function vc(t,e){var n=t.getAxisModel(),i=t._percentWindow,r=t._valueWindow;if(i){var o=Ir(r,[0,500]);o=Math.min(o,20);var a=e||0===i[0]&&100===i[1];n.setRange(a?null:+r[0].toFixed(o),a?null:+r[1].toFixed(o))}}function yc(t){var e=t._minMaxSpan={},n=t._dataZoomModel;Vb(["min","max"],function(i){e[i+"Span"]=n.get(i+"Span");var r=n.get(i+"ValueSpan");if(null!=r&&(e[i+"ValueSpan"]=r,null!=(r=t.getAxisModel().axis.scale.parse(r)))){var o=t._dataExtent;e[i+"Span"]=xr(o[0]+r,o,[0,100],!0)}})}function xc(t){var e={};return Gb(["start","end","startValue","endValue","throttle"],function(n){t.hasOwnProperty(n)&&(e[n]=t[n])}),e}function _c(t,e){var n=t._rangePropMode,i=t.get("rangeMode");Gb([["start","startValue"],["end","endValue"]],function(t,r){var o=null!=e[t[0]],a=null!=e[t[1]];o&&!a?n[r]="percent":!o&&a?n[r]="value":i?n[r]=i[r]:o&&(n[r]="percent")})}function wc(t,e){var n=t[e]-t[1-e];return{span:Math.abs(n),sign:n>0?-1:n<0?1:e?-1:1}}function bc(t,e){return Math.min(e[1],Math.max(e[0],t))}function Sc(t){return{x:"y",y:"x",radius:"angle",angle:"radius"}[t]}function Mc(t){return"vertical"===t?"ns-resize":"ew-resize"}function Ic(t,e,n){Dc(t)[e]=n}function Tc(t,e,n){var i=Dc(t);i[e]===n&&(i[e]=null)}function Cc(t,e){return!!Dc(t)[e]}function Dc(t){return t[nS]||(t[nS]={})}function Ac(t){this.pointerChecker,this._zr=t,this._opt={};var e=m,i=e(kc,this),r=e(Pc,this),o=e(Lc,this),s=e(Oc,this),l=e(zc,this);xp.call(this),this.setPointerChecker=function(t){this.pointerChecker=t},this.enable=function(e,h){this.disable(),this._opt=a(n(h)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,preventDefaultMouseMove:!0}),null==e&&(e=!0),!0!==e&&"move"!==e&&"pan"!==e||(t.on("mousedown",i),t.on("mousemove",r),t.on("mouseup",o)),!0!==e&&"scale"!==e&&"zoom"!==e||(t.on("mousewheel",s),t.on("pinch",l))},this.disable=function(){t.off("mousedown",i),t.off("mousemove",r),t.off("mouseup",o),t.off("mousewheel",s),t.off("pinch",l)},this.dispose=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}function kc(t){if(!(sn(t)||t.target&&t.target.draggable)){var e=t.offsetX,n=t.offsetY;this.pointerChecker&&this.pointerChecker(t,e,n)&&(this._x=e,this._y=n,this._dragging=!0)}}function Pc(t){if(!sn(t)&&Nc(this,"moveOnMouseMove",t)&&this._dragging&&"pinch"!==t.gestureEvent&&!Cc(this._zr,"globalPan")){var e=t.offsetX,n=t.offsetY,i=this._x,r=this._y,o=e-i,a=n-r;this._x=e,this._y=n,this._opt.preventDefaultMouseMove&&Ag(t.event),this.trigger("pan",o,a,i,r,e,n)}}function Lc(t){sn(t)||(this._dragging=!1)}function Oc(t){if(Nc(this,"zoomOnMouseWheel",t)&&0!==t.wheelDelta){var e=t.wheelDelta>0?1.1:1/1.1;Ec.call(this,t,e,t.offsetX,t.offsetY)}}function zc(t){if(!Cc(this._zr,"globalPan")){var e=t.pinchScale>1?1.1:1/1.1;Ec.call(this,t,e,t.pinchX,t.pinchY)}}function Ec(t,e,n,i){this.pointerChecker&&this.pointerChecker(t,n,i)&&(Ag(t.event),this.trigger("zoom",e,n,i))}function Nc(t,e,n){var i=t._opt[e];return i&&(!_(i)||n.event[i+"Key"])}function Rc(t,e){var n=Hc(t),i=e.dataZoomId,r=e.coordId;d(n,function(t,n){var o=t.dataZoomInfos;o[i]&&l(e.allCoordIds,r)<0&&(delete o[i],t.count--)}),Wc(n);var o=n[r];o||((o=n[r]={coordId:r,dataZoomInfos:{},count:0}).controller=Gc(t,o),o.dispatchAction=v(jc,t)),!o.dataZoomInfos[i]&&o.count++,o.dataZoomInfos[i]=e;var a=Yc(o.dataZoomInfos);o.controller.enable(a.controlType,a.opt),o.controller.setPointerChecker(e.containsPoint),ha(o,"dispatchAction",e.throttleRate,"fixRate")}function Bc(t,e){var n=Hc(t);d(n,function(t){t.controller.dispose();var n=t.dataZoomInfos;n[e]&&(delete n[e],t.count--)}),Wc(n)}function Vc(t,e){if(t&&"dataZoom"===t.type&&t.batch)for(var n=0,i=t.batch.length;n<i;n++)if(t.batch[n].dataZoomId===e)return!1;return!0}function Fc(t){return t.type+"\0_"+t.id}function Hc(t){var e=t.getZr();return e[rS]||(e[rS]={})}function Gc(t,e){var n=new Ac(t.getZr());return n.on("pan",iS(Zc,e)),n.on("zoom",iS(Uc,e)),n}function Wc(t){d(t,function(e,n){e.count||(e.controller.dispose(),delete t[n])})}function Zc(t,e,n,i,r,o,a){Xc(t,function(s){return s.panGetRange(t.controller,e,n,i,r,o,a)})}function Uc(t,e,n,i){Xc(t,function(r){return r.zoomGetRange(t.controller,e,n,i)})}function Xc(t,e){var n=[];d(t.dataZoomInfos,function(t){var i=e(t);!t.disabled&&i&&n.push({dataZoomId:t.dataZoomId,start:i[0],end:i[1]})}),t.dispatchAction(n)}function jc(t,e){t.dispatchAction({type:"dataZoom",batch:e})}function Yc(t){var e,n={},i={type_true:2,type_move:1,type_false:0,type_undefined:-1};return d(t,function(t){var r=!t.disabled&&(!t.zoomLock||"move");i["type_"+r]>i["type_"+e]&&(e=r),o(n,t.roamControllerOpt)}),{controlType:e,opt:n}}function qc(t,e){lS[t]=e}function $c(t){return lS[t]}function Kc(t){return 0===t.indexOf("my")}function Qc(t){this.model=t}function Jc(t){this.model=t}function td(t){var e={},n=[],i=[];return t.eachRawSeries(function(t){var r=t.coordinateSystem;if(!r||"cartesian2d"!==r.type&&"polar"!==r.type)n.push(t);else{var o=r.getBaseAxis();if("category"===o.type){var a=o.dim+"_"+o.index;e[a]||(e[a]={categoryAxis:o,valueAxis:r.getOtherAxis(o),series:[]},i.push({axisDim:o.dim,axisIndex:o.index})),e[a].series.push(t)}else n.push(t)}}),{seriesGroupByCategoryAxis:e,other:n,meta:i}}function ed(t){var e=[];return d(t,function(t,n){var i=t.categoryAxis,r=t.valueAxis.dim,o=[" "].concat(f(t.series,function(t){return t.name})),a=[i.model.getCategories()];d(t.series,function(t){a.push(t.getRawData().mapArray(r,function(t){return t}))});for(var s=[o.join(vS)],l=0;l<a[0].length;l++){for(var h=[],u=0;u<a.length;u++)h.push(a[u][l]);s.push(h.join(vS))}e.push(s.join("\n"))}),e.join("\n\n"+mS+"\n\n")}function nd(t){return f(t,function(t){var e=t.getRawData(),n=[t.name],i=[];return e.each(e.dimensions,function(){for(var t=arguments.length,r=arguments[t-1],o=e.getName(r),a=0;a<t-1;a++)i[a]=arguments[a];n.push((o?o+vS:"")+i.join(vS))}),n.join("\n")}).join("\n\n"+mS+"\n\n")}function id(t){var e=td(t);return{value:g([ed(e.seriesGroupByCategoryAxis),nd(e.other)],function(t){return t.replace(/[\n\t\s]/g,"")}).join("\n\n"+mS+"\n\n"),meta:e.meta}}function rd(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function od(t){if(t.slice(0,t.indexOf("\n")).indexOf(vS)>=0)return!0}function ad(t){for(var e=t.split(/\n+/g),n=[],i=f(rd(e.shift()).split(yS),function(t){return{name:t,data:[]}}),r=0;r<e.length;r++){var o=rd(e[r]).split(yS);n.push(o.shift());for(var a=0;a<o.length;a++)i[a]&&(i[a].data[r]=o[a])}return{series:i,categories:n}}function sd(t){for(var e=t.split(/\n+/g),n=rd(e.shift()),i=[],r=0;r<e.length;r++){var o,a=rd(e[r]).split(yS),s="",l=!1;isNaN(a[0])?(l=!0,s=a[0],a=a.slice(1),i[r]={name:s,value:[]},o=i[r].value):o=i[r]=[];for(var h=0;h<a.length;h++)o.push(+a[h]);1===o.length&&(l?i[r].value=o[0]:i[r]=o[0])}return{name:n,data:i}}function ld(t,e){var n={series:[]};return d(t.split(new RegExp("\n*"+mS+"\n*","g")),function(t,i){if(od(t)){var r=ad(t),o=e[i],a=o.axisDim+"Axis";o&&(n[a]=n[a]||[],n[a][o.axisIndex]={data:r.categories},n.series=n.series.concat(r.series))}else{r=sd(t);n.series.push(r)}}),n}function hd(t){this._dom=null,this.model=t}function ud(t,e){return f(t,function(t,n){var i=e&&e[n];return w(i)&&!y(i)?(w(t)&&!y(t)&&(t=t.value),a({value:t},i)):t})}function cd(t){xp.call(this),this._zr=t,this.group=new Jp,this._brushType,this._brushOption,this._panels,this._track=[],this._dragging,this._covers=[],this._creatingCover,this._creatingPanel,this._enableGlobalPan,this._uid="brushController_"+LS++,this._handlers={},_S(OS,function(t,e){this._handlers[e]=m(t,this)},this)}function dd(t,e){var r=t._zr;t._enableGlobalPan||Ic(r,DS,t._uid),_S(t._handlers,function(t,e){r.on(e,t)}),t._brushType=e.brushType,t._brushOption=i(n(PS),e,!0)}function fd(t){var e=t._zr;Tc(e,DS,t._uid),_S(t._handlers,function(t,n){e.off(n,t)}),t._brushType=t._brushOption=null}function pd(t,e){var n=zS[e.brushType].createCover(t,e);return n.__brushOption=e,vd(n,e),t.group.add(n),n}function gd(t,e){var n=xd(e);return n.endCreating&&(n.endCreating(t,e),vd(e,e.__brushOption)),e}function md(t,e){var n=e.__brushOption;xd(e).updateCoverShape(t,e,n.range,n)}function vd(t,e){var n=e.z;null==n&&(n=IS),t.traverse(function(t){t.z=n,t.z2=n})}function yd(t,e){xd(e).updateCommon(t,e),md(t,e)}function xd(t){return zS[t.__brushOption.brushType]}function _d(t,e,n){var i=t._panels;if(!i)return!0;var r,o=t._transform;return _S(i,function(t){t.isTargetByCursor(e,n,o)&&(r=t)}),r}function wd(t,e){var n=t._panels;if(!n)return!0;var i=e.__brushOption.panelId;return null==i||n[i]}function bd(t){var e=t._covers,n=e.length;return _S(e,function(e){t.group.remove(e)},t),e.length=0,!!n}function Sd(t,e){var i=wS(t._covers,function(t){var e=t.__brushOption,i=n(e.range);return{brushType:e.brushType,panelId:e.panelId,range:i}});t.trigger("brush",i,{isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function Md(t){var e=t._track;if(!e.length)return!1;var n=e[e.length-1],i=e[0],r=n[0]-i[0],o=n[1]-i[1];return MS(r*r+o*o,.5)>TS}function Id(t){var e=t.length-1;return e<0&&(e=0),[t[0],t[e]]}function Td(t,e,n,i){var r=new Jp;return r.add(new gv({name:"main",style:kd(n),silent:!0,draggable:!0,cursor:"move",drift:xS(t,e,r,"nswe"),ondragend:xS(Sd,e,{isEnd:!0})})),_S(i,function(n){r.add(new gv({name:n,style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:xS(t,e,r,n),ondragend:xS(Sd,e,{isEnd:!0})}))}),r}function Cd(t,e,n,i){var r=i.brushStyle.lineWidth||0,o=SS(r,CS),a=n[0][0],s=n[1][0],l=a-r/2,h=s-r/2,u=n[0][1],c=n[1][1],d=u-o+r/2,f=c-o+r/2,p=u-a,g=c-s,m=p+r,v=g+r;Ad(t,e,"main",a,s,p,g),i.transformable&&(Ad(t,e,"w",l,h,o,v),Ad(t,e,"e",d,h,o,v),Ad(t,e,"n",l,h,m,o),Ad(t,e,"s",l,f,m,o),Ad(t,e,"nw",l,h,o,o),Ad(t,e,"ne",d,h,o,o),Ad(t,e,"sw",l,f,o,o),Ad(t,e,"se",d,f,o,o))}function Dd(t,e){var n=e.__brushOption,i=n.transformable,r=e.childAt(0);r.useStyle(kd(n)),r.attr({silent:!i,cursor:i?"move":"default"}),_S(["w","e","n","s","se","sw","ne","nw"],function(n){var r=e.childOfName(n),o=Od(t,n);r&&r.attr({silent:!i,invisible:!i,cursor:i?kS[o]+"-resize":null})})}function Ad(t,e,n,i,r,o,a){var s=e.childOfName(n);s&&s.setShape(Bd(Rd(t,e,[[i,r],[i+o,r+a]])))}function kd(t){return a({strokeNoScale:!0},t.brushStyle)}function Pd(t,e,n,i){var r=[bS(t,n),bS(e,i)],o=[SS(t,n),SS(e,i)];return[[r[0],o[0]],[r[1],o[1]]]}function Ld(t){return lr(t.group)}function Od(t,e){if(e.length>1)return("e"===(i=[Od(t,(e=e.split(""))[0]),Od(t,e[1])])[0]||"w"===i[0])&&i.reverse(),i.join("");var n={left:"w",right:"e",top:"n",bottom:"s"},i=ur({w:"left",e:"right",n:"top",s:"bottom"}[e],Ld(t));return n[i]}function zd(t,e,n,i,r,o,a,s){var l=i.__brushOption,h=t(l.range),u=Nd(n,o,a);_S(r.split(""),function(t){var e=AS[t];h[e[0]][e[1]]+=u[e[0]]}),l.range=e(Pd(h[0][0],h[1][0],h[0][1],h[1][1])),yd(n,i),Sd(n,{isEnd:!1})}function Ed(t,e,n,i,r){var o=e.__brushOption.range,a=Nd(t,n,i);_S(o,function(t){t[0]+=a[0],t[1]+=a[1]}),yd(t,e),Sd(t,{isEnd:!1})}function Nd(t,e,n){var i=t.group,r=i.transformCoordToLocal(e,n),o=i.transformCoordToLocal(0,0);return[r[0]-o[0],r[1]-o[1]]}function Rd(t,e,i){var r=wd(t,e);return r&&!0!==r?r.clipPath(i,t._transform):n(i)}function Bd(t){var e=bS(t[0][0],t[1][0]),n=bS(t[0][1],t[1][1]);return{x:e,y:n,width:SS(t[0][0],t[1][0])-e,height:SS(t[0][1],t[1][1])-n}}function Vd(t,e,n){if(t._brushType){var i=t._zr,r=t._covers,o=_d(t,e,n);if(!t._dragging)for(var a=0;a<r.length;a++){var s=r[a].__brushOption;if(o&&(!0===o||s.panelId===o.panelId)&&zS[s.brushType].contain(r[a],n[0],n[1]))return}o&&i.setCursorStyle("crosshair")}}function Fd(t){var e=t.event;e.preventDefault&&e.preventDefault()}function Hd(t,e,n){return t.childOfName("main").contain(e,n)}function Gd(t,e,i,r){var o,a=t._creatingCover,s=t._creatingPanel,l=t._brushOption;if(t._track.push(i.slice()),Md(t)||a){if(s&&!a){"single"===l.brushMode&&bd(t);var h=n(l);h.brushType=Wd(h.brushType,s),h.panelId=!0===s?null:s.panelId,a=t._creatingCover=pd(t,h),t._covers.push(a)}if(a){var u=zS[Wd(t._brushType,s)];a.__brushOption.range=u.getCreatingRange(Rd(t,a,t._track)),r&&(gd(t,a),u.updateCommon(t,a)),md(t,a),o={isEnd:r}}}else r&&"single"===l.brushMode&&l.removeOnClick&&_d(t,e,i)&&bd(t)&&(o={isEnd:r,removeOnClick:!0});return o}function Wd(t,e){return"auto"===t?e.defaultBrushType:t}function Zd(t){if(this._dragging){Fd(t);var e=Gd(this,t,this.group.transformCoordToLocal(t.offsetX,t.offsetY),!0);this._dragging=!1,this._track=[],this._creatingCover=null,e&&Sd(this,e)}}function Ud(t){return{createCover:function(e,n){return Td(xS(zd,function(e){var n=[e,[0,100]];return t&&n.reverse(),n},function(e){return e[t]}),e,n,[["w","e"],["n","s"]][t])},getCreatingRange:function(e){var n=Id(e);return[bS(n[0][t],n[1][t]),SS(n[0][t],n[1][t])]},updateCoverShape:function(e,n,i,r){var o,a=wd(e,n);if(!0!==a&&a.getLinearBrushOtherExtent)o=a.getLinearBrushOtherExtent(t,e._transform);else{var s=e._zr;o=[0,[s.getWidth(),s.getHeight()][1-t]]}var l=[i,o];t&&l.reverse(),Cd(e,n,l,r)},updateCommon:Dd,contain:Hd}}function Xd(t,e,n){var i=e.getComponentByElement(t.topTarget),r=i&&i.coordinateSystem;return i&&i!==n&&!ES[i.mainType]&&r&&r.model!==n}function jd(t){return t=$d(t),function(e,n){return dr(e,t)}}function Yd(t,e){return t=$d(t),function(n){var i=null!=e?e:n,r=i?t.width:t.height,o=i?t.x:t.y;return[o,o+(r||0)]}}function qd(t,e,n){return t=$d(t),function(i,r,o){return t.contain(r[0],r[1])&&!Xd(i,e,n)}}function $d(t){return Xt.create(t)}function Kd(t,e,n){var i=this._targetInfoList=[],r={},o=Jd(e,t);NS(GS,function(t,e){(!n||!n.include||RS(n.include,e)>=0)&&t(o,i,r)})}function Qd(t){return t[0]>t[1]&&t.reverse(),t}function Jd(t,e){return An(t,e,{includeMainTypes:FS})}function tf(t,e,n,i){var r=n.getAxis(["x","y"][t]),o=Qd(f([0,1],function(t){return e?r.coordToData(r.toLocalCoord(i[t])):r.toGlobalCoord(r.dataToCoord(i[t]))})),a=[];return a[t]=o,a[1-t]=[NaN,NaN],{values:o,xyMinMax:a}}function ef(t,e,n,i){return[e[0]-i[t]*n[0],e[1]-i[t]*n[1]]}function nf(t,e){var n=rf(t),i=rf(e),r=[n[0]/i[0],n[1]/i[1]];return isNaN(r[0])&&(r[0]=1),isNaN(r[1])&&(r[1]=1),r}function rf(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[NaN,NaN]}function of(t,e){var n=hf(t);jS(e,function(e,i){for(var r=n.length-1;r>=0&&!n[r][i];r--);if(r<0){var o=t.queryComponents({mainType:"dataZoom",subType:"select",id:i})[0];if(o){var a=o.getPercentRange();n[0][i]={dataZoomId:i,start:a[0],end:a[1]}}}}),n.push(e)}function af(t){var e=hf(t),n=e[e.length-1];e.length>1&&e.pop();var i={};return jS(n,function(t,n){for(var r=e.length-1;r>=0;r--)if(t=e[r][n]){i[n]=t;break}}),i}function sf(t){t[YS]=null}function lf(t){return hf(t).length}function hf(t){var e=t[YS];return e||(e=t[YS]=[{}]),e}function uf(t,e,n){(this._brushController=new cd(n.getZr())).on("brush",m(this._onBrush,this)).mount(),this._isZoomActive}function cf(t){var e={};return d(["xAxisIndex","yAxisIndex"],function(n){e[n]=t[n],null==e[n]&&(e[n]="all"),(!1===e[n]||"none"===e[n])&&(e[n]=[])}),e}function df(t,e){t.setIconStatus("back",lf(e)>1?"emphasis":"normal")}function ff(t,e,n,i,r){var o=n._isZoomActive;i&&"takeGlobalCursor"===i.type&&(o="dataZoomSelect"===i.key&&i.dataZoomSelectActive),n._isZoomActive=o,t.setIconStatus("zoom",o?"emphasis":"normal");var a=new Kd(cf(t.option),e,{include:["grid"]});n._brushController.setPanels(a.makePanelOpts(r,function(t){return t.xAxisDeclared&&!t.yAxisDeclared?"lineX":!t.xAxisDeclared&&t.yAxisDeclared?"lineY":"rect"})).enableBrush(!!o&&{brushType:"auto",brushStyle:{lineWidth:0,fill:"rgba(0,0,0,0.2)"}})}function pf(t){this.model=t}function gf(t){return eM(t)}function mf(){if(!rM&&oM){rM=!0;var t=oM.styleSheets;t.length<31?oM.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):t[0].addRule(".zrvml","behavior:url(#default#VML)")}}function vf(t){return parseInt(t,10)}function yf(t,e){mf(),this.root=t,this.storage=e;var n=document.createElement("div"),i=document.createElement("div");n.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",i.style.cssText="position:absolute;left:0;top:0;",t.appendChild(n),this._vmlRoot=i,this._vmlViewport=n,this.resize();var r=e.delFromStorage,o=e.addToStorage;e.delFromStorage=function(t){r.call(e,t),t&&t.onRemove&&t.onRemove(i)},e.addToStorage=function(t){t.onAdd&&t.onAdd(i),o.call(e,t)},this._firstPaint=!0}function xf(t){return function(){jp('In IE8.0 VML mode painter not support method "'+t+'"')}}function _f(t){return document.createElementNS(VM,t)}function wf(t){return WM(1e4*t)/1e4}function bf(t){return t<qM&&t>-qM}function Sf(t,e){var n=e?t.textFill:t.fill;return null!=n&&n!==GM}function Mf(t,e){var n=e?t.textStroke:t.stroke;return null!=n&&n!==GM}function If(t,e){e&&Tf(t,"transform","matrix("+HM.call(e,",")+")")}function Tf(t,e,n){(!n||"linear"!==n.type&&"radial"!==n.type)&&t.setAttribute(e,n)}function Cf(t,e,n){t.setAttributeNS("http://www.w3.org/1999/xlink",e,n)}function Df(t,e,n){if(Sf(e,n)){var i=n?e.textFill:e.fill;i="transparent"===i?GM:i,"none"!==t.getAttribute("clip-path")&&i===GM&&(i="rgba(0, 0, 0, 0.002)"),Tf(t,"fill",i),Tf(t,"fill-opacity",e.opacity)}else Tf(t,"fill",GM);if(Mf(e,n)){var r=n?e.textStroke:e.stroke;Tf(t,"stroke",r="transparent"===r?GM:r),Tf(t,"stroke-width",(n?e.textStrokeWidth:e.lineWidth)/(!n&&e.strokeNoScale?e.host.getLineScale():1)),Tf(t,"paint-order",n?"stroke":"fill"),Tf(t,"stroke-opacity",e.opacity),e.lineDash?(Tf(t,"stroke-dasharray",e.lineDash.join(",")),Tf(t,"stroke-dashoffset",WM(e.lineDashOffset||0))):Tf(t,"stroke-dasharray",""),e.lineCap&&Tf(t,"stroke-linecap",e.lineCap),e.lineJoin&&Tf(t,"stroke-linejoin",e.lineJoin),e.miterLimit&&Tf(t,"stroke-miterlimit",e.miterLimit)}else Tf(t,"stroke",GM)}function Af(t){for(var e=[],n=t.data,i=t.len(),r=0;r<i;){var o="",a=0;switch(n[r++]){case FM.M:o="M",a=2;break;case FM.L:o="L",a=2;break;case FM.Q:o="Q",a=4;break;case FM.C:o="C",a=6;break;case FM.A:var s=n[r++],l=n[r++],h=n[r++],u=n[r++],c=n[r++],d=n[r++],f=n[r++],p=n[r++],g=Math.abs(d),m=bf(g-jM)&&!bf(g),v=!1;v=g>=jM||!bf(g)&&(d>-XM&&d<0||d>XM)==!!p;var y=wf(s+h*UM(c)),x=wf(l+u*ZM(c));m&&(d=p?jM-1e-4:1e-4-jM,v=!0,9===r&&e.push("M",y,x));var _=wf(s+h*UM(c+d)),w=wf(l+u*ZM(c+d));e.push("A",wf(h),wf(u),WM(f*YM),+v,+p,_,w);break;case FM.Z:o="Z";break;case FM.R:var _=wf(n[r++]),w=wf(n[r++]),b=wf(n[r++]),S=wf(n[r++]);e.push("M",_,w,"L",_+b,w,"L",_+b,w+S,"L",_,w+S,"L",_,w)}o&&e.push(o);for(var M=0;M<a;M++)e.push(wf(n[r++]))}return e.join(" ")}function kf(t){return"middle"===t?"middle":"bottom"===t?"baseline":"hanging"}function Pf(){}function Lf(t,e,n,i){for(var r=0,o=e.length,a=0,s=0;r<o;r++){var l=e[r];if(l.removed){for(var h=[],u=s;u<s+l.count;u++)h.push(u);l.indices=h,s+=l.count}else{for(var h=[],u=a;u<a+l.count;u++)h.push(u);l.indices=h,a+=l.count,l.added||(s+=l.count)}}return e}function Of(t){return{newPos:t.newPos,components:t.components.slice(0)}}function zf(t,e,n,i,r){this._zrId=t,this._svgRoot=e,this._tagNames="string"==typeof n?[n]:n,this._markLabel=i,this._domName=r||"_dom",this.nextId=0}function Ef(t,e){zf.call(this,t,e,["linearGradient","radialGradient"],"__gradient_in_use__")}function Nf(t,e){zf.call(this,t,e,"clipPath","__clippath_in_use__")}function Rf(t,e){zf.call(this,t,e,["filter"],"__filter_in_use__","_shadowDom")}function Bf(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY||t.textShadowBlur||t.textShadowOffsetX||t.textShadowOffsetY)}function Vf(t){return parseInt(t,10)}function Ff(t){return t instanceof xi?$M:t instanceof je?KM:t instanceof ov?QM:$M}function Hf(t,e){return e&&t&&e.parentNode!==t}function Gf(t,e,n){if(Hf(t,e)&&n){var i=n.nextSibling;i?t.insertBefore(e,i):t.appendChild(e)}}function Wf(t,e){if(Hf(t,e)){var n=t.firstChild;n?t.insertBefore(e,n):t.appendChild(e)}}function Zf(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)}function Uf(t){return t.__textSvgEl}function Xf(t){return t.__svgEl}function jf(t){return function(){jp('In SVG mode painter not support method "'+t+'"')}}var Yf=2311,qf=function(){return Yf++},$f={},Kf=$f="undefined"!=typeof wx?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0}:function(t){var e={},n={},i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge\/([\d.]+)/),a=/micromessenger/i.test(t);return i&&(n.firefox=!0,n.version=i[1]),r&&(n.ie=!0,n.version=r[1]),o&&(n.edge=!0,n.version=o[1]),a&&(n.weChat=!0),{browser:n,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!n.ie&&!n.edge,pointerEventsSupported:"onpointerdown"in window&&(n.edge||n.ie&&n.version>=11)}}(navigator.userAgent),Qf={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},Jf={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},tp=Object.prototype.toString,ep=Array.prototype,np=ep.forEach,ip=ep.filter,rp=ep.slice,op=ep.map,ap=ep.reduce,sp={},lp=function(){return sp.createCanvas()};sp.createCanvas=function(){return document.createElement("canvas")};var hp,up="__ec_primitive__";E.prototype={constructor:E,get:function(t){return this.hasOwnProperty(t)?this[t]:null},set:function(t,e){return this[t]=e},each:function(t,e){void 0!==e&&(t=m(t,e));for(var n in this)this.hasOwnProperty(n)&&t(this[n],n)},removeKey:function(t){delete this[t]}};var cp=(Object.freeze||Object)({$override:e,clone:n,merge:i,mergeAll:r,extend:o,defaults:a,createCanvas:lp,getContext:s,indexOf:l,inherits:h,mixin:u,isArrayLike:c,each:d,map:f,reduce:p,filter:g,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},bind:m,curry:v,isArray:y,isFunction:x,isString:_,isObject:w,isBuiltInObject:b,isTypedArray:S,isDom:M,eqNaN:I,retrieve:T,retrieve2:C,retrieve3:D,slice:A,normalizeCssArray:k,assert:P,trim:L,setAsPrimitive:O,isPrimitive:z,createHashMap:N,concatArray:function(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n},noop:R}),dp="undefined"==typeof Float32Array?Array:Float32Array,fp=Z,pp=U,gp=Y,mp=q,vp=(Object.freeze||Object)({create:B,copy:V,clone:F,set:function(t,e,n){return t[0]=e,t[1]=n,t},add:H,scaleAndAdd:G,sub:W,len:Z,length:fp,lenSquare:U,lengthSquare:pp,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:X,normalize:j,distance:Y,dist:gp,distanceSquare:q,distSquare:mp,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t},applyTransform:$,min:K,max:Q});J.prototype={constructor:J,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(tt(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.dispatchToElement(tt(e,t),"drag",t.event);var a=this.findHover(n,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.dispatchToElement(tt(s,t),"dragleave",t.event),a&&a!==s&&this.dispatchToElement(tt(a,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(tt(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(tt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var yp=Array.prototype.slice,xp=function(){this._$handlers={}};xp.prototype={constructor:xp,one:function(t,e,n){var i=this._$handlers;if(!e||!t)return this;i[t]||(i[t]=[]);for(var r=0;r<i[t].length;r++)if(i[t][r].h===e)return this;return i[t].push({h:e,one:!0,ctx:n||this}),this},on:function(t,e,n){var i=this._$handlers;if(!e||!t)return this;i[t]||(i[t]=[]);for(var r=0;r<i[t].length;r++)if(i[t][r].h===e)return this;return i[t].push({h:e,one:!1,ctx:n||this}),this},isSilent:function(t){var e=this._$handlers;return e[t]&&e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!=e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){if(this._$handlers[t]){var e=arguments,n=e.length;n>3&&(e=yp.call(e,1));for(var i=this._$handlers[t],r=i.length,o=0;o<r;){switch(n){case 1:i[o].h.call(i[o].ctx);break;case 2:i[o].h.call(i[o].ctx,e[1]);break;case 3:i[o].h.call(i[o].ctx,e[1],e[2]);break;default:i[o].h.apply(i[o].ctx,e)}i[o].one?(i.splice(o,1),r--):o++}}return this},triggerWithContext:function(t){if(this._$handlers[t]){var e=arguments,n=e.length;n>4&&(e=yp.call(e,1,e.length-1));for(var i=e[e.length-1],r=this._$handlers[t],o=r.length,a=0;a<o;){switch(n){case 1:r[a].h.call(i);break;case 2:r[a].h.call(i,e[1]);break;case 3:r[a].h.call(i,e[1],e[2]);break;default:r[a].h.apply(i,e)}r[a].one?(r.splice(a,1),o--):a++}}return this}};var _p="silent";nt.prototype.dispose=function(){};var wp=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],bp=function(t,e,n,i){xp.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new nt,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,J.call(this),this.setHandlerProxy(n)};bp.prototype={constructor:bp,setHandlerProxy:function(t){this.proxy&&this.proxy.dispose(),t&&(d(wp,function(e){t.on&&t.on(e,this[e],this)},this),t.handler=this),this.proxy=t},mousemove:function(t){var e=t.zrX,n=t.zrY,i=this._hovered,r=i.target;r&&!r.__zr&&(r=(i=this.findHover(i.x,i.y)).target);var o=this._hovered=this.findHover(e,n),a=o.target,s=this.proxy;s.setCursor&&s.setCursor(a?a.cursor:"default"),r&&a!==r&&this.dispatchToElement(i,"mouseout",t),this.dispatchToElement(o,"mousemove",t),a&&a!==r&&this.dispatchToElement(o,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,"mouseout",t);var e,n=t.toElement||t.relatedTarget;do{n=n&&n.parentNode}while(n&&9!=n.nodeType&&!(e=n===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(t){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o=et(e,t,n);i&&(i[r]&&(o.cancelBubble=i[r].call(i,o)),i.trigger(e,o),i=i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)}))}},findHover:function(t,e,n){for(var i=this.storage.getDisplayList(),r={x:t,y:e},o=i.length-1;o>=0;o--){var a;if(i[o]!==n&&!i[o].ignore&&(a=it(i[o],t,e))&&(!r.topTarget&&(r.topTarget=i[o]),a!==_p)){r.target=i[o];break}}return r}},d(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){bp.prototype[t]=function(e){var n=this.findHover(e.zrX,e.zrY),i=n.target;if("mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||gp(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}),u(bp,xp),u(bp,J);var Sp="undefined"==typeof Float32Array?Array:Float32Array,Mp=(Object.freeze||Object)({create:rt,identity:ot,copy:at,mul:st,translate:lt,rotate:ht,scale:ut,invert:ct,clone:function(t){var e=rt();return at(e,t),e}}),Ip=ot,Tp=5e-5,Cp=function(t){(t=t||{}).position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},Dp=Cp.prototype;Dp.transform=null,Dp.needLocalTransform=function(){return dt(this.rotation)||dt(this.position[0])||dt(this.position[1])||dt(this.scale[0]-1)||dt(this.scale[1]-1)},Dp.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),i=this.transform;n||e?(i=i||rt(),n?this.getLocalTransform(i):Ip(i),e&&(n?st(i,t.transform,i):at(i,t.transform)),this.transform=i,this.invTransform=this.invTransform||rt(),ct(this.invTransform,i)):i&&Ip(i)},Dp.getLocalTransform=function(t){return Cp.getLocalTransform(this,t)},Dp.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},Dp.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var Ap=[];Dp.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(st(Ap,t.invTransform,e),e=Ap);var n=e[0]*e[0]+e[1]*e[1],i=e[2]*e[2]+e[3]*e[3],r=this.position,o=this.scale;dt(n-1)&&(n=Math.sqrt(n)),dt(i-1)&&(i=Math.sqrt(i)),e[0]<0&&(n=-n),e[3]<0&&(i=-i),r[0]=e[4],r[1]=e[5],o[0]=n,o[1]=i,this.rotation=Math.atan2(-e[1]/i,e[0]/n)}},Dp.getGlobalScale=function(){var t=this.transform;if(!t)return[1,1];var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]),n=Math.sqrt(t[2]*t[2]+t[3]*t[3]);return t[0]<0&&(e=-e),t[3]<0&&(n=-n),[e,n]},Dp.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&$(n,n,i),n},Dp.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&$(n,n,i),n},Cp.getLocalTransform=function(t,e){Ip(e=e||[]);var n=t.origin,i=t.scale||[1,1],r=t.rotation||0,o=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),ut(e,e,i),r&&ht(e,e,r),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=o[0],e[5]+=o[1],e};var kp={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-kp.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*kp.bounceIn(2*t):.5*kp.bounceOut(2*t-1)+.5}};ft.prototype={constructor:ft,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var n=(t-this._startTime-this._pausedTime)/this._life;if(!(n<0)){n=Math.min(n,1);var i=this.easing,r="string"==typeof i?kp[i]:i,o="function"==typeof r?r(n):n;return this.fire("frame",o),1==n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){this[t="on"+t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};var Pp=function(){this.head=null,this.tail=null,this._len=0},Lp=Pp.prototype;Lp.insert=function(t){var e=new Op(t);return this.insertEntry(e),e},Lp.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},Lp.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},Lp.len=function(){return this._len},Lp.clear=function(){this.head=this.tail=null,this._len=0};var Op=function(t){this.value=t,this.next,this.prev},zp=function(t){this._list=new Pp,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},Ep=zp.prototype;Ep.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var o=n.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new Op(e),a.key=t,n.insertEntry(a),i[t]=a}return r},Ep.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},Ep.clear=function(){this._list.clear(),this._map={}};var Np={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},Rp=new zp(20),Bp=null,Vp=At,Fp=kt,Hp=(Object.freeze||Object)({parse:Mt,lift:Ct,toHex:Dt,fastLerp:At,fastMapToColor:Vp,lerp:kt,mapToColor:Fp,modifyHSL:function(t,e,n,i){if(t=Mt(t))return t=Tt(t),null!=e&&(t[0]=gt(e)),null!=n&&(t[1]=yt(n)),null!=i&&(t[2]=yt(i)),Lt(It(t),"rgba")},modifyAlpha:Pt,stringify:Lt}),Gp=Array.prototype.slice,Wp=function(t,e,n,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||Ot,this._setter=i||zt,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};Wp.prototype={when:function(t,e){var n=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!n[i]){n[i]=[];var r=this._getter(this._target,i);if(null==r)continue;0!==t&&n[i].push({time:0,value:Gt(r)})}n[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;n<e;n++)t[n].call(this)},start:function(t,e){var n,i=this,r=0;for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var a=Ut(this,t,function(){--r||i._doneCallback()},this._tracks[o],o,e);a&&(this._clipList.push(a),r++,this.animation&&this.animation.addClip(a),n=a)}if(n){var s=n.onframe;n.onframe=function(t,e){s(t,e);for(var n=0;n<i._onframeList.length;n++)i._onframeList[n](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var Zp=1;"undefined"!=typeof window&&(Zp=Math.max(window.devicePixelRatio||1,1));var Up=Zp,Xp=function(){},jp=Xp,Yp=function(){this.animators=[]};Yp.prototype={constructor:Yp,animate:function(t,e){var n,i=!1,r=this,o=this.__zr;if(t){var a=t.split("."),s=r;i="shape"===a[0];for(var h=0,u=a.length;h<u;h++)s&&(s=s[a[h]]);s&&(n=s)}else n=r;if(n){var c=r.animators,d=new Wp(n,e);return d.during(function(t){r.dirty(i)}).done(function(){c.splice(l(c,d),1)}),c.push(d),o&&o.animation.addAnimator(d),d}jp('Property "'+t+'" is not existed in element '+r.id)},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;i<n;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,o){_(n)?(r=i,i=n,n=0):x(i)?(r=i,i="linear",n=0):x(n)?(r=n,n=0):x(e)?(r=e,e=500):e||(e=500),this.stopAnimation(),this._animateToShallow("",this,t,e,n);var a=this.animators.slice(),s=a.length;s||r&&r();for(var l=0;l<a.length;l++)a[l].done(function(){--s||r&&r()}).start(i,o)},_animateToShallow:function(t,e,n,i,r){var o={},a=0;for(var s in n)if(n.hasOwnProperty(s))if(null!=e[s])w(n[s])&&!c(n[s])?this._animateToShallow(t?t+"."+s:s,e[s],n[s],i,r):(o[s]=n[s],a++);else if(null!=n[s])if(t){var l={};l[t]={},l[t][s]=n[s],this.attr(l)}else this.attr(s,n[s]);return a>0&&this.animate(t,!1).when(null==i?500:i,o).delay(r||0),this}};var qp=function(t){Cp.call(this,t),xp.call(this,t),Yp.call(this,t),this.id=t.id||qf()};qp.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var n=this[t];n||(n=this[t]=[]),n[0]=e[0],n[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(w(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},u(qp,Yp),u(qp,Cp),u(qp,xp);var $p=$,Kp=Math.min,Qp=Math.max;Xt.prototype={constructor:Xt,union:function(t){var e=Kp(t.x,this.x),n=Kp(t.y,this.y);this.width=Qp(t.x+t.width,this.x+this.width)-e,this.height=Qp(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:function(){var t=[],e=[],n=[],i=[];return function(r){if(r){t[0]=n[0]=this.x,t[1]=i[1]=this.y,e[0]=i[0]=this.x+this.width,e[1]=n[1]=this.y+this.height,$p(t,t,r),$p(e,e,r),$p(n,n,r),$p(i,i,r),this.x=Kp(t[0],e[0],n[0],i[0]),this.y=Kp(t[1],e[1],n[1],i[1]);var o=Qp(t[0],e[0],n[0],i[0]),a=Qp(t[1],e[1],n[1],i[1]);this.width=o-this.x,this.height=a-this.y}}}(),calculateTransform:function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=rt();return lt(r,r,[-e.x,-e.y]),ut(r,r,[n,i]),lt(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof Xt||(t=Xt.create(t));var e=this,n=e.x,i=e.x+e.width,r=e.y,o=e.y+e.height,a=t.x,s=t.x+t.width,l=t.y,h=t.y+t.height;return!(i<a||s<n||o<l||h<r)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new Xt(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},Xt.create=function(t){return new Xt(t.x,t.y,t.width,t.height)};var Jp=function(t){t=t||{},qp.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};Jp.prototype={constructor:Jp,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof Jp&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,i=this._children,r=l(i,t);return r<0?this:(i.splice(r,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof Jp&&t.delChildrenFromStorage(n)),e&&e.refresh(),this)},removeAll:function(){var t,e,n=this._children,i=this.__storage;for(e=0;e<n.length;e++)t=n[e],i&&(i.delFromStorage(t),t instanceof Jp&&t.delChildrenFromStorage(i)),t.parent=null;return n.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof Jp&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof Jp&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new Xt(0,0,0,0),i=t||this._children,r=[],o=0;o<i.length;o++){var a=i[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(r);l?(n.copy(s),n.applyTransform(l),(e=e||n.clone()).union(n)):(e=e||s.clone()).union(s)}}return e||n}},h(Jp,qp);var tg=32,eg=7,ng=function(){this._roots=[],this._displayList=[],this._displayListLen=0};ng.prototype={constructor:ng,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,Kf.canvasSupported&&te(n,ee)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.clipPath}if(t.isGroup){for(var a=t._children,s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof Jp&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(n=0;n<this._roots.length;n++){var e=this._roots[n];e instanceof Jp&&e.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var n=0,i=t.length;n<i;n++)this.delRoot(t[n]);else{var r=l(this._roots,t);r>=0&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof Jp&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:ee};var ig={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},rg=function(t,e,n){return ig.hasOwnProperty(e)?n*=t.dpr:n},og=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],ag=function(t,e){this.extendFrom(t,!1),this.host=e};ag.prototype={constructor:ag,host:null,fill:"#000",stroke:null,opacity:1,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){for(var i=this,r=n&&n.style,o=!r,a=0;a<og.length;a++){var s=og[a],l=s[0];(o||i[l]!==r[l])&&(t[l]=rg(t,l,i[l]||s[1]))}if((o||i.fill!==r.fill)&&(t.fillStyle=i.fill),(o||i.stroke!==r.stroke)&&(t.strokeStyle=i.stroke),(o||i.opacity!==r.opacity)&&(t.globalAlpha=null==i.opacity?1:i.opacity),(o||i.blend!==r.blend)&&(t.globalCompositeOperation=i.blend||"source-over"),this.hasStroke()){var h=i.lineWidth;t.lineWidth=h/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||!0!==e&&(!1===e?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var i=("radial"===e.type?ie:ne)(t,e,n),r=e.colorStops,o=0;o<r.length;o++)i.addColorStop(r[o].offset,r[o].color);return i}};for(var sg=ag.prototype,lg=0;lg<og.length;lg++){var hg=og[lg];hg[0]in sg||(sg[hg[0]]=hg[1])}ag.getGradient=sg.getGradient;var ug=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};ug.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")};var cg=function(t,e,n){var i;n=n||Up,"string"==typeof t?i=oe(t,e,n):w(t)&&(t=(i=t).id),this.id=t,this.dom=i;var r=i.style;r&&(i.onselectstart=re,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n};cg.prototype={constructor:cg,__dirty:!0,__used:!1,__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=oe("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!=t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!=n&&this.ctxBack.scale(n,n))},clear:function(t,e){var n=this.dom,i=this.ctx,r=n.width,o=n.height,e=e||this.clearColor,a=this.motionBlur&&!t,s=this.lastFrameAlpha,l=this.dpr;if(a&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(n,0,0,r/l,o/l)),i.clearRect(0,0,r,o),e&&"transparent"!==e){var h;e.colorStops?(h=e.__canvasGradient||ag.getGradient(i,e,{x:0,y:0,width:r,height:o}),e.__canvasGradient=h):e.image&&(h=ug.prototype.getCanvasPattern.call(e,i)),i.save(),i.fillStyle=h||e,i.fillRect(0,0,r,o),i.restore()}if(a){var u=this.domBack;i.save(),i.globalAlpha=s,i.drawImage(u,0,0,r,o),i.restore()}}};var dg="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},fg=new zp(50),pg={},gg=0,mg=5e3,vg=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,yg="12px sans-serif",xg={};xg.measureText=function(t,e){var n=s();return n.font=e||yg,n.measureText(t)};var _g={left:1,right:1,center:1},wg={top:1,bottom:1,middle:1},bg=new Xt,Sg=function(){};Sg.prototype={constructor:Sg,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&De(n);var i=n.text;if(null!=i&&(i+=""),Ue(i,n)){t.save();var r=this.transform;n.transformText?this.setTransform(t):r&&(bg.copy(e),bg.applyTransform(r),e=bg),ke(this,t,i,n,e),t.restore()}}},Xe.prototype={constructor:Xe,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,inplace:!1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},dirty:function(){this.__dirty=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?qp.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new ag(t,this),this.dirty(!1),this}},h(Xe,qp),u(Xe,Sg),je.prototype={constructor:je,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r=this._image=se(i,this._image,this,this.onload);if(r&&he(r)){var o=n.x||0,a=n.y||0,s=n.width,l=n.height,h=r.width/r.height;if(null==s&&null!=l?s=l*h:null==l&&null!=s?l=s/h:null==s&&null==l&&(s=r.width,l=r.height),this.setTransform(t),n.sWidth&&n.sHeight){var u=n.sx||0,c=n.sy||0;t.drawImage(r,u,c,n.sWidth,n.sHeight,o,a,s,l)}else if(n.sx&&n.sy){var d=s-(u=n.sx),f=l-(c=n.sy);t.drawImage(r,u,c,d,f,o,a,s,l)}else t.drawImage(r,o,a,s,l);null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new Xt(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},h(je,Xe);var Mg=new Xt(0,0,0,0),Ig=new Xt(0,0,0,0),Tg=function(t,e,n){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=o({},n||{}),this.dpr=n.devicePixelRatio||Up,this._singleCanvas=i,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],s=this._layers={};if(this._layerConfig={},this._needsManuallyCompositing=!1,i){var l=t.width,h=t.height;null!=n.width&&(l=n.width),null!=n.height&&(h=n.height),this.dpr=n.devicePixelRatio||1,t.width=l*this.dpr,t.height=h*this.dpr,this._width=l,this._height=h;var u=new cg(t,this,this.dpr);u.__builtin__=!0,u.initContext(),s[314159]=u,a.push(314159),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var c=this._domRoot=Je(this._width,this._height);t.appendChild(c)}this._hoverlayer=null,this._hoverElements=[]};Tg.prototype={constructor:Tg,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<n.length;i++){var r=n[i],o=this._layers[r];if(!o.__builtin__&&o.refresh){var a=0===i?this._backgroundColor:null;o.refresh(a)}}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape});n.__from=t,t.__hoverMir=n,n.setStyle(e),this._hoverElements.push(n)}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=l(n,e);i>=0&&n.splice(i,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,n=0;n<e.length;n++){var i=e[n].__from;i&&(i.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){te(t,this.storage.displayableSortFunc),n||(n=this._hoverlayer=this.getLayer(1e5));var i={};n.ctx.save();for(var r=0;r<e;){var o=t[r],a=o.__from;a&&a.__zr?(r++,a.invisible||(o.transform=a.transform,o.invTransform=a.invTransform,o.__clipPaths=a.__clipPaths,this._doPaintEl(o,n,!0,i))):(t.splice(r,1),a.__hoverMir=null,e--)}n.ctx.restore()}},getHoverLayer:function(){return this.getLayer(1e5)},_paintList:function(t,e,n){if(this._redrawId===n){e=e||!1,this._updateLayerStatus(t);var i=this._doPaintList(t,e);if(this._needsManuallyCompositing&&this._compositeManually(),!i){var r=this;dg(function(){r._paintList(t,e,n)})}}},_compositeManually:function(){var t=this.getLayer(314159).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer(function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)})},_doPaintList:function(t,e){for(var n=[],i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];(s=this._layers[r]).__builtin__&&s!==this._hoverlayer&&(s.__dirty||e)&&n.push(s)}for(var o=!0,a=0;a<n.length;a++){var s=n[a],l=s.ctx,h={};l.save();var u=e?s.__startIndex:s.__drawIndex,c=!e&&s.incremental&&Date.now,f=c&&Date.now(),p=s.zlevel===this._zlevelList[0]?this._backgroundColor:null;if(s.__startIndex===s.__endIndex)s.clear(!1,p);else if(u===s.__startIndex){var g=t[u];g.incremental&&g.notClear&&!e||s.clear(!1,p)}-1===u&&(console.error("For some unknown reason. drawIndex is -1"),u=s.__startIndex);for(var m=u;m<s.__endIndex;m++){var v=t[m];if(this._doPaintEl(v,s,e,h),v.__dirty=!1,c&&Date.now()-f>15)break}s.__drawIndex=m,s.__drawIndex<s.__endIndex&&(o=!1),h.prevElClipPaths&&l.restore(),l.restore()}return Kf.wxa&&d(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,n,i){var r=e.ctx,o=t.transform;if((e.__dirty||n)&&!t.invisible&&0!==t.style.opacity&&(!o||o[0]||o[3])&&(!t.culling||!$e(t,this._width,this._height))){var a=t.__clipPaths;i.prevElClipPaths&&!Ke(a,i.prevElClipPaths)||(i.prevElClipPaths&&(e.ctx.restore(),i.prevElClipPaths=null,i.prevEl=null),a&&(r.save(),Qe(a,r),i.prevElClipPaths=a)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,i.prevEl||null),i.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=314159);var n=this._layers[t];return n||((n=new cg("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]&&i(n,this._layerConfig[t],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},insertLayer:function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=null,a=-1,s=this._domRoot;if(n[t])jp("ZLevel "+t+" has been used already");else if(qe(e)){if(r>0&&t>i[0]){for(a=0;a<r-1&&!(i[a]<t&&i[a+1]>t);a++);o=n[i[a]]}if(i.splice(a+1,0,t),n[t]=e,!e.virtual)if(o){var l=o.dom;l.nextSibling?s.insertBefore(e.dom,l.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom)}else jp("Layer of zlevel "+t+" is not valid")},eachLayer:function(t,e){var n,i,r=this._zlevelList;for(i=0;i<r.length;i++)n=r[i],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){var n,i,r,o=this._zlevelList;for(r=0;r<o.length;r++)i=o[r],(n=this._layers[i]).__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){var n,i,r,o=this._zlevelList;for(r=0;r<o.length;r++)i=o[r],(n=this._layers[i]).__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){n&&(n.__endIndex!==t&&(n.__dirty=!0),n.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(r=1;r<t.length;r++)if((a=t[r]).zlevel!==t[r-1].zlevel||a.incremental){this._needsManuallyCompositing=!0;break}for(var n=null,i=0,r=0;r<t.length;r++){var o,a=t[r],s=a.zlevel;a.incremental?((o=this.getLayer(s+.001,this._needsManuallyCompositing)).incremental=!0,i=1):o=this.getLayer(s+(i>0?.01:0),this._needsManuallyCompositing),o.__builtin__||jp("ZLevel "+s+" has been used by unkown layer "+o.id),o!==n&&(o.__used=!0,o.__startIndex!==r&&(o.__dirty=!0),o.__startIndex=r,o.incremental?o.__drawIndex=-1:o.__drawIndex=r,e(r),n=o),a.__dirty&&(o.__dirty=!0,o.incremental&&o.__drawIndex<0&&(o.__drawIndex=r))}e(r),this.eachBuiltinLayer(function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?i(n[t],e,!0):n[t]=e;for(var r=0;r<this._zlevelList.length;r++){var o=this._zlevelList[r];o!==t&&o!==t+.01||i(this._layers[o],n[t],!0)}}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(l(n,t),1))},resize:function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!=t||e!=this._height){n.style.width=t+"px",n.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);d(this._progressiveLayers,function(n){n.resize(t,e)}),this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(314159).resize(t,e)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new cg("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var o={},a=this.storage.getDisplayList(!0),s=0;s<a.length;s++){var l=a[s];this._doPaintEl(l,e,!0,o)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[i]||Ye(s[n])||Ye(a.style[n]))-(Ye(s[r])||0)-(Ye(s[o])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),r=t.getBoundingRect(),o=t.style,a=o.shadowBlur*e,s=o.shadowOffsetX*e,l=o.shadowOffsetY*e,h=o.hasStroke()?o.lineWidth:0,u=Math.max(h/2,-s+a),c=Math.max(h/2,s+a),d=Math.max(h/2,-l+a),f=Math.max(h/2,l+a),p=r.width+u+c,g=r.height+d+f;n.width=p*e,n.height=g*e,i.scale(e,e),i.clearRect(0,0,p,g),i.dpr=e;var m={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);var v=new je({style:{x:0,y:0,image:n}});return null!=m.position&&(v.position=t.position=m.position),null!=m.rotation&&(v.rotation=t.rotation=m.rotation),null!=m.scale&&(v.scale=t.scale=m.scale),v}};var Cg="undefined"!=typeof window&&!!window.addEventListener,Dg=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ag=Cg?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0},kg=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,xp.call(this)};kg.prototype={constructor:kg,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=l(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],o=[],a=0;a<i;a++){var s=n[a],l=s.step(t,e);l&&(r.push(l),o.push(s))}for(a=0;a<i;)n[a]._needsRemove?(n[a]=n[i-1],n.pop(),i--):a++;i=r.length;for(a=0;a<i;a++)o[a].fire(r[a]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(dg(t),!e._paused&&e._update())}var e=this;this._running=!0,dg(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){var n=new Wp(t,(e=e||{}).loop,e.getter,e.setter);return this.addAnimator(n),n}},u(kg,xp);var Pg=function(){this._track=[]};Pg.prototype={constructor:Pg,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=en(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in Lg)if(Lg.hasOwnProperty(e)){var n=Lg[e](this._track,t);if(n)return n}}};var Lg={pinch:function(t,e){var n=t.length;if(n){var i=(t[n-1]||{}).points,r=(t[n-2]||{}).points||i;if(r&&r.length>1&&i&&i.length>1){var o=ln(i)/ln(r);!isFinite(o)&&(o=1),e.pinchScale=o;var a=hn(i);return e.pinchX=a[0],e.pinchY=a[1],{type:"pinch",target:t[0].target,event:e}}}}},Og=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],zg=["touchstart","touchend","touchmove"],Eg={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},Ng=f(Og,function(t){var e=t.replace("mouse","pointer");return Eg[e]?e:t}),Rg={mousemove:function(t){t=rn(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){var e=(t=rn(this.dom,t)).toElement||t.relatedTarget;if(e!=this.dom)for(;e&&9!=e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger("mouseout",t)},touchstart:function(t){(t=rn(this.dom,t)).zrByTouch=!0,this._lastTouchMoment=new Date,cn(this,t,"start"),Rg.mousemove.call(this,t),Rg.mousedown.call(this,t),dn(this)},touchmove:function(t){(t=rn(this.dom,t)).zrByTouch=!0,cn(this,t,"change"),Rg.mousemove.call(this,t),dn(this)},touchend:function(t){(t=rn(this.dom,t)).zrByTouch=!0,cn(this,t,"end"),Rg.mouseup.call(this,t),+new Date-this._lastTouchMoment<300&&Rg.click.call(this,t),dn(this)},pointerdown:function(t){Rg.mousedown.call(this,t)},pointermove:function(t){fn(t)||Rg.mousemove.call(this,t)},pointerup:function(t){Rg.mouseup.call(this,t)},pointerout:function(t){fn(t)||Rg.mouseout.call(this,t)}};d(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){Rg[t]=function(e){e=rn(this.dom,e),this.trigger(t,e)}});var Bg=gn.prototype;Bg.dispose=function(){for(var t=Og.concat(zg),e=0;e<t.length;e++){var n=t[e];an(this.dom,un(n),this._handlers[n])}},Bg.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},u(gn,xp);var Vg=!Kf.canvasSupported,Fg={canvas:Tg},Hg={},Gg=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,r=new ng,o=n.renderer;if(Vg){if(!Fg.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");o="vml"}else o&&Fg[o]||(o="canvas");var a=new Fg[o](e,r,n,t);this.storage=r,this.painter=a;var s=Kf.node||Kf.worker?null:new gn(a.getViewportRoot());this.handler=new bp(r,a,s,a.root),this.animation=new kg({stage:{update:m(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,h=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(i)},r.addToStorage=function(t){h.call(r,t),t.addSelfToZr(i)}};Gg.prototype={constructor:Gg,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){this.painter.addHover&&(this.painter.addHover(t,e),this.refreshHover())},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,yn(this.id)}};var Wg=(Object.freeze||Object)({version:"4.0.3",init:mn,dispose:function(t){if(t)t.dispose();else{for(var e in Hg)Hg.hasOwnProperty(e)&&Hg[e].dispose();Hg={}}return this},getInstance:function(t){return Hg[t]},registerPainter:vn}),Zg=d,Ug=w,Xg=y,jg="series\0",Yg=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],qg=0,$g=".",Kg="___EC__COMPONENT__CONTAINER___",Qg=0,Jg=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,n,i){for(var r={},o=0;o<t.length;o++){var a=t[o][1];if(!(n&&l(n,a)>=0||i&&l(i,a)<0)){var s=e.getShallow(a);null!=s&&(r[t[o][0]]=s)}}return r}},tm=Jg([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),em={getLineStyle:function(t){var e=tm(this,t),n=this.getLineDash(e.lineWidth);return n&&(e.lineDash=n),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"===e||null==e?null:"dashed"===e?[i,i]:[n,n]}},nm=Jg([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),im={getAreaStyle:function(t,e){return nm(this,t,e)}},rm=Math.pow,om=Math.sqrt,am=1e-8,sm=1e-4,lm=om(3),hm=1/3,um=B(),cm=B(),dm=B(),fm=Math.min,pm=Math.max,gm=Math.sin,mm=Math.cos,vm=2*Math.PI,ym=B(),xm=B(),_m=B(),wm=[],bm=[],Sm={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Mm=[],Im=[],Tm=[],Cm=[],Dm=Math.min,Am=Math.max,km=Math.cos,Pm=Math.sin,Lm=Math.sqrt,Om=Math.abs,zm="undefined"!=typeof Float32Array,Em=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};Em.prototype={constructor:Em,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=Om(1/Up/t)||0,this._uy=Om(1/Up/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(Sm.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=Om(t-this._xi)>this._ux||Om(e-this._yi)>this._uy||this._len<5;return this.addData(Sm.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,o){return this.addData(Sm.C,t,e,n,i,r,o),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,o):this._ctx.bezierCurveTo(t,e,n,i,r,o)),this._xi=r,this._yi=o,this},quadraticCurveTo:function(t,e,n,i){return this.addData(Sm.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,o){return this.addData(Sm.A,t,e,n,n,i,r-i,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=km(r)*n+t,this._yi=Pm(r)*n+t,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Sm.R,t,e,n,i),this},closePath:function(){this.addData(Sm.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length==e||!zm||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();zm&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,o=this._dashOffset,a=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,d=Lm(u*u+c*c),f=l,p=h,g=a.length;for(u/=d,c/=d,o<0&&(o=r+o),f-=(o%=r)*u,p-=o*c;u>0&&f<=t||u<0&&f>=t||0==u&&(c>0&&p<=e||c<0&&p>=e);)f+=u*(n=a[i=this._dashIdx]),p+=c*n,this._dashIdx=(i+1)%g,u>0&&f<l||u<0&&f>l||c>0&&p<h||c<0&&p>h||s[i%2?"moveTo":"lineTo"](u>=0?Dm(f,t):Am(f,t),c>=0?Dm(p,e):Am(p,e));u=f-t,c=p-e,this._dashOffset=-Lm(u*u+c*c)},_dashedBezierTo:function(t,e,n,i,r,o){var a,s,l,h,u,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,m=this._yi,v=Gn,y=0,x=this._dashIdx,_=f.length,w=0;for(d<0&&(d=c+d),d%=c,a=0;a<1;a+=.1)s=v(g,t,n,r,a+.1)-v(g,t,n,r,a),l=v(m,e,i,o,a+.1)-v(m,e,i,o,a),y+=Lm(s*s+l*l);for(;x<_&&!((w+=f[x])>d);x++);for(a=(w-d)/y;a<=1;)h=v(g,t,n,r,a),u=v(m,e,i,o,a),x%2?p.moveTo(h,u):p.lineTo(h,u),a+=f[x]/y,x=(x+1)%_;x%2!=0&&p.lineTo(r,o),s=r-h,l=o-u,this._dashOffset=-Lm(s*s+l*l)},_dashedQuadraticTo:function(t,e,n,i){var r=n,o=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,o)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,zm&&(this.data=new Float32Array(t)))},getBoundingRect:function(){Mm[0]=Mm[1]=Tm[0]=Tm[1]=Number.MAX_VALUE,Im[0]=Im[1]=Cm[0]=Cm[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,n=0,i=0,r=0,o=0;o<t.length;){var a=t[o++];switch(1==o&&(i=e=t[o],r=n=t[o+1]),a){case Sm.M:e=i=t[o++],n=r=t[o++],Tm[0]=i,Tm[1]=r,Cm[0]=i,Cm[1]=r;break;case Sm.L:ei(e,n,t[o],t[o+1],Tm,Cm),e=t[o++],n=t[o++];break;case Sm.C:ni(e,n,t[o++],t[o++],t[o++],t[o++],t[o],t[o+1],Tm,Cm),e=t[o++],n=t[o++];break;case Sm.Q:ii(e,n,t[o++],t[o++],t[o],t[o+1],Tm,Cm),e=t[o++],n=t[o++];break;case Sm.A:var s=t[o++],l=t[o++],h=t[o++],u=t[o++],c=t[o++],d=t[o++]+c,f=(t[o++],1-t[o++]);1==o&&(i=km(c)*h+s,r=Pm(c)*u+l),ri(s,l,h,u,c,d,f,Tm,Cm),e=km(d)*h+s,n=Pm(d)*u+l;break;case Sm.R:ei(i=e=t[o++],r=n=t[o++],i+t[o++],r+t[o++],Tm,Cm);break;case Sm.Z:e=i,n=r}K(Mm,Mm,Tm),Q(Im,Im,Cm)}return 0===o&&(Mm[0]=Mm[1]=Im[0]=Im[1]=0),new Xt(Mm[0],Mm[1],Im[0]-Mm[0],Im[1]-Mm[1])},rebuildPath:function(t){for(var e,n,i,r,o,a,s=this.data,l=this._ux,h=this._uy,u=this._len,c=0;c<u;){var d=s[c++];switch(1==c&&(e=i=s[c],n=r=s[c+1]),d){case Sm.M:e=i=s[c++],n=r=s[c++],t.moveTo(i,r);break;case Sm.L:o=s[c++],a=s[c++],(Om(o-i)>l||Om(a-r)>h||c===u-1)&&(t.lineTo(o,a),i=o,r=a);break;case Sm.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case Sm.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case Sm.A:var f=s[c++],p=s[c++],g=s[c++],m=s[c++],v=s[c++],y=s[c++],x=s[c++],_=s[c++],w=g>m?g:m,b=g>m?1:g/m,S=g>m?m/g:1,M=v+y;Math.abs(g-m)>.001?(t.translate(f,p),t.rotate(x),t.scale(b,S),t.arc(0,0,w,v,M,1-_),t.scale(1/b,1/S),t.rotate(-x),t.translate(-f,-p)):t.arc(f,p,w,v,M,1-_),1==c&&(e=km(v)*g+f,n=Pm(v)*m+p),i=km(M)*g+f,r=Pm(M)*m+p;break;case Sm.R:e=i=s[c],n=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case Sm.Z:t.closePath(),i=e,r=n}}}},Em.CMD=Sm;var Nm=2*Math.PI,Rm=2*Math.PI,Bm=Em.CMD,Vm=2*Math.PI,Fm=1e-4,Hm=[-1,-1,-1],Gm=[-1,-1],Wm=ug.prototype.getCanvasPattern,Zm=Math.abs,Um=new Em(!0);xi.prototype={constructor:xi,type:"path",__dirtyPath:!0,strokeContainThreshold:5,brush:function(t,e){var n=this.style,i=this.path||Um,r=n.hasStroke(),o=n.hasFill(),a=n.fill,s=n.stroke,l=o&&!!a.colorStops,h=r&&!!s.colorStops,u=o&&!!a.image,c=r&&!!s.image;if(n.bind(t,this,e),this.setTransform(t),this.__dirty){var d;l&&(d=d||this.getBoundingRect(),this._fillGradient=n.getGradient(t,a,d)),h&&(d=d||this.getBoundingRect(),this._strokeGradient=n.getGradient(t,s,d))}l?t.fillStyle=this._fillGradient:u&&(t.fillStyle=Wm.call(a,t)),h?t.strokeStyle=this._strokeGradient:c&&(t.strokeStyle=Wm.call(s,t));var f=n.lineDash,p=n.lineDashOffset,g=!!t.setLineDash,m=this.getGlobalScale();i.setScale(m[0],m[1]),this.__dirtyPath||f&&!g&&r?(i.beginPath(t),f&&!g&&(i.setLineDash(f),i.setLineDashOffset(p)),this.buildPath(i,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),o&&i.fill(t),f&&g&&(t.setLineDash(f),t.lineDashOffset=p),r&&i.stroke(t),f&&g&&t.setLineDash([]),null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(t,e,n){},createPathProxy:function(){this.path=new Em},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var i=this.path;i||(i=this.path=new Em),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){r.copy(t);var o=e.lineWidth,a=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(o=Math.max(o,this.strokeContainThreshold||4)),a>1e-10&&(r.width+=o/a,r.height+=o/a,r.x-=o/a/2,r.y-=o/a/2)}return r}return t},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path.data;if(r.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(r.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),yi(o,a/s,t,e)))return!0}if(r.hasFill())return vi(o,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Xe.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(w(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&Zm(t[0]-1)>1e-10&&Zm(t[3]-1)>1e-10?Math.sqrt(Zm(t[0]*t[3]-t[2]*t[1])):1}},xi.extend=function(t){var e=function(e){xi.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var i=this.shape;for(var r in n)!i.hasOwnProperty(r)&&n.hasOwnProperty(r)&&(i[r]=n[r])}t.init&&t.init.call(this,e)};h(e,xi);for(var n in t)"style"!==n&&"shape"!==n&&(e.prototype[n]=t[n]);return e},h(xi,Xe);var Xm=Em.CMD,jm=[[],[],[]],Ym=Math.sqrt,qm=Math.atan2,$m=function(t,e){var n,i,r,o,a,s,l=t.data,h=Xm.M,u=Xm.C,c=Xm.L,d=Xm.R,f=Xm.A,p=Xm.Q;for(r=0,o=0;r<l.length;){switch(n=l[r++],o=r,i=0,n){case h:case c:i=1;break;case u:i=3;break;case p:i=2;break;case f:var g=e[4],m=e[5],v=Ym(e[0]*e[0]+e[1]*e[1]),y=Ym(e[2]*e[2]+e[3]*e[3]),x=qm(-e[1]/y,e[0]/v);l[r]*=v,l[r++]+=g,l[r]*=y,l[r++]+=m,l[r++]*=v,l[r++]*=y,l[r++]+=x,l[r++]+=x,o=r+=2;break;case d:s[0]=l[r++],s[1]=l[r++],$(s,s,e),l[o++]=s[0],l[o++]=s[1],s[0]+=l[r++],s[1]+=l[r++],$(s,s,e),l[o++]=s[0],l[o++]=s[1]}for(a=0;a<i;a++)(s=jm[a])[0]=l[r++],s[1]=l[r++],$(s,s,e),l[o++]=s[0],l[o++]=s[1]}},Km=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],Qm=Math.sqrt,Jm=Math.sin,tv=Math.cos,ev=Math.PI,nv=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},iv=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(nv(t)*nv(e))},rv=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(iv(t,e))},ov=function(t){Xe.call(this,t)};ov.prototype={constructor:ov,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&De(n),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;null!=i&&(i+=""),n.bind(t,this,e),Ue(i,n)&&(this.setTransform(t),ke(this,t,i,n),this.restoreTransform(t))},getBoundingRect:function(){var t=this.style;if(this.__dirty&&De(t),!this._rect){var e=t.text;null!=e?e+="":e="";var n=ce(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.rich);if(n.x+=t.x||0,n.y+=t.y||0,He(t.textStroke,t.textStrokeWidth)){var i=t.textStrokeWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect}},h(ov,Xe);var av=xi.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),sv=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],lv=function(t){return Kf.browser.ie&&Kf.browser.version>=11?function(){var e,n=this.__clipPaths,i=this.style;if(n)for(var r=0;r<n.length;r++){var o=n[r],a=o&&o.shape,s=o&&o.type;if(a&&("sector"===s&&a.startAngle===a.endAngle||"rect"===s&&(!a.width||!a.height))){for(l=0;l<sv.length;l++)sv[l][2]=i[sv[l][0]],i[sv[l][0]]=sv[l][1];e=!0;break}}if(t.apply(this,arguments),e)for(var l=0;l<sv.length;l++)i[sv[l][0]]=sv[l][2]}:t},hv=xi.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:lv(xi.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(a),u=Math.sin(a);t.moveTo(h*r+n,u*r+i),t.lineTo(h*o+n,u*o+i),t.arc(n,i,o,a,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,a,l),t.closePath()}}),uv=xi.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}}),cv=function(t,e){for(var n=t.length,i=[],r=0,o=1;o<n;o++)r+=Y(t[o-1],t[o]);var a=r/2;a=a<n?n:a;for(o=0;o<a;o++){var s,l,h,u=o/(a-1)*(e?n:n-1),c=Math.floor(u),d=u-c,f=t[c%n];e?(s=t[(c-1+n)%n],l=t[(c+1)%n],h=t[(c+2)%n]):(s=t[0===c?c:c-1],l=t[c>n-2?n-1:c+1],h=t[c>n-3?n-1:c+2]);var p=d*d,g=d*p;i.push([Ii(s[0],f[0],l[0],h[0],d,p,g),Ii(s[1],f[1],l[1],h[1],d,p,g)])}return i},dv=function(t,e,n,i){var r,o,a,s,l=[],h=[],u=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;d<f;d++)K(a,a,t[d]),Q(s,s,t[d]);K(a,a,i[0]),Q(s,s,i[1])}for(var d=0,f=t.length;d<f;d++){var p=t[d];if(n)r=t[d?d-1:f-1],o=t[(d+1)%f];else{if(0===d||d===f-1){l.push(F(t[d]));continue}r=t[d-1],o=t[d+1]}W(h,o,r),X(h,h,e);var g=Y(p,r),m=Y(p,o),v=g+m;0!==v&&(g/=v,m/=v),X(u,h,-g),X(c,h,m);var y=H([],p,u),x=H([],p,c);i&&(Q(y,y,a),K(y,y,s),Q(x,x,a),K(x,x,s)),l.push(y),l.push(x)}return n&&l.push(l.shift()),l},fv=xi.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){Ti(t,e,!0)}}),pv=xi.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){Ti(t,e,!1)}}),gv=xi.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width,o=e.height;e.r?Ce(t,e):t.rect(n,i,r,o),t.closePath()}}),mv=xi.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.percent;0!==a&&(t.moveTo(n,i),a<1&&(r=n*(1-a)+r*a,o=i*(1-a)+o*a),t.lineTo(r,o))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),vv=[],yv=xi.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,h=e.cpy2,u=e.percent;0!==u&&(t.moveTo(n,i),null==l||null==h?(u<1&&(Qn(n,a,r,u,vv),a=vv[1],r=vv[2],Qn(i,s,o,u,vv),s=vv[1],o=vv[2]),t.quadraticCurveTo(a,s,r,o)):(u<1&&(Xn(n,a,l,r,u,vv),a=vv[1],l=vv[2],r=vv[3],Xn(i,s,h,o,u,vv),s=vv[1],h=vv[2],o=vv[3]),t.bezierCurveTo(a,s,l,h,r,o)))},pointAt:function(t){return Ci(this.shape,t,!1)},tangentAt:function(t){var e=Ci(this.shape,t,!0);return j(e,e)}}),xv=xi.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),h=Math.sin(o);t.moveTo(l*r+n,h*r+i),t.arc(n,i,r,o,a,!s)}}),_v=xi.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),xi.prototype.getBoundingRect.call(this)}}),wv=function(t){this.colorStops=t||[]};wv.prototype={constructor:wv,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};var bv=function(t,e,n,i,r,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=o||!1,wv.call(this,r)};bv.prototype={constructor:bv},h(bv,wv);var Sv=function(t,e,n,i,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=r||!1,wv.call(this,i)};Sv.prototype={constructor:Sv},h(Sv,wv),Di.prototype.incremental=!0,Di.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},Di.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},Di.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},Di.prototype.eachPendingDisplayable=function(t){for(e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(var e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},Di.prototype.update=function(){this.updateTransform();for(t=this._cursor;t<this._displayables.length;t++)(e=this._displayables[t]).parent=this,e.update(),e.parent=null;for(var t=0;t<this._temporaryDisplayables.length;t++){var e=this._temporaryDisplayables[t];e.parent=this,e.update(),e.parent=null}},Di.prototype.brush=function(t,e){for(n=this._cursor;n<this._displayables.length;n++)(i=this._temporaryDisplayables[n]).beforeBrush&&i.beforeBrush(t),i.brush(t,n===this._cursor?null:this._displayables[n-1]),i.afterBrush&&i.afterBrush(t);this._cursor=n;for(var n=0;n<this._temporaryDisplayables.length;n++){var i=this._temporaryDisplayables[n];i.beforeBrush&&i.beforeBrush(t),i.brush(t,0===n?null:this._temporaryDisplayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var Mv=[];Di.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Xt(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(Mv)),t.union(i)}this._rect=t}return this._rect},Di.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++)if(this._displayables[i].contain(t,e))return!0;return!1},h(Di,Xe);var Iv=Math.round,Tv=Math.max,Cv=Math.min,Dv={},Av=(Object.freeze||Object)({extendShape:Ai,extendPath:function(t,e){return Mi(t,e)},makePath:ki,makeImage:Pi,mergePath:function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),n.push(o.path)}var a=new xi(e);return a.createPathProxy(),a.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},a},resizePath:Oi,subPixelOptimizeLine:zi,subPixelOptimizeRect:Ei,subPixelOptimize:Ni,setHoverStyle:qi,setLabelStyle:$i,setTextStyle:Ki,setText:function(t,e,n){var i,r={isRectText:!0};!1===n?i=!0:r.autoColor=n,Qi(t,e,r,i),t.host&&t.host.dirty&&t.host.dirty(!1)},getFont:rr,updateProps:ar,initProps:sr,getTransform:lr,applyTransform:hr,transformDirection:ur,groupTransition:cr,clipPointsByRect:dr,clipRectByRect:function(t,e){var n=Tv(t.x,e.x),i=Cv(t.x+t.width,e.x+e.width),r=Tv(t.y,e.y),o=Cv(t.y+t.height,e.y+e.height);if(i>=n&&o>=r)return{x:n,y:r,width:i-n,height:o-r}},createIcon:fr,Group:Jp,Image:je,Text:ov,Circle:av,Sector:hv,Ring:uv,Polygon:fv,Polyline:pv,Rect:gv,Line:mv,BezierCurve:yv,Arc:xv,IncrementalDisplayable:Di,CompoundPath:_v,LinearGradient:bv,RadialGradient:Sv,BoundingRect:Xt}),kv=["textStyle","color"],Pv={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(kv):null)},getFont:function(){return rr({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return ce(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("rich"),this.getShallow("truncateText"))}},Lv=Jg([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),Ov={getItemStyle:function(t,e){var n=Lv(this,t,e),i=this.getBorderLineDash();return i&&(n.lineDash=i),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},zv=u,Ev=Dn();pr.prototype={constructor:pr,init:null,mergeOption:function(t){i(this.option,t,!0)},get:function(t,e){return null==t?this.option:gr(this.option,this.parsePath(t),!e&&mr(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&mr(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n,i=null==t?this.option:gr(this.option,t=this.parsePath(t));return e=e||(n=mr(this,t))&&n.getModel(t),new pr(i,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){return new(0,this.constructor)(n(this.option))},setReadOnly:function(t){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){Ev(this).getParent=t},isAnimationEnabled:function(){if(!Kf.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},En(pr),Nn(pr),zv(pr,em),zv(pr,im),zv(pr,Pv),zv(pr,Ov);var Nv=0,Rv=1e-4,Bv=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/,Vv=(Object.freeze||Object)({linearMap:xr,parsePercent:_r,round:wr,asc:br,getPrecision:Sr,getPrecisionSafe:Mr,getPixelPrecision:Ir,getPercentWithPrecision:Tr,MAX_SAFE_INTEGER:9007199254740991,remRadian:Cr,isRadianAroundZero:Dr,parseDate:Ar,quantity:kr,nice:Lr,reformIntervals:function(t){function e(t,n,i){return t.interval[i]<n.interval[i]||t.interval[i]===n.interval[i]&&(t.close[i]-n.close[i]==(i?-1:1)||!i&&e(t,n,1))}t.sort(function(t,n){return e(t,n,0)?-1:1});for(var n=-1/0,i=1,r=0;r<t.length;){for(var o=t[r].interval,a=t[r].close,s=0;s<2;s++)o[s]<=n&&(o[s]=n,a[s]=s?1:1-i),n=o[s],i=a[s];o[0]===o[1]&&a[0]*a[1]!=1?t.splice(r,1):r++}return t},isNumeric:function(t){return t-parseFloat(t)>=0}}),Fv=k,Hv=["a","b","c","d","e","f","g"],Gv=function(t,e){return"{"+t+(null==e?"":e)+"}"},Wv=ve,Zv=ce,Uv=(Object.freeze||Object)({addCommas:Or,toCamelCase:zr,normalizeCssArray:Fv,encodeHTML:Er,formatTpl:Nr,formatTplSimple:function(t,e,n){return d(e,function(e,i){t=t.replace("{"+i+"}",n?Er(e):e)}),t},getTooltipMarker:Rr,formatTime:Vr,capitalFirst:Fr,truncateText:Wv,getTextRect:Zv}),Xv=d,jv=["left","right","top","bottom","width","height"],Yv=[["width","left","right"],["height","top","bottom"]],qv=Hr,$v=(v(Hr,"vertical"),v(Hr,"horizontal"),{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}),Kv=Dn(),Qv=pr.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){pr.call(this,t,e,n,i),this.uid=vr("ec_cpt_model")},init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,r=n?Ur(t):{};i(t,e.getTheme().get(this.mainType)),i(t,this.getDefaultOption()),n&&Zr(t,r,n)},mergeOption:function(t,e){i(this.option,t,!0);var n=this.layoutMode;n&&Zr(this.option,t,n)},optionUpdated:function(t,e){},getDefaultOption:function(){var t=Kv(this);if(!t.defaultOption){for(var e=[],n=this.constructor;n;){var r=n.prototype.defaultOption;r&&e.push(r),n=n.superClass}for(var o={},a=e.length-1;a>=0;a--)o=i(o,e[a],!0);t.defaultOption=o}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});Vn(Qv,{registerWhenExtend:!0}),function(t){var e={};t.registerSubTypeDefaulter=function(t,n){t=On(t),e[t.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=On(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r}}(Qv),function(t,e){function n(t){var n={},o=[];return d(t,function(a){var s=i(n,a),h=r(s.originalDeps=e(a),t);s.entryCount=h.length,0===s.entryCount&&o.push(a),d(h,function(t){l(s.predecessor,t)<0&&s.predecessor.push(t);var e=i(n,t);l(e.successor,t)<0&&e.successor.push(a)})}),{graph:n,noEntryList:o}}function i(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function r(t,e){var n=[];return d(t,function(t){l(e,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,e,i,r){function o(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}if(t.length){var a=n(e),s=a.graph,l=a.noEntryList,h={};for(d(t,function(t){h[t]=!0});l.length;){var u=l.pop(),c=s[u],f=!!h[u];f&&(i.call(r,u,c.originalDeps.slice()),delete h[u]),d(c.successor,f?function(t){h[t]=!0,o(t)}:o)}d(h,function(){throw new Error("Circle dependency may exists")})}}}(Qv,function(t){var e=[];return d(Qv.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=f(e,function(t){return On(t).main}),"dataset"!==t&&l(e,"dataset")<=0&&e.unshift("dataset"),e}),u(Qv,$v);var Jv="";"undefined"!=typeof navigator&&(Jv=navigator.platform||"");var ty={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:Jv.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},ey=Dn(),ny={clearColorPalette:function(){ey(this).colorIdx=0,ey(this).colorNameMap={}},getColorFromPalette:function(t,e,n){var i=ey(e=e||this),r=i.colorIdx||0,o=i.colorNameMap=i.colorNameMap||{};if(o.hasOwnProperty(t))return o[t];var a=xn(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=n&&s?jr(s,n):a;if((l=l||a)&&l.length){var h=l[r];return t&&(o[t]=h),i.colorIdx=(r+1)%l.length,h}}},iy={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis")[0],o=t.getReferringComponents("yAxis")[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),qr(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),qr(o)&&(i.set("y",o),e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis")[0];e.coordSysDims=["single"],n.set("single",r),qr(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar")[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),qr(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),qr(a)&&(i.set("angle",a),e.firstCategoryDimIndex=1)},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,o=r.getComponent("parallel",t.get("parallelIndex")),a=e.coordSysDims=o.dimensions.slice();d(o.parallelAxisIndex,function(t,o){var s=r.getComponent("parallelAxis",t),l=a[o];n.set(l,s),qr(s)&&null==e.firstCategoryDimIndex&&(i.set(l,s),e.firstCategoryDimIndex=o)})}},ry="original",oy="arrayRows",ay="objectRows",sy="keyedColumns",ly="unknown",hy="typedArray",uy="column",cy="row";$r.seriesDataToSource=function(t){return new $r({data:t,sourceFormat:S(t)?hy:ry,fromDataset:!1})},Nn($r);var dy=Dn(),fy="\0_ec_inner",py=pr.extend({constructor:py,init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new pr(n),this._optionManager=i},setOption:function(t,e){P(!(fy in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var i=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(i)):co.call(this,i),e=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var r=n.getTimelineOption(this);r&&(this.mergeOption(r),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o.length&&d(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){var e=this.option,r=this._componentsMap,a=[];Jr(this),d(t,function(t,r){null!=t&&(Qv.hasClass(r)?r&&a.push(r):e[r]=null==e[r]?n(t):i(e[r],t,!0))}),Qv.topologicalTravel(a,Qv.getAllClassMainTypes(),function(n,i){var a=xn(t[n]),s=Sn(r.get(n),a);Mn(s),d(s,function(t,e){var i=t.option;w(i)&&(t.keyInfo.mainType=n,t.keyInfo.subType=po(n,i,t.exist))});var l=fo(r,i);e[n]=[],r.set(n,[]),d(s,function(t,i){var a=t.exist,s=t.option;if(P(w(s)||a,"Empty component definition"),s){var h=Qv.getClass(n,t.keyInfo.subType,!0);if(a&&a instanceof h)a.name=t.keyInfo.name,a.mergeOption(s,this),a.optionUpdated(s,!1);else{var u=o({dependentModels:l,componentIndex:i},t.keyInfo);o(a=new h(s,this,this,u),u),a.init(s,this,this,u),a.optionUpdated(null,!0)}}else a.mergeOption({},this),a.optionUpdated({},!1);r.get(n)[i]=a,e[n][i]=a.option},this),"series"===n&&go(this,r.get("series"))},this),this._seriesIndicesMap=N(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var t=n(this.option);return d(t,function(e,n){if(Qv.hasClass(n)){for(var i=(e=xn(e)).length-1;i>=0;i--)Tn(e[i])&&e.splice(i,1);t[n]=e}}),delete t[fy],t},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);if(n)return n[e||0]},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n=t.index,i=t.id,r=t.name,o=this._componentsMap.get(e);if(!o||!o.length)return[];var a;if(null!=n)y(n)||(n=[n]),a=g(f(n,function(t){return o[t]}),function(t){return!!t});else if(null!=i){var s=y(i);a=g(o,function(t){return s&&l(i,t.id)>=0||!s&&t.id===i})}else if(null!=r){var h=y(r);a=g(o,function(t){return h&&l(r,t.name)>=0||!h&&t.name===r})}else a=o.slice();return mo(a,t)},findComponents:function(t){var e=t.query,n=t.mainType,i=function(t){var e=n+"Index",i=n+"Id",r=n+"Name";return!t||null==t[e]&&null==t[i]&&null==t[r]?null:{mainType:n,index:t[e],id:t[i],name:t[r]}}(e);return function(e){return t.filter?g(e,t.filter):e}(mo(i?this.queryComponents(i):this._componentsMap.get(n),t))},eachComponent:function(t,e,n){var i=this._componentsMap;"function"==typeof t?(n=e,e=t,i.each(function(t,i){d(t,function(t,r){e.call(n,i,t,r)})})):_(t)?d(i.get(t),e,n):w(t)&&d(this.findComponents(t),e,n)},getSeriesByName:function(t){return g(this._componentsMap.get("series"),function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){return g(this._componentsMap.get("series"),function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(t,e){d(this._seriesIndices,function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)},this)},eachRawSeries:function(t,e){d(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,n){d(this._seriesIndices,function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)},this)},eachRawSeriesByType:function(t,e,n){return d(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){go(this,g(this._componentsMap.get("series"),t,e))},restoreData:function(t){var e=this._componentsMap;go(this,e.get("series"));var n=[];e.each(function(t,e){n.push(e)}),Qv.topologicalTravel(n,Qv.getAllClassMainTypes(),function(n,i){d(e.get(n),function(e){("series"!==n||!ho(e,t))&&e.restoreData()})})}});u(py,ny);var gy=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"],my={};yo.prototype={constructor:yo,create:function(t,e){var n=[];d(my,function(i,r){var o=i.create(t,e);n=n.concat(o||[])}),this._coordinateSystems=n},update:function(t,e){d(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},yo.register=function(t,e){my[t]=e},yo.get=function(t){return my[t]};var vy=d,yy=n,xy=f,_y=i,wy=/^(min|max)?(.+)$/;xo.prototype={constructor:xo,setOption:function(t,e){t&&d(xn(t.series),function(t){t&&t.data&&S(t.data)&&O(t.data)}),t=yy(t,!0);var n=this._optionBackup,i=_o.call(this,t,e,!n);this._newBaseOption=i.baseOption,n?(Mo(n.baseOption,i.baseOption),i.timelineOptions.length&&(n.timelineOptions=i.timelineOptions),i.mediaList.length&&(n.mediaList=i.mediaList),i.mediaDefault&&(n.mediaDefault=i.mediaDefault)):this._optionBackup=i},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=xy(e.timelineOptions,yy),this._mediaList=xy(e.mediaList,yy),this._mediaDefault=yy(e.mediaDefault),this._currentMediaIndices=[],yy(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=yy(n[i.getCurrentIndex()],!0))}return e},getMediaOption:function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],a=[];if(!i.length&&!r)return a;for(var s=0,l=i.length;s<l;s++)wo(i[s].query,e,n)&&o.push(s);return!o.length&&r&&(o=[-1]),o.length&&!So(o,this._currentMediaIndices)&&(a=xy(o,function(t){return yy(-1===t?r.option:i[t].option)})),this._currentMediaIndices=o,a}};var by=d,Sy=w,My=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"],Iy=function(t,e){by(Po(t.series),function(t){Sy(t)&&ko(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),by(n,function(e){by(Po(t[e]),function(t){t&&(Do(t,"axisLabel"),Do(t.axisPointer,"label"))})}),by(Po(t.parallel),function(t){var e=t&&t.parallelAxisDefault;Do(e,"axisLabel"),Do(e&&e.axisPointer,"label")}),by(Po(t.calendar),function(t){To(t,"itemStyle"),Do(t,"dayLabel"),Do(t,"monthLabel"),Do(t,"yearLabel")}),by(Po(t.radar),function(t){Do(t,"name")}),by(Po(t.geo),function(t){Sy(t)&&(Ao(t),by(Po(t.regions),function(t){Ao(t)}))}),by(Po(t.timeline),function(t){Ao(t),To(t,"label"),To(t,"itemStyle"),To(t,"controlStyle",!0);var e=t.data;y(e)&&d(e,function(t){w(t)&&(To(t,"label"),To(t,"itemStyle"))})}),by(Po(t.toolbox),function(t){To(t,"iconStyle"),by(t.feature,function(t){To(t,"iconStyle")})}),Do(Lo(t.axisPointer),"label"),Do(Lo(t.tooltip).axisPointer,"label")},Ty=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],Cy=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],Dy=function(t,e){Iy(t,e),t.series=xn(t.series),d(t.series,function(t){if(w(t)){var e=t.type;if("pie"!==e&&"gauge"!==e||null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===e){var n=Oo(t,"pointer.color");null!=n&&zo(t,"itemStyle.normal.color",n)}Eo(t)}}),t.dataRange&&(t.visualMap=t.dataRange),d(Cy,function(e){var n=t[e];n&&(y(n)||(n=[n]),d(n,function(t){Eo(t)}))})},Ay=Ro.prototype;Ay.pure=!1,Ay.persistent=!0,Ay.getSource=function(){return this._source};var ky={arrayRows_column:{pure:!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:Fo},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],n=this._data,i=0;i<n.length;i++){var r=n[i];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:Bo,getItem:Vo,appendData:Fo},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],n=this._source.dimensionsDefine,i=0;i<n.length;i++){var r=this._data[n[i].name];e.push(r?r[t]:null)}return e},appendData:function(t){var e=this._data;d(t,function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])})}},original:{count:Bo,getItem:Vo,appendData:Fo},typedArray:{persistent:!1,pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t){t-=this._offset;for(var e=[],n=this._dimSize*t,i=0;i<this._dimSize;i++)e[i]=this._data[n+i];return e},appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}},Py={arrayRows:Ho,objectRows:function(t,e,n,i){return null!=n?t[i]:t},keyedColumns:Ho,original:function(t,e,n,i){var r=wn(t);return null!=n&&r instanceof Array?r[n]:r},typedArray:Ho},Ly={arrayRows:Go,objectRows:function(t,e,n,i){return Wo(t[e],this._dimensionInfos[e])},keyedColumns:Go,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return!this._rawData.pure&&bn(t)&&(this.hasItemOption=!0),Wo(r instanceof Array?r[i]:r,this._dimensionInfos[e])},typedArray:function(t,e,n,i){return t[i]}},Oy=/\{@(.+?)\}/g,zy={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t,!0),a=n.getRawDataItem(t),s=n.getItemVisual(t,"color");return{componentType:this.mainType,componentSubType:this.subType,seriesType:"series"===this.mainType?this.subType:null,seriesIndex:this.seriesIndex,seriesId:this.id,seriesName:this.name,name:o,dataIndex:r,data:a,dataType:e,value:i,color:s,marker:Rr(s),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,i,r){e=e||"normal";var o=this.getData(n),a=o.getItemModel(t),s=this.getDataParams(t,n);null!=i&&s.value instanceof Array&&(s.value=s.value[i]);var l=a.get("normal"===e?[r||"label","formatter"]:[e,r||"label","formatter"]);return"function"==typeof l?(s.status=e,l(s)):"string"==typeof l?Nr(l,s).replace(Oy,function(e,n){var i=n.length;return"["===n.charAt(0)&&"]"===n.charAt(i-1)&&(n=+n.slice(1,i-1)),Zo(o,t,n)}):void 0},getRawValue:function(t,e){return Zo(this.getData(e),t)},formatTooltip:function(){}},Ey=jo.prototype;Ey.perform=function(t){var e=this._upstream,n=t&&t.skip;if(this._dirty&&e){var i=this.context;i.data=i.outputData=e.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this);var r;this._plan&&!n&&(r=this._plan(this.context));var o;(this._dirty||"reset"===r)&&(this._dirty=!1,o=Yo(this,n));var a=t&&t.step;if(this._dueEnd=e?e._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var s=this._dueIndex,l=Math.min(null!=a?this._dueIndex+a:1/0,this._dueEnd);!n&&(o||s<l)&&this._progress({start:s,end:l},this.context),this._dueIndex=l;var h=null!=this._settedOutputEnd?this._settedOutputEnd:l;this._outputDueEnd=h}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},Ey.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},Ey.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},Ey.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},Ey.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},Ey.getUpstream=function(){return this._upstream},Ey.getDownstream=function(){return this._downstream},Ey.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Ny=Dn(),Ry=Qv.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.color",layoutMode:null,init:function(t,e,n,i){this.seriesIndex=this.componentIndex,this.dataTask=Xo({count:Ko,reset:Qo}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),to(this);var r=this.getInitialData(t,n);ta(r,this),this.dataTask.context.data=r,Ny(this).dataBeforeProcessed=r,qo(this)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,r=n?Ur(t):{},o=this.subType;Qv.hasClass(o)&&(o+="Series"),i(t,e.getTheme().get(this.subType)),i(t,this.getDefaultOption()),_n(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Zr(t,r,n)},mergeOption:function(t,e){t=i(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&Zr(this.option,t,n),to(this);var r=this.getInitialData(t,e);ta(r,this),this.dataTask.dirty(),this.dataTask.context.data=r,Ny(this).dataBeforeProcessed=r,qo(this)},fillDataTextStyle:function(t){if(t)for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&_n(t[n],"label",e)},getInitialData:function(){},appendData:function(t){this.getRawData().appendData(t.data)},getData:function(t){var e=na(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return Ny(this).data},setData:function(t){var e=na(this);if(e){var n=e.context;n.data!==t&&e.isOverallFilter&&e.setOutputEnd(t.count()),n.outputData=t,e!==this.dataTask&&(n.data=t)}Ny(this).data=t},getSource:function(){return Qr(this)},getRawData:function(){return Ny(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,n){function i(t){return Er(Or(t))}var r=this.getData(),o=r.mapDimension("defaultedTooltip",!0),a=o.length,s=this.getRawValue(t),l=y(s),h=r.getItemVisual(t,"color");w(h)&&h.colorStops&&(h=(h.colorStops[0]||{}).color),h=h||"transparent";var u=a>1||l&&!a?function(n){function i(t,n){var i=r.getDimensionInfo(n);if(i&&!1!==i.otherDims.tooltip){var o=i.type,l=Rr({color:h,type:"subItem"}),u=(a?l+Er(i.displayName||"-")+": ":"")+Er("ordinal"===o?t+"":"time"===o?e?"":Vr("yyyy/MM/dd hh:mm:ss",t):Or(t));u&&s.push(u)}}var a=p(n,function(t,e,n){var i=r.getDimensionInfo(n);return t|=i&&!1!==i.tooltip&&null!=i.displayName},0),s=[];return o.length?d(o,function(e){i(Zo(r,t,e),e)}):d(n,i),(a?"<br/>":"")+s.join(a?"<br/>":", ")}(s):i(a?Zo(r,t,o[0]):l?s[0]:s),c=Rr(h),f=r.getName(t),g=this.name;return In(this)||(g=""),g=g?Er(g)+(e?": ":"<br/>"):"",e?c+g+u:g+c+(f?Er(f)+": "+u:u)},isAnimationEnabled:function(){if(Kf.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,n){var i=this.ecModel,r=ny.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});u(Ry,zy),u(Ry,ny);var By=function(){this.group=new Jp,this.uid=vr("viewComponent")};By.prototype={constructor:By,init:function(t,e){},render:function(t,e,n,i){},dispose:function(){}};var Vy=By.prototype;Vy.updateView=Vy.updateLayout=Vy.updateVisual=function(t,e,n,i){},En(By),Vn(By,{registerWhenExtend:!0});var Fy=function(){var t=Dn();return function(e){var n=t(e),i=e.pipelineContext,r=n.large,o=n.canProgressiveRender,a=n.large=i.large,s=n.canProgressiveRender=i.canProgressiveRender;return!!(r^a||o^s)&&"reset"}},Hy=Dn(),Gy=Fy();ia.prototype={type:"chart",init:function(t,e){},render:function(t,e,n,i){},highlight:function(t,e,n,i){oa(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){oa(t.getData(),i,"normal")},remove:function(t,e){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null};var Wy=ia.prototype;Wy.updateView=Wy.updateLayout=Wy.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},En(ia),Vn(ia,{registerWhenExtend:!0}),ia.markUpdateMethod=function(t,e){Hy(t).updateMethod=e};var Zy={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},Uy="\0__throttleOriginMethod",Xy="\0__throttleRate",jy="\0__throttleType",Yy={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=(t.visualColorAccessPath||"itemStyle.color").split("."),r=t.get(i)||t.getColorFromPalette(t.name,null,e.getSeriesCount());if(n.setVisual("color",r),!e.isSeriesFiltered(t)){"function"!=typeof r||r instanceof wv||n.each(function(e){n.setItemVisual(e,"color",r(t.getDataParams(e)))});return{dataEach:n.hasItemOption?function(t,e){var n=t.getItemModel(e).get(i,!0);null!=n&&t.setItemVisual(e,"color",n)}:null}}}},qy={toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}},$y=function(t,e){function n(t,e){if("string"!=typeof t)return t;var n=t;return d(e,function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),n}function i(t){var e=o.get(t);if(null==e){for(var n=t.split("."),i=qy.aria,r=0;r<n.length;++r)i=i[n[r]];return i}return e}function r(t){return qy.series.typeNames[t]||"自定义图"}var o=e.getModel("aria");if(o.get("show"))if(o.get("description"))t.setAttribute("aria-label",o.get("description"));else{var a=0;e.eachSeries(function(t,e){++a},this);var s,l=o.get("data.maxCount")||10,h=o.get("series.maxCount")||10,u=Math.min(a,h);if(!(a<1)){var c=function(){var t=e.getModel("title").option;return t&&t.length&&(t=t[0]),t&&t.text}();s=c?n(i("general.withTitle"),{title:c}):i("general.withoutTitle");var f=[];s+=n(i(a>1?"series.multiple.prefix":"series.single.prefix"),{seriesCount:a}),e.eachSeries(function(t,e){if(e<u){var o,s=t.get("name"),h="series."+(a>1?"multiple":"single")+".";o=n(o=i(s?h+"withName":h+"withoutName"),{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:r(t.subType)});var c=t.getData();window.data=c,c.count()>l?o+=n(i("data.partialData"),{displayCnt:l}):o+=i("data.allData");for(var d=[],p=0;p<c.count();p++)if(p<l){var g=c.getName(p),m=Zo(c,p);d.push(n(i(g?"data.withName":"data.withoutName"),{name:g,value:m}))}o+=d.join(i("data.separator.middle"))+i("data.separator.end"),f.push(o)}}),s+=f.join(i("series.multiple.separator.middle"))+i("series.multiple.separator.end"),t.setAttribute("aria-label",s)}}},Ky=Math.PI,Qy=ca.prototype;Qy.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context;return{step:!e&&n.progressiveEnabled&&(!i||i.canProgressiveRender)&&t.__idxInPipeline>n.bockIndex?n.step:null}}},Qy.getPipeline=function(t){return this._pipelineMap.get(t)},Qy.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold");t.pipelineContext=n.context={canProgressiveRender:r,large:o}},Qy.restorePipelines=function(t){var e=this,n=e._pipelineMap=N();t.eachSeries(function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),bockIndex:-1,step:i||700,count:0}),Sa(e,t,t.dataTask)})},Qy.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.ecInstance.getModel(),n=this.api;d([this._dataProcessorHandlers,this._visualHandlers],function(i){d(i,function(i){var r=t.get(i.uid)||t.set(i.uid,[]);i.reset&&fa(this,i,r,e,n),i.overallReset&&pa(this,i,r,e,n)},this)},this)},Qy.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,Sa(this,e,r)},Qy.performDataProcessorTasks=function(t,e){da(this,this._dataProcessorHandlers,t,e,{block:!0})},Qy.performVisualTasks=function(t,e,n){da(this,this._visualHandlers,t,e,n)},Qy.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},Qy.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.bockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var Jy=Qy.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)};ca.wrapStageHandler=function(t,e){return x(t)&&(t={overallReset:t,seriesType:Ma(t)}),t.uid=vr("stageHandler"),e&&(t.visualType=e),t};var tx,ex={},nx={};Ia(ex,py),Ia(nx,vo),ex.eachSeriesByType=ex.eachRawSeriesByType=function(t){tx=t},ex.eachComponent=function(t){"series"===t.mainType&&t.subType&&(tx=t.subType)};var ix=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],rx={color:ix,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],ix]},ox=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],ax={color:ox,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:"#eee"},crossStyle:{color:"#eee"}}},legend:{textStyle:{color:"#eee"}},textStyle:{color:"#eee"},title:{textStyle:{color:"#eee"}},toolbox:{iconStyle:{normal:{borderColor:"#eee"}}},dataZoom:{textStyle:{color:"#eee"}},visualMap:{textStyle:{color:"#eee"}},timeline:{lineStyle:{color:"#eee"},itemStyle:{normal:{color:ox[1]}},label:{normal:{textStyle:{color:"#eee"}}},controlStyle:{normal:{color:"#eee",borderColor:"#eee"}}},timeAxis:{axisLine:{lineStyle:{color:"#eee"}},axisTick:{lineStyle:{color:"#eee"}},axisLabel:{textStyle:{color:"#eee"}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:"#eee"}}},logAxis:{axisLine:{lineStyle:{color:"#eee"}},axisTick:{lineStyle:{color:"#eee"}},axisLabel:{textStyle:{color:"#eee"}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:"#eee"}}},valueAxis:{axisLine:{lineStyle:{color:"#eee"}},axisTick:{lineStyle:{color:"#eee"}},axisLabel:{textStyle:{color:"#eee"}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:"#eee"}}},categoryAxis:{axisLine:{lineStyle:{color:"#eee"}},axisTick:{lineStyle:{color:"#eee"}},axisLabel:{textStyle:{color:"#eee"}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:"#eee"}}},line:{symbol:"circle"},graph:{color:ox},gauge:{title:{textStyle:{color:"#eee"}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};ax.categoryAxis.splitLine.show=!1;var sx=P,lx=d,hx=x,ux=w,cx=Qv.parseClassType,dx={zrender:"4.0.3"},fx=1e3,px=1e3,gx=3e3,mx={PROCESSOR:{FILTER:fx,STATISTIC:5e3},VISUAL:{LAYOUT:px,GLOBAL:2e3,CHART:gx,COMPONENT:4e3,BRUSH:5e3}},vx="__flagInMainProcess",yx="__optionUpdated",xx=/^[a-zA-Z0-9_]+$/;Ca.prototype.on=Ta("on"),Ca.prototype.off=Ta("off"),Ca.prototype.one=Ta("one"),u(Ca,xp);var _x=Da.prototype;_x._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[yx]){var e=this[yx].silent;this[vx]=!0,ka(this),bx.update.call(this),this[vx]=!1,this[yx]=!1,za.call(this,e),Ea.call(this,e)}else if(t.unfinished){var n=1,i=this._model;this._api;t.unfinished=!1;do{var r=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),La(this,i),t.performVisualTasks(i),Ha(this,this._model,0,"remain"),n-=+new Date-r}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},_x.getDom=function(){return this._dom},_x.getZr=function(){return this._zr},_x.setOption=function(t,e,n){var i;if(ux(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[vx]=!0,!this._model||e){var r=new xo(this._api),o=this._theme,a=this._model=new py(null,null,o,r);a.scheduler=this._scheduler,a.init(null,null,o,r)}this._model.setOption(t,Cx),n?(this[yx]={silent:i},this[vx]=!1):(ka(this),bx.update.call(this),this._zr.flush(),this[yx]=!1,this[vx]=!1,za.call(this,i),Ea.call(this,i))},_x.setTheme=function(){console.log("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},_x.getModel=function(){return this._model},_x.getOption=function(){return this._model&&this._model.getOption()},_x.getWidth=function(){return this._zr.getWidth()},_x.getHeight=function(){return this._zr.getHeight()},_x.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},_x.getRenderedCanvas=function(t){if(Kf.canvasSupported)return(t=t||{}).pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor"),this._zr.painter.getRenderedCanvas(t)},_x.getSvgDataUrl=function(){if(Kf.svgSupported){var t=this._zr;return d(t.storage.getDisplayList(),function(t){t.stopAnimation(!0)}),t.painter.pathToDataUrl()}},_x.getDataURL=function(t){var e=(t=t||{}).excludeComponents,n=this._model,i=[],r=this;lx(e,function(t){n.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)})});var o="svg"===this._zr.painter.getType()?this.getSvgDataUrl():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return lx(i,function(t){t.group.ignore=!1}),o},_x.getConnectedDataURL=function(t){if(Kf.canvasSupported){var e=this.group,i=Math.min,r=Math.max;if(Ox[e]){var o=1/0,a=1/0,s=-1/0,l=-1/0,h=[],u=t&&t.pixelRatio||1;d(Lx,function(u,c){if(u.group===e){var d=u.getRenderedCanvas(n(t)),f=u.getDom().getBoundingClientRect();o=i(f.left,o),a=i(f.top,a),s=r(f.right,s),l=r(f.bottom,l),h.push({dom:d,left:f.left,top:f.top})}});var c=(s*=u)-(o*=u),f=(l*=u)-(a*=u),p=lp();p.width=c,p.height=f;var g=mn(p);return lx(h,function(t){var e=new je({style:{x:t.left*u-o,y:t.top*u-a,image:t.dom}});g.add(e)}),g.refreshImmediately(),p.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},_x.convertToPixel=v(Aa,"convertToPixel"),_x.convertFromPixel=v(Aa,"convertFromPixel"),_x.containPixel=function(t,e){var n;return t=An(this._model,t),d(t,function(t,i){i.indexOf("Models")>=0&&d(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n|=!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n|=o.containPoint(e,t))}},this)},this),!!n},_x.getVisual=function(t,e){var n=(t=An(this._model,t,{defaultMainType:"series"})).seriesModel.getData(),i=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?n.indexOfRawIndex(t.dataIndex):null;return null!=i?n.getItemVisual(i,e):n.getVisual(e)},_x.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},_x.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var bx={prepareAndUpdate:function(t){ka(this),bx.update.call(this,t)},update:function(t){var e=this._model,n=this._api,i=this._zr,r=this._coordSysMgr,o=this._scheduler;if(e){e.restoreData(t),o.performSeriesTasks(e),r.create(e,n),o.performDataProcessorTasks(e,t),La(this,e),r.update(e,n),Ba(e),o.performVisualTasks(e,t),Va(this,e,n,t);var a=e.get("backgroundColor")||"transparent";if(Kf.canvasSupported)i.setBackgroundColor(a);else{var s=Mt(a);a=Lt(s,"rgb"),0===s[3]&&(a="transparent")}Ga(e,n)}},updateTransform:function(t){var e=this._model,n=this,i=this._api;if(e){var r=[];e.eachComponent(function(o,a){var s=n.getViewOfComponentModel(a);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(a,e,i,t);l&&l.update&&r.push(s)}else r.push(s)});var o=N();e.eachSeries(function(r){var a=n._chartsMap[r.__viewId];if(a.updateTransform){var s=a.updateTransform(r,e,i,t);s&&s.update&&o.set(r.uid,1)}else o.set(r.uid,1)}),Ba(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0,dirtyMap:o}),Ha(n,e,0,t,o),Ga(e,this._api)}},updateView:function(t){var e=this._model;e&&(ia.markUpdateMethod(t,"updateView"),Ba(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),Va(this,this._model,this._api,t),Ga(e,this._api))},updateVisual:function(t){bx.update.call(this,t)},updateLayout:function(t){bx.update.call(this,t)}};_x.resize=function(t){this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[vx]=!0,n&&ka(this),bx.update.call(this),this[vx]=!1,za.call(this,i),Ea.call(this,i)}},_x.showLoading=function(t,e){if(ux(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Px[t]){var n=Px[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},_x.hideLoading=function(){this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},_x.makeActionFromEvent=function(t){var e=o({},t);return e.type=Ix[t.type],e},_x.dispatchAction=function(t,e){ux(e)||(e={silent:!!e}),Mx[t.type]&&this._model&&(this[vx]?this._pendingActions.push(t):(Oa.call(this,t,e.silent),e.flush?this._zr.flush(!0):!1!==e.flush&&Kf.browser.weChat&&this._throttledZrFlush(),za.call(this,e.silent),Ea.call(this,e.silent)))},_x.appendData=function(t){var e=t.seriesIndex;this.getModel().getSeriesByIndex(e).appendData(t),this._scheduler.unfinished=!0},_x.on=Ta("on"),_x.off=Ta("off"),_x.one=Ta("one");var Sx=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];_x._initEvents=function(){lx(Sx,function(t){this._zr.on(t,function(e){var n,i=this.getModel(),r=e.target;if("globalout"===t)n={};else if(r&&null!=r.dataIndex){var a=r.dataModel||i.getSeriesByIndex(r.seriesIndex);n=a&&a.getDataParams(r.dataIndex,r.dataType)||{}}else r&&r.eventData&&(n=o({},r.eventData));n&&(n.event=e,n.type=t,this.trigger(t,n))},this)},this),lx(Ix,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},_x.isDisposed=function(){return this._disposed},_x.clear=function(){this.setOption({series:[]},!0)},_x.dispose=function(){if(!this._disposed){this._disposed=!0,Pn(this.getDom(),Nx,"");var t=this._api,e=this._model;lx(this._componentsViews,function(n){n.dispose(e,t)}),lx(this._chartsViews,function(n){n.dispose(e,t)}),this._zr.dispose(),delete Lx[this.id]}},u(Da,xp);var Mx={},Ix={},Tx=[],Cx=[],Dx=[],Ax=[],kx={},Px={},Lx={},Ox={},zx=new Date-0,Ex=new Date-0,Nx="_echarts_instance_",Rx={},Bx=Ya;es(2e3,Yy),Ka(Dy),Qa(5e3,function(t){var e=N();t.eachSeries(function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),o={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!o.stackedDimension||!o.isStackedByIndex&&!o.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}}),e.each(No)}),is("default",function(t,e){a(e=e||{},{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var n=new gv({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),i=new xv({shape:{startAngle:-Ky/2,endAngle:-Ky/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),r=new gv({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});i.animateShape(!0).when(1e3,{endAngle:3*Ky/2}).start("circularInOut"),i.animateShape(!0).when(1e3,{startAngle:3*Ky/2}).delay(300).start("circularInOut");var o=new Jp;return o.add(i),o.add(r),o.add(n),o.resize=function(){var e=t.getWidth()/2,o=t.getHeight()/2;i.setShape({cx:e,cy:o});var a=i.shape.r;r.setShape({x:e-a,y:o-a,width:2*a,height:2*a}),n.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},o.resize(),o}),Ja({type:"highlight",event:"highlight",update:"highlight"},R),Ja({type:"downplay",event:"downplay",update:"downplay"},R),$a("light",rx),$a("dark",ax);var Vx={};hs.prototype={constructor:hs,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t=this._old,e=this._new,n={},i=[],r=[];for(us(t,{},i,"_oldKeyGetter",this),us(e,n,r,"_newKeyGetter",this),o=0;o<t.length;o++)null!=(s=n[a=i[o]])?((h=s.length)?(1===h&&(n[a]=null),s=s.unshift()):n[a]=null,this._update&&this._update(s,o)):this._remove&&this._remove(o);for(var o=0;o<r.length;o++){var a=r[o];if(n.hasOwnProperty(a)){var s=n[a];if(null==s)continue;if(s.length)for(var l=0,h=s.length;l<h;l++)this._add&&this._add(s[l]);else this._add&&this._add(s)}}}};var Fx=N(["tooltip","label","itemName","itemId","seriesName"]),Hx=w,Gx="undefined"==typeof window?global:window,Wx="e\0\0",Zx={float:void 0===Gx.Float64Array?Array:Gx.Float64Array,int:void 0===Gx.Int32Array?Array:Gx.Int32Array,ordinal:Array,number:Array,time:Array},Ux=void 0===Gx.Uint32Array?Array:Gx.Uint32Array,Xx=void 0===Gx.Uint16Array?Array:Gx.Uint16Array,jx=["hasItemOption","_nameList","_idList","_calculationInfo","_invertedIndicesMap","_rawData","_rawExtent","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],Yx=function(t,e){t=t||["x","y"];for(var n={},i=[],r={},o=0;o<t.length;o++){var a=t[o];_(a)&&(a={name:a});var s=a.name;a.type=a.type||"float",a.coordDim||(a.coordDim=s,a.coordDimIndex=0),a.otherDims=a.otherDims||{},i.push(s),n[s]=a,a.index=o,a.createInvertedIndices&&(r[s]=[])}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=cs(this),this._invertedIndicesMap=r,this._calculationInfo={}},qx=Yx.prototype;qx.type="list",qx.hasItemOption=!0,qx.getDimension=function(t){return isNaN(t)||(t=this.dimensions[t]||t),t},qx.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},qx.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},qx.mapDimension=function(t,e){var n=this._dimensionsSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return!0===e?(i||[]).slice():i&&i[e]},qx.initData=function(t,e,n){($r.isInstance(t)||c(t))&&(t=new Ro(t,this.dimensions.length)),this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},n||(this.hasItemOption=!1),this.defaultDimValueGetter=Ly[this._rawData.getSource().sourceFormat],this._dimValueGetter=n=n||this.defaultDimValueGetter,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},qx.getProvider=function(){return this._rawData},qx.appendData=function(t){var e=this._rawData,n=this.count();e.appendData(t);var i=e.count();e.persistent||(i+=n),this._initDataFromProvider(n,i)},qx._initDataFromProvider=function(t,e){if(!(t>=e)){for(var n,i=this._chunkSize,r=this._rawData,o=this._storage,a=this.dimensions,s=this._dimensionInfos,l=this._nameList,h=this._idList,u=this._rawExtent,c=this._nameRepeatCount={},d=this._chunkCount,f=d-1,p=0;p<a.length;p++){u[I=a[p]]||(u[I]=[1/0,-1/0]);var g=s[I];0===g.otherDims.itemName&&(n=this._nameDimIdx=p),0===g.otherDims.itemId&&(this._idDimIdx=p);var m=Zx[g.type];o[I]||(o[I]=[]);var v=o[I][f];if(v&&v.length<i){for(var y=new m(Math.min(e-f*i,i)),x=0;x<v.length;x++)y[x]=v[x];o[I][f]=y}for(M=d*i;M<e;M+=i)o[I].push(new m(Math.min(e-M,i)));this._chunkCount=o[I].length}for(var _=t;_<e;_++){for(var w=r.getItem(_),b=Math.floor(_/i),S=_%i,M=0;M<a.length;M++){var I=a[M],T=o[I][b],C=this._dimValueGetter(w,I,_,M);T[S]=C,C<u[I][0]&&(u[I][0]=C),C>u[I][1]&&(u[I][1]=C)}if(!r.pure){var D=l[_];w&&!D&&(null!=n?D=this._getNameFromStore(_):null!=w.name&&(l[_]=D=w.name));var A=null==w?null:w.id;null==A&&null!=D&&(c[D]=c[D]||0,A=D,c[D]>0&&(A+="__ec__"+c[D]),c[D]++),null!=A&&(h[_]=A)}}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},vs(this)}},qx._getNameFromStore=function(t){var e=this._nameDimIdx;if(null!=e){var n=this._chunkSize,i=Math.floor(t/n),r=t%n,o=this.dimensions[e],a=this._dimensionInfos[o].ordinalMeta;if(a)return a.categories[t];var s=this._storage[o][i];return s&&s[r]}},qx._getIdFromStore=function(t){var e=this._idDimIdx;if(null!=e){var n=this._chunkSize,i=Math.floor(t/n),r=t%n,o=this.dimensions[e],a=this._dimensionInfos[o].ordinalMeta;if(a)return a.categories[t];var s=this._storage[o][i];return s&&s[r]}},qx.count=function(){return this._count},qx.getIndices=function(){if(this._indices)return new(t=this._indices.constructor)(this._indices.buffer,0,this._count);for(var t=ps(this),e=new t(this.count()),n=0;n<e.length;n++)e[n]=n;return e},qx.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._storage;if(!n[t])return NaN;e=this.getRawIndex(e);var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize;return n[t][i][r]},qx.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._storage[t];if(!n)return NaN;var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize;return n[i][r]},qx._getFast=function(t,e){var n=Math.floor(e/this._chunkSize),i=e%this._chunkSize;return this._storage[t][n][i]},qx.getValues=function(t,e){var n=[];y(t)||(e=t,t=this.dimensions);for(var i=0,r=t.length;i<r;i++)n.push(this.get(t[i],e));return n},qx.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,n=this._dimensionInfos,i=0,r=e.length;i<r;i++)if("ordinal"!==n[e[i]].type&&isNaN(this.get(e[i],t)))return!1;return!0},qx.getDataExtent=function(t){t=this.getDimension(t);var e=[1/0,-1/0];if(!this._storage[t])return e;var n,i=this.count();if(!this._indices)return this._rawExtent[t].slice();if(n=this._extent[t])return n.slice();for(var r=(n=e)[0],o=n[1],a=0;a<i;a++){var s=this._getFast(t,this.getRawIndex(a));s<r&&(r=s),s>o&&(o=s)}return n=[r,o],this._extent[t]=n,n},qx.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},qx.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},qx.getCalculationInfo=function(t){return this._calculationInfo[t]},qx.setCalculationInfo=function(t,e){Hx(t)?o(this._calculationInfo,t):this._calculationInfo[t]=e},qx.getSum=function(t){var e=0;if(this._storage[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},qx.rawIndexOf=function(t,e){var n=(t&&this._invertedIndicesMap[t])[e];return null==n||isNaN(n)?-1:n},qx.indexOfName=function(t){for(var e=0,n=this.count();e<n;e++)if(this.getName(e)===t)return e;return-1},qx.indexOfRawIndex=function(t){if(!this._indices)return t;if(t>=this._rawCount||t<0)return-1;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},qx.indicesOfNearest=function(t,e,n){var i=[];if(!this._storage[t])return i;null==n&&(n=1/0);for(var r=Number.MAX_VALUE,o=-1,a=0,s=this.count();a<s;a++){var l=e-this.get(t,a),h=Math.abs(l);l<=n&&h<=r&&((h<r||l>=0&&o<0)&&(r=h,o=l,i.length=0),i.push(a))}return i},qx.getRawIndex=ys,qx.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],n=0;n<this.dimensions.length;n++){var i=this.dimensions[n];e.push(this.get(i,t))}return e},qx.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||this._getNameFromStore(e)||""},qx.getId=function(t){return _s(this,this.getRawIndex(t))},qx.each=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;for(var r=(t=f(ws(t),this.getDimension,this)).length,o=0;o<this.count();o++)switch(r){case 0:e.call(n,o);break;case 1:e.call(n,this.get(t[0],o),o);break;case 2:e.call(n,this.get(t[0],o),this.get(t[1],o),o);break;default:for(var a=0,s=[];a<r;a++)s[a]=this.get(t[a],o);s[a]=o,e.apply(n,s)}}},qx.filterSelf=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=f(ws(t),this.getDimension,this);for(var r=this.count(),o=new(ps(this))(r),a=[],s=t.length,l=0,h=t[0],u=0;u<r;u++){var c,d=this.getRawIndex(u);if(0===s)c=e.call(n,u);else if(1===s){var p=this._getFast(h,d);c=e.call(n,p,u)}else{for(var g=0;g<s;g++)a[g]=this._getFast(h,d);a[g]=u,c=e.apply(n,a)}c&&(o[l++]=d)}return l<r&&(this._indices=o),this._count=l,this._extent={},this.getRawIndex=this._indices?xs:ys,this}},qx.selectRange=function(t){if(this._count){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);var i=e.length;if(i){var r=this.count(),o=new(ps(this))(r),a=0,s=e[0],l=t[s][0],h=t[s][1],u=!1;if(!this._indices){var c=0;if(1===i){for(var d=this._storage[e[0]],f=0;f<this._chunkCount;f++)for(var p=d[f],g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++)(w=p[m])>=l&&w<=h&&(o[a++]=c),c++;u=!0}else if(2===i){for(var d=this._storage[s],v=this._storage[e[1]],y=t[e[1]][0],x=t[e[1]][1],f=0;f<this._chunkCount;f++)for(var p=d[f],_=v[f],g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++){var w=p[m],b=_[m];w>=l&&w<=h&&b>=y&&b<=x&&(o[a++]=c),c++}u=!0}}if(!u)if(1===i)for(m=0;m<r;m++){M=this.getRawIndex(m);(w=this._getFast(s,M))>=l&&w<=h&&(o[a++]=M)}else for(m=0;m<r;m++){for(var S=!0,M=this.getRawIndex(m),f=0;f<i;f++){var I=e[f];((w=this._getFast(n,M))<t[I][0]||w>t[I][1])&&(S=!1)}S&&(o[a++]=this.getRawIndex(m))}return a<r&&(this._indices=o),this._count=a,this._extent={},this.getRawIndex=this._indices?xs:ys,this}}},qx.mapArray=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n),r},qx.map=function(t,e,n,i){n=n||i||this;var r=bs(this,t=f(ws(t),this.getDimension,this));r._indices=this._indices,r.getRawIndex=r._indices?xs:ys;for(var o=r._storage,a=[],s=this._chunkSize,l=t.length,h=this.count(),u=[],c=r._rawExtent,d=0;d<h;d++){for(var p=0;p<l;p++)u[p]=this.get(t[p],d);u[l]=d;var g=e&&e.apply(n,u);if(null!=g){"object"!=typeof g&&(a[0]=g,g=a);for(var m=this.getRawIndex(d),v=Math.floor(m/s),y=m%s,x=0;x<g.length;x++){var _=t[x],w=g[x],b=c[_],S=o[_];S&&(S[v][y]=w),w<b[0]&&(b[0]=w),w>b[1]&&(b[1]=w)}}}return r},qx.downSample=function(t,e,n,i){for(var r=bs(this,[t]),o=r._storage,a=[],s=Math.floor(1/e),l=o[t],h=this.count(),u=this._chunkSize,c=r._rawExtent[t],d=new(ps(this))(h),f=0,p=0;p<h;p+=s){s>h-p&&(s=h-p,a.length=s);for(var g=0;g<s;g++){var m=this.getRawIndex(p+g),v=Math.floor(m/u),y=m%u;a[g]=l[v][y]}var x=n(a),_=this.getRawIndex(Math.min(p+i(a,x)||0,h-1)),w=_%u;l[Math.floor(_/u)][w]=x,x<c[0]&&(c[0]=x),x>c[1]&&(c[1]=x),d[f++]=_}return r._count=f,r._indices=d,r.getRawIndex=xs,r},qx.getItemModel=function(t){var e=this.hostModel;return new pr(this.getRawDataItem(t),e,e&&e.ecModel)},qx.diff=function(t){var e=this;return new hs(t?t.getIndices():[],this.getIndices(),function(e){return _s(t,e)},function(t){return _s(e,t)})},qx.getVisual=function(t){var e=this._visual;return e&&e[t]},qx.setVisual=function(t,e){if(Hx(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},qx.setLayout=function(t,e){if(Hx(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},qx.getLayout=function(t){return this._layout[t]},qx.getItemLayout=function(t){return this._itemLayouts[t]},qx.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?o(this._itemLayouts[t]||{},e):e},qx.clearItemLayouts=function(){this._itemLayouts.length=0},qx.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},qx.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=i,Hx(e))for(var o in e)e.hasOwnProperty(o)&&(i[o]=e[o],r[o]=!0);else i[e]=n,r[e]=!0},qx.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};var $x=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};qx.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse($x,e)),this._graphicEls[t]=e},qx.getItemGraphicEl=function(t){return this._graphicEls[t]},qx.eachItemGraphicEl=function(t,e){d(this._graphicEls,function(n,i){n&&t&&t.call(e,n,i)})},qx.cloneShallow=function(t){if(!t){var e=f(this.dimensions,this.getDimensionInfo,this);t=new Yx(e,this.hostModel)}if(t._storage=this._storage,ms(t,this),this._indices){var i=this._indices.constructor;t._indices=new i(this._indices)}else t._indices=null;return t.getRawIndex=t._indices?xs:ys,t._extent=n(this._extent),t._approximateExtent=n(this._approximateExtent),t},qx.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(A(arguments)))})},qx.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],qx.CHANGABLE_METHODS=["filterSelf","selectRange"];var Kx=function(t,e){return e=e||{},Is(e.coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})};Os.prototype.parse=function(t){return t},Os.prototype.getSetting=function(t){return this._setting[t]},Os.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},Os.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},Os.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},Os.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Os.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Os.prototype.getExtent=function(){return this._extent.slice()},Os.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},Os.prototype.getTicksLabels=function(){for(var t=[],e=this.getTicks(),n=0;n<e.length;n++)t.push(this.getLabel(e[n]));return t},Os.prototype.isBlank=function(){return this._isBlank},Os.prototype.setBlank=function(t){this._isBlank=t},En(Os),Vn(Os,{registerWhenExtend:!0}),zs.createByAxisModel=function(t){var e=t.option,n=e.data,i=n&&f(n,Ns);return new zs({categories:i,needCollect:!i,deduplication:!1!==e.dedplication})};var Qx=zs.prototype;Qx.getOrdinal=function(t){return Es(this).get(t)},Qx.parseAndCollect=function(t){var e,n=this._needCollect;if("string"!=typeof t&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=Es(this);return null==(e=i.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e};var Jx=Os.prototype,t_=Os.extend({type:"ordinal",init:function(t,e){t&&!y(t)||(t=new zs({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),Jx.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return Jx.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(Jx.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push(n),n++;return t},getLabel:function(t){return this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:R,niceExtent:R});t_.create=function(){return new t_};var e_=wr,n_=wr,i_=Os.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),i_.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Bs(t)},getTicks:function(){return Hs(this._interval,this._extent,this._niceExtent,this._intervalPrecision)},getTicksLabels:function(){for(var t=[],e=this.getTicks(),n=0;n<e.length;n++)t.push(this.getLabel(e[n]));return t},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=Mr(t)||0:"auto"===n&&(n=this._intervalPrecision),t=n_(t,n,!0),Or(t)},niceTicks:function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var o=Rs(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax?e[0]-=n/2:(e[1]+=n/2,e[0]-=n/2)}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=n_(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=n_(Math.ceil(e[1]/r)*r))}});i_.create=function(){return new i_};var r_="__ec_stack_",o_=i_.prototype,a_=Math.ceil,s_=Math.floor,l_=function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n},h_=i_.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return Vr(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=864e5,e[1]+=864e5),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-864e5}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(e[0]=wr(s_(e[0]/i)*i)),t.fixMax||(e[1]=wr(a_(e[1]/i)*i))},niceTicks:function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0],o=r/t;null!=e&&o<e&&(o=e),null!=n&&o>n&&(o=n);var a=u_.length,s=l_(u_,o,0,a),l=u_[Math.min(s,a-1)],h=l[1];"year"===l[0]&&(h*=Lr(r/h/t,!0));var u=this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3,c=[Math.round(a_((i[0]-u)/h)*h+u),Math.round(s_((i[1]-u)/h)*h+u)];Fs(c,i),this._stepLvl=l,this._interval=h,this._niceExtent=c},parse:function(t){return+Ar(t)}});d(["contain","normalize"],function(t){h_.prototype[t]=function(e){return o_[t].call(this,this.parse(e))}});var u_=[["hh:mm:ss",1e3],["hh:mm:ss",5e3],["hh:mm:ss",1e4],["hh:mm:ss",15e3],["hh:mm:ss",3e4],["hh:mm\nMM-dd",6e4],["hh:mm\nMM-dd",3e5],["hh:mm\nMM-dd",6e5],["hh:mm\nMM-dd",9e5],["hh:mm\nMM-dd",18e5],["hh:mm\nMM-dd",36e5],["hh:mm\nMM-dd",72e5],["hh:mm\nMM-dd",216e5],["hh:mm\nMM-dd",432e5],["MM-dd\nyyyy",864e5],["MM-dd\nyyyy",1728e5],["MM-dd\nyyyy",2592e5],["MM-dd\nyyyy",3456e5],["MM-dd\nyyyy",432e6],["MM-dd\nyyyy",5184e5],["week",6048e5],["MM-dd\nyyyy",864e6],["week",12096e5],["week",18144e5],["month",26784e5],["week",36288e5],["month",53568e5],["week",36288e5],["quarter",8208e6],["month",107136e5],["month",13392e6],["half-year",16416e6],["month",214272e5],["month",26784e6],["year",32832e6]];h_.create=function(t){return new h_({useUTC:t.ecModel.get("useUTC")})};var c_=Os.prototype,d_=i_.prototype,f_=Mr,p_=wr,g_=Math.floor,m_=Math.ceil,v_=Math.pow,y_=Math.log,x_=Os.extend({type:"log",base:10,$constructor:function(){Os.apply(this,arguments),this._originalScale=new i_},getTicks:function(){var t=this._originalScale,e=this._extent,n=t.getExtent();return f(d_.getTicks.call(this),function(i){var r=wr(v_(this.base,i));return r=i===e[0]&&t.__fixMin?Xs(r,n[0]):r,r=i===e[1]&&t.__fixMax?Xs(r,n[1]):r},this)},getLabel:d_.getLabel,scale:function(t){return t=c_.scale.call(this,t),v_(this.base,t)},setExtent:function(t,e){var n=this.base;t=y_(t)/y_(n),e=y_(e)/y_(n),d_.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=c_.getExtent.call(this);e[0]=v_(t,e[0]),e[1]=v_(t,e[1]);var n=this._originalScale,i=n.getExtent();return n.__fixMin&&(e[0]=Xs(e[0],i[0])),n.__fixMax&&(e[1]=Xs(e[1],i[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=y_(t[0])/y_(e),t[1]=y_(t[1])/y_(e),c_.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=kr(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var r=[wr(m_(e[0]/i)*i),wr(g_(e[1]/i)*i)];this._interval=i,this._niceExtent=r}},niceExtent:function(t){d_.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});d(["contain","normalize"],function(t){x_.prototype[t]=function(e){return e=y_(e)/y_(this.base),c_[t].call(this,e)}}),x_.create=function(){return new x_};var __={getFormattedLabels:function(){return Qs(this.axis,this.get("axisLabel.formatter"))},getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!=typeof n&&!I(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!=typeof n&&!I(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:R,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},w_=Ai({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),b_=Ai({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),S_=Ai({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,h=Math.asin(s/a),u=Math.cos(h)*a,c=Math.sin(h),d=Math.cos(h),f=.6*a,p=.7*a;t.moveTo(n-u,l+s),t.arc(n,l,a,Math.PI-h,2*Math.PI+h),t.bezierCurveTo(n+u-c*f,l+s+d*f,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-u+c*f,l+s+d*f,n-u,l+s),t.closePath()}}),M_=Ai({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),I_={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},T_={};d({line:mv,rect:gv,roundRect:gv,square:gv,circle:av,diamond:b_,pin:S_,arrow:M_,triangle:w_},function(t,e){T_[e]=new t});var C_=Ai({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style;"pin"===this.shape.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t.textAlign="center",t.textVerticalAlign="middle")},buildPath:function(t,e,n){var i=e.symbolType,r=T_[i];"none"!==e.symbolType&&(r||(r=T_[i="rect"]),I_[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n))}}),D_={isDimensionStacked:As,enableDataStack:Ds},A_=(Object.freeze||Object)({createList:function(t){return ks(t.getSource(),t)},getLayoutRect:Gr,dataStack:D_,createScale:function(t,e){var n=e;pr.isInstance(e)||u(n=new pr(e),__);var i=$s(n);return i.setExtent(t[0],t[1]),qs(i,n),i},mixinAxisModelCommonMethods:function(t){u(t,__)},completeDimensions:Is,createDimensions:Kx,createSymbol:el}),k_=1e-8;rl.prototype={constructor:rl,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,n=[e,e],i=[-e,-e],r=[],o=[],a=this.geometries,s=0;s<a.length;s++)"polygon"===a[s].type&&(ti(a[s].exterior,r,o),K(n,n,r),Q(i,i,o));return 0===s&&(n[0]=n[1]=i[0]=i[1]=0),this._rect=new Xt(n[0],n[1],i[0]-n[0],i[1]-n[1])},contain:function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++)if("polygon"===n[i].type){var o=n[i].exterior,a=n[i].interiors;if(il(o,t[0],t[1])){for(var s=0;s<(a?a.length:0);s++)if(il(a[s]))continue t;return!0}}return!1},transformTo:function(t,e,n,i){var r=this.getBoundingRect(),o=r.width/r.height;n?i||(i=n/o):n=o*i;for(var a=new Xt(t,e,n,i),s=r.calculateTransform(a),l=this.geometries,h=0;h<l.length;h++)if("polygon"===l[h].type){for(var u=l[h].exterior,c=l[h].interiors,d=0;d<u.length;d++)$(u[d],u[d],s);for(var f=0;f<(c?c.length:0);f++)for(d=0;d<c[f].length;d++)$(c[f][d],c[f][d],s)}(r=this._rect).copy(a),this.center=[r.x+r.width/2,r.y+r.height/2]}};var P_=function(t){return ol(t),f(g(t.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var e=t.properties,n=t.geometry,i=n.coordinates,r=[];"Polygon"===n.type&&r.push({type:"polygon",exterior:i[0],interiors:i.slice(1)}),"MultiPolygon"===n.type&&d(i,function(t){t[0]&&r.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var o=new rl(e.name,r,e.cp);return o.properties=e,o})},L_=xr,O_=[0,1],z_=function(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1,this._labelInterval};z_.prototype={constructor:z_,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},containData:function(t){return this.contain(this.dataToCoord(t))},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return Ir(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&sl(n=n.slice(),i.count()),L_(t,O_,n,e)},coordToData:function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&sl(n=n.slice(),i.count());var r=L_(t,n,O_,e);return this.scale.scale(r)},pointToData:function(t,e){},getTicksCoords:function(t){if(this.onBand&&!t){for(var e=this.getBands(),n=[],i=0;i<e.length;i++)n.push(e[i][0]);return e[i-1]&&n.push(e[i-1][1]),n}return f(this.scale.getTicks(),this.dataToCoord,this)},getLabelsCoords:function(){return f(this.scale.getTicks(),this.dataToCoord,this)},getBands:function(){for(var t=this.getExtent(),e=[],n=this.scale.count(),i=t[0],r=t[1]-i,o=0;o<n;o++)e.push([r*o/n+i,r*(o+1)/n+i]);return e},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},isHorizontal:null,getRotate:null,getLabelInterval:function(){var t=this._labelInterval;if(!t){var e=this.model,n=e.getModel("axisLabel");t=n.get("interval"),"category"!==this.type||null!=t&&"auto"!==t||(t=Ks(f(this.scale.getTicks(),this.dataToCoord,this),e.getFormattedLabels(),n.getFont(),this.getRotate?this.getRotate():this.isHorizontal&&!this.isHorizontal()?90:0,n.get("rotate"))),this._labelInterval=t}return t}};var E_=P_,N_={};d(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){N_[t]=cp[t]});rs({type:"dataset",defaultOption:{seriesLayoutBy:uy,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){Kr(this)}});os({type:"dataset"}),Ry.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,e){return ks(this.getSource(),this)},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clipOverflow:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:!1,connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var R_=cl.prototype;R_._createSymbol=function(t,e,n,i){this.removeAll();var r=el(t,-1,-1,2,2,e.getItemVisual(n,"color"));r.attr({z2:100,culling:!0,scale:ul(i)}),r.drift=dl,this._symbolType=t,this.add(r)},R_.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},R_.getSymbolPath=function(){return this.childAt(0)},R_.getScale=function(){return this.childAt(0).scale},R_.highlight=function(){this.childAt(0).trigger("emphasis")},R_.downplay=function(){this.childAt(0).trigger("normal")},R_.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},R_.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":"pointer"},R_.updateData=function(t,e,n){this.silent=!1;var i=t.getItemVisual(e,"symbol")||"circle",r=t.hostModel,o=hl(t,e),a=i!==this._symbolType;if(a?this._createSymbol(i,t,e,o):((s=this.childAt(0)).silent=!1,ar(s,{scale:ul(o)},r,e)),this._updateCommon(t,e,o,n),a){var s=this.childAt(0),l=n&&n.fadeIn,h={scale:s.scale.slice()};l&&(h.style={opacity:s.style.opacity}),s.scale=[0,0],l&&(s.style.opacity=0),sr(s,h,r,e)}this._seriesModel=r};var B_=["itemStyle"],V_=["emphasis","itemStyle"],F_=["label"],H_=["emphasis","label"];R_._updateCommon=function(t,e,n,i){var r=this.childAt(0),a=t.hostModel,s=t.getItemVisual(e,"color");"image"!==r.type&&r.useStyle({strokeNoScale:!0});var l=i&&i.itemStyle,h=i&&i.hoverItemStyle,u=i&&i.symbolRotate,c=i&&i.symbolOffset,d=i&&i.labelModel,f=i&&i.hoverLabelModel,p=i&&i.hoverAnimation,g=i&&i.cursorStyle;if(!i||t.hasItemOption){var m=i&&i.itemModel?i.itemModel:t.getItemModel(e);l=m.getModel(B_).getItemStyle(["color"]),h=m.getModel(V_).getItemStyle(),u=m.getShallow("symbolRotate"),c=m.getShallow("symbolOffset"),d=m.getModel(F_),f=m.getModel(H_),p=m.getShallow("hoverAnimation"),g=m.getShallow("cursor")}else h=o({},h);var v=r.style;r.attr("rotation",(u||0)*Math.PI/180||0),c&&r.attr("position",[_r(c[0],n[0]),_r(c[1],n[1])]),g&&r.attr("cursor",g),r.setColor(s,i&&i.symbolInnerColor),r.setStyle(l);var y=t.getItemVisual(e,"opacity");null!=y&&(v.opacity=y);var x=i&&i.useNameLabel;$i(v,h,d,f,{labelFetcher:a,labelDataIndex:e,defaultText:function(e,n){return x?t.getName(e):ll(t,e)},isRectText:!0,autoColor:s}),r.off("mouseover").off("mouseout").off("emphasis").off("normal"),r.hoverStyle=h,qi(r);var _=ul(n);if(p&&a.isAnimationEnabled()){var w=function(){if(!this.incremental){var t=_[1]/_[0];this.animateTo({scale:[Math.max(1.1*_[0],_[0]+3),Math.max(1.1*_[1],_[1]+3*t)]},400,"elasticOut")}},b=function(){this.incremental||this.animateTo({scale:_},400,"elasticOut")};r.on("mouseover",w).on("mouseout",b).on("emphasis",w).on("normal",b)}},R_.fadeOut=function(t,e){var n=this.childAt(0);this.silent=n.silent=!0,!(e&&e.keepLabel)&&(n.style.text=null),ar(n,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},h(cl,Jp);var G_=fl.prototype;G_.updateData=function(t,e){e=gl(e);var n=this.group,i=t.hostModel,r=this._data,o=this._symbolCtor,a=ml(t);r||n.removeAll(),t.diff(r).add(function(i){var r=t.getItemLayout(i);if(pl(t,r,i,e)){var s=new o(t,i,a);s.attr("position",r),t.setItemGraphicEl(i,s),n.add(s)}}).update(function(s,l){var h=r.getItemGraphicEl(l),u=t.getItemLayout(s);pl(t,u,s,e)?(h?(h.updateData(t,s,a),ar(h,{position:u},i)):(h=new o(t,s)).attr("position",u),n.add(h),t.setItemGraphicEl(s,h)):n.remove(h)}).remove(function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut(function(){n.remove(e)})}).execute(),this._data=t},G_.isPersistent=function(){return!0},G_.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,n){var i=t.getItemLayout(n);e.attr("position",i)})},G_.incrementalPrepareUpdate=function(t){this._seriesScope=ml(t),this._data=null,this.group.removeAll()},G_.incrementalUpdate=function(t,e,n){n=gl(n);for(var i=t.start;i<t.end;i++){var r=e.getItemLayout(i);if(pl(e,r,i,n)){var o=new this._symbolCtor(e,i,this._seriesScope);o.traverse(function(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}),o.attr("position",r),this.group.add(o),e.setItemGraphicEl(i,o)}}},G_.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var W_=function(t,e,n,i,r,o,a,s){for(var l=_l(t,e),h=[],u=[],c=[],d=[],f=[],p=[],g=[],m=vl(r,e,a),v=vl(o,t,s),y=0;y<l.length;y++){var x=l[y],_=!0;switch(x.cmd){case"=":var w=t.getItemLayout(x.idx),b=e.getItemLayout(x.idx1);(isNaN(w[0])||isNaN(w[1]))&&(w=b.slice()),h.push(w),u.push(b),c.push(n[x.idx]),d.push(i[x.idx1]),g.push(e.getRawIndex(x.idx1));break;case"+":S=x.idx;h.push(r.dataToPoint([e.get(m.dataDimsForPoint[0],S),e.get(m.dataDimsForPoint[1],S)])),u.push(e.getItemLayout(S).slice()),c.push(xl(m,r,e,S)),d.push(i[S]),g.push(e.getRawIndex(S));break;case"-":var S=x.idx,M=t.getRawIndex(S);M!==S?(h.push(t.getItemLayout(S)),u.push(o.dataToPoint([t.get(v.dataDimsForPoint[0],S),t.get(v.dataDimsForPoint[1],S)])),c.push(n[S]),d.push(xl(v,o,t,S)),g.push(M)):_=!1}_&&(f.push(x),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});for(var I=[],T=[],C=[],D=[],A=[],y=0;y<p.length;y++){S=p[y];I[y]=h[S],T[y]=u[S],C[y]=c[S],D[y]=d[S],A[y]=f[S]}return{current:I,next:T,stackedOnCurrent:C,stackedOnNext:D,status:A}},Z_=K,U_=Q,X_=G,j_=V,Y_=[],q_=[],$_=[],K_=xi.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:lv(xi.prototype.brush),buildPath:function(t,e){var n=e.points,i=0,r=n.length,o=Tl(n,e.smoothConstraint);if(e.connectNulls){for(;r>0&&wl(n[r-1]);r--);for(;i<r&&wl(n[i]);i++);}for(;i<r;)i+=bl(t,n,i,r,r,1,o.min,o.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),Q_=xi.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:lv(xi.prototype.brush),buildPath:function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length,a=e.smoothMonotone,s=Tl(n,e.smoothConstraint),l=Tl(i,e.smoothConstraint);if(e.connectNulls){for(;o>0&&wl(n[o-1]);o--);for(;r<o&&wl(n[r]);r++);}for(;r<o;){var h=bl(t,n,r,o,o,1,s.min,s.max,e.smooth,a,e.connectNulls);bl(t,i,r+h-1,h,o,-1,l.min,l.max,e.stackedOnSmooth,a,e.connectNulls),r+=h+1,t.closePath()}}});ia.extend({type:"line",init:function(){var t=new Jp,e=new fl;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var i=t.coordinateSystem,r=this.group,o=t.getData(),s=t.getModel("lineStyle"),l=t.getModel("areaStyle"),h=o.mapArray(o.getItemLayout),u="polar"===i.type,c=this._coordSys,d=this._symbolDraw,f=this._polyline,p=this._polygon,g=this._lineGroup,m=t.get("animation"),v=!l.isEmpty(),y=l.get("origin"),x=kl(i,o,vl(i,o,y)),_=t.get("showSymbol"),w=_&&!u&&!t.get("showAllSymbol")&&this._getSymbolIgnoreFunc(o,i),b=this._data;b&&b.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),b.setItemGraphicEl(e,null))}),_||d.remove(),r.add(g);var S=!u&&t.get("step");if(f&&c.type===i.type&&S===this._step){v&&!p?p=this._newPolygon(h,x,i,m):p&&!v&&(g.remove(p),p=this._polygon=null);var M=Ol(i,!1,t);g.setClipPath(M),_&&d.updateData(o,{isIgnore:w,clipShape:M}),o.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),Cl(this._stackedOnPoints,x)&&Cl(this._points,h)||(m?this._updateAnimation(o,x,i,n,S,y):(S&&(h=zl(h,i,S),x=zl(x,i,S)),f.setShape({points:h}),p&&p.setShape({points:h,stackedOnPoints:x})))}else _&&d.updateData(o,{isIgnore:w,clipShape:Ol(i,!1,t)}),S&&(h=zl(h,i,S),x=zl(x,i,S)),f=this._newPolyline(h,i,m),v&&(p=this._newPolygon(h,x,i,m)),g.setClipPath(Ol(i,!0,t));var I=El(o,i)||o.getVisual("color");f.useStyle(a(s.getLineStyle(),{fill:"none",stroke:I,lineJoin:"bevel"}));var T=t.get("smooth");if(T=Dl(t.get("smooth")),f.setShape({smooth:T,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),p){var C=o.getCalculationInfo("stackedOnSeries"),D=0;p.useStyle(a(l.getAreaStyle(),{fill:I,opacity:.7,lineJoin:"bevel"})),C&&(D=Dl(C.get("smooth"))),p.setShape({smooth:T,stackedOnSmooth:D,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=o,this._coordSys=i,this._stackedOnPoints=x,this._points=h,this._step=S,this._valueOrigin=y},dispose:function(){},highlight:function(t,e,n,i){var r=t.getData(),o=Cn(r,i);if(!(o instanceof Array)&&null!=o&&o>=0){var a=r.getItemGraphicEl(o);if(!a){var s=r.getItemLayout(o);if(!s)return;(a=new cl(r,o)).position=s,a.setZ(t.get("zlevel"),t.get("z")),a.ignore=isNaN(s[0])||isNaN(s[1]),a.__temp=!0,r.setItemGraphicEl(o,a),a.stopSymbolAnimation(!0),this.group.add(a)}a.highlight()}else ia.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var r=t.getData(),o=Cn(r,i);if(null!=o&&o>=0){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else ia.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new K_({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new Q_({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n,n},_getSymbolIgnoreFunc:function(t,e){var n=e.getAxesByScale("ordinal")[0];if(n&&n.isLabelIgnored)return m(n.isLabelIgnored,n)},_updateAnimation:function(t,e,n,i,r,o){var a=this._polyline,s=this._polygon,l=t.hostModel,h=W_(this._data,t,this._stackedOnPoints,e,this._coordSys,n,this._valueOrigin,o),u=h.current,c=h.stackedOnCurrent,d=h.next,f=h.stackedOnNext;r&&(u=zl(h.current,n,r),c=zl(h.stackedOnCurrent,n,r),d=zl(h.next,n,r),f=zl(h.stackedOnNext,n,r)),a.shape.__points=h.current,a.shape.points=u,ar(a,{shape:{points:d}},l),s&&(s.setShape({points:u,stackedOnPoints:c}),ar(s,{shape:{points:d,stackedOnPoints:f}},l));for(var p=[],g=h.status,m=0;m<g.length;m++)if("="===g[m].cmd){var v=t.getItemGraphicEl(g[m].idx1);v&&p.push({el:v,ptIdx:m})}a.animators&&a.animators.length&&a.animators[0].during(function(){for(var t=0;t<p.length;t++)p[t].el.attr("position",a.shape.__points[p[t].ptIdx])})},remove:function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});var J_=function(t,e,n){return{seriesType:t,performRawSeries:!0,reset:function(t,i,r){var o=t.getData(),a=t.get("symbol")||e,s=t.get("symbolSize");if(o.setVisual({legendSymbol:n||a,symbol:a,symbolSize:s}),!i.isSeriesFiltered(t)){var l="function"==typeof s;return{dataEach:o.hasItemOption||l?function(e,n){if("function"==typeof s){var i=t.getRawValue(n),r=t.getDataParams(n);e.setItemVisual(n,"symbolSize",s(i,r))}if(e.hasItemOption){var o=e.getItemModel(n),a=o.getShallow("symbol",!0),l=o.getShallow("symbolSize",!0);null!=a&&e.setItemVisual(n,"symbol",a),null!=l&&e.setItemVisual(n,"symbolSize",l)}}:null}}}}},tw=function(t){return{seriesType:t,plan:Fy(),reset:function(t){var e=t.getData(),n=t.coordinateSystem,i=t.pipelineContext.large;if(n){var r=f(n.dimensions,function(t){return e.mapDimension(t)}).slice(0,2),o=r.length;return As(e,r[0],r[1])&&(r[0]=e.getCalculationInfo("stackResultDimension")),As(e,r[1],r[0])&&(r[1]=e.getCalculationInfo("stackResultDimension")),o&&{progress:function(t,e){for(var a=t.end-t.start,s=i&&new Float32Array(a*o),l=t.start,h=0,u=[],c=[];l<t.end;l++){var d;if(1===o)f=e.get(r[0],l,!0),d=!isNaN(f)&&n.dataToPoint(f,null,c);else{var f=u[0]=e.get(r[0],l,!0),p=u[1]=e.get(r[1],l,!0);d=!isNaN(f)&&!isNaN(p)&&n.dataToPoint(u,null,c)}i?(s[h++]=d?d[0]:NaN,s[h++]=d?d[1]:NaN):e.setItemLayout(l,d&&d.slice()||[NaN,NaN])}i&&e.setLayout("symbolPoints",s)}}}}}},ew={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return e},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return e},nearest:function(t){return t[0]}},nw=function(t,e){return Math.round(t.length/2)},iw=function(t){this._axes={},this._dimList=[],this.name=t||""};iw.prototype={constructor:iw,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return f(this._dimList,Nl,this)},getAxesByScale:function(t){return t=t.toLowerCase(),g(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var o=n[r],a=this._axes[o];i[o]=a[e](t[o])}return i}},Rl.prototype={constructor:Rl,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,n){var i=this.getAxis("x"),r=this.getAxis("y");return n=n||[],n[0]=i.toGlobalCoord(i.dataToCoord(t[0])),n[1]=r.toGlobalCoord(r.dataToCoord(t[1])),n},clampData:function(t,e){var n=this.getAxis("x").scale.getExtent(),i=this.getAxis("y").scale.getExtent();return e=e||[],e[0]=Math.min(Math.max(Math.min(n[0],n[1]),t[0]),Math.max(n[0],n[1])),e[1]=Math.min(Math.max(Math.min(i[0],i[1]),t[1]),Math.max(i[0],i[1])),e},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return e=e||[],e[0]=n.coordToData(n.toLocalCoord(t[0])),e[1]=i.coordToData(i.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},h(Rl,iw);var rw=function(t,e,n,i,r){z_.call(this,t,e,n),this.type=i||"value",this.position=r||"bottom"};rw.prototype={constructor:rw,index:0,onZero:!1,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},isLabelIgnored:function(t){if("category"===this.type){var e=this.getLabelInterval();return"function"==typeof e&&!e(t,this.scale.getLabel(t))||t%(e+1)}},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},h(rw,z_);var ow={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},aw={};aw.categoryAxis=i({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},ow),aw.valueAxis=i({boundaryGap:[0,0],splitNumber:5},ow),aw.timeAxis=a({scale:!0,min:"dataMin",max:"dataMax"},aw.valueAxis),aw.logAxis=a({scale:!0,logBase:10},aw.valueAxis);var sw=["value","category","time","log"],lw=function(t,e,n,o){d(sw,function(a){e.extend({type:t+"Axis."+a,mergeDefaultAndTheme:function(e,r){var o=this.layoutMode,s=o?Ur(e):{};i(e,r.getTheme().get(a+"Axis")),i(e,this.getDefaultOption()),e.type=n(t,e),o&&Zr(e,s,o)},optionUpdated:function(){"category"===this.option.type&&(this.__ordinalMeta=zs.createByAxisModel(this))},getCategories:function(){if("category"===this.option.type)return this.__ordinalMeta.categories},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:r([{},aw[a+"Axis"],o],!0)})}),Qv.registerSubTypeDefaulter(t+"Axis",v(n,t))},hw=Qv.extend({type:"cartesian2dAxis",axis:null,init:function(){hw.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){hw.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){hw.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});i(hw.prototype,__);var uw={offset:0};lw("x",hw,Bl,uw),lw("y",hw,Bl,uw),Qv.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var cw=d,dw=function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)},fw=qs,pw=Gl.prototype;pw.type="grid",pw.axisPointerEnabled=!0,pw.getRect=function(){return this._rect},pw.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),cw(n.x,function(t){fw(t.scale,t.model)}),cw(n.y,function(t){fw(t.scale,t.model)}),cw(n.x,function(t){Wl(n,"y",t)}),cw(n.y,function(t){Wl(n,"x",t)}),this.resize(this.model,e)},pw.resize=function(t,e,n){function i(){cw(o,function(t){var e=t.isHorizontal(),n=e?[0,r.width]:[0,r.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),Ul(t,e?r.x:r.y)})}var r=Gr(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=r;var o=this._axesList;i(),!n&&t.get("containLabel")&&(cw(o,function(t){if(!t.model.get("axisLabel.inside")){var e=Hl(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get("axisLabel.margin");r[n]-=e[n]+i,"top"===t.position?r.y+=e.height+i:"left"===t.position&&(r.x+=e.width+i)}}}),i())},pw.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},pw.getAxes=function(){return this._axesList.slice()},pw.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}w(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},pw.getCartesians=function(){return this._coordsList.slice()},pw.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},pw.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},pw._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,o=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],a=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,h=this._coordsList;return r?l(h,n=r.coordinateSystem)<0&&(n=null):o&&a?n=this.getCartesian(o.componentIndex,a.componentIndex):o?i=this.getAxis("x",o.componentIndex):a?i=this.getAxis("y",a.componentIndex):s&&s.coordinateSystem===this&&(n=this._coordsList[0]),{cartesian:n,axis:i}},pw.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},pw._initCartesian=function(t,e,n){function i(n){return function(i,s){if(Vl(i,t,e)){var l=i.get("position");"x"===n?"top"!==l&&"bottom"!==l&&r[l="bottom"]&&(l="top"===l?"bottom":"top"):"left"!==l&&"right"!==l&&r[l="left"]&&(l="left"===l?"right":"left"),r[l]=!0;var h=new rw(n,$s(i),[0,0],i.get("type"),l),u="category"===h.type;h.onBand=u&&i.get("boundaryGap"),h.inverse=i.get("inverse"),h.onZero=i.get("axisLine.onZero"),h.onZeroAxisIndex=i.get("axisLine.onZeroAxisIndex"),i.axis=h,h.model=i,h.grid=this,h.index=s,this._axesList.push(h),o[n][s]=h,a[n]++}}}var r={left:!1,right:!1,top:!1,bottom:!1},o={x:{},y:{}},a={x:0,y:0};if(e.eachComponent("xAxis",i("x"),this),e.eachComponent("yAxis",i("y"),this),!a.x||!a.y)return this._axesMap={},void(this._axesList=[]);this._axesMap=o,cw(o.x,function(e,n){cw(o.y,function(i,r){var o="x"+n+"y"+r,a=new Rl(o);a.grid=this,a.model=t,this._coordsMap[o]=a,this._coordsList.push(a),a.addAxis(e),a.addAxis(i)},this)},this)},pw._updateScale=function(t,e){function n(t,e,n){cw(t.mapDimension(e.dim,!0),function(n){e.scale.unionExtentFromData(t,n)})}d(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(i){if(jl(i)){var r=Xl(i),o=r[0],a=r[1];if(!Vl(o,e,t)||!Vl(a,e,t))return;var s=this.getCartesian(o.componentIndex,a.componentIndex),l=i.getData(),h=s.getAxis("x"),u=s.getAxis("y");"list"===l.type&&(n(l,h),n(l,u))}},this)},pw.getTooltipAxes=function(t){var e=[],n=[];return cw(this.getCartesians(),function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(r);l(e,r)<0&&e.push(r),l(n,o)<0&&n.push(o)}),{baseAxes:e,otherAxes:n}};var gw=["xAxis","yAxis"];Gl.create=function(t,e){var n=[];return t.eachComponent("grid",function(i,r){var o=new Gl(i,t,e);o.name="grid_"+r,o.resize(i,e,!0),i.coordinateSystem=o,n.push(o)}),t.eachSeries(function(t){if(jl(t)){var e=Xl(t),n=e[0],i=e[1],r=n.getCoordSysModel().coordinateSystem;t.coordinateSystem=r.getCartesian(n.componentIndex,i.componentIndex)}}),n},Gl.dimensions=Gl.prototype.dimensions=Rl.prototype.dimensions,yo.register("cartesian2d",Gl);var mw=Math.PI,vw=function(t,e){this.opt=e,this.axisModel=t,a(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new Jp;var n=new Jp({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n};vw.prototype={constructor:vw,hasBuilder:function(t){return!!yw[t]},add:function(t){yw[t].call(this)},getGroup:function(){return this.group}};var yw={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var n=this.axisModel.axis.getExtent(),i=this._transform,r=[n[0],0],a=[n[1],0];i&&($(r,r,i),$(a,a,i));var s=o({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle());this.group.add(new mv(zi({anid:"line",shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:s,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})));var l=e.get("axisLine.symbol"),h=e.get("axisLine.symbolSize"),u=e.get("axisLine.symbolOffset")||0;if("number"==typeof u&&(u=[u,u]),null!=l){"string"==typeof l&&(l=[l,l]),"string"!=typeof h&&"number"!=typeof h||(h=[h,h]);var c=h[0],f=h[1];d([{rotate:t.rotation+Math.PI/2,offset:u[0],r:0},{rotate:t.rotation-Math.PI/2,offset:u[1],r:Math.sqrt((r[0]-a[0])*(r[0]-a[0])+(r[1]-a[1])*(r[1]-a[1]))}],function(e,n){if("none"!==l[n]&&null!=l[n]){var i=el(l[n],-c/2,-f/2,c,f,s.stroke,!0),o=e.r+e.offset,a=[r[0]+o*Math.cos(t.rotation),r[1]-o*Math.sin(t.rotation)];i.attr({rotation:e.rotate,position:a,silent:!0}),this.group.add(i)}},this)}}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,n=eh(this,t,e);Kl(t,nh(this,t,e),n)},axisName:function(){var t=this.opt,e=this.axisModel,n=T(t.axisName,e.get("name"));if(n){var i,r=e.get("nameLocation"),a=t.nameDirection,s=e.getModel("nameTextStyle"),l=e.get("nameGap")||0,h=this.axisModel.axis.getExtent(),u=h[0]>h[1]?-1:1,c=["start"===r?h[0]-u*l:"end"===r?h[1]+u*l:(h[0]+h[1])/2,th(r)?t.labelOffset+a*l:0],d=e.get("nameRotate");null!=d&&(d=d*mw/180);var f;th(r)?i=xw(t.rotation,null!=d?d:t.rotation,a):(i=ql(t,r,d||0,h),null!=(f=t.axisNameAvailableWidth)&&(f=Math.abs(f/Math.sin(i.rotation)),!isFinite(f)&&(f=null)));var p=s.getFont(),g=e.get("nameTruncate",!0)||{},m=g.ellipsis,v=T(t.nameTruncateMaxWidth,g.maxWidth,f),y=null!=m&&null!=v?Wv(n,v,p,m,{minChar:2,placeholder:g.placeholder}):n,x=e.get("tooltip",!0),_=e.mainType,w={componentType:_,name:n,$vars:["name"]};w[_+"Index"]=e.componentIndex;var b=new ov({anid:"name",__fullText:n,__truncatedText:y,position:c,rotation:i.rotation,silent:$l(e),z2:1,tooltip:x&&x.show?o({content:n,formatter:function(){return n},formatterParams:w},x):null});Ki(b.style,s,{text:y,textFont:p,textFill:s.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:i.textAlign,textVerticalAlign:i.textVerticalAlign}),e.get("triggerEvent")&&(b.eventData=Yl(e),b.eventData.targetType="axisName",b.eventData.name=n),this._dumbGroup.add(b),b.updateTransform(),this.group.add(b),b.decomposeTransform()}}},xw=vw.innerTextLayout=function(t,e,n){var i,r,o=Cr(e-t);return Dr(o)?(r=n>0?"top":"bottom",i="center"):Dr(o-mw)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&o<mw?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},_w=vw.ifIgnoreOnTick=function(t,e,n,i,r,o){if(0===e&&r||e===i-1&&o)return!1;var a,s=t.scale;return"ordinal"===s.type&&("function"==typeof n?(a=s.getTicks()[e],!n(a,s.getLabel(a))):e%(n+1))},ww=vw.getInterval=function(t,e){var n=t.get("interval");return null!=n&&"auto"!=n||(n=e),n},bw=d,Sw=v,Mw=os({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,i){this.axisPointerClass&&hh(t),Mw.superApply(this,"render",arguments),ph(this,t,0,n,0,!0)},updateAxisPointer:function(t,e,n,i,r){ph(this,t,0,n,0,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),Mw.superApply(this,"remove",arguments)},dispose:function(t,e){gh(this,e),Mw.superApply(this,"dispose",arguments)}}),Iw=[];Mw.registerAxisPointerClass=function(t,e){Iw[t]=e},Mw.getAxisPointerClass=function(t){return t&&Iw[t]};var Tw=vw.ifIgnoreOnTick,Cw=vw.getInterval,Dw=["axisLine","axisTickLabel","axisName"],Aw=["splitArea","splitLine"],kw=Mw.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,n,i){this.group.removeAll();var r=this._axisGroup;if(this._axisGroup=new Jp,this.group.add(this._axisGroup),t.get("show")){var o=t.getCoordSysModel(),a=mh(o,t),s=new vw(t,a);d(Dw,s.add,s),this._axisGroup.add(s.getGroup()),d(Aw,function(e){t.get(e+".show")&&this["_"+e](t,o,a.labelInterval)},this),cr(r,this._axisGroup,t),kw.superCall(this,"render",t,e,n,i)}},_splitLine:function(t,e,n){var i=t.axis;if(!i.scale.isBlank()){var r=t.getModel("splitLine"),o=r.getModel("lineStyle"),s=o.get("color"),l=Cw(r,n);s=y(s)?s:[s];for(var h=e.coordinateSystem.getRect(),u=i.isHorizontal(),c=0,d=i.getTicksCoords(),f=i.scale.getTicks(),p=t.get("axisLabel.showMinLabel"),g=t.get("axisLabel.showMaxLabel"),m=[],v=[],x=o.getLineStyle(),_=0;_<d.length;_++)if(!Tw(i,_,l,d.length,p,g)){var w=i.toGlobalCoord(d[_]);u?(m[0]=w,m[1]=h.y,v[0]=w,v[1]=h.y+h.height):(m[0]=h.x,m[1]=w,v[0]=h.x+h.width,v[1]=w);var b=c++%s.length;this._axisGroup.add(new mv(zi({anid:"line_"+f[_],shape:{x1:m[0],y1:m[1],x2:v[0],y2:v[1]},style:a({stroke:s[b]},x),silent:!0})))}}},_splitArea:function(t,e,n){var i=t.axis;if(!i.scale.isBlank()){var r=t.getModel("splitArea"),o=r.getModel("areaStyle"),s=o.get("color"),l=e.coordinateSystem.getRect(),h=i.getTicksCoords(),u=i.scale.getTicks(),c=i.toGlobalCoord(h[0]),d=i.toGlobalCoord(h[0]),f=0,p=Cw(r,n),g=o.getAreaStyle();s=y(s)?s:[s];for(var m=t.get("axisLabel.showMinLabel"),v=t.get("axisLabel.showMaxLabel"),x=1;x<h.length;x++)if(!(Tw(i,x,p,h.length,m,v)&&x<h.length-1)){var _,w,b,S,M=i.toGlobalCoord(h[x]);i.isHorizontal()?(_=c,w=l.y,b=M-_,S=l.height):(_=l.x,w=d,b=l.width,S=M-w);var I=f++%s.length;this._axisGroup.add(new gv({anid:"area_"+u[x],shape:{x:_,y:w,width:b,height:S},style:a({fill:s[I]},g),silent:!0})),c=_+b,d=w+S}}}});kw.extend({type:"xAxis"}),kw.extend({type:"yAxis"}),os({type:"grid",render:function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new gv({shape:t.coordinateSystem.getRect(),style:a({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),Ka(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),es(J_("line","circle","line")),ts(tw("line")),Qa(mx.PROCESSOR.STATISTIC,function(t){return{seriesType:t,reset:function(t,e,n){var i=t.getData(),r=t.get("sampling"),o=t.coordinateSystem;if("cartesian2d"===o.type&&r){var a=o.getBaseAxis(),s=o.getOtherAxis(a),l=a.getExtent(),h=l[1]-l[0],u=Math.round(i.count()/h);if(u>1){var c;"string"==typeof r?c=ew[r]:"function"==typeof r&&(c=r),c&&t.setData(i.downSample(s.dim,1/u,c,nw))}}}}}("line")),Ry.extend({type:"series.__base_bar__",getInitialData:function(t,e){return ks(this.getSource(),this)},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var n=e.dataToPoint(e.clampData(t)),i=this.getData(),r=i.getLayout("offset"),o=i.getLayout("size");return n[e.getBaseAxis().isHorizontal()?0:1]+=r+o/2,n}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,itemStyle:{},emphasis:{}}}).extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect"});var Pw=Jg([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Lw={getBarItemStyle:function(t){var e=Pw(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(e.lineDash=n)}return e}},Ow=["itemStyle","barBorderWidth"];o(pr.prototype,Lw),ss({type:"bar",render:function(t,e,n){var i=t.get("coordinateSystem");return"cartesian2d"!==i&&"polar"!==i||this._render(t,e,n),this.group},dispose:R,_render:function(t,e,n){var i,r=this.group,o=t.getData(),a=this._data,s=t.coordinateSystem,l=s.getBaseAxis();"cartesian2d"===s.type?i=l.isHorizontal():"polar"===s.type&&(i="angle"===l.dim);var h=t.isAnimationEnabled()?t:null;o.diff(a).add(function(e){if(o.hasValue(e)){var n=o.getItemModel(e),a=Ew[s.type](o,e,n),l=zw[s.type](o,e,n,a,i,h);o.setItemGraphicEl(e,l),r.add(l),wh(l,o,e,n,a,t,i,"polar"===s.type)}}).update(function(e,n){var l=a.getItemGraphicEl(n);if(o.hasValue(e)){var u=o.getItemModel(e),c=Ew[s.type](o,e,u);l?ar(l,{shape:c},h,e):l=zw[s.type](o,e,u,c,i,h,!0),o.setItemGraphicEl(e,l),r.add(l),wh(l,o,e,u,c,t,i,"polar"===s.type)}else r.remove(l)}).remove(function(t){var e=a.getItemGraphicEl(t);"cartesian2d"===s.type?e&&xh(t,h,e):e&&_h(t,h,e)}).execute(),this._data=o},remove:function(t,e){var n=this.group,i=this._data;t.get("animation")?i&&i.eachItemGraphicEl(function(e){"sector"===e.type?_h(e.dataIndex,t,e):xh(e.dataIndex,t,e)}):n.removeAll()}});var zw={cartesian2d:function(t,e,n,i,r,a,s){var l=new gv({shape:o({},i)});if(a){var h=l.shape,u=r?"height":"width",c={};h[u]=0,c[u]=i[u],Av[s?"updateProps":"initProps"](l,{shape:c},a,e)}return l},polar:function(t,e,n,i,r,o,s){var l=i.startAngle<i.endAngle,h=new hv({shape:a({clockwise:l},i)});if(o){var u=h.shape,c=r?"r":"endAngle",d={};u[c]=r?0:i.startAngle,d[c]=i[c],Av[s?"updateProps":"initProps"](h,{shape:d},o,e)}return h}},Ew={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=bh(n,i),o=i.width>0?1:-1,a=i.height>0?1:-1;return{x:i.x+o*r/2,y:i.y+a*r/2,width:i.width-o*r,height:i.height-a*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}};ts(v(function(t,e,n){var i=[];e.eachSeriesByType(t,function(t){t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type&&i.push(t)});var r=Zs(i),o={};d(i,function(t){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),a=Gs(t),s=r[Ws(i)][a],l=s.offset,h=s.width,u=n.getOtherAxis(i),c=t.get("barMinHeight")||0;o[a]=o[a]||[],e.setLayout({offset:l,size:h});for(var d=e.mapDimension(u.dim),f=e.mapDimension(i.dim),p=As(e,d,f),g=u.isHorizontal(),m=i.onZero||p?u.toGlobalCoord(u.dataToCoord(0)):u.getGlobalExtent()[0],v=0,y=e.count();v<y;v++){var x=e.get(d,v),_=e.get(f,v);if(!isNaN(x)){var w=x>=0?"p":"n",b=m;p&&(o[a][_]||(o[a][_]={p:m,n:m}),b=o[a][_][w]);var S,M,I,T;if(g)S=b,M=(C=n.dataToPoint([x,_]))[1]+l,I=C[0]-m,T=h,Math.abs(I)<c&&(I=(I<0?-1:1)*c),p&&(o[a][_][w]+=I);else{var C=n.dataToPoint([_,x]);S=C[0]+l,M=b,I=h,T=C[1]-m,Math.abs(T)<c&&(T=(T<=0?-1:1)*c),p&&(o[a][_][w]+=T)}e.setItemLayout(v,{x:S,y:M,width:I,height:T})}}},this)},"bar")),es(function(t){t.eachSeriesByType("bar",function(t){t.getData().setVisual("legendSymbol","roundRect")})});var Nw=function(t,e,n){e=y(e)&&{coordDimensions:e}||o({},e);var i=t.getSource(),r=Kx(i,e),a=new Yx(r,t);return a.initData(i,n),a},Rw={updateSelectedMap:function(t){this._targetList=y(t)?t.slice():[],this._selectTargetMap=p(t||[],function(t,e){return t.set(e.name,e),t},N())},select:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);"single"===this.get("selectedMode")&&this._selectTargetMap.each(function(t){t.selected=!1}),n&&(n.selected=!0)},unSelect:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);n&&(n.selected=!1)},toggleSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);if(null!=n)return this[n.selected?"unSelect":"select"](t,e),n.selected},isSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return n&&n.selected}},Bw=as({type:"series.pie",init:function(t){Bw.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()},this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){Bw.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(t,e){return Nw(this,["value"])},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),n=[],i=0,r=t.count();i<r;i++)n.push({name:t.getName(i),value:t.get(e,i),selected:Uo(t,i,"selected")});return n},getDataParams:function(t){var e=this.getData(),n=Bw.superCall(this,"getDataParams",t),i=[];return e.each(e.mapDimension("value"),function(t){i.push(t)}),n.percent=Tr(i,t,e.hostModel.get("percentPrecision")),n.$vars.push("percent"),n},_defaultLabelLine:function(t){_n(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,label:{rotate:!1,show:!0,position:"outer"},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationEasing:"cubicOut"}});u(Bw,Rw);var Vw=Ih.prototype;Vw.updateData=function(t,e,n){function i(){s.stopAnimation(!0),s.animateTo({shape:{r:u.r+l.get("hoverOffset")}},300,"elasticOut")}function r(){s.stopAnimation(!0),s.animateTo({shape:{r:u.r}},300,"elasticOut")}var s=this.childAt(0),l=t.hostModel,h=t.getItemModel(e),u=t.getItemLayout(e),c=o({},u);c.label=null,n?(s.setShape(c),"scale"===l.getShallow("animationType")?(s.shape.r=u.r0,sr(s,{shape:{r:u.r}},l,e)):(s.shape.endAngle=u.startAngle,ar(s,{shape:{endAngle:u.endAngle}},l,e))):ar(s,{shape:c},l,e);var d=t.getItemVisual(e,"color");s.useStyle(a({lineJoin:"bevel",fill:d},h.getModel("itemStyle").getItemStyle())),s.hoverStyle=h.getModel("emphasis.itemStyle").getItemStyle();var f=h.getShallow("cursor");f&&s.attr("cursor",f),Mh(this,t.getItemLayout(e),l.isSelected(null,e),l.get("selectedOffset"),l.get("animation")),s.off("mouseover").off("mouseout").off("emphasis").off("normal"),h.get("hoverAnimation")&&l.isAnimationEnabled()&&s.on("mouseover",i).on("mouseout",r).on("emphasis",i).on("normal",r),this._updateLabel(t,e),qi(this)},Vw._updateLabel=function(t,e){var n=this.childAt(1),i=this.childAt(2),r=t.hostModel,o=t.getItemModel(e),a=t.getItemLayout(e).label,s=t.getItemVisual(e,"color");ar(n,{shape:{points:a.linePoints||[[a.x,a.y],[a.x,a.y],[a.x,a.y]]}},r,e),ar(i,{style:{x:a.x,y:a.y}},r,e),i.attr({rotation:a.rotation,origin:[a.x,a.y],z2:10});var l=o.getModel("label"),h=o.getModel("emphasis.label"),u=o.getModel("labelLine"),c=o.getModel("emphasis.labelLine"),s=t.getItemVisual(e,"color");$i(i.style,i.hoverStyle={},l,h,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:t.getName(e),autoColor:s,useInsideStyle:!!a.inside},{textAlign:a.textAlign,textVerticalAlign:a.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),i.ignore=i.normalIgnore=!l.get("show"),i.hoverIgnore=!h.get("show"),n.ignore=n.normalIgnore=!u.get("show"),n.hoverIgnore=!c.get("show"),n.setStyle({stroke:s,opacity:t.getItemVisual(e,"opacity")}),n.setStyle(u.getModel("lineStyle").getLineStyle()),n.hoverStyle=c.getModel("lineStyle").getLineStyle();var d=u.get("smooth");d&&!0===d&&(d=.4),n.setShape({smooth:d})},h(Ih,Jp);ia.extend({type:"pie",init:function(){var t=new Jp;this._sectorGroup=t},render:function(t,e,n,i){if(!i||i.from!==this.uid){var r=t.getData(),o=this._data,a=this.group,s=e.get("animation"),l=!o,h=t.get("animationType"),u=v(Sh,this.uid,t,s,n),c=t.get("selectedMode");if(r.diff(o).add(function(t){var e=new Ih(r,t);l&&"scale"!==h&&e.eachChild(function(t){t.stopAnimation(!0)}),c&&e.on("click",u),r.setItemGraphicEl(t,e),a.add(e)}).update(function(t,e){var n=o.getItemGraphicEl(e);n.updateData(r,t),n.off("click"),c&&n.on("click",u),a.add(n),r.setItemGraphicEl(t,n)}).remove(function(t){var e=o.getItemGraphicEl(t);a.remove(e)}).execute(),s&&l&&r.count()>0&&"scale"!==h){var d=r.getItemLayout(0),f=Math.max(n.getWidth(),n.getHeight())/2,p=m(a.removeClipPath,a);a.setClipPath(this._createClipPath(d.cx,d.cy,f,d.startAngle,d.clockwise,p,t))}this._data=r}},dispose:function(){},_createClipPath:function(t,e,n,i,r,o,a){var s=new hv({shape:{cx:t,cy:e,r0:0,r:n,startAngle:i,endAngle:i,clockwise:r}});return sr(s,{shape:{endAngle:i+(r?1:-1)*Math.PI*2}},a,o),s},containPoint:function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,r=t[1]-n.cy,o=Math.sqrt(i*i+r*r);return o<=n.r&&o>=n.r0}}});var Fw=function(t,e,n,i){var r,o,a=t.getData(),s=[],l=!1;a.each(function(n){var i,h,u,c,d=a.getItemLayout(n),f=a.getItemModel(n),p=f.getModel("label"),g=p.get("position")||f.get("emphasis.label.position"),m=f.getModel("labelLine"),v=m.get("length"),y=m.get("length2"),x=(d.startAngle+d.endAngle)/2,_=Math.cos(x),w=Math.sin(x);r=d.cx,o=d.cy;var b="inside"===g||"inner"===g;if("center"===g)i=d.cx,h=d.cy,c="center";else{var S=(b?(d.r+d.r0)/2*_:d.r*_)+r,M=(b?(d.r+d.r0)/2*w:d.r*w)+o;if(i=S+3*_,h=M+3*w,!b){var I=S+_*(v+e-d.r),T=M+w*(v+e-d.r),C=I+(_<0?-1:1)*y,D=T;i=C+(_<0?-5:5),h=D,u=[[S,M],[I,T],[C,D]]}c=b?"center":_>0?"left":"right"}var A=p.getFont(),k=p.get("rotate")?_<0?-x+Math.PI:-x:0,P=ce(t.getFormattedLabel(n,"normal")||a.getName(n),A,c,"top");l=!!k,d.label={x:i,y:h,position:g,height:P.height,len:v,len2:y,linePoints:u,textAlign:c,verticalAlign:"middle",rotation:k,inside:b},b||s.push(d.label)}),!l&&t.get("avoidLabelOverlap")&&Ch(s,r,o,e,n,i)},Hw=2*Math.PI,Gw=Math.PI/180;!function(t,e){d(e,function(e){e.update="updateView",Ja(e,function(n,i){var r={};return i.eachComponent({mainType:"series",subType:t,query:n},function(t){t[e.method]&&t[e.method](n.name,n.dataIndex);var i=t.getData();i.each(function(e){var n=i.getName(e);r[n]=t.isSelected(n)||!1})}),{name:n.name,selected:r}})})}("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),es(function(t){return{getTargetSeries:function(e){var n={},i=N();return e.eachSeriesByType(t,function(t){t.__paletteScope=n,i.set(t.uid,t)}),i},reset:function(t,e){var n=t.getRawData(),i={},r=t.getData();r.each(function(t){var e=r.getRawIndex(t);i[e]=t}),n.each(function(e){var o=i[e],a=null!=o&&r.getItemVisual(o,"color",!0);if(a)n.setItemVisual(e,"color",a);else{var s=n.getItemModel(e).get("itemStyle.color")||t.getColorFromPalette(n.getName(e)||e+"",t.__paletteScope,n.count());n.setItemVisual(e,"color",s),null!=o&&r.setItemVisual(o,"color",s)}})}}}("pie")),ts(v(function(t,e,n,i){e.eachSeriesByType(t,function(t){var e=t.getData(),i=e.mapDimension("value"),r=t.get("center"),o=t.get("radius");y(o)||(o=[0,o]),y(r)||(r=[r,r]);var a=n.getWidth(),s=n.getHeight(),l=Math.min(a,s),h=_r(r[0],a),u=_r(r[1],s),c=_r(o[0],l/2),d=_r(o[1],l/2),f=-t.get("startAngle")*Gw,p=t.get("minAngle")*Gw,g=0;e.each(i,function(t){!isNaN(t)&&g++});var m=e.getSum(i),v=Math.PI/(m||g)*2,x=t.get("clockwise"),_=t.get("roseType"),w=t.get("stillShowZeroSum"),b=e.getDataExtent(i);b[0]=0;var S=Hw,M=0,I=f,T=x?1:-1;if(e.each(i,function(t,n){var i;if(isNaN(t))e.setItemLayout(n,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:x,cx:h,cy:u,r0:c,r:_?NaN:d});else{(i="area"!==_?0===m&&w?v:t*v:Hw/g)<p?(i=p,S-=p):M+=t;var r=I+T*i;e.setItemLayout(n,{angle:i,startAngle:I,endAngle:r,clockwise:x,cx:h,cy:u,r0:c,r:_?xr(t,b,[c,d]):d}),I=r}}),S<Hw&&g)if(S<=.001){var C=Hw/g;e.each(i,function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n);i.angle=C,i.startAngle=f+T*n*C,i.endAngle=f+T*(n+1)*C}})}else v=S/M,I=f,e.each(i,function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n),r=i.angle===p?p:t*v;i.startAngle=I,i.endAngle=I+T*r,I+=T*r}});Fw(t,d,a,s)})},"pie")),Qa(function(t){return{seriesType:t,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var i=t.getData();i.filterSelf(function(t){for(var e=i.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(e))return!1;return!0})}}}}("pie")),Ry.extend({type:"series.scatter",dependencies:["grid","polar","geo","singleAxis","calendar"],getInitialData:function(t,e){return ks(this.getSource(),this)},brushSelector:"point",getProgressive:function(){var t=this.option.progressive;return null==t?this.option.large?5e3:this.get("progressive"):t},getProgressiveThreshold:function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?1e4:this.get("progressiveThreshold"):t},defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},progressive:null}});var Ww=Ai({shape:{points:null},symbolProxy:null,buildPath:function(t,e){var n=e.points,i=e.size,r=this.symbolProxy,o=r.shape;if(!((t.getContext?t.getContext():t)&&i[0]<4))for(var a=0;a<n.length;){var s=n[a++],l=n[a++];isNaN(s)||isNaN(l)||(o.x=s-i[0]/2,o.y=l-i[1]/2,o.width=i[0],o.height=i[1],r.buildPath(t,o,!0))}},afterBrush:function(t){var e=this.shape,n=e.points,i=e.size;if(i[0]<4){this.setTransform(t);for(var r=0;r<n.length;){var o=n[r++],a=n[r++];isNaN(o)||isNaN(a)||t.fillRect(o-i[0]/2,a-i[1]/2,i[0],i[1])}this.restoreTransform(t)}},findDataIndex:function(t,e){for(var n=this.shape,i=n.points,r=n.size,o=Math.max(r[0],4),a=Math.max(r[1],4),s=i.length/2-1;s>=0;s--){var l=2*s,h=i[l]-o/2,u=i[l+1]-a/2;if(t>=h&&e>=u&&t<=h+o&&e<=u+a)return s}return-1}}),Zw=Dh.prototype;Zw.isPersistent=function(){return!this._incremental},Zw.updateData=function(t){this.group.removeAll();var e=new Ww({rectHover:!0,cursor:"default"});e.setShape({points:t.getLayout("symbolPoints")}),this._setCommon(e,t),this.group.add(e),this._incremental=null},Zw.updateLayout=function(t){if(!this._incremental){var e=t.getLayout("symbolPoints");this.group.eachChild(function(t){if(null!=t.startIndex){var n=2*(t.endIndex-t.startIndex),i=4*t.startIndex*2;e=new Float32Array(e.buffer,i,n)}t.setShape("points",e)})}},Zw.incrementalPrepareUpdate=function(t){this.group.removeAll(),this._clearIncremental(),t.count()>2e6?(this._incremental||(this._incremental=new Di({silent:!0})),this.group.add(this._incremental)):this._incremental=null},Zw.incrementalUpdate=function(t,e){var n;this._incremental?(n=new Ww,this._incremental.addDisplayable(n,!0)):((n=new Ww({rectHover:!0,cursor:"default",startIndex:t.start,endIndex:t.end})).incremental=!0,this.group.add(n)),n.setShape({points:e.getLayout("symbolPoints")}),this._setCommon(n,e,!!this._incremental)},Zw._setCommon=function(t,e,n){var i=e.hostModel,r=e.getVisual("symbolSize");t.setShape("size",r instanceof Array?r:[r,r]),t.symbolProxy=el(e.getVisual("symbol"),0,0,0,0),t.setColor=t.symbolProxy.setColor;var o=t.shape.size[0]<4;t.useStyle(i.getModel("itemStyle").getItemStyle(o?["color","shadowBlur","shadowColor"]:["color"]));var a=e.getVisual("color");a&&t.setColor(a),n||(t.seriesIndex=i.seriesIndex,t.on("mousemove",function(e){t.dataIndex=null;var n=t.findDataIndex(e.offsetX,e.offsetY);n>=0&&(t.dataIndex=n+(t.startIndex||0))}))},Zw.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},Zw._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()},ss({type:"scatter",render:function(t,e,n){var i=t.getData();this._updateSymbolDraw(i,t).updateData(i),this._finished=!0},incrementalPrepareRender:function(t,e,n){var i=t.getData();this._updateSymbolDraw(i,t).incrementalPrepareUpdate(i),this._finished=!1},incrementalRender:function(t,e,n){this._symbolDraw.incrementalUpdate(t,e.getData()),this._finished=t.end===e.getData().count()},updateTransform:function(t,e,n){var i=t.getData();if(this.group.dirty(),!this._finished||i.count()>1e4||!this._symbolDraw.isPersistent())return{update:!0};var r=tw().reset(t);r.progress&&r.progress({start:0,end:i.count()},i),this._symbolDraw.updateLayout(i)},_updateSymbolDraw:function(t,e){var n=this._symbolDraw,i=e.pipelineContext.large;return n&&i===this._isLargeDraw||(n&&n.remove(),n=this._symbolDraw=i?new Dh:new fl,this._isLargeDraw=i,this.group.removeAll()),this.group.add(n.group),n},remove:function(t,e){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},dispose:function(){}}),es(J_("scatter","circle")),ts(tw("scatter")),Ka(function(t){var e=t.graphic;y(e)?e[0]&&e[0].elements?t.graphic=[t.graphic[0]]:t.graphic=[{elements:e}]:e&&!e.elements&&(t.graphic=[{elements:[e]}])});var Uw=rs({type:"graphic",defaultOption:{elements:[],parentId:null},_elOptionsToUpdate:null,mergeOption:function(t){var e=this.option.elements;this.option.elements=null,Uw.superApply(this,"mergeOption",arguments),this.option.elements=e},optionUpdated:function(t,e){var n=this.option,i=(e?n:t).elements,r=n.elements=e?[]:n.elements,o=[];this._flatten(i,o);var a=Sn(r,o);Mn(a);var s=this._elOptionsToUpdate=[];d(a,function(t,e){var n=t.option;n&&(s.push(n),Oh(t,n),zh(r,e,n),Eh(r[e],n))},this);for(var l=r.length-1;l>=0;l--)null==r[l]?r.splice(l,1):delete r[l].$action},_flatten:function(t,e,n){d(t,function(t){if(t){n&&(t.parentOption=n),e.push(t);var i=t.children;"group"===t.type&&i&&this._flatten(i,e,t),delete t.children}},this)},useElOptionsToUpdate:function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t}});os({type:"graphic",init:function(t,e){this._elMap=N(),this._lastGraphicModel},render:function(t,e,n){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t,n),this._relocate(t,n)},_updateElements:function(t,e){var n=t.useElOptionsToUpdate();if(n){var i=this._elMap,r=this.group;d(n,function(t){var e=t.$action,n=t.id,o=i.get(n),a=t.parentId,s=null!=a?i.get(a):r;if("text"===t.type){var l=t.style;t.hv&&t.hv[1]&&(l.textVerticalAlign=l.textBaseline=null),!l.hasOwnProperty("textFill")&&l.fill&&(l.textFill=l.fill),!l.hasOwnProperty("textStroke")&&l.stroke&&(l.textStroke=l.stroke)}var h=Ph(t);e&&"merge"!==e?"replace"===e?(kh(o,i),Ah(n,s,h,i)):"remove"===e&&kh(o,i):o?o.attr(h):Ah(n,s,h,i);var u=i.get(n);u&&(u.__ecGraphicWidth=t.width,u.__ecGraphicHeight=t.height)})}},_relocate:function(t,e){for(var n=t.option.elements,i=this.group,r=this._elMap,o=n.length-1;o>=0;o--){var a=n[o],s=r.get(a.id);if(s){var l=s.parent;Wr(s,a,l===i?{width:e.getWidth(),height:e.getHeight()}:{width:l.__ecGraphicWidth||0,height:l.__ecGraphicHeight||0},null,{hv:a.hv,boundingMode:a.bounding})}}},_clear:function(){var t=this._elMap;t.each(function(e){kh(e,t)}),this._elMap=N()},dispose:function(){this._clear()}});var Xw=function(t,e){var n,i=[],r=t.seriesIndex;if(null==r||!(n=e.getSeriesByIndex(r)))return{point:[]};var o=n.getData(),a=Cn(o,t);if(null==a||a<0||y(a))return{point:[]};var s=o.getItemGraphicEl(a),l=n.coordinateSystem;if(n.getTooltipPosition)i=n.getTooltipPosition(a)||[];else if(l&&l.dataToPoint)i=l.dataToPoint(o.getValues(f(l.dimensions,function(t){return o.mapDimension(t)}),a,!0))||[];else if(s){var h=s.getBoundingRect().clone();h.applyTransform(s.transform),i=[h.x+h.width/2,h.y+h.height/2]}return{point:i,el:s}},jw=d,Yw=v,qw=Dn(),$w=(rs({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}}),Dn()),Kw=d,Qw=os({type:"axisPointer",render:function(t,e,n){var i=e.getComponent("tooltip"),r=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";Xh("axisPointer",n,function(t,e,n){"none"!==r&&("leave"===t||r.indexOf(t)>=0)&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){Qh(e.getZr(),"axisPointer"),Qw.superApply(this._model,"remove",arguments)},dispose:function(t,e){Qh("axisPointer",e),Qw.superApply(this._model,"dispose",arguments)}}),Jw=Dn(),tb=n,eb=m;(Jh.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,i){var r=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,i||this._lastValue!==r||this._lastStatus!==o){this._lastValue=r,this._lastStatus=o;var a=this._group,s=this._handle;if(!o||"hide"===o)return a&&a.hide(),void(s&&s.hide());a&&a.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,n);var h=l.graphicKey;h!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=h;var u=this._moveAnimation=this.determineAnimation(t,e);if(a){var c=v(tu,e,u);this.updatePointerEl(a,l,c,e),this.updateLabelEl(a,l,c,e)}else a=this._group=new Jp,this.createPointerEl(a,l,t,e),this.createLabelEl(a,l,t,e),n.getZr().add(a);ru(a,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"===n||null==n){var a=this.animationThreshold;if(r&&i.getBandWidth()>a)return!0;if(o){var s=uh(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1}return!0===n},makeElOption:function(t,e,n,i,r){},createPointerEl:function(t,e,n,i){var r=e.pointer;if(r){var o=Jw(t).pointerEl=new Av[r.type](tb(e.pointer));t.add(o)}},createLabelEl:function(t,e,n,i){if(e.label){var r=Jw(t).labelEl=new gv(tb(e.label));t.add(r),nu(r,i)}},updatePointerEl:function(t,e,n){var i=Jw(t).pointerEl;i&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){var r=Jw(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{shape:e.label.shape,position:e.label.position}),nu(r,i))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e=this._axisPointerModel,n=this._api.getZr(),i=this._handle,r=e.getModel("handle"),o=e.get("status");if(!r.get("show")||!o||"hide"===o)return i&&n.remove(i),void(this._handle=null);var a;this._handle||(a=!0,i=this._handle=fr(r.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){Ag(t.event)},onmousedown:eb(this._onHandleDragMove,this,0,0),drift:eb(this._onHandleDragMove,this),ondragend:eb(this._onHandleDragEnd,this)}),n.add(i)),ru(i,e,!1);var s=["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];i.setStyle(r.getItemStyle(null,s));var l=r.get("size");y(l)||(l=[l,l]),i.attr("scale",[l[0]/2,l[1]/2]),ha(this,"_doDispatchAxisPointer",r.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,a)}},_moveHandleToValue:function(t,e){tu(this._axisPointerModel,!e&&this._moveAnimation,this._handle,iu(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n=this._handle;if(n){this._dragging=!0;var i=this.updateHandleTransform(iu(n),[t,e],this._axisModel,this._axisPointerModel);this._payloadInfo=i,n.stopAnimation(),n.attr(iu(i)),Jw(n).lastProp=null,this._doDispatchAxisPointer()}},_doDispatchAxisPointer:function(){if(this._handle){var t=this._payloadInfo,e=this._axisModel;this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]})}},_onHandleDragEnd:function(t){if(this._dragging=!1,this._handle){var e=this._axisPointerModel.get("value");this._moveHandleToValue(e),this._api.dispatchAction({type:"hideTip"})}},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return n=n||0,{x:t[n],y:t[1-n],width:e[n],height:e[1-n]}}}).constructor=Jh,En(Jh);var nb=Jh.extend({makeElOption:function(t,e,n,i,r){var o=n.axis,a=o.grid,s=i.get("type"),l=fu(a,o).getOtherAxis(o).getGlobalExtent(),h=o.toGlobalCoord(o.dataToCoord(e,!0));if(s&&"none"!==s){var u=ou(i),c=ib[s](o,h,l,u);c.style=u,t.graphicKey=c.type,t.pointer=c}uu(e,t,mh(a.model,n),n,i,r)},getHandleTransform:function(t,e,n){var i=mh(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:hu(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n,i){var r=n.axis,o=r.grid,a=r.getGlobalExtent(!0),s=fu(o,r).getOtherAxis(r).getGlobalExtent(),l="x"===r.dim?0:1,h=t.position;h[l]+=e[l],h[l]=Math.min(a[1],h[l]),h[l]=Math.max(a[0],h[l]);var u=(s[1]+s[0])/2,c=[u,u];c[l]=h[l];var d=[{verticalAlign:"middle"},{align:"center"}];return{position:h,rotation:t.rotation,cursorPoint:c,tooltipOption:d[l]}}}),ib={line:function(t,e,n,i){var r=cu([e,n[0]],[e,n[1]],pu(t));return zi({shape:r,style:i}),{type:"Line",shape:r}},shadow:function(t,e,n,i){var r=t.getBandWidth(),o=n[1]-n[0];return{type:"Rect",shape:du([e-r/2,n[0]],[r,o],pu(t))}}};Mw.registerAxisPointerClass("CartesianAxisPointer",nb),Ka(function(t){if(t){(!t.axisPointer||0===t.axisPointer.length)&&(t.axisPointer={});var e=t.axisPointer.link;e&&!y(e)&&(t.axisPointer.link=[e])}}),Qa(mx.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=ih(t,e)}),Ja({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},function(t,e,n){var i=t.currTrigger,r=[t.x,t.y],o=t,a=t.dispatchAction||m(n.dispatchAction,n),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){Uh(r)&&(r=Xw({seriesIndex:o.seriesIndex,dataIndex:o.dataIndex},e).point);var l=Uh(r),h=o.axesInfo,u=s.axesInfo,c="leave"===i||Uh(r),d={},f={},p={list:[],map:{}},g={showPointer:Yw(Bh,f),showTooltip:Yw(Vh,p)};jw(s.coordSysMap,function(t,e){var n=l||t.containPoint(r);jw(s.coordSysAxesInfo[e],function(t,e){var i=t.axis,o=Wh(h,t);if(!c&&n&&(!h||o)){var a=o&&o.value;null!=a||l||(a=i.pointToData(r)),null!=a&&Nh(t,a,g,!1,d)}})});var v={};return jw(u,function(t,e){var n=t.linkGroup;n&&!f[e]&&jw(n.axesInfo,function(e,i){var r=f[i];if(e!==t&&r){var o=r.value;n.mapper&&(o=t.axis.scale.parse(n.mapper(o,Zh(e),Zh(t)))),v[t.key]=o}})}),jw(v,function(t,e){Nh(u[e],t,g,!0,d)}),Fh(f,u,d),Hh(p,r,t,a),Gh(u,0,n),d}}),rs({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:8,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var rb=d,ob=zr,ab=["","-webkit-","-moz-","-o-"];yu.prototype={constructor:yu,_enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),n=t.style;"absolute"!==n.position&&"absolute"!==e.position&&(n.position="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+vu(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var n,i=this._zr;i&&i.painter&&(n=i.painter.getViewportRootOffset())&&(t+=n.offsetLeft,e+=n.offsetTop);var r=this.el.style;r.left=t+"px",r.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(m(this.hide,this),t)):this.hide())},isShow:function(){return this._show}};var sb=m,lb=d,hb=_r,ub=new gv({shape:{x:-1,y:-1,width:2,height:2}});os({type:"tooltip",init:function(t,e){if(!Kf.node){var n=new yu(e.getDom(),e);this._tooltipContent=n}},render:function(t,e,n){if(!Kf.node&&!Kf.wxa){this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent");var i=this._tooltipContent;i.update(),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow()}},_initGlobalListener:function(){var t=this._tooltipModel.get("triggerOn");Xh("itemTooltip",this._api,sb(function(e,n,i){"none"!==t&&(t.indexOf(e)>=0?this._tryShow(n,i):"leave"===e&&this._hide(i))},this))},_keepShow:function(){var t=this._tooltipModel,e=this._ecModel,n=this._api;if(null!=this._lastX&&null!=this._lastY&&"none"!==t.get("triggerOn")){var i=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.manuallyShowTip(t,e,n,{x:i._lastX,y:i._lastY})})}},manuallyShowTip:function(t,e,n,i){if(i.from!==this.uid&&!Kf.node){var r=_u(i,n);this._ticket="";var o=i.dataByCoordSys;if(i.tooltip&&null!=i.x&&null!=i.y){var a=ub;a.position=[i.x,i.y],a.update(),a.tooltip=i.tooltip,this._tryShow({offsetX:i.x,offsetY:i.y,target:a},r)}else if(o)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,event:{},dataByCoordSys:i.dataByCoordSys,tooltipOption:i.tooltipOption},r);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var s=Xw(i,e),l=s.point[0],h=s.point[1];null!=l&&null!=h&&this._tryShow({offsetX:l,offsetY:h,position:i.position,target:s.el,event:{}},r)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target,event:{}},r))}},manuallyHideTip:function(t,e,n,i){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,i.from!==this.uid&&this._hide(_u(i,n))},_manuallyAxisShowTip:function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){var s=e.getSeriesByIndex(r);if(s&&"axis"===(t=xu([s.getData().getItemModel(o),s,(s.coordinateSystem||{}).model,t])).get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}},_tryShow:function(t,e){var n=t.target;if(this._tooltipModel){this._lastX=t.offsetX,this._lastY=t.offsetY;var i=t.dataByCoordSys;i&&i.length?this._showAxisTooltip(i,t):n&&null!=n.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,n,e)):n&&n.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,n,e)):(this._lastDataByCoordSys=null,this._hide(e))}},_showOrMove:function(t,e){var n=t.get("showDelay");e=m(e,this),clearTimeout(this._showTimout),n>0?this._showTimout=setTimeout(e,n):e()},_showAxisTooltip:function(t,e){var n=this._ecModel,i=this._tooltipModel,r=[e.offsetX,e.offsetY],o=[],a=[],s=xu([e.tooltipOption,i]);lb(t,function(t){lb(t.dataByAxis,function(t){var e=n.getComponent(t.axisDim+"Axis",t.axisIndex),i=t.value,r=[];if(e&&null!=i){var s=lu(i,e.axis,n,t.seriesDataIndices,t.valueLabelOpt);d(t.seriesDataIndices,function(o){var l=n.getSeriesByIndex(o.seriesIndex),h=o.dataIndexInside,u=l&&l.getDataParams(h);u.axisDim=t.axisDim,u.axisIndex=t.axisIndex,u.axisType=t.axisType,u.axisId=t.axisId,u.axisValue=Js(e.axis,i),u.axisValueLabel=s,u&&(a.push(u),r.push(l.formatTooltip(h,!0)))});var l=s;o.push((l?Er(l)+"<br />":"")+r.join("<br />"))}})},this),o.reverse(),o=o.join("<br /><br />");var l=e.position;this._showOrMove(s,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(s,l,r[0],r[1],this._tooltipContent,a):this._showTooltipContent(s,o,a,Math.random(),r[0],r[1],l)})},_showSeriesItemTooltip:function(t,e,n){var i=this._ecModel,r=e.seriesIndex,o=i.getSeriesByIndex(r),a=e.dataModel||o,s=e.dataIndex,l=e.dataType,h=a.getData(),u=xu([h.getItemModel(s),a,o&&(o.coordinateSystem||{}).model,this._tooltipModel]),c=u.get("trigger");if(null==c||"item"===c){var d=a.getDataParams(s,l),f=a.formatTooltip(s,!1,l),p="item_"+a.name+"_"+s;this._showOrMove(u,function(){this._showTooltipContent(u,f,d,p,t.offsetX,t.offsetY,t.position,t.target)}),n({type:"showTip",dataIndexInside:s,dataIndex:h.getRawIndex(s),seriesIndex:r,from:this.uid})}},_showComponentItemTooltip:function(t,e,n){var i=e.tooltip;if("string"==typeof i){var r=i;i={content:r,formatter:r}}var o=new pr(i,this._tooltipModel,this._ecModel),a=o.get("content"),s=Math.random();this._showOrMove(o,function(){this._showTooltipContent(o,a,o.get("formatterParams")||{},s,t.offsetX,t.offsetY,t.position,e)}),n({type:"showTip",from:this.uid})},_showTooltipContent:function(t,e,n,i,r,o,a,s){if(this._ticket="",t.get("showContent")&&t.get("show")){var l=this._tooltipContent,h=t.get("formatter");a=a||t.get("position");var u=e;if(h&&"string"==typeof h)u=Nr(h,n,!0);else if("function"==typeof h){var c=sb(function(e,i){e===this._ticket&&(l.setContent(i),this._updatePosition(t,a,r,o,l,n,s))},this);this._ticket=i,u=h(n,i,c)}l.setContent(u),l.show(t),this._updatePosition(t,a,r,o,l,n,s)}},_updatePosition:function(t,e,n,i,r,o,a){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var h=r.getSize(),u=t.get("align"),c=t.get("verticalAlign"),d=a&&a.getBoundingRect().clone();if(a&&d.applyTransform(a.transform),"function"==typeof e&&(e=e([n,i],o,r.el,d,{viewSize:[s,l],contentSize:h.slice()})),y(e))n=hb(e[0],s),i=hb(e[1],l);else if(w(e)){e.width=h[0],e.height=h[1];var f=Gr(e,{width:s,height:l});n=f.x,i=f.y,u=null,c=null}else"string"==typeof e&&a?(n=(p=Mu(e,d,h))[0],i=p[1]):(n=(p=wu(n,i,r.el,s,l,u?null:20,c?null:20))[0],i=p[1]);if(u&&(n-=Iu(u)?h[0]/2:"right"===u?h[0]:0),c&&(i-=Iu(c)?h[1]/2:"bottom"===c?h[1]:0),t.get("confine")){var p=bu(n,i,r.el,s,l);n=p[0],i=p[1]}r.moveTo(n,i)},_updateContentNotChangedOnAxis:function(t){var e=this._lastDataByCoordSys,n=!!e&&e.length===t.length;return n&&lb(e,function(e,i){var r=e.dataByAxis||{},o=(t[i]||{}).dataByAxis||[];(n&=r.length===o.length)&&lb(r,function(t,e){var i=o[e]||{},r=t.seriesDataIndices||[],a=i.seriesDataIndices||[];(n&=t.value===i.value&&t.axisType===i.axisType&&t.axisId===i.axisId&&r.length===a.length)&&lb(r,function(t,e){var i=a[e];n&=t.seriesIndex===i.seriesIndex&&t.dataIndex===i.dataIndex})})}),this._lastDataByCoordSys=t,!!n},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){Kf.node||(this._tooltipContent.hide(),Qh("itemTooltip",e))}}),Ja({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),Ja({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var cb=rs({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{}},mergeOption:function(t){cb.superCall(this,"mergeOption",t)},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}!e&&this.select(t[0].get("name"))}},_updateData:function(t){var e=[],n=[];t.eachRawSeries(function(i){var r=i.name;n.push(r);var o;if(i.legendDataProvider){var a=i.legendDataProvider(),s=a.mapArray(a.getName);t.isSeriesFiltered(i)||(n=n.concat(s)),s.length?e=e.concat(s):o=!0}else o=!0;o&&In(i)&&e.push(i.name)}),this._availableNames=n;var i=f(this.get("data")||e,function(t){return"string"!=typeof t&&"number"!=typeof t||(t={name:t}),new pr(t,this,this.ecModel)},this);this._data=i},getData:function(){return this._data},select:function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&d(this._data,function(t){e[t.get("name")]=!1}),e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&l(this._availableNames,t)>=0},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",textStyle:{color:"#333"},selectedMode:!0,tooltip:{show:!1}}});Ja("legendToggleSelect","legendselectchanged",v(Tu,"toggleSelected")),Ja("legendSelect","legendselected",v(Tu,"select")),Ja("legendUnSelect","legendunselected",v(Tu,"unSelect"));var db=v,fb=d,pb=Jp,gb=os({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new pb),this._backgroundEl},getContentGroup:function(){return this._contentGroup},render:function(t,e,n){if(this.resetInner(),t.get("show",!0)){var i=t.get("align");i&&"auto"!==i||(i="right"===t.get("left")&&"vertical"===t.get("orient")?"right":"left"),this.renderInner(i,t,e,n);var r=t.getBoxLayoutParams(),o={width:n.getWidth(),height:n.getHeight()},s=t.get("padding"),l=Gr(r,o,s),h=this.layoutInner(t,i,l),u=Gr(a({width:h.width,height:h.height},r),o,s);this.group.attr("position",[u.x-h.x,u.y-h.y]),this.group.add(this._backgroundEl=Du(h,t))}},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl)},renderInner:function(t,e,n,i){var r=this.getContentGroup(),o=N(),a=e.get("selectedMode");fb(e.getData(),function(s,l){var h=s.get("name");if(this.newlineDisabled||""!==h&&"\n"!==h){var u=n.getSeriesByName(h)[0];if(!o.get(h))if(u){var c=u.getData(),d=c.getVisual("color");"function"==typeof d&&(d=d(u.getDataParams(0)));var f=c.getVisual("legendSymbol")||"roundRect",p=c.getVisual("symbol");this._createItem(h,l,s,e,f,p,t,d,a).on("click",db(Au,h,i)).on("mouseover",db(ku,u,null,i)).on("mouseout",db(Pu,u,null,i)),o.set(h,!0)}else n.eachRawSeries(function(n){if(!o.get(h)&&n.legendDataProvider){var r=n.legendDataProvider(),u=r.indexOfName(h);if(u<0)return;var c=r.getItemVisual(u,"color");this._createItem(h,l,s,e,"roundRect",null,t,c,a).on("click",db(Au,h,i)).on("mouseover",db(ku,n,h,i)).on("mouseout",db(Pu,n,h,i)),o.set(h,!0)}},this)}else r.add(new pb({newline:!0}))},this)},_createItem:function(t,e,n,i,r,a,s,l,h){var u=i.get("itemWidth"),c=i.get("itemHeight"),d=i.get("inactiveColor"),f=i.isSelected(t),p=new pb,g=n.getModel("textStyle"),m=n.get("icon"),v=n.getModel("tooltip"),y=v.parentModel;if(r=m||r,p.add(el(r,0,0,u,c,f?l:d,!0)),!m&&a&&(a!==r||"none"==a)){var x=.8*c;"none"===a&&(a="circle"),p.add(el(a,(u-x)/2,(c-x)/2,x,x,f?l:d))}var _="left"===s?u+5:-5,w=s,b=i.get("formatter"),S=t;"string"==typeof b&&b?S=b.replace("{name}",null!=t?t:""):"function"==typeof b&&(S=b(t)),p.add(new ov({style:Ki({},g,{text:S,x:_,y:c/2,textFill:f?g.getTextColor():d,textAlign:w,textVerticalAlign:"middle"})}));var M=new gv({shape:p.getBoundingRect(),invisible:!0,tooltip:v.get("show")?o({content:t,formatter:y.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},v.option):null});return p.add(M),p.eachChild(function(t){t.silent=!0}),M.silent=!h,this.getContentGroup().add(p),qi(p),p.__legendDataIndex=e,p},layoutInner:function(t,e,n){var i=this.getContentGroup();qv(t.get("orient"),i,t.get("itemGap"),n.width,n.height);var r=i.getBoundingRect();return i.attr("position",[-r.x,-r.y]),this.group.getBoundingRect()}});Qa(function(t){var e=t.findComponents({mainType:"legend"});e&&e.length&&t.filterSeries(function(t){for(var n=0;n<e.length;n++)if(!e[n].isSelected(t.name))return!1;return!0})}),Qv.registerSubTypeDefaulter("legend",function(){return"plain"});var mb=cb.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,n,i){var r=Ur(t);mb.superCall(this,"init",t,e,n,i),Lu(this,t,r)},mergeOption:function(t,e){mb.superCall(this,"mergeOption",t,e),Lu(this,this.option,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}}}),vb=Jp,yb=["width","height"],xb=["x","y"],_b=gb.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){_b.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new vb),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new vb),this._showController},resetInner:function(){_b.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,e,n,i){function r(t,n){var r=t+"DataIndex",l=fr(e.get("pageIcons",!0)[e.getOrient().name][n],{onclick:m(o._pageGo,o,r,e,i)},{x:-s[0]/2,y:-s[1]/2,width:s[0],height:s[1]});l.name=t,a.add(l)}var o=this;_b.superCall(this,"renderInner",t,e,n,i);var a=this._controllerGroup,s=e.get("pageIconSize",!0);y(s)||(s=[s,s]),r("pagePrev",0);var l=e.getModel("pageTextStyle");a.add(new ov({name:"pageText",style:{textFill:l.getTextColor(),font:l.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),r("pageNext",1)},layoutInner:function(t,e,n){var i=this.getContentGroup(),r=this._containerGroup,o=this._controllerGroup,a=t.getOrient().index,s=yb[a],l=yb[1-a],h=xb[1-a];qv(t.get("orient"),i,t.get("itemGap"),a?n.width:null,a?null:n.height),qv("horizontal",o,t.get("pageButtonItemGap",!0));var u=i.getBoundingRect(),c=o.getBoundingRect(),d=this._showController=u[s]>n[s],f=[-u.x,-u.y];f[a]=i.position[a];var p=[0,0],g=[-c.x,-c.y],m=C(t.get("pageButtonGap",!0),t.get("itemGap",!0));d&&("end"===t.get("pageButtonPosition",!0)?g[a]+=n[s]-c[s]:p[a]+=c[s]+m),g[1-a]+=u[l]/2-c[l]/2,i.attr("position",f),r.attr("position",p),o.attr("position",g);var v=this.group.getBoundingRect();if((v={x:0,y:0})[s]=d?n[s]:u[s],v[l]=Math.max(u[l],c[l]),v[h]=Math.min(0,c[h]+g[1-a]),r.__rectSize=n[s],d){var y={x:0,y:0};y[s]=Math.max(n[s]-c[s]-m,0),y[l]=v[l],r.setClipPath(new gv({shape:y})),r.__rectSize=y[s]}else o.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var x=this._getPageInfo(t);return null!=x.pageIndex&&ar(i,{position:x.contentPosition},!!d&&t),this._updatePageInfoView(t,x),v},_pageGo:function(t,e,n){var i=this._getPageInfo(e)[t];null!=i&&n.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:e.id})},_updatePageInfoView:function(t,e){var n=this._controllerGroup;d(["pagePrev","pageNext"],function(i){var r=null!=e[i+"DataIndex"],o=n.childOfName(i);o&&(o.setStyle("fill",r?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),o.cursor=r?"pointer":"default")});var i=n.childOfName("pageText"),r=t.get("pageFormatter"),o=e.pageIndex,a=null!=o?o+1:0,s=e.pageCount;i&&r&&i.setStyle("text",_(r)?r.replace("{current}",a).replace("{total}",s):r({current:a,total:s}))},_getPageInfo:function(t){function e(t){var e=t.getBoundingRect().clone();return e[f]+=t.position[u],e}var n,i,r,o,a=t.get("scrollDataIndex",!0),s=this.getContentGroup(),l=s.getBoundingRect(),h=this._containerGroup.__rectSize,u=t.getOrient().index,c=yb[u],d=yb[1-u],f=xb[u],p=s.position.slice();this._showController?s.eachChild(function(t){t.__legendDataIndex===a&&(o=t)}):o=s.childAt(0);var g=h?Math.ceil(l[c]/h):0;if(o){var m=o.getBoundingRect(),v=o.position[u]+m[f];p[u]=-v-l[f],n=Math.floor(g*(v+m[f]+h/2)/l[c]),n=l[c]&&g?Math.max(0,Math.min(g-1,n)):-1;var y={x:0,y:0};y[c]=h,y[d]=l[d],y[f]=-p[u]-l[f];var x,_=s.children();if(s.eachChild(function(t,n){var i=e(t);i.intersect(y)&&(null==x&&(x=n),r=t.__legendDataIndex),n===_.length-1&&i[f]+i[c]<=y[f]+y[c]&&(r=null)}),null!=x){var w=e(_[x]);if(y[f]=w[f]+w[c]-y[c],x<=0&&w[f]>=y[f])i=null;else{for(;x>0&&e(_[x-1]).intersect(y);)x--;i=_[x].__legendDataIndex}}}return{contentPosition:p,pageIndex:n,pageCount:g,pagePrevDataIndex:i,pageNextDataIndex:r}}});Ja("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})}),rs({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),os({type:"title",render:function(t,e,n){if(this.group.removeAll(),t.get("show")){var i=this.group,r=t.getModel("textStyle"),o=t.getModel("subtextStyle"),a=t.get("textAlign"),s=t.get("textBaseline"),l=new ov({style:Ki({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10}),h=l.getBoundingRect(),u=t.get("subtext"),c=new ov({style:Ki({},o,{text:u,textFill:o.getTextColor(),y:h.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink");l.silent=!d,c.silent=!f,d&&l.on("click",function(){window.open(d,"_"+t.get("target"))}),f&&c.on("click",function(){window.open(f,"_"+t.get("subtarget"))}),i.add(l),u&&i.add(c);var p=i.getBoundingRect(),g=t.getBoxLayoutParams();g.width=p.width,g.height=p.height;var m=Gr(g,{width:n.getWidth(),height:n.getHeight()},t.get("padding"));a||("middle"===(a=t.get("left")||t.get("right"))&&(a="center"),"right"===a?m.x+=m.width:"center"===a&&(m.x+=m.width/2)),s||("center"===(s=t.get("top")||t.get("bottom"))&&(s="middle"),"bottom"===s?m.y+=m.height:"middle"===s&&(m.y+=m.height/2),s=s||"top"),i.attr("position",[m.x,m.y]);var v={textAlign:a,textVerticalAlign:s};l.setStyle(v),c.setStyle(v),p=i.getBoundingRect();var y=m.margin,x=t.getItemStyle(["color","opacity"]);x.fill=t.get("backgroundColor");var _=new gv({shape:{x:p.x-y[3],y:p.y-y[0],width:p.width+y[1]+y[3],height:p.height+y[0]+y[2],r:t.get("borderRadius")},style:x,silent:!0});Ei(_),i.add(_)}}});var wb=Or,bb=Er,Sb=rs({type:"marker",dependencies:["series","grid","polar","geo"],init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n),this.mergeOption(t,n,i.createdBySelf,!0)},isAnimationEnabled:function(){if(Kf.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},mergeOption:function(t,e,n,i){var r=this.constructor,a=this.mainType+"Model";n||e.eachSeries(function(t){var n=t.get(this.mainType),s=t[a];n&&n.data?(s?s.mergeOption(n,e,!0):(i&&Ou(n),d(n.data,function(t){t instanceof Array?(Ou(t[0]),Ou(t[1])):Ou(t)}),o(s=new r(n,this,e),{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),s.__hostSeries=t),t[a]=s):t[a]=null},this)},formatTooltip:function(t){var e=this.getData(),n=this.getRawValue(t),i=y(n)?f(n,wb).join(", "):wb(n),r=e.getName(t),o=bb(this.name);return(null!=n||r)&&(o+="<br />"),r&&(o+=bb(r),null!=n&&(o+=" : ")),null!=n&&(o+=bb(i)),o},getData:function(){return this._data},setData:function(t){this._data=t}});u(Sb,zy),Sb.extend({type:"markPoint",defaultOption:{zlevel:0,z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}}});var Mb=l,Ib=v,Tb={min:Ib(Nu,"min"),max:Ib(Nu,"max"),average:Ib(Nu,"average")},Cb=os({type:"marker",init:function(){this.markerGroupMap=N()},render:function(t,e,n){var i=this.markerGroupMap;i.each(function(t){t.__keep=!1});var r=this.type+"Model";e.eachSeries(function(t){var i=t[r];i&&this.renderSeries(t,i,e,n)},this),i.each(function(t){!t.__keep&&this.group.remove(t.group)},this)},renderSeries:function(){}});Cb.extend({type:"markPoint",updateTransform:function(t,e,n){e.eachSeries(function(t){var e=t.markPointModel;e&&(Wu(e.getData(),t,n),this.markerGroupMap.get(t.id).updateLayout(e))},this)},renderSeries:function(t,e,n,i){var r=t.coordinateSystem,o=t.id,a=t.getData(),s=this.markerGroupMap,l=s.get(o)||s.set(o,new fl),h=Zu(r,t,e);e.setData(h),Wu(e.getData(),t,i),h.each(function(t){var n=h.getItemModel(t),i=n.getShallow("symbolSize");"function"==typeof i&&(i=i(e.getRawValue(t),e.getDataParams(t))),h.setItemVisual(t,{symbolSize:i,color:n.get("itemStyle.color")||a.getVisual("color"),symbol:n.getShallow("symbol")})}),l.updateData(h),this.group.add(l.group),h.eachItemGraphicEl(function(t){t.traverse(function(t){t.dataModel=e})}),l.__keep=!0,l.group.silent=e.get("silent")||t.get("silent")}}),Ka(function(t){t.markPoint=t.markPoint||{}}),Sb.extend({type:"markLine",defaultOption:{zlevel:0,z:5,symbol:["circle","arrow"],symbolSize:[8,16],precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end"},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"}});var Db=mv.prototype,Ab=yv.prototype,kb=Ai({type:"ec-line",style:{stroke:"#000",fill:null},shape:{x1:0,y1:0,x2:0,y2:0,percent:1,cpx1:null,cpy1:null},buildPath:function(t,e){(Uu(e)?Db:Ab).buildPath(t,e)},pointAt:function(t){return Uu(this.shape)?Db.pointAt.call(this,t):Ab.pointAt.call(this,t)},tangentAt:function(t){var e=this.shape,n=Uu(e)?[e.x2-e.x1,e.y2-e.y1]:Ab.tangentAt.call(this,t);return j(n,n)}}),Pb=["fromSymbol","toSymbol"],Lb=$u.prototype;Lb.beforeUpdate=function(){var t=this,e=t.childOfName("fromSymbol"),n=t.childOfName("toSymbol"),i=t.childOfName("label");if(e||n||!i.ignore){for(var r=1,o=this.parent;o;)o.scale&&(r/=o.scale[0]),o=o.parent;var a=t.childOfName("line");if(this.__dirty||a.__dirty){var s=a.shape.percent,l=a.pointAt(0),h=a.pointAt(s),u=W([],h,l);if(j(u,u),e&&(e.attr("position",l),c=a.tangentAt(0),e.attr("rotation",Math.PI/2-Math.atan2(c[1],c[0])),e.attr("scale",[r*s,r*s])),n){n.attr("position",h);var c=a.tangentAt(1);n.attr("rotation",-Math.PI/2-Math.atan2(c[1],c[0])),n.attr("scale",[r*s,r*s])}if(!i.ignore){i.attr("position",h);var d,f,p,g=5*r;if("end"===i.__position)d=[u[0]*g+h[0],u[1]*g+h[1]],f=u[0]>.8?"left":u[0]<-.8?"right":"center",p=u[1]>.8?"top":u[1]<-.8?"bottom":"middle";else if("middle"===i.__position){var m=s/2,v=[(c=a.tangentAt(m))[1],-c[0]],y=a.pointAt(m);v[1]>0&&(v[0]=-v[0],v[1]=-v[1]),d=[y[0]+v[0]*g,y[1]+v[1]*g],f="center",p="bottom";var x=-Math.atan2(c[1],c[0]);h[0]<l[0]&&(x=Math.PI+x),i.attr("rotation",x)}else d=[-u[0]*g+l[0],-u[1]*g+l[1]],f=u[0]>.8?"right":u[0]<-.8?"left":"center",p=u[1]>.8?"bottom":u[1]<-.8?"top":"middle";i.attr({style:{textVerticalAlign:i.__verticalAlign||p,textAlign:i.__textAlign||f},position:d,scale:[r,r]})}}}},Lb._createLine=function(t,e,n){var i=t.hostModel,r=Yu(t.getItemLayout(e));r.shape.percent=0,sr(r,{shape:{percent:1}},i,e),this.add(r);var o=new ov({name:"label"});this.add(o),d(Pb,function(n){var i=ju(n,t,e);this.add(i),this[Xu(n)]=t.getItemVisual(e,n)},this),this._updateCommonStl(t,e,n)},Lb.updateData=function(t,e,n){var i=t.hostModel,r=this.childOfName("line"),o=t.getItemLayout(e),a={shape:{}};qu(a.shape,o),ar(r,a,i,e),d(Pb,function(n){var i=t.getItemVisual(e,n),r=Xu(n);if(this[r]!==i){this.remove(this.childOfName(n));var o=ju(n,t,e);this.add(o)}this[r]=i},this),this._updateCommonStl(t,e,n)},Lb._updateCommonStl=function(t,e,n){var i=t.hostModel,r=this.childOfName("line"),o=n&&n.lineStyle,s=n&&n.hoverLineStyle,l=n&&n.labelModel,h=n&&n.hoverLabelModel;if(!n||t.hasItemOption){var u=t.getItemModel(e);o=u.getModel("lineStyle").getLineStyle(),s=u.getModel("emphasis.lineStyle").getLineStyle(),l=u.getModel("label"),h=u.getModel("emphasis.label")}var c=t.getItemVisual(e,"color"),f=D(t.getItemVisual(e,"opacity"),o.opacity,1);r.useStyle(a({strokeNoScale:!0,fill:"none",stroke:c,opacity:f},o)),r.hoverStyle=s,d(Pb,function(t){var e=this.childOfName(t);e&&(e.setColor(c),e.setStyle({opacity:f}))},this);var p,g,m,v=l.getShallow("show"),y=h.getShallow("show"),x=this.childOfName("label");if(v||y){if(p=c||"#000",null==(g=i.getFormattedLabel(e,"normal",t.dataType))){var _=i.getRawValue(e);g=null==_?t.getName(e):isFinite(_)?wr(_):_}m=C(i.getFormattedLabel(e,"emphasis",t.dataType),g)}if(v){var w=Ki(x.style,l,{text:g},{autoColor:p});x.__textAlign=w.textAlign,x.__verticalAlign=w.textVerticalAlign,x.__position=l.get("position")||"middle"}else x.setStyle("text",null);x.hoverStyle=y?{text:m,textFill:h.getTextColor(!0),fontStyle:h.getShallow("fontStyle"),fontWeight:h.getShallow("fontWeight"),fontSize:h.getShallow("fontSize"),fontFamily:h.getShallow("fontFamily")}:{text:null},x.ignore=!v&&!y,qi(this)},Lb.highlight=function(){this.trigger("emphasis")},Lb.downplay=function(){this.trigger("normal")},Lb.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},Lb.setLinePoints=function(t){var e=this.childOfName("line");qu(e.shape,t),e.dirty()},h($u,Jp);var Ob=Ku.prototype;Ob.isPersistent=function(){return!0},Ob.updateData=function(t){var e=this,n=e.group,i=e._lineData;e._lineData=t,i||n.removeAll();var r=tc(t);t.diff(i).add(function(n){Qu(e,t,n,r)}).update(function(n,o){Ju(e,i,t,o,n,r)}).remove(function(t){n.remove(i.getItemGraphicEl(t))}).execute()},Ob.updateLayout=function(){var t=this._lineData;t.eachItemGraphicEl(function(e,n){e.updateLayout(t,n)},this)},Ob.incrementalPrepareUpdate=function(t){this._seriesScope=tc(t),this._lineData=null,this.group.removeAll()},Ob.incrementalUpdate=function(t,e){for(var n=t.start;n<t.end;n++)if(nc(e.getItemLayout(n))){var i=new this._ctor(e,n,this._seriesScope);i.traverse(function(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}),this.group.add(i)}},Ob.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},Ob._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()};var zb=function(t,e,r,a){var s=t.getData(),l=a.type;if(!y(a)&&("min"===l||"max"===l||"average"===l||null!=a.xAxis||null!=a.yAxis)){var h,u;if(null!=a.yAxis||null!=a.xAxis)h=null!=a.yAxis?"y":"x",e.getAxis(h),u=T(a.yAxis,a.xAxis);else{var c=Bu(a,s,e,t);h=c.valueDataDim,c.valueAxis,u=Gu(s,h,l)}var d="x"===h?0:1,f=1-d,p=n(a),g={};p.type=null,p.coord=[],g.coord=[],p.coord[f]=-1/0,g.coord[f]=1/0;var m=r.get("precision");m>=0&&"number"==typeof u&&(u=+u.toFixed(Math.min(m,20))),p.coord[d]=g.coord[d]=u,a=[p,g,{type:l,valueIndex:a.valueIndex,value:u}]}return a=[Ru(t,a[0]),Ru(t,a[1]),o({},a[2])],a[2].type=a[2].type||"",i(a[2],a[0]),i(a[2],a[1]),a};Cb.extend({type:"markLine",updateTransform:function(t,e,n){e.eachSeries(function(t){var e=t.markLineModel;if(e){var i=e.getData(),r=e.__from,o=e.__to;r.each(function(e){ac(r,e,!0,t,n),ac(o,e,!1,t,n)}),i.each(function(t){i.setItemLayout(t,[r.getItemLayout(t),o.getItemLayout(t)])}),this.markerGroupMap.get(t.id).updateLayout()}},this)},renderSeries:function(t,e,n,i){function r(e,n,r){var o=e.getItemModel(n);ac(e,n,r,t,i),e.setItemVisual(n,{symbolSize:o.get("symbolSize")||g[r?0:1],symbol:o.get("symbol",!0)||p[r?0:1],color:o.get("itemStyle.color")||s.getVisual("color")})}var o=t.coordinateSystem,a=t.id,s=t.getData(),l=this.markerGroupMap,h=l.get(a)||l.set(a,new Ku);this.group.add(h.group);var u=sc(o,t,e),c=u.from,d=u.to,f=u.line;e.__from=c,e.__to=d,e.setData(f);var p=e.get("symbol"),g=e.get("symbolSize");y(p)||(p=[p,p]),"number"==typeof g&&(g=[g,g]),u.from.each(function(t){r(c,t,!0),r(d,t,!1)}),f.each(function(t){var e=f.getItemModel(t).get("lineStyle.color");f.setItemVisual(t,{color:e||c.getItemVisual(t,"color")}),f.setItemLayout(t,[c.getItemLayout(t),d.getItemLayout(t)]),f.setItemVisual(t,{fromSymbolSize:c.getItemVisual(t,"symbolSize"),fromSymbol:c.getItemVisual(t,"symbol"),toSymbolSize:d.getItemVisual(t,"symbolSize"),toSymbol:d.getItemVisual(t,"symbol")})}),h.updateData(f),u.line.eachItemGraphicEl(function(t,n){t.traverse(function(t){t.dataModel=e})}),h.__keep=!0,h.group.silent=e.get("silent")||t.get("silent")}}),Ka(function(t){t.markLine=t.markLine||{}}),Sb.extend({type:"markArea",defaultOption:{zlevel:0,z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}}});var Eb=function(t,e,n,i){var o=Ru(t,i[0]),a=Ru(t,i[1]),s=T,l=o.coord,h=a.coord;l[0]=s(l[0],-1/0),l[1]=s(l[1],-1/0),h[0]=s(h[0],1/0),h[1]=s(h[1],1/0);var u=r([{},o,a]);return u.coord=[o.coord,a.coord],u.x0=o.x,u.y0=o.y,u.x1=a.x,u.y1=a.y,u},Nb=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]];Cb.extend({type:"markArea",updateTransform:function(t,e,n){e.eachSeries(function(t){var e=t.markAreaModel;if(e){var i=e.getData();i.each(function(e){var r=f(Nb,function(r){return cc(i,e,r,t,n)});i.setItemLayout(e,r),i.getItemGraphicEl(e).setShape("points",r)})}},this)},renderSeries:function(t,e,n,i){var r=t.coordinateSystem,o=t.name,s=t.getData(),l=this.markerGroupMap,h=l.get(o)||l.set(o,{group:new Jp});this.group.add(h.group),h.__keep=!0;var u=dc(r,t,e);e.setData(u),u.each(function(e){u.setItemLayout(e,f(Nb,function(n){return cc(u,e,n,t,i)})),u.setItemVisual(e,{color:s.getVisual("color")})}),u.diff(h.__data).add(function(t){var e=new fv({shape:{points:u.getItemLayout(t)}});u.setItemGraphicEl(t,e),h.group.add(e)}).update(function(t,n){var i=h.__data.getItemGraphicEl(n);ar(i,{shape:{points:u.getItemLayout(t)}},e,t),h.group.add(i),u.setItemGraphicEl(t,i)}).remove(function(t){var e=h.__data.getItemGraphicEl(t);h.group.remove(e)}).execute(),u.eachItemGraphicEl(function(t,n){var i=u.getItemModel(n),r=i.getModel("label"),o=i.getModel("emphasis.label"),s=u.getItemVisual(n,"color");t.useStyle(a(i.getModel("itemStyle").getItemStyle(),{fill:Pt(s,.4),stroke:s})),t.hoverStyle=i.getModel("emphasis.itemStyle").getItemStyle(),$i(t.style,t.hoverStyle,r,o,{labelFetcher:e,labelDataIndex:n,defaultText:u.getName(n)||"",isRectText:!0,autoColor:s}),qi(t,{}),t.dataModel=e}),h.__data=u,h.group.silent=e.get("silent")||t.get("silent")}}),Ka(function(t){t.markArea=t.markArea||{}}),Qv.registerSubTypeDefaulter("dataZoom",function(){return"slider"});var Rb=["cartesian2d","polar","singleAxis"],Bb=function(t,e){var n=f(t=t.slice(),Fr),i=f(e=(e||[]).slice(),Fr);return function(r,o){d(t,function(t,a){for(var s={name:t,capital:n[a]},l=0;l<e.length;l++)s[e[l]]=t+i[l];r.call(o,s)})}}(["x","y","z","radius","angle","single"],["axisIndex","axis","index","id"]),Vb=d,Fb=br,Hb=function(t,e,n,i){this._dimName=t,this._axisIndex=e,this._valueWindow,this._percentWindow,this._dataExtent,this._minMaxSpan,this.ecModel=i,this._dataZoomModel=n};Hb.prototype={constructor:Hb,hostedBy:function(t){return this._dataZoomModel===t},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var t=[],e=this.ecModel;return e.eachSeries(function(n){if(fc(n.get("coordinateSystem"))){var i=this._dimName,r=e.queryComponents({mainType:i+"Axis",index:n.get(i+"AxisIndex"),id:n.get(i+"AxisId")})[0];this._axisIndex===(r&&r.componentIndex)&&t.push(n)}},this),t},getAxisModel:function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var t,e,n=this._dimName,i=this.ecModel,r=this.getAxisModel();"x"===n||"y"===n?(e="gridIndex",t="x"===n?"y":"x"):(e="polarIndex",t="angle"===n?"radius":"angle");var o;return i.eachComponent(t+"Axis",function(t){(t.get(e)||0)===(r.get(e)||0)&&(o=t)}),o},getMinMaxSpan:function(){return n(this._minMaxSpan)},calculateDataWindow:function(t){var e=this._dataExtent,n=this.getAxisModel().axis.scale,i=this._dataZoomModel.getRangePropMode(),r=[0,100],o=[t.start,t.end],a=[];return Vb(["startValue","endValue"],function(e){a.push(null!=t[e]?n.parse(t[e]):null)}),Vb([0,1],function(t){var s=a[t],l=o[t];"percent"===i[t]?(null==l&&(l=r[t]),s=n.parse(xr(l,r,e,!0))):l=xr(s,e,r,!0),a[t]=s,o[t]=l}),{valueWindow:Fb(a),percentWindow:Fb(o)}},reset:function(t){if(t===this._dataZoomModel){var e=this.getTargetSeriesModels();this._dataExtent=gc(this,this._dimName,e);var n=this.calculateDataWindow(t.option);this._valueWindow=n.valueWindow,this._percentWindow=n.percentWindow,yc(this),vc(this)}},restore:function(t){t===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,vc(this,!0))},filterData:function(t,e){function n(t){return t>=a[0]&&t<=a[1]}if(t===this._dataZoomModel){var i=this._dimName,r=this.getTargetSeriesModels(),o=t.get("filterMode"),a=this._valueWindow;"none"!==o&&Vb(r,function(t){var e=t.getData(),r=e.mapDimension(i,!0);"weakFilter"===o?e.filterSelf(function(t){for(var n,i,o,s=0;s<r.length;s++){var l=e.get(r[s],t),h=!isNaN(l),u=l<a[0],c=l>a[1];if(h&&!u&&!c)return!0;h&&(o=!0),u&&(n=!0),c&&(i=!0)}return o&&n&&i}):Vb(r,function(i){if("empty"===o)t.setData(e.map(i,function(t){return n(t)?t:NaN}));else{var r={};r[i]=a,e.selectRange(r)}}),Vb(r,function(t){e.setApproximateExtent(a,t)})})}}};var Gb=d,Wb=Bb,Zb=rs({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis","singleAxis","series"],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null,minSpan:null,maxSpan:null,minValueSpan:null,maxValueSpan:null,rangeMode:null},init:function(t,e,n){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0,this._rangePropMode=["percent","percent"];var i=xc(t);this.mergeDefaultAndTheme(t,n),this.doInit(i)},mergeOption:function(t){var e=xc(t);i(this.option,t,!0),this.doInit(e)},doInit:function(t){var e=this.option;Kf.canvasSupported||(e.realtime=!1),this._setDefaultThrottle(t),_c(this,t),Gb([["start","startValue"],["end","endValue"]],function(t,n){"value"===this._rangePropMode[n]&&(e[t[0]]=null)},this),this.textStyleModel=this.getModel("textStyle"),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var t=this._axisProxies;this.eachTargetAxis(function(e,n,i,r){var o=this.dependentModels[e.axis][n],a=o.__dzAxisProxy||(o.__dzAxisProxy=new Hb(e.name,n,this,r));t[e.name+"_"+n]=a},this)},_resetTarget:function(){var t=this.option,e=this._judgeAutoMode();Wb(function(e){var n=e.axisIndex;t[n]=xn(t[n])},this),"axisIndex"===e?this._autoSetAxisIndex():"orient"===e&&this._autoSetOrient()},_judgeAutoMode:function(){var t=this.option,e=!1;Wb(function(n){null!=t[n.axisIndex]&&(e=!0)},this);var n=t.orient;return null==n&&e?"orient":e?void 0:(null==n&&(t.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var t=!0,e=this.get("orient",!0),n=this.option,i=this.dependentModels;if(t){var r="vertical"===e?"y":"x";i[r+"Axis"].length?(n[r+"AxisIndex"]=[0],t=!1):Gb(i.singleAxis,function(i){t&&i.get("orient",!0)===e&&(n.singleAxisIndex=[i.componentIndex],t=!1)})}t&&Wb(function(e){if(t){var i=[],r=this.dependentModels[e.axis];if(r.length&&!i.length)for(var o=0,a=r.length;o<a;o++)"category"===r[o].get("type")&&i.push(o);n[e.axisIndex]=i,i.length&&(t=!1)}},this),t&&this.ecModel.eachSeries(function(t){this._isSeriesHasAllAxesTypeOf(t,"value")&&Wb(function(e){var i=n[e.axisIndex],r=t.get(e.axisIndex),o=t.get(e.axisId);l(i,r=t.ecModel.queryComponents({mainType:e.axis,index:r,id:o})[0].componentIndex)<0&&i.push(r)})},this)},_autoSetOrient:function(){var t;this.eachTargetAxis(function(e){!t&&(t=e.name)},this),this.option.orient="y"===t?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(t,e){var n=!0;return Wb(function(i){var r=t.get(i.axisIndex),o=this.dependentModels[i.axis][r];o&&o.get("type")===e||(n=!1)},this),n},_setDefaultThrottle:function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this.ecModel.option;this.option.throttle=e.animation&&e.animationDurationUpdate>0?100:20}},getFirstTargetAxisModel:function(){var t;return Wb(function(e){if(null==t){var n=this.get(e.axisIndex);n.length&&(t=this.dependentModels[e.axis][n[0]])}},this),t},eachTargetAxis:function(t,e){var n=this.ecModel;Wb(function(i){Gb(this.get(i.axisIndex),function(r){t.call(e,i,r,this,n)},this)},this)},getAxisProxy:function(t,e){return this._axisProxies[t+"_"+e]},getAxisModel:function(t,e){var n=this.getAxisProxy(t,e);return n&&n.getAxisModel()},setRawRange:function(t,e){var n=this.option;Gb([["start","startValue"],["end","endValue"]],function(e){null==t[e[0]]&&null==t[e[1]]||(n[e[0]]=t[e[0]],n[e[1]]=t[e[1]])},this),!e&&_c(this,t)},getPercentRange:function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},getValueRange:function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var n=this.findRepresentativeAxisProxy();return n?n.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(t){if(t)return t.__dzAxisProxy;var e=this._axisProxies;for(var n in e)if(e.hasOwnProperty(n)&&e[n].hostedBy(this))return e[n];for(var n in e)if(e.hasOwnProperty(n)&&!e[n].hostedBy(this))return e[n]},getRangePropMode:function(){return this._rangePropMode.slice()}}),Ub=By.extend({type:"dataZoom",render:function(t,e,n,i){this.dataZoomModel=t,this.ecModel=e,this.api=n},getTargetCoordInfo:function(){function t(t,e,n,i){for(var r,o=0;o<n.length;o++)if(n[o].model===t){r=n[o];break}r||n.push(r={model:t,axisModels:[],coordIndex:i}),r.axisModels.push(e)}var e=this.dataZoomModel,n=this.ecModel,i={};return e.eachTargetAxis(function(e,r){var o=n.getComponent(e.axis,r);if(o){var a=o.getCoordSysModel();a&&t(a,o,i[a.mainType]||(i[a.mainType]=[]),a.componentIndex)}},this),i}}),Xb=(Zb.extend({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}}),function(t,e,n,i,r,o){e[0]=bc(e[0],n),e[1]=bc(e[1],n),t=t||0;var a=n[1]-n[0];null!=r&&(r=bc(r,[0,a])),null!=o&&(o=Math.max(o,null!=r?r:0)),"all"===i&&(r=o=Math.abs(e[1]-e[0]),i=0);var s=wc(e,i);e[i]+=t;var l=r||0,h=n.slice();s.sign<0?h[0]+=l:h[1]-=l,e[i]=bc(e[i],h);u=wc(e,i);null!=r&&(u.sign!==s.sign||u.span<r)&&(e[1-i]=e[i]+s.sign*r);var u=wc(e,i);return null!=o&&u.span>o&&(e[1-i]=e[i]+u.sign*o),e}),jb=gv,Yb=xr,qb=br,$b=m,Kb=d,Qb="horizontal",Jb=5,tS=["line","bar","candlestick","scatter"],eS=Ub.extend({type:"dataZoom.slider",init:function(t,e){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=e},render:function(t,e,n,i){eS.superApply(this,"render",arguments),ha(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=t.get("orient"),!1!==this.dataZoomModel.get("show")?(i&&"dataZoom"===i.type&&i.from===this.uid||this._buildView(),this._updateView()):this.group.removeAll()},remove:function(){eS.superApply(this,"remove",arguments),ua(this,"_dispatchZoomAction")},dispose:function(){eS.superApply(this,"dispose",arguments),ua(this,"_dispatchZoomAction")},_buildView:function(){var t=this.group;t.removeAll(),this._resetLocation(),this._resetInterval();var e=this._displayables.barGroup=new Jp;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},_resetLocation:function(){var t=this.dataZoomModel,e=this.api,n=this._findCoordRect(),i={width:e.getWidth(),height:e.getHeight()},r=this._orient===Qb?{right:i.width-n.x-n.width,top:i.height-30-7,width:n.width,height:30}:{right:7,top:n.y,width:30,height:n.height},o=Ur(t.option);d(["right","top","width","height"],function(t){"ph"===o[t]&&(o[t]=r[t])});var a=Gr(o,i,t.padding);this._location={x:a.x,y:a.y},this._size=[a.width,a.height],"vertical"===this._orient&&this._size.reverse()},_positionGroup:function(){var t=this.group,e=this._location,n=this._orient,i=this.dataZoomModel.getFirstTargetAxisModel(),r=i&&i.get("inverse"),o=this._displayables.barGroup,a=(this._dataShadowInfo||{}).otherAxisInverse;o.attr(n!==Qb||r?n===Qb&&r?{scale:a?[-1,1]:[-1,-1]}:"vertical"!==n||r?{scale:a?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:a?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:a?[1,1]:[1,-1]});var s=t.getBoundingRect([o]);t.attr("position",[e.x-s.x,e.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var t=this.dataZoomModel,e=this._size,n=this._displayables.barGroup;n.add(new jb({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40})),n.add(new jb({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:m(this._onClickPanelClick,this)}))},_renderDataShadow:function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(t){var e=this._size,n=t.series,i=n.getRawData(),r=n.getShadowDim?n.getShadowDim():t.otherDim;if(null!=r){var o=i.getDataExtent(r),s=.3*(o[1]-o[0]);o=[o[0]-s,o[1]+s];var l,h=[0,e[1]],u=[0,e[0]],c=[[e[0],0],[0,0]],d=[],f=u[1]/(i.count()-1),p=0,g=Math.round(i.count()/e[0]);i.each([r],function(t,e){if(g>0&&e%g)p+=f;else{var n=null==t||isNaN(t)||""===t,i=n?0:Yb(t,o,h,!0);n&&!l&&e?(c.push([c[c.length-1][0],0]),d.push([d[d.length-1][0],0])):!n&&l&&(c.push([p,0]),d.push([p,0])),c.push([p,i]),d.push([p,i]),p+=f,l=n}});var m=this.dataZoomModel;this._displayables.barGroup.add(new fv({shape:{points:c},style:a({fill:m.get("dataBackgroundColor")},m.getModel("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new pv({shape:{points:d},style:m.getModel("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))}}},_prepareDataShadowInfo:function(){var t=this.dataZoomModel,e=t.get("showDataShadow");if(!1!==e){var n,i=this.ecModel;return t.eachTargetAxis(function(r,o){d(t.getAxisProxy(r.name,o).getTargetSeriesModels(),function(t){if(!(n||!0!==e&&l(tS,t.get("type"))<0)){var a,s=i.getComponent(r.axis,o).axis,h=Sc(r.name),u=t.coordinateSystem;null!=h&&u.getOtherAxis&&(a=u.getOtherAxis(s).inverse),h=t.getData().mapDimension(h),n={thisAxis:s,series:t,thisDim:r.name,otherDim:h,otherAxisInverse:a}}},this)},this),n}},_renderHandle:function(){var t=this._displayables,e=t.handles=[],n=t.handleLabels=[],i=this._displayables.barGroup,r=this._size,o=this.dataZoomModel;i.add(t.filler=new jb({draggable:!0,cursor:Mc(this._orient),drift:$b(this._onDragMove,this,"all"),onmousemove:function(t){Ag(t.event)},ondragstart:$b(this._showDataInfo,this,!0),ondragend:$b(this._onDragEnd,this),onmouseover:$b(this._showDataInfo,this,!0),onmouseout:$b(this._showDataInfo,this,!1),style:{fill:o.get("fillerColor"),textPosition:"inside"}})),i.add(new jb(Ei({silent:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{stroke:o.get("dataBackgroundColor")||o.get("borderColor"),lineWidth:1,fill:"rgba(0,0,0,0)"}}))),Kb([0,1],function(t){var r=fr(o.get("handleIcon"),{cursor:Mc(this._orient),draggable:!0,drift:$b(this._onDragMove,this,t),onmousemove:function(t){Ag(t.event)},ondragend:$b(this._onDragEnd,this),onmouseover:$b(this._showDataInfo,this,!0),onmouseout:$b(this._showDataInfo,this,!1)},{x:-1,y:0,width:2,height:2}),a=r.getBoundingRect();this._handleHeight=_r(o.get("handleSize"),this._size[1]),this._handleWidth=a.width/a.height*this._handleHeight,r.setStyle(o.getModel("handleStyle").getItemStyle());var s=o.get("handleColor");null!=s&&(r.style.fill=s),i.add(e[t]=r);var l=o.textStyleModel;this.group.add(n[t]=new ov({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",textFill:l.getTextColor(),textFont:l.getFont()},z2:10}))},this)},_resetInterval:function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[Yb(t[0],[0,100],e,!0),Yb(t[1],[0,100],e,!0)]},_updateInterval:function(t,e){var n=this.dataZoomModel,i=this._handleEnds,r=this._getViewExtent(),o=n.findRepresentativeAxisProxy().getMinMaxSpan(),a=[0,100];Xb(e,i,r,n.get("zoomLock")?"all":t,null!=o.minSpan?Yb(o.minSpan,a,r,!0):null,null!=o.maxSpan?Yb(o.maxSpan,a,r,!0):null),this._range=qb([Yb(i[0],r,a,!0),Yb(i[1],r,a,!0)])},_updateView:function(t){var e=this._displayables,n=this._handleEnds,i=qb(n.slice()),r=this._size;Kb([0,1],function(t){var i=e.handles[t],o=this._handleHeight;i.attr({scale:[o/2,o/2],position:[n[t],r[1]/2-o/2]})},this),e.filler.setShape({x:i[0],y:0,width:i[1]-i[0],height:r[1]}),this._updateDataInfo(t)},_updateDataInfo:function(t){function e(t){var e=lr(i.handles[t].parent,this.group),n=ur(0===t?"right":"left",e),s=this._handleWidth/2+Jb,l=hr([c[t]+(0===t?-s:s),this._size[1]/2],e);r[t].setStyle({x:l[0],y:l[1],textVerticalAlign:o===Qb?"middle":n,textAlign:o===Qb?n:"center",text:a[t]})}var n=this.dataZoomModel,i=this._displayables,r=i.handleLabels,o=this._orient,a=["",""];if(n.get("showDetail")){var s=n.findRepresentativeAxisProxy();if(s){var l=s.getAxisModel().axis,h=this._range,u=t?s.calculateDataWindow({start:h[0],end:h[1]}).valueWindow:s.getDataValueWindow();a=[this._formatLabel(u[0],l),this._formatLabel(u[1],l)]}}var c=qb(this._handleEnds.slice());e.call(this,0),e.call(this,1)},_formatLabel:function(t,e){var n=this.dataZoomModel,i=n.get("labelFormatter"),r=n.get("labelPrecision");null!=r&&"auto"!==r||(r=e.getPixelPrecision());var o=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel(Math.round(t)):t.toFixed(Math.min(r,20));return x(i)?i(t,o):_(i)?i.replace("{value}",o):o},_showDataInfo:function(t){t=this._dragging||t;var e=this._displayables.handleLabels;e[0].attr("invisible",!t),e[1].attr("invisible",!t)},_onDragMove:function(t,e,n){this._dragging=!0;var i=hr([e,n],this._displayables.barGroup.getLocalTransform(),!0);this._updateInterval(t,i[0]);var r=this.dataZoomModel.get("realtime");this._updateView(!r),r&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1),!this.dataZoomModel.get("realtime")&&this._dispatchZoomAction()},_onClickPanelClick:function(t){var e=this._size,n=this._displayables.barGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(n[0]<0||n[0]>e[0]||n[1]<0||n[1]>e[1])){var i=this._handleEnds,r=(i[0]+i[1])/2;this._updateInterval("all",n[0]-r),this._updateView(),this._dispatchZoomAction()}},_dispatchZoomAction:function(){var t=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:t[0],end:t[1]})},_findCoordRect:function(){var t;if(Kb(this.getTargetCoordInfo(),function(e){if(!t&&e.length){var n=e[0].model.coordinateSystem;t=n.getRect&&n.getRect()}}),!t){var e=this.api.getWidth(),n=this.api.getHeight();t={x:.2*e,y:.2*n,width:.6*e,height:.6*n}}return t}});Zb.extend({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,preventDefaultMouseMove:!0}});var nS="\0_ec_interaction_mutex";Ja({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},function(){}),u(Ac,xp);var iS=v,rS="\0_ec_dataZoom_roams",oS=m,aS=Ub.extend({type:"dataZoom.inside",init:function(t,e){this._range},render:function(t,e,n,i){aS.superApply(this,"render",arguments),Vc(i,t.id)&&(this._range=t.getPercentRange()),d(this.getTargetCoordInfo(),function(e,i){var r=f(e,function(t){return Fc(t.model)});d(e,function(e){var o=e.model,a=t.option;Rc(n,{coordId:Fc(o),allCoordIds:r,containsPoint:function(t,e,n){return o.coordinateSystem.containPoint([e,n])},dataZoomId:t.id,throttleRate:t.get("throttle",!0),panGetRange:oS(this._onPan,this,e,i),zoomGetRange:oS(this._onZoom,this,e,i),zoomLock:a.zoomLock,disabled:a.disabled,roamControllerOpt:{zoomOnMouseWheel:a.zoomOnMouseWheel,moveOnMouseMove:a.moveOnMouseMove,preventDefaultMouseMove:a.preventDefaultMouseMove}})},this)},this)},dispose:function(){Bc(this.api,this.dataZoomModel.id),aS.superApply(this,"dispose",arguments),this._range=null},_onPan:function(t,e,n,i,r,o,a,s,l){var h=this._range.slice(),u=t.axisModels[0];if(u){var c=sS[e]([o,a],[s,l],u,n,t),d=c.signal*(h[1]-h[0])*c.pixel/c.pixelLength;return Xb(d,h,[0,100],"all"),this._range=h}},_onZoom:function(t,e,n,i,r,o){var a=this._range.slice(),s=t.axisModels[0];if(s){var l=sS[e](null,[r,o],s,n,t),h=(l.signal>0?l.pixelStart+l.pixelLength-l.pixel:l.pixel-l.pixelStart)/l.pixelLength*(a[1]-a[0])+a[0];i=Math.max(1/i,0),a[0]=(a[0]-h)*i+h,a[1]=(a[1]-h)*i+h;var u=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return Xb(0,a,[0,100],0,u.minSpan,u.maxSpan),this._range=a}}}),sS={grid:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem.getRect();return t=t||[0,0],"x"===o.dim?(a.pixel=e[0]-t[0],a.pixelLength=s.width,a.pixelStart=s.x,a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=s.height,a.pixelStart=s.y,a.signal=o.inverse?-1:1),a},polar:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),h=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===n.mainType?(a.pixel=e[0]-t[0],a.pixelLength=l[1]-l[0],a.pixelStart=l[0],a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=h[1]-h[0],a.pixelStart=h[0],a.signal=o.inverse?-1:1),a},singleAxis:function(t,e,n,i,r){var o=n.axis,a=r.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===o.orient?(s.pixel=e[0]-t[0],s.pixelLength=a.width,s.pixelStart=a.x,s.signal=o.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=a.height,s.pixelStart=a.y,s.signal=o.inverse?-1:1),s}};Qa({getTargetSeries:function(t){var e=N();return t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,n,i){d(i.getAxisProxy(t.name,n).getTargetSeriesModels(),function(t){e.set(t.uid,t)})})}),e},isOverallFilter:!0,overallReset:function(t,e){t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,n,i){i.getAxisProxy(t.name,n).reset(i,e)}),t.eachTargetAxis(function(t,n,i){i.getAxisProxy(t.name,n).filterData(i,e)})}),t.eachComponent("dataZoom",function(t){var e=t.findRepresentativeAxisProxy(),n=e.getDataPercentWindow(),i=e.getDataValueWindow();t.setRawRange({start:n[0],end:n[1],startValue:i[0],endValue:i[1]},!0)})}}),Ja("dataZoom",function(t,e){var n=pc(m(e.eachComponent,e,"dataZoom"),Bb,function(t,e){return t.get(e.axisIndex)}),i=[];e.eachComponent({mainType:"dataZoom",query:t},function(t,e){i.push.apply(i,n(t).nodes)}),d(i,function(e,n){e.setRawRange({start:t.start,end:t.end,startValue:t.startValue,endValue:t.endValue})})});var lS={},hS=rs({type:"toolbox",layoutMode:{type:"box",ignoreSize:!0},mergeDefaultAndTheme:function(t){hS.superApply(this,"mergeDefaultAndTheme",arguments),d(this.option.feature,function(t,e){var n=$c(e);n&&i(t,n.defaultOption)})},defaultOption:{show:!0,z:6,zlevel:0,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}}}});os({type:"toolbox",render:function(t,e,n,i){function r(r,a){var s,c=u[r],d=u[a],f=new pr(l[c],t,t.ecModel);if(c&&!d){if(Kc(c))s={model:f,onclick:f.option.onclick,featureName:c};else{var p=$c(c);if(!p)return;s=new p(f,e,n)}h[c]=s}else{if(!(s=h[d]))return;s.model=f,s.ecModel=e,s.api=n}c||!d?f.get("show")&&!s.unusable?(o(f,s,c),f.setIconStatus=function(t,e){var n=this.option,i=this.iconPaths;n.iconStatus=n.iconStatus||{},n.iconStatus[t]=e,i[t]&&i[t].trigger(e)},s.render&&s.render(f,e,n,i)):s.remove&&s.remove(e,n):s.dispose&&s.dispose(e,n)}function o(i,r,o){var l=i.getModel("iconStyle"),h=i.getModel("emphasis.iconStyle"),u=r.getIcons?r.getIcons():i.get("icon"),c=i.get("title")||{};if("string"==typeof u){var f=u,p=c;c={},(u={})[o]=f,c[o]=p}var g=i.iconPaths={};d(u,function(o,u){var d=fr(o,{},{x:-s/2,y:-s/2,width:s,height:s});d.setStyle(l.getItemStyle()),d.hoverStyle=h.getItemStyle(),qi(d),t.get("showTitle")&&(d.__title=c[u],d.on("mouseover",function(){var t=h.getItemStyle();d.setStyle({text:c[u],textPosition:t.textPosition||"bottom",textFill:t.fill||t.stroke||"#000",textAlign:t.textAlign||"center"})}).on("mouseout",function(){d.setStyle({textFill:null})})),d.trigger(i.get("iconStatus."+u)||"normal"),a.add(d),d.on("click",m(r.onclick,r,e,n,u)),g[u]=d})}var a=this.group;if(a.removeAll(),t.get("show")){var s=+t.get("itemSize"),l=t.get("feature")||{},h=this._features||(this._features={}),u=[];d(l,function(t,e){u.push(e)}),new hs(this._featureNames||[],u).add(r).update(r).remove(v(r,null)).execute(),this._featureNames=u,Cu(a,t,n),a.add(Du(a.getBoundingRect(),t)),a.eachChild(function(t){var e=t.__title,i=t.hoverStyle;if(i&&e){var r=ce(e,Te(i)),o=t.position[0]+a.position[0],l=!1;t.position[1]+a.position[1]+s+r.height>n.getHeight()&&(i.textPosition="top",l=!0);var h=l?-5-r.height:s+8;o+r.width/2>n.getWidth()?(i.textPosition=["100%",h],i.textAlign="right"):o-r.width/2<0&&(i.textPosition=[0,h],i.textAlign="left")}})}},updateView:function(t,e,n,i){d(this._features,function(t){t.updateView&&t.updateView(t.model,e,n,i)})},remove:function(t,e){d(this._features,function(n){n.remove&&n.remove(t,e)}),this.group.removeAll()},dispose:function(t,e){d(this._features,function(n){n.dispose&&n.dispose(t,e)})}});var uS=qy.toolbox.saveAsImage;Qc.defaultOption={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:uS.title,type:"png",name:"",excludeComponents:["toolbox"],pixelRatio:1,lang:uS.lang.slice()},Qc.prototype.unusable=!Kf.canvasSupported,Qc.prototype.onclick=function(t,e){var n=this.model,i=n.get("name")||t.get("title.0.text")||"echarts",r=document.createElement("a"),o=n.get("type",!0)||"png";r.download=i+"."+o,r.target="_blank";var a=e.getConnectedDataURL({type:o,backgroundColor:n.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",excludeComponents:n.get("excludeComponents"),pixelRatio:n.get("pixelRatio")});if(r.href=a,"function"!=typeof MouseEvent||Kf.browser.ie||Kf.browser.edge)if(window.navigator.msSaveOrOpenBlob){for(var s=atob(a.split(",")[1]),l=s.length,h=new Uint8Array(l);l--;)h[l]=s.charCodeAt(l);var u=new Blob([h]);window.navigator.msSaveOrOpenBlob(u,i+"."+o)}else{var c=n.get("lang"),d='<body style="margin:0;"><img src="'+a+'" style="max-width:100%;" title="'+(c&&c[0]||"")+'" /></body>';window.open().document.write(d)}else{var f=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1});r.dispatchEvent(f)}},qc("saveAsImage",Qc);var cS=qy.toolbox.magicType;Jc.defaultOption={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z",tiled:"M2.3,2.2h22.8V25H2.3V2.2z M35,2.2h22.8V25H35V2.2zM2.3,35h22.8v22.8H2.3V35z M35,35h22.8v22.8H35V35z"},title:n(cS.title),option:{},seriesIndex:{}};var dS=Jc.prototype;dS.getIcons=function(){var t=this.model,e=t.get("icon"),n={};return d(t.get("type"),function(t){e[t]&&(n[t]=e[t])}),n};var fS={line:function(t,e,n,r){if("bar"===t)return i({id:e,type:"line",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},r.get("option.line")||{},!0)},bar:function(t,e,n,r){if("line"===t)return i({id:e,type:"bar",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},r.get("option.bar")||{},!0)},stack:function(t,e,n,r){if("line"===t||"bar"===t)return i({id:e,stack:"__ec_magicType_stack__"},r.get("option.stack")||{},!0)},tiled:function(t,e,n,r){if("line"===t||"bar"===t)return i({id:e,stack:""},r.get("option.tiled")||{},!0)}},pS=[["line","bar"],["stack","tiled"]];dS.onclick=function(t,e,n){var i=this.model,r=i.get("seriesIndex."+n);if(fS[n]){var o={series:[]};d(pS,function(t){l(t,n)>=0&&d(t,function(t){i.setIconStatus(t,"normal")})}),i.setIconStatus(n,"emphasis"),t.eachComponent({mainType:"series",query:null==r?null:{seriesIndex:r}},function(e){var r=e.subType,s=e.id,l=fS[n](r,s,e,i);l&&(a(l,e.option),o.series.push(l));var h=e.coordinateSystem;if(h&&"cartesian2d"===h.type&&("line"===n||"bar"===n)){var u=h.getAxesByScale("ordinal")[0];if(u){var c=u.dim+"Axis",d=t.queryComponents({mainType:c,index:e.get(name+"Index"),id:e.get(name+"Id")})[0].componentIndex;o[c]=o[c]||[];for(var f=0;f<=d;f++)o[c][d]=o[c][d]||{};o[c][d].boundaryGap="bar"===n}}}),e.dispatchAction({type:"changeMagicType",currentType:n,newOption:o})}},Ja({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(t,e){e.mergeOption(t.newOption)}),qc("magicType",Jc);var gS=qy.toolbox.dataView,mS=new Array(60).join("-"),vS="\t",yS=new RegExp("["+vS+"]+","g");hd.defaultOption={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:n(gS.title),lang:n(gS.lang),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"},hd.prototype.onclick=function(t,e){function n(){i.removeChild(o),x._dom=null}var i=e.getDom(),r=this.model;this._dom&&i.removeChild(this._dom);var o=document.createElement("div");o.style.cssText="position:absolute;left:5px;top:5px;bottom:5px;right:5px;",o.style.backgroundColor=r.get("backgroundColor")||"#fff";var a=document.createElement("h4"),s=r.get("lang")||[];a.innerHTML=s[0]||r.get("title"),a.style.cssText="margin: 10px 20px;",a.style.color=r.get("textColor");var l=document.createElement("div"),h=document.createElement("textarea");l.style.cssText="display:block;width:100%;overflow:auto;";var u=r.get("optionToContent"),c=r.get("contentToOption"),d=id(t);if("function"==typeof u){var f=u(e.getOption());"string"==typeof f?l.innerHTML=f:M(f)&&l.appendChild(f)}else l.appendChild(h),h.readOnly=r.get("readOnly"),h.style.cssText="width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;",h.style.color=r.get("textColor"),h.style.borderColor=r.get("textareaBorderColor"),h.style.backgroundColor=r.get("textareaColor"),h.value=d.value;var p=d.meta,g=document.createElement("div");g.style.cssText="position:absolute;bottom:0;left:0;right:0;";var m="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",v=document.createElement("div"),y=document.createElement("div");m+=";background-color:"+r.get("buttonColor"),m+=";color:"+r.get("buttonTextColor");var x=this;on(v,"click",n),on(y,"click",function(){var t;try{t="function"==typeof c?c(l,e.getOption()):ld(h.value,p)}catch(t){throw n(),new Error("Data view format error "+t)}t&&e.dispatchAction({type:"changeDataView",newOption:t}),n()}),v.innerHTML=s[1],y.innerHTML=s[2],y.style.cssText=m,v.style.cssText=m,!r.get("readOnly")&&g.appendChild(y),g.appendChild(v),on(h,"keydown",function(t){if(9===(t.keyCode||t.which)){var e=this.value,n=this.selectionStart,i=this.selectionEnd;this.value=e.substring(0,n)+vS+e.substring(i),this.selectionStart=this.selectionEnd=n+1,Ag(t)}}),o.appendChild(a),o.appendChild(l),o.appendChild(g),l.style.height=i.clientHeight-80+"px",i.appendChild(o),this._dom=o},hd.prototype.remove=function(t,e){this._dom&&e.getDom().removeChild(this._dom)},hd.prototype.dispose=function(t,e){this.remove(t,e)},qc("dataView",hd),Ja({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(t,e){var n=[];d(t.newOption.series,function(t){var i=e.getSeriesByName(t.name)[0];if(i){var r=i.get("data");n.push({name:t.name,data:ud(t.data,r)})}else n.push(o({type:"scatter"},t))}),e.mergeOption(a({series:n},t.newOption))});var xS=v,_S=d,wS=f,bS=Math.min,SS=Math.max,MS=Math.pow,IS=1e4,TS=6,CS=6,DS="globalPan",AS={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},kS={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},PS={brushStyle:{lineWidth:2,stroke:"rgba(0,0,0,0.3)",fill:"rgba(0,0,0,0.1)"},transformable:!0,brushMode:"single",removeOnClick:!1},LS=0;cd.prototype={constructor:cd,enableBrush:function(t){return this._brushType&&fd(this),t.brushType&&dd(this,t),this},setPanels:function(t){if(t&&t.length){var e=this._panels={};d(t,function(t){e[t.panelId]=n(t)})}else this._panels=null;return this},mount:function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var e=this.group;return this._zr.add(e),e.attr({position:t.position||[0,0],rotation:t.rotation||0,scale:t.scale||[1,1]}),this._transform=e.getLocalTransform(),this},eachCover:function(t,e){_S(this._covers,t,e)},updateCovers:function(t){function e(t,e){return(null!=t.id?t.id:o+e)+"-"+t.brushType}function r(e,n){var i=t[e];if(null!=n&&a[n]===h)s[e]=a[n];else{var r=s[e]=null!=n?(a[n].__brushOption=i,a[n]):gd(l,pd(l,i));yd(l,r)}}t=f(t,function(t){return i(n(PS),t,!0)});var o="\0-brush-index-",a=this._covers,s=this._covers=[],l=this,h=this._creatingCover;return new hs(a,t,function(t,n){return e(t.__brushOption,n)},e).add(r).update(r).remove(function(t){a[t]!==h&&l.group.remove(a[t])}).execute(),this},unmount:function(){return this.enableBrush(!1),bd(this),this._zr.remove(this.group),this},dispose:function(){this.unmount(),this.off()}},u(cd,xp);var OS={mousedown:function(t){if(this._dragging)Zd.call(this,t);else if(!t.target||!t.target.draggable){Fd(t);var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);this._creatingCover=null,(this._creatingPanel=_d(this,t,e))&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(t){var e=this.group.transformCoordToLocal(t.offsetX,t.offsetY);if(Vd(this,t,e),this._dragging){Fd(t);var n=Gd(this,t,e,!1);n&&Sd(this,n)}},mouseup:Zd},zS={lineX:Ud(0),lineY:Ud(1),rect:{createCover:function(t,e){return Td(xS(zd,function(t){return t},function(t){return t}),t,e,["w","e","n","s","se","sw","ne","nw"])},getCreatingRange:function(t){var e=Id(t);return Pd(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(t,e,n,i){Cd(t,e,n,i)},updateCommon:Dd,contain:Hd},polygon:{createCover:function(t,e){var n=new Jp;return n.add(new pv({name:"main",style:kd(e),silent:!0})),n},getCreatingRange:function(t){return t},endCreating:function(t,e){e.remove(e.childAt(0)),e.add(new fv({name:"main",draggable:!0,drift:xS(Ed,t,e),ondragend:xS(Sd,t,{isEnd:!0})}))},updateCoverShape:function(t,e,n,i){e.childAt(0).setShape({points:Rd(t,e,n)})},updateCommon:Dd,contain:Hd}},ES={axisPointer:1,tooltip:1,brush:1},NS=d,RS=l,BS=v,VS=["dataToPoint","pointToData"],FS=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],HS=Kd.prototype;HS.setOutputRanges=function(t,e){this.matchOutputRanges(t,e,function(t,e,n){if((t.coordRanges||(t.coordRanges=[])).push(e),!t.coordRange){t.coordRange=e;var i=US[t.brushType](0,n,e);t.__rangeOffset={offset:XS[t.brushType](i.values,t.range,[1,1]),xyMinMax:i.xyMinMax}}})},HS.matchOutputRanges=function(t,e,n){NS(t,function(t){var i=this.findTargetInfo(t,e);i&&!0!==i&&d(i.coordSyses,function(i){var r=US[t.brushType](1,i,t.range);n(t,r.values,i,e)})},this)},HS.setInputRanges=function(t,e){NS(t,function(t){var n=this.findTargetInfo(t,e);if(t.range=t.range||[],n&&!0!==n){t.panelId=n.panelId;var i=US[t.brushType](0,n.coordSys,t.coordRange),r=t.__rangeOffset;t.range=r?XS[t.brushType](i.values,r.offset,nf(i.xyMinMax,r.xyMinMax)):i.values}},this)},HS.makePanelOpts=function(t,e){return f(this._targetInfoList,function(n){var i=n.getPanelRect();return{panelId:n.panelId,defaultBrushType:e&&e(n),clipPath:jd(i),isTargetByCursor:qd(i,t,n.coordSysModel),getLinearBrushOtherExtent:Yd(i)}})},HS.controlSeries=function(t,e,n){var i=this.findTargetInfo(t,n);return!0===i||i&&RS(i.coordSyses,e.coordinateSystem)>=0},HS.findTargetInfo=function(t,e){for(var n=this._targetInfoList,i=Jd(e,t),r=0;r<n.length;r++){var o=n[r],a=t.panelId;if(a){if(o.panelId===a)return o}else for(r=0;r<WS.length;r++)if(WS[r](i,o))return o}return!0};var GS={grid:function(t,e){var n=t.xAxisModels,i=t.yAxisModels,r=t.gridModels,o=N(),a={},s={};(n||i||r)&&(NS(n,function(t){var e=t.axis.grid.model;o.set(e.id,e),a[e.id]=!0}),NS(i,function(t){var e=t.axis.grid.model;o.set(e.id,e),s[e.id]=!0}),NS(r,function(t){o.set(t.id,t),a[t.id]=!0,s[t.id]=!0}),o.each(function(t){var r=t.coordinateSystem,o=[];NS(r.getCartesians(),function(t,e){(RS(n,t.getAxis("x").model)>=0||RS(i,t.getAxis("y").model)>=0)&&o.push(t)}),e.push({panelId:"grid--"+t.id,gridModel:t,coordSysModel:t,coordSys:o[0],coordSyses:o,getPanelRect:ZS.grid,xAxisDeclared:a[t.id],yAxisDeclared:s[t.id]})}))},geo:function(t,e){NS(t.geoModels,function(t){var n=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:n,coordSyses:[n],getPanelRect:ZS.geo})})}},WS=[function(t,e){var n=t.xAxisModel,i=t.yAxisModel,r=t.gridModel;return!r&&n&&(r=n.axis.grid.model),!r&&i&&(r=i.axis.grid.model),r&&r===e.gridModel},function(t,e){var n=t.geoModel;return n&&n===e.geoModel}],ZS={grid:function(){return this.coordSys.grid.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(lr(t)),e}},US={lineX:BS(tf,0),lineY:BS(tf,1),rect:function(t,e,n){var i=e[VS[t]]([n[0][0],n[1][0]]),r=e[VS[t]]([n[0][1],n[1][1]]),o=[Qd([i[0],r[0]]),Qd([i[1],r[1]])];return{values:o,xyMinMax:o}},polygon:function(t,e,n){var i=[[1/0,-1/0],[1/0,-1/0]];return{values:f(n,function(n){var r=e[VS[t]](n);return i[0][0]=Math.min(i[0][0],r[0]),i[1][0]=Math.min(i[1][0],r[1]),i[0][1]=Math.max(i[0][1],r[0]),i[1][1]=Math.max(i[1][1],r[1]),r}),xyMinMax:i}}},XS={lineX:BS(ef,0),lineY:BS(ef,1),rect:function(t,e,n){return[[t[0][0]-n[0]*e[0][0],t[0][1]-n[0]*e[0][1]],[t[1][0]-n[1]*e[1][0],t[1][1]-n[1]*e[1][1]]]},polygon:function(t,e,n){return f(t,function(t,i){return[t[0]-n[0]*e[i][0],t[1]-n[1]*e[i][1]]})}},jS=d,YS="\0_ec_hist_store";Zb.extend({type:"dataZoom.select"}),Ub.extend({type:"dataZoom.select"});var qS=qy.toolbox.dataZoom,$S=d,KS="\0_ec_\0toolbox-dataZoom_";uf.defaultOption={show:!0,icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:n(qS.title)};var QS=uf.prototype;QS.render=function(t,e,n,i){this.model=t,this.ecModel=e,this.api=n,ff(t,e,this,i,n),df(t,e)},QS.onclick=function(t,e,n){JS[n].call(this)},QS.remove=function(t,e){this._brushController.unmount()},QS.dispose=function(t,e){this._brushController.dispose()};var JS={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(af(this.ecModel))}};QS._onBrush=function(t,e){function n(t,e,n){var a=e.getAxis(t),s=a.model,l=i(t,s,o),h=l.findRepresentativeAxisProxy(s).getMinMaxSpan();null==h.minValueSpan&&null==h.maxValueSpan||(n=Xb(0,n.slice(),a.scale.getExtent(),0,h.minValueSpan,h.maxValueSpan)),l&&(r[l.id]={dataZoomId:l.id,startValue:n[0],endValue:n[1]})}function i(t,e,n){var i;return n.eachComponent({mainType:"dataZoom",subType:"select"},function(n){n.getAxisModel(t,e.componentIndex)&&(i=n)}),i}if(e.isEnd&&t.length){var r={},o=this.ecModel;this._brushController.updateCovers([]),new Kd(cf(this.model.option),o,{include:["grid"]}).matchOutputRanges(t,o,function(t,e,i){if("cartesian2d"===i.type){var r=t.brushType;"rect"===r?(n("x",i,e[0]),n("y",i,e[1])):n({lineX:"x",lineY:"y"}[r],i,e)}}),of(o,r),this._dispatchZoomAction(r)}},QS._dispatchZoomAction=function(t){var e=[];$S(t,function(t,i){e.push(n(t))}),e.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:e})},qc("dataZoom",uf),Ka(function(t){function e(t,e){if(e){var r=t+"Index",o=e[r];null==o||"all"==o||y(o)||(o=!1===o||"none"===o?[]:[o]),n(t,function(e,n){if(null==o||"all"==o||-1!==l(o,n)){var a={type:"select",$fromToolbox:!0,id:KS+t+n};a[r]=n,i.push(a)}})}}function n(e,n){var i=t[e];y(i)||(i=i?[i]:[]),$S(i,n)}if(t){var i=t.dataZoom||(t.dataZoom=[]);y(i)||(t.dataZoom=i=[i]);var r=t.toolbox;if(r&&(y(r)&&(r=r[0]),r&&r.feature)){var o=r.feature.dataZoom;e("xAxis",o),e("yAxis",o)}}});var tM=qy.toolbox.restore;pf.defaultOption={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:tM.title},pf.prototype.onclick=function(t,e,n){sf(t),e.dispatchAction({type:"restore",from:this.uid})},qc("restore",pf),Ja({type:"restore",event:"restore",update:"prepareAndUpdate"},function(t,e){e.resetOption("recreate")});var eM,nM="urn:schemas-microsoft-com:vml",iM="undefined"==typeof window?null:window,rM=!1,oM=iM&&iM.document;if(oM&&!Kf.canvasSupported)try{!oM.namespaces.zrvml&&oM.namespaces.add("zrvml",nM),eM=function(t){return oM.createElement("<zrvml:"+t+' class="zrvml">')}}catch(t){eM=function(t){return oM.createElement("<"+t+' xmlns="'+nM+'" class="zrvml">')}}var aM=Em.CMD,sM=Math.round,lM=Math.sqrt,hM=Math.abs,uM=Math.cos,cM=Math.sin,dM=Math.max;if(!Kf.canvasSupported){var fM=21600,pM=fM/2,gM=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=fM+","+fM,t.coordorigin="0,0"},mM=function(t){return String(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;")},vM=function(t,e,n){return"rgb("+[t,e,n].join(",")+")"},yM=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},xM=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},_M=function(t,e,n){return 1e5*(parseFloat(t)||0)+1e3*(parseFloat(e)||0)+n},wM=function(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t},bM=function(t,e,n){var i=Mt(e);n=+n,isNaN(n)&&(n=1),i&&(t.color=vM(i[0],i[1],i[2]),t.opacity=n*i[3])},SM=function(t){var e=Mt(t);return[vM(e[0],e[1],e[2]),e[3]]},MM=function(t,e,n){var i=e.fill;if(null!=i)if(i instanceof wv){var r,o=0,a=[0,0],s=0,l=1,h=n.getBoundingRect(),u=h.width,c=h.height;if("linear"===i.type){r="gradient";var d=n.transform,f=[i.x*u,i.y*c],p=[i.x2*u,i.y2*c];d&&($(f,f,d),$(p,p,d));var g=p[0]-f[0],m=p[1]-f[1];(o=180*Math.atan2(g,m)/Math.PI)<0&&(o+=360),o<1e-6&&(o=0)}else{r="gradientradial";var f=[i.x*u,i.y*c],d=n.transform,v=n.scale,y=u,x=c;a=[(f[0]-h.x)/y,(f[1]-h.y)/x],d&&$(f,f,d),y/=v[0]*fM,x/=v[1]*fM;var _=dM(y,x);s=0/_,l=2*i.r/_-s}var w=i.colorStops.slice();w.sort(function(t,e){return t.offset-e.offset});for(var b=w.length,S=[],M=[],I=0;I<b;I++){var T=w[I],C=SM(T.color);M.push(T.offset*l+s+" "+C[0]),0!==I&&I!==b-1||S.push(C)}if(b>=2){var D=S[0][0],A=S[1][0],k=S[0][1]*e.opacity,P=S[1][1]*e.opacity;t.type=r,t.method="none",t.focus="100%",t.angle=o,t.color=D,t.color2=A,t.colors=M.join(","),t.opacity=P,t.opacity2=k}"radial"===r&&(t.focusposition=a.join(","))}else bM(t,i,e.opacity)},IM=function(t,e){null!=e.lineDash&&(t.dashstyle=e.lineDash.join(" ")),null==e.stroke||e.stroke instanceof wv||bM(t,e.stroke,e.opacity)},TM=function(t,e,n,i){var r="fill"==e,o=t.getElementsByTagName(e)[0];null!=n[e]&&"none"!==n[e]&&(r||!r&&n.lineWidth)?(t[r?"filled":"stroked"]="true",n[e]instanceof wv&&xM(t,o),o||(o=gf(e)),r?MM(o,n,i):IM(o,n),yM(t,o)):(t[r?"filled":"stroked"]="false",xM(t,o))},CM=[[],[],[]],DM=function(t,e){var n,i,r,o,a,s,l=aM.M,h=aM.C,u=aM.L,c=aM.A,d=aM.Q,f=[],p=t.data,g=t.len();for(o=0;o<g;){switch(r=p[o++],i="",n=0,r){case l:i=" m ",n=1,a=p[o++],s=p[o++],CM[0][0]=a,CM[0][1]=s;break;case u:i=" l ",n=1,a=p[o++],s=p[o++],CM[0][0]=a,CM[0][1]=s;break;case d:case h:i=" c ",n=3;var m,v,y=p[o++],x=p[o++],_=p[o++],w=p[o++];r===d?(m=_,v=w,_=(_+2*y)/3,w=(w+2*x)/3,y=(a+2*y)/3,x=(s+2*x)/3):(m=p[o++],v=p[o++]),CM[0][0]=y,CM[0][1]=x,CM[1][0]=_,CM[1][1]=w,CM[2][0]=m,CM[2][1]=v,a=m,s=v;break;case c:var b=0,S=0,M=1,I=1,T=0;e&&(b=e[4],S=e[5],M=lM(e[0]*e[0]+e[1]*e[1]),I=lM(e[2]*e[2]+e[3]*e[3]),T=Math.atan2(-e[1]/I,e[0]/M));var C=p[o++],D=p[o++],A=p[o++],k=p[o++],P=p[o++]+T,L=p[o++]+P+T;o++;var O=p[o++],z=C+uM(P)*A,E=D+cM(P)*k,y=C+uM(L)*A,x=D+cM(L)*k,N=O?" wa ":" at ";Math.abs(z-y)<1e-4&&(Math.abs(L-P)>.01?O&&(z+=.0125):Math.abs(E-D)<1e-4?O&&z<C||!O&&z>C?x-=.0125:x+=.0125:O&&E<D||!O&&E>D?y+=.0125:y-=.0125),f.push(N,sM(((C-A)*M+b)*fM-pM),",",sM(((D-k)*I+S)*fM-pM),",",sM(((C+A)*M+b)*fM-pM),",",sM(((D+k)*I+S)*fM-pM),",",sM((z*M+b)*fM-pM),",",sM((E*I+S)*fM-pM),",",sM((y*M+b)*fM-pM),",",sM((x*I+S)*fM-pM)),a=y,s=x;break;case aM.R:var R=CM[0],B=CM[1];R[0]=p[o++],R[1]=p[o++],B[0]=R[0]+p[o++],B[1]=R[1]+p[o++],e&&($(R,R,e),$(B,B,e)),R[0]=sM(R[0]*fM-pM),B[0]=sM(B[0]*fM-pM),R[1]=sM(R[1]*fM-pM),B[1]=sM(B[1]*fM-pM),f.push(" m ",R[0],",",R[1]," l ",B[0],",",R[1]," l ",B[0],",",B[1]," l ",R[0],",",B[1]);break;case aM.Z:f.push(" x ")}if(n>0){f.push(i);for(var V=0;V<n;V++){var F=CM[V];e&&$(F,F,e),f.push(sM(F[0]*fM-pM),",",sM(F[1]*fM-pM),V<n-1?",":"")}}}return f.join("")};xi.prototype.brushVML=function(t){var e=this.style,n=this._vmlEl;n||(n=gf("shape"),gM(n),this._vmlEl=n),TM(n,"fill",e,this),TM(n,"stroke",e,this);var i=this.transform,r=null!=i,o=n.getElementsByTagName("stroke")[0];if(o){var a=e.lineWidth;if(r&&!e.strokeNoScale){var s=i[0]*i[3]-i[1]*i[2];a*=lM(hM(s))}o.weight=a+"px"}var l=this.path||(this.path=new Em);this.__dirtyPath&&(l.beginPath(),this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),n.path=DM(l,this.transform),n.style.zIndex=_M(this.zlevel,this.z,this.z2),yM(t,n),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},xi.prototype.onRemove=function(t){xM(t,this._vmlEl),this.removeRectText(t)},xi.prototype.onAdd=function(t){yM(t,this._vmlEl),this.appendRectText(t)};var AM=function(t){return"object"==typeof t&&t.tagName&&"IMG"===t.tagName.toUpperCase()};je.prototype.brushVML=function(t){var e,n,i=this.style,r=i.image;if(AM(r)){var o=r.src;if(o===this._imageSrc)e=this._imageWidth,n=this._imageHeight;else{var a=r.runtimeStyle,s=a.width,l=a.height;a.width="auto",a.height="auto",e=r.width,n=r.height,a.width=s,a.height=l,this._imageSrc=o,this._imageWidth=e,this._imageHeight=n}r=o}else r===this._imageSrc&&(e=this._imageWidth,n=this._imageHeight);if(r){var h=i.x||0,u=i.y||0,c=i.width,d=i.height,f=i.sWidth,p=i.sHeight,g=i.sx||0,m=i.sy||0,v=f&&p,y=this._vmlEl;y||(y=oM.createElement("div"),gM(y),this._vmlEl=y);var x,_=y.style,w=!1,b=1,S=1;if(this.transform&&(x=this.transform,b=lM(x[0]*x[0]+x[1]*x[1]),S=lM(x[2]*x[2]+x[3]*x[3]),w=x[1]||x[2]),w){var M=[h,u],I=[h+c,u],T=[h,u+d],C=[h+c,u+d];$(M,M,x),$(I,I,x),$(T,T,x),$(C,C,x);var D=dM(M[0],I[0],T[0],C[0]),A=dM(M[1],I[1],T[1],C[1]),k=[];k.push("M11=",x[0]/b,",","M12=",x[2]/S,",","M21=",x[1]/b,",","M22=",x[3]/S,",","Dx=",sM(h*b+x[4]),",","Dy=",sM(u*S+x[5])),_.padding="0 "+sM(D)+"px "+sM(A)+"px 0",_.filter="progid:DXImageTransform.Microsoft.Matrix("+k.join("")+", SizingMethod=clip)"}else x&&(h=h*b+x[4],u=u*S+x[5]),_.filter="",_.left=sM(h)+"px",_.top=sM(u)+"px";var P=this._imageEl,L=this._cropEl;P||(P=oM.createElement("div"),this._imageEl=P);var O=P.style;if(v){if(e&&n)O.width=sM(b*e*c/f)+"px",O.height=sM(S*n*d/p)+"px";else{var z=new Image,E=this;z.onload=function(){z.onload=null,e=z.width,n=z.height,O.width=sM(b*e*c/f)+"px",O.height=sM(S*n*d/p)+"px",E._imageWidth=e,E._imageHeight=n,E._imageSrc=r},z.src=r}L||((L=oM.createElement("div")).style.overflow="hidden",this._cropEl=L);var N=L.style;N.width=sM((c+g*c/f)*b),N.height=sM((d+m*d/p)*S),N.filter="progid:DXImageTransform.Microsoft.Matrix(Dx="+-g*c/f*b+",Dy="+-m*d/p*S+")",L.parentNode||y.appendChild(L),P.parentNode!=L&&L.appendChild(P)}else O.width=sM(b*c)+"px",O.height=sM(S*d)+"px",y.appendChild(P),L&&L.parentNode&&(y.removeChild(L),this._cropEl=null);var R="",B=i.opacity;B<1&&(R+=".Alpha(opacity="+sM(100*B)+") "),R+="progid:DXImageTransform.Microsoft.AlphaImageLoader(src="+r+", SizingMethod=scale)",O.filter=R,y.style.zIndex=_M(this.zlevel,this.z,this.z2),yM(t,y),null!=i.text&&this.drawRectText(t,this.getBoundingRect())}},je.prototype.onRemove=function(t){xM(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},je.prototype.onAdd=function(t){yM(t,this._vmlEl),this.appendRectText(t)};var kM,PM={},LM=0,OM=document.createElement("div"),zM=function(t){var e=PM[t];if(!e){LM>100&&(LM=0,PM={});var n,i=OM.style;try{i.font=t,n=i.fontFamily.split(",")[0]}catch(t){}e={style:i.fontStyle||"normal",variant:i.fontVariant||"normal",weight:i.fontWeight||"normal",size:0|parseFloat(i.fontSize||12),family:n||"Microsoft YaHei"},PM[t]=e,LM++}return e};!function(t,e){xg[t]=e}("measureText",function(t,e){var n=oM;kM||((kM=n.createElement("div")).style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",oM.body.appendChild(kM));try{kM.style.font=e}catch(t){}return kM.innerHTML="",kM.appendChild(n.createTextNode(t)),{width:kM.offsetWidth}});for(var EM=new Xt,NM=[Sg,Xe,je,xi,ov],RM=0;RM<NM.length;RM++){var BM=NM[RM].prototype;BM.drawRectText=function(t,e,n,i){var r=this.style;this.__dirty&&De(r);var o=r.text;if(null!=o&&(o+=""),o){if(r.rich){var a=Me(o,r);o=[];for(var s=0;s<a.lines.length;s++){for(var l=a.lines[s].tokens,h=[],u=0;u<l.length;u++)h.push(l[u].text);o.push(h.join(""))}o=o.join("\n")}var c,d,f=r.textAlign,p=r.textVerticalAlign,g=zM(r.font),m=g.style+" "+g.variant+" "+g.weight+" "+g.size+'px "'+g.family+'"';n=n||ce(o,m,f,p);var v=this.transform;if(v&&!i&&(EM.copy(e),EM.applyTransform(v),e=EM),i)c=e.x,d=e.y;else{var y=r.textPosition,x=r.textDistance;if(y instanceof Array)c=e.x+wM(y[0],e.width),d=e.y+wM(y[1],e.height),f=f||"left";else{var _=me(y,e,x);c=_.x,d=_.y,f=f||_.textAlign,p=p||_.textVerticalAlign}}c=pe(c,n.width,f),d=ge(d,n.height,p),d+=n.height/2;var w,b,S,M=gf,I=this._textVmlEl;I?b=(w=(S=I.firstChild).nextSibling).nextSibling:(I=M("line"),w=M("path"),b=M("textpath"),S=M("skew"),b.style["v-text-align"]="left",gM(I),w.textpathok=!0,b.on=!0,I.from="0 0",I.to="1000 0.05",yM(I,S),yM(I,w),yM(I,b),this._textVmlEl=I);var T=[c,d],C=I.style;v&&i?($(T,T,v),S.on=!0,S.matrix=v[0].toFixed(3)+","+v[2].toFixed(3)+","+v[1].toFixed(3)+","+v[3].toFixed(3)+",0,0",S.offset=(sM(T[0])||0)+","+(sM(T[1])||0),S.origin="0 0",C.left="0px",C.top="0px"):(S.on=!1,C.left=sM(c)+"px",C.top=sM(d)+"px"),b.string=mM(o);try{b.style.font=m}catch(t){}TM(I,"fill",{fill:r.textFill,opacity:r.opacity},this),TM(I,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash},this),I.style.zIndex=_M(this.zlevel,this.z,this.z2),yM(t,I)}},BM.removeRectText=function(t){xM(t,this._textVmlEl),this._textVmlEl=null},BM.appendRectText=function(t){yM(t,this._textVmlEl)}}ov.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},ov.prototype.onRemove=function(t){this.removeRectText(t)},ov.prototype.onAdd=function(t){this.appendRectText(t)}}yf.prototype={constructor:yf,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,n=0;n<t.length;n++){var i=t[n];i.invisible||i.ignore?(i.__alreadyNotVisible||i.onRemove(e),i.__alreadyNotVisible=!0):(i.__alreadyNotVisible&&i.onAdd(e),i.__alreadyNotVisible=!1,i.__dirty&&(i.beforeBrush&&i.beforeBrush(),(i.brushVML||i.brush).call(i,e),i.afterBrush&&i.afterBrush())),i.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){var t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;if(this._width!=t||this._height!=e){this._width=t,this._height=e;var n=this._vmlViewport.style;n.width=t+"px",n.height=e+"px"}},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||vf(e.width))-vf(e.paddingLeft)-vf(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||vf(e.height))-vf(e.paddingTop)-vf(e.paddingBottom)|0}},d(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){yf.prototype[t]=xf(t)}),vn("vml",yf);var VM="http://www.w3.org/2000/svg",FM=Em.CMD,HM=Array.prototype.join,GM="none",WM=Math.round,ZM=Math.sin,UM=Math.cos,XM=Math.PI,jM=2*Math.PI,YM=180/XM,qM=1e-4,$M={};$M.brush=function(t){var e=t.style,n=t.__svgEl;n||(n=_f("path"),t.__svgEl=n),t.path||t.createPathProxy();var i=t.path;if(t.__dirtyPath){i.beginPath(),t.buildPath(i,t.shape),t.__dirtyPath=!1;var r=Af(i);r.indexOf("NaN")<0&&Tf(n,"d",r)}Df(n,e),If(n,t.transform),null!=e.text&&tI(t,t.getBoundingRect())};var KM={};KM.brush=function(t){var e=t.style,n=e.image;if(n instanceof HTMLImageElement&&(n=n.src),n){var i=e.x||0,r=e.y||0,o=e.width,a=e.height,s=t.__svgEl;s||(s=_f("image"),t.__svgEl=s),n!==t.__imageSrc&&(Cf(s,"href",n),t.__imageSrc=n),Tf(s,"width",o),Tf(s,"height",a),Tf(s,"x",i),Tf(s,"y",r),If(s,t.transform),null!=e.text&&tI(t,t.getBoundingRect())}};var QM={},JM=new Xt,tI=function(t,e,n){var i=t.style;t.__dirty&&De(i);var r=i.text;if(null!=r){r+="";var o=t.__textSvgEl;o||(o=_f("text"),t.__textSvgEl=o);var a,s,l=i.textPosition,h=i.textDistance,u=i.textAlign||"left";"number"==typeof i.fontSize&&(i.fontSize+="px");var c=i.font||[i.fontStyle||"",i.fontWeight||"",i.fontSize||"",i.fontFamily||""].join(" ")||yg,d=kf(i.textVerticalAlign),f=(n=ce(r,c,u,d)).lineHeight;if(l instanceof Array)a=e.x+l[0],s=e.y+l[1];else{var p=me(l,e,h);a=p.x,s=p.y,d=kf(p.textVerticalAlign),u=p.textAlign}Tf(o,"alignment-baseline",d),c&&(o.style.font=c);var g=i.textPadding;if(Tf(o,"x",a),Tf(o,"y",s),Df(o,i,!0),t instanceof ov||t.style.transformText)If(o,t.transform);else{if(t.transform)JM.copy(e),JM.applyTransform(t.transform),e=JM;else{var m=t.transformCoordToGlobal(e.x,e.y);e.x=m[0],e.y=m[1]}var v=i.textOrigin;"center"===v?(a=n.width/2+a,s=n.height/2+s):v&&(a=v[0]+a,s=v[1]+s),Tf(o,"transform","rotate("+180*-i.textRotation/Math.PI+","+a+","+s+")")}var y=r.split("\n"),x=y.length,_=u;"left"===_?(_="start",g&&(a+=g[3])):"right"===_?(_="end",g&&(a-=g[1])):"center"===_&&(_="middle",g&&(a+=(g[3]-g[1])/2));var w=0;if("baseline"===d?(w=-n.height+f,g&&(w-=g[2])):"middle"===d?(w=(-n.height+f)/2,g&&(s+=(g[0]-g[2])/2)):g&&(w+=g[0]),t.__text!==r||t.__textFont!==c){var b=t.__tspanList||[];t.__tspanList=b;for(M=0;M<x;M++)(I=b[M])?I.innerHTML="":(I=b[M]=_f("tspan"),o.appendChild(I),Tf(I,"alignment-baseline",d),Tf(I,"text-anchor",_)),Tf(I,"x",a),Tf(I,"y",s+M*f+w),I.appendChild(document.createTextNode(y[M]));for(;M<b.length;M++)o.removeChild(b[M]);b.length=x,t.__text=r,t.__textFont=c}else if(t.__tspanList.length)for(var S=t.__tspanList.length,M=0;M<S;++M){var I=t.__tspanList[M];I&&(Tf(I,"x",a),Tf(I,"y",s+M*f+w))}}};QM.drawRectText=tI,QM.brush=function(t){var e=t.style;null!=e.text&&(e.textPosition=[0,0],tI(t,{x:e.x||0,y:e.y||0,width:0,height:0},t.getBoundingRect()))},Pf.prototype={diff:function(t,e,n){n||(n=function(t,e){return t===e}),this.equals=n;var i=this;t=t.slice();var r=(e=e.slice()).length,o=t.length,a=1,s=r+o,l=[{newPos:-1,components:[]}],h=this.extractCommon(l[0],e,t,0);if(l[0].newPos+1>=r&&h+1>=o){for(var u=[],c=0;c<e.length;c++)u.push(c);return[{indices:u,count:e.length}]}for(;a<=s;){var d=function(){for(var n=-1*a;n<=a;n+=2){var s,h=l[n-1],u=l[n+1],c=(u?u.newPos:0)-n;h&&(l[n-1]=void 0);var d=h&&h.newPos+1<r,f=u&&0<=c&&c<o;if(d||f){if(!d||f&&h.newPos<u.newPos?(s=Of(u),i.pushComponent(s.components,void 0,!0)):((s=h).newPos++,i.pushComponent(s.components,!0,void 0)),c=i.extractCommon(s,e,t,n),s.newPos+1>=r&&c+1>=o)return Lf(0,s.components);l[n]=s}else l[n]=void 0}a++}();if(d)return d}},pushComponent:function(t,e,n){var i=t[t.length-1];i&&i.added===e&&i.removed===n?t[t.length-1]={count:i.count+1,added:e,removed:n}:t.push({count:1,added:e,removed:n})},extractCommon:function(t,e,n,i){for(var r=e.length,o=n.length,a=t.newPos,s=a-i,l=0;a+1<r&&s+1<o&&this.equals(e[a+1],n[s+1]);)a++,s++,l++;return l&&t.components.push({count:l}),t.newPos=a,s},tokenize:function(t){return t.slice()},join:function(t){return t.slice()}};var eI=new Pf,nI=function(t,e,n){return eI.diff(t,e,n)};zf.prototype.createElement=_f,zf.prototype.getDefs=function(t){var e=this._svgRoot,n=this._svgRoot.getElementsByTagName("defs");return 0===n.length?t?((n=e.insertBefore(this.createElement("defs"),e.firstChild)).contains||(n.contains=function(t){var e=n.children;if(!e)return!1;for(var i=e.length-1;i>=0;--i)if(e[i]===t)return!0;return!1}),n):null:n[0]},zf.prototype.update=function(t,e){if(t){var n=this.getDefs(!1);if(t[this._domName]&&n.contains(t[this._domName]))"function"==typeof e&&e(t);else{var i=this.add(t);i&&(t[this._domName]=i)}}},zf.prototype.addDom=function(t){this.getDefs(!0).appendChild(t)},zf.prototype.removeDom=function(t){var e=this.getDefs(!1);e&&t[this._domName]&&(e.removeChild(t[this._domName]),t[this._domName]=null)},zf.prototype.getDoms=function(){var t=this.getDefs(!1);if(!t)return[];var e=[];return d(this._tagNames,function(n){var i=t.getElementsByTagName(n);e=e.concat([].slice.call(i))}),e},zf.prototype.markAllUnused=function(){var t=this;d(this.getDoms(),function(e){e[t._markLabel]="0"})},zf.prototype.markUsed=function(t){t&&(t[this._markLabel]="1")},zf.prototype.removeUnused=function(){var t=this.getDefs(!1);if(t){var e=this;d(this.getDoms(),function(n){"1"!==n[e._markLabel]&&t.removeChild(n)})}},zf.prototype.getSvgProxy=function(t){return t instanceof xi?$M:t instanceof je?KM:t instanceof ov?QM:$M},zf.prototype.getTextSvgElement=function(t){return t.__textSvgEl},zf.prototype.getSvgElement=function(t){return t.__svgEl},h(Ef,zf),Ef.prototype.addWithoutUpdate=function(t,e){if(e&&e.style){var n=this;d(["fill","stroke"],function(i){if(e.style[i]&&("linear"===e.style[i].type||"radial"===e.style[i].type)){var r,o=e.style[i],a=n.getDefs(!0);o._dom?(r=o._dom,a.contains(o._dom)||n.addDom(r)):r=n.add(o),n.markUsed(e);var s=r.getAttribute("id");t.setAttribute(i,"url(#"+s+")")}})}},Ef.prototype.add=function(t){var e;if("linear"===t.type)e=this.createElement("linearGradient");else{if("radial"!==t.type)return jp("Illegal gradient type."),null;e=this.createElement("radialGradient")}return t.id=t.id||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-gradient-"+t.id),this.updateDom(t,e),this.addDom(e),e},Ef.prototype.update=function(t){var e=this;zf.prototype.update.call(this,t,function(){var n=t.type,i=t._dom.tagName;"linear"===n&&"linearGradient"===i||"radial"===n&&"radialGradient"===i?e.updateDom(t,t._dom):(e.removeDom(t),e.add(t))})},Ef.prototype.updateDom=function(t,e){if("linear"===t.type)e.setAttribute("x1",t.x),e.setAttribute("y1",t.y),e.setAttribute("x2",t.x2),e.setAttribute("y2",t.y2);else{if("radial"!==t.type)return void jp("Illegal gradient type.");e.setAttribute("cx",t.x),e.setAttribute("cy",t.y),e.setAttribute("r",t.r)}t.global?e.setAttribute("gradientUnits","userSpaceOnUse"):e.setAttribute("gradientUnits","objectBoundingBox"),e.innerHTML="";for(var n=t.colorStops,i=0,r=n.length;i<r;++i){var o=this.createElement("stop");o.setAttribute("offset",100*n[i].offset+"%"),o.setAttribute("stop-color",n[i].color),e.appendChild(o)}t._dom=e},Ef.prototype.markUsed=function(t){if(t.style){var e=t.style.fill;e&&e._dom&&zf.prototype.markUsed.call(this,e._dom),(e=t.style.stroke)&&e._dom&&zf.prototype.markUsed.call(this,e._dom)}},h(Nf,zf),Nf.prototype.update=function(t){var e=this.getSvgElement(t);e&&this.updateDom(e,t.__clipPaths,!1);var n=this.getTextSvgElement(t);n&&this.updateDom(n,t.__clipPaths,!0),this.markUsed(t)},Nf.prototype.updateDom=function(t,e,n){if(e&&e.length>0){var i,r,o=this.getDefs(!0),a=e[0],s=n?"_textDom":"_dom";a[s]?(r=a[s].getAttribute("id"),i=a[s],o.contains(i)||o.appendChild(i)):(r="zr"+this._zrId+"-clip-"+this.nextId,++this.nextId,(i=this.createElement("clipPath")).setAttribute("id",r),o.appendChild(i),a[s]=i);var l=this.getSvgProxy(a);if(a.transform&&a.parent.invTransform&&!n){var h=Array.prototype.slice.call(a.transform);st(a.transform,a.parent.invTransform,a.transform),l.brush(a),a.transform=h}else l.brush(a);var u=this.getSvgElement(a);i.innerHTML="",i.appendChild(u.cloneNode()),t.setAttribute("clip-path","url(#"+r+")"),e.length>1&&this.updateDom(i,e.slice(1),n)}else t&&t.setAttribute("clip-path","none")},Nf.prototype.markUsed=function(t){var e=this;t.__clipPaths&&t.__clipPaths.length>0&&d(t.__clipPaths,function(t){t._dom&&zf.prototype.markUsed.call(e,t._dom),t._textDom&&zf.prototype.markUsed.call(e,t._textDom)})},h(Rf,zf),Rf.prototype.addWithoutUpdate=function(t,e){if(e&&Bf(e.style)){var n,i=e.style;i._shadowDom?(n=i._shadowDom,this.getDefs(!0).contains(i._shadowDom)||this.addDom(n)):n=this.add(e),this.markUsed(e);var r=n.getAttribute("id");t.style.filter="url(#"+r+")"}},Rf.prototype.add=function(t){var e=this.createElement("filter"),n=t.style;return n._shadowDomId=n._shadowDomId||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-shadow-"+n._shadowDomId),this.updateDom(t,e),this.addDom(e),e},Rf.prototype.update=function(t,e){var n=e.style;if(Bf(n)){var i=this;zf.prototype.update.call(this,e,function(t){i.updateDom(e,t._shadowDom)})}else this.remove(t,n)},Rf.prototype.remove=function(t,e){null!=e._shadowDomId&&(this.removeDom(e),t.style.filter="")},Rf.prototype.updateDom=function(t,e){var n=e.getElementsByTagName("feDropShadow");n=0===n.length?this.createElement("feDropShadow"):n[0];var i,r,o,a,s=t.style,l=t.scale?t.scale[0]||1:1,h=t.scale?t.scale[1]||1:1;if(s.shadowBlur||s.shadowOffsetX||s.shadowOffsetY)i=s.shadowOffsetX||0,r=s.shadowOffsetY||0,o=s.shadowBlur,a=s.shadowColor;else{if(!s.textShadowBlur)return void this.removeDom(e,s);i=s.textShadowOffsetX||0,r=s.textShadowOffsetY||0,o=s.textShadowBlur,a=s.textShadowColor}n.setAttribute("dx",i/l),n.setAttribute("dy",r/h),n.setAttribute("flood-color",a);var u=o/2/l+" "+o/2/h;n.setAttribute("stdDeviation",u),e.setAttribute("x","-100%"),e.setAttribute("y","-100%"),e.setAttribute("width",Math.ceil(o/2*200)+"%"),e.setAttribute("height",Math.ceil(o/2*200)+"%"),e.appendChild(n),s._shadowDom=e},Rf.prototype.markUsed=function(t){var e=t.style;e&&e._shadowDom&&zf.prototype.markUsed.call(this,e._shadowDom)};var iI=function(t,e,n,i){this.root=t,this.storage=e,this._opts=n=o({},n||{});var r=_f("svg");r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("version","1.1"),r.setAttribute("baseProfile","full"),r.style.cssText="user-select:none;position:absolute;left:0;top:0;",this.gradientManager=new Ef(i,r),this.clipPathManager=new Nf(i,r),this.shadowManager=new Rf(i,r);var a=document.createElement("div");a.style.cssText="overflow:hidden;position:relative",this._svgRoot=r,this._viewport=a,t.appendChild(a),a.appendChild(r),this.resize(n.width,n.height),this._visibleList=[]};iI.prototype={constructor:iI,getType:function(){return"svg"},getViewportRoot:function(){return this._viewport},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0);this._paintList(t)},setBackgroundColor:function(t){this._viewport.style.background=t},_paintList:function(t){this.gradientManager.markAllUnused(),this.clipPathManager.markAllUnused(),this.shadowManager.markAllUnused();var e,n=this._svgRoot,i=this._visibleList,r=t.length,o=[];for(e=0;e<r;e++){var a=Ff(f=t[e]),s=Xf(f)||Uf(f);f.invisible||(f.__dirty&&(a&&a.brush(f),this.clipPathManager.update(f),f.style&&(this.gradientManager.update(f.style.fill),this.gradientManager.update(f.style.stroke),this.shadowManager.update(s,f)),f.__dirty=!1),o.push(f))}var l,h=nI(i,o);for(e=0;e<h.length;e++)if((c=h[e]).removed)for(d=0;d<c.count;d++){var s=Xf(f=i[c.indices[d]]),u=Uf(f);Zf(n,s),Zf(n,u)}for(e=0;e<h.length;e++){var c=h[e];if(c.added)for(d=0;d<c.count;d++){var s=Xf(f=o[c.indices[d]]),u=Uf(f);l?Gf(n,s,l):Wf(n,s),s?Gf(n,u,s):l?Gf(n,u,l):Wf(n,u),Gf(n,u,s),l=u||s||l,this.gradientManager.addWithoutUpdate(s,f),this.shadowManager.addWithoutUpdate(l,f),this.clipPathManager.markUsed(f)}else if(!c.removed)for(var d=0;d<c.count;d++){var f=o[c.indices[d]];l=s=Uf(f)||Xf(f)||l,this.gradientManager.markUsed(f),this.gradientManager.addWithoutUpdate(s,f),this.shadowManager.markUsed(f),this.shadowManager.addWithoutUpdate(s,f),this.clipPathManager.markUsed(f)}}this.gradientManager.removeUnused(),this.clipPathManager.removeUnused(),this.shadowManager.removeUnused(),this._visibleList=o},_getDefs:function(t){var e=this._svgRoot,n=this._svgRoot.getElementsByTagName("defs");return 0===n.length?t?((n=e.insertBefore(_f("defs"),e.firstChild)).contains||(n.contains=function(t){var e=n.children;if(!e)return!1;for(var i=e.length-1;i>=0;--i)if(e[i]===t)return!0;return!1}),n):null:n[0]},resize:function(t,e){var n=this._viewport;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!==t||this._height!==e){this._width=t,this._height=e;var r=n.style;r.width=t+"px",r.height=e+"px";var o=this._svgRoot;o.setAttribute("width",t),o.setAttribute("height",e)}},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[i]||Vf(s[n])||Vf(a.style[n]))-(Vf(s[r])||0)-(Vf(s[o])||0)|0},dispose:function(){this.root.innerHTML="",this._svgRoot=this._viewport=this.storage=null},clear:function(){this._viewport&&this.root.removeChild(this._viewport)},pathToDataUrl:function(){return this.refresh(),"data:image/svg+xml;charset=UTF-8,"+this._svgRoot.outerHTML}},d(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){iI.prototype[t]=jf(t)}),vn("svg",iI),t.version="4.0.4",t.dependencies=dx,t.PRIORITY=mx,t.init=function(t,e,n){var i=qa(t);if(i)return i;var r=new Da(t,e,n);return r.id="ec_"+zx++,Lx[r.id]=r,Pn(t,Nx,r.id),ja(r),r},t.connect=function(t){if(y(t)){var e=t;t=null,lx(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+Ex++,lx(e,function(e){e.group=t})}return Ox[t]=!0,t},t.disConnect=Ya,t.disconnect=Bx,t.dispose=function(t){"string"==typeof t?t=Lx[t]:t instanceof Da||(t=qa(t)),t instanceof Da&&!t.isDisposed()&&t.dispose()},t.getInstanceByDom=qa,t.getInstanceById=function(t){return Lx[t]},t.registerTheme=$a,t.registerPreprocessor=Ka,t.registerProcessor=Qa,t.registerPostUpdate=function(t){Dx.push(t)},t.registerAction=Ja,t.registerCoordinateSystem=function(t,e){yo.register(t,e)},t.getCoordinateSystemDimensions=function(t){var e=yo.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},t.registerLayout=ts,t.registerVisual=es,t.registerLoading=is,t.extendComponentModel=rs,t.extendComponentView=os,t.extendSeriesModel=as,t.extendChartView=ss,t.setCanvasCreator=function(t){e("createCanvas",t)},t.registerMap=function(t,e,n){e.geoJson&&!e.features&&(n=e.specialAreas,e=e.geoJson),"string"==typeof e&&(e="undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")()),Rx[t]={geoJson:e,specialAreas:n}},t.getMap=function(t){return Rx[t]},t.dataTool=Vx,t.zrender=Wg,t.graphic=Av,t.number=Vv,t.format=Uv,t.throttle=la,t.helper=A_,t.matrix=Mp,t.vector=vp,t.color=Hp,t.parseGeoJSON=P_,t.parseGeoJson=E_,t.util=N_,t.List=Yx,t.Model=pr,t.Axis=z_,t.env=Kf});
