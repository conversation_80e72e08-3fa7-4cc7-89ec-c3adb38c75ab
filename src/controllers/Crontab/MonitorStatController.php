<?php
/**
 * Copyright (c) 2020,2345
 * 摘    要：
 * 作    者：zhub
 * 修改日期：2021-05-08
 */

namespace Crontab;

use Admin\SmsStatModel;
use Controller;
use Octopus\PdoEx;
use Service\Func\SmsFunc;
use WebLogger\Facade\LoggerFacade;

class MonitorStatController extends Controller
{
    /**
     * SmsStatController constructor.
     */
    public function __construct()
    {
        $dbConfig = \Config::get("database");
        $this->redis = \RedisAction::connect();
        $this->dbName = DATABASE_SMS;
        $this->pdo = PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);

        parent::__construct();
    }

    /**
     * 作    者: zhub
     * 功    能: 监控业务
     * 修改日期: 2021-05-08
     * @param int $failReasonGapTime 最近多长时间失败原因统计
     * @param int $failGapTime 最近多长时间没有成功
     * @return void
     */
    public function actionMonitor(int $failReasonGapTime = 600, int $failGapTime = 3600)
    {
        // 处理失败的订单
        $this->dealFail($failReasonGapTime);
        // 处理服务商没有成功订单
        $this->dealChannelFail($failGapTime);
    }

    /**
     * 作    者: zhub
     * 功    能: 处理失败的订单
     * @param int $time 最近多长时间
     * 修改日期: 2021-05-08
     * @return void
     */
    private function dealFail(int $time = 3600)
    {
        $smsStatModel = SmsStatModel::getInstance();
        $data = $smsStatModel->getSmsFailMsg($time);
        $responses = [];
        $responseStr = $_ENV['VALID_RESPONSE'];
        $responseStr = mb_convert_encoding($responseStr, 'GBK', 'utf-8');
        $validResponse = explode(",", $responseStr);
        foreach ((array)$data as $k => $v) {
            $response = $v['send_response'] . "_" . strval($v['code_desc']);
            if (!in_array($response, $validResponse)) {
                $responses[] = $response;
            }
        }
        if ($responses) {
            LoggerFacade::alert("存在未知的短信失败返回", $responses);
        }
    }

    /**
     * 作    者: zhub
     * 功    能: 处理没有的订单账号
     * @param int $time 最近多长时间
     * 修改日期: 2021-05-08
     * @return void
     */
    private function dealChannelFail(int $time = 3600)
    {
        $successStatus = 1;
        $failStatus = 0;
        $channelStr = $_ENV['MONITOR_CHANNELS'];
        $channels = explode(",", $channelStr);
        foreach ((array)$channels as $channel) {
            $smsStatModel = SmsStatModel::getInstance();
            $num = $smsStatModel->getChannelTotalNum($successStatus, $channel, $time);
            if (empty($num)) {
                $failNum = $smsStatModel->getChannelTotalNum($failStatus, $channel, $time);
                $num1 = $smsStatModel->getChannelTotalNum($successStatus, $channel, $time, 86400);
                $num2 = $smsStatModel->getChannelTotalNum($successStatus, $channel, $time, 86400 * 7);
                if ($failNum || $num1 || $num2) {
                    LoggerFacade::alert("服务商通道近期没有成功订单", ['channel' => $channel, 'today' => $num, 'todayFail' => $failNum, 'yesterday' => $num1, 'lastWeek' => $num2]);
                }
            }
        }
    }

    /**
     * 监控发送成功率异常
     * @return void
     */
    public function actionSendRatioAbnormal()
    {
        date_default_timezone_set('Asia/Shanghai');
        $ts = time() - 3600;
        $start = date("Y-m-d H:00:00", $ts);
        $end = date("Y-m-d H:59:59", $ts);
        $table = "sms_logs_archive_" . date("Ym", $ts);

        $sql = sprintf("select account,max(channel) as channel,count(1) as cnt,sum(case when callback_status=1 then 1 else 0 end) as succ_cnt from  $table where send_time between '%s' and '%s' and account !='' group by account", $start, $end);
        $list = $this->pdo->findAll($sql);
        foreach ($list as $key => $item) {
            SmsFunc::MonitorSendRatioAbnormal(date("Y-m-d H", $ts), $item);
        }
    }
}