<!DOCTYPE html>
<html>
    <head>
        <meta content="text/html; charset=gb2312" http-equiv="Content-Type"/>
        <title>2345帐号登录</title>
        <link href="/browser2/css/sign_20140421.css" rel="stylesheet" type="text/css" />
        <script src="/browser2/js/login.js" type="text/javascript"></script>
        <script type="text/javascript">
            function check(){
            if (document.getElementById("automatic").checked){
            window.external.RCCoralSetIsAutoLogin(true);
            } else{
            window.external.RCCoralSetIsAutoLogin(false);
            }
            }
        </script>
    </head>
    <body scroll="no" style="overflow:hidden;">
        <div class="sign_success_hd">
            <div class="wp">
                <div class="logo"></div>
                <div class="home">
                    <a href="http://ie.2345.com" target="_blank">官方网站</a>
                </div>
            </div>
        </div>
        <div class="sign_success_co">
            <div class="wp">
                <form action="" method="post" onsubmit="check();">
                    <h1 class="{{if $pageArray.type=='qq'}}qq{{elseif $pageArray.type=='weibo'}}sina{{/if}}_big">2345王牌浏览器 登录成功</h1>
                    <h3>恭喜您成功登陆2345王牌浏览器  欢迎关注我们的微博</h3>
                    <p{{if $pageArray.type!='weibo'}} style="text-align:center;"{{/if}}>
                        <input type="hidden" name="act" value="done"/>
                        <input checked="checked" class="check1" id="automatic" type="checkbox" value="1" />
                        <label for="automatic">下次自动登录浏览器</label>
                        {{if $pageArray.type=='weibo'}}
                        <input checked="checked" class="check1" id="concerned" name="concerned" type="checkbox" value="1" />
                        <label for="concerned">关注2345王牌浏览器</label>
                        {{/if}}
                    </p>
                    <p>
                        <input class="sign_btn" style="border:none;cursor:pointer;" type="submit" value="完成" />
                    </p>
                </form>
            </div>
        </div>
        <script type="text/javascript">
                    function loadJs(_url){
                    var callback = arguments[1] || function(){};
                            var _js = document.createElement("script");
                            _js.setAttribute("type", "text/javascript");
                            _js.setAttribute("src", _url);
                            _js.setAttribute("defer", true);
                            if (document.all){
                    _js.onreadystatechange = function(){
                    if (this.readyState == "loaded" || this.readyState == 4 || this.readyState == "complete"){
                    setTimeout(callback, 10);
                    }
                    };
                    } else{
                    _js.onload = function(){
                    callback();
                    };
                    }
                    document.getElementsByTagName("head")[0].appendChild(_js);
                    }
            function cc(a) {
            var b = arguments,
                    web = "ajax62",
                    a2,
                    i1 = document.cookie.indexOf("uUiD="),
                    i2;
                    if (b.length > 1) web = b[1];
                    if (i1 != - 1) {
            i2 = document.cookie.indexOf(";", i1);
                    a2 = i2 != - 1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
            }
            if (!a2) {
            a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                    document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
            }
            if (a.length > 0) {
            var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                    loadJs(c);
            }
            return true;
            }
            {{if $pageArray.type == 'weibo'}}
            cc('login_weibo_success');
            {{elseif $pageArray.type == 'qq'}}
            cc('login_qq_success');
            {{/if}}
        </script>
    </body>
</html>
