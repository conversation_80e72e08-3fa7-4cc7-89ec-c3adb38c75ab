<?php

namespace Admin;

class SmsQueryLogModel extends \Model
{

    /**
     * 初始数据库
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    public function getSmsLog($data = array(), $limit = array())
    {
        $paramArr = array();
        $col = 'log_id,phone,send_time,text,send_status,send_response,type,business_id,pid,channel,code_status,msg_id,sms_count,code_desc,callback_time,callback_status,callback_message,client_ip,server_ip';

        if (isset($data['count']))
        {
            $col = ' count(1) as total ';
        }

        if (strtotime($data['dateRange'][0]) >= strtotime($data['dateRange'][1]))
        {
            return array();
        }

        if (date('Ym', strtotime($data['dateRange'][0])) !== date('Ym', strtotime($data['dateRange'][1])))
        {
            return array();
        }
        if (date('Ym', strtotime($data['dateRange'][0])) >= 201805)
        {
            $col .= ',account,code_time';
        }
        if (date('Ym', strtotime($data['dateRange'][0])) >= 201811)
        {
            $col .= ',encrypt_phone';
        }
        $table = self::getTableName(date('Ym', strtotime($data['dateRange'][0])));
        if (!$this->checkTableExist($table))
        {
            return array();
        }
        $sql = "SELECT {$col} FROM `{$table}` AS msl ";
        $sql .= ' WHERE 1 ';

        $sql .= ' AND send_time BETWEEN :startTime AND :endTime ';
        $paramArr[':startTime'] = $data['dateRange'][0];
        $paramArr[':endTime'] = $data['dateRange'][1];
        if (!empty($data['logId']))
        {
            $sql .= ' AND log_id = :log_id ';
            $paramArr[':log_id'] = $data['logId'];
        }
        if (!empty($data['phone']))
        {
            if (strlen($data['phone']) === 11)
            {
                /*加密代码上线之前的数据*/
                if ($data['dateRange'][1] <= '2018-11-01 10:30')
                {
                    $sql .= ' AND phone = :phone ';
                    $paramArr[':phone'] = $data['phone'];
                }
                else
                {
                    $data['phone'] = \SecurityAction::desensitize($data['phone']);
                    $sql .= ' AND phone = :phone AND encrypt_phone = :encrypt_phone';
                    $paramArr[':phone'] = $data['phone'];
                    $paramArr[':encrypt_phone'] = $data['encrypt_phone'];
                }
            }
            else
            {
                $sql .= ' AND phone LIKE :phone ';
                $paramArr[':phone'] = $data['phone'] . '%';
            }
        }
        if (!empty($data['operator']))
        {
            $sql .= ' AND channel = :channel ';
            $paramArr[':channel'] = $data['operator'];
        }
        if (!empty($data['type']))
        {
            $sql .= ' AND type = :type ';
            $paramArr[':type'] = $data['type'];
        }
        if (!empty($data['callbackStatus']))
        {
            switch ($data['callbackStatus'])
            {
                case 0://全部
                default:
                    break;
                case 1://成功
                    $sql .= ' AND callback_status = :callbackStatus ';
                    $paramArr[':callbackStatus'] = 1;
                    break;
                case 2://失败
                    $sql .= ' AND callback_status = :callbackStatus ';
                    $paramArr[':callbackStatus'] = 0;
                    break;
                case 9://尚无数据
                    $sql .= " AND callback_status is NULL ";
                    break;
            }
        }
        if (!empty($data['submitStatus']))
        {
            switch ($data['submitStatus'])
            {
                case 0://全部
                default:
                    break;
                case 1://成功
                    $sql .= ' AND send_status > :status0 ';
                    $paramArr[':status0'] = 0;
                    break;
                case 2://失败
                    $sql .= ' AND send_status <= :status0 ';
                    $paramArr[':status0'] = 0;
                    break;
                case 9://尚无数据
                    $sql .= " AND send_status is NULL ";
                    break;
            }
        }
        /*
        if( !empty($data['msgId']) )
        {
            $sql .= ' AND mslc.msgId = :msgId ';
            $paramArr[':msgId'] = $data['msgId'];
        }
        */
        if (!empty($data['position']))
        {
            $sql .= ' AND business_id = :position ';
            $paramArr[':position'] = $data['position'];
        }

        if (!empty($data['project']))
        {
            $sql .= ' AND pid = :project ';
            $paramArr[':project'] = $data['project'];
        }

        if (!isset($data['count']))
        {
            $sql .= ' ORDER BY log_id DESC';
        }
        if (!isset($data['count']) && isset($limit['page']) && !empty($limit['limit']))
        {
            $start = ($limit['page'] - 1) * $limit['limit'];
            $sql .= " limit {$start},{$limit['limit']}";
        }
        if (isset($data['count']))
        {
            $logInfo = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $logInfo = $this->pdo->findAll($sql, $paramArr);
        }
        return $logInfo;
    }

    public function getMultiSmsLog($data = array(), $limit = array())
    {
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(msl.id) as total ';
        }
        $sql = "SELECT {$col} FROM `my_multi_sms_logs` AS msl , `my_multi_sms_logs_channel` AS mslc WHERE msl.id=mslc.`log_id`";
        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $sql .= ' AND msl.send_time >=:startTime AND msl.send_time <=:endTime ';
            $paramArr[':startTime'] = $data['startTime'] . ' 00:00:00 ';
            $paramArr[':endTime'] = $data['endTime'] . ' 23:59:59';
        }
        if (!empty($data['queryType']) && !empty($data['queryValue']))
        {
            if ($data['queryType'] == 1)
            {
                $sql .= ' AND msl.phone = :phone ';
                $paramArr[':phone'] = $data['queryValue'];
            }
            if ($data['queryType'] == 2)
            {
                $sql .= ' AND msl.passid = :passid ';
                $paramArr[':passid'] = $data['queryValue'];
            }
        }

        if (!empty($data['operatorId']))
        {
            $sql .= ' AND mslc.channel = :channel ';
            $paramArr[':channel'] = $data['operatorId'];
        }
        if (!empty($data['msgId']))
        {
            $sql .= ' AND mslc.msgId = :msgId ';
            $paramArr[':msgId'] = $data['msgId'];
        }
        if (!empty($data['plId']))
        {
            $sql .= ' AND msl.business_id = :plId ';
            $paramArr[':plId'] = $data['plId'];
        }

        //如果想买是114 以下的  那么做转换兼容历史数据
        if (!empty($data['projectId']) && $data['projectId'] <= 114)
        {
            $sql .= ' AND (msl.business_id = :business_id or msl.pid = :business_id)';
            $paramArr[':business_id'] = $data['projectId'];
        }
        elseif (!empty($data['projectId']) && $data['projectId'] == 115)
        {
            if (empty($data['plId']))
            {
                $sql .= ' AND msl.business_id in(1,2,3,4,5,6,7,8,20,21,22,23,24,25,26,27,28) ';
            }
        }
        else
        {
            if (!empty($data['projectId']))
            {
                $sql .= ' AND msl.pid = :pid ';
                $paramArr[':pid'] = $data['projectId'];
            }
        }
        if (!empty($data['projectsArr']))
        {
            $sql .= " AND msl.pid in({$data['projectsArr']}) ";
        }
        $sql .= ' ORDER BY msl.send_time DESC';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            $logInfo = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $logInfo = $this->pdo->findAll($sql, $paramArr);
        }
        return $logInfo;
    }

    public function getVoiceLog($data = array(), $limit = array())
    {

        $paramArr = array();
        $col = 'msl.id,msl.phone,msl.send_time,msl.text,msl.send_status,msl.send_response,msl.passid,msl.type,msl.business_id,msl.pid,mslc.channel,mslc.codeStatus,mslc.codeDesc';
        $where = '';
        if (!empty($data['count']))
        {
            $col = ' count(msl.id) as total ';
        }

        $sql = "SELECT {$col} FROM voice_logs_list AS vll INNER JOIN my_sms_logs AS msl ON
                msl.`id`=vll.log_id INNER JOIN my_sms_logs_channel AS mslc ON mslc.`log_id`=  vll.log_id";
        //  WHERE vll.add_time >= '2016-05-16' AND vll.add_time < '2016-05-17' AND msl.pid=125";

        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $where .= 'vll.add_time >=:startTime AND vll.add_time <=:endTime AND';
            $paramArr[':startTime'] = $data['startTime'] . ' 00:00:00 ';
            $paramArr[':endTime'] = $data['endTime'] . ' 23:59:59';
        }

        if (!empty($data['queryType']) && !empty($data['queryValue']))
        {
            if ($data['queryType'] == 1)
            {
                $sql .= ' AND msl.phone = :phone ';
                $paramArr[':phone'] = $data['queryValue'];
            }
            if ($data['queryType'] == 2)
            {
                $sql .= ' AND msl.passid = :passid ';
                $paramArr[':passid'] = $data['queryValue'];
            }
        }

        if (!empty($data['operatorId']))
        {
            $sql .= ' AND mslc.channel = :channel ';
            $paramArr[':channel'] = $data['operatorId'];
        }

        if (!empty($data['projectId']))
        {
            $where .= ' msl.pid=:projectId AND';
            $paramArr[':projectId'] = $data['projectId'];
        }

        if (!empty($data['plId']))
        {
            $sql .= ' AND msl.business_id = :plId ';
            $paramArr[':plId'] = $data['plId'];
        }

        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }
        $sql .= $where . ' ORDER BY vll.add_time DESC';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            $logInfo = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $logInfo = $this->pdo->findAll($sql, $paramArr);
        }
        return $logInfo;
    }

    /**
     * User: panj
     * 按logId单条查询
     * @param array $data data
     *
     * @return array|bool
     */
    public function getByLogId($data)
    {
        if (!isset($data['logId']) || $data['logId'] <= 0)
        {
            return [];
        }
        if (strtotime($data['dateRange'][0]) >= strtotime($data['dateRange'][1]))
        {
            return array();
        }

        if (date('Ym', strtotime($data['dateRange'][0])) !== date('Ym', strtotime($data['dateRange'][1])))
        {
            return array();
        }
        $table = self::getTableName(date('Ym', strtotime($data['dateRange'][0])));
        if (!$this->checkTableExist($table))
        {
            return array();
        }
        $col = ' log_id, phone';
        if (date('Ym', strtotime($data['dateRange'][0])) >= 201811)
        {
            $col .= ',encrypt_phone';
        }

        $sql = "SELECT {$col} FROM `{$table}` AS msl  where 1 AND log_id = :log_id limit 1";
        $paramArr[':log_id'] = $data['logId'];

        return $this->pdo->find($sql, $paramArr);
    }
}
