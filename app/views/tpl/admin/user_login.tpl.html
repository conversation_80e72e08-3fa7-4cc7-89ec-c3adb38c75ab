<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>用户登录查询</title>
    <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/v3/top.css">
    <script src="/js/jquery-1.8.3.min.js"></script>
</head>
<body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
<div class="mainWrap">
<div class="location">位置：<a href="#" target="_blank">帐号体系</a>  &gt; <span>用户登录查询</span></div>
<div class="TabMenu mt10">
    <a href="/houtai_gl/login_query/" >异常登录统计</a>
    <a href="/houtai_gl/login_query/unusual" class="currTabMenu">用户登录查询</a>            
</div>
<div class="yc_wrap">
    <!--div class="title-link clearfix mt30"><a href="/houtai_gl/login_query/">异常登录统计</a><a href="/houtai_gl/login_query/unusual" class="curr-title">用户登录查询</a></div-->
    
    <form action="/houtai_gl/login_query/unusual" id="searchform" method="get" >
    
        <div class="funList mt20 clearfix">
        	<div class="selectWrap left w170">
                <div class="selectInput"><input type="text" class="getListVal" autocomplete="off" value="用户名" readonly=""><i>下拉箭头</i></div>
                <dl style="display: none;" class="selectList">
                    <dd><a href="javascript:" value="username">用户名</a></dd>
                    <dd><a href="javascript:" value="passid">passid</a></dd>
                </dl>
                <input type="hidden" name="type" value="username"/>
            </div><!--selectWrap end-->
            <div class="left ml10 clearfix">
                <span class="input-search"><i>搜索Icon</i><input type="text" autocomplete="off" name="searchvalue" value="{{$pageArray.searchvalue}}"></span>
                <a class="styleBtnBlue left ml10" href="javascript:" id="searchbtn">查询</a>
            </div>
        </div>
    
    </form>
    <div class="contentWrap">
            <table cellspacing="0" cellpadding="0" border="0" class="tabMod forFixed mt10">
                <tr>
                    <th width="75">passid</th>
                    <th>用户名</th>
                    <th>邮箱地址</th>
                    <th width="100">绑定手机</th>
                    <th width="150">时间</th>
                    <th width="75">地点</th>
                    <th width="110">IP</th>
                    <th width="120">浏览器</th>
                    <th width="120">设备</th>
                </tr>
                {{foreach from = $pageArray.loginlog item=item}}
                <tr {{if $item.area == $pageArray.uncommonarea}}class="yc_color"{{/if}}>
                    <td>{{$pageArray.userinfo.id}}</td>
                    <td>{{$pageArray.userinfo.username}}</td>
                    <td>{{$pageArray.userinfo.email}}</td>
                    <td>{{$pageArray.userinfo.phone}}</td>
                    <td>{{$item.logintime}}</td>
                    <td>{{$item.area}}</td>
                    <td>{{$item.ip}}</td>
                    <td>{{$item.browser}}</td>
                    <td>{{$item.system}}</td>
                </tr>
                {{/foreach}}
            </table>
        </div>
  

</div><!--yc_wrap end-->
<script>
    $("#searchbtn").click(function (){
        $("#searchform").submit();
    });
    
    
    $('.selectList dd a').click(function (){
        $('input[name="type"]').val( $(this).attr('value') );
    });
    
    $('.selectList dd a').each (function (a,b){
        var selectArr = new Array;
        selectArr['username'] = '用户名';
        selectArr['passid'] = 'passid';
         if ( $(this).attr('value') == '{{$pageArray.type}}'   )
         {
            $(this).parents(".selectWrap").find(".getListVal").val( selectArr[$(this).attr('value')] );
            $('input[name="type"]').val( $(this).attr('value') );
         }
    });
    

$('.navList a').each(function (){
    if (  $(this).attr('href') == '/houtai_gl/login_query' )
    {
        $(this).addClass('select');
    }
});

</script>
 <script src="/js/global.js"></script>
</div>
</body>
</html>