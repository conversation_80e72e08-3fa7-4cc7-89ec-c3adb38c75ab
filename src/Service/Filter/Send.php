<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：Send.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：31/08/2018 09:59
 */

namespace Service\Filter;

use Service\Func\RequestFunc;
use Service\Func\SmsFunc;
use Service\Lib\Model\ConfigModel;
use Service\Lib\Provider;
use Service\Swoole\Client;
use Service\Swoole\Tool\Log;

class Send extends BaseSend
{
    protected $illegalPhoneList;
    protected $restrictPhoneList;
    protected $Provider;
    protected $sessionId;
    /**
     * User: panj
     * 前置必要检查
     *
     * @param array $data data
     *
     * @return mixed
     */
    public function preCheck($data)
    {
        // 手机号数量
        if (count($this->phoneList) > RequestFunc::PHONE_LIST_MAX)
        {
            $status = RequestFunc::MULTI_SEND_PHONE_NUMBER_NOT_ALLOWED;
            $msg = RequestFunc::$multiErrStr[RequestFunc::MULTI_SEND_PHONE_NUMBER_NOT_ALLOWED];

            return $this->setReturnInfo($status, $msg, []);
        }

        // 短信内容长度，字符1000以内
        if (mb_strlen($this->msg, "GBK") > RequestFunc::MSG_CONTENT_LENGTH)
        {
            $status = RequestFunc::SMS_CONTENT_LENGTH_TOO_LONG;
            $msg = RequestFunc::$errText[RequestFunc::SMS_CONTENT_LENGTH_TOO_LONG];

            return $this->setReturnInfo($status, $msg, []);
        }
        $requestFunc = new RequestFunc();
        //前置检测必要字段
        $isTrue = $requestFunc->checkRequireParams($data);

        if ($isTrue !== true)
        {
            list($status, $msg) = $isTrue;

            return $this->setReturnInfo($status, $msg, []);
        }
    }

    /**
     * User: panj
     * 生成Logid,存储原始请求数据
     *
     * @return mixed
     */
    public function initLogId()
    {
        $insertData = array();
        //sessionId = 定义一个唯一的sessionid； 用来表示该群发为同一个会话.
        $sessionId = uniqid();
        $column = array(
            "phone",
            "encrypt_phone",
            "text",
            "sessionid",
            "type",
            "send_time",
            "send_status",
            "send_response",
            "passid",
            "pid",
            'business_id',
            'client_ip',
            'server_ip',
        );

        foreach ($this->phoneList as $phone)
        {
            $insertData[$phone] = array(
                $phone,
                $phone,
                $this->msg,
                $sessionId,
                $this->smsType,
                date("Y-m-d H:i:s"),
                - 22,
                '等待发送',
                $this->passid,
                $this->pid,
                $this->positionId,
                $this->clientIp,
                get_client_ip(),
            );
        }
        //生成log_id
        $phoneLogIds = (new Provider())->setSmsLogs($insertData, $column);
        if ($phoneLogIds)
        {
            $this->phoneLogIds = $phoneLogIds;
            $this->sessionId = $sessionId;
        }
        else
        {
            $info = var_export($insertData, true) . PHP_EOL . ' setSmsLogs return :' . var_export($phoneLogIds, true);
            \WebLogger\Facade\LoggerFacade::info($info);
            $status = RequestFunc::RECEIVE_SMS_REQUEST_FAILED;
            $msg = RequestFunc::$errText[RequestFunc::RECEIVE_SMS_REQUEST_FAILED];

            return $this->setReturnInfo($status, $msg);
        }
    }

    /**
     * User: panj
     * 严格后置检查,黑白名单
     *
     * @return  mixed
     */
    public function strictCheck()
    {
        $configModel = new ConfigModel();

        // 为了兼容返回值... 手机号码格式不正确和黑名单
        if (!$this->isMulti)
        {
            //是否存在黑名单
            if ($configModel->isInBlackList($this->phoneList[0]))
            {
                $status = RequestFunc::PHONE_NUMBER_IN_BLACKLIST;
                $msg = RequestFunc::$errText[RequestFunc::PHONE_NUMBER_IN_BLACKLIST];

                return $this->setReturnInfo($status, $msg);
            }
            //检查是否是合法的手机号码
            if (!SmsFunc::checkPhone($this->phoneList[0]))
            {
                $status = RequestFunc::PHONE_NUMBER_NOT_CORRECT;
                $msg = RequestFunc::$errText[RequestFunc::PHONE_NUMBER_NOT_CORRECT];

                return $this->setReturnInfo($status, $msg);
            }
        }
        else
        {
            //是否存在黑名单和非法手机号
            list($legalPhoneList, $illegalPhoneList) = SmsFunc::checkMultiPhone($this->phoneList);
            if (empty($legalPhoneList))
            {
                // 全部不合法
                $status = RequestFunc::MULTI_SEND_LIMIT;
                $msg = RequestFunc::$multiErrStr[RequestFunc::MULTI_SEND_LIMIT];

                return $this->setReturnInfo($status, $msg);
            }
            // phone 赋值为合法手机号列表
            $this->phoneList = $legalPhoneList;
            $this->illegalPhoneList = $illegalPhoneList;
        }

        //短信内容检查  类型为2时(语音)可以不传msg
        if (!SmsFunc::checkContent($this->msg) && $this->appType !== RequestFunc::SMS_APP_TYPE_VOICE)
        {
            $status = RequestFunc::SMS_CONTENT_TOO_SHORT;
            $msg = RequestFunc::$errText[RequestFunc::SMS_CONTENT_TOO_SHORT];

            return $this->setReturnInfo($status, $msg);
        }

        //查看传来的sms短信类型 是否在该项目短信类型之中
        $smsTypes = $configModel->getSmsConfigProjectTypeList($this->pid);
        if (!is_array($smsTypes) || !in_array($this->smsType, $smsTypes))
        {
            $status = RequestFunc::INVALID_SMS_TYPE;
            $msg = RequestFunc::$errText[RequestFunc::INVALID_SMS_TYPE];

            return $this->setReturnInfo($status, $msg);
        }
        //ip白名单 检测
        if (!ConfigModel::accessCheck())
        {
            $status = RequestFunc::REMOTE_IP_CHECK_FAILED;
            $msg = RequestFunc::$errText[RequestFunc::REMOTE_IP_CHECK_FAILED];

            return $this->setReturnInfo($status, $msg);
        }
        //获取类型配置
        $smsConfigs = $configModel->getSmsTypeConfig($this->smsType);
        //对于配置了多个运营商的类型，随机分配运营商
        $smsConfig = SmsFunc::randomTypeConfig($smsConfigs);
        $restrictPhoneList = array();
        foreach ($this->phoneList as $key => $phone)
        {
            $getPhoneWhite = $configModel->isPhoneWhiteList($phone);
            //看该type是否是 验证码类型
            if (empty($getPhoneWhite) && $smsConfig['is_restrict'] == 1)
            {
                $smsLimit = $configModel->getSmsNumByPhoneType(
                    $phone,
                    $this->smsType,
                    date("Y-m-d")
                );
                $limitNums = !empty($smsConfig['restrict_nums']) ? $smsConfig['restrict_nums'] : RequestFunc::CHECK_CODE_LIMIT;
                if ($smsLimit >= $limitNums)
                {
                    if ($this->isMulti)
                    {
                        unset($this->phoneList[$key]);
                        $restrictPhoneList[] = $phone;
                    }
                    else
                    {
                        $status = RequestFunc::DAILY_SEND_LIMIT;
                        $msg = RequestFunc::$errText[RequestFunc::DAILY_SEND_LIMIT];

                        return $this->setReturnInfo($status, $msg);
                    }
                }
            }
        }
        $this->restrictPhoneList = $restrictPhoneList;

        // 不合规号码 只记录
        if (!empty($illegalPhoneList))
        {
            $logIds = array();
            foreach ($illegalPhoneList as $phone)
            {
                $logIds[] = $this->phoneLogIds[$phone];
            }
            $updateData = array(
                "logIds"        => $logIds,
                "send_status"   => RequestFunc::MULTI_SEND_LIMIT,
                "send_response" => RequestFunc::$multiErrStr[RequestFunc::MULTI_SEND_LIMIT],
            );
            $isTrue = $this->Provider->upSmsLogs($updateData);
        }

        // 超限制 只记录
        if (!empty($restrictPhoneList))
        {
            $logIds = array();
            foreach ($restrictPhoneList as $phone)
            {
                $logIds[] = $this->phoneLogIds[$phone];
            }
            $updateData = array(
                "logIds"        => $logIds,
                "send_status"   => RequestFunc::MULTI_SEND_LIMIT,
                "send_response" => RequestFunc::$multiErrStr[RequestFunc::MULTI_SEND_LIMIT],
            );
            $isTrue = $this->Provider->upSmsLogs($updateData);
        }

        if (empty($this->phoneList))
        {
            // 已经没有有效的手机号了, 返回错误
            $status = RequestFunc::MULTI_SEND_LIMIT;
            $msg = RequestFunc::$multiErrStr[RequestFunc::MULTI_SEND_LIMIT];

            return $this->setReturnInfo($status, $msg);
        }
        $this->phoneList = array_merge($this->phoneList);



        // 通道关闭检测
        $check = SmsFunc::checkChannelStatus($smsConfig);
        if ($check[0] < 0)
        {
            list($status, $msg) = $check;

            return $this->setReturnInfo($status, $msg);
        }

        return true;
    }

    /**
     * User: panj
     * 封装数据，提交调取服务商接口
     *
     * @param array $data data
     *
     * @return mixed
     */
    public function request($data)
    {
        $configModel = new ConfigModel();
        $configModel->incrMonitor(count($this->phoneList), 'send', $this->smsType, $this->pid);

        $data['phoneList'] = $this->phoneList;
        $data['phoneLogIds'] = $this->phoneLogIds;
        $data['illegalPhoneList'] = $this->illegalPhoneList;
        $data['restrictPhoneList'] = $this->restrictPhoneList;
        $data['sessionId'] = $this->sessionId;

        $requestSwooleData = array_merge(
            $data,
            [
                'ReceiveHandle' => 'ProcessHandle',
                'Handle'        => 'SendHandle',
                'sync'          => false,
                'StorageType' => Provider::getStorageMold()
            ]
        );
        $res = Client::sendTo($requestSwooleData);

        return $this->setReturnInfo($res['status'], $res['msg'], $res['data']);
    }

    /**
     * User: panj
     * 初始化数据
     *
     * @param array $data data
     * @return null
     */
    public function initData($data)
    {
        parent::initData($data);
    }

    /**
     * User: panj
     * 程序入口
     *
     * @param array $data data
     *
     * @return array
     */
    public function run($data)
    {
        $this->Provider = new Provider();
        $this->initData($data);  //初始化数据结构
        if ($this->breakOff)
        {
            return $this->getReturnInfo();
        }

        //生成LogId的前置检查
        $this->preCheck($data);
        if ($this->breakOff)
        {
            return $this->getReturnInfo();
        }

        //生成LogId,存原始请求数据
        $this->initLogId();
        if ($this->breakOff)
        {
            return $this->getReturnInfo();
        }

        //提交服务商之前的黑白名单等严格检查
        $this->strictCheck();
        if ($this->breakOff)
        {
            return $this->getReturnInfo();
        }

        $this->request($data);

        return $this->getReturnInfo();
    }
}
