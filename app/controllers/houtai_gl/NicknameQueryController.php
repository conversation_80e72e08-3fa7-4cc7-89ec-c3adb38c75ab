<?php

use Octopus\PdoEx;

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:NicknameQueryController.php
 * 摘    要:
 * 作    者:<EMAIL>
 * 修改日期: 2015/11/25
 */
includeBase('AdminController');

class NicknameQueryController extends AdminController
{

    protected $authName = '昵称查询与编辑';

    /**
     * 用户昵称查询
     * <AUTHOR>
     */
    public function actionIndex()
    {
        foreach ($_GET as $key => $value) {
            $_GET[$key] = \Common\Utils\Url::getStringParam($key);
        }
        $limit = 20;
        $page = \Common\Utils\Url::getPage();
        $startRecord = ($page - 1) * $limit;
        $rows = array();
        if (isset($_GET['type']))
        {
            $type = empty($_GET['type']) ? 'nickname' : $_GET['type'];
            $search['type'] = $type;
            $value = $_GET['value'];
            $search['value'] = $value;
            if (!empty($_GET['start']))
            {
                $start = $_GET['start'] . " 00:00:00";
                $search['start'] = $_GET['start'];
            }
            if (!empty($_GET['end']))
            {
                $end = $_GET['end'] . " 23:59:59";
                $search['end'] = $_GET['end'];
            }
            if (!empty($_GET['ip']))
            {
                $editip = $_GET['ip'];
                $search['ip'] = $_GET['ip'];
            }
            $dbConfig = Config::get("database");
            $pdo = PdoEx::getInstance(DB_PASSPORT_USER, $dbConfig[DB_PASSPORT_USER]);
            $sql = "SELECT count(*) AS cnt
                FROM members_nickname";
            $where = " where 1";
            $params = array();
            if (!empty($start))
            {
                $where .= " and  addtime >= :addtime_start ";
                $params[':addtime_start'] = $start;
            }
            if (!empty($end))
            {
                $where .= " and addtime <= :addtime_end ";
                $params[':addtime_end'] = $end;
            }
            if (!empty($editip))
            {
                $where .= " and ip = :ip";
                $params[':ip'] = $editip;
            }
            if ($value != '')
            {
                if ($type == 'nickname')
                {
                    $where .= " and nickname = :nickname ";
                    $params[':nickname'] = $value;
                }
                else
                {
                    if ($type == 'passid')
                    {
                        $where .= " and passid = :passid ";
                        $params[':passid'] = $value;
                    }
                }
            }
            $arr = [
                'start' => \Common\Utils\Url::getStringParam('start'),
                'end' => \Common\Utils\Url::getStringParam('end'),
                'regip' => \Common\Utils\Url::getStringParam('regip'),
                'type' => \Common\Utils\Url::getStringParam('type'),
                'value' => \Common\Utils\Url::getStringParam('value'),
            ];
            $sql .= $where;
            $row = $pdo->find($sql, $params);
            $totalRecord = $row['cnt'];
            $PageAction = loadAction('Page');
            $arr['listPage'] = $PageAction->showPage($totalRecord, '/houtai_gl/nickname_query');
            $pageList =  $PageAction->returnPageConfig(20);
            $sql = "SELECT passid, nickname, addtime, ip FROM members_nickname";
            $sql .= $where;
            $sql .= " order by addtime desc ";
            $sql .= " limit {$pageList['page']}, {$pageList['limit']}";
            $rows = $pdo->findAll($sql, $params);
        }
        $arr['user'] = $rows;
        loadView('admin/nickname_query.tpl.html', $arr);
    }

}