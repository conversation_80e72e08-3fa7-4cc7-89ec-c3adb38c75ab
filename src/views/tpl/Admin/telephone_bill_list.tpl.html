<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>语音通道-话单列表</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
  <script src="/style/js/global.js"></script>
  <script src="/style/js/tableFixed.js"></script>
  <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">语音通道</a>  >  <span>话单列表</span></div>
    <!--面包屑导航 结束-->
    
    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="javascript:">话单列表</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    
     <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="" method="post" id="queryFromId">
            <div class="dataWrap clearfix">
        	<em>日期：</em>
            <div class="dataList w130">
            	<div class="getTime"><input type="text" value="{{$pageArray.startTime}}" autocomplete="off" name="startTime" onclick="WdatePicker()"/><i>日历icon</i></div>
            </div>
            </div><!--dataWrap end-->
            <span class="dataTxt">至</span>
            <div class="dataWrap clearfix">
                <div class="dataList w130">
                	<div class="getTime"><input type="text" value="{{$pageArray.endTime}}" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->
        	
            <div class="dataWrap ml10 clearfix">
            	<em>请求来源：</em>
                <select class="selectMod w90 selectProject" name="from">
                    <option value="0">全部</option>
                    {{foreach from=$pageArray.projectList item=projectInfo}}
                        <option {{if $pageArray.from eq $projectInfo.channel}} selected="selected"{{/if}} value="{{$projectInfo.channel}}">{{$projectInfo.desc}}</option>
                    {{/foreach}}
                </select>
            </div>
            
            <div class="dataWrap ml10 clearfix">
            	<em>响铃：</em>
                <select class="selectMod w90 selectProject" name="isRang">
                    <option value="-1">全部</option>
                    <option value="1" {{if $pageArray.isRang eq '1'}}selected{{/if}} >是</option>
                    <option value="0" {{if $pageArray.isRang eq '0'}}selected{{/if}} >否</option>
                </select>
            </div>
            
            <div class="dataWrap ml10 clearfix">
            	<em>接听：</em>
                <select class="selectMod w90 selectProject" name="isAnswed">
                    <option value="-1">全部</option>
                    <option value="1" {{if $pageArray.isAnswed eq '1'}}selected{{/if}}>是</option>
                    <option value="0" {{if $pageArray.isAnswed eq '0'}}selected{{/if}}>否</option>
                </select>
            </div>
            
            <div class="dataWrap ml10 clearfix">
            	<em>发送类型：</em>
                <select class="selectMod w90 selectProject" name="isSms">
                    <option value="-1">全部</option>
                    <option value="1" {{if $pageArray.isSms eq '1'}}selected{{/if}}>短信</option>
                    <option value="0" {{if $pageArray.isSms eq '0'}}selected{{/if}}>语音</option>
                </select>
            </div>
            
            <div class="dataWrap ml10 clearfix" style="margin-top:10px">
            	<em>发送短信状态：</em>
                <select class="selectMod w90 selectProject" name="statusSms">
                    <option value="-1">全部</option>
                    <option value="1" {{if $pageArray.statusSms eq '1'}}selected{{/if}}>成功</option>
                    <option value="0" {{if $pageArray.statusSms eq '0'}}selected{{/if}}>失败</option>
                </select>
            </div>
            
            <div class="dataWrap ml10 clearfix" style="margin-top:10px">
            	<em>手机号码：</em>
                <input value="{{$pageArray.mobile}}" type="text" name="mobile"/>
            </div>
            
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 queryFromSubmit" style="margin-top:10px">查询</a>
        
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    <!--主体内容 开始-->
    <div class="contentWrap">        
        
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th width="76">日期</th>
                        <th width="88">手机号</th>
                        <th width="60">验证码</th>
                        <th width="80">使用状态</th>
                        <th width="80">请求来源</th>
                        <th width="100">语音请求状态</th>
                        <th width="150">请求时间</th>
                        
                        <th width="50">方式</th>
                        <th width="111">短信请求状态</th>
                        <th width="50">响铃</th>
                        <th>接听</th>
                        <th>挂机原因</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th width="76">日期</th>
                <th width="88">手机号</th>
                <th width="60">验证码</th>
                <th width="80">使用状态</th>
                <th width="80">请求来源</th>
                <th width="100">语音请求状态</th>
                <th width="150">请求时间</th>
                
                <th width="50">方式</th>
                <th width="111">短信请求状态</th>
                <th width="50">响铃</th>
                <th>接听</th>
                <th>挂机原因</th>
            </tr>
            {{foreach from=$pageArray.telList item=telInfo}}
            <tr>
                <td>{{$telInfo.date}}</td>
                <td>{{$telInfo.mobile}}</td>
                <td>{{$telInfo.code}}</td>
                <td>{{$telInfo.status}}</td>
                <td>{{$telInfo.from}}</td>
                <td>
                	<span class="{{if $telInfo.request_status eq 1}}greenBg{{else}}yellowBg{{/if}} showIP">{{$telInfo.statusDesc}}
                		<div class="ipWrap"><i></i><em>IP:{{$telInfo.server_ip}}</em></div>
                    </span>
                </td>
                <td>{{$telInfo.add_time}}</td>
                
                <td><i class="icon-i {{if $telInfo.is_sms eq 1}}messIcon{{else}}phoneIcon{{/if}}"></i></td>
                <td>{{$telInfo.statusSmsDesc}}</td>
                <td><i class="icon-i {{if $telInfo.is_rang eq 1}} yesIcon{{else}} noIcon{{/if}} "></i></td>
                <td><span class="{{if $telInfo.is_answed eq 1}}greenBg{{else}}redBg{{/if}} ">{{$telInfo.isAnswedDesc}}</span></td>
                <td>{{$telInfo.hangup_cause}}</td>
                
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
        
        <!--翻页 开始-->
        {{$pageArray.showPage}}
        <!--翻页 结束-->
        
    </div><!--contentWrap end-->
    <!--主体内容 结束-->
</div><!--mainWrap end-->
</body>
</html>

<script>
$(function()
{
	$(".showIP").hover(function()
	{
		$(this).find(".ipWrap").show();	
	},function()
	{
		$(this).find(".ipWrap").hide();
	})
});//JQ
$('.queryFromSubmit').click(function (){
    $('#queryFromId').submit();
});
</script>