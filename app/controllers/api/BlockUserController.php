<?php

/**
 * Copyright (c)  上海二三四五网络科技有限公司
 * 文件名称：BlockUserController.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：04-01, 2017
 */
class BlockUserController extends Controller
{

    /**
     * BlockUserController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            $this->codeResponse(- 401, "", "限制IP，无访问权限！");
        }
    }

    /**
     * 批量冻结
     * @return array
     */
    public function actionBatch()
    {
        $type = \Common\Utils\Url::getStringParam("accountType");
        $accounts = \Common\Utils\Url::getStringParam("accounts");
        $domain = \Common\Utils\Url::getStringParam("domain");

        if (!$domain)
        {
            $this->codeResponse(- 1, "", "缺少参数domain!");
            exit;
        }
        // todo 这么处理有问题！！
        if ($type == "passid")
        {
            $accounts = preg_replace("/[^0-9]/", ",", $accounts);
        }
        else
        {
            $type = "username";
            $accounts = preg_replace("/[^0-9a-zA-Z_]/", ",", $accounts);
        }

        $accountList = explode(",", $accounts);
        $accountList = array_filter($accountList);
        $failAccounts = array();
        if (!$accountList)
        {
            $this->codeResponse(- 2, "", "请检查参数列表");
            exit;
        }
        if (count($accountList) > 500)
        {
            $this->codeResponse(- 3, "", "限制单次冻结个数最多为500");
            exit;
        }

        $userAction = loadAction("User");
        $memberModel = loadModel('member');
        foreach ($accountList as $account)
        {
            if ($type == "passid")
            {
                /** @see UserAction::blockUserByPassid() */
                $res = $userAction->blockUserByPassid($account);
            }
            else
            {
                /** @see MemberModel::getPassidByUsername() */
                $res = $memberModel->getPassidByUsername($account);
                if ($res)
                {
                    /** @see UserAction::blockUserByPassid() */
                    $res = $userAction->blockUserByPassid($res);
                }
            }
            if (!$res)
            {
                $failAccounts[] = $account;
            }
        }

        $this->codeResponse(1, array("failAccounts" => $failAccounts));
        exit;
    }

}
