
<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务平台-位置标记统计</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
  <script src="/style/js/global.js"></script>
  <script src="/style/js/tableFixed.js"></script>
  <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a>  >  <span>位置标记统计</span></div>
    <!--面包屑导航 结束-->
    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a href="/Admin/SmsTotal/Index">按日短信统计</a>
        <a href="/Admin/TagTotal/Index">用户中心发送量</a>
        <a class="currTabMenu" href="/Admin/TagTotal/TagPostionTotal">用户中心发送细分</a>
        <a href="/Admin/SmsTotal/stat">按月短信统计</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="/Admin/TagTotal/TagPostionTotal" method="post" id="queryFromId">
            <div class="dataWrap clearfix">
        	<em>日期：</em>
            <div class="dataList w130">
            	<div class="getTime"><input type="text" value="{{$pageArray.startTime}}" autocomplete="off" name="startTime" onclick="WdatePicker()"/><i>日历icon</i></div>
            </div>
            </div><!--dataWrap end-->
            <span class="dataTxt">至</span>
            <div class="dataWrap clearfix">
                <div class="dataList w130">
                	<div class="getTime"><input type="text" value="{{$pageArray.endTime}}" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->
        	<a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 queryFromSubmit" >查询</a>
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 exportChannelData">导出</a>
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    <!--主体内容 开始-->
    <div class="contentWrap" style="overflow-x: scroll;">
        
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
                    <tr>
                        <th style="width: 100px">日期</th>
                        <th style="width: 100px">位置</th>
                        {{foreach from=$pageArray.tagList item=tagListInfo}}
                            <th style="width: 60px">{{$tagListInfo}}</th>
                        {{/foreach}}
                        <th style="width: 60px">总计</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
            <tr>
                <th style="width: 100px">日期</th>
                <th style="width: 100px">位置</th>
                {{foreach from=$pageArray.tagList item=tagListInfo}}
                    <th style="width: 60px">{{$tagListInfo}}</th>
                {{/foreach}}
                <th style="width: 60px">总计</th>
            </tr>
            {{foreach from=$pageArray.tagTotalList key=tagTotalKey item=tagTotalInfo}}
                <tr>
                    <td rowspan="{{count($tagTotalInfo) + 1}}">{{$tagTotalKey}}</td>
                </tr>

                {{foreach from=$tagTotalInfo key=tagKey item=tagInfo}}
                    <tr>
                        <td>{{$tagKey}}</td>

                        {{foreach from=$pageArray.tagList key=tagListKeys item=tagListInfo}}
                            <td>{{$tagInfo[$tagListKeys].total }}</td>
                        {{/foreach}}

                        <td>{{$pageArray.total[$tagTotalKey][$tagKey]}}</td>
                    </tr>
                {{/foreach}}
                <tr>
                    <td colspan="2">合计</td>

                    {{foreach from=$pageArray.tagList key=tagListKeys item=tagListInfo}}
                        <td>{{$pageArray.totalCount[$tagTotalKey][$tagListKeys]}}</td>
                    {{/foreach}}

                    <td>{{$pageArray.totalCount[$tagTotalKey].total}}</td>
                </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
        
    </div><!--contentWrap end-->
    <!--主体内容 结束-->
</div><!--mainWrap end-->
<script>
$('.queryFromSubmit').click(function (){
    $('#queryFromId').submit();
});
$('.exportChannelData').click(function (){
    var link = '/Admin/TagTotal/TagPostionTotal?act=export&' + $('#queryFromId').serialize();
    window.open(link);
});
</script>
</body>
</html>