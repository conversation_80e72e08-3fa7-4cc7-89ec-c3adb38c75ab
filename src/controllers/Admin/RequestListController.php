<?php

namespace Admin;
use Controller;
class RequestListController extends Controller
{
    public function actionIndex()
    {
        $pageArray = array();

        $pageArray['mobile'] = $this->Filter->getPost('mobile');
        $pageArray['from'] = $this->Filter->getPost('from');
        
        
        $PageAction = \PageAction::getInstance();
        $TelLogModel = TelLogModel::getInstance();
        $TelAction = TelAction::getInstance();
        $pageArray['projectList'] = $TelAction->getProjectList();
        $projectArr = array();
        foreach ($pageArray['projectList'] as $projectInfos )
        {
            $projectArr[$projectInfos['channel']] = $projectInfos['desc'];
        }
        $queryData = array('mobile' => $pageArray['mobile'], 'from' => $pageArray['from']);
        $telLog = $TelLogModel->getVoiceCodeLog($queryData,$PageAction->returnPageConfig(30));
        foreach ($telLog as $telLogKey=>$telLogInfo)
        {
            $telLog[$telLogKey]['date'] = date('Y-m-d', $telLogInfo['add_time']);
            $telLog[$telLogKey]['add_date'] = date('Y-m-d H:i:s', $telLogInfo['add_time']);
            $telLog[$telLogKey]['use_date'] = $telLogInfo['use_time'] > 0 ? date('Y-m-d H:i:s', $telLogInfo['add_time']) : '';
            $telLog[$telLogKey]['requestStatusDesc'] =  TelAction::getStatusDesc($telLogInfo['request_status']);
            $telLog[$telLogKey]['from'] = !empty($projectArr[$telLogInfo['from']]) ?$projectArr[$telLogInfo['from']] :$telLogInfo['from'];
            
            
            if ($telLogInfo['status'] == 0)
            {
                if (($telLogInfo['add_time'] + 1800) < time())
                {
                    $telLog[$telLogKey]['status'] = '已过期';
                }
                else
                {
                    $telLog[$telLogKey]['status'] = '未使用';
                }
            }
            elseif ($telLogInfo['status'] == 1)
            {
                $telLog[$telLogKey]['status'] = "已使用";
            }
        }
        $queryData['count'] = true;
        $queryData['row'] = true;
        $telLogCount = $TelLogModel->getVoiceCodeLog($queryData);
        $pageArray['showPage'] = $PageAction->showPage($telLogCount['total'], '/Admin/RequestList/Index');
        $pageArray['telLog'] = $telLog;
        loadView('Admin/request_list_index.tpl.html', $pageArray);
    }
}

?>