{{include file="header.tpl.html"}}
<script>
	function show()
	{
		var ua = navigator.userAgent;
		if(ua.indexOf("Chrom") > -1)
		{
			document.write("选择文件");
		}else
		{
			document.write("浏览");
		}
	}
</script>
<div class="wrap">
	<div class="main wrap_op">
		<h2>导入和导出</h2>
		<div class="box">
			<div class="op"><a class="back" href="index.php">返回收藏夹</a></div>
			<div class="sbox">
				<div class="sbox_inner">
					<form enctype="multipart/form-data" method="post" action="?action=input">
						<p>导入收藏夹步骤：</p>
						<p>1、先将浏览器的收藏夹导出为html文件（支持IE、遨游、火狐等）。<a target="_blank" href="/fav/export.htm" class="underline">如何从浏览器导出？</a></p>
						<p>2、单击下面的"<script>show();</script>"按钮，选择导出的html文件，单击"上传"即可将网址导入。</p>
						<p><input type="file" style="width:200px" name="file_fav" class="text02"><input type="submit" value="上传" class="btn09"></p>
                    </form> 
				</div>
			</div>
			<div class="sbox">
				<div class="sbox_inner">
					<p>导出收藏夹数据</p>
					<p>1.您可以将在线收藏夹的数据导出成html文件，通过html文件可以方便的将数据导入到其他的收藏夹中。</p>
					<p align="center"><a href="export_or_input.php?action=export" class="a_btn">导出数据</a></p>
				</div>
			</div>
		</div>
	</div>
</div>
{{include file="footer.tpl.html"}}
<script src="//web.50bangzh.com/js/bookmark"></script>