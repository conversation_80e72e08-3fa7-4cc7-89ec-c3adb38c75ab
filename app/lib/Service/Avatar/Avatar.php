<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：Avatar.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：10-08, 2016
 */

namespace Service\Avatar;

use Service\UserBase\Rules;

class Avatar
{
    /**
     * Avatar constructor.
     */
    public function __construct()
    {
    }

    /**
     *
     * 更新修改头像时间
     *
     * @param int $passid $passid
     * @return bool|mixed
     */
    public static function storeUpdateTime($passid)
    {
        $res = false;
        $isUseRedis = \Config::get("isUseRedis");
        if ($isUseRedis) {
            $avatarCache = new namespace\Models\AvatarCache();
            $res = $avatarCache->update($passid);
        }
        return $res;
    }

    /**
     * 获取头像,大图
     *
     * @param int $passid $passid
     * @return string
     */
    public static function getAvatarBigUrl($passid)
    {
        return self::getAvatarUrl($passid, "big");
    }

    /**
     * 获取头像,中图
     *
     * @param int $passid $passid
     * @return string
     */
    public static function getAvatarMiddleUrl($passid)
    {
        return self::getAvatarUrl($passid, "middle");
    }

    /**
     * 获取头像, 小图
     *
     * @param int $passid $passid
     * @return string
     */
    public static function getAvatarSmallUrl($passid)
    {
        return self::getAvatarUrl($passid, "small");
    }

    /**
     * 获取头像
     *
     * @param int $passId $passId
     * @param string $size $size
     * @return string
     */
    public static function getAvatarUrl($passId, $size = "middle")
    {
        if ($avatarUrl = self::getHeader($passId, $size)) {
            $prefix = "http://";
            isHttps() && $prefix = "https://";
            $updateTime = "";
            $isUseRedis = \Config::get("isUseRedis");
            if ($isUseRedis) {
                $avatarCache = new namespace\Models\AvatarCache();
                $updateTime = $avatarCache->getUpdateTime($passId);
            }
            if (!$updateTime) {
                // 没有修改时间戳,使用当前月1号的时间戳.
                $updateTime = strtotime(date("Y-m-01 23:45:01"));
            }
            return $prefix . $_SERVER['HTTP_HOST'] . $avatarUrl . "?v=" . $updateTime;
        }
        return '';
    }

    /**
     * 获取用户头像
     * -
     * @param array $avatarInfo 头像审核状态数组
     * @param int $passid 用户ID
     * @param string $size 获取头像大小
     * @return string
     */
    public static function getUserAvatarUrl($avatarInfo, $passid, $size = 'middle')
    {
        //2017-07-01之前正常显示  之后未审核显示未审核图片
        if (strtotime($avatarInfo['uptime']) < strtotime('2018-07-01')) {
            $avatarPath = self::getAvatarUrl($passid, $size);
        } else {
            $prefix = "http://";
            if (isHttps()) {
                $prefix = "https://";
            }
            $avatarHost = $prefix . $_SERVER['HTTP_HOST'];
            if ($avatarInfo['status'] == 1) {
                //已审核通过
                $avatarPath = self::getAvatarUrl($passid, $size);
            } elseif ($avatarInfo['status'] == 2) {
                //审核失败  显示默认图片
                $avatarPath = $avatarHost . '/pic/avatar/default_v2.jpg';
            } else {
                //审核中
                $avatarPath = $avatarHost . '/pic/avatar/auditing_' . $size . '.jpg';
            }
        }
        return $avatarPath;
    }



    /**
     * 功  能：检测头像是否存在
     *
     * @param int $passId $passId
     * @param string $tag $tag
     * @return bool
     */
    public static function getHeader($passId, $tag = 'middle')
    {
        $config = \Config::get('msv');
        $url = $config['header'] . "/header/get/{$passId}?tag={$tag}";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 2);
        $output = curl_exec($ch);
        curl_close($ch);
        $output && $output = json_decode($output, true);
        if (isset($output['data']['result']) && $output['data']['result']) {
            $avatar = $output['data']['result'];
        } else {
            $avatar = '';
        }
        return $avatar;
    }

    /**
     * 功  能：上传单个文件
     *
     * @param string $file $file
     * @param string $urlPath $urlPath
     * @return bool
     */
    public static function uploadSingle($file, $urlPath = '')
    {
        $passId = $tag = '';
        if (empty($urlPath) && preg_match("/\/(\d+)_(.+)\.jpg/i", $file, $matches)) {
            $passId = $matches[1];
            $tag = $matches[2];
        }
        $data = [
            'file' => new \CURLFile($file),
            'pass_id' => $passId,
            'tag' => $tag,
            'url_path' => $urlPath,
        ];
        $config = \Config::get('msv');
        $url = $config['header'] . "/header/upload";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 3);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($ch);
        curl_close($ch);
        @unlink($file);
        $output && $output = json_decode($output, true);
        return $output['data']['result'] ?? false;
    }

    /**
     * 功  能：删除
     *
     * @param int $passId $passId
     * @return array
     */
    public static function deleteHeader($passId)
    {
        $config = \Config::get('msv');
        $url = $config['header'] . "/header/delete/" . $passId;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 2);
        $output = curl_exec($ch);
        curl_close($ch);
        return $output;
    }

    /**
     * 功 能：上传一张图片，根据可用户端提供裁剪成大中小三张并保存
     * 修改日期: 2020/3/11
     *
     * @param string $passId 用户passId
     * @param string $srcfile 源文件
     * @param int $x x坐标
     * @param int $y y坐标
     * @param int $w 宽
     * @param int $h 长
     * @return array
     */
    public static function cropSave($passId, $srcfile, $x, $y, $w, $h)
    {
        $outData = [
            'status' => - 1,
            'msg'    => '保存失败，请稍后再试',
        ];
        //裁剪并按新的尺寸保存
        $result = self::crop($srcfile, $srcfile, $x, $y, $w, $h, 200, 200);
        if ($result['suc'] == 0) {
            $outData['msg'] = $result['msg'];

            return $outData;
        }
        $userBaseAction = loadAction('UserBase');
        $mid = 'login';
        $errorMsg = [];
        $isUpdate = $userBaseAction->customUploadAvatar($mid, $passId, $srcfile, $errorMsg);
        if ($isUpdate) {
            $outData['status'] = 1;
            $outData['msg'] = '上传成功，审核通过后展示！';

            return $outData;
        } else {

            return $outData;
        }
    }

    /**
     * 功    能： 将一张图片根据坐标裁剪成一张新图
     * 修改日期：2020/3/9
     *
     * @param string $fileSrc 待裁剪图片
     * @param string $fileSrc 裁剪后图片
     * @param string $x 客户端选择区域左上角x轴坐标
     * @param int $y 客户端选择区域左上角y轴坐标
     * @param int $w 客户端选择区的宽
     * @param int $h 客户端选择区的高
     * @param int $newW 裁剪后图片新的宽
     * @param int $newH 裁剪后图片新的高
     * @return array
     */
    public static function crop($fileSrc, $fileDes, $x, $y, $w, $h, $newW, $newH)
    {
        $info = "";
        $data = getimagesize($fileSrc, $info);
        $type = $data[2];
        //只允许jpg和png两种格式的图片
        if ($type != 2 && $type != 3) {
            return [
                'suc' => 0,
                'msg' => '图片格式不允许，请使用.jpg或.png格式',
            ];
        }
        //处理JPG格式
        if ($type == 2) {
            if (!function_exists("imagecreatefromjpeg")) {
                return [
                    'suc' => 0,
                    'msg' => 'GD图像库不支持jpeg',
                ];
            } else {
                $im = imagecreatefromjpeg($fileSrc);
            }
        } elseif ($type == 3) {
            //处理PNG格式
            if (!function_exists("imagecreatefrompng")) {
                return [
                    'suc' => 0,
                    'msg' => 'GD图像库不支持png',
                ];
            } else {
                $im = imagecreatefrompng($fileSrc);
            }
        }
        $newim = imagecreatetruecolor($newW, $newH);
        imagecopyresampled($newim, $im, 0, 0, $x, $y, $newW, $newH, $w, $h);
        $return = $type == 2 ? imagejpeg($newim, $fileDes) : imagepng($newim, $fileDes);
        imagedestroy($im);
        imagedestroy($newim);
        if ($return) {
            return [
                'suc' => 1,
                'msg' => '保存成功',
            ];
        } else {
            return [
                'suc' => 0,
                'msg' => '保存失败',
            ];
        }
    }

    /**
     * 获取转移到OSS的头像地址
     * @param string $mid      项目标识
     * @param int    $passid   passid
     * @param int    $showType 1、对自己 2、对外展示
     * @return string
     * @throws
     */
    public static function getTransferAvatar($mid, $passid, $size, $showType = 1)
    {
        $warningAction = loadAction('Warning');
        $prefix = "http://";
        if (isHttps()) {
            $prefix = "https://";
        }
        $avatarHost = $prefix . $_SERVER['HTTP_HOST'];
        $userBaseModel = loadModel('UserBase');
        $userAvatarInfo = $userBaseModel->getAvatarNicknameInfoByPassId($mid, $passid, '', true);
        list($avatarUrl, $isDefaultAvatar) = Rules::getAvatarNicknameByRule($mid, $passid, $showType, $userAvatarInfo, 'avatar', $size);
        $warningAction->setRequestInfo(['externalShow' => $userAvatarInfo['in_approve_avatar_source'], 'selfShow' => $userAvatarInfo['valid_avatar_source']]);
        if ($isDefaultAvatar){
            $avatarUrl = $avatarHost . '/pic/avatar/default_v2.jpg';// 给一个默认地址
        }
        \Octopus\PdoEx::delInstance(DB_PASSPORT_USER);
        \Octopus\PdoEx::delInstance(DB_PASSPORT_AVATAR);
        \RedisEx::delInstance();

        return [$avatarUrl, $isDefaultAvatar];
    }
}
