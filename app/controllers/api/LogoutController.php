<?php
/**
 * Class LogoutController
 */
class LogoutController extends Controller
{
    private $logoutType = 'avatar';


    /**
     * 默认构造函数
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight()) {
            die('401');
        }
    }

    /**
     * 生态用户注销入口函数
     */
    public function actionIndex()
    {
        $mid = trim(\Common\Utils\Url::getStringParam("mid"));
        $passId = trim(\Common\Utils\Url::getStringParam("passId"));
        $type = \Common\Utils\Url::getStringParam("type") ?: $this->logoutType;

        $errorInfo = $this->checkParams($mid, $passId);
        $errorInfo && $this->codeResponse(500, '', $errorInfo);

        $type = explode(',', $type);

        foreach ($type as $item) {
            $method = $item . 'InfoDel';
            if (method_exists($this, $method)) {
                $errorInfo = call_user_func_array([$this, $method], [$mid, $passId]);
            }
            $errorInfo && $this->codeResponse(500, '', $errorInfo);
        }

        $this->codeResponse(200, '', '成功');
    }

    /**
     * 检查入参合法
     * @param string $mid 项目mid
     * @param int $passId 用户passid
     * @return string
     */
    private function checkParams($mid, $passId)
    {
        if (empty($mid) || !isset(Config::get('mid')[$mid])) {
            return 'mid非法';
        }

        if (empty($passId)) {
            return 'passId缺失';
        }

        $group = \Service\UserBase\Rules::getGroupNameByMid($mid);
        if (empty($group)) {
            return '该项目无生态数据';
        }

        return '';
    }

    /**
     * 删除昵称、头像数据
     * @param string $mid 项目mid
     * @param int $passId 用户passid
     * @return string
     */
    private function avatarInfoDel($mid, $passId) {
        $nickNameConfig = \Service\UserBase\Rules::getNicknameRule($mid);
        if (empty($nickNameConfig) || !isset($nickNameConfig['duplication'])) {
            return '配置数据缺失';
        }

        $ret = \Service\UserBase\UserAvatarCache::delUserAvatarHash($mid, $passId);
        if ($ret === false) {
            return '删除失败';
        }

        $model = loadModel('UserBase');
        $nickName = '';

        // 用户名不允许重复，需同步删除user_nickname_map表内数据
        if ((int) $nickNameConfig['duplication'] === 0) {
            $nickName = $model->getAvatarNicknameInfoByPassId($mid, $passId);
        }

        $ret = $model->delAvatarNicknameInfoByPassId($mid, $passId, $nickName ? $nickName['nickname'] : '');
        if ($ret === false) {
            return '删除失败';
        }

        return '';
    }
}