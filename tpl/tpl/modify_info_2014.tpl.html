{{include file="header.tpl.html"}}
<div class="wrap">
    <div class="box clearfix">
        <div class="my-options clearfix">
            <div class="avatar"><a href="/member/avatar.php" class="upload"><img src="/{{$pageArray.userimg}}" onerror="this.src='../pic/photo.jpg'"/><i class="ico-upload"></i></a></div>
            <div class="others">
                <div class="username">
                    <span class="name">{{$smarty.const.USERNAME}}</span>
                    {{if $pageArray.OAuthType}}
                    <i class="ico ico-edit"></i><a href="edit_user.php" class="cBlue">&nbsp;完善帐号资料</a>
                    {{else}}
                    <i class="ico ico-edit"></i><a href="edit_password.php" class="cBlue">&nbsp;修改密码</a>
                    {{/if}}
                </div>
                <div class="f14"><i class="ico ico-mail"></i>&nbsp;密保邮箱</div>
                <div class="mt10">
                    {{if $pageArray.OAuthType}}
                    <span style="color:#ff5500;margin:0 30px;">未设置</span><a href="improve_notice.php" class="btn-a mr20">设置邮箱</a>
                    {{elseif $pageArray.info.email_status eq 0}}
                    <input type="text" name="email" id="email" class="inputTxtA mr20" value="{{$pageArray.info.email}}" onblur="checkEmail();" />
                    <input type="hidden" id="email_old" value="{{$pageArray.info.email}}" />
                    <i id="email_ok" class="ico ico-right mr20" style="display: none;"></i>
                    <a href="javascript:;" onclick="validate();" class="btn-a mr20">验证邮箱</a>
                    <span id="send" class="cGray">可通过邮箱找回忘记的密码</span>
                    <div id="email_msg" class="mt5" style="display:none;">
                        <i class="ico ico-warn2"></i>
                        <span class="cRed">您输入的邮箱格式不正确！</span>
                    </div>
                    {{else}}
                    <span style="margin-right:20px;">{{$pageArray.info.email}}</span>
                    <a href="modify_email.php" class="btn-a mr20">修改邮箱</a>
                    <span class="cGray">可通过邮箱找回忘记的密码</span>
                    {{/if}}
                </div>
            </div>
        </div>
        <div class="clear"></div>
        <div class="acount clearfix">
            <div class="th">
                <span class="tit"><i class="ico ico-user"></i>帐号绑定</span>绑定QQ，微博后，您可以使用QQ，微博帐号快速登录2345，不用担心记不住用户名和密码！
            </div>
            {{if $pageArray.OAuthMsg == 'weibobind'}}
            <div class="tip-org mt10"><i class="ico ico-warn1"></i><span class="con">此微博帐号已经同其他2345帐号绑定，请更换绑定的微博帐号。</span></div>
            {{/if}}
            {{if $pageArray.OAuthMsg == 'qqbind'}}
            <div class="tip-org mt10"><i class="ico ico-warn1"></i><span class="con">此QQ帐号已经同其他2345帐号绑定，请更换绑定的QQ帐号。</span></div>
            {{/if}}
            <div class="tip-org mt10" id="unbind_email_msg" style="display: none;"></div>
            <ul class="ulBind">
                {{if $pageArray.OAuthType == 'qq'}}
                <li><div class="logo"><i class="ico logo_qq"></i></div><div class="p">已经绑定QQ帐号{{if $pageArray.OAuthBinds.qq.nickname}}"{{$pageArray.OAuthBinds.qq.nickname|cutSubStr:"8":"..."}}"{{/if}}</div><a href="improve_notice.php" class="btn-c mt5">解除绑定</a></li>
                <li><div class="logo"><i class="ico logo_wb"></i></div><div class="p">绑定新浪微博，一键使用2345服务</div><a href="improve_notice.php" class="btn-b mt5">绑定</a></li>
                {{elseif $pageArray.OAuthType == 'weibo'}}
                <li><div class="logo"><i class="ico logo_qq"></i></div><div class="p">绑定腾讯QQ，一键使用2345服务</div><a href="improve_notice.php" class="btn-b mt5">绑定</a></li>
                <li><div class="logo"><i class="ico logo_wb"></i></div><div class="p">已经绑定微博帐号{{if $pageArray.OAuthBinds.weibo.nickname}}"{{$pageArray.OAuthBinds.weibo.nickname|cutSubStr:"8":"..."}}"{{/if}}</div><a href="improve_notice.php" class="btn-c mt5">解除绑定</a></li>
                {{else}}
                {{if $pageArray.OAuthBinds.qq}}
                <li><div class="logo"><i class="ico logo_qq"></i></div><div class="p">已经绑定QQ帐号{{if $pageArray.OAuthBinds.qq.nickname}}"{{$pageArray.OAuthBinds.qq.nickname|cutSubStr:"8":"..."}}"{{/if}}</div><a href="#" {{if $pageArray.info.email_status != 0}}onclick="$.getScript('http://login.2345.com/oauth/bind/qq.php?act=request_unbind',function(){show_unbind_email_msg();});return false;"{{else}}onclick="alert('请先验证您的邮箱。');return false;"{{/if}} class="btn-c mt5">解除绑定</a></li>
                {{else}}
                <li><div class="logo"><i class="ico logo_qq"></i></div><div class="p">绑定腾讯QQ，一键使用2345服务</div><a href="http://login.2345.com/oauth/bind/qq" target="_blank" class="btn-b mt5">绑定</a></li>
                {{/if}}
                {{if $pageArray.OAuthBinds.weibo}}
                <li><div class="logo"><i class="ico logo_wb"></i></div><div class="p">已经绑定微博帐号{{if $pageArray.OAuthBinds.weibo.nickname}}"{{$pageArray.OAuthBinds.weibo.nickname|cutSubStr:"8":"..."}}"{{/if}}</div><a href="#" {{if $pageArray.info.email_status != 0}}onclick="$.getScript('http://login.2345.com/oauth/bind/weibo?act=request_unbind',function(){show_unbind_email_msg();});return false;"{{else}}onclick="alert('请先验证您的邮箱。');return false;"{{/if}} class="btn-c mt5">解除绑定</a></li>
                {{else}}
                <li><div class="logo"><i class="ico logo_wb"></i></div><div class="p">绑定新浪微博，一键使用2345服务</div><a href="http://login.2345.com/oauth/bind/weibo" target="_blank" class="btn-b mt5">绑定</a></li>
                {{/if}}
                {{/if}}
            </ul>
        </div>
        <div class="mod-a">
            <div class="th-a"><span><i class="ico ico-a"></i>基本信息</span></div>
            <div class="tb-a">
                <form id="my" action="" method="post" onsubmit="return checkAll();">
                    <input type="hidden" name="act" value="edit"/>
                    <div class="form-a">
                        <div class="fieldset">
                            <div class="label">QQ：</div>
                            <input type="text" class="inputTxtA" id="qqnew" name="qq" value="{{$pageArray.info.qq}}" maxlength="12" onblur="checkQq();">
                            <div class="mt5" id="qq_msg" style="display:none;">
                                <i class="ico ico-warn2"></i>
                                <span class="cRed">您输入的qq格式不正确！</span>
                            </div>
                        </div>
                        <div class="fieldset">
                            <div class="label">真实姓名：</div>
                            <input type="text" class="inputTxtA" id="truename" name="nickname" value="{{$pageArray.info.name}}" maxlength="26" onblur="checkName();" >
                            <div class="mt5" id="name_msg" style="display:none;">
                                <i class="ico ico-warn2"></i>
                                <span class="cRed">您输入的姓名格式不正确！</span>
                            </div>
                        </div>
                        <div class="fieldset">
                            <div class="label">性别：</div>
                            <div class="pt5">
                                <label for="female"><span class="radio{{if $pageArray.info.gender==2}} radio-sel{{/if}}"></span>女士&nbsp;&nbsp;<input class="none" type="radio" name="gender" id="female" value="2"{{if $pageArray.info.gender==2}} checked="checked"{{/if}}></label>
                                <label for="male"><span class="radio{{if $pageArray.info.gender==1}} radio-sel{{/if}}"></span>男士&nbsp;&nbsp;<input class="none" type="radio" name="gender" id="male" value="1"{{if $pageArray.info.gender==1}} checked="checked"{{/if}}></label>
                                <label for="nomale"><span class="radio{{if $pageArray.info.gender==0}} radio-sel{{/if}}"></span>保密<input class="none" type="radio" name="gender" id="nomale" value="0"{{if $pageArray.info.gender==0}} checked="checked"{{/if}}></label>
                            </div>
                        </div>
                        <div class="fieldset">
                            <div class="label">生日：</div>
                            <div class="selectA" id="year" style="z-index: 10">
                                <input type="hidden" name="year" value="{{$pageArray.info.bday.0}}"/>
                                <div class="text">年</div>
                                <div class="holder"><i class="arrow-btm"></i></div>
                                <div class="option" style="display:none">
                                    <ul>
                                        {{foreach key=key item=item from=$pageArray.cdate.year}}
                                        <li><a href="#" value="{{$item}}">{{$item}}</a></li>
                                        {{/foreach}}
                                    </ul>
                                </div>
                            </div>
                            <div class="selectA" id="month" style="z-index: 10">
                                <input type="hidden" name="month" value="{{$pageArray.info.bday.1}}"/>
                                <div class="text">月</div>
                                <div class="holder"><i class="arrow-btm"></i></div>
                                <div class="option" style="display:none">
                                    <ul>
                                        {{foreach key=key item=item from=$pageArray.cdate.month}}
                                        <li><a href="#" value="{{$item}}">{{$item}}</a></li>
                                        {{/foreach}}
                                    </ul>
                                </div>
                            </div>
                            <div class="selectA" id="day" style="z-index: 10">
                                <input type="hidden" name="day" value="{{$pageArray.info.bday.1}}"/>
                                <div class="text">日</div>
                                <div class="holder"><i class="arrow-btm"></i></div>
                                <div class="option" style="display:none">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="fieldset">
                            <div class="label">地区：</div>
                            <div class="selectA" id="mcl" style="z-index: 9">
                                <input type="hidden" name="area1"/>
                                <div class="text">省</div>
                                <div class="holder"><i class="arrow-btm"></i></div>
                                <div class="option" style="display:none">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                            <div class="selectA" id="mcm" style="z-index: 9">
                                <input type="hidden" name="area2"/>
                                <div class="text">市</div>
                                <div class="holder"><i class="arrow-btm"></i></div>
                                <div class="option" style="display:none">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                            <div class="selectA" id="mcn" style="z-index: 9">
                                <input type="hidden" name="area3"/>
                                <div class="text">区县</div>
                                <div class="holder"><i class="arrow-btm"></i></div>
                                <div class="option" style="display:none;">
                                    <ul>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="fieldset">
                            <input class="btn-d mt10" value="保存修改" type="submit" />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="wrap_btm"></div>
</div>
<script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
<script type="text/javascript">
                        var emailVal = '{{$pageArray.info.email}}';
                        var emailExp = emailVal.split('@');
                        if (emailExp[1] == 'gmail.com') {
                            var emailHost = 'http://www.' + emailExp[1];
                        } else {
                            var emailHost = 'http://mail.' + emailExp[1];
                        }
                        function show_unbind_email_msg() {
                            $('#unbind_email_msg').html('解绑邮件已发送到您的邮箱，访问您的邮箱完成解绑，<a href="' + emailHost + '" target="_blank">去邮箱收件</a>');
                            $('#unbind_email_msg').show();
                        }
                        var mcn_v = '{{$pageArray.info.area}}';
                        var mcl_v = mcn_v.substring(0, 2) + '0000';
                        var mcm_v = mcn_v.substring(0, 4) + '00';
                        function checkEmail()
                        {
                            $('#email_msg').hide();
                            $('#email_ok').hide();
                            var val = $('#email').val();
                            if (val != '')
                            {
                                if (!/^[_\.0-9a-z-]+@([0-9a-z][0-9a-z-]+\.)+[a-z]{2,4}$/.test(val))
                                {
                                    $('#email_msg span.cRed').html('您输入的邮箱格式不正确');
                                    $('#email_msg').show();
                                    $('#send').hide();
                                    return false;
                                }
                                $('#email_ok').show();
                                $('#send').show();
                            } else {
                                $('#email_msg span.cRed').html('邮箱不能为空');
                                $('#email_msg').show();
                                $('#send').hide();
                                return false;
                            }
                            return true;
                        }
                        function checkEmailValue()
                        {
                            var val = $('#email').val();
                            var old_val = $('#email_old').val();
                            if (val == old_val) {
                                return true;
                            } else {
                                return false;
                            }
                        }
                        function validate()
                        {
                            if (!checkEmail())
                                return false;
                            '{{if $pageArray.score > 5000 && $pageArray.isBindPhone}}';
                            if (checkEmailValue()) {
                                $('#send').show();
                                $('#send').html('验证邮件发送中...');
                                var url = 'http://{{$smarty.const.MY_DOMAIN}}/member/mail.php';
                                $.post(url, {'type': 'validate', 'email': $('#email').val()}, function(data) {
                                    if (data == '1') {
                                        var emailExp = $('#email').val().split('@');
                                        if (emailExp[1] == 'gmail.com') {
                                            var emailHost = 'http://www.' + emailExp[1];
                                        } else {
                                            var emailHost = 'http://mail.' + emailExp[1];
                                        }
                                        $('#send').html('验证邮件已发送到您的邮箱，<a href="' + emailHost + '" target="_blank">去邮箱收件</a>');
                                    } else {
                                        switch (data) {
                                            case '2':
                                                $('#email_msg span.cRed').html('您输入的邮箱已经被绑定');
                                                break;
                                            case '3':
                                                $('#email_msg span.cRed').html('您已经绑定过邮箱');
                                                break;
                                            case '4':
                                                $('#email_msg span.cRed').html('您输入的邮箱格式不正确');
                                                break;
                                        }
                                        $('#email_msg').show();
                                        $('#email_ok').hide();
                                        $('#send').html('');
                                        $('#send').hide();
                                    }
                                });
                            } else {
                                var new_email = $("#email").val();
                                window.location.href = 'shouji_validate_email.php?email=' + new_email;
                            }
                            '{{else}}';
                            $('#send').show();
                            $('#send').html('验证邮件发送中...');
                            var arg = 'type=validate&email=' + $('#email').val();
                            var url = 'http://{{$smarty.const.MY_DOMAIN}}/member/mail.php';
                            $.post(url, {'type': 'validate', 'email': $('#email').val()}, function(data) {
                                if (data == '1') {
                                    var emailExp = $('#email').val().split('@');
                                    if (emailExp[1] == 'gmail.com') {
                                        var emailHost = 'http://www.' + emailExp[1];
                                    } else {
                                        var emailHost = 'http://mail.' + emailExp[1];
                                    }
                                    $('#send').html('验证邮件已发送到您的邮箱，<a href="' + emailHost + '" target="_blank">去邮箱收件</a>');
                                } else {
                                    switch (data) {
                                        case '2':
                                            $('#email_msg span.cRed').html('邮箱已经被使用，请更换邮箱');
                                            break;
                                        case '3':
                                            $('#email_msg span.cRed').html('您已经绑定过邮箱');
                                            break;
                                        case '4':
                                            $('#email_msg span.cRed').html('您输入的邮箱格式不正确');
                                            break;
                                    }
                                    $('#email_msg').show();
                                    $('#email_ok').hide();
                                    $('#send').html('');
                                    $('#send').hide();
                                }
                            });
                            '{{/if}}';
                        }
                        $("span.radio").parents("label").click(function() {
                            $("span.radio").attr("class", "radio");
                            $(this).find("span.radio").addClass("radio-sel");
                        });
                        $(".selectA").click(function() {
                            $(this).find('.option').show();
                        }).hover(function() {
                            clearTimeout($(this).data('dh'));
                        }, function() {
                            var that = this;
                            $(this).data('dh', setTimeout(function() {
                                $(that).find(".option").hide();
                            }, 200));
                        });
                        $('.selectA .option ul li a').live('click', function() {
                            $(this).parents('.selectA').find('.text').html($(this).html());
                            $(this).parents('.selectA').find('input:hidden').val($(this).attr('value'));
                            $(this).parents('.selectA').find('.option').hide();
                            $(this).parents('.selectA').trigger('change');
                            return false;
                        });
                        $("#year,#month").change(function() {
                            allDay();
                        });
                        $("#mcl").change(function() {
                            my_ds.on_select_l($(this).find('input:hidden').val());
                        });
                        $("#mcm").change(function() {
                            my_ds.on_select_m($(this).find('input:hidden').val());
                        });
                        function allDay()
                        {
                            var year = $("#year input:hidden").val();
                            var month = $("#month input:hidden").val();
                            var maxDay;
                            if (year != "" && month != "")
                            {
                                if (month == '01' || month == '03' || month == '05' || month == '07' || month == '08' || month == '10' || month == '12')
                                {
                                    maxDay = 31;
                                }
                                else if (month == '04' || month == '06' || month == '09' || month == '11')
                                {
                                    maxDay = 30;
                                }
                                else
                                {
                                    if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0)
                                    {
                                        maxDay = 29;
                                    }
                                    else
                                    {
                                        maxDay = 28;
                                    }
                                }
                                var lis = [];
                                for (var i = 1; i <= maxDay; i++)
                                {
                                    var ii = i < 10 ? '0' + i : i;
                                    lis.push('<li><a href="#" value="' + ii + '">' + ii + '</a></li>');
                                }
                                $('#day .option ul').html(lis.join(''));
                            }
                        }
                        if ('{{$pageArray.info.bday.0}}' != '0000')
                        {
                            $("#year .text").html("{{$pageArray.info.bday.0}}");
                            $("#year input:hidden").val("{{$pageArray.info.bday.0}}")
                            if ('{{$pageArray.info.bday.1}}' != '00')
                            {
                                $("#month .text").html("{{$pageArray.info.bday.1}}");
                                $("#month input:hidden").val("{{$pageArray.info.bday.1}}")
                                allDay();
                                if ('{{$pageArray.info.bday.2}}' != '00')
                                {
                                    $("#day .text").html("{{$pageArray.info.bday.2}}");
                                    $("#day input:hidden").val("{{$pageArray.info.bday.2}}");
                                }
                            }
                        }

                        function checkAll()
                        {
                            if (!checkQq())
                                return false;
                            if (!checkName())
                                return false;
                            return true;
                        }

                        function checkName()
                        {
                            $('#name_msg').hide();
                            var val = $('#truename').val();
                            if (val != '')
                            {
                                if (!/^[\u4E00-\u9FA5\w_ ]*$/.test(val))
                                {
                                    $('#name_msg').show();
                                    return false;
                                }
                            }
                            return true;
                        }

                        function checkQq()
                        {
                            $('#qq_msg').hide();
                            var val = $('#qqnew').val();
                            if (val != '')
                            {
                                if (!(/^\d{5,}$/.test(val)))
                                {
                                    $('#qq_msg').show();
                                    return false;
                                }
                            }
                            return true;
                        }
                        //点击统计函数
                        document.onclick = function(e)//兼容IE,FF,OPERA
                        {
                            e = window.event || e;
                            sE = e.srcElement || e.target;
                            var isNotImg = true;
                            if (sE.tagName == "IMG" || sE.tagName == "A" || sE.tagName == "AREA")
                            {
                                if (sE.tagName == "IMG" && sE.src != "")
                                {
                                    sE = sE.parentNode;
                                    isNotImg = false;
                                }
                                if ((sE.tagName == "A" || sE.tagName == "AREA") && sE.href != "")
                                {
                                    cc(sE.href);
                                }
                            }
                        };
                        function cc(a) {
                            var b = arguments,
                                    web = "ajax54",
                                    a2,
                                    i1 = document.cookie.indexOf("uUiD="),
                                    i2;
                            if (b.length > 1)
                                web = b[1];
                            if (i1 != -1) {
                                i2 = document.cookie.indexOf(";", i1);
                                a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
                            }
                            if (!a2) {
                                a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                                document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
                            }
                            if (a.length > 0) {
                                var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                                $.getScript(c)
                            }
                            return true;
                        }
</script>
<script src="/js/my_city_2014.js" type="text/javascript"></script>
{{include file="footer.tpl.html"}}