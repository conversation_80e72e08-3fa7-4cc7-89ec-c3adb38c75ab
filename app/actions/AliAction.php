<?php

use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;

class AliAction extends Action
{
    public static function getPhoneNumber($params)
    {
        // https://github.com/aliyun/openapi-sdk-php/blob/master/README.md
        try {
            AlibabaCloud::accessKeyClient($params['appid'], $params['appkey'])
                ->regionId('cn-hangzhou')
                ->asDefaultClient();

            $result = AlibabaCloud::rpc()
                ->product('Dypnsapi')
                ->scheme('https') // https | http
                ->version('2017-05-25')
                ->action('GetMobile')
                ->method('POST')
                ->host('dypnsapi.aliyuncs.com')
                ->options([
                    'query' => [
                        'RegionId' => "cn-hangzhou",
                        'AccessToken' => $params['accessToken'],
                        //'OutId' => "",
                    ],
                ])
                ->request();
            return $result->toArray();
        } catch (ClientException $e) {
            xLog('ali', 'getPhoneNumber', $e->getErrorMessage());
        } catch (ServerException $e) {
            xLog('ali', 'getPhoneNumber2', $e->getErrorMessage());
        }
        return false;
    }
}
