<?php
includeRedis("RedminApp");
includeRedis("RedminSession");

class RedisDb extends Redis
{
    /**
     * redis构造函数
     */
    public function __construct()
    {
        $config = RedminApp::instance()->config;

        $this->connect($config['database']['redis']['host'], $config['database']['redis']['port'], 10);
        if (isset($config['database']['redis']['password']))
        {
            $this->auth($config['database']['redis']['password']);
        }
        $this->select(RedminSession::instance()->has('db') ? RedminSession::instance()->db : $config['database']['redis']['database']);
    }

    /**
     * 切换redis
     * @param $db
     * @return $this
     * <AUTHOR>
     */
    public function changeDB($db)
    {
        $this->select($db);
        return $this;
    }

}
