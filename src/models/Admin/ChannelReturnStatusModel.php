<?php

namespace Admin;

class ChannelReturnStatusModel extends \Model
{
    /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    public function getChannelReturnInfo($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        if (!empty($data['count']))
        {
            $sql = "SELECT count(1) AS total FROM my_sms_logs AS msl ";
            if (isset($data['channel']) && $data['channel'] > 0)
            {
                $sql .= " INNER JOIN my_sms_logs_channel as mslc ON msl.id = mslc.log_id ";
            }
            if (isset($data['receiptStatus']) && in_array($data['receiptStatus'], array(1, 2, 9)))
            {
                $sql .= " LEFT JOIN `sms_channel_status` AS scs ON msl.id = scs.`log_id` ";
            }
        }
        else
        {
            $sql = "SELECT msl.id,msl.phone,msl.send_time,msl.send_status,msl.send_response,msl.server_ip,msl.client_ip,mslc.codeDesc,scs.scs_id,scs.status,mslc.channel,scs.message,scs.add_time
            FROM my_sms_logs AS msl
            LEFT JOIN my_sms_logs_channel AS mslc ON msl.id = mslc.log_id
            LEFT JOIN `sms_channel_status` AS scs ON msl.id = scs.`log_id`";
        }
        if (!empty($data['phone']))
        {
            $where .= " msl.phone = :phone AND";
            $paramArr[':phone'] = $data['phone'];
        }

        if (isset($data['log_id']))
        {
            $where .= " msl.id = :log_id AND";
            $paramArr[':log_id'] = $data['log_id'];
        }

        if (isset($data['pid']) && !empty($data['pid']))
        {
            $where .= " msl.pid = :pid AND";
            $paramArr[':pid'] = $data['pid'];
        }

        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $where .= '  msl.send_time >=:startTime AND msl.send_time <=:endTime AND';
            $paramArr[':startTime'] = $data['startTime'] . ' 00:00:00 ';
            $paramArr[':endTime'] = $data['endTime'] . ' 23:59:59';
        }

        if (isset($data['channel']) && $data['channel'] > 0)
        {
            $where .= " mslc.channel = :channel AND";
            $paramArr[':channel'] = $data['channel'];
        }

        if (isset($data['receiptStatus']))
        {
            switch ($data['receiptStatus'])
            {
                case 0://全部
                default:
                    break;
                case 1://成功
                    $where .= " scs.status = 1 AND";
                    break;
                case 2://失败
                    $where .= " scs.status = 0 AND";
                    break;
                case 9://尚无数据
                    $where .= " scs.status is NULL AND";
                    break;
            }
        }

        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }
        $sql .= $where;
        if (empty($data['row']))
        {
            $sql .= ' order by msl.send_time desc';
        }

        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    public function getMultiChannelReturnInfo($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' msl.id,msl.phone,msl.illegal_phone,msl.send_time,msl.send_status,msl.send_response,msl.server_ip,msl.client_ip,mslc.codeDesc,scs.scs_id,scs.status,mslc.channel,scs.message ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col}
            FROM my_multi_sms_logs AS msl
            LEFT JOIN my_multi_sms_logs_channel AS mslc ON msl.id = mslc.log_id
            LEFT JOIN `sms_channel_status` AS scs ON msl.id = scs.`log_id`";

        if (isset($data['log_id']))
        {
            $where .= " msl.id = :log_id AND";
            $paramArr[':log_id'] = $data['log_id'];
        }

        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $where .= '  msl.send_time >=:startTime AND msl.send_time <=:endTime AND';
            $paramArr[':startTime'] = $data['startTime'] . ' 00:00:00 ';
            $paramArr[':endTime'] = $data['endTime'] . ' 23:59:59';
        }

        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by msl.id desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    public function getSmsChannelStatus($logId)
    {
        $sql = "SELECT `status`,message FROM sms_channel_status  WHERE log_id = :log_id";
        $paramArr[':log_id'] = $logId;

        return $this->pdo->find($sql, $paramArr);
    }

}
