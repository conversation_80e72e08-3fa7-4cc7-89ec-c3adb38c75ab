<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：YyCallbackController.php
 * 摘    要：语音回调
 * 修改日期：2016.03.27
 * */
namespace YyInterface;
use Controller;
class YyCallbackController extends Controller
{
    private static $isSms = false;
    public function __construct()
    {
        $sipIp = array(
            '***************',
            '***************',
            '**********',
            '************' //博霞出口IP
                );
        $ip = get_client_ip();
        //如果访问IP 不在数组中  返回401
        if (!in_array($ip, $sipIp))
        {
            header('HTTP/1.1 401 Unauthorized ');
            exit;
        }
    }

    public function actionCallbackReceiveLog()
    {
        $cdr = $this->Filter->post('cdr');
        $cdrData = preg_replace("/(.*)(\"sip_full_from\".*)(\"sip_full_to\".*)/isx", "$1$3",
            $cdr);
        $dataStr = \EncodingAction::transcoding($cdrData, 'utf-8');
        $arr = json_decode($dataStr, true);
        $passid = intval($arr['variables']['sip_h_X-passid']);
        $billsec = intval($arr['variables']['billsec']);
        $userAgent = isset($arr['variables']['X-sip_user_agent']) ? $arr['variables']['X-sip_user_agent'] : false;
        $client = strtolower($userAgent ? $userAgent : $arr['variables']['sip_user_agent']);
        $callPrefix = isset($arr['variables']['X-prefix']) ? $arr['variables']['X-prefix'] :
            0; // 被叫前缀
        if (strstr($client, 'im-client') || strstr($client, 'apple') || strstr($client,
            'csipsimple'))
        {
            // 之前打电话区分的渠道, 保留判断
        }
        else
        {
            $YyRedisAction = YyRedisAction::getInstance();
            //语音验证码
            // 发送验证码至用户失败，记录至缓存
            if (!isset($arr['variables']['is_rang_success']))
            {
                $YyRedisAction->voiceCodeSendError($arr['variables']['X-phone']);
                $isRang = 0;
            }
            elseif ($arr['variables']['is_rang_success'] == "true")
            {
                $isRang = 1;
                // 用户未应答，记录至缓存
                if (!isset($arr['variables']['is_user_answed']))
                {
                    $YyRedisAction->voiceCodeAnswerError($arr['variables']['X-phone']);
                }
            }

            $this->voiceCodeInsert($arr['variables']); //更新话单
        }


    }

    /**
     * 返回项目对应的ID
     * */
    public function getProjectTagNumber($tag)
    {
        $TelAction = \Admin\TelAction::getInstance();
        $projectList = $TelAction->getProjectList();
        foreach ($projectList as $projectInfo)
        {
            if ('audio_ver_' . $projectInfo['channel'] == $tag)
            {
                self::$isSms =  $projectInfo['sms'];
                return $projectInfo['number'];
            }
        }
        return - 1;
    }

    /**
     * 语音电话单入库
     * */
    private function voiceCodeInsert($variables)
    {
        $time = time();
        if (!empty($variables))
        {
            $gateway_flag = isset($variables['is_last_gateway']) ? trim($variables['is_last_gateway']) : false;
            if ($gateway_flag == "true")
            {
                $vid = isset($variables['X-vid']) ? intval($variables['X-vid']) : 0;
                if ($vid)
                {
                    $date = date("Y-m-d", $time);
                    $from = $this->getProjectTagNumber($variables['sip_from_display']);
                    $isRang = isset($variables['is_rang_success']) && $variables['is_rang_success'] ==
                        "true" ? 1 : 0;
                    $isAnswed = isset($variables['is_user_answed']) && $variables['is_user_answed'] ==
                        "true" ? 1 : 0;
                    $callStart = isset($variables['start_stamp']) ? strtotime($variables['start_stamp']) :
                        0;
                    $callEnd = isset($variables['end_stamp']) ? strtotime($variables['end_stamp']) :
                        0;
                    $callTime = isset($variables['billsec']) ? intval($variables['billsec']) : 0;
                    $serverIp = isset($variables['current_server_ip']) ? trim($variables['current_server_ip']) :
                        "";
                    $hangupCause = isset($variables['hangup_cause']) ? trim($variables['hangup_cause']) :
                        "";
                    $gatewayName = isset($variables['current_gateway_name']) ? trim($variables['current_gateway_name']) :
                        "";
                    $gatewayList = isset($variables['rout_gateways']) ? trim($variables['rout_gateways']) :
                        "";
                    $callsum = isset($variables['X-callsum']) ? intval($variables['X-callsum']) : 1;
                    
                    $YyCallbackModel = YyCallbackModel::getInstance();
                    if ($callsum > 1)
                    {
                        $upData = array(
                            'is_rang' => $isRang,
                            'is_answed' => $isAnswed,
                            'call_start' => $callStart,
                            'call_end' => $callEnd,
                            'call_time' => $callTime,
                            'server_ip' => $serverIp,
                            'hangup_cause' => $hangupCause);
                        $condition = array('where' => ' vid = :vid ', 'params' => array(':vid' => $vid, ));
                        $isTrue = $YyCallbackModel->upVoiceCode($upData, $condition);
                    }
                    else
                    {
                        $inserData = array(
                            'date' => $date,
                            'from' => $from,
                            'vid' => $vid,
                            'is_rang' => $isRang,
                            'is_answed' => $isAnswed,
                            'call_start' => $callStart,
                            'call_end' => $callEnd,
                            'call_time' => $callTime,
                            'server_ip' => $serverIp,
                            'hangup_cause' => $hangupCause,
                            'gateway_name' => $gatewayName,
                            'gateway_list' => $gatewayList);
                        $isTrue = $YyCallbackModel->addVoiceCode($inserData);
                        //没有接听的时候 快修王的不发短信
                        if ( ! $isAnswed && self::$isSms )
                        {
                            $YyRedisAction = YyRedisAction::getInstance();
                            $YyRedisAction->sendNoticeSms($variables['X-phone'],$variables['X-code'],$vid);
                        }
                    }
                }
            }
        }
    }
}

?>