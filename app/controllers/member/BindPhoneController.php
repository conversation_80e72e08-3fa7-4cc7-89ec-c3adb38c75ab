<?php

/**
 * 绑定手机号
 */
class BindPhoneController extends Controller
{

    private $pageArray;

    /**
     * 构造函数初始化数据
     */
    public function __construct()
    {
        $this->pageArray['page'] = "phone";
        $this->pageArray['nav'] = "bindphone";
        $this->pageArray['tabIndex'] = 5; //账号管理菜单
        $this->pageArray['open'] = ($_GET['page'] == "open") ? true : false;
        $memberModel = loadModel("member");
        //取得用户信息
        $userinfo = $memberModel->read(PASSID);
        $this->pageArray['phonetype'] = ($userinfo['phone']) ? "edit" : "bind";
        $this->pageArray['emailtype'] = ($userinfo['email'] && $userinfo['email_status']) ? "edit" : "bind";
    }

    /**
     * 默认方法
     */
    public function actionIndex()
    {

        $forward = getForwardUrl();
        $refer = getReferDomain();
        if ($refer && $refer != 'login.2345.com')
        {
            setcookie("refer_domain", $refer, 0, "/");
        }
        $memberModel = loadModel("member");
        //取得用户信息
        $userinfo = $memberModel->read(PASSID);
        if ($userinfo['phone'] || $userinfo['phone_redundancy'])
        {
            $url = '';
            if ($this->pageArray['open'])
            {
                $url .= "?page=open";
            }
            $url .= $forward ? (($url ? "&" : "?") . "forward=" . urlencode($forward)) : "";
            $url = "/member/editPhone" . $url;
            redirect($url);
        }
        $this->pageArray['forward'] = $forward;
        $this->pageArray['screen'] = $_GET['screen'];
        $this->pageArray['title'] = "绑定手机-2345用户中心";
        if (IsMobile())
        {
            loadView('member/m/bind_phone_wap.tpl.html', $this->pageArray);
        }
        else
        {
            loadView('member/bind_phone.tpl.html', $this->pageArray);
        }
    }

    /**
     * 绑定手机
     */
    public function actionDo()
    {
        $forward = getForwardUrl();
        $memberModel = loadModel("member");
        $userinfo = $memberModel->read(PASSID);
        if (!$userinfo["phone"] && $_SERVER['REQUEST_METHOD'] == 'POST')
        {
            $encryptAction = loadAction('Encrypt');
            $_POST['phone'] = $encryptAction::aes128cbcHexDecrypt($_POST['phone']);
            $phone = $_POST['phone'];
            $code = $_POST['code'];
            $checkcode = md5(trim($phone) . trim($code));
            $phoneAction = loadAction("phone");
            $retStatus = $phoneAction->checkBindCode("login", PASSID, $checkcode);
            if ($retStatus)
            {
                $bindResult = $phoneAction->bind(PASSID, $phone);
                $redis = RedisEx::getInstance();
                $timeKey = "PB:time:login:" . PASSID;
                $codeKey = "PB:code:login:" . PASSID;
                if ($bindResult == '200.0')
                {
                    //手机绑定成功，记录日志
                    $userinfo['passid'] = PASSID;

                    //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:PHONE;
                    $objLogV2Action = loadAction('logV2');
                    $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'PHONE', $userinfo, '', $phone);

                    $redis->del($timeKey);
                    $redis->del($codeKey);
                    $this->log('', $phone);
                    if (IsMobile())
                    {
                        $userinfo = $memberModel->read(PASSID);
                        $this->pageArray['info'] = $userinfo;
                        $this->pageArray['forward'] = $forward;
                        $this->pageArray['tabIndex'] = 2;
                        loadView('member/m/bind_phone_wap.tpl.html', $this->pageArray);
                        return;
                    }
                    else
                    {
                        echo 200;
                    }
                }
            }
            else
            {
                echo 300;
            }
        }
    }

    /**
     * 发送验证码
     */
    public function actionSendCode()
    {
        $encryptAction = loadAction('Encrypt');
        $_POST['phone'] = $encryptAction::aes128cbcHexDecrypt($_POST['phone']);
        $phone = $_POST['phone'];
        $phoneAction = loadAction("phone");
        $retCode = $phoneAction->sendBindCode("login", PASSID, $phone, 5, 149);
        echo $retCode;
    }

    /**
     * 检测验证码
     */
    public function actionCheckCode()
    {
        $phone = $_POST['phone'];
        $code = $_POST['code'];
        $checkcode = md5($phone . $code);
        $regex = Config::get('regex');
        if (preg_match($regex['phone'], $phone))
        {
            $phoneAction = loadAction("phone");
            $status = $phoneAction->checkBindCode("login", PASSID, $checkcode);
            $flag = $status ? "200" : "400";
            echo $flag;
        }
        else
        {
            echo "300";
        }
    }

    /**
     *
     * @param $phoneOld
     * @param $phone
     * <AUTHOR>
     */
    private function log($phoneOld, $phone)
    {
        $clientIp = get_client_ip();
        $refer = $_COOKIE['refer_domain'] ? $_COOKIE['refer_domain'] : '';
        $from = 'login.2345.com';
        $phoneModel = loadModel('phone');
        $phoneModel->log(PASSID, array('phone_old' => $phoneOld, 'phone' => $phone, 'op_time' => time(), 'op_ip' => $clientIp, 'op_domain' => "login.2345.com", 'op_refer' => $refer, 'op_from' => $from));
    }

}
