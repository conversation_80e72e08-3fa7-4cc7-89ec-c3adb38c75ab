<?php

use WebLogger\Facade\LoggerFacade;

class FindAction extends Action
{

    /**
     * 生成找回密码链接并发送邮件
     * @param string $username 用户名
     * @return array 状态
     */
    public function find($username)
    {
        $isUseRedis = Config::get("isUseRedis");
        if ($username == 'admin')
        {
            return array('1002');
        }
        $domain = 1;
        if ($isUseRedis)
        {
            $redis = RedisEx::getInstance();
            $nowTime = time();
            $getPwdKey = "getPwdTimeLog:" . md5($username . '#' . $domain);
            $timeLogs = $redis->lRange($getPwdKey, 0, 9);
            if ($timeLogs)
            {
                if (isset($timeLogs[9]))
                {
                    $redis->lTrim($getPwdKey, 0, 9);
                    if ($timeLogs[9] > ($nowTime - 60 * 60 * 24 * 15))
                    {
                        return array('1001');
                    }
                }
            }
            $redis->zRemRangeByScore('getPwdForbidenUsers', 0, $nowTime - 60 * 60 * 24 * 15);
        }
        $memberModel = loadModel('member');
        $result = $memberModel->findPassword($username);
        if ($isUseRedis)
        {
            if (count($result))
            {
                $redis->lPush($getPwdKey, $nowTime);
                $redis->expireAt($getPwdKey, $nowTime + 60 * 60 * 24 * 15);
                $timeLogs = $redis->lRange($getPwdKey, 0, 9);
                if (isset($timeLogs[9]))
                {
                    if ($timeLogs[9] > ($nowTime - 60 * 60 * 24 * 15))
                    {
                        //添加禁止用户
                        $redis->zAdd('getPwdForbidenUsers', $nowTime, mb_convert_encoding($username, "UTF-8", "GBK"));
                    }
                }
            }
        }
        if (sizeof($result))
        {
            if ($result[0]['email'] == "")
            {
                return array(1004);
            }
            $checkCode = $result[0]['checkcode'];
            $checkStr = md5($result[0]['id'] . $result[0]['domain'] . MD5KEY);
            $smtpConfig = Config::get('smtp');
            $smtpemailto = $result[0]['email'];
            $mailsubject = "2345网址导航用户中心取回密码服务";
            $smtp = loadVendor('smtp');
            $smtp->init($smtpConfig['server'], $smtpConfig['port'], $smtpConfig['username'], $smtpConfig['password']);
            if (strpos($smtpemailto, '@21cn.com') !== false || strpos($smtpemailto, '@189.cn') !== false)
            {
                $link_TXT = PASSPORT_HOST . "/find/password?id=" . $result[0]['id'] . "&domain=" . $result[0]['domain'] . "&key=" . $checkStr . "&checkCode=" . $checkCode;
                $mailbody_TXT = "您好：" . $username . ",请将该网址（" . $link_TXT . "）复制并粘贴至新的浏览器窗口中，非常感谢您使用我们的服务。如果您有任何问题或疑问，请浏览帮助，地址如下：http://gaoji.2345.com/help.php?c=4";
                $mailtype = "TXT"; //邮件格式（HTML/TXT）,TXT为文本邮件
                $smtp->sendmail($smtpemailto, $smtpConfig['email'], $mailsubject, $mailbody_TXT, $mailtype);
            }
            else
            {
                $link = "<a href='" . PASSPORT_HOST . "/find/password?id=" . $result[0]['id'] . "&domain=" . $result[0]['domain'] . "&key=" . $checkStr . "&checkCode=" . $checkCode . "' target='_blank'>http://login.2345.com/find/password?id=" . $result[0]['id'] . "&domain=" . $result[0]['domain'] . "&key=" . $checkStr . "&checkCode=" . $checkCode . "</a>";
                $mailbody = "<pre>您好：" . $username . "<br><p><br>&nbsp;&nbsp;&nbsp;&nbsp;如果您没有请求找回您的2345帐号的密码，请不用理会此封邮件，请直接删除！<br></p><p><br>&nbsp;&nbsp;&nbsp;&nbsp;点击以下地址找回密码<br></p><p>&nbsp;&nbsp;&nbsp;&nbsp;" . $link . "<br></p><p>&nbsp;&nbsp;&nbsp;&nbsp;如果通过点击以上链接无法访问，请将该网址复制并粘贴至新的浏览器窗口中。非常感谢您使用我们的服务。 <br></p><p><br>&nbsp;&nbsp;&nbsp;&nbsp;如果您有任何问题或疑问，请浏览帮助，地址如下：<br></p><p>&nbsp;&nbsp;&nbsp;&nbsp;http://gaoji.2345.com/help.php?c=4<br></p><p><br>&nbsp;&nbsp;&nbsp;&nbsp;该邮件为系统自动发出,请不要回复,谢谢！<br></p><p><br>2345.com网址导航-实用查询<br></p>http://www.2345.com/</pre>";
                $mailtype = "HTML"; //邮件格式（HTML/TXT）,TXT为文本邮件
                $smtp->sendmail($smtpemailto, $smtpConfig['email'], $mailsubject, $mailbody, $mailtype);
            }
            $email = $result[0]['email'];
            $email_other = explode("@", $result[0]['email']);
            $email_uname = $email_other[0];
            $c = intval(strlen($email_uname) / 3);
            $new = substr($email_uname, 0, strlen($email_uname) - $c) . "****";
            $email = $new . "@" . $email_other[1];
            return array('1', $email);
        }
        else
        {
            return array('1003');
        }
    }

    /**
     * 判断账号类型
     * */
    public function checkAccoutType($username)
    {
        $memberModel = loadModel('member');
        $regex = Config::get('regex');
        if (preg_match($regex['phone'], $username))
        {
            $ret = $memberModel->getPassidByPhone($username);
            if ($ret !== false)
            {
                return array('type' => 'phone', 'passid' => $ret);
            }
        }
        if (filter_var($username, FILTER_VALIDATE_EMAIL))
        {
            //查询已经验证过邮箱的用户
            $ret = $memberModel->getPassidByEmail($username, 1);
            if ($ret[0] != 0)
            {
                return array('type' => 'email', 'passid' => $ret);
            }
        }
        $ret = $memberModel->getPassidByUsername($username);
        if ($ret !== false)
        {
            return array('type' => 'username', 'passid' => $ret);
        }
        if (filter_var($username, FILTER_VALIDATE_EMAIL))
        {
            //查询未验证过邮箱的用户
            $ret = $memberModel->getPassidByEmail($username, 0);
            if ($ret[0] != 0)
            {
                return array('type' => 'email', 'passid' => $ret);
            }
        }
        return array('type' => '', 'passid' => '');
    }

}
