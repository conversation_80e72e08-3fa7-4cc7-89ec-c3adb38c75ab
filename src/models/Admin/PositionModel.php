<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：PositionModel.php
 * 摘    要：短信位置
 * 作    者：zhangchao
 * 修改日期：2015.12.29
 * */
class PositionModel extends Model
{
    
    const PositionTable = 'position_list';    
    
    //项目管理权限(删除 添加 修改)
    public $project_role = array(
        'add'       =>  array(1,2,3),
        'update'    =>  array(1,2,3),
        'del'       =>  array(1,2,3)
    );
    
    
    //操作错误码
    public $erro = array(
        '0' => '参数缺失',
        '1' => '操作成功',
        '2' => '操作失败',
        '3' => '没有权限',
        '4' => '项目已经存在或是数据库错误'
    );

            /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    
    /**
     * 功    能：获取位置列表
     */
    public function getPositionList($positionName = '', $data)
    {
        $col = ' pl_id,pid,position_name,add_time,type ';
        if (!empty($data['count']))
        {
            $col = ' count(pl_id) as total';
        }

        $sql = "SELECT {$col} FROM " . self::PositionTable;
                
        if (!empty($positionName))
        {
            $sql .= " where position_name like '%{$positionName}%'";
        }

        if (isset($data['page']) && !empty($data['limit']))
        {
            $sql .= "  order by pl_id desc limit {$data['page']} , {$data['limit']}";
        }

        if (!empty($data['count']))
        {
            return $this->pdo->find($sql);
        }
        else
        {
            return $this->pdo->findAll($sql);
        }

    }
    
    
    /**
     * 功    能：删除位置
     */
    public function delPositionById($pl_id) {        
        return ( $this->pdo->delete(
                    self::PositionTable, 
                    array(
                        'where' => ' pl_id = :pl_id ', 
                        'params' => array(':pl_id' => $pl_id)
                      )
                  ) === false ) ? false : true;
    }
    
    
    /**
     * 功    能：是否有位置的权限
     */
    public function ifRole($roleId, $action = 'add') {
        return in_array($roleId, $this->project_role[$action]) ? true : false;
    }
    
    
    /**
     * 功    能：添加位置
     */
    public function addPosition($data) {
        //查看项目是否已经存在
        $sql_exit = 'select pl_id from ' . self::PositionTable . ' WHERE position_name=:position_name LIMIT 1';
        if ($this->pdo->find($sql_exit, array(':position_name' => $data['position_name'])) ) {
            return false;
        } else {
            $data['add_time'] = date('Y-m-d H:i:s');
            return ( $this->pdo->insert(self::PositionTable, $data) === false ) ? false : $this->pdo->lastInsertId();
        }        
    }
    
    
    /**
     * 功    能：更新位置信息
     */
    public function editPosition($data)
    {
        $condition = array('where' => ' pl_id = :pl_id ', 'params' => array(':pl_id' =>
                    $data['pl_id']));
        
        if (!empty($data['pid']))
        {
            $updata['pid'] = $data['pid'];
        }

        if (!empty($data['position_name']))
        {
            $updata['position_name'] = $data['position_name'];
        }
        
        if (!empty($data['type']))
        {
            $updata['type'] = $data['type'];
        }
                
        return ( $this->pdo->update(self::PositionTable, $updata, $condition) === false ) ? false : true;
    }

    

}
