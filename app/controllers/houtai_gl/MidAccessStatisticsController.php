<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：MidAccessStatisticsController.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：20/09/2018 09:19
 */

includeBase('AdminController');

class MidAccessStatisticsController extends AdminController
{
    private $redis;

    /**
     * User: panj
     * @return bool|mixed
     */
    protected function getRedis()
    {
        if (is_null($this->redis))
        {
            loadAction('LoadRedis');
            $this->redis = LoadRedisAction::getRedis('dlx_log', 'hash1');
        }

        return $this->redis;
    }

    /**
     * User: panj
     *
     * @param string $mid mid
     * @param string $date key date
     *
     * @return string
     */
    protected function getRedisKey($mid, $date)
    {
        loadAction('Monitor');

        return MonitorAction::getMidSecondKey($mid, $date);
    }

    /**
     * User: panj
     * @return array
     */
    protected function getAllMid()
    {
        return Config::get('mid');
    }

    /**
     * User: panj
     *
     * @return null
     */
    public function actionRun()
    {
        $code = !empty($_GET['code']) ? $_GET['code'] : '';
        if (empty($code) || $code != 'gybiet49LKOCe2P31xFvwh2VBCP3C9UP')
        {
            exit("非法访问");
        }
        $begin = !empty($_GET['begin']) ? $_GET['begin'] : date("Y-m-d 23:59:00", strtotime("-1 days"));
        $end = !empty($_GET['end']) ? $_GET['end'] : date("Y-m-d 00:02:00", time());
        $date = date('Ymd');
        $csvFile = APPPATH . "/logs/mid_access_{$date}.csv";
        if (file_exists($csvFile))
        {
            exit("2");
        }
        $begin = new DateTime($begin);
        $end = new DateTime($end);
        $interval = DateInterval::createFromDateString(' 1 seconds');
        $period = new DatePeriod($begin, $interval, $end);

        $midAccess = [
            [
                'mid',
                '项目名称',
                '访问时间',
                '访问量',
            ],
        ];
        foreach ($this->getAllMid() as $mid => $name)
        {
            foreach ($period as $dt)
            {
                $keyDate = $dt->format('Ymd-His');
                $redisKey = $this->getRedisKey($mid, $keyDate);
                if (!$this->getRedis()->exists($redisKey))
                {
                    continue;
                }
                else
                {
                    $count = $this->getRedis()->get($redisKey);
                    $midAccess[] = [
                        $mid,
                        $name,
                        $dt->format('Y-m-d H:i:s'),
                        $count,
                    ];
                }
            }
        }

        loadAction('Encoding');

        $fp = fopen($csvFile, 'w');
        fwrite($fp, chr(0xEF).chr(0xBB).chr(0xBF)); // 写入BOM头，防止乱码
        while ($field = current($midAccess))
        {
            $field = EncodingAction::transcoding($field, 'utf-8');
            fputcsv($fp, $field);
            next($midAccess);
        }

        fclose($fp);
        echo '请在208上logs目录下查看';
    }
}
