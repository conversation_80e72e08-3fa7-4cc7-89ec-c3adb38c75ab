<?php

namespace Admin;
use Controller;
class TelephoneBillController extends Controller
{

    public function actionList()
    {
        $pageArray = array();

        $pageArray['startTime'] = $this->Filter->getPost('startTime');
        $pageArray['endTime'] = $this->Filter->getPost('endTime');
        $pageArray['from'] = $this->Filter->getPost('from');
        $pageArray['isRang'] = $this->Filter->getPost('isRang');
        $pageArray['isAnswed'] = $this->Filter->getPost('isAnswed');
        $pageArray['isSms'] = $this->Filter->getPost('isSms');
        $pageArray['statusSms'] = $this->Filter->getPost('statusSms');
        $pageArray['mobile'] = $this->Filter->getPost('mobile');

        if (isset($pageArray['isRang']) && $pageArray['isRang'] == '')
        {
            $pageArray['isRang'] = -1;
        }

        if (isset($pageArray['isAnswed']) && $pageArray['isAnswed'] == '')
        {
            $pageArray['isAnswed'] = -1;
        }

        if (isset($pageArray['isSms']) && $pageArray['isSms'] == '')
        {
            $pageArray['isSms'] = -1;
        }

        if (isset($pageArray['statusSms']) && $pageArray['statusSms'] == '')
        {
            $pageArray['statusSms'] = -1;
        }

        if (empty($pageArray['startTime']) || empty($pageArray['endTime']))
        {
            $pageArray['startTime'] = date("Y-m-d", strtotime('-1 day'));
            $pageArray['endTime'] = date('Y-m-d');
        }


        $pageArray['isRang'] = ($pageArray['isRang'] >= 0) ? $pageArray['isRang'] : -1;
        $pageArray['isAnswed'] = ($pageArray['isAnswed'] >= 0) ? $pageArray['isAnswed'] :
            -1;
        $pageArray['isSms'] = ($pageArray['isSms'] !== '' && $pageArray['isSms'] >= 0) ?
            $pageArray['isSms'] : -1;
        $pageArray['statusSms'] = ($pageArray['statusSms'] >= 0) ? $pageArray['statusSms'] :
            -1;
        $TelephoneBillModel = TelephoneBillModel::getInstance();

        $TelAction = TelAction::getInstance();

        $PageAction = \PageAction::getInstance();


        $pageArray['projectList'] = $TelAction->getProjectList();
        $projectArr = array();
        foreach ($pageArray['projectList'] as $projectInfos )
        {
            $projectArr[$projectInfos['channel']] = $projectInfos['desc'];
        }

        $queryData = array(
            'startTime' => $pageArray['startTime'],
            'endTime' => $pageArray['endTime'],
            'from' => $pageArray['from'],
            'isRang' => $pageArray['isRang'],
            'isAnswed' => $pageArray['isAnswed'],
            'isSms' => $pageArray['isSms'],
            'statusSms' => $pageArray['statusSms'],
            'mobile' => $pageArray['mobile']);

        $telList = $TelephoneBillModel->getTelBillList($queryData, $PageAction->
            returnPageConfig(30));

        foreach ($telList as $telListKey => $telListInfo)
        {
            if ($telListInfo['status'] == 0)
            {
                if (($telListInfo['add_time'] + 1800) < time())
                {
                    $telList[$telListKey]['status'] = '已过期';
                }
                else
                {
                    $telList[$telListKey]['status'] = '未使用';
                }
            }
            elseif ($telListInfo['status'] == 1)
            {
                $telList[$telListKey]['status'] = "已使用";
            }

            $telList[$telListKey]['statusDesc'] = TelAction::getStatusDesc($telListInfo['request_status']);
            $telList[$telListKey]['add_time'] = date('Y-m-d H:i:s', $telListInfo['add_time']);
            $telList[$telListKey]['use_time'] = $telListInfo['use_time'] > 0 ? date('Y-m-d H:i:s',
                $telListInfo['add_time']) : '';
            $telList[$telListKey]['statusSmsDesc'] = $telListInfo['is_sms'] ? TelAction::
                getStatusSmsDesc($telListInfo['status_sms'], $telListInfo['is_sms']) : "";
            $telList[$telListKey]['isAnswedDesc'] = $telListInfo['is_answed'] ? '是' : '否';
            
            $telList[$telListKey]['from'] = !empty($projectArr[$telListInfo['from']]) ?$projectArr[$telListInfo['from']] :$telListInfo['from'];

        }
        $pageArray['telList'] = $telList;

        $queryData['count'] = true;
        $queryData['row'] = true;

        $telListCount = $TelephoneBillModel->getTelBillList($queryData);
        $pageArray['showPage'] = $PageAction->showPage($telListCount['total'],
            '/Admin/TelephoneBill/List');

        //print_r($pageArray);exit;
        loadView('Admin/telephone_bill_list.tpl.html', $pageArray);
    }
}

?>