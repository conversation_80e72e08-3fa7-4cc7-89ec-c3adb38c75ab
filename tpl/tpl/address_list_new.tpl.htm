{{include file="header.tpl.html"}}
<style type="text/css">
	* {border: 0;margin: 0;padding: 0;}
	.ui-dialog{text-align: center;background: #fff;border: 0 none;border-collapse: collapse;border-spacing: 0;margin: 0;width: auto;border-radius: 6px;}
	.ui-dialog-header{text-align:left;}
	.ui-dialog-title{height: 36px;line-height: 36px;padding: 0 15px;font-size: 16px;font-weight: bold;}
	.ui-dialog-close{font-size: 24px;margin-right:8px;font-weight: normal;cursor: pointer;display: inline-block;float: right;background:#fff;}
	.ui-dialog-close:hover{color: #f5694b;}
	.ui-dialog-content{padding:14px;line-height:22px;width:480px;text-align: left;}
	.ui-dialog-autofocus{font-size: 14px;width: 100px;height:30px;cursor: pointer;margin-bottom:15px;}
</style>
<!-- main -->
<div class="wrap">
	<div class="box clearfix">
        <div class="tit clearfix">
			<span class="titleft"></span>
			<span class="titright"></span>
			<h2>通讯录</h2></div>
		<div class="boxbd clearfix">
			<div class="module_1">
				<div class="set-cnt">   
					<h2 class="txlnav"><span class="h2l"></span><span class="h2bg" ><b id="refresh_cache">在线通讯录</b>通讯录是个人机密信息，仅限本人登录才能查看</span><span class="h2r"></span><font class="d-tx"> <a href="#" id="import_contact">导入</a>/<a href="#" id="export_contact">导出</a></font> </h2>
					<div class="txl">
						<div class="txl-l">
							<div class="txl-l-cnt"><input id="contact_search_input" type="text" class="ipt-lxr" value="搜索联系人" name=""> <input id="contact_search_button" type="button" value="" name="" class="btn-lxr"><br>
								<div id="group_actionbar" ><input id="group_edit" type="button" value="编辑组" name="" class="edit01">&nbsp;<input id="group_delete" type="button" value="删除组" name="" class="del01"></div>
								<div class="t-group">
									<dl id="addrbook_contact"></dl>
									<dl><dd id="addrbook_contact_search"></dd></dl>
									<input class="backlist" id="back_list" style="cursor:pointer;" type="button" value="返回列表" />
									<input id="add_group" type="button" value="添加分组" name="" class="addgrp" style="cursor:pointer;">
								</div>
							</div>
						</div>

						<div class="txl-r">
							<div class="btn-grp" id="contact_actionbar"><input class="lxr-1" type="button" value="添加联系人"/> <input class="lxr-2" type="button" disabled value="编辑联系人" /> <input class="lxr-2" disabled type="button" value="删除联系人"/> <select id="group_list_0" disabled><option>移动到...</option></select>
							</div>

							<div class="txl-txt" id="addrbook_content">


								<div id="addrbook_all" style="display:none;">
									<h3 id="contact_count"><b>通讯录</b></h3>
									<table width="300" cellspacing="0" cellpadding="0" border="0" style="margin: 0pt auto;">
										<tbody>
											<tr>
												<td height="105" width="115" align="center"><img align="left" src="/images/talk.gif"></td>
												<td><p>“通讯录”是导入、存储和查看所有重要联系人信息的位置。您也可以创建自己的联系人群组，以便能更好的记住他们。</p></td>
											</tr>
										</tbody>
									</table>
									<h3><b>你现在可以：</b></h3><br>
									<div class="btn-grp" id="content_buttons">
										添加联系人&gt;&gt;  <input type="button" id="content_buttons1" value="添加联系人" name="" class="lxr-1">   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;添加群组&gt;&gt;   <input type="button" id="content_buttons2" value="添加群组" name="" class="lxr-1">  
									</div>
								</div>

								<div id="addrbook_add" style="display:none;">
									<h3><b id="contact_name">添加联系人</b></h3><br>
									<form action="#" method="post" target="addrbook_iframe" id="contact_form">
										<table width="400" cellspacing="0" cellpadding="0" border="0" class="addpp">
											<tbody>
												<tr>
													<td width="70" valign="middle" align="right">姓名： </td>
													<td valign="middle" align="left"><input type="text" id="textfield" name="name">
														<select id="group_list_1" name="gid"></select></td>
												</tr>
												<tr>
													<td width="70" valign="middle" align="right">电话：</td>
													<td valign="middle" align="left"><input type="text" id="textfield2" name="telephone"></td>
												</tr>
												<tr>
													<td width="70" valign="middle" align="right">地址：</td>
													<td valign="middle" align="left"><input type="text" id="textfield3" name="address"></td>
												</tr>
												<tr>
													<td width="70" valign="middle" align="right">QQ：</td>
													<td valign="middle" align="left"><input type="text" id="textfield4" name="qq"></td>
												</tr>
												<tr>
													<td width="70" valign="middle" align="right">电子邮件：</td>
													<td valign="middle" align="left"><input type="text" id="textfield5" name="email"></td>
												</tr>
												<tr>
													<td width="70" valign="top" align="right">生日：</td>
													<td valign="top" align="left"><select name="year"></select>年<select name="month"></select>月<select name="date"></select>日 <a href="#"><input type="hidden" name="remind" value="0"/>设置提醒</a></td>
												</tr>
												<tr id="form_more">
													<td width="70" valign="top" align="right">更多详情：</td>
													<td valign="middle" align="left">
														<select id="more_list" >
															<option>选择</option><option>昵称</option><option>网站</option><option>MSN</option><option>公司</option><option>职位</option><option>传真</option>
														</select></td>
												</tr>
												<tr>
													<td width="70" valign="top" align="right">备注：</td>
													<td valign="top" align="left"><textarea rows="5" cols="45" id="textarea" name="remarks" class="t-bz"></textarea></td>
												</tr>
											</tbody>
										</table><br /><input class="btn-n" type="submit" value="保存"/> <input class="btn-n" type="button" name="reset" value="取消" />
									</form>
								</div>
								<div id="addrbook_view" style="display:none;">
									<h3><b id="contact_name"></b></h3>
									<table class="txl-opt" width="100%" border="0" cellspacing="0" cellpadding="0" style="word-break:break-all;">
										<tbody id="content_view_tbody"></tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
				<iframe style="display: none;" name="addrbook_iframe"></iframe>
				<form action="#" method="post" id="move_contact_form" target="addrbook_iframe"><input type="hidden" name="gid"/><input type="hidden" name="id"/></form>
				<form action="#" method="post" id="delete_contact_form" target="addrbook_iframe"><input type="hidden" name="id"/></form>
				<form action="#" method="post" id="export_contact_form" target="addrbook_iframe"><input type="hidden" name="data"/></form>
				<form action="#" id="refresh_cache_form" method="post" target="addrbook_iframe"></form>
			</div>
		</div>         
	</div>
	<script src="/js/my2011.js?v=1" type="text/javascript"></script>
	<script src="/js/addrbook.js?v=2016" type="text/javascript"></script>
	<div style="position:absolute;background:#deebfc;z-index:101;top:200px; left:320px;border:1px solid #9cbde8;padding:15px 10px; text-align:left;display:none;">
		<form action="#" method="post" id="import_contact_form" target="addrbook_iframe" enctype="multipart/form-data">
			导入功能正在研发中，敬请期待！
			<input id="import_submit_btn" type="hidden" class="lxr-1" value="关闭"/><input style="margin-top:2px;" type="button" class="lxr-1" value="关闭"/></form>
	</div>

	<div id="sheild" style="position:absolute;background:#ccc;top:0px;left:0px;filter:alpha(opacity=60);opacity:0.6;z-index:100;"></div>
	<div style="position:absolute;background:#deebfc;z-index:101;top:200px; left:320px;display:none;border:1px solid #9cbde8;padding:15px 10px; text-align:left;">
		<form action="#" target="addrbook_iframe" method="post" id="group_delete_form"><input type="hidden" name="id"/><input type="hidden" name="name"/>
			请选择：<br />
			<div style="width:285px; text-align:left;"><input type="radio" name="contact" id="with_contact_0" value="0" checked/><label for="with_contact_0">删除该组，并将该组下所有联系人移至未分组。</label></div>
			<div style=" text-align:left;"><input type="radio" name="contact" id="with_contact_1" value="1"/><label for="with_contact_1">删除该组，同时删除该组下所有联系人。</label></div>
			<div style="text-align:left"><input style="margin:2px 0 0px 100px;" type="submit" class="lxr-1" value="确定"/>&nbsp;<input style="margin-top:2px;" type="button" class="lxr-1" value="取消"/></div></form>
	</div>
</div>
<script type="text/javascript" src="/js/jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="/js/dialog-min.js"></script>
<script>
    jQuery.noConflict();
    jQuery(document).ready(
        function() {
            var d = dialog({
                title: '尊敬的2345的用户',
                content: '<div class="shang-body"><p>您好。由于业务调整，[通讯录]模块预计将在2019年3月底下线，请您及时备份数据，给大家造成的不变还请谅解！</p></div>',
                quickClose: false,
                okValue: '我知道了',
                ok: function () {},
                cancelValue: '关闭',
            });
            d.showModal();
        }
    );
</script>
<!-- footer -->
{{include file="footer.tpl.html"}}