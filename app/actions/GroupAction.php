<?php
/**
 * 头像/昵称生态配置相关公用方法
 * User: yinmt
 * Date: 2020/04/27
 */
class GroupAction extends Action
{
    /**
     * 保存生态与mid映射
     * @auth yinmt
     * @param string $group 生态
     * @param string $mid 项目id
     * @return bool
     */
    public function saveGroupMidMap($group, $mid) {
        $model = loadModel('GroupMidMap');
        $result = $model->checkExist($mid);

        if ($result === null) {
            return $model->addGroupMidMap($group, $mid);
        }

        if ($result === false) {
            return false;
        }

        if ($result === $group) {
            return true;
        }

        return $model->editGroupMidMap($group, $mid);
    }

    /**
     * 添加生态生态信息至联合登陆列表中
     * @aurh yinmt
     * @param array $midList 联合登陆列表
     * @param array $groupList 生态信息列表
     * @return array
     */
    public function addGroupInfo($midList, $groupList) {
        if (empty($groupList)) {
            return $midList;
        } else {
            $groupList = array_column($groupList, 'group_name', 'mid');

            return array_map(function ($item) use ($groupList) {
                $mid = $item['mid'];
                $item['groupName'] = isset($groupList[$mid]) ? $groupList[$mid] : '';
                return $item;
            }, $midList);
        }
    }
}
