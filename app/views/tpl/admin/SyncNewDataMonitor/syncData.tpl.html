<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="GBK">
    <title></title>
    <link rel="stylesheet" media="all" type="text/css" href="/css/redmin/bootstrap/bootstrap.min.css">
    <!--<script src="/js/jquery.min.js"></script>-->
    <script src="/js/vue/vue.min.js"></script>
    <script src="/js/vue/vue-resource.min.js"></script>
    <script src="/js/jquery-1.9.1.min.js"></script>
</head>
<body>
<div class="jumbotron" id="indexForm">
    <div class="container">
        {{include file = "admin/SyncNewDataMonitor/nav.tpl.html"}}
        <div class="panel panel-default">
            <div class="panel-body">
                    <div class="panel panel-default">
                        <div class="panel-body">

                            <table  class="table table-bordered  table-hover">
                                <tr>
                                    <th style="width: 150px">当前队列</th>
                                    <th style="width: 250px;">当前passid</th>
                                    <th style="width: 60px;">当前状态</th>
                                </tr>
                                <tr  v-for="(n,index) in syncPassId">
                                    <td >${n.key}</td>
                                    <td >
                                        <span v-on:dblclick="setCurrPassId(n.key)">${n.val}</span>
                                        <span>
                                            <input type="text" v-bind:class="n.className" v-if="showInput == n.key" @blur="blurfns($event)" >
                                            <button class="btn btn-primary" v-if="showInput == n.key" v-on:click="resetCurrPassId(n.className, n.key)">重置</button>
                                        </span>
                                    </td>
                                    <td><a href="" v-on:click="openClose(n.key, n.status)">${n.statusMsg}</a></td>
                                </tr>
                            </table>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>
<script>
    var syncPassId= {{$pageArray.syncPassId}};
    var app = new Vue({
        el: "#indexForm",
        delimiters: ['${', '}'],
        data: {
            'syncPassId' : syncPassId,
            'showInput' : ""
        },
        methods: {
            openClose : function (processName, status) {
                this.$http.post("/houtai_gl/SyncNewDataMonitor/SetSyncRunStatus", {
                    'processName' : processName,
                    'status' : status
                }, {"emulateJSON": true}).then(function (res) {
                    if (res.body.status == 200){
                        alert(res.body.msg)
                        window.location.reload()
                    }else{
                        alert(res.body.msg)
                    }
                }, function () {
                    console.log('请求失败处理');
                })
            },
            setCurrPassId : function (processName, val,className) {
                this.showInput = processName;
            },
            blurfns :function (event) {
                if (event.currentTarget.value == ''){
                    this.showInput = ''
                }
            },
            resetCurrPassId : function (className, processName) {
                this.$http.post("/houtai_gl/SyncNewDataMonitor/SetCurrPassId", {
                    'processName' : processName,
                    'passid' : $('.' + className).val()
                }, {"emulateJSON": true}).then(function (res) {
                    if (res.body.code == 200){
                        alert(res.body.msg)
                        this.showInput = 0
                        window.location.reload()
                    }else{
                        alert(res.body.msg)
                    }
                }, function () {
                    console.log('请求失败处理');
                })

            }
        }
    });
</script>
</body>
</html>