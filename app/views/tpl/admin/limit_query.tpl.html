<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=GBK">
        <script type="text/javascript" src="/js/datepicker/WdatePicker.js"></script>
        <title>注册禁止IP列表</title>
        <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
        <link href="/css/v3/zhanghao.css" rel="stylesheet">
        <link rel="stylesheet" href="/css/v3/top.css">
        <script type="text/javascript" src="/js/jquery.min.js"></script>
    </head>
    <body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
        <div class="mainWrap">
    
		<!--面包屑导航 开始-->
        <div class="location">位置：<a href="#" target="_blank">查询限制信息</a>  &gt; <span>注册禁止IP列表</span></div>
        <!--面包屑导航 结束-->
        
        <div class="selectTab mt10 clearfix">
            <a href="/houtai_gl/limit_setting">设置</a>
            <a href="/houtai_gl/limit_query?type=regforbidenip" {{if $smarty.get.type == 'regforbidenip' || $smarty.get.type ==''}} class="cur" {{/if}}  >注册禁止IP列表</a> 
            <a href="/houtai_gl/limit_query?type=loginforbidenip" {{if $smarty.get.type == 'loginforbidenip'}} class="cur" {{/if}}>登录禁止IP列表</a> 
            <a href="/houtai_gl/limit_query?type=getpwdforbidenuser" {{if $smarty.get.type == 'getpwdforbidenuser'}} class="cur" {{/if}}>找回密码禁止用户列表</a> 
            <a href="/houtai_gl/limit_query?type=checkforbidenip" {{if $smarty.get.type == 'checkforbidenip'}} class="cur" {{/if}}>Check禁止IP列表</a> 
            <a href="/houtai_gl/limit_query?type=visitforbidenip" {{if $smarty.get.type == 'visitforbidenip'}} class="cur" {{/if}}>访问禁止IP列表</a>
        </div>
        
        <div class="redTxtem"><strong>{{$pageArray.msg}} </strong></div>
        
        <div class="funList mt10 clearfix">
            <div class="left clearfix">
                {{if $pageArray.type=='loginforbidenip'||$pageArray.type=='regforbidenip'}}
                <form action="" method="post" id="limitFromID">
                    <span class="left">添加禁止IP：</span>
                    <span style=" padding-left:10px;" class="input-search"><input type="text" name="ip" autocomplete="off"></span>
                    <a href="javascript:" class="styleBtnBlue left ml10" onclick="$('#limitFromID').submit();">添加</a>
                    <!--input type="submit" value="添加"/-->
                </form>
                {{/if}}
        
            </div>
        </div>
    
        <div class="contentWrap">
            <!--table数据列表 开始-->            
            <table cellspacing="0" cellpadding="0" class="tabMod forFixed mt10">
                <tbody>
                	<tr>
                        <th>值</th>
                        <th>{{if $pageArray.type==''||$pageArray.type=='regforbidenip'}}失效时间{{else}}加入时间{{/if}}</th>
                        <th>操作</th>
                    </tr>
                    {{foreach from=$pageArray.data item=vitem key=key}}
                    <tr>
                        <td>
                            {{if $pageArray.type=='loginforbidenip'}}
                            <a href="/houtai_gl/suspect/passids?ip={{$vitem.value}}" target="_blank">{{$vitem.value}}</a>
                            {{else}}
                            {{$vitem.value}}
                            {{/if}}
                        </td>
                        <td>{{$vitem.date}}</td>
                        <td><a  link="/houtai_gl/limit_query?type={{$pageArray.type}}&act=del&val={{$vitem.val}}" href="javascript:" class="limitQueryClass">删除</a></td>
                    
                    </tr>
                    {{/foreach}}
            	</tbody>
            </table>
            <!--table数据列表 结束-->        
        </div><!--contentWrap end-->
        {{if !empty($pageArray.data)}}
            {{$pageArray.listPage}}
        {{/if}}
	</div>
    <script type="text/javascript" src="/js/boxfixed.js"></script>
    <script>
        $('.navList a').each(function (){
            if (  $(this).attr('href') == '/houtai_gl/limit_query' )
            {
                $(this).addClass('select');
            }
        });
    
    $('.limitQueryClass').boxVertical({
    		boxTitle: "是否确定删除？          ",
    		boxTxt: "是否确定删除？       ",
            callbackFun:function(obj){
                var link = obj.attr('link');
                window.location = link;
            }
        });
    </script>
    </body>
</html>