<?php
/**
 * 生态与项目映射MODEL
 * User: yinmt
 * Date: 2020/04/27
 */
class GroupMidMapModel extends Model
{
    private $pdo;
    private $tableName = 'group_mid_map';

    /**
     * 初始化
     * -
     * AdminModel constructor.
     */
    public function __construct() {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_MAIN, $dbConfig[DB_PASSPORT_MAIN]);
    }

    /**
     * 获取生态列表
     * <AUTHOR>
     * @return array|bool
     */
    public function getGroups() {
        $sql = 'select distinct `group_name` from ' . $this->tableName;
        return $this->pdo->findAll($sql);
    }

    /**
     * 检查mid是否已配置生态
     * @auth yinmt
     * @param string $mid 项目id
     * @return array|bool
     */
    public function checkExist($mid) {
        $sql = 'select `group_name` from ' . $this->tableName . ' where mid=:mid';
        $params = [':mid' => $mid];

        $result = $this->pdo->findAll($sql, $params);
        return isset($result[0]) ? $result : $result[0]['group_name'];
    }

    /**
     * 添加生态与项目映射关系
     * <AUTHOR>
     * @param string $group_name 生态
     * @param string $mid 项目id
     * @return bool|int
     */
    public function addGroupMidMap($group_name, $mid) {
        $arr = [
            'group_name' => $group_name,
            'mid' => $mid,
            'created_by' => $_COOKIE['admin_user'],
        ];
        return $this->pdo->insert($this->tableName, $arr);
    }

    /**
     * 编辑生态与项目映射关系
     * <AUTHOR>
     * @param string $group 生态
     * @param string $mid 项目id
     * @return bool|int
     */
    public function editGroupMidMap($group, $mid) {
        $condition = [
            "where" => "mid = :mid",
            "params" => [
                ":mid" => $mid,
            ],
        ];

        $this->pdo->delete($this->tableName, $condition);

        if (empty($group)) {
            return true;
        }
        return $this->addGroupMidMap($group, $mid);
    }

    /**
     * 获取生态与项目映射关系列表
     * <AUTHOR>
     * @param string $mid 项目id
     * @return array|bool
     */
    public function getList($mids) {
        $sql = 'select `mid`, `group_name` from ' . $this->tableName . ' where mid in (';
        $params = [];
        foreach ($mids as $key => $value) {
            $sql .= ':mid_' . $key . ',';
            $params[':mid_' . $key] = $value;
        }

        $sql = substr($sql, 0, -1) . ')';
        return $this->pdo->findAll($sql, $params);
    }

    /**
     * 获取生态与项目映射关系列表
     * <AUTHOR>
     * @param string $mid 项目id
     * @return array|bool
     */
    public function getMids($mids) {
        $sql = 'select `mid`, `group_name` from ' . $this->tableName . ' where `group_name` in (';
        $params = [];
        foreach ($mids as $key => $value) {
            $sql .= ':group_' . $key . ',';
            $params[':group_' . $key] = $value;
        }

        $sql = substr($sql, 0, -1) . ')';
        return $this->pdo->findAll($sql, $params);
    }
}
