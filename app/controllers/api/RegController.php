<?php

class RegController extends Controller
{

    public function actionIndex()
    {
        if (!isset($_POST['isValidate']) || !$_POST['isValidate'])
        {
            //   * error:未验证通过
            die(serialize(array('400.5')));
        }
        //调用服务接口的域名是否正确
        $servClientDomain = $_POST['domain'];
        $this->filterServer($servClientDomain);
        if (!isset($_POST['pwd_strength']))
        {
            $pwdStrength = 2;
        }
        else
        {
            $pwdStrength = $_POST['pwd_strength'];
        }
        $regAction = loadAction('reg');
        $result = $regAction->reg(
            $_POST['username'],
            $_POST['password'],
            $_POST['email'],
            $pwdStrength,
            $_POST['refer'],
            $servClientDomain,
            $_POST['ip'],
            $servClientDomain
        );
        echo serialize($result);
    }

    /**
     * 手机号注册接口
     *
     * @return void
     */
    public function actionPhone()
    {
        //调用服务接口的域名是否正确
        $servClientDomain = $_POST['domain'];
        $this->filterServer($servClientDomain);
        if (!isset($_POST['pwd_strength']))
        {
            $pwdStrength = 2;
        }
        else
        {
            $pwdStrength = $_POST['pwd_strength'];
        }
        $regAction = loadAction('reg');
        /** @see RegAction::regPhone */
        $result = $regAction->regPhone(
            $_POST['phone'],
            $_POST['password'],
            $pwdStrength,
            $_POST['ip'],
            $servClientDomain,
            200,
            $servClientDomain
        );
        echo serialize($result);
    }

    /**
     * 邮箱注册接口
     */
    public function actionEmail()
    {
        //调用服务接口的域名是否正确
        $servClientDomain = $_POST['domain'];
        $this->filterServer($servClientDomain);
        if (!isset($_POST['pwd_strength']))
        {
            $pwdStrength = 2;
        }
        else
        {
            $pwdStrength = $_POST['pwd_strength'];
        }
        $regAction = loadAction('reg');
        $result = $regAction->regEmail(
            $_POST['email'],
            $_POST['password'],
            $pwdStrength,
            $_POST['ip'],
            $servClientDomain,
            $servClientDomain
        );
        echo serialize($result);
    }

    /**
     * 过滤服务端请求
     *
     * @param string $servClientDomain servClientDomain
     *
     * @return void
     */
    private function filterServer($servClientDomain)
    {
        $isUseRedis = Config::get("isUseRedis");
        $allowServDomain = Config::get("domainServerIPs");
        if (empty($servClientDomain) || !isset($allowServDomain[$servClientDomain]))
        {
            //   * error:非法域名调用
            die(serialize(array('400.0')));
        }
        if ($isUseRedis)
        {
            $redis = RedisEx::getInstance();
            if ($redis->get("regForbidenDomain:" . str_replace(".", "_", $servClientDomain)))
            {
                //   * error:非法域名调用
                die(serialize(array('400.0')));
            }
        }
        //判断调用接口的IP是否正常
        if (!\Service\Security\Server::checkServerRequestRight()) {
            die(serialize(array('400.1')));
        }
    }

}
