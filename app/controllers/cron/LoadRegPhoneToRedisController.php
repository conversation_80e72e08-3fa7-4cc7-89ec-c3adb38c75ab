<?php

includeBase('CronController');

/**
 * 加载注册手机用户数据到redis
 */
class LoadRegPhoneToRedisController extends CronController
{
    //redis数量
    private $partCount = 3;
    //总进程数
    private $totalProcess = 10;
    //执行间歇步长
    private $sleepCount = 100000;
    //间歇时间
    private $sleepTime = 2;
    //每页查询数量
    private $pageSize = 5000;
    //用户手机号码数据hash_key
    private $regPhoneHashKey = REG_PHONE_INFO_REDIS_KEY;
    //进程锁
    private $processLockHashKey = 'dlx:regPhoneInfo:processLock';
    //最大处理时间
    private $maxExecuteTime = 3600 * 12;
    //config
    private $config;

    /**
     * LoadUserDataToRedisController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->config = Config::get('redis');
    }

    /**
     * actionIndex
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        global $cronArg;
        set_time_limit(0);
        $startTime = time();

        //1.获取参数(1:进程;2:是否全部重新加载)
        $process = isset($cronArg[0]) ? intval($cronArg[0]) : 1;
        if ($process <= 0 || $process > $this->totalProcess)
        {
            echo '参数错误,停止执行';
            exit();
        }

        //2.添加进程锁
        $redis = RedisEx::getInstance(); //初始化业务数据redis
        if ($redis->hExists($this->processLockHashKey, 'process_' . $process) && $redis->hGet($this->processLockHashKey, 'process_' . $process) == 1)
        {
            echo '进程[' . $process . ']已被锁,停止执行';
            exit();
        }
        //添加进程锁
        $redis->hSet($this->processLockHashKey, 'process_' . $process, 1);

        //3.获取日志
        $logger = $this->getLogger("loadRegPhoneToRedis", "loadRegPhoneToRedis_" . $process);
        $logger->info('进程[' . $process . ']开始执行..');

        //4.查询总数,分配每个进程执行数量 (总数需要找出一个过去的ID,或者过去的某个时间点)
        $memberModel = loadModel('member');
        $endTime = '2018-02-02 20:00:00';
        $totalCount = $memberModel->getMembersPhoneCount($endTime);
        $everyProcessTotal = floor($totalCount / $this->totalProcess);

        if ($everyProcessTotal <= 0)
        {
            $redis->hDel($this->processLockHashKey, 'process_' . $process);
            $str = '当前进程分配到的数据为0,停止执行..';
            $logger->info($str);
            echo $str;
            exit();
        }

        //5.执行变量 初始化
        $loadedCount = 0; //共加载
        $successCount = 0; //成功数量
        $failedCount = 0;  //失败数量
        $tmpLoadedCount = 0;

        if ($process == $this->totalProcess)
        {
            $processCount = $totalCount - ($everyProcessTotal * ($this->totalProcess - 1));
        }
        else
        {
            $processCount = $everyProcessTotal; //当前进程需要处理的数据条数,默认是分配的所有数据
        }
        $logger->info('members_phone表一共有[' . $totalCount . ']条数据处理(last_update <' . $endTime . '), 当前进程分配到[' . $everyProcessTotal . ']条数据..');

        $memberRedis = loadAction('memberRedis');

        //7.计算出总页数
        if ($this->pageSize > $processCount)
        {
            $pageCount = 1;
            $this->pageSize = $processCount;
        }
        else
        {
            $pageCount = ceil($processCount / $this->pageSize); //总页数
        }

        if ($process == $this->totalProcess)
        {
            $offset = ($process - 1) * $everyProcessTotal;
        }
        else
        {
            $offset = ($process - 1) * $processCount; //进程处理数据的起始位置
        }

        $logger->info('总页数:['. $pageCount . '], 起始位置:[' . $offset . ']..');

        $lastPassId = 0;

        //8.分页处理
        for ($i = 0; $i < $pageCount; $i++)
        {
            $logger->info('当前处理页:[' . ($i + 1) . ']..');
            try
            {
                if (!$redis)
                {
                    $redis = RedisEx::getInstance();
                }
                if (time() - $startTime > $this->maxExecuteTime)
                {
                    $memberRedis->closeUserDataRedis();
                    $redis->hDel($this->processLockHashKey, 'process_' . $process);
                    $str = '当前进程脚本执行时间已超过最大时间限制:' . $this->maxExecuteTime . ', 停止执行';
                    $logger->info($str);
                    exit();
                }
                if ($i == ($pageCount - 1))
                {
                    $tmpPageSize = $processCount - (($pageCount - 1) * $this->pageSize);
                }
                else
                {
                    $tmpPageSize = $this->pageSize;
                }
                if ($i == 0 || $lastPassId <= 0)
                {
                    $regPhoneList = $memberModel->getRegPhoneListByPage($i, $tmpPageSize, $offset, $this->pageSize);
                }
                else
                {
                    $regPhoneList = $memberModel->getRegPhoneListByPageNew($tmpPageSize, $lastPassId);
                }

                foreach ($regPhoneList as $key => $phoneInfo)
                {
                    if (empty($phoneInfo['passId']) || empty($phoneInfo['phone']))
                    {
                        continue;
                    }
                    $lastPassId = $phoneInfo['passId'];
                    $phone = $phoneInfo['phone']; //用户信息序列化后存储
                    $saveInfo = $memberModel->getSaveInfoMemberRedis($phoneInfo['passId'], unserialize($phoneInfo['m_uid']), $phoneInfo['gid'], $phoneInfo['username']);

                    $setResult = $memberRedis->hashSet($phone, $this->regPhoneHashKey, $phone, serialize($saveInfo));
                    if ($setResult === -1)
                    {
                        $redis->hDel($this->processLockHashKey, 'process_' . $process);
                        $str = '用户数据redis链接失败, 停止执行';
                        $logger->info($str);
                        $memberRedis->closeUserDataRedis();
                        PdoEx::delInstance(DB_PASSPORT);
                        echo $str;
                        exit();
                    }
                    $tmpLoadedCount++;
                    $loadedCount++;
                    if (!$setResult)
                    {
                        $failedCount++;
                    }
                    else
                    {
                        $successCount++;
                    }
                }

                if ($tmpLoadedCount >= $this->sleepCount)
                {
                    $tmpLoadedCount = 0;
                    $logger->info('当前进程执行了[' . $loadedCount . ']条数据了,休息一下..');
                    $memberRedis->closeUserDataRedis();
                    PdoEx::delInstance(DB_PASSPORT);
                    sleep($this->sleepTime);
                }
            }
            catch (Exception $ex)
            {
                echo $ex;
                $logger->info('出现 Exception..');
                PdoEx::delInstance(DB_PASSPORT);
                $memberRedis->closeUserDataRedis();
                break;
            }
            catch (Error $er)
            {
                echo $er;
                $logger->info('出现 Error..');
                PdoEx::delInstance(DB_PASSPORT);
                $memberRedis->closeUserDataRedis();
                break;
            }
            finally
            {
                $memberRedis->closeUserDataRedis();
                PdoEx::delInstance(DB_PASSPORT);
            }
        }
        //删除进程锁
        $redis->hDel($this->processLockHashKey, 'process_' . $process);
        $finishStr = sprintf("执行结束,共执行[%s]条,成功[%s]条,失败[%s]条.", $loadedCount, $successCount, $failedCount);
        $logger->info($finishStr);
        echo $finishStr;
    }
}
