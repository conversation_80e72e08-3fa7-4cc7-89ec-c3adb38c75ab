<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：LogAction.php
 * 摘    要：记录日志类
 * 修改日期：2015.04.16
 * */
class KeyAction extends Action
{
    /**
     * 获取手机号码黑名单KEY
     * */
    public static function getBlackPhoneKey ($phone)
    {
        return Config_CacheKey::SMS_PHONE_BLACK . $phone;
    }
    
    /**
     * 获取手机号码白名单KEY 
     * */
    public static function getWritePhoneKey($phone)
    {
        return Config_CacheKey::SMS_PHONE_WHITE . $phone;
    }
    
    /**
     * 获取项目下配置类型KEY
     * */
    public static function getProjectTypeKey($pid)
    {
        return Config_CacheKey::SMS_PROJECT_TYPE . $pid;
    }
    
    /**
     * 获取IP白名单
     * */
    public static function getProjectIpWriteListKey ($ip)
    {
        return Config_CacheKey::SMS_IP_WHITE_LIST .$ip;
    }
    
    /**
     * 获取类型下的发送条数
     * */
    public static function getSendTypeNumsKey ($phone,$data,$type)
    {
        return Config_CacheKey::SMS_SEND_PHONE_TYPE_NUMS . $type . '_'. str_replace('-','_',$data) .'_' . $phone;
    }
    
    /**
     * 获取类型配置KEY
     * */
    public static function getSmsTypeConfigKey($smsType)
    {
        return Config_CacheKey::SMS_TYPE_CONFIG . $smsType;   
    }

    /**
     * 获取BI数据统计key
     * @param string $date date
     *
     * @return string
     */
    public static function getBIDataStatisticKey($date)
    {
        return Config_CacheKey::BI_DATA_STATISTIC_DAILY . ':' . $date;
    }
}


?>