# Login Service - Go Version

This is a Go language rewrite of the PHP login service (`login_for_index_0327.php`).

## Features

### Core Functions
- **logout()**: Complete PHP to Go conversion of the logout functionality
- **getForwardUrl()**: Secure URL validation and filtering for redirect parameters

### Security Features
- **Cookie management**: Clears all login-related cookies (u_sec, passid, name, uid, iden, name_ie, user_info, I)
- **URL validation**: Validates and sanitizes forward URLs to prevent security vulnerabilities
- **Domain whitelist**: Only allows redirects to trusted domains (2345.com, hao774.com, 7255.com, etc.)
- **Input sanitization**: Removes quotes and handles special characters in URLs

### Integration Features
- **SSO integration**: Calls `https://passport.2345.com/v3/user/sso/clearToken` to clear server-side sessions
- **Domain-based routing**: Handles different domains (2345, hao774, 7255, etc.) with appropriate redirects
- **Development environment support**: Special handling for development mode with domain conversion
- **HTTP client**: Built-in HTTP client for external API calls
- **Comprehensive testing**: Full unit test coverage for all functions and scenarios

## Project Structure

```
.
├── main.go                      # Main application entry point
├── logout.go                    # Direct PHP-to-Go logout function translation
├── logout_test.go              # Unit tests for logout function
├── go.mod                      # Go module definition
├── PHP_TO_GO_COMPARISON.md     # Detailed PHP vs Go comparison
├── config/
│   └── config.go              # Configuration management
├── handlers/
│   ├── logout.go              # Logout request handlers (OOP style)
│   └── logout_test.go         # Handler tests
├── utils/
│   ├── cookie.go              # Cookie management utilities
│   ├── http.go                # HTTP client utilities
│   ├── forward.go             # URL validation and forward parameter handling
│   └── forward_test.go        # Tests for forward URL functionality
├── dev/
│   └── dev.go                 # Development environment utilities
└── README.md                  # This file
```

## Features

- **Direct PHP-to-Go translation** of the original logout() function
- **Complete logout functionality** with cookie clearing and SSO integration
- **Domain-specific routing** for different 2345 network sites (hao774.com, 7255.com, etc.)
- **Development environment support** with special domain handling
- **Type-safe configuration** management with environment variables
- **Comprehensive error handling** and logging
- **Docker containerization** ready for deployment
- **Extensive unit tests** for reliable functionality
- **Line-by-line code comparison** documentation (PHP vs Go)

## Installation

1. **Install Go** (version 1.21 or later)
2. **Clone or copy the files** to your project directory
3. **Install dependencies**:
   ```bash
   go mod tidy
   ```

## Configuration

The application can be configured using environment variables:

- `PORT`: Server port (default: 8080)
- `HTTP_HOST`: Host header for domain detection
- `RUNMODE`: Run mode (`development` or `production`)
- `RUNMODEST`: Development environment tag
- `BASE_PATH`: Base path for the application

Create a `.env` file for local development:
```env
PORT=8080
HTTP_HOST=login.2345.com
RUNMODE=production
```

## Usage

### Running the Server

```bash
go run main.go
```

The server will start on the configured port (default: 8080).

### API Endpoints

#### Main Endpoint
- **URL**: `/login_for_index_0327.php`
- **Methods**: GET, POST
- **Parameters**: 
  - `action=logout`: Triggers logout functionality

#### Logout Endpoint  
- **URL**: `/logout`
- **Methods**: GET, POST
- **Parameters**:
  - `forward`: URL to redirect to after logout

### Examples

1. **Access main page**:
   ```
   http://localhost:8080/login_for_index_0327.php
   ```

2. **Logout**:
   ```
   http://localhost:8080/login_for_index_0327.php?action=logout
   ```

3. **Logout with forward URL**:
   ```
   http://localhost:8080/login_for_index_0327.php?action=logout&forward=http://www.2345.com
   ```

4. **Using getForwardUrl function**:
   ```go
   import "login-service/utils"

   func handleRequest(w http.ResponseWriter, r *http.Request) {
       forwardURL := utils.GetForwardUrl(r)
       if forwardURL != "" {
           http.Redirect(w, r, forwardURL, http.StatusFound)
       }
   }
   ```

5. **Run the demo**:
   ```bash
   go run examples/forward_url_example.go
   ```
   or
   ```
   http://localhost:8080/logout
   ```

3. **Logout with forward URL**:
   ```
   http://localhost:8080/logout?forward=https://www.example.com
   ```

## Key Differences from PHP Version

### Improvements
- **Type safety**: Go's static typing prevents many runtime errors
- **Better error handling**: Explicit error handling throughout
- **Concurrent safety**: Thread-safe singleton pattern for dev instance
- **Modern HTTP handling**: Uses Gorilla Mux for routing
- **Environment-based config**: Clean separation of configuration

### Functional Equivalents
- `mySetCookie()` → `CookieManager.SetCookie()`
- `curl_get_contents()` → `HTTPClient.Get()`
- `get_domain()` → `utils.GetDomain()`
- `dev::getInstance()` → `dev.GetInstance()`
- `$_GET['action']` → `r.URL.Query().Get("action")`
- `$_REQUEST['forward']` → `r.URL.Query().Get("forward")` or `r.FormValue("forward")`

## Development

### Building
```bash
go build -o login-service
```

### Testing
```bash
go test ./...
```

### Running with specific environment
```bash
RUNMODE=development HTTP_HOST=st1.login.2345.cn go run main.go
```

## Dependencies

- **gorilla/mux**: HTTP router and URL matcher
- **joho/godotenv**: Environment variable loading from .env files

## Notes

- The SSO integration calls `https://passport.2345.com/v3/user/sso/clearToken`
- Cookie domains are automatically detected based on the HTTP_HOST
- Development environment handling includes special domain conversion logic
- All cookie clearing follows the same pattern as the original PHP version

## Migration from PHP

To migrate from the PHP version:

1. **Update web server configuration** to proxy requests to the Go service
2. **Set appropriate environment variables** for your deployment
3. **Test logout functionality** with your existing cookie setup
4. **Verify SSO integration** works with your authentication system

The Go version maintains full compatibility with the original PHP behavior while providing better performance and maintainability.
