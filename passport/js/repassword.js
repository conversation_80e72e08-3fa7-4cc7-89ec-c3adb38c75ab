function check()
{
	if (!checkPass()) return false;
	if (!checkRepass()) return false;
	
	return true;
}

function checkPass()
{
	var pass = $("password").value;
	if (pass.length < 6)
	{
		$("msg_password").style.display='';
		$("msg_password").className = 'pwtip';
		$("msg_password").innerHTML = '最少6个字符';
		$("password").className = "ipt_01 ipt_02";
		$("safe").style.display = "none";
		return false;
	}
	if (pass.length > 16)
	{
		$("msg_password").style.display='';
		$("msg_password").className = 'pwtip';
		$("msg_password").innerHTML = '最多16个字符';
		$("password").className = "ipt_01 ipt_02";
		$("safe").style.display = "none";
		return false;
	}
	
	if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
	{
		$("msg_password").style.display='';
		$("msg_password").className = 'pwtip';
		$("msg_password").innerHTML = '您的密码过于简单，请重新输入';
		$("password").className = "ipt_01 ipt_02";
		$("safe").style.display = "none";
		return false;
	}
	
	$("msg_password").className = '';
	$("msg_password").innerHTML = '<img src="images/check.jpg" />';
	$("password").className = "ipt_01";
	return true;
}

function checkRepass()
{
	var pass = $("repassword").value;
	if (pass.length < 6)
	{
		$("msg_repassword").style.display='';
		$("msg_repassword").className = 'pwtip';
		$("msg_repassword").innerHTML = '最少6个字符';
		$("repassword").className = "ipt_01 ipt_02";
		return false;
	}
	if (pass.length > 16)
	{
		$("msg_repassword").style.display='';
		$("msg_repassword").className = 'pwtip';
		$("msg_repassword").innerHTML = '最多16个字符';
		$("repassword").className = "ipt_01 ipt_02";
		return false;
	}
	
	if ($("repassword").value != $("password").value)
	{
		$("msg_repassword").style.display='';
		$("msg_repassword").className = 'pwtip';
		$("msg_repassword").innerHTML = '两次输入密码不一致';
		$("repassword").className = "ipt_01 ipt_02";
		return false;
	}
	$("msg_repassword").className = '';
	$("msg_repassword").innerHTML = '<img src="images/check.jpg" />';
	$("repassword").className = "ipt_01";
	return true;
}

function checkPassSame(pass)
{
	var first = pass.substring(0,1);
	var exp = new RegExp('^'+first+'+$');
	if(exp.test(pass))
	{
		return false;
	}
	
	if (first == 'a' || first == 'A')
	{
		f = pass.charCodeAt(0);
		for(i = 1; i < pass.length; i++)
		{
			tmp = pass.charCodeAt(i);
			if (tmp - f != i)
			{
				return true;
			}
		}
		return false;
	}
	return true;
}

function safe()
{
	var pass = $("password").value;
	if (pass.length < 6)
	{
		var score = 0;
	}
	else if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
	{
		var score = 0;
	}
	else
	{
		var score = passwordGrade(pass);
	}
//	$('safe').innerHTML = score;
//	return;
	if (score <= 10)
	{
		$('w1').style.display = '';
		$('w2').style.display = 'none';
		$('w3').style.display = 'none';
		$('w4').style.display = 'none';
		$('pwd_strength').value = 1;
	}
	else if (score >= 11 && score <= 20)
	{
		$('w1').style.display = 'none';
		$('w2').style.display = '';
		$('w3').style.display = 'none';
		$('w4').style.display = 'none';
		$('pwd_strength').value = 2;
	}
	else if (score >= 21 && score <= 30)
	{
		$('w1').style.display = 'none';
		$('w2').style.display = 'none';
		$('w3').style.display = '';
		$('w4').style.display = 'none';
		$('pwd_strength').value = 3;
	}
	else
	{
		$('w1').style.display = 'none';
		$('w2').style.display = 'none';
		$('w3').style.display = 'none';
		$('w4').style.display = '';
		$('pwd_strength').value = 4;
	}
}

function passwordGrade(pwd) {
    var score = 0;
    var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
    var repeatCount = 0;
    var prevChar = '';

    //check length
    var len = pwd.length;
    score += len > 18 ? 18 : len;

    //check type
    for (var i = 0, num = regexArr.length; i < num; i++) { if (eval('/' + regexArr[i] + '/').test(pwd)) score += 4; }

    //bonus point
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2) score += 2;
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5) score += 2;
    }

    //deduction
    for (var i = 0, num = pwd.length; i < num; i++) {
        if (pwd.charAt(i) == prevChar) repeatCount++;
        else prevChar = pwd.charAt(i);
    }
    score -= repeatCount * 1;

    return score;
}