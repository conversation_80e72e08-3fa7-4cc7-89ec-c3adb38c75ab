<?php

use Octopus\PdoEx;

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:NicknameEditController.php
 * 摘    要:
 * 作    者:<EMAIL>
 * 修改日期: 2015/11/25
 */
includeBase('AdminController');

class NicknameEditController extends AdminController
{

    protected $authName = '昵称查询与编辑';

    /**
     * 昵称修改页面展示
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $passid = intval($_GET['passid']);
        $memberModel = loadModel('member');
        $arr = $memberModel->getNicknameByPassid($passid);
        $newArr['info'] = $arr;
        $newArr['info']['passid'] = $passid;
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']))
        {
            loadView('admin/nickname_edit.tpl.html', $newArr);
        }
        else
        {
            $newArr['info']['nickname'] = iconv('gbk','utf-8',$newArr['info']['nickname']);
            echo json_encode(array('data'=>$newArr));
        }
    }

    /**
     * 昵称后台修改
     * <AUTHOR>
     */
    public function actionEdit()
    {
        if ($_POST)
        {
            $logs = array();
            if (empty($_POST['newnickname']) || empty($_POST['passid']))
            {
                echo json_encode(array("code" => "300.1", "msg" => iconv("GBK", "UTF-8//IGNORE", "新昵称不能为空")));
                return;
            }
            else
            {
                $logs['newnickname'] = '修改了昵称';
                $nickname = iconv("UTF-8", "GBK", $_POST["newnickname"]);
            }
            $passid = intval($_POST['passid']);
            $ip = get_client_ip();
            $time = date('Y-m-d H:i:s');
            $regex = Config::get('regex');
            if (mb_strlen($nickname, "GBK") < 2)
            {
                echo json_encode(array("code" => "300.2", "msg" => iconv("GBK", "UTF-8//IGNORE", "昵称不能少于2个字符")));
                return;
            }
            if (strlen($nickname) > 16)
            {
                echo json_encode(array("code" => "300.3", "msg" => iconv("GBK", "UTF-8//IGNORE", "昵称不能超过16个字符")));
                return;
            }
            if (preg_match($regex['username'], $nickname) || preg_match($regex['phone'], $nickname) || filter_var($nickname, FILTER_VALIDATE_EMAIL))
            {
                echo json_encode(array("code" => "300.4", "msg" => iconv("GBK", "UTF-8//IGNORE", "昵称不能是手机号码或邮箱")));
                return;
            }
            $validator = new \Common\Validator\Validator();
            $validator->addValidator($nickname, \Common\Validator\Validator::BAD_WORDS, array("昵称"));
            list($badWordsStatus, $msg) = $validator->validate();
            if ($badWordsStatus != \Common\Msg\ResponseMsg::SUCCESS) {
                echo json_encode(array("code" => "300.5", "msg" => iconv("GBK", "UTF-8//IGNORE", "昵称不能包括敏感词汇")));

                return;
            }

            $memberModel = loadModel('member');
            if ($memberModel->checkNickname($nickname))
            {
                echo json_encode(array("code" => "300.5", "msg" => iconv("GBK", "UTF-8//IGNORE", "昵称已被使用")));
                return;
            }
            $memberModel = loadModel('member');
            $dbConfig = Config::get("database");
            $pdo = PdoEx::getInstance(DB_PASSPORT_USER, $dbConfig[DB_PASSPORT_USER]);
            $ret = $pdo->update("members_nickname", array("nickname" => $nickname, 'addtime' => $time, 'ip' => $ip), array("where" => "passid = :passid", "params" => array(":passid" => $passid)));
            $uid = $memberModel->getUid($passid);
            noticeToChange($passid, 'changeNickname', array(
                'passid' => $passid,
                'uid' => $uid,
                'type' => 'nickname',
                'value' => $nickname,
                'addtime' => $time,
                'ip' => $ip,
            ));
            if (sizeof($logs))
            {
                $logData = array(
                    'passid' => $passid,
                    'log' => implode(',', $logs),
                    'op_user' => $_COOKIE['admin_user'],
                    'op_time' => date('Y-m-d H:i:s')
                );
                $logModel = loadModel('Log');
                $logModel->addAdminLog($logData);
            }
            echo json_encode(array("code" => "200.0", "msg" => iconv("GBK", "UTF-8//IGNORE", "昵称修改成功")));
            return;
        }
    }
}