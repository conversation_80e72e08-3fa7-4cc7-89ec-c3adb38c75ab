<?php

namespace Admin;
use Controller;
class TagListController extends Controller
{
    public function actionIndex()
    {
        $pageArray= array();
        $pageArray['mid'] = $this->Filter->getPost('mid');

        $objOAProductAction = \OAProductAction::getInstance();
        $pageArray['allProjectProduct'] = $objOAProductAction->getAllProjectProduct();

        $TagListModel = TagListModel::getInstance();
        $queryData = array(
            'mid' => $pageArray['mid']
        );
        
        $tagList = $TagListModel->getTagList($queryData);
        foreach ($tagList as &$arrTagInfo) {
            $intOAProductId = $arrTagInfo['oa_product_id'] ?? 0;
            $arrOAProject = $objOAProductAction->getProjectByProduct($intOAProductId);
            $arrTagInfo['oa_project_id'] = $arrOAProject['project_id'] ?? 0;
            $arrTagInfo['oa_project_name'] = $arrOAProject['project_name'] ?? '未知项目';
            $arrTagInfo['oa_product_name'] = $arrOAProject['product_list'][$intOAProductId]['product_name'] ?? '未知产品';
        }
        
        $pageArray['tagList'] = $tagList;
        loadView('Admin/tag_list_index.tpl.html', $pageArray);
    }
    
    
    public function actionSetTagInfo()
    {
        $mid = $this->Filter->getPost('mid');
        $tagName = $this->Filter->getPost('tag_name');
        $intOaProductId = $this->Filter->getPost('oa_product_id');
        if ( empty($mid) || empty($tagName) )
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => 'mid或者mid描述不能为空' ) );
        }
        $tagName = \EncodingAction::transcoding($tagName);
        $TagListModel = TagListModel::getInstance();
        $data = array(
            'mid' => $mid,
            'tagName' => $tagName,
            'oa_product_id' => $intOaProductId,
        );
        $isTrue = $TagListModel->setTagInfo($data);
        if($isTrue === false )
        {
            return \MsgAction::returnJsonData(array('status' => 'P00003', 'msg' => '设置失败' ));
        }
        else
        {
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '设置成功' ));
        }
        
        
    }
    
    public function actionGetTagInfo()
    {
        $id = (int)$this->Filter->getPost('id');
        if ( empty($id) )
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => 'ID不能为空' ) );
        }

        $TagListModel = TagListModel::getInstance();
        $tagInfo = $TagListModel->getTagList(array('id' => $id, 'row' => true));

        $intOAProductId = $tagInfo['oa_product_id'] ?? 0;
        $arrOAProject = \OAProductAction::getInstance()->getProjectByProduct($intOAProductId);
        $tagInfo['oa_project_id'] = $arrOAProject['project_id'] ?? 0;

        return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => $tagInfo ));
    }
}