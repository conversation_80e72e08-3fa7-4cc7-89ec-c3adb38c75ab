<?php

use Service\Encryption\Sign\SignManager;
use Service\Encryption\Aes\AesManager;

includeBase('NController');

class NPasswordController extends NController
{
    /**
     * 修改密码 2个条件修改密码
     * 1、登录状态下
     * 2、必须绑定了手机号码
     * 满足以上条件才能修改密码
     * /clientapi/password
     * @return void
     */
    public function actionIndex()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        $necessaryField = self::getNeedField(['password', 'mid', SignManager::$paramSignName,]);
        list($password) = $this->getParamAndAutoDeny($necessaryField);
        $password = (new AesManager())->aes128cbcHexDecrypt($password);
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo) {
            $this->codeResponseNew(401, "", '身份验证失效，请重新登录！', 2000012);
        }
        $passid = intval($authInfo['i']);
        //   * error:密码最少6个字符
        if (strlen($password) < 6) {
            $this->codeResponseNew(300, "", '密码最少6个字符！', 1100001);
        }
        //   * error:密码最多16个字符
        if (strlen($password) > 16) {
            $this->codeResponseNew(300, "", '密码最多16个字符！', 1100002);
        }
        $memberModel = loadModel("member");
        $userinfo = $memberModel->read($passid);
        if (empty($userinfo['phone'])) {
            $this->codeResponseNew(402, "", '未绑定手机！', 2100062);
        }

        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        if ($memberModel->updatePassword($passid, $password, $pwdStrength, get_client_ip())) {

            //日志行为打点:用户信息修改; type:USER_INFO_MODIFY; sub_type:PASSWORD;
            $userinfo['passid'] = $passid;
            $objLogV2Action = loadAction('logV2');
            $objLogV2Action->report('ALL', 'USER_INFO_MODIFY', 'PASSWORD', $userinfo, '', '');

            $uid = intval($authInfo['u']);
            $username = $authInfo['n'];
            $userMod = isset($authInfo['m']) ? $authInfo['m'] : 0;
            $nickname = isset($authInfo['k']) ? $authInfo['k'] : '';
            $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod, '', '', $nickname);
            $this->codeResponseNew(200, [
                'source' => encryptCookieIByAes128cbc($cookie["I"]),
            ], '修改成功！', 2000000);
        } else {
            $this->codeResponseNew(400, "", '修改失败！', 2100063);
        }
    }
}
