<?php
/**
 * Copyright (c) 2019,上海二三四五网络科技有限公司
 * 文件名称：Amqp.php
 * 摘    要：MQ服务操作类
 * 作    者：王文兵
 * 修改日期：2019.08.31
 */

namespace Service\Amqp;

use Exception;

class Amqp
{
    private static $objInstance = null; // 连接单例

    /* 默认配置 */
    private $arrConfig = [
        'host' => '***********',
        'port' => 5672,
        'login' => 'pingtai',
        'password' => 'pingtai',
        'vhost' => 'userlevel',
        'read_timeout' => 1, // 如果是以监听方式处理，此处设为0，否则会超时报错
        'write_timeout' => 1,
        'connect_timeout' => 1,
    ];

    private $objConn = null; // 连接
    private $objChannel = null; // 通道
    private $arrExchange = []; // 交换机数组
    private $arrQueue = []; // 队列数组

    /**
     * 获取单例
     * @param array $arrConfig
     * @return self
     * @throws Exception
     */
    public static function getInstance(array $arrConfig = [])
    {
        if (is_null(self::$objInstance)) {
            self::$objInstance = new self($arrConfig);
        }
        return self::$objInstance;
    }

    /**
     * 关闭连接
     * @return bool
     */
    public function close()
    {
        $this->objChannel && $this->objChannel->close();
        $this->objConn && $this->objConn->disconnect();
        self::$objInstance = null;
        return true;
    }

    /**
     * 构造函数
     * @param array $arrConfig
     * @throws Exception
     */
    private function __construct(array $arrConfig = [])
    {
        if (class_exists('AMQPConnection')) {
            $this->objConn = new \AMQPConnection($arrConfig ?: $this->arrConfig);
            if ($this->tryConnect()) {
                $this->objChannel = new \AMQPChannel($this->objConn);
                if (!$this->objChannel->isConnected()) {
                    throw new Exception('can not connect to amqp channel');
                }
            } else {
                throw new Exception('can not connect to amqp server');
            }
        } else {
            throw new Exception('class "AMQPConnection" not exists');
        }
    }

    /**
     * 连接重试
     * @param int $tryNum 连接重试次数
     *
     * @return bool
     * @throws Exception
     */
    private function tryConnect($tryNum = 3)
    {
        while ($tryNum > 0) {
            try {
                return $this->objConn->connect();
            } catch (Exception $e) {
                if (--$tryNum > 0) {
                    continue;
                }
                throw new Exception($e->getMessage(), $e->getCode());
            }
        }

        return false;
    }

    /**
     * 设置Exch
     * @param string $strExchangeName 交换器名称
     * @param string $strType AMQP_EX_TYPE_DIRECT（直接投递）、AMQP_EX_TYPE_FANOUT、AMQP_EX_TYPE_HEADERS、AMQP_EX_TYPE_TOPIC
     * @param int $intFlag AMQP_NOPARAM、AMQP_DURABLE（持久化）、AMQP_PASSIVE、AMQP_AUTODELETE，可以多项并用
     * @return boolean
     */
    private function setExchange(
        string $strExchangeName,
        string $strType = AMQP_EX_TYPE_DIRECT,
        int $intFlag = AMQP_DURABLE
    )
    {
        $blnRe = false;
        if ($this->objChannel) {
            if (!isset($this->arrExchange[$strExchangeName])) {
                $this->arrExchange[$strExchangeName] = new \AMQPExchange($this->objChannel);
                $this->arrExchange[$strExchangeName]->setName($strExchangeName);
                $this->arrExchange[$strExchangeName]->setType($strType);
                $this->arrExchange[$strExchangeName]->setFlags($intFlag);
                $this->arrExchange[$strExchangeName]->declareExchange();
            }
            $blnRe = true;
        }
        return $blnRe;
    }

    /**
     * 设置队列，并绑定交换机
     * @param string $strQueueName 队列名
     * @param string $strBindRouteKey 为空时，与队列名一致
     * @param string $strExchangeName 交换器名称
     * @param string $strUnbindRouteKey 解除绑定的Routekey
     * @param int $intFlag AMQP_DURABLE（持久化）、AMQP_PASSIVE、AMQP_EXCLUSIVE、AMQP_AUTODELETE，可以多项并用
     * @return boolean
     */
    private function setQueue(
        string $strQueueName,
        string $strBindRouteKey = '',
        string $strExchangeName = '',
        string $strUnbindRouteKey = '',
        int $intFlag = AMQP_DURABLE
    )
    {
        $blnRe = false;
        if ($this->objChannel) {
            if (!isset($this->arrQueue[$strQueueName])) {
                $this->arrQueue[$strQueueName] = new \AMQPQueue($this->objChannel);
                $this->arrQueue[$strQueueName]->setName($strQueueName);
                $this->arrQueue[$strQueueName]->setFlags($intFlag);
                $this->arrQueue[$strQueueName]->declareQueue();
                strlen($strBindRouteKey) || $strBindRouteKey = $strQueueName;
                $this->arrQueue[$strQueueName]->bind($strExchangeName, $strBindRouteKey);
            }
            strlen($strUnbindRouteKey) && $this->arrQueue[$strQueueName]->unbind($strExchangeName, $strUnbindRouteKey);
            $blnRe = true;
        }
        return $blnRe;
    }

    /**
     * 取队列中未处理的消息个数
     * @param string $strQueueName
     * @param int $intFlag
     * @return false or int
     */
    public function getQueueLen(string $strQueueName, int $intFlag = AMQP_DURABLE)
    {
        $intRe = false;
        if ($this->objChannel) {
            if (!isset($this->arrQueue[$strQueueName])) {
                $this->arrQueue[$strQueueName] = new \AMQPQueue($this->objChannel);
                $this->arrQueue[$strQueueName]->setName($strQueueName);
                $this->arrQueue[$strQueueName]->setFlags($intFlag);
            }
            $intRe = $this->arrQueue[$strQueueName]->declareQueue();
        }
        return $intRe;
    }

    /**
     * 生产者发布一条消息
     * @param string $strMsg 消息体
     * @param string $strRouteKey 交换器投递的路由
     * @param string $strExchangeName 交换器
     * @param string $strType 交换器类型
     * @param array $arrAttr 'delivery_mode' => 2 表示消息持久化
     * @param int $intFlag AMQP_NOPARAM、AMQP_MANDATORY、AMQP_IMMEDIATE，可以多项并用
     * @return bool 投递是否成功
     */
    public function putMsg(
        string $strMsg,
        string $strRouteKey,
        string $strExchangeName,
        string $strType = AMQP_EX_TYPE_DIRECT,
        array $arrAttr = [],
        int $intFlag = AMQP_NOPARAM
    )
    {
        $blnRe = false;
        if (strlen($strMsg)) {
            if ($this->setExchange($strExchangeName, $strType)
                && $this->setQueue($strRouteKey, $strRouteKey, $strExchangeName)) {
                $arrAttr || $arrAttr = ['delivery_mode' => 2, 'content_type' => 'application/json'];
                $blnRe = $this->arrExchange[$strExchangeName]->publish($strMsg, $strRouteKey, $intFlag, $arrAttr);
            }
        }
        return $blnRe;
    }

    /**
     * 从队列中获取一条消息
     * @param string $strQueueName
     * @param string $strRouteKey
     * @param string $strExchangeName
     * @return array $arrRe
     */
    public function getMsg(
        string $strQueueName,
        string $strRouteKey = '',
        string $strExchangeName = ''
    )
    {
        $arrRe = [];
        if ($this->setQueue($strQueueName, $strRouteKey, $strExchangeName)) {
            $objAMQPEnvelope = $this->arrQueue[$strQueueName]->get(); // 不要传参AMQP_AUTOACK
            if ($objAMQPEnvelope) {
                $arrRe = [
                    'id' => $objAMQPEnvelope->getDeliveryTag(),
                    'msg' => $objAMQPEnvelope->getBody(),
                ];
            }
        }
        return $arrRe;
    }

    /**
     * 确认消息已处理
     * @param string $strQueueName
     * @param int $intMsgId
     * @param string $strRouteKey
     * @param string $strExchangeName
     * @param int $intFlag AMQP_NOPARAM，AMQP_MULTIPLE
     * @return bool $blnRe
     */
    public function ackMsg(
        string $strQueueName,
        int $intMsgId,
        string $strRouteKey = '',
        string $strExchangeName = '',
        int $intFlag = AMQP_NOPARAM
    )
    {
        $blnRe = false;
        if ($this->setQueue($strQueueName, $strRouteKey, $strExchangeName)) {
            $blnRe = $this->arrQueue[$strQueueName]->ack($intMsgId, $intFlag);
        }
        return $blnRe;
    }

    /**
     * 回调方式消费队列（慎用）
     * @param string $strQueueName
     * @param callable $callable 回调
     * @param string $strRouteKey
     * @param string $strExchangeName
     * @param int $intFlag
     */
    public function consumeMsg(
        string $strQueueName,
        callable $callable,
        string $strRouteKey = '',
        string $strExchangeName = '',
        int $intFlag = AMQP_NOPARAM
    )
    {
        if ($this->setQueue($strQueueName, $strRouteKey, $strExchangeName)) {
            $this->arrQueue[$strQueueName]->consume($callable, $intFlag);
        }
    }
}
