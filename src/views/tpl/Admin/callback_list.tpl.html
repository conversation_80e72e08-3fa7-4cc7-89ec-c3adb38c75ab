<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台-权限列表</title>
    <link rel="stylesheet" href="/style/css/global.css">
</head>

<body>
<div class="mainWrap">
    <!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a> > <span>服务商配置</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a href="/Admin/OperatorConfig/Index">发送账号配置</a>
        <a class="currTabMenu" href="/Admin/Callback/Index">回执账号配置</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->

    <div class="funList mt20 clearfix">
        <a class="styleBtn_s styleBtnBlue left" href="javascript:" id="addAuthInfoId">添加</a>
    </div>

    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>ID</th>
                        <th>说明</th>
                        <th>状态</th>
                        <th>参数1</th>
                        <th>参数2</th>
                        <th>数据</th>
                        <th>频率等级</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->

        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th>ID</th>
                <th>说明</th>
                <th>状态</th>
                <th>参数1</th>
                <th>参数2</th>
                <th>数据</th>
                <th>频率等级</th>
                <th>更新时间</th>
                <th>操作</th>
            </tr>
            {{foreach from=$pageArray.list item=listItem}}
            <tr {{if $listItem.status> 0}}class="lightYellowGreenBg"{{/if}}>
                <td>{{$listItem.id}}</td>
                <td>{{$listItem.name}}</td>
                <td>{{if $listItem.status == 0}}<a class="enableConfigClass" authId="{{$listItem.id}}">关</a>{{elseif $listItem.status == 1}}<a class="disableConfigClass" authId="{{$listItem.id}}">开</a>{{elseif $listItem.status == 2}}测试{{/if}}</td>
                <td>{{$listItem.class}}</td>
                <td>{{$listItem.method}}</td>
                <td>{{$listItem.data}}</td>
                <td>{{$listItem.freq}}</td>
                <td>{{$listItem.timeStamp}}</td>
                <td><a class="editConfigInfoClass" authId="{{$listItem.id}}">修改</a> | <a authId="{{$listItem.id}}" class="delConfigInfoClass">删除</a></td>
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->


    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->


<!--透明遮罩-->
<div class="boxMask" style="display: none;">透明遮罩层</div>

<!--通道信息 弹窗 开始-->
<div class="boxMod w520" style="display:none;">
    <div class="boxTop"><h2 class="boxTit divSetAuth">添加权限</h2><a class="BoxClose" href="javascript:">关闭按钮</a></div>
    <div class="tabFrom mtb20">
        <form id="configFromId">
            <table class="tabFromMod" cellpadding="0" cellspacing="0">
                <tr>
                    <td class="tabLeftTit">说明：</td>
                    <td><input class="tabInput" type="text" autocomplete="off" name="config[name]"/></td>
                </tr>
                <tr>
                    <td class="tabLeftTit">参数1：</td>
                    <td><input class="tabInput" type="text" autocomplete="off" name="config[class]"/></td>
                </tr>
                <tr>
                    <td class="tabLeftTit">参数2：</td>
                    <td><input class="tabInput" type="text" autocomplete="off" name="config[method]"/></td>
                </tr>
                <tr>
                    <td class="tabLeftTit">账号数据：</td>
                    <td><textarea style="width: 90%;padding-left: 5px;border: 1px solid #d9d9d9;" rows="5" name="config[data]"></textarea></td>
                </tr>
                <tr>
                    <td class="tabLeftTit">频率等级：<br/>0-自动，1~4递增</td>
                    <td><input class="tabInput" type="text" autocomplete="off" name="config[freq]"/></td>
                </tr>
                <tr>
                    <input type="hidden" name="config[id]" value="0"/>
                    <td colspan="2" align="center"><a class="styleBtnBlue tabsave" id="submitconfigFromId" href="javascript:">确定</a></td>
                </tr>
            </table>


        </form>
    </div><!--tabFrom end-->
</div><!--boxMod end-->
<!--通道信息 弹窗 结束-->

</body>
</html>
<script src="/style/js/jquery-1.8.3.min.js"></script>
<script src="/style/js/tableFixed.js"></script>
<script src="/style/js/boxLocation.js"></script>
<script>
    $('#addAuthInfoId').click(function () {
        $('.boxMask').show();
        $('.boxMod').show();
        $('#configFromId')[0].reset();
        cleanBox();
        $('.divSetAuth').html('新增回执账号');
    });
    $('.BoxClose').click(function () {
        $('.boxMask').hide();
        $('.boxMod').hide();
    });

    $('#submitconfigFromId').click(function () {
        $.post('/Admin/Callback/SetConfigItem', $('#configFromId').serialize(), function (data) {
            if (data.status === 'P00001') {
                window.location = '/Admin/Callback/Index';
            }
            else {
                alert(data.msg);
            }
        });
    });

    function cleanBox() {
        $('input[name="config[name]"]').val('');
        $('input[name="config[class]"]').val("\\Smschannel\\VirtualSmsChannelAction");
        $('input[name="config[method]"]').val('callbackStatusConfig');
        $('textarea[name="config[data]"]').html('{"api_url":"","username":"","password":""}');
        $('input[name="config[freq]"]').val('0');
        $('input[name="config[id]"]').val('0');
    }

    $('.editConfigInfoClass').click(function () {
        $('.boxMask').show();
        $('.boxMod').show();
        $('.divSetAuth').html('编辑回执账号-加载中...');
        cleanBox();
        var id = $(this).attr('authId');
        $.post('/Admin/Callback/GetConfigItem', 'id=' + id, function (data) {
            $('.divSetAuth').html('编辑回执账号');
            $('input[name="config[name]"]').val(data.msg.name);
            $('input[name="config[class]"]').val(data.msg.class);
            $('input[name="config[method]"]').val(data.msg.method);
            $('textarea[name="config[data]"]').html(data.msg.data);
            $('input[name="config[freq]"]').val(data.msg.freq);
            $('input[name="config[id]"]').val(data.msg.id);
        });
    });

    $('.delConfigInfoClass').click(function () {
        if (confirm('确认删除')) {
            var id = $(this).attr('authId');
            if (id === '') {
                alert('ID不能为空');
                return false;
            }
            $.post('/Admin/Callback/DelConfigItem', 'id=' + id, function (data) {
                if (data.status === 'P00001') {
                    window.location = '/Admin/Callback/Index';
                }
                else {
                    alert(data.msg);
                }
            });
        }
    });

    $('.enableConfigClass').click(function () {
        if (confirm('确认启用？')) {
            var id = $(this).attr('authId');
            $.post('/Admin/Callback/SetConfigItem', 'config[id]=' + id + '&config[status]=1', function (data) {
                if (data.status === 'P00001') {
                    window.location = '/Admin/Callback/Index';
                }
                else {
                    alert(data.msg);
                }
            });
        }
    });

    $('.disableConfigClass').click(function () {
        if (confirm('确认停用？')) {
            var id = $(this).attr('authId');
            $.post('/Admin/Callback/SetConfigItem', 'config[id]=' + id + '&config[status]=0', function (data) {
                if (data.status === 'P00001') {
                    window.location = '/Admin/Callback/Index';
                }
                else {
                    alert(data.msg);
                }
            });
        }
    });
</script>
