{{include file="header.tpl.html"}}
<div class="wrap">
    <div class="box clearfix">
        <div class="tit clearfix">
            <span class="titleft"></span>
            <span class="titright"></span>
            <h2>我的帐号</h2></div>
        <div class="boxbd clearfix">
            <div class="info_side">
                <span class="face">
                    <img src="/{{$pageArray.userimg}}" onerror="this.src='../pic/photo.jpg'"/>
                    <p><a href="/member/avatar.php">修改头像</a></p>
                </span>
            </div> 
            <form id="my" action="" method="post" onsubmit="return checkAll();">
                <script type="text/javascript">
                        var mcn_v = '{{$pageArray.info.area}}';
                        var mcl_v = mcn_v.substring(0, 2) + '0000';
                        var mcm_v = mcn_v.substring(0, 4) + '00';                </script>
                <div class="info_cnt">
                    <h4 class="bt">帐号信息{{if $pageArray.NeedSetPwd}} <a href="edit_user.php?act=setpwd">完善2345帐号资料</a>{{/if}}</h4>
                    {{if $pageArray.OAuthType}}
                    <p style="margin:20px 0 20px 39px;"><a href="edit_user.php">完善帐号信息</a>后，便可生成您的2345帐号</p>
                    {{else}}
                    <table class="infotable" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <th>2345帐号：</th>
                            <td width="160"> {{$pageArray.username}}</td>
                            <td width="80"><a href="edit_password.php">修改密码</a></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>绑定邮箱：</th>
                            <td id="email_info">
                                {{if $pageArray.info.email_status eq 0}}
                                <input type="text" name="email" id="email" width="180" value="{{$pageArray.info.email}}" onblur="checkEmail();" />
                                <input type="hidden" name="email_old" id="email_old" width="180" value="{{$pageArray.info.email}}" />
                                {{else}}
                                {{$pageArray.info.email}}
                                {{/if}}
                            </td>
                            <td id="mtd">
                                {{if $pageArray.info.email_status eq 0}} 
                                <a href="javascript:;" onclick="validate();">验证邮箱</a>

                                <span id="email_ok" style="display:none"><img src="../images/check.jpg" style="vertical-align:middle;" /></span>
                                {{else}}
                                <a href="modify_email.php">修改邮箱</a>
                                &nbsp;<span id="cemail"></span>
                                {{/if}}                
                            </td>
                            <td><span id="email_msg" style="display:none; color:#e92828;">您输入的邮箱格式不正确</span><span id="send" class="fgray">可通过邮箱找回忘记的密码</span></td>
                        </tr>
                        {{if $smarty.const.JIFEN_AD_STATE}}
                        <tr>
                            <th>绑定手机：</th>
                            <td>{{if $pageArray.isBindPhone}}{{$pageArray.displayPhone}}{{else}}未绑定{{/if}}</td>
                            <td>
                                {{if $pageArray.isBindPhone}}
                                <a href="http://jifen.yl234.com/user/account.php?act=choose_entrance" target="_blank">更换手机</a>
                                {{else}}
                                <a href="http://jifen.yl234.com/user/account.php?act=bind_phone" target="_blank">绑定手机</a>
                                {{/if}}
                            </td>
                            <td><span class="fgray">绑定手机，帐户更安全</span></td>
                        </tr>
                        {{/if}}
                    </table>
                    {{/if}}
                    <div class="bind">
                        <b>帐号绑定：</b>
                        <p>您可以使用2345帐号或者以下已绑定帐号访问2345网址导航、浏览器等产品。</p>
                        {{if $pageArray.OAuthMsg == 'weibobind'}}<span class="bind_tip">此微博帐号已经同其他2345帐号绑定，请更换绑定的微博帐号。</span>{{/if}}
                        {{if $pageArray.OAuthMsg == 'qqbind'}}<span class="bind_tip">此QQ帐号已经同其他2345帐号绑定，请更换绑定的QQ帐号。</span>{{/if}}
                        {{if $pageArray.OAuthMsg == 'taobaobind'}}<span class="bind_tip">此淘宝帐号已经同其他2345帐号绑定，请更换绑定的淘宝帐号。</span>{{/if}}
                        {{if $pageArray.info.email_status != 0}}
                        <span id="unbind_email_msg" class="bind_tip" style="display: none;"></span>
                        <script type="text/javascript">
                        var emailVal = '{{$pageArray.info.email}}';
                        var emailExp = emailVal.split('@');
                        if(emailExp[1] == 'gmail.com'){
                            var emailHost = 'http://www.' + emailExp[1];
                        }else{
                            var emailHost = 'http://mail.' + emailExp[1];
                        }
                        document.getElementById('unbind_email_msg').innerHTML = '解绑邮件已发送到您的邮箱，访问您的邮箱完成解绑，<a href="' + emailHost + '" target="_blank">去邮箱收件</a>';
                        function show_unbind_email_msg(){
                        document.getElementById('unbind_email_msg').style.display = '';
                        }
                        </script>
                        {{/if}}
                        <div class="bindbox clearfix">
                            {{if $pageArray.OAuthType == 'weibo'}}
                            <span><img src="../pic/blogsina.png"/>已经绑定微博帐号{{if $pageArray.OAuthBinds.weibo.nickname}}"{{$pageArray.OAuthBinds.weibo.nickname|cutSubStr:"12":"..."}}"{{/if}}</span>
                            {{elseif $pageArray.OAuthType == 'qq'}}
                            <span><img src="../pic/qqlogo.png"/>已经绑定腾讯QQ帐号{{if $pageArray.OAuthBinds.qq.nickname}}"{{$pageArray.OAuthBinds.qq.nickname|cutSubStr:"12":"..."}}"{{/if}}</span>
                            {{else}}
                            {{if $pageArray.OAuthBinds.weibo}}
                            <span><img src="../pic/blogsina.png"/>已经绑定微博帐号{{if $pageArray.OAuthBinds.weibo.nickname}}"{{$pageArray.OAuthBinds.weibo.nickname}}"{{/if}}<a href="#" {{if $pageArray.info.email_status != 0}}onclick="loadJs('http://login.2345.com/oauth/bind/weibo?act=request_unbind',function(){show_unbind_email_msg();});return false;"{{else}}onclick="alert('请先验证您的邮箱。');return false;"{{/if}} class="btn_jiechu">解除绑定</a></span>
                            {{else}}
                            <span><img src="../pic/blogsina.png"/>绑定新浪微博，一键使用2345服务<a href="http://login.2345.com/oauth/bind/weibo" target="_blank" class="btn_bangding">绑定</a></span>
                            {{/if}}
                            {{if $pageArray.OAuthBinds.qq}}
                            <span class="dotted"><img src="../pic/qqlogo.png"/>已经绑定腾讯QQ帐号{{if $pageArray.OAuthBinds.qq.nickname}}"{{$pageArray.OAuthBinds.qq.nickname}}"{{/if}}<a href="#" {{if $pageArray.info.email_status != 0}}onclick="loadJs('http://login.2345.com/oauth/bind/qq?act=request_unbind',function(){show_unbind_email_msg();});return false;"{{else}}onclick="alert('请先验证您的邮箱。');return false;"{{/if}} class="btn_jiechu">解除绑定</a></span>
                            {{else}}
                            <span class="dotted"><img src="../pic/qqlogo.png"/>绑定腾讯QQ，一键使用2345服务<a href="http://login.2345.com/oauth/bind/qq" target="_blank" class="btn_bangding">绑定</a></span>
                            {{/if}}
                            {{/if}}
                        </div>
                    </div>

                    <h4 class="bt">基本信息</h4>
                    <input type="hidden" name="act" value="edit"/>
                    <table class="infotable" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <th>性别：</th>
                            <td>
                                <input class="radio01" type="radio" name="gender" id="gender_2" value="2" {{if $pageArray.info.gender==2}} checked="checked"{{/if}}>女士&nbsp;&nbsp;&nbsp;
                                       <input class="radio01" type="radio" name="gender" id="gender_1" value="1" {{if $pageArray.info.gender==1}} checked="checked"{{/if}}>先生&nbsp;&nbsp;&nbsp;
                                       <input class="radio01" type="radio" name="gender" id="gender_0" value="0" {{if $pageArray.info.gender==0}} checked="checked"{{/if}}>保密</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <th>生日：</th>
                            <td><select name="year" id="year" onchange="allDay();">
                                    <option value="">年</option>
                                    {{foreach key=key item=item from=$pageArray.cdate.year}}
                                    <option value="{{$item}}" {{if $item==$pageArray.info.bday.0}}selected{{/if}}>{{$item}}</option> 
                                    {{/foreach}}

                                </select>
                                &nbsp;
                                <select name="month" id="month" onchange="allDay();">
                                    <option value="">月</option>
                                    {{foreach key=key item=item from=$pageArray.cdate.month}}
                                    <option value="{{$item}}" {{if $item==$pageArray.info.bday.1}}selected{{/if}}>{{$item}}</option> 
                                    {{/foreach}}
                                </select>
                                &nbsp;
                                <select name="day" id="day">
                                    <option value="">日</option>
                                </select>
                            </td>
                            <td>&nbsp;<span id="bday_msg" style="display:none; color:#e92828;">您输入的生日格式不正确</span></td>
                        </tr>
                        <tr>
                            <th>QQ：</th>
                            <td><input class="inbox2" type="text" name="qq" id="qqnew" value="{{$pageArray.info.qq}}" maxlength="12" onblur="checkQq();" >    

                            </td>
                            <td> &nbsp;<span id="qq_msg" style="display:none; color:#e92828;">您输入的qq格式不正确</span>
                                <span id="qq_ok" style="display:none"><img src="../images/check.jpg" /></span></td>
                        </tr>
                        <tr>
                            <th>真实姓名：</th>
                            <td><input class="inbox2" type="text" id="truename" name="nickname" value="{{$pageArray.info.name}}" maxlength="26" onblur="checkName();" />
                            </td>
                            <td>&nbsp;<span id="name_msg" style="display:none; color:#e92828;">您输入的姓名格式不正确</span>
                                <span id="name_ok" style="display:none"><img src="../images/check.jpg" /></span></td>
                        </tr>
                        <tr>
                            <th>地区：</th>
                            <td colspan="2"><select name="area1" id="mcl" onchange="my_ds.on_select_l(this);" style="width:100px">
                                    <option value="0">请选择</option>
                                </select>
                                <select name="area2" id="mcm" onchange="my_ds.on_select_m(this);">
                                    <option value="0">请选择</option>
                                </select>
                                <select name="area3" id="mcn" onchange="my_ds.on_select_n(this);">
                                    <option value="0">请选择</option>
                                </select></td>
                        </tr>
                        <tr>
                            <th>&nbsp;<span id="tel_msg" style="display:none; color:#e92828;">您输入的固定电话格式不正确</span></th>
                            <td style="height:60px; line-height:60px;" colspan="2"><input type="submit" value="确认修改" class="btn4" onmouseover="this.className = 'btn4_hov'" onmouseout="this.className = 'btn4'">  {{$pageArray.strShow}}</td>
                        </tr>
                    </table>
                </div>
            </form>
        </div>
    </div>         
</div>
<script type="text/javascript" src="/js/ajax_tt.js"></script>
<script type="text/javascript">
                        var loadJs = function(_url, _callback){
                var callback = arguments[1] || function() {
                };
                        var _script = document.createElement("SCRIPT");
                        _script.setAttribute("type", "text/javascript");
                        _script.setAttribute("src", _url);
                        document.getElementsByTagName("head")[0].appendChild(_script);
                        if (document.all) {
                _script.onreadystatechange = function() {
                if (/onload|loaded|complete/.test(_script.readyState)) {
                callback && callback();
                }
                };
                } else {
                _script.onload = function() {
                callback();
                };
                }
                }
                function $(id){
                return document.getElementById(id);
                }

                function validate()
                {
                if (!checkEmail()) return false;
                {{if $pageArray.score > 5000 && $pageArray.isBindPhone}}
                if (checkEmailValue()){
                $('send').style.display = '';
                        $('send').innerHTML = '验证邮件发送中...';
                        var arg = 'type=validate&email=' + $('email').value;
                        var url = 'http://{{$smarty.const.MY_DOMAIN}}/member/mail.php';
                        startrequest(arg, url, function(data){
                if (data == '1'){
                var emailExp = $('email').value.split('@');
                        if(emailExp[1] == 'gmail.com'){
                            var emailHost = 'http://www.' + emailExp[1];
                        }else{
                            var emailHost = 'http://mail.' + emailExp[1];
                        }
                        $('send').innerHTML = '验证邮件已发送到您的邮箱，<a href="' + emailHost + '" target="_blank">去邮箱收件</a>';
                } else{
                switch (data){
                case '2':
                        $('email_msg').innerHTML = '您输入的邮箱已经被绑定';
                        break;
                        case '3':
                        $('email_msg').innerHTML = '您已经绑定过邮箱';
                        break;
                        case '4':
                        $('email_msg').innerHTML = '您输入的邮箱格式不正确';
                        break;
                }
                $('email_msg').style.display = '';
                        $('email_ok').style.display = 'none';
                        $('send').innerHTML = '';
                        $('send').style.display = 'none';
                }
                });
                } else{
                var new_email = $("email").value;
                        window.location.href = 'shouji_validate_email.php?email=' + new_email;
                }
                {{ else}}
                $('send').style.display = '';
                        $('send').innerHTML = '验证邮件发送中...';
                        var arg = 'type=validate&email=' + $('email').value;
                        var url = 'http://{{$smarty.const.MY_DOMAIN}}/member/mail.php';
                        startrequest(arg, url, function(data){
                if (data == '1'){
                var emailExp = $('email').value.split('@');
                        if(emailExp[1] == 'gmail.com'){
                            var emailHost = 'http://www.' + emailExp[1];
                        }else{
                            var emailHost = 'http://mail.' + emailExp[1];
                        }
                        $('send').innerHTML = '验证邮件已发送到您的邮箱，<a href="' + emailHost + '" target="_blank">去邮箱收件</a>';
                } else{
                switch (data){
                case '2':
                        $('email_msg').innerHTML = '邮箱已经被使用，请更换邮箱';
                        break;
                        case '3':
                        $('email_msg').innerHTML = '您已经绑定过邮箱';
                        break;
                        case '4':
                        $('email_msg').innerHTML = '您输入的邮箱格式不正确';
                        break;
                }
                $('email_msg').style.display = '';
                        $('email_ok').style.display = 'none';
                        $('send').innerHTML = '';
                        $('send').style.display = 'none';
                }
                });
                {{/if}}
                }

                function allDay()
                {
                var year = $('year').value;
                        var month = $('month').value;
                        var maxDay;
                        if (year != "" && month != "")
                {
                if (month == '01' || month == '03' || month == '05' || month == '07' || month == '08' || month == '10' || month == '12')
                {
                maxDay = 31;
                }
                else if (month == '04' || month == '06' || month == '09' || month == '11')
                {
                maxDay = 30;
                }
                else
                {
                if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0)
                {
                maxDay = 29;
                }
                else
                {
                maxDay = 28;
                }
                }
                $('day').length = 1;
                        for (var i = 1; i <= maxDay; i++)
                {
                var ii = i < 10?'0' + i:i;
                        $('day').options.add(new Option(ii, ii));
                }
                }
                }

                function checkNull(obj)
                {
                if (!(/^\d*$/.test(obj.value)))
                {
                obj.value = '';
                }
                }

                function checkAll()
                {
                if ($('suc'))
                {
                $('suc').style.display = 'none';
                }
                if ($('email'))
                {
                if (!checkEmail()) return false;
                }
                if (!checkBday()) return false;
                        if (!checkQq()) return false;
                        if (!checkName()) return false;
                        if (!checkPhone()) return false;
                        return true;
                }

                function checkName()
                {
                $('name_msg').style.display = 'none';
                        $('name_ok').style.display = 'none';
                        var val = $('truename').value;
                        if (val != '')
                {
                if (!/^[\u4E00-\u9FA5\w_ ]*$/.test(val))
                {
                $('name_msg').style.display = '';
                        $('name_ok').style.display = 'none';
                        return false;
                }
                $('name_msg').style.display = 'none';
                        $('name_ok').style.display = '';
                }
                return true;
                }

                function checkEmail()
                {
                $('email_msg').style.display = 'none';
                        $('email_ok').style.display = 'none';
                        var val = $('email').value;
                        if (val != '')
                {
                if (!/^[_\.0-9a-z-]+@([0-9a-z][0-9a-z-]+\.)+[a-z]{2,4}$/.test(val))
                {
                $('email_msg').innerHTML = '您输入的邮箱格式不正确';
                        $('email_msg').style.display = '';
                        $('email_ok').style.display = 'none';
                        $('send').style.display = 'none';
                        return false;
                }
                $('email_msg').style.display = 'none';
                        $('email_ok').style.display = '';
                        $('send').style.display = '';
                } else{
                $('email_msg').innerHTML = '邮箱不能为空';
                        $('email_msg').style.display = '';
                        $('email_ok').style.display = 'none';
                        $('send').style.display = 'none';
                        return false;
                }
                return true;
                }
                function checkEmailValue()
                {
                var val = $('email').value;
                        var old_val = $('email_old').value;
                        if (val == old_val){
                return true;
                } else{
                return false;
                }
                }

                function checkQq()
                {
                $('qq_msg').style.display = 'none';
                        $('qq_ok').style.display = 'none';
                        var val = $('qqnew').value;
                        if (val != '')
                {
                if (!(/^\d{5,}$/.test(val)))
                {
                $('qq_msg').style.display = '';
                        $('qq_ok').style.display = 'none';
                        return false;
                }
                $('qq_msg').style.display = 'none';
                        $('qq_ok').style.display = '';
                }
                return true;
                }

                function checkAddress()
                {
                $('address_msg').style.display = 'none';
                        $('address_ok').style.display = 'none';
                        var area1 = $('mcl').value;
                        var area2 = $('mcm').value;
                        var area3 = $('mcn').value;
                        var addr = $('address1').value;
                        if (area1 != '0' || area2 != '0' || area3 != '0')
                {
                $('address_msg').style.display = 'none';
                        $('address_ok').style.display = '';
                }
                return true;
                }

                function checkZip()
                {
                $('zip_msg').style.display = 'none';
                        $('zip_ok').style.display = 'none';
                        var val = $('zip').value;
                        if (val != '')
                {
                if (!(/^\d{6}$/.test(val)))
                {
                $('zip_msg').style.display = '';
                        $('zip_ok').style.display = 'none';
                        return false;
                }
                $('zip_msg').style.display = 'none';
                        $('zip_ok').style.display = '';
                }
                return true;
                }

                function checkPhone()
                {
                var tel1 = $('tel1').value;
                        if (tel1 == '区号') tel1 = '';
                        var tel2 = $('tel2').value;
                        if (tel2 == '电话号码') tel2 = '';
                        var tel3 = $('tel3').value;
                        if (tel3 == '分机') tel3 = '';
                        if (tel1 != '')
                {
                if (!(/^\d{3,4}$/.test(tel1)))
                {
                $('tel_msg').style.display = '';
                        return false;
                }
                }
                if (tel2 != '')
                {
                if (!(/^\d{7,8}$/.test(tel2)))
                {
                $('tel_msg').style.display = '';
                        return false;
                }
                }
                if (tel3 != '')
                {
                if (!(/^\d{1,4}$/.test(tel3)))
                {
                $('tel_msg').style.display = '';
                        return false;
                }
                }
                if ((tel1 == '' || tel2 == '') && (tel3 != '' || tel2 != '' || tel1 != ''))
                {
                $('tel_msg').style.display = '';
                        return false;
                }
                $('tel_msg').style.display = 'none';
                        return true;
                }

                function checkBday()
                {
                var year = $('year').value;
                        var month = $('month').value;
                        var day = $('day').value;
                        if (!(year == '' && month == '' && day == '' || year != '' && month != '' && day != ''))
                {
                $('bday_msg').style.display = '';
                        return false;
                }
                return true;
                }

                allDay();
                        if ('{{$pageArray.info.bday.2}}' == '0')
                {
                var day = "";
                }
                else
                {
                var day = "{{$pageArray.info.bday.2}}";
                }
                $('day').value = day;
                        //点击统计函数
                        document.onclick = function(e)//兼容IE,FF,OPERA
                {
                e = window.event || e;
                        sE = e.srcElement || e.target;
                        var isNotImg = true;
                        if (sE.tagName == "IMG" || sE.tagName == "A" || sE.tagName == "AREA")
                {
                if (sE.tagName == "IMG" && sE.src != "")
                {
                sE = sE.parentNode;
                        isNotImg = false;
                }
                if ((sE.tagName == "A" || sE.tagName == "AREA") && sE.href != "")
                {
                cc(sE.href);
                }
                }
                }

                function cc(a) {
                var b = arguments,
                        web = "ajax54",
                        a2,
                        i1 = document.cookie.indexOf("uUiD="),
                        i2;
                        if (b.length > 1) web = b[1];
                        if (i1 != - 1) {
                i2 = document.cookie.indexOf(";", i1);
                        a2 = i2 != - 1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
                }
                if (!a2) {
                a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                        document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
                }
                if (a.length > 0) {
                var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                        loadJs(c)
                }    return true;
                }
</script>
<script src="/js/my_city_120716.js" type="text/javascript"></script>
{{include file="footer.tpl.html"}}