<?php

class GetUserWithBindByPassidController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }

        if ($_GET)
        {
            $passid = intval($_GET['passid']);
            if ($passid > 0)
            {
                $memberModel = loadModel('member');
                $user = $memberModel->getMember($passid, ['username', 'reg_time']);
                if ($user)
                {
                    $data = array('username' => $user['username'], 'regTime' => $user['reg_time']);
                    $oauthModel = loadModel('oauth');
                    $binds = $oauthModel->getBindByPassid($passid);
                    !$binds && $binds = [];
                    foreach ($binds as $bind)
                    {
                        $data[$bind['type']] = array('nickname' => $bind['nickname'], 'bindTime' => date('Y-m-d H:i:s', $bind['bindtime']));
                    }
                    echo serialize($data);
                }
            }
        }
    }

}
