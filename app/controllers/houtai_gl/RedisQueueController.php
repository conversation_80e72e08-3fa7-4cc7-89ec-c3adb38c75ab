<?php
includeBase('AdminController');

class RedisQueueController extends AdminController
{
    protected $config;

    /**
     * RedisQueueController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->config = Config::get('redis');
        $ip = get_client_ip();
        if (!\Service\Security\Server::checkCompanyIp($ip))
        {
            header('HTTP/1.1 404 Not Found');
            exit();
        }
    }

    /**
     * actionIndex
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $loginQueueInfo = $this->queueLogin();
        echo implode("<br/>", $loginQueueInfo);
        echo "<br/>";
        $regQueueInfo = $this->queueReg();
        echo implode("<br/>", $regQueueInfo);
        echo "<br/>";
        $logQueueInfo = $this->queueLog();
        echo implode("<br/>", $logQueueInfo);
    }

    /**
     * queueReg
     * -
     * @return array
     * <AUTHOR>
     */
    private function queueReg()
    {
        $result = array();
        $regQueueRedis = loadAction('regQueueRedis');
        $length = $regQueueRedis->regQueueLength();
        if ($length === -1 || !$regQueueRedis->regQueueRedis)
        {
            array_push($result, '用户注册数据redis连接失败');
        }
        else
        {
            array_push($result, 'regQueueRedis Length: ' . $length);

            $memory = $regQueueRedis->regQueueRedis->info("MEMORY");
            // 10737418240 10G
            if (isset($memory["used_memory"]) && isset($memory["used_memory_human"]))
            {
                array_push($result, 'regQueueRedis used_memory:' . $memory["used_memory"] . ', used_memory_human:' . $memory["used_memory_human"]);
            }
        }
        $regQueueRedis->closeRegQueueRedis();
        return $result;
    }

    /**
     * queueLogin
     * -
     * @return array
     * <AUTHOR>
     */
    private function queueLogin()
    {
        $result = array();
        $loginQueueRedis = loadAction('loginQueueRedis');
        $length = $loginQueueRedis->loginQueueLength();
        if ($length === -1)
        {
            array_push($result, '用户登录数据redis连接失败');
        }
        else
        {
            array_push($result, 'loginQueueRedis Length: ' . $length);
            $memory = $loginQueueRedis->loginQueueRedis->info("MEMORY");
            // 10737418240 10G
            if (isset($memory["used_memory"]) && isset($memory["used_memory_human"]))
            {
                array_push($result, 'regQueueRedis used_memory:' . $memory["used_memory"] . ', used_memory_human:' . $memory["used_memory_human"]);
            }
        }
        $loginQueueRedis->closeLoginQueueRedis();
        return $result;
    }

    /**
     * 登录日志队列
     * <AUTHOR>
     * @DateTime 2018-05-29T15:25:23+0800
     * @return   [type]                   [description]
     */
    private function queueLog()
    {
        $result = array();
        try {
            $redis = RedisEx::getInstance();
        }
        catch (\Exception $e)
        {
            array_push($result, date("Y-m-d H:i:s") . " redis error:" . $e->getMessage());
            return $result;
        }
        $length = $redis->lLen("log:setLogin");
        $redis->close();
        array_push($result, 'log:setLogin Length: ' . $length);
        return $result;
    }
}
