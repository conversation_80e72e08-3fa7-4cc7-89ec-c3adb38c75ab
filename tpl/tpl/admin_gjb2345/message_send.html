<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gbk" />
<script charset="utf-8" src="js/kindedit/kindeditor.js"></script>
<script charset="utf-8" src="js/kindedit/lang/zh_CN.js"></script>
<script>
	KindEditor.ready(function(K) {
		var editor1 = K.create('textarea[name="content"]', {
			uploadJson : 'upload_json.php',
			fileManagerJson : 'file_manager_json.php',
			allowFileManager : true,
			afterCreate : function() {
				var self = this;
				K.ctrl(document, 13, function() {
					self.sync();
					K('form[name=message_send_form]')[0].submit();
				});
				K.ctrl(self.edit.doc, 13, function() {
					self.sync();
					K('form[name=message_send_form]')[0].submit();
				});
			}
		});
	});
</script>
<title>管理中心-2345网址导航高级版</title>

<link href="css/gaoji.css" rel="stylesheet" type="text/css" />
</head>

<body>

<!-- wrap -->
<div class="wrap">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td width="200" class="nav_border">
	<div class="left_nav">
    {{include file="admin_gjb2345/menu.html"}}
    </div></td>
    <td><div class="right_cnt">
<style>
*,body{ font-size:12px; }
.mytable{ background-color:#9cc; padding:0 0 0 0; text-align:left; padding:1px 1px 1px 1px;}
.mytable td {background-color:#FFF; padding:1px 1px 1px 1px;}

<!--
*{ padding:0; margin:0;}
.tag ul,li{ list-style:none;}
img{ border:0;}
.tag li{ float:left; display:block; height:20px; line-height:20px; background:#ecf7ff; border:1px solid #d2dde2; margin:1px 6px 1px 0; padding:0 5px;word-wrap:normal;
 word-break:keep-all; }
.tag li a{ color:#39477e; text-decoration:none; float:left; word-break:keep-all;}
.tag li a:hover{ color:#f60; text-decoration:underline;}
.tag li a img{ vertical-align:middle; margin-top:4px; float:left; margin-left:5px;}

-->
</style>
    <h4>发送通知</h4>
    <br />

	<div style="width:95%; margin-left:30px; border:1px solid #CCC;">
	<form method="post" id="message_send_form" name="message_send_form" enctype="multipart/form-data">
		<table class="mytable" width="100%" border="0" cellspacing="1" cellpadding="0">
		  <tr>
			<td style="text-align:right" width="93">收件人:</td>
			<td width="620"><div style="width:100%; height:85px; margin:0 auto; overflow: auto;">
			<ul class="tag" id="user_list">
				{{if $pageArray.userInfo != ''}}
					{{$pageArray.userInfo}}					
				{{/if}}
			</ul>
			</div>
			{{if $pageArray.hideUid == ''}}
				<input type="hidden" name="user_id" id="user_id" />
				<input type="hidden" name="user_name" id="user_name" />
			{{else}}
				{{$pageArray.hideUid}}
				{{$pageArray.hideUser}}
			{{/if}}
			</td>
		  </tr>
		  <tr style="height: 50px">
			<td style="text-align:right" width="93"></td>
			<td width="377">
			查找收信人：<input type="text" id="user_info" size="43" value="用戶名、Email、QQ" onclick="this.value=''" />
			<input type="button" id="search" value="查找" onclick="searchs();" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			批量操作：<input type="file" name="batch" id="batch" /> <input type="button" value="清除批量" onclick="$('batch').value=''" />
			<br>
			<div id="user_info_list" style="height: 150px; overflow-y: auto;"></div>
			</td>
		  </tr> 
		  <tr>
			<td style="text-align:right">主题:</td>
			<td>
			<input type="text" name="title" id="title" style="width:350px;" maxlength="25" required="required" /></td>
		  </tr>
		  <tr>
			<td style="text-align:right">内容:</td>
			<td>
			<textarea name="content" id="content" cols="120" rows="15"></textarea>
			<font size="5" color="red">上传图片宽度不要超过685像素，否则前台页面会错误</font>			
			</td>
		  </tr>
		  
		  <tr>
			<td colspan="2"><span style="color:red;">您的通知代表瑞创网络,请慎重注意自己的言论啊^</span></td>
		  </tr>
		  <tr>
			<td>&nbsp;</td>
			<td><input type="submit" name="button" id="button" value="提交" onclick="return checkForm();" /></td>
		  </tr>
		</table>
	</form>
	</div>
	<script>
		function $(_a){
			return document.getElementById(_a);
			}
		function searchs(){
			var user_info = document.getElementById("user_info").value;
			if(user_info != ""){
				$('user_info_list').innerHTML='<img src="../../images/wait.gif" title="加载中..." />';
				loadJs("http://login.2345.com/passport/api/list.php?action=user_list&user_info="+encodeURI(user_info));
				}else{
					alert("搜索不能為空");
					return false;
					}
			}
		function loadJs(_url) {
			var callback = arguments[1] || function() {
				
			};
			// load_js_url = _url;
			var _script = _c("SCRIPT");
			_script.setAttribute("type", "text/javascript");
			_script.setAttribute("src", _url);
			document.getElementsByTagName("head")[0].appendChild(_script);
			if (document.all) {				
				_script.onreadystatechange = function() {
					if (/onload|loaded|complete/.test(_script.readyState)) {
						callback && callback();
					}
				};
			} else {
				_script.onload = function() {
					callback();
				};
			}
		}
		function _c(_lab) {
			return document.createElement(_lab);
		}
		function addSender(uid){
			var username = document.getElementById("u_"+uid).innerHTML;
			document.getElementById("state"+uid).innerHTML = "已添加";
			var uid = uid;
			var uname_list = document.getElementById("user_name").value;
			var user_list = document.getElementById("user_list").innerHTML;
			if(uname_list.indexOf(";"+username+";") == -1){
				document.getElementById("user_list").innerHTML += '<li id="m_'+uid+'"><a id="a_'+uid+'">'+username+'</a><a href="javascript:delSender('+uid+')"><img src="images/close.gif" /></a></li>';
				if(uname_list == ""){
				document.getElementById("user_name").value += ";"+username+";";
				document.getElementById("user_id").value += ";"+uid+";";
				}else{
					document.getElementById("user_name").value += username+";";
					document.getElementById("user_id").value += uid+";";
					}
			}
			
		}
		function delSender(uid){
			$("m_"+uid).style.display = "none";
			//document.getElementById("state"+uid).innerHTML = '<a style=\"cursor: pointer;\" onclick=\"addSender('+uid+')\">添加到收信人</a>';
			$("user_name").value = ReplaceAll($("user_name").value, ";"+$("a_"+uid).innerHTML, '');
			$("user_id").value = ReplaceAll($("user_id").value, ";"+uid, '');
			}
		function ReplaceAll(str, sptr, sptr1) {
			while (str.indexOf(sptr) >= 0) {
				str = str.replace(sptr, sptr1);
			}
			return str;
		}
		function checkForm(){
			if($("user_name").value == ";" || $("user_id").value == ";" || $("user_name").value == "" || $("user_id").value == "")
			{
				if ($("batch").value == '')
				{
					alert("请选择要发送的用户！");
					return false;
				}
				
			}
			return true;
		}
	</script>
{{include file="admin_gjb2345/footer.html"}}