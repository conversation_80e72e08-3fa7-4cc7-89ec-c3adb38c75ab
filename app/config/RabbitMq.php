<?php
/**
 * rabbitMq配置文件
 */
$config['rabbitMq'] = [
    // mq链接配置
    'default' => [
        'host' => env('RABBIT_MQ_HOST'),
        'port' => env('RABBIT_MQ_PORT'),
        'login' => env('RABBIT_MQ_LOGIN'),
        'password' => env('RABBIT_MQ_PWD'),
        'vhost' => env('RABBIT_MQ_VHOST'),
        'read_timeout' => env('RABBIT_MQ_READ_TIMEOUT'), // 如果是以监听方式处理，此处设为0，否则会超时报错
        'write_timeout' => env('RABBIT_MQ_WRITE_TIMEOUT'),
        'cacert' => env('RABBIT_MQ_CACERT'),
        'cert' => env('RABBIT_MQ_CERT'),
        'key' => env('RABBIT_MQ_KEY'),
        'verify' => env('RABBIT_MQ_VERIFY'),
    ],
    // 日志服务链接配置
    'service_log' => [
        'host' => env('RABBIT_MQ_HOST_SERVICE_LOG'),
        'port' => env('RABBIT_MQ_PORT_SERVICE_LOG'),
        'login' => env('RABBIT_MQ_LOGIN_SERVICE_LOG'),
        'password' => env('RABBIT_MQ_PWD_SERVICE_LOG'),
        'vhost' => env('RABBIT_MQ_VHOST_SERVICE_LOG'),
        'read_timeout' => env('RABBIT_MQ_READ_TIMEOUT_SERVICE_LOG'), // 如果是以监听方式处理，此处设为0，否则会超时报错
        'write_timeout' => env('RABBIT_MQ_WRITE_TIMEOUT_SERVICE_LOG'),
    ],
    // 开启头像昵称更新推送项目id（mid）
    'notice_used_mid' => explode(',', env('RABBIT_MQ_NOTICE_USED_MID')),
    // 数据签名key，目前不区分生态或者项目
    'sign_key' => env('RABBIT_MQ_DATA_SIGN_KEY'),

    //日志服务v2的交换机和路由
    'service_log_v2_exchange_name' => env('SERVICE_LOG_V2_EXCHANGE_NAME'),
    'service_log_v2_route_key' => env('SERVICE_LOG_V2_ROUTE_KEY'),
];
