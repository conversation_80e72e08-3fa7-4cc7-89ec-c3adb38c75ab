<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务平台-IP白名单</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
   <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a> > <span>IP白名单</span></div>
    <!--面包屑导航 结束-->
    
    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">        
        <a  href="/Admin/BlackList/Index">手机黑名单</a>
        <a href="/Admin/BlackList/WriteList">手机白名单</a>
        <a  class="currTabMenu" href="/Admin/BlackList/IpWriteList">IP白名单</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    
    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="" method="post" id="queryFormDiv">
            <div class="dataWrap clearfix">
            	<em>日期：</em>
                <div class="dataList w130">
                	<div class="getTime"><input value="{{$pageArray.startTime}}" type="text" autocomplete="off" name="startTime" onclick="WdatePicker()" /><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->
            <span class="dataTxt">至</span>
            <div class="dataWrap clearfix">
                <div class="dataList w130">
                	<div class="getTime"><input value="{{$pageArray.endTime}}" type="text" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->
    
            
          <span class="input-search ml10"><i>搜索Icon</i><input value="{{$pageArray.ip}}" type="text" autocomplete="off" name="ip" placeholder="IP" /></span>
        
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 querySubmitForm">查询</a>
            
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 exportIpWhite">批量导出</a>
        
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    <div class="funList mt20 clearfix">
        <a class="styleBtn_s styleBtnBlue left addIpblackClass" href="javascript:">添加IP白名单</a>
        <a style="margin-left: 5px;" class="styleBtn_s styleBtnBlue left" target="_blank" href="/Admin/SmsProject/SmsSyncIpWriteList">更新IP白名单</a>
    
        <form action="/Admin/BlackList/IpWhiteUpload" method="post" enctype="multipart/form-data">
            <input type="file" style="margin-left: 5px;" name="Filedata"/>
            <input type="submit" value="批量导入"/>
        </form>
    </div>
    <!--主体内容 开始-->
    <div class="contentWrap">
    
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>ID</th>
                        <th>IP</th>
                        <th>添加人</th>
                        <th>备注</th>
                        <th>添加时间</th>
                        <th>操作</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th>ID</th>
                <th>IP</th>
                <th>添加人</th>
                <th>备注</th>
                <th>添加时间</th>
                <th>操作</th>
            </tr>
            {{foreach from=$pageArray.ipList item=ipInfo}}
            <tr>
                <td>{{$ipInfo.ilId}}</td>
                <td>{{$ipInfo.ip}}</td>
                <td>{{$ipInfo.addName}}</td>
                <td>{{$ipInfo.comment}}</td>
                <td>{{$ipInfo.addTime}}</td>
                <td><a href="javascript:" ilId="{{$ipInfo.ilId}}" class="delblackClass">解除</a></td>
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
        
       {{$pageArray.showPage}}
        
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->

	<!--透明遮罩-->
    <div class="boxMask" style="display: none;">透明遮罩层</div>
 <!--添加权限 弹窗 开始-->
	<div class="boxMod w520 addIpBlackDiv">
    	<div class="boxTop"><h2 class="boxTit">添加IP白名单</h2><a class="BoxClose" href="javascript:">关闭按钮</a></div>
        <div class="tabFrom mtb20">
            <form id="blackListFromId">
                <table class="tabFromMod" cellpadding="0" cellspacing="0">
                    <tr>
                        <td class="tabLeftTit">IP：</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="blackList[ip]" /></td>
                    </tr>
                    <tr>
                        <td class="tabLeftTit">备注：</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="blackList[comment]" /></td>
                    </tr>
                    <tr>
                        <td colspan="2" align="center">
                            <a class="styleBtnBlue tabsave submitBlackList" href="javascript:">保存</a></td>
                    </tr>
                </table>
            </form>
        </div><!--tabFrom end-->
    </div><!--boxMod end-->
    <!--添加权限 弹窗 结束-->
    

<script src="/style/js/tableFixed.js"></script>
<script src="/style/js/boxLocation.js"></script>
<script>
$('.addIpblackClass').click(function (){
    $('.boxMask').show();
    $('.addIpBlackDiv').show();      
});

$('.submitBlackList').click(function (){
    var ip = $('input[name="blackList[ip]"]').val();
    if (ip == '')
    {
        alert('ip不能为空');
        return false;
    }
    
    $.post('/Admin/BlackList/SetIpWriteList',$('#blackListFromId').serialize(),function (data){
        if (data.status === 'P00001')
        {
            window.location = '/Admin/BlackList/IpWriteList';
        }
        else
        {
            alert(data.msg)
        }
    });
});

$('.editblackClass').click(function(){
    $('.boxMask').show();
    $('.addBlackDiv').show();
    var bpl_id = $(this).attr('bpl_id');
    $.post('/Admin/BlackList/GetBlackInfo','bpl_id='+bpl_id,function (data){
        $('select[name="blackList[blackType]"]').val(data.data.black_type);
        $('input[name="blackList[phone]"]').val(data.data.phone);
        $('input[name="blackList[bpl_id]"]').val(bpl_id);
        $('.phoneTwo').val(data.data.phone);
        //$('.surePhone').hide();
    })
    
});

$('.delblackClass').click(function (){
   if ( confirm('确定解除吗?') )
   {
        var ilId = $(this).attr('ilId');
       $.post('/Admin/BlackList/DelIpWriteList','ilId='+ilId,function(data){
            
            if ( data.status === 'P00001')
            {
                window.location = '/Admin/BlackList/IpWriteList';
            }
            else
            {
                alert(data.msg);
            }
       }) 
   }
   
});
$('.BoxClose').click(function (){
    $('.boxMask').hide();
    $('.boxMod').hide();
});

$('.querySubmitForm').click(function (){
    $('#queryFormDiv').submit();
});

$('.exportIpWhite').click(function(){
    var link = '/Admin/BlackList/IpWriteList?act=export&' + $('#queryFormDiv').serialize();
    window.open(link); 
})
</script>
</body>
</html>