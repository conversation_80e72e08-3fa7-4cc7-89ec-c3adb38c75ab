#!/bin/bash
action=$1
machine=$2
PHP=/opt/app/php7/bin/php;
PHPCODE=/opt/case/smsp.2345.net/console/index.php;
if [ -z "${action}" ];then
    exit 1;
fi
case "${action}" in
   'SyncBlackPhone' )
        #自动同步手机黑白名单
        ${PHP} ${PHPCODE} Crontab\\SmsSetKeyController actionSmsSyncBlackPhoneKey
        ;;
   'SyncIpWriteList' )
        #自动同步IP白名单
        ${PHP} ${PHPCODE} Crontab\\SmsSetKeyController actionSmsSyncIpWriteList
        ;;
   'BingFaTotalIndex' )
        #每分钟一次
        ${PHP} ${PHPCODE}  Crontab\\BingFaTotalController actionIndex
        ;;
   'BingFaTotalBingFaNumsTotal' )
        #凌晨30 跑统计并发
        ${PHP} ${PHPCODE}  Crontab\\BingFaTotalController actionBingFaNumsTotal
        ;;
   'YyRequestTotal' )
        # 凌晨1点 30 跑语音分析
        ${PHP} ${PHPCODE}  Crontab\\YyRequestTotalController actionRun
        ;;
   'YyMonitor' )
        # 语音监控
        ${PHP} ${PHPCODE}  Crontab\\YyMonitorController actionRun
        ;;
   'AddupCache' )
        # 建立短信统计缓存 每天早上7点跑一次
        ${PHP} -d safe_mode=Off ${PHPCODE} Crontab\\AddupCacheController actionRun
        ;;
   'MailMonitor' )
        #每分钟跑一次 发送预警、回执预警
        ${PHP} -d safe_mode=Off ${PHPCODE} Crontab\\ChannelCallbackStatusController actionIndex
        ;;
   'PullCallbackStatus' )
        #每分钟跑一次 主动拉取服务商回执、上行回复内容
        ${PHP} -d safe_mode=Off ${PHPCODE} Crontab\\ChannelCallbackStatusController actionAcceptSmsResponseInfo
        ;;
   'CallbackStatus2DB' )
        #每分钟跑一次 回执从缓存入库
        ${PHP} -d safe_mode=Off ${PHPCODE} Crontab\\ChannelCallbackStatusController actionNewProcessor
        ;;
   'CallbackSms2DB' )
        #每分钟跑一次 上行短信从缓存入库
        ${PHP} -d safe_mode=Off ${PHPCODE} Crontab\\ChannelCallbackStatusController actionGetSmsResponse
        ;;
   'PushCallback' )
        #每分钟跑一次 上行短信发送到贷款王
        ${PHP} -d safe_mode=Off ${PHPCODE} Crontab\\ChannelCallbackStatusController actionPushSmsResponse
        ;;
   'SendQueue' )
        #每分钟跑一次 可能多进程共存 发送队列短信
        ${PHP} -d safe_mode=Off ${PHPCODE} Crontab\\SendQueueController actionRun ${machine}
        ;;
   'redis2DB' )
        #每分钟跑一次 可能多进程共存 redis入数据库
        ${PHP} ${PHPCODE} Crontab\\SmsCronController actionRun
        ;;
   'BIDataStatistic' )
        #每天跑一次，BI统计数据推送
        ${PHP} -d max_execution_time=300 ${PHPCODE}  Crontab\\DataStatisticController actionRun
        ;;
   'ClearSmsProjectBindLog' )
        #每天跑一次，清除三天前的流水号缓存
        ${PHP} ${PHPCODE} Crontab\\ChannelCallbackStatusController actionClearSmsProjectBindLog
        ;;
   'MonthSmsStatistic' )
        #每月跑一次,计算各个项目的计费短信
        ${PHP} ${PHPCODE}  Crontab\\SmsStatController actionSmsStat
        ;;
    * )
        ;;
esac;
