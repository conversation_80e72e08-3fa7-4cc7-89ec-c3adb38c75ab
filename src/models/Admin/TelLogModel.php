<?php

namespace Admin;
class TelLogModel extends \Model
{
    /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }
    
    public function getVoiceCodeLog($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col} FROM `freecall_voice_code_log` ";

        
        if (!empty($data['mobile']))
        {
            $where .= " mobile = :mobile AND";
            $paramArr[':mobile'] = $data['mobile'];
        }
        
        if (!empty($data['from']))
        {
            $where .= " `from` = :from AND";
            $paramArr[':from'] = $data['from'];
        }
        
        
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by add_time desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }
    
   


}