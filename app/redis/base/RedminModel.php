<?php
includeRedis("RedminApp");
includeRed<PERSON>("RedminRouter");
includeRedis("RedminSession");
includeRedis("RedminDb");

class RedminModel
{
    private $_objects = Null;

    /**
     * 构造方法
     */
    public function __construct()
    {
        $this->_objects['app'] = RedminApp::instance();
        $this->_objects['router'] = RedminRouter::instance();
        $this->_objects['session'] = RedminSession::instance();
        $this->_objects['db'] = RedminDb::factory();
    }

    /**
     * 魔术方法
     * @param $object
     * @return null
     * <AUTHOR>
     */
    public function __get($object)
    {
        return isset($this->_objects[$object]) ? $this->_objects[$object] : Null;
    }
}
