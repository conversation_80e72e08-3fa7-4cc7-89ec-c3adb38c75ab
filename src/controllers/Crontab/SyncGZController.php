<?php

namespace Crontab;

use Controller;
use Octopus\PdoEx;

/**
 * 临时脚本，用于短信上云期间的数据同步
 *
 * Class SyncGZController
 * @package Crontab
 */
class SyncGZController extends Controller
{
    /**
     * @var PdoEx
     */
    private $from;

    /**
     * @var PdoEx
     */
    private $to;

    /**
     * AddupCacheController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->from = PdoEx::getInstance("from", \Config::get("database_sync_from"));
        $this->to = PdoEx::getInstance("to", \Config::get("database_sync_to"));
    }

    /**
     * @param string $startTime
     * @param string $endTime
     * cron脚本入口
     * @return void
     */
    public function actionRun($startTime = "", $endTime = "")
    {
        if (!$startTime || !$endTime) {
            die("请输入日期范围" . PHP_EOL);
        }

        $logId = 0;
        $limit = 500;
        $table = "sms_logs_cache";
        $monthTable = "sms_logs_archive_" . date("Ym");

        $sql = "select count(*)a from sms_logs_cache where send_time between '{$startTime}' and '{$endTime}' ";
        $res = $this->from->find($sql);
        echo "count:{$res["a"]}" . PHP_EOL;

        while (1) {
            $sql = "select * " .
                "from sms_logs_cache " .
                "where send_time between '{$startTime}' and '{$endTime}' " .
                "and log_id> {$logId} " .
                "order by log_id limit {$limit}";
            $res = $this->from->findAll($sql, [], 1);
            if (!$res) {
                break;
            }
            foreach ($res as $item) {
                $logId = $item["log_id"];
                $local = $this->to->find("select * from {$table} where encrypt_phone='{$item["encrypt_phone"]}' and send_time='{$item["send_time"]}'");
                if ($local) {
                    if ($local["callback_status"] == 1) {
                        continue;
                    } else {
                        if ($item["callback_status"] == 1) {
                            //更新回调数据
                            $return = $this->to->update($table, [
                                "send_status" => $item["send_status"],
                                "callback_time" => $item["callback_time"],
                                "callback_status" => $item["callback_status"],
                            ], [
                                'where' => ' log_id = :log_id ',
                                'params' => array(':log_id' => $local['log_id'])
                            ]);
                            if (!$return) {
                                echo "to: update {$table} fail" . PHP_EOL;
                                continue;
                            }
                            //更新月表数据
                            $remote = $this->from->find("select * from {$monthTable} where log_id='{$item["log_id"]}'");
                            if (!$remote) {
                                echo "from: select {$monthTable} fail: {$item["log_id"]}" . PHP_EOL;
                                continue;
                            }
                            $return = $this->to->update($monthTable, [
                                "channel" => $remote["channel"],
                                "account" => $remote["account"],
                                "msg_id" => $remote["msg_id"],
                                "code_time" => $remote["code_time"],
                                "code_status" => $remote["code_status"],
                                "code_desc" => $remote["code_desc"],
                                "sms_count" => $remote["sms_count"],
                                "callback_time" => $remote["callback_time"],
                                "callback_status" => $remote["callback_status"],
                                "callback_message" => $remote["callback_message"],
                            ], [
                                'where' => ' log_id = :log_id ',
                                'params' => array(':log_id' => $local['log_id'])
                            ]);
                            if (!$return) {
                                echo "to: update {$monthTable} fail" . PHP_EOL;
                                continue;
                            }
                        }
                    }
                } else {
                    //插入数据
                    $tmp = $item["log_id"];
                    unset($item["log_id"]);
                    if (!$this->to->insert($table, $item)) {
                        echo "to: insert {$table} fail: " . serialize($item) . PHP_EOL;
                    }
                    //插入月表
                    $lastId = $this->to->lastInsertId();
                    $remote = $this->from->find("select * from {$monthTable} where log_id='{$tmp}'");
                    if ($remote) {
                        $remote["log_id"] = $lastId;
                        if ($this->to->insert($monthTable, $remote)) {
                            echo "to: insert {$monthTable} fail: " . serialize($remote) . PHP_EOL;
                        }
                    } else {
                        echo "from: select {$monthTable} fail: {$tmp}" . PHP_EOL;
                    }
                }
            }
        }
        echo "done" . PHP_EOL;
    }
}
