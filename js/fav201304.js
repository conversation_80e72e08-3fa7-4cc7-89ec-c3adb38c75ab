var cid = 0; //当前类别为常用分类
var cateory_id = 0; //0为添加一个新的顶级分类
var show_way = "table";
var del_fav_more_ids = "";
var now_n = "";
var flag = 0;
var agt = navigator.userAgent.toLowerCase();
var isFF = (agt.indexOf("firefox") != -1);
var jq;
var search='';
My.loadJs("/js/min.js?tim=" + (+new Date), function(){
	jq = jQuery.noConflict();
});

//点击统计函数
document.onclick = function(e)//兼容IE,FF,OPERA
{
	e = window.event || e;
	sE = e.srcElement || e.target;
	var isNotImg = true;
	if(sE.tagName=="IMG"||sE.tagName=="A"||sE.tagName=="AREA")
	{
		if(sE.tagName=="IMG" && sE.src != "")
		{
			sE = sE.parentNode;
			isNotImg = false;
		}
		if( (sE.tagName == "A"||sE.tagName == "AREA") && sE.href != "" )
		{
			cc(sE.href);
		}
	}
}
 
function cc(a) {
	var b = arguments,
	web = "ajax28",
	a2,
	i1 = document.cookie.indexOf("uUiD="),
	i2;
	if (b.length > 1) web = b[1];
	if (i1 != -1) {
		i2 = document.cookie.indexOf(";", i1);
		a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
	}
	if (!a2) {
		a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
		document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
	}
	if (a.length > 0) {
		var c = "http://web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
		My.loadJs(c)
	}
	return true;
}

//监听添加网址时键盘上下键操作事件
document.onkeyup = function(event){
	event = event ? event : (window.event ? window.event : null); 
	var keycode = event.which||event.keyCode;
	if(getObj("url_liangxiang").style.display != 'none')
	{
		switch(keycode){ 
			case 38://向上
				if(now_n === ''){
					now_n = (url_count-1);
				}else if(now_n>0){
					now_n = now_n-1;
				}
				else{
					now_n = (url_count-1);
				}
				for(var i=0;i<url_count;i++){
					getObj("keycode_"+i).className = '';	
				}
				getObj("keycode_"+now_n).className = 'keycode';
				getObj("vUrl").value = getObj("keycode_"+now_n).innerHTML;
				break;
			case 40://向下
				if(now_n === ''){
					now_n = 0;
				}else if(now_n<(url_count-1) ){
					now_n ++;
				}else{
					now_n = 0;
				}
				for(var i=0;i<url_count;i++){
					getObj("keycode_"+i).className = '';	
				}
				getObj("keycode_"+now_n).className = 'keycode';
				getObj("vUrl").value = getObj("keycode_"+now_n).innerHTML;
				break;
			case 13:
				getObj("url_liangxiang").style.display = 'none';
				now_n = "";
				var vurl = $("vUrl").value;
				myfav.initXhr();
				var url = "/fav/fav_url.php?action=gettitle&value="+vurl+"&d="+new Date();
				myfav.xhr.open('GET',url,true);
				myfav.xhr.onreadystatechange = function()
				{
					if(myfav.xhr.readyState==4 && myfav.xhr.status==200)
					{
						document.getElementById('vTitle').value = myfav.xhr.responseText;
						myfav.$("title_loading").style.display='none';
					}
				};
				myfav.xhr.send(null);
				break;
		}
	}
}
//监听网址联想管理
var ua = navigator.userAgent;
if(document.attachEvent){
	document.attachEvent("onclick",function (e){
		getObj("url_liangxiang").style.display = "none";
		return ;
	},false);
}else{
	document.addEventListener("click",function (e){
		getObj("url_liangxiang").style.display = "none";
		return ;
	},false);
}
//监听添加分类下拉列表
if(document.attachEvent){
	document.attachEvent("onclick",function (e){
		var t = e.target || e.srcElement;

		if(t.parentNode.tagName == "HTML" || t.parentNode.tagName == "BODY"){
			getObj("add_sort").style.display = "none";
			getObj("add_sort_div").style.display = "none";
			return ;
		}
	},false);
}else{
	document.addEventListener("click",function (e){
		var t = e.target || e.srcElement;
		//alert(t.parentNode.parentNode.parentNode.parentNode.id);
		if(t.parentNode.tagName == "HTML" || t.parentNode.tagName == "BODY"){
			getObj("add_sort_div").style.display = "none";
			getObj("add_sort").style.display = "none";
			return ;
		}
	},false);
}

function changePage(Url){
	My.loadJs(Url);
}
//全选 
function checkAll(e, itemName)
{
	var obj = document.getElementsByName(itemName);
	for (var i=0; i<obj.length; i++)
		obj[i].checked = e.checked;
   
	getObj("c_All_2").checked = e.checked;
	getObj("c_All_1").checked = e.checked;
  
}

//检察添加收藏
function c_add_fav(){
	var tObj = getObj('vTitle');
	var vLen = tObj.value.cnSize();
	if (vLen < 1 || vLen > 20) {
		alert("标题请在1-20个字节以内..");
		tObj.focus();
		return false;
	}
	var uObj = getObj('vUrl');
	var vLen = uObj.value.cnSize();
	if (vLen < 11) {
		alert("链接请在11个字节以上..");
		uObj.focus();
		return false;
	}
	var mObj = getObj('vMK');
	var mLen = mObj.value.cnSize();
	if (mLen > 255) {
		alert("备注字数请少于255个");
		mObj.focus();
		return false;
	}
	return true;
}

//关闭对话框
function closeDiv()
{   
	
	if(isFF){
		location.hash="";
	}
	parent.initURL=0;
	parent.mySet(true);
}
var fNum = 255; //总字数
//换算中文字长
String.prototype.cnSize = function(){
	var arr = this.match(/[^\x00-\xff]/ig);
	return this.length + (arr == null ? 0 : arr.length);
};
//取出部分文字
String.prototype.sub_cnStr = function(num, mode, vvStr){
	if (!/\d+/.test(num)) 
		return (this);
	var str = this.substr(0, num);
	if (!mode) 
		return str;
	var strNum = str.cnSize();
	if (strNum < num) {
		return this;
	}
	else {
		var n = strNum - str.length;
		num = num - parseInt(n / 2);
		return this.substr(0, num) + vvStr;
	}
};
//判断非法字符
function noStr(vStr){
	if (/[\<\>]/i.vStr) 
		return true;
	else 
		return false;
}

//还原html
function HtmlToStr(vStr){
	vStr = vStr.replace(/&amp;/g, "&");
	vStr = vStr.replace(/&quot;/g, '"');
	vStr = vStr.replace(/&#039;/g, "'");
	vStr = vStr.replace(/&lt;/g, "<");
	vStr = vStr.replace(/&gt;/g, ">");
	return vStr;
}

//还原url
function urlToStr(vStr){
	vStr = vStr.replace(/%3C/g, "\<");
	vStr = vStr.replace(/%3E/g, "\>");
	vStr = vStr.replace(/%27/g, "'");
	vStr = vStr.replace(/%22/g, '"');
	vStr = vStr.replace(/\\/g, '/');
	return vStr;
}


//获取通用对象
function getObj(id){
	if (document.getElementById) {
		return document.getElementById(id);
	}
	else 
	if (document.all) {
		return document.all[id];
	}
	else 
	if (document.layers) {
		return document.layers[id];
	}
}





//复制内容至剪切板
function copyToMemory(vStr, vMsg){
	window.clipboardData.setData("Text", vStr);
	alert(vMsg);
	return true;
}



//检察函数
function c_add_sort(){
	var vSort = getObj('vSort');
	var vLen = vSort.value.cnSize();
	if (vLen < 1 || vLen > 12) {
		alert("文件夹请在1-12个字节以内..");
		vSort.focus();
		return false;
	}
	return true;
}


//链接扩展命令
function ext_cmd(){
	var cmd = location.href.split("#");
	if (cmd.length > 1) {
		if (cmd[1] == "mf") 
			show_move_fav();
	}
	return true;
}

function wopen(){
	window.open('export.php', '', 'top=50,left=200,height=300, width=450, toolbar =no, menubar=no, scrollbars=auto, resizable=yes, location=no, status=no');
}



myfav = {
	selectedSort: 'all',
	sortList: [],
	favList: [],
	sortTotalNum: 0,
	sortUnknowNum: 0,
	sortCYNum: 0,
	isLoaded: true,
	isSearch: false,
	nowTab: 1,
	nowPage: 1,
	xhr: null,
	$: function(id){
		if (document.getElementById) {
			return document.getElementById(id);
		}
		else 
		if (document.all) {
			return document.all[id];
		}
		else 
		if (document.layers) {
			return document.layers[id];
		}
	},
	show: function(id){
		myfav.$(id).style.display = 'block';
	},
	hide: function(id){
		myfav.$(id).style.display = 'none';
	},
	loadJs: function(js, callback){
		My.loadJs(js, callback);
	},
	//设置分类列表
	setSortList: function(arr){
		myfav.sortList = arr;
	},
	//设置收藏列表
	setFavList: function(arr){
		myfav.favList = arr;
	},
	//id获取收藏
	getFavById: function(id){
		var fav = myfav.favList;
		if (fav.length == 0) 
			return false;
		for (var i = 0; i < fav.length; i++) {
			if (fav[i][0] == id) {
				return fav[i];
			}
		}
		return false;
	},
	//id获取分类
	getSortById: function(id){
		var sort = myfav.sortList;
		if (sort.length == 0) 
			return false;
		for (var i = 0; i < sort.length; i++) {
			if (sort[i][0] == id) {
				return sort[i];
			}
		}
		return false;
	},
	//显示添加分类
	showAddSort: function(){
		myfav.$('add_sort_font').innerHTML = '新增文件夹';
		myfav.$('add_sort_cmd').value = 'add_sort';
		myfav.$('add_sort_id').value = '';
		myfav.$('del_sort_a').style.display = 'none';
		myfav.initXhr();
		var action = "now_sort";
		var date = new Date();
		var url = "fav_api.php?action="+action+"&cid="+cid+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				myfav.$("cateory_name").innerHTML = myfav.xhr.responseText;
				myfav.$('vSort').value = '';
				cateory_id = cid;
				myfav.showDiv('addNewSort','vSort');
			}
		};
		myfav.xhr.send(null);
	},
	//显示编辑分类
	showEditSort: function(fid,sortid, sortvalue){
		myfav.$('add_sort_font').innerHTML = '修改文件夹';
		myfav.$('vSort').value = sortvalue;
		myfav.$('add_sort_cmd').value = 'edit_sort';
		myfav.$('add_sort_id').value = sortid;
		myfav.$('del_sort_a').style.display = '';
		myfav.initXhr();
		var action = "now_sort";
		var date = new Date();
		var url = "fav_api.php?action="+action+"&cid="+fid+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				myfav.$("cateory_name").innerHTML = myfav.xhr.responseText;
				cateory_id = fid;
				cid = sortid;
				myfav.showDiv('addNewSort','vSort');
			}
		};
		myfav.xhr.send(null);
	},
	//显示添加收藏
	showAddFav: function(){
		//加入查询当先所点击的分类 2011/12/2
		
		myfav.$('vTitle').value = '';
		myfav.$('vUrl').value = '';
		myfav.$('vMK').value = '';
		myfav.$("vId").value = '';
		myfav.$("fav_operation_title").innerHTML = '添加网址';
		myfav.$('url_liangxiang').style.display='none';
		myfav.$('title_loading').style.display='none';
		if(cid==-2){
			myfav.$("addFavRemark").style.display='none';
		}else{
			myfav.$("addFavRemark").style.display='';
		}
		myfav.showDiv('addFav','vUrl');


	},
	//显示编辑收藏
	showEditFav: function(favid){
		//ajax查询网址
		myfav.$('fav_operation_title').innerHTML = '修改网址';
		myfav.$('title_loading').style.display='none';
		myfav.initXhr();
		var action = "select_fav_for_id";
		var date = new Date();
		var sort = myfav.$("vSort").value;
		var url = "fav_api.php?action="+action+"&id="+favid+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				var fav = JSON.parse(myfav.xhr.responseText);
				//alert(typeof(fav));
				myfav.$("vId").value = fav.vId;
				myfav.$('vTitle').value = fav.vTitle;
				myfav.$('vUrl').value = fav.vUrl;
				myfav.$('vMK').value = fav.vMK;
				myfav.$("addFavRemark").style.display='';
				myfav.showDiv('addFav');
				myfav.$("vUrl").focus();
			}
		};
		myfav.xhr.send(null);
	},
	showEditFavRemark: function(favid){
		//ajax查询网址
		myfav.$('title_loading').style.display='none';
		myfav.initXhr();
		var action = "select_fav_for_id";
		var date = new Date();
		var url = "fav_api.php?action="+action+"&id="+favid+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				var fav = JSON.parse(myfav.xhr.responseText);
				//alert(typeof(fav));
				myfav.$("vFavId").value = fav.vId;
				myfav.$('vFavRemark').value = fav.vMK;
				myfav.showDiv('editFavRemark');
				myfav.$("vFavRemark").focus();
			}
		};
		myfav.xhr.send(null);
	},
	//显示删除分类2011/11/28
	showDeleteSort:function(){
		//ajax后台查询当前分类下的网址数量
		myfav.initXhr();
		var action = "get_fav_for_cid";
		var date = new Date();
		var url = "fav_api.php?action="+action+"&cid="+cid+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				var total = myfav.xhr.responseText;
				if (total > 0)
					myfav.showDiv('del_cateory_for_have');
				else
					myfav.showDiv('del_cateory_for_nohave');
			}
		};
		myfav.xhr.send(null);	
	},
	//删除分类
	delSort: function(vKey){
		var st = myfav.$('st' + vKey).innerHTML;
		if (st > 0) {
			alert("删除文件夹前请转移该文件夹下的 " + st + " 收藏。");
			return false;
		}
		else {
			if (confirm('确认删除文件夹？')) {
				var delform = myfav.$('delconfirm');
				myfav.$('delconfirm_id').value = vKey;
				myfav.$('delconfirm_cmd').value = 'del_sort';
				delform.submit();
			}
		}
		return false;
        
	},
	//删除收藏
	delFav: function(vKey){
    
		if (confirm('确认删除这个收藏？')) {
			var delform = myfav.$('delconfirm');
			if (myfav.selectedSort == 'cy') {
            
				myfav.$('delconfirm_id').value = vKey;
				myfav.$('delconfirm_cmd').value = 'del_cy_fav';
			}
			else {
				myfav.$('delconfirm_id').value = vKey;
				myfav.$('delconfirm_cmd').value = 'del_fav';
			}
            
			delform.submit();
		}
        
		return false;
        
	},
	//显示div
	showDiv: function(nDiv){
		var w, h, ow, oh;
		var dbg = myfav.$("divBg");
		w = document.body.clientWidth;
		h = document.body.clientHeight;
		dbg.style.width = w + 'px';
		dbg.style.height = h + 'px';
		dbg.style.display = "block";
        
		var onDiv = myfav.$(nDiv);
		var pw = parseInt(onDiv.style.width.substr(0, (onDiv.style.width.length - 2))); //ff
		if (isNaN(pw)) {
			pw = 0;
		}
		onDiv.style.left = (w - pw) / 2 + "px";
		onDiv.style.top = 250 + "px";
		onDiv.style.display = "block";
		onDiv.disabled = false;
		//		scroll(0,0);
		var s = arguments.length;
		if (s == 2)
			myfav.$(arguments[1]).focus();
		return false;
	},
	//隐藏div
	hideDiv: function(nDiv){
		var s = arguments.length;
		//不关闭背景
		if (s != 2) 
			myfav.$("divBg").style.display = "none";
		var onDiv = myfav.$(nDiv);
		onDiv.style.display = "none";
		onDiv.disabled = true;
		return true;
	},
	favMouseover: function(id){
		myfav.$('fav' + id).className = 'favsa';
		myfav.$('ct__' + id).style.display = '';
	},
	favMouseout: function(id){
		if (myfav.$('check' + id).checked == false) {
			myfav.$('fav' + id).className = '';
		}
		myfav.$('ct__' + id).style.display = 'none';
	},
	favSelectAll: function(fName){
		var ff = myfav.$(fName);
		var sB = myfav.$("favSelectAll");
		var cR = "";
		var liClass = '';
		if (sB.checked == true) {
			liClass = 'favsa';
			cR = true;
		}
		else {
			liClass = '';
			cR = false;
		}
		for (var i = 1; i < ff.elements.length; i++) {
			if (ff.elements[i].type == "checkbox") {
				ff.elements[i].parentNode.className = liClass;
				ff.elements[i].checked = cR;
			}
		}
		return true;
	},
	oncheckFavBox: function(checkb){
		var ff = myfav.$('mmForm');
		var sB = myfav.$("favSelectAll");
		if (checkb.checked == false) {
			sB.checked = false;
		}
		else {
			var num = 0, j = 0;
			for (var i = 1; i < ff.elements.length; i++) {
				if (ff.elements[i].type == "checkbox") {
					num++;
					if (ff.elements[i].checked == true) {
						j++;
					}
				}
			}
			if (num == j) {
				sB.checked = true;
			}
		}
        
	},
	html: function(id, str){
		//alert(str);
		(myfav.$(id)).innerHTML = str;
		if(search !='')
		{
			cid = 0;
			myfav.$("search").value = search;
		}
	},
	msg: function(m){
		var msgDiv = myfav.$('show_msg');
		myfav.html('show_msg', m);
		jq("#show_msg").show("slow");
		setTimeout(function(){
			jq("#show_msg").fadeOut("slow");
		}, 2000);
	},
	setPage: function(str){
		myfav.html('thePage', str);
	},
	getData: function(url, callback){
		var _callBack = arguments[1] ||
		function(){
		};
		var _url = url;
		if (myfav.xhr == null) 
			myfav.initXhr();
		if (myfav.xhr) {
			myfav.xhr.abort();
			myfav.xhr.onreadystatechange = _callBack;
			myfav.xhr.open("GET", _url, true);
			myfav.xhr.send(null);
		}
	},
	initXhr: function(){
		if (window.XMLHttpRequest) {
			try {
				myfav.xhr = new XMLHttpRequest();
			} 
			catch (e) {
				myfav.xhr = false;
			}
		}
		else 
		if (window.ActiveXObject) {
			try {
				myfav.xhr = new ActiveXObject("Msxml2.XMLHTTP");
			} 
			catch (e) {
				try {
					myfav.xhr = new ActiveXObject("Microsoft.XMLHTTP");
				} 
				catch (e) {
					myfav.xhr = false;
				}
			}
		}
	},
	favMoveOrder: function(id, op){
		var favmoveupdown = myfav.$('favmoveupdown');
		myfav.$('favmoveupdown_id').value = id;
		myfav.$('favmoveupdown_op').value = op;
		favmoveupdown.submit();
		return false;
	},
	checkMKNum: function(vObj, fNum, myevent){
		var nowNum = fNum - vObj.value.cnSize();
		if (nowNum < 0) {
			nowNum = 0;
			var event = myevent || window.event;
			var  keycode=event.keyCode;
			if(keycode != 8 && keycode != 13)
				alert("该内容已经达到最大字数限制..\n您的文字将在 " + fNum + " 个字节后被截取..");
			var text = vObj.value.sub_cnStr(fNum);
			vObj.value = text;
			return false;
		}
	},
	searchEnter: function(myevent){
		var event = myevent || window.event;
		if (event.keyCode == 13) {
			var button = document.getElementById("search_button"); //bsubmit 为botton按钮的id 
			button.click();
			return false;
		}
	},
	//全角转半角
	DBC2SBC: function(str){
		var result = "";
		for (var i = 0; i < str.length; i++) {
			code = str.charCodeAt(i);//获取当前字符的unicode编码
			if (code >= 65281 && code <= 65373)//在这个unicode编码范围中的是所有的英文字母已经各种字符
			{
				var d = str.charCodeAt(i) - 65248;
				result += String.fromCharCode(d);//把全角字符的unicode编码转换为对应半角字符的unicode码
			}
			else 
			if (code == 12288)//空格
			{
				var d = str.charCodeAt(i) - 12288 + 32;
				result += String.fromCharCode(d);
			}
			else {
				result += str.charAt(i);
			}
		}
		return result;
	},
	getAllDate:function(type,id){
		var result = null,s = '';
		if(cid == id&&id!=0){
			return;
		}
		cid = id;
		if(cid == -2){
			My.loadJs('/api/get_server_site_str.php?d='+new Date(),function(){
				T.siteClicks.show();
			});
			return;
		}
		if(is_browser){
			My.loadJs("/fav/fav_api.php?action=getData&id="+id+"&s="+s+"&is_browser&d="+new Date());
		}else{
			My.loadJs("/fav/fav_api.php?action=getData&id="+id+"&s="+s+"&d="+new Date());
		}
		var container= document.getElementById("all_sort_list");
		for(var i=0,len=container.getElementsByTagName("li").length;i<len;i++){
			var var_id = container.getElementsByTagName("li")[i].id;
			if(var_id != "cateorys_-2" && var_id != 'cateorys_0'){
				container.getElementsByTagName("li")[i].className = "open";
			}
		}
		document.getElementById("cateorys_0").className = "open myfavtit";
		if(document.getElementById("cateorys_-2")){
			document.getElementById("cateorys_-2").className = "open import";
		}
		if(id == 0){
			document.getElementById("cateorys_0").className = "open myfavtit cur";
		}else if(document.getElementById("cateorys_"+id)){
			document.getElementById("cateorys_"+id).className = "open cur";
		}
	},
	edit:function(){
		var all_site = myfav.$("list_n_1");
		if(myfav.$("state").innerHTML == "编辑"){
			myfav.$("state").innerHTML = "保存";
			all_site.className = "list_n_1 list_n_nomarg";
		}else{
			myfav.$("state").innerHTML = "编辑";
			all_site.className = "list_n_1";
		}
		for(var i = 0 ;i<all_site.getElementsByTagName("input").length;i++){
			if(all_site.getElementsByTagName("input")[i].style.display == "none")
				all_site.getElementsByTagName("input")[i].style.display = "";
			else
				all_site.getElementsByTagName("input")[i].style.display = "none"
		}
		if(myfav.$("list_n_2").style.display == "none")
			myfav.$("list_n_2").style.display = "";
		else
			myfav.$("list_n_2").style.display = "none";
	},
	fav_div_cateory:function(divName,div_name,block_div,type){
		//处理ajax加载全部分类
		if(myfav.$(divName).style.display == "none"){
			myfav.initXhr();
			var action = "all_sort";
			var date = new Date();
			var sort = myfav.$("vSort").value;
			if(myfav.$('add_sort_cmd').value == "add_sort"){
				var url = "fav_api.php?action="+action+"&in_name="+div_name+"&div_name="+divName+"&sa=0&cid="+cid+"&block_div="+block_div+"&type="+type+"&d="+date;
			}else{
				var url = "fav_api.php?action="+action+"&in_name="+div_name+"&div_name="+divName+"&sa=1&cid="+cid+"&block_div="+block_div+"&type="+type+"&d="+date;
			}
			myfav.xhr.open("POST", url, true);
			myfav.xhr.onreadystatechange = function(){
				//匿名函数
				if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
					if(type !='')
					{
						if(type =='lebie')
						{
							myfav.$(divName).innerHTML = '<li><a href="javascript:;" onclick="myfav.select_sort(this.innerHTML,0,\''+div_name+'\',\''+divName+'\',\''+block_div+'\');myfav.$(\'lebie_id\').value=0;">未分类</a></li>'+myfav.xhr.responseText;					
						}
						else
						{
							myfav.$(divName).innerHTML = '<li><a href="javascript:;" onclick="myfav.select_sort(this.innerHTML,0,\''+div_name+'\',\''+divName+'\',\''+block_div+'\');myfav.$(\'lebie_id\').value=0;">我的收藏夹</a></li>'+myfav.xhr.responseText;
						}
					}
					else
					{
						myfav.$(divName).innerHTML = '<li><a href="javascript:;" onclick="myfav.select_sort(this.innerHTML,0,\''+div_name+'\',\''+divName+'\',\''+block_div+'\')">我的收藏夹</a></li>'+myfav.xhr.responseText;
					}
					myfav.$(divName).style.display = "";
					myfav.$(block_div).style.display = "";
				}
			};
			myfav.xhr.send(null);
		}else{
			myfav.$(divName).style.display = "none"
			myfav.$(block_div).style.display = "none";
		}

	},
	select_sort:function(html,cid,in_name,div_name,block_div){
		myfav.$(in_name).innerHTML = html;
		cateory_id = cid;
		myfav.$(div_name).style.display = "none";
		myfav.$(block_div).style.display = "none";
	//alert(cateory_id);
	},
	add_sort:function(){
		if(myfav.$("vSort").value == ""){
			alert("请填写文件夹名称...");
			return false;
		}
		myfav.initXhr();
		var sortid = myfav.$('add_sort_id').value;
		var action = myfav.$('add_sort_cmd').value;
		var date = new Date();
		var sort = myfav.$("vSort").value;
		var url = "fav_api.php?action="+action+"&name="+sort+"&fid="+cateory_id+"&cid="+sortid+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				if(myfav.xhr.responseText == "error"){
					//添加了重复的URL
					alert("重复记录 添加失败");
					myfav.hideDiv('addNewSort');
					return false;
				}
				window.location = "/fav/index.php?cid="+myfav.xhr.responseText;
			}
		};
		myfav.xhr.send(null);
	},
	add_fav:function(){
		if(myfav.$("vUrl").value == ""){
			alert("请填写网站地址...");
			return false;
		}
		var vTitle = myfav.$("vTitle").value;
		var vUrl   = escape(myfav.$("vUrl").value.replace("http://","").replace("https://",""));
		var vMK    = myfav.$("vMK").value;
		var vID	   = myfav.$("vId").value;
		if(vID == "")
			var action = "add_fav";
		else
			var action = "edit_fav";
		if(cid==-2){
			T.siteClicks.set(action,[vUrl,vTitle,vID]);
			return;
		}
		myfav.initXhr();
		var date = new Date();
		if(cateory_id == 0){
			cateory_id = cid;
		}
		var url = "fav_api.php?action="+action+"&vTitle="+vTitle+"&vUrl="+vUrl+"&vMK="+vMK+"&cid="+cateory_id+"&id="+vID+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				if(myfav.xhr.responseText == "error"){
					//添加了重复的URL
					alert("标题重复  添加失败");
					myfav.hideDiv('addFav');
					return false;
				}
				window.location = "index.php?cid="+cid;
			}
		};
		myfav.xhr.send(null);
	},
	edit_fav_remark:function(){
		var vID	   = myfav.$("vFavId").value;
		var vMK    = myfav.$("vFavRemark").value;
		var action = "edit_fav_remark";
		myfav.initXhr();
		var date = new Date();
		var url = "fav_api.php?action="+action+"&vMK="+vMK+"&id="+vID+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				window.location = "index.php?cid="+cid;
			}
		};
		myfav.xhr.send(null);
	},
	del_fav:function(id){
		if(confirm("确认删除网址？")){
			myfav.initXhr();
			var action = "del_fav";
			var date = new Date();
			var url = "fav_api.php?action="+action+"&vId="+id+"&d="+date;
			myfav.xhr.open("POST", url, true);
			myfav.xhr.onreadystatechange = function(){
				//匿名函数
				if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
					window.location = "index.php?cid="+cid;
				}
			};
			myfav.xhr.send(null);
		}
	},
	del_fav_more:function(){
		//del_fav_more_ids
		if(del_fav_more_ids == "" || del_fav_more_ids == ";" || del_fav_more_ids == ";;"){
			alert("请选择你要删除的网址...");
			del_fav_more_ids = "";
			return false;
		}
		if(confirm("确认删除选中的网址？")){
			myfav.initXhr();
			var action = "del_fav_more";
			var date = new Date();
			var url = "fav_api.php?action="+action+"&ids="+del_fav_more_ids+"&d="+date;
			myfav.xhr.open("POST", url, true);
			myfav.xhr.onreadystatechange = function(){
				//匿名函数
				if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
					window.location = "index.php?cid="+cid;
				}
			};
			myfav.xhr.send(null);
		}
	},
	more_fav:function(id){
		//alert(myfav.$(id).checked);
		if(myfav.$(id).checked == true){
			if(del_fav_more_ids == "")
				del_fav_more_ids += ";"+myfav.$(id).value+";";
			else
				del_fav_more_ids += myfav.$(id).value+";";
		}else{
			del_fav_more_ids = ReplaceAll(del_fav_more_ids, ";"+myfav.$(id).value, '');
		}
	//alert(del_fav_more_ids);
	},
	all_select:function(){
		//将全部网址加到del_fav_more_ids里面
		//alert(document.frm.test.getAttribute("type"));
		if(myfav.$("c_All_1").checked == true){
			var fav_table = myfav.$("fav_table");
			for(var i = 0 ;i<fav_table.getElementsByTagName("input").length;i++){
				if(fav_table.getElementsByTagName("input")[i].getAttribute("type") == "checkbox"){
					var id = fav_table.getElementsByTagName("input")[i].id;
					if(del_fav_more_ids == "")
						del_fav_more_ids += ";"+myfav.$(id).value+";";
					else
						del_fav_more_ids += myfav.$(id).value+";";
				}
			}
		}else{
			del_fav_more_ids = "";
		}
	},
	fav_move:function(fav_id){
		myfav.$("fav_id").value=fav_id;
		myfav.$("lebie_id").value = -1;
		//设置移动网址的种类，单个移动
		myfav.$("url_type").value=0;
		myfav.showDiv('move_url');
	},
	all_fav_move:function(lebie_id){
		//设置移动网址的种类，选中全部移动
		if(del_fav_more_ids == "" || del_fav_more_ids == ";" || del_fav_more_ids == ";;")
		{
			alert("请选择您要移动的网址...");
			del_fav_more_ids = "";
			return false;
		}
		myfav.$("url_type").value=1;
		myfav.$("lebie_id").value = lebie_id;
		myfav.showDiv('move_url');
	},
	move_url:function(){
		var url_type = myfav.$("url_type").value;
		var lebie_id = myfav.$("lebie_id").value;
		//移动单个网址
		if(url_type == 0)
		{
			var fav_id = myfav.$("fav_id").value
			myfav.initXhr();
			var action = "fav_move";
			var date = new Date();
			var url = "fav_api.php?action="+action+"&id="+fav_id+"&cid="+lebie_id+"&d="+date;
			myfav.xhr.open("POST", url, true);
			myfav.xhr.onreadystatechange = function(){
				//匿名函数
				if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
					var text=myfav.xhr.responseText;
					var arr =text.split(",");
					if(arr.length==2)
					{
						alert("("+arr[1]+")标题重复  添加失败");
						return false;
					}else{
						window.location = "index.php?cid="+lebie_id;
					}
				}
			};
			myfav.xhr.send(null);	
		}
		//批量移动网址
		else
		{
			myfav.initXhr();
			var action = "all_fav_move";
			var date = new Date();
			var ajax_url = "fav_api.php?action="+action+"&ids="+del_fav_more_ids+"&cid="+lebie_id+"&d="+date;
			myfav.xhr.open("POST", ajax_url, true);
			myfav.xhr.onreadystatechange = function(){
				//匿名函数
				if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
					window.location = "index.php?cid="+lebie_id;
				}
			};
			myfav.xhr.send(null);	
		}
	},
	cateort_manage:function(type){
		if(type == 1){
			//显示和隐藏管理分类的下拉列表
			if(myfav.$("cateort_manage").style.display == "none" && myfav.$("cateort_manage_not").style.display == "none"){
				if(cid == 0 || cid == "" || cid == -1 || cid == -2 ){
					//2011/11/28
					myfav.$("cateort_manage_not").style.display = "";
				//myfav.$("ca").style.display = "";
				}else{
					myfav.$("cateort_manage").style.display = "";
				}
			}else{
				myfav.$("cateort_manage").style.display = "none";
				myfav.$("cateort_manage_not").style.display = "none"
				myfav.$("ca").style.display = "none";
			}
		}else if(type == 2){
			//重命名操作
			//2011/11/28 ajax提交重命名
			myfav.initXhr();
			var newName = myfav.$("newName").value;
			var action = "rename_cateory";
			var date = new Date();
			var url = "fav_api.php?action="+action+"&rename="+newName+"&cid="+cid+"&d="+date;
			myfav.xhr.open("POST", url, true);
			myfav.xhr.onreadystatechange = function(){
				//匿名函数
				if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
					window.location = "index.php";
				}
			};
			myfav.xhr.send(null);
		}else if(type == 3){
			//移动分类2011/11/28
			if(cid == cateory_id){
				
				myfav.hideDiv("move_cateory");
				return false;
			}
			myfav.initXhr();
			var newName = myfav.$("newName").value;
			var action = "move_cateory";
			var date = new Date();
			var url = "fav_api.php?action="+action+"&moveid="+cateory_id+"&cid="+cid+"&d="+date;
			myfav.xhr.open("POST", url, true);
			myfav.xhr.onreadystatechange = function(){
				//匿名函数
				if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
					window.location = "index.php";
				}
			};
			myfav.xhr.send(null);
		}
	},
	del_cateory:function(){
		//删除分类2011/11/28
		myfav.initXhr();
		var action = "del_cateory";
		var date = new Date();
		var url = "fav_api.php?action="+action+"&cid="+cid+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				window.location = "index.php";
			}
		};
		myfav.xhr.send(null);
	},
	more_del_cateory:function(){
		if(myfav.$("only_cateory").checked == true){
			var type = 1;
		}
		if(myfav.$("all_cateory").checked == true){
			var type = 2;
		}
		myfav.initXhr();
		var action = "more_del_cateory";
		var date = new Date();
		var url = "fav_api.php?action="+action+"&cid="+cid+"&type="+type+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				window.location = "index.php";
			}
		};
		myfav.xhr.send(null);
	},
	search:function(){
		var s = '';
		if(myfav.$("search").value != "" && myfav.$("search").value != "在收藏的网址中搜索"){
			s =  myfav.$("search").value;
		}else{
			alert('请输入您要搜索的网站名称或网址');
			return false;
		}
		//当ID等于0的时候则显示全部分类	
		if(is_browser){
			My.loadJs("/fav/fav_api.php?action=getData&s="+s+"&type="+show_way+"&is_browser&d="+new Date());
		}else{
			My.loadJs("/fav/fav_api.php?action=getData&s="+s+"&type="+show_way+"&d="+new Date());
		}
	},
	ulShow:function(cid){
		flag++;
		if(cid == 0){
			return false;
		}  
		
		myfav.initXhr();
		var action = "get_sun_id";
		var date = new Date();
		var url = "fav_api.php?action="+action+"&cid="+cid+"&d="+date;
		myfav.xhr.open("POST", url, true);
		myfav.xhr.onreadystatechange = function(){
			//匿名函数
			if(myfav.xhr.readyState==4 && myfav.xhr.status==200){
				if(myfav.xhr.responseText != 'null'){
					var responseText = JSON.parse(myfav.xhr.responseText);
					for (var i=0; i<responseText.cid.length; i++){
						var var_id = myfav.$("cateorys_"+responseText.cid[i].cid);
						if(var_id.style.display == "none"){
							var_id.style.display = "";
							myfav.$("cateorys_"+cid).className = "open";
						}else{
							var ids = responseText.cids;
							var ids_arr= new Array(); //定义一数组
							ids_arr=ids.split(","); //字符分割
							for(var j = 0;j<ids_arr.length;j++){
								myfav.$("cateorys_"+ids_arr[j]).style.display = "none";
								myfav.$("cateorys_"+ids_arr[j]).className = "close";

							}
							return;
						}
					}
				}
			}
		};
		myfav.xhr.send(null);
	},
	titleup:function(url){
		My.loadJs("/fav/fav_api.php?action=geturl&value="+url.value+"&d="+new Date());
	},
	vurlfoc:function(){
		var url = $("vTitle").value;
		alert(url);
	},
	updateNum:function(id, num){
		var liObj = document.getElementById("cateorys_"+id);
		if(typeof(liObj) != "undefined"){
			liObj.getElementsByTagName("span")[0].innerHTML = '('+num+')';
		}
	}
};
myfav.getAllDate(show_way,default_cid);

if(document.attachEvent){
	document.getElementById('vUrl').attachEvent('onkeyup',function(o){
		var vurl = document.getElementById("vUrl").value;
		My.loadJs("/fav/fav_url.php?action=geturl&value="+vurl+"&d="+new Date());
	});
}else{
	var urla = false;
	document.getElementById('vUrl').oninput=function(){
		if(!urla)
		{
			urla = true;
			var vurl = document.getElementById("vUrl").value;
			My.loadJs("/fav/fav_url.php?action=geturl&value="+vurl+"&d="+new Date());
			window.setTimeout(function(){
				urla = false;
			},10);
		}
	}
}
document.getElementById('auto_get_title').onclick = function(){
	var vurl = $("vUrl").value;
	if(vurl == '')
	{
		alert('请选输入网址');
		myfav.$('vUrl').focus();
		return;
	}
	myfav.$("title_loading").style.display='';
	myfav.initXhr();
	var url = "/fav/fav_url.php?action=gettitle&value="+vurl+"&d="+new Date();
	myfav.xhr.open('GET',url,true);
	myfav.xhr.onreadystatechange = function()
	{
		if(myfav.xhr.readyState==4 && myfav.xhr.status==200)
		{
			document.getElementById('vTitle').value = myfav.xhr.responseText;
			myfav.$("title_loading").style.display='none';
		}
	};
	myfav.xhr.send(null);
}
function ReplaceAll(str, sptr, sptr1) {
	while (str.indexOf(sptr) >= 0) {
		str = str.replace(sptr, sptr1);
	}
	return str;
}
if (typeof(HTMLElement) != "undefined") //给firefox定义contains()方法，ie下不起作用
{
	HTMLElement.prototype.contains = function(obj){
		while (obj != null && typeof(obj.tagName) != "undefind") {
			if (obj == this) 
				return true;
			obj = obj.parentNode;
		}
		return false;
	};
}
var JSON;
if (!JSON) {
	JSON = {}
} (function() {
	"use strict";
	function f(n) {
		return n < 10 ? '0' + n: n
	}
	if (typeof Date.prototype.toJSON !== 'function') {
		Date.prototype.toJSON = function(a) {
			return isFinite(this.valueOf()) ? this.getUTCFullYear() + '-' + f(this.getUTCMonth() + 1) + '-' + f(this.getUTCDate()) + 'T' + f(this.getUTCHours()) + ':' + f(this.getUTCMinutes()) + ':' + f(this.getUTCSeconds()) + 'Z': null
		};
		String.prototype.toJSON = Number.prototype.toJSON = Boolean.prototype.toJSON = function(a) {
			return this.valueOf()
		}
	}
	var e = /[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
	escapable = /[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
	gap,
	indent,
	meta = {
		'\b': '\\b',
		'\t': '\\t',
		'\n': '\\n',
		'\f': '\\f',
		'\r': '\\r',
		'"': '\\"',
		'\\': '\\\\'
	},
	rep;
	function quote(b) {
		escapable.lastIndex = 0;
		return escapable.test(b) ? '"' + b.replace(escapable, 
			function(a) {
				var c = meta[a];
				return typeof c === 'string' ? c: '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice( - 4)
			}) + '"': '"' + b + '"'
	}
	function str(a, b) {
		var i,
		k,
		v,
		length,
		mind = gap,
		partial,
		value = b[a];
		if (value && typeof value === 'object' && typeof value.toJSON === 'function') {
			value = value.toJSON(a)
		}
		if (typeof rep === 'function') {
			value = rep.call(b, a, value)
		}
		switch (typeof value) {
			case 'string':
				return quote(value);
			case 'number':
				return isFinite(value) ? String(value) : 'null';
			case 'boolean':
			case 'null':
				return String(value);
			case 'object':
				if (!value) {
					return 'null'
				}
				gap += indent;
				partial = [];
				if (Object.prototype.toString.apply(value) === '[object Array]') {
					length = value.length;
					for (i = 0; i < length; i += 1) {
						partial[i] = str(i, value) || 'null'
					}
					v = partial.length === 0 ? '[]': gap ? '[\n' + gap + partial.join(',\n' + gap) + '\n' + mind + ']': '[' + partial.join(',') + ']';
					gap = mind;
					return v
				}
				if (rep && typeof rep === 'object') {
					length = rep.length;
					for (i = 0; i < length; i += 1) {
						if (typeof rep[i] === 'string') {
							k = rep[i];
							v = str(k, value);
							if (v) {
								partial.push(quote(k) + (gap ? ': ': ':') + v)
							}
						}
					}
				} else {
					for (k in value) {
						if (Object.prototype.hasOwnProperty.call(value, k)) {
							v = str(k, value);
							if (v) {
								partial.push(quote(k) + (gap ? ': ': ':') + v)
							}
						}
					}
				}
				v = partial.length === 0 ? '{}': gap ? '{\n' + gap + partial.join(',\n' + gap) + '\n' + mind + '}': '{' + partial.join(',') + '}';
				gap = mind;
				return v
		}
	}
	if (typeof JSON.stringify !== 'function') {
		JSON.stringify = function(a, b, c) {
			var i;
			gap = '';
			indent = '';
			if (typeof c === 'number') {
				for (i = 0; i < c; i += 1) {
					indent += ' '
				}
			} else if (typeof c === 'string') {
				indent = c
			}
			rep = b;
			if (b && typeof b !== 'function' && (typeof b !== 'object' || typeof b.length !== 'number')) {
				throw new Error('JSON.stringify')
			}
			return str('', {
				'': a
			})
		}
	}
	if (typeof JSON.parse !== 'function') {
		JSON.parse = function(c, d) {
			var j;
			function walk(a, b) {
				var k,
				v,
				value = a[b];
				if (value && typeof value === 'object') {
					for (k in value) {
						if (Object.prototype.hasOwnProperty.call(value, k)) {
							v = walk(value, k);
							if (v !== undefined) {
								value[k] = v
							} else {
								delete value[k]
							}
						}
					}
				}
				return d.call(a, b, value)
			}
			c = String(c);
			e.lastIndex = 0;
			if (e.test(c)) {
				c = c.replace(e, 
					function(a) {
						return '\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice( - 4)
					})
			}
			if (/^[\],:{}\s]*$/.test(c.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g, '@').replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g, ']').replace(/(?:^|:|,)(?:\s*\[)+/g, ''))) {
				j = eval('(' + c + ')');
				return typeof d === 'function' ? walk({
					'': j
				},
				'') : j
			};
			throw new SyntaxError('JSON.parse');
		}
	}
} ());

function getElementsByName(tag,eltname){
	var elts=document.getElementsByTagName(tag);
	var count=0;
	var elements=[];
	for(var i=0;i<elts.length;i++){
		if(elts[i].getAttribute("name")==eltname){
			elements[count++]=elts[i];
		}
	}
	return elements;
}
