{{include file = "public/mem_header.tpl.html"}}
{{include file = "public/left_nav.tpl.html}}
<style type="text/css">
    .nickname-tip {
        margin: 2px 0 10px 94px;
    }
</style>
<!--主体左侧 结束-->
<!--主体右侧 开始-->
<div class="account_right" style="display: none" id="ie678Flash">
    <h2 class="pf" style=" margin-bottom:30px;">修改头像</h2>
    <div id="altContent"></div>
    <script type="text/javascript" src="/js/FaustCplus/swfobject.js"></script>
    <script type="text/javascript">
        var forward = '{{$pageArray.forward}}';
        function uploadevent(status) {
            status += '';
            switch (status) {
                case '1':
                    alert("头像修改成功！");
                    try {
                        if (window.external.RCCoralOnlineFavPage("DetailVersion") >= 3.0 && window.external.RCCoralOnlineFavPage("passid") && '{{$smarty.const.PASSID}}' == window.external.RCCoralOnlineFavPage("passid")) {
                            window.external.RCCoralOnUserHeadImageChanged();
                        }
                    } catch (e) {
                        try {
                            if (chrome.sync.onlineFavPage("passid") && '{{$smarty.const.PASSID}}' == chrome.sync.onlineFavPage("passid")) {
                                chrome.sync.onUserHeadImageChanged();
                            }
                        } catch (e) {
                        }
                    }
                    if(forward!=''){
                        window.location = forward;
                    }else{
                        window.location.reload();
                    }
                    break;
                case '-1':
                    window.location = "/member/edit_info.php";
                    break;
                case '-2':
                    alert("上传失败！");
                    window.location.reload();
                    break;
                default:
                    alert(typeof(status) + ' ' + status);
            }
        }
        function langFunc() {
            return {
                "CX0189": "您上传的头像会自动生成三种尺寸\n请注意中小尺寸的头像是否清晰",
                "CX0193": "仅支持JPG、PNG图片文件，且文件小于2M"
            };
        }
        var flashvars = {
            "jsfunc": "uploadevent",
            "jslang": "langFunc",
            "imgUrl": "{{$pageArray.flashImgUrl}}",
            "pid": "75642723",
            "uploadSrc": false,
            "showBrow": true,
            "showCame": true,
            "uploadUrl": "{{$pageArray.uploadUrl}}",
            "uploadTmpUrl": "{{$pageArray.uploadTmpUrl}}",
            "pSize": "300|300|200|200|120|120|48|48"
        };
        var params = {
            menu: "false",
            scale: "noScale",
            allowFullscreen: "true",
            allowScriptAccess: "always",
            wmode: "transparent",
            bgcolor: "#FFFFFF"
        };
        var attributes = {
            id: "FaustCplus"
        };
        swfobject.embedSWF("/js/FaustCplus/FaustCplus.swf?v=********", "altContent", "750", "500", "9.0.0", "expressInstall.swf", flashvars, params, attributes);
    </script>
</div><!--account_right end-->
<!--main end-->
<!--主体 end-->

<link rel="stylesheet" href="/css/mycropper.css?v={{$smarty.const.STATIC_FILE_VERSION}}">

<div class="account_right" style="display: none" id="baseJsUpload">
    <div style="display :none">
        <h2 class="pf" style=" margin-bottom:30px;">基础信息昵称</h2>
        <ul class="conUl">
            <li>
                <span class="conTit">昵称：</span>
                <div class="coninput w318">
                    <input class="defaultInput" type="text" id="nickname" name="nickname" onblur="checkNickname();return false;"
                           autocomplete="off" value="{{if $pageArray.nickname != ''}}{{$pageArray.nickname}}{{else}}{{$pageArray.nicknameTips}}{{/if}}"  title="{{$pageArray.nicknameTips}}" maxlength="20"/>
                </div>
            </li>
        </ul>
        <div class="nickname-tip " id ="nickname-tip">
            请注意：设置或修改昵称后，将会进行违规检测，若涉及不正当用词或违反国家规定，设置内容将不会生效。
        </div>
    </div>
    <h2 class="pf" style=" margin-bottom:30px;">基础信息头像</h2>
    <div class="choose-img">
        <div class="a-uplod">
            <span href="javascript:;">选择图片</span>
            <input type="file" value="选择图片" id="uploadFile" value="{{$pageArray.imgUrl}}">
        </div>
        <div class="error-tip">
            仅支持png、jpg格式且不超过1M大小的图片
        </div>
    </div>
    <div class="crop">
        <div class="crop-content">
            <div class="crop_image_content" id="cropBox">
                <div>
                    <img src="{{$pageArray.imgUrl}}" class="crop-images" id="cropImagesId">
                </div>
                <!-- 剪切框 -->
                <div class="cropper_modal"></div>
                <div class="crop_box" id="mainBox">
                    <div class="crop_box_image">
                        <img src="{{$pageArray.imgUrl}}" alt="" width="300px">
                    </div>
                    <div class="crop_box-jshack" id="mainBoxJsHack">
                        <span id="right-down" class="minBox right-down" move-to="right-down"></span>
                    </div>
                </div>
            </div>
            <div class="preview-box clearfix">
                <div class="preview-title">
                    您上传的头像会自动生成三种尺寸<br/>
                    请注意中小尺寸的头像是否清晰
                </div>
                <div class="preview-list ">
                    <div class="preview-item preview-item-1">

                    </div>
                    <div class="preview-item preview-item-2">

                    </div>
                    <div class="preview-item preview-item-3">

                    </div>
                </div>
                <span class="tip tip1">
            200*200像素
          </span>
                <span class="tip tip2">
            120*120像素
          </span>
                <span class="tip tip3">
            48*48像素
          </span>
            </div>
        </div>
        <div class="crop-submit">
            <button id="crop-sure">保存</button>
            <button id="crop-cancel">取消</button>
        </div>

    </div>
</div>
{{include file = "public/qrcode.tpl.html"}}
<script src="/js/jquery-ui-1.10.4.custom.min.js"></script>
<script src="/js/mycropper.js"></script>
<!--[if IE 6]>
<script src="/js/DD_belatedPNG_0.0.8a-min.js"></script>
<script type="text/javascript">
    DD_belatedPNG.fix('.png24');
</script>
<![endif]-->

<script>
    if ( $("#nickname").val() !== $("#nickname").attr("title")) {
        $("#nickname").addClass("colorInput");
    }
    function checkNickname(n) {
        var nickname = $("#nickname").val()

        if ($("#nickname").val() !== $("#nickname").attr("title") && $("#nickname").val() != "") {
            var formData = new FormData();
            formData.append("nickname", nickname);
            $.ajax({
                url: "/member/avatar/checkNickname",
                type: "POST",
                data: formData,
                /**  *必须false才会自动加上正确的Content-Type  */
                contentType: false,
                /**  * 必须false才会避开jQuery对 formdata 的默认处理  * XMLHttpRequest会对 formdata 进行正确的处理  */
                processData: false,
                success: function (data) {
                    if (data.code != "200") {
                        var errorMsg = (data.code == 3000016? "昵称涉及敏感词，请修改": data.msg)
                        $("#nickname-tip").html(errorMsg)
                        $("#nickname-tip").addClass("ts-error")
                    } else {
                        $("#nickname-tip").html("请注意：设置或修改昵称后，将会进行违规检测，若涉及不正当用词或违反国家规定，设置内容将不会生效。")
                        $("#nickname-tip").removeClass("ts-error")
                    }
                },
                error: function () {
                    alert("修改失败！");
                }
            });
        }
    }
    if (safariVersion != 'undefined' && safariVersion <= 9) {
        $('#baseJsUpload').hide();
        $('#ie678Flash').show();
    } else {
        $('#ie678Flash').hide();
        $('#baseJsUpload').show();

    }
    $(function()
    {
        //用户头像hover
        $(".user_pic").mouseover(function()
        {
            $(this).find("span").show();
        }).mouseleave(function()
        {
            $(this).find("span").hide();
        });

        //关闭提示信息
        $(".btn-close").bind("click",function()
        {
            $(this).parent(".remark").hide();
        });
    });//JQ
    $(".defaultInput").bind(
        {
            focus: function () {
                $(this).parent().removeClass("inputTxtError");
                $(this).parent().addClass("inputTxtFocus");
                if ($(this).val() == $(this).attr("title")) {
                    $(this).val("");
                }
                $(this).addClass("colorInput");
            },
            blur: function () {
                $(this).parent().removeClass("inputTxtFocus");
                if ($.trim($(this).val()) == "") {
                    $(this).val($(this).attr("title"));
                    $(this).removeClass("colorInput");
                }
                ;

            }
        }
    );
</script>
<!--底部 begin-->
</div>
{{include file = "public/footer.tpl.html"}}
