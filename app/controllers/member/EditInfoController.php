<?php

use WebLogger\Facade\LoggerFacade;

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:EditinfoController.php
 * 摘    要:
 * 作    者:<EMAIL>
 * 修改日期: 2015/11/11
 */
class EditInfoController extends Controller
{
    private $pageArray;

    public function __construct()
    {
        $this->pageArray['page'] = 'userIndex';
    }

    /**
     * 用户信息页
     *
     * @return void
     */
    public function actionIndex()
    {
        $loginAction = loadAction('login');
        $loginInfo = $loginAction->checkAuthCookie();
        if (strpos($_SERVER["HTTP_REFERER"], "/member/avatar") != 0)
        {
            $this->pageArray['refreshAvatarTime'] = time();
        }
        if ($_GET["msg"])
        {
            setcookie("OAuthMsg", $_GET["msg"], time() + 3600);
            redirect("/member/editInfo");
            exit;
        }
        $memberModel = loadModel("member");
        //取得用户信息
        $avatarStatus = $memberModel->isDelete(PASSID);
        //获取用户修改密码时间
        $pwdChg = $memberModel->chgPwdLog(PASSID);
        $score = 100;//得分初始值
        if (empty($pwdChg['time']))
        {
            $logRegTime = $memberModel->readRegLoginTime(PASSID);
        }
        $lastPwdTime = empty($pwdChg['time']) ? strtotime($logRegTime['reg_time']) : $pwdChg['time'];
        $this->pageArray['isPwdOld'] = ((time() - $lastPwdTime) > 15724800) ? true : false;
        if ($this->pageArray['isPwdOld'])
        {
            $score -= 20;
        }
        if (intval($avatarStatus['status']) == 2)
        {
            $this->pageArray['deleted'] = true;
        }

        $redis = RedisEx::getInstance();
        $loginLogs = $redis->zRevRangeByScore("LSSP:" . PASSID, time(), 0, array('withscores' => true));
        $commonArea = array();
        foreach ($loginLogs as $log => $logtime)
        {
            list($ip, $from, $browser, $system) = explode("|", $log);
            $cityId = getCityId($ip);
            $area = getAreaNameByIpLib($ip);
            $commonArea[$area] = is_null($commonArea[$area]) ? 0 : $commonArea[$area];
            $commonArea[$area] ++;
        }
        $this->pageArray['isUncommonarea'] = count($commonArea) > 1;

        $OAuthBinds = array("weibo" => false, "qq" => false);
        $oauthModel = loadModel('oauth');
        $result = $oauthModel->getBindByPassid(PASSID);
        !is_array($result) && $result = [];
        foreach ($result as $row)
        {
            $OAuthBinds[$row["type"]] = $row;
        }
        $this->pageArray["OAuthBinds"] = $OAuthBinds;
        if (!$OAuthBinds['qq'])
        {
            $score -= 5;
        }
        $redirectUrl = "";
        if ($_COOKIE["OAuthMsg"])
        {
            $host = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
            preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $host, $matches);
            if ($matches[0] == "2345.com" || $matches[0] == "2345.cn")
            {
                $redirectUrl = $_COOKIE['qqforward'];
            }
            $this->pageArray["OAuthMsg"] = $_COOKIE["OAuthMsg"];
            setcookie("OAuthMsg", "", time() - 3600);
        }


        $this->pageArray["redirectUrl"] = $redirectUrl;
        $userinfo = $memberModel->read(PASSID);
        if ($userinfo["email"])
        {
            $email_pre = explode("@", $userinfo["email"]);
            if (strlen($email_pre[0]) > 7)
            {
                $email_pre[0] = substr($email_pre[0], 0, 3) . "****" . substr($email_pre[0], strlen($email_pre[0]) - 3, 3);
            }
            else
            {
                $email_pre[0] = substr($email_pre[0], 0, 3) . "****";
            }
            $userinfo["email"] = $email_pre[0] . "@" . $email_pre[1];
            !$userinfo["email_status"] && $score -= 20;
        }
        else
        {
            $score -= 30;
        }

        if ($userinfo['phone'])
        {
            $userinfo["phone"] = substr($userinfo['phone'], 0, 3) . '****' . substr($userinfo['phone'], -4);
        }
        else
        {
            $score -= 30;
        }

        // 星座
        $constellation = false;
        if ($userinfo && $userinfo['bday'] && $userinfo['bday'] != "0000-00-00")
        {
            list($bYear, $bMonth, $bDay) = explode("-", $userinfo['bday']);
            if ($bMonth && $bDay)
            {
                $constellation = $this->getConstellation($bMonth, $bDay);
            }
        }

        $gameList = $this->getGameListData();

        $apiDataService = new \Service\ThirdParty\ApiData();
        $userCenterGame = $apiDataService->getUserCenterGames();
        list($userCenterVideo, $videoJsonData) = $apiDataService->getUserCenterVideo();
        $this->pageArray["v3"] = 0;

        // 获取头像
        $this->pageArray["mid"] = "";
        if (isset($_GET["mid"]) && checkMid($_GET["mid"])) {
            $this->pageArray["v3"] = 1;
            $this->pageArray["mid"] = $_GET["mid"];
        } else {
            list($avatar, $isDefaultAvatar) = $avatarUrl = \Service\Avatar\Avatar::getTransferAvatar('login', PASSID, 'big', 1);
        }
        $this->pageArray['gameList'] = $gameList;
        $this->pageArray['constellation'] = $constellation;
        $this->pageArray['userCenterVideo'] = $userCenterVideo;
        $this->pageArray['videoJsonData'] = $videoJsonData;
        $this->pageArray['userCenterGame'] = $userCenterGame;
        $this->pageArray['info'] = $userinfo;
        $this->pageArray['userimg'] = $avatar;
        $this->pageArray['tabIndex'] = 2;
        $this->pageArray['score'] = $score;
        $this->pageArray['title'] = "首页-2345用户中心";
        $this->pageArray['now'] = date('Y年m月d日');
        $everyDayDataUrl = 'http://day.2345.com';
        $this->pageArray['everyDayUrl'] = $everyDayDataUrl;
        $everyDayData = $this->getEveryDayData($everyDayDataUrl);
        //容错处理，防止取不到数据时，写一个默认数据
        if ($everyDayData == null || $everyDayData == '')
        {
            $this->pageArray['everyDayData'] = '父子赌气妻子出差，丈夫和儿子都不愿做饭，只得赌气上饭店。 儿子对服务生说：“ 给那角落的老头上好酒好菜， 我买单！” 服务生不解： “为何？” 儿子说：“我和他儿媳相好！ ”服务生看老头吃的欢， 便问： “他和';
        }
        else
        {
            $this->pageArray['everyDayData'] = $everyDayData;
        }
        loadCompatibleView('member/edit_info.tpl.html', 'member/m/edit_info_wap.tpl.html', $this->pageArray);
    }

    /**
     * 获取每日一笑数据(数据做了redis缓存，key为Y-m-d)
     * @param string $url 地址
     *
     * @author：dongx
     * @return mixed
     */
    private function getEveryDayData($url)
    {
        $url = \dev::getDevDomain($url);
        $prefix = 'mryx_';
        $redis = RedisEx::getInstance();
        $day = $prefix . date("Y-m-d");
        $data = $redis->get($day);
        if ($data)
        {
            return $data;
        }
        else
        {
            $current = mSecTime();
            $all = file_get_contents($url);
            LoggerFacade::info("获取每日一笑数据", [
                "type" => "http_request",
                "path" => $url,
                "exec_time" => mSecTime() - $current,
                "code" => null,
                "http_code" => $all == false ? 500 : 200,
            ]);
            preg_match_all(
                "/<div class=\"now-cont\">([\s\S]*)<p>(.*?)<\/p>/",
                $all,
                $matche_body
            );

            $matche_body = $matche_body[1][0];
            preg_match_all(
                "/<p>【开心一刻】(.*?)<\/p>/",
                $matche_body,
                $matche_body
            );
            $data = $matche_body[1][0];
            $data = \Common\Utils\Tools::subStr($data, 200);
            $data .= '...';
            $redis->set($day, $data);
            return $data;

        }
    }

    /**
     * 获取 星座信息
     * @param int $month month
     * @param int $day day
     *
     * @return bool
     */
    private function getConstellation($month, $day)
    {
        // 检查参数有效性
        if ($month < 1 || $month > 12 || $day < 1 || $day > 31) {
            return false;
        }
        // 星座名称以及开始日期
        $signs = array(
            array("20" => array("shuiping", "水瓶座", "1月20日-2月18日")),
            array("19" => array("shuangyu", "双鱼座", "2月19日-3月20日")),
            array("21" => array("baiyang", "白羊座", "3月21日-4月19日")),
            array("20" => array("jinniu", "金牛座", "4月20日-5月20日")),
            array("21" => array("shuangzi", "双子座", "5月21日-6月21日")),
            array("22" => array("juxie", "巨蟹座", "6月22日-7月22日")),
            array("23" => array("shizi", "狮子座", "7月23日-8月22日")),
            array("23" => array("chunv", "处女座", "8月23日-9月22日")),
            array("23" => array("tiancheng", "天秤座", "9月23日-10月23日")),
            array("24" => array("tianxie", "天蝎座", "10月24日-11月22日")),
            array("22" => array("sheshou", "射手座", "11月23日-12月21日")),
            array("22" => array("mojie", "摩羯座", "12月22日-1月19日")),
        );
        foreach ($signs[(int)$month - 1] as $key => $item) {
            $sign_start = $key;
            $sign_name = $item;
        }
        if ($day < $sign_start) {
            $exactMonth = ($month - 2 < 0) ? 11 : $month - 2;
            foreach ($signs[$exactMonth] as $item) {
                $sign_name = $item;
            }
        }

        return $sign_name;
    }

    /**
     * 小游戏推广列表
     *
     * @return array
     */
    private function getGameListData()
    {
        return array(
            [
                "双人",
                "https://xiaoyouxi.2345.com/zhuanti/202.htm",
                "https://passport.2345.com/images/yximg/sr.jpg",
            ],
            [
                "斗地主",
                "https://xiaoyouxi.2345.com/zhuanti/174.htm",
                "https://passport.2345.com/images/yximg/ddz.jpg",
            ],
            [
                "黄金矿工",
                "https://xiaoyouxi.2345.com/zhuanti/110.htm",
                "https://passport.2345.com/images/yximg/hjkg.jpg",
            ],
            [
                "连连看",
                "https://xiaoyouxi.2345.com/zhuanti/61.htm",
                "https://passport.2345.com/images/yximg/llk.jpg",
            ],
            [
                "僵尸",
                "https://xiaoyouxi.2345.com/zhuanti/60.htm",
                "https://passport.2345.com/images/yximg/js.jpg",
            ],
            [
                "坦克",
                "https://xiaoyouxi.2345.com/zhuanti/133.htm",
                "https://passport.2345.com/images/yximg/tk.jpg",
            ],
            [
                "赛车",
                "https://xiaoyouxi.2345.com/zhuanti/112.htm",
                "https://passport.2345.com/images/yximg/sc.jpg",
            ],
            [
                "拳皇",
                "https://xiaoyouxi.2345.com/zhuanti/180.htm",
                "https://passport.2345.com/images/yximg/qh.jpg",
            ],
            [
                "闯关",
                "https://xiaoyouxi.2345.com/zhuanti/2.htm",
                "https://passport.2345.com/images/yximg/cg.jpg",
            ],
            [
                "钓鱼",
                "https://xiaoyouxi.2345.com/zhuanti/40.htm",
                "https://passport.2345.com/images/yximg/dy.jpg",
            ],
        );
    }

    /**
     * 关闭头像
     * <AUTHOR>
     */
    public function actionCloseAvatar()
    {
        if (IS_LOGIN && $_POST['type'] == 'close')
        {
            $memberModel = loadModel("member");
            $memberModel->closeAvatar(PASSID);
        }
    }

    /**
     *
     * @param $domain
     * @return string
     * <AUTHOR>
     */
    private function detectProduct($domain)
    {
        switch ($domain)
        {
            case "buy.2345.com":
                $product = "购物";
                break;
            case "wan.2345.com":
                $product = "游戏";
                break;
            case "book.2345.com":
                $product = "小说";
                break;
            case "caipiao.2345.com":
                $product = "彩票";
                break;
            case "cps.2345.com":
                $product = "效果联盟平台";
                break;
            case "chrome.login.2345.com":
                $product = "加速浏览器";
                break;
            case "game.2345.com":
                $product = "游戏";
                break;
            case "ie.2345.com":
                $product = "浏览器";
                break;
            case "jifen.2345.com":
            case "jifen.yl234.com":
                $product = "技术员联盟";
                break;
            case "login.2345.com":
                $product = "用户中心";
                break;
            case "m.book.2345.com":
                $product = "小说";
                break;
            case "v.2345.com":
                $product = "2345影视";
                break;
            case "kan.2345.com":
                $product = "2345影视kan";
                break;
            case "dianying.2345.com":
                $product = "2345电影";
                break;
            case "tv.2345.com":
                $product = "2345电视剧";
                break;
            case "dongman.2345.com":
                $product = "2345动漫";
                break;
            case "m.jifen.2345.com":
                $product = "技术员联盟";
                break;
            case "m.shouji.2345.com":
                $product = "手机联盟";
                break;
            case "mobile.login.2345.com":
                $product = "用户中心";
                break;
            case "zhushou.2345.com":
                $product = "手机助手";
                break;
            default:
                $product = "用户中心";
                break;
        }
        return $product;
    }

    /**
     * 登录日志
     * @return void
     */
    public function actionLoginLog()
    {
        $redis = RedisEx::getInstance();
        $loginLogs = $redis->zRevRangeByScore("LSSP:" . PASSID, time(), 0, array('withscores' => true));
        $commonArea = array();
        $count = 0;
        foreach ($loginLogs as $log => $logtime)
        {
            $day = date("Y-m-d", $logtime);
            $second = date("H:i:s", $logtime);
            list($ip, $from, $browser, $system) = explode("|", $log);
            $cityId = getCityId($ip);
            $area = getAreaNameByIpLib($ip);
            $ipExp = explode(".", $ip);
            $hideIp = implode(".", array($ipExp[0], $ipExp[1], $ipExp[2], "*"));
            $this->pageArray['loginlog'][$count]['day'] = $day;
            $this->pageArray['loginlog'][$count]['second'] = $second;
            $this->pageArray['loginlog'][$count]['ip'] = $hideIp;
            $this->pageArray['loginlog'][$count]['browser'] = $browser;
            $this->pageArray['loginlog'][$count]['product'] = $this->detectProduct($from);
            $this->pageArray['loginlog'][$count]['area'] = $area;
            $commonArea[$area] = is_null($commonArea[$area]) ? 0 : $commonArea[$area];
            $commonArea[$area] ++;
            $count ++;
        }

        if (count($commonArea) > 1)
        {
            $this->pageArray['uncommon'] = true;
            $minvalue = min($commonArea);
            $minArea = array_search($minvalue, $commonArea);
            $this->pageArray['uncommonarea'] = $minArea;
        }

        $this->pageArray['tabIndex'] = 2;
        $this->pageArray['title'] = "首页-2345用户中心";

        loadView('member/edit_info_login_log.tpl.html', $this->pageArray);
    }

}

/**
 * 函数名称：getOAuthBindCheckUrl
 * 参    数：用户passid
 * 作    者：张小虎
 * 功    能：获取用户第三方登录帐号绑定状态
 * 修改日期：2012.11.01
 */
function getOAuthBindCheckUrl($passid)
{
    $api_access_code = '9a053e6290f08f82fa1ede269486a4c4';
    $timestamp = time();
    $code = md5($timestamp . $api_access_code . $passid);

    return LOGIN_HOST . '/oauth/bind/check.php?passid=' . $passid . '&timestamp=' . $timestamp . '&code=' . $code;
}