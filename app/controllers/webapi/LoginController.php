<?php
use Common\Utils\Url as Requester;

/**
 * 登录接口
 * */
class LoginController extends Controller
{
    private $checkLogin;

    /**
     * LoginController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->checkLogin = loadAction('checkLogin');
    }

    /**
     * returnJson
     * -
     * @param array $data Data
     * @return string
     * <AUTHOR>
     */
    private function returnJson($data)
    {
        return json_encode($data);
    }

    /**
     * actionIndex
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        exit($this->returnJson(array(
            'status' => 'P10086',
            'msg' => mb_convert_encoding("请刷新重试，仍异常可致电400-000-2345", "utf8", "gbk"),
            'display' => true,
        )));
        session_start();
        if (count($_POST) == 1 && isset($_POST['data']) && is_string($_POST['data']))
        {
            $encryptAction = loadAction('Encrypt');
            $_POST = $encryptAction::aes128cbcHexDecrypt($_POST['data']);
            $jsonArray = json_decode($_POST, true);
            if ($jsonArray != null)
            {
                $_POST = $jsonArray;
            }
            else
            {
                parse_str($_POST, $_POST);
            }
        }
        if (!isset($_SESSION['expire']))
        {
            $_SESSION['expire'] = 0;
        }
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && $_POST['cmd'] == "login")
        {
            //$forward = getForwardUrl();
            header('P3P: CP="CURa ADMa DEVa PSAo PSDo OUR BUS UNI PUR INT DEM STA PRE COM NAV OTC NOI DSP COR"');

            if ((($_POST['currtime'] - time()) >= 3600 * 24 * 30))
            {
                exit($this->returnJson(array(
                            'status' => 'P00002',
                            'msg' => mb_convert_encoding('系统检测到您的电脑日期和实际日期不符，导致登录失败。<br/>请修改您的电脑日期后再登录', "utf8", "gbk")
                )));
            }
            if (isset($_COOKIE['oauth_type']))
            {
                setcookie('oauth_type', '', time() - 3600, '/', '2345.com');
            }
            $errInfo = '';
            /*
             * 检查登录token username字段 页面进入时间
             * 如果必须要传验证码,则判断是否有传
             */
            if ($this->checkLogin->checkFLoginInfo($_POST, $errInfo))
            {
                $this->doLogin();
            }
            else
            {
                $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
                // 登录有误，错误代码1008，请联系客服
                $info = '登录已过期，请重新登录 ';
                if (RUNMODE == 'development')
                {
                    $info .= '[' . $errInfo . ']';
                }

                $this->checkLogin->clearFInfo();
                $this->checkLogin->clearCaptcha();

                exit($this->returnJson(array(
                    'status' => 'P10086',
                    'msg' => mb_convert_encoding($info, "utf8", "gbk"),
                    'display' => true,
                )));
            }
        }
        elseif ($_GET['action'] == "logout")
        {
            header('P3P: CP="CURa ADMa DEVa PSAo PSDo OUR BUS UNI PUR INT DEM STA PRE COM NAV OTC NOI DSP COR"');
            $this->doLogout();
        }
    }

    /**
     * actionXc
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionFaction()
    {
        session_start();
        $isShowCaptcha = 0;
        /**
         * 针对页游登录被刷,强制开启图片验证码
         */
        $this->checkLogin->showCodeLimitCount = 0;
        $fLoginInfo = $this->checkLogin->initFLoginInfo($isShowCaptcha);
        $_SESSION[FLOGIN_SESSION_KEY] = serialize($fLoginInfo);
        if (isset($_GET['le']))
        {
            $result = array(
                'status' => 'P10086',
                'unf' => $fLoginInfo['userNameField'],
                'token' => $fLoginInfo['token'],
                'isShow' => $isShowCaptcha,
                'le' => intval($_GET['le']),
            );
        }
        else
        {
            $result = array(
                'status' => 'P10086',
                'unf' => $fLoginInfo['userNameField'],
                'token' => $fLoginInfo['token'],
                'isShow' => $isShowCaptcha,
            );
        }

        session_write_close();

        //$returnStr = sprintf("('P10086','%s','%s','%s','%s')", $result['unf'], $result['token'], $result['isShow'], $loginError);
        //exit($returnStr);

        if (isset($_GET['callback']) && !empty($_GET['callback']))
        {
            $callback = \Common\Utils\Url::getStringParam("callback");
            $result = $callback . "('" . $this->returnJson($result) . "')";
        }
        else
        {
            $result = '';
        }
        exit($result);
    }

    /**
     * doLogin
     * -
     * @return void
     * <AUTHOR>
     */
    private function doLogin()
    {
        //$forward = getForwardUrl();
        if (($_SERVER['HTTP_USER_AGENT'] == ('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) &&
                !isset($_SERVER['HTTP_REFERER'])))
        {
            exit($this->returnJson(array(
                        'status' => 'P00003',
                        'msg' => mb_convert_encoding('登录有误，错误代码1007，请联系客服', "utf8", "gbk")
            )));
        }
        else
        {
            $loginAction = loadAction('login');
            $isValidate = 0;
            $isShowCode = false;
            //$showCodeLimitCount = Config::get("showCodeLimitCount");
            $showCodeLimitCount = 1;

            /* 去除微端特殊验证
            if ($_POST['micro'] && empty($_POST['check_code']))
            {
                $isShowCode = true;
                $isValidate = 1;
            }
            */
            if (isset($_POST['requiredCaptcha']) && $_POST['requiredCaptcha'] == '1')
            {
                //强制验证
                $check_code = $_POST['check_code'];
                if (isset($check_code) && (isset($_SESSION['checkIMGCode_new']) || isset($_SESSION['captcha_code'])))
                {
                    $codeValidate = (0 == strcmp($check_code, $_SESSION['checkIMGCode_new'])) || (0 == strcmp($check_code, $_SESSION['captcha_code']));
                    if (!$codeValidate)
                    {
                        $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
                        //$_SESSION['checkIMGCode_new'] = "";
                        exit($this->returnJson(array(
                            'status' => 'P00004',
                            'msg' => mb_convert_encoding('验证码输入错误', "utf8", "gbk"),
                            'display' => true
                        )));
                    }
                }
                else
                {
                    $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
                    exit($this->returnJson(array(
                        'status' => 'P00005',
                        'msg' => mb_convert_encoding('验证码不能为空', "utf8", "gbk"),
                        'display' => true
                    )));
                }
                $this->checkLogin->clearCaptcha();
                $isValidate = 1;
            }
            elseif ($this->checkLogin->isNeedCheckCaptcha())
            {
                if (isset($_POST['check_code']) && !empty($_POST['check_code']) && (isset($_SESSION['checkIMGCode_new']) || isset($_SESSION['captcha_code'])))
                {
                    $check_code = $_POST['check_code'];
                    $codeValidate = (0 == strcmp($check_code, $_SESSION['checkIMGCode_new'])) || (0 == strcmp($check_code, $_SESSION['captcha_code']));
                    if (!$codeValidate)
                    {
                        $this->checkLogin->clearFInfo();
                        $this->checkLogin->clearCaptcha();
                        //$_SESSION['checkIMGCode_new'] = "";
                        exit($this->returnJson(array(
                                    'status' => 'P00004',
                                    'msg' => mb_convert_encoding('验证码输入错误', "utf8", "gbk"),
                                    'display' => true
                        )));
                    }
                }
                else
                {
                    $this->checkLogin->clearFInfo();
                    $this->checkLogin->clearCaptcha();
                    exit($this->returnJson(array(
                                'status' => 'P00005',
                                'msg' => mb_convert_encoding('验证码不能为空', "utf8", "gbk"),
                                'display' => true
                    )));
                }
                $this->checkLogin->clearCaptcha();
                $isValidate = 1;
            }

            $userNameField = $this->checkLogin->getUserNameField();
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                $username = mb_convert_encoding(trim($_POST[$userNameField]), "gbk", "utf8");
            }
            else
            {
                $username = trim($_POST[$userNameField]);
            }
            $password = strrev(trim($_POST['password']));
            $domain = trim($_POST['domain']);
            $result = $loginAction->login($username, $password, $domain, get_client_ip(), $isValidate, $_SERVER['HTTP_USER_AGENT'], $domain);
            $state = $result[0]; //返回状态
            //  $state返回值，分别代表的意思如下
            //	 * -2:通行证帐号有重名，需要改名
            //	 * -1:2345帐号密码错误，同一用户连续三次-1的情况，需要加验证码
            //	 * 0:需要帐号认领
            //	 * 1:登录成功，$result中的值包含帐号信息
            //   * 2:通行证帐号冻结中需要激活。
            //新增需要添加验证码状态
            //   * 3:登录需要添加验证码。
            //   * 4:非法域名。
            //   * 5:非法ip。
            //   * 6:限制ip6小时。
            //$state = -1;
            if ($state == 6)
            {
                //限制ip6小时
                exit($this->returnJson(array(
                            'status' => 'P00006',
                            'msg' => mb_convert_encoding('登录有误，错误代码1006，请联系客服', "utf8", "gbk")
                )));
            }
            elseif ($state == 5)
            {
                //非法IP
                exit($this->returnJson(array(
                            'status' => 'P00007',
                            'msg' => mb_convert_encoding('登录有误，错误代码1005，请联系客服', "utf8", "gbk")
                )));
            }
            elseif ($state == 4)
            {
                //非法域名
                exit($this->returnJson(array(
                            'status' => 'P00008',
                            'msg' => mb_convert_encoding('登录有误，错误代码1004，请联系客服', "utf8", "gbk")
                )));
            }
            elseif ($state == 3)
            {
                $this->checkLogin->clearFInfo();
                $this->checkLogin->clearCaptcha();
                $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
                exit($this->returnJson(array(
                            'status' => 'P00009',
                            'msg' => mb_convert_encoding('验证码输入错误', "utf8", "gbk")
                )));
            }
            elseif ($state == 2)
            {
                setcookie('active_passid', $result['passId'], 0, '/');
                setcookie('active_email', $result['email'], 0, '/');
                exit($this->returnJson(array(
                            'status' => 'P00010',
                            'msg' => '',
                            'location' => PASSPORT_HOST . '/active_error.html',
                )));
            }
            elseif ($state === -1)
            {
                $this->checkLogin->clearFInfo();
                $this->checkLogin->clearCaptcha();
                $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
                if ($this->checkLogin->isNeedCheckCaptcha())
                {
                    $isShowCode = true;
                }
                exit($this->returnJson(array(
                            'status' => 'P00011',
                            'msg' => mb_convert_encoding('2345帐号或密码错误，均区分大小写', "utf8", "gbk"),
                            "display" => $isShowCode
                )));
            }
            elseif ($state === 1)
            {
                /* 去除微端特殊验证
                if($_POST['micro'] && empty($_POST['check_code']))
                {
                    if ($_SESSION['expire'] > $showCodeLimitCount || $loginAction->decideCode(get_client_ip()))
                    {
                        $isShowCode = true;
                        echo "login.checkLoginStatusBack ( ".  json_encode(array('display' => $isShowCode )) .")" ;
                        exit;
                    }
                }
                */

                unset($_SESSION['expire']);

                if ($_POST['vTime'] != 0)
                {
                    $cTime = time() + $_POST['vTime'];
                }
                else
                {
                    $cTime = time() + 3600 * 24 * 30;
                }

                $cookie = $result['cookie'];

                if (isset($_POST['autoLogin']))
                {
                    $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);
                    setcookie('autoLogin', 1, $cTime, '/', $_SERVER['HTTP_HOST']);
                }
                else
                {
                    $loginAction->setLoginCookie($cookie, 0, DOMAIN);
                    setcookie('autoLogin', "", time() - 3600, '/', $_SERVER['HTTP_HOST']);
                }

                if (isset($_POST['serverCall']))
                {
                    $serverCall = parse_url($_POST['serverCall']);
                    $serverPath = $serverCall['scheme'] . '://' . $serverCall['host'];
                    if (preg_match("/^(http|https):\/\/[^\/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|ym\.com)$/", $serverPath))
                    {
                        RedisEx::delInstance();
                        $curlOption = [
                            CURLOPT_CONNECTTIMEOUT => 1,
                            CURLOPT_TIMEOUT        => 1,
                        ];
                        http_post($_POST['serverCall'], $result, $curlOption);
                    }
                }

                // 设置其他域名的登录cookie： 2345.cn等
                $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);

                exit($this->returnJson(array(
                    'status'    => 'P00001',
                    'loadPage'  => $thirdCallbackCookies,
                    'setCookie' => json_encode(array('I' => $cookie['I'])),
                )));
            }
        }
    }

    /**
     * doLogout
     * -
     * @return void
     * <AUTHOR>
     */
    private function doLogout()
    {
        setcookie('name', "", time() - 3600, '/', DOMAIN);
        setcookie('uid', "", time() - 3600, '/', DOMAIN);
        setcookie('u_sec', "", time() - 3600, '/', DOMAIN);
        setcookie('iden', "", time() - 3600, '/', DOMAIN);
        setcookie('passid', "", time() - 3600, '/', DOMAIN);
        setcookie('user_info', "", time() - 3600, '/', DOMAIN);
        setcookie('name_ie', "", time() - 3600, '/', DOMAIN);
        setcookie('I', "", time() - 3600, '/', DOMAIN, false, true);
        $forward = getForwardUrl();
        if ($forward)
        {
            redirect($forward);
        }
        else
        {
            redirect('http://www.' . DOMAIN);
        }
    }

}
