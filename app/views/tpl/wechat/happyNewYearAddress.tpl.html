<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="gbk">
    <title>收货地址</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="/css/wechat/happNewYear/style.css?cccccc">
    <link rel="stylesheet" href="/js/wechat/happyNewYear/zepto.mtimer.css">
</head>
<body style="background: #eee">
<form id="myForm">

    <div class="message">
        <ul class="inputBox">
            <li>
                <lable>姓名</lable>
                <input type="text" name="name" id="name"/>
                <input type="hidden" name="id" value="{{$pageArray.id}}"/>
            </li>
            <li>
                <lable>手机号</lable>
                <input type="number" name="phone" id="phone"/>
            </li>
            <li id="address">
                <lable>收货城市</lable>
                <input name="city" id="city" type="text" readonly="readonly"/>
                <i class="arrowR"></i>
            </li>
            <li>
                <lable>详细地址</lable>
                <textarea  maxlength="200" name="address" id="Maddress"
                          onfocus="if($(this).html()=='请输入详细地址'){$(this).addClass('cBlack');$(this).html('');}" placeholder="请输入详细地址,最多200个字符"></textarea>
            </li>
        </ul>
        <p class="topText">春节物流限制,预计2018.2.26日发货</p>
        <a class="btnRed" href="javascript:saveAddress()">确认领取</a>
        <p class="botText">2345会替您向您的朋友送上祝福~</p>
    </div>


</form>

<script src="/js/wechat/happyNewYear/zepto.min.js"></script>
<script src="/js/wechat/happyNewYear/picker.min.js" charset="gb2312"></script>
<script src="/js/wechat/happyNewYear/jd_address_new.js" charset="gb2312"></script>
<script src="/js/wechat/happyNewYear/addressM.js" charset="gb2312"></script>

<script>
    $(function () {
        addressM($('#address input'));
    });

    function checkRequired() {
        var required = [
            'name',
            'phone',
            'city',
            'Maddress',
        ]
        var flag = true  //必填全部填写完毕
        var mid
        for (var i in required) {
            mid = $.trim($('#' + required[i]).val());
            if (mid == '') {
                flag = false
                break;
            }

        }
        return flag
    }

    function saveAddress() {
        $.ajax({
            type: "POST",
            url: "/wechat/HappyNewYear/SaveAddress",
            data: $('#myForm').serialize(),
            beforeSend: function () {

                var flag = checkRequired()
                if (!flag) {
                    alert('抱歉，您的收货信息有误/不完整！为保奖品顺利送达，烦请重新填写。');
                    return false
                }
                var phone_reg={{$pageArray.phone_reg}};
                if(!phone_reg.test($('#phone').val())){
                    alert('手机号格式不正确');
                    return false
                }
            },
            success: function (ret) {
                if(ret.code!='200'){
                    alert(ret.msg);
                }else{
                    alert('领取成功!');
                    location.href='/wechat/HappyNewYear/bag'
                }
            }
        });
    }
</script>
</body>
</html>