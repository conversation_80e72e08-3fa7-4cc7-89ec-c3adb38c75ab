<?php

/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：DataMonitorAction.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：10-14, 2016
 */


namespace Monitor;

class DataMonitorAction extends \Action
{
    const RATE = 0.4;
    const AVG_MAX = 50;

    protected $pdo;
    protected $channelList = array();
    protected $projectList = array();

    /**
     * todo 注意这里查询的是从库
     *
     * DataMonitorAction constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = \Config::get("slave_database");
        $this->pdo = \Octopus\PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);
    }

    /**
     * 获取 用户回执 比率
     *
     * @param int    $channel   channelId
     * @param string $endTime   time
     * @param string $startTime time
     *
     * @return array|bool
     */
    public function getCallbackRate($channel, $endTime, $startTime)
    {
        $tableName = \Model::getTableName();
        $sql = <<<SQL
SELECT callback_status AS status, send_time, channel, COUNT(1) AS num 
FROM {$tableName} 
WHERE (send_time BETWEEN :startTime AND :endTime) AND channel = :channel 
GROUP BY callback_status 
SQL;

        $params = array(
            ":startTime" => $startTime,
            ":endTime"   => $endTime,
            ":channel"   => $channel,
        );
        $res = $this->pdo->findList($sql, $params);
        $success = $failed = $noCallback = 0;
        foreach ($res as $key => $val)
        {
            if ($val['status'] === null)
            {
                $noCallback += $val["num"];
            }
            elseif ($val['status'] == 1)
            {
                $success += $val["num"];
            }
            elseif ($val['status'] == 0)
            {
                $failed += $val["num"];
            }
        }
        if ($failed + $noCallback === 0 || $success + $failed + $noCallback <= 20)
        {
            $rate = 1;
        }
        else
        {
            $rate = round($success / ($success + $failed + $noCallback), 2);
        }

        return array($rate, $success, $failed, $noCallback, $sql);
    }

    /**
     * 获取 接收失败 和 提交失败 比率
     *
     * @param int    $smsType   smsType
     * @param string $endTime   time
     * @param string $startTime time
     *
     * @return array|bool
     */
    public function getSendRate($smsType, $endTime, $startTime)
    {
        $tableName = \Model::getTableName();
        $sql = <<<SQL
SELECT
  send_status,
  send_time,
  type,
  COUNT(1) AS count
FROM
  {$tableName} 
WHERE send_time BETWEEN :startTime AND :endTime AND type = :type
GROUP BY send_status
SQL;

        $params = array(
            ":startTime" => $startTime,
            ":endTime"   => $endTime,
            ":type"      => $smsType,
        );
        $res = $this->pdo->findList($sql, $params);
        $success = $failCommit = $failReceive = 0;
        foreach ((array)$res as $key => $val)
        {
            if ($val['send_status'] >= 1)
            {
                // 提交成功
                $success += $val["count"];
            }
            elseif ($val['send_status'] == 0)
            {
                // 提交服务商失败
                $failCommit += $val["count"];
            }
            else
            {
                if (array_key_exists($val['send_status'], array_keys(array(-10 => "超过数量限制"))))
                {
                    continue;
                }

                // 项目提交失败
                $failReceive += $val["count"];
            }
        }
        // 总成功率
        $rate = ($failCommit + $failReceive) == 0 ? 1 : round($success / ($success + $failCommit + $failReceive), 2);
        // 项目提交成功率
        $receiveRate = $failReceive == 0 ? 1 : round(
            ($success + $failCommit) / ($success + $failReceive + $failCommit),
            2
        );
        // 提交服务商成功率
        $commitRate = $failCommit == 0 ? 1 : round(
            $success / ($success + $failCommit),
            2
        );

        // 样本总数小于20，不监控，结果比例为100%
        if ($success + $failCommit + $failReceive <= 20)
        {
            $rate = $commitRate = $receiveRate = 1;
        }

        return array($rate, $receiveRate, $commitRate, $success, $failCommit, $failReceive, $sql);
    }

    /**
     * @param int    $channel   channel
     * @param int    $status    status
     * @param string $startTime startTime
     * @param string $endTime   endTime
     *
     * @return mixed
     */
    private function getChannelSerialSendStatus($channel, $status, $startTime, $endTime)
    {
        $sql = "SELECT
              DAY(sms.send_time)    AS day,
              HOUR(sms.send_time)   AS hour,
              minute(sms.send_time) AS minute,
              COUNT(1)              AS count
            FROM
              my_sms_logs AS sms
              JOIN
              my_sms_logs_channel AS ch ON sms.id = ch.log_id
            WHERE
              sms.send_time >= :startTime
              AND sms.send_time < :endTime
              AND ch.channel = :channel
              AND sms.send_status = :status
            GROUP BY DAY(sms.send_time), HOUR(sms.send_time), Minute(sms.send_time)";
        $params = array(
            ":startTime" => $startTime,
            ":endTime"   => $endTime,
            ":channel"   => $channel,
            ":status"    => $status,
        );
        $res = $this->pdo->findList($sql, $params);
        //        var_dump($res);

        list($minutes) = $this->getMinuteArray($startTime, $endTime);
        foreach ($res as $key => $val)
        {
            $time = sprintf("%02d-%02d:%02d", $val["day"], $val["hour"], $val["minute"]);
            if (isset($minutes[$time]))
            {
                $minutes[$time] = $val["count"];
            }
        }

        return $minutes;
    }

    /**
     * @param int    $channel   channel
     * @param int    $status    status
     * @param string $startTime startTime
     * @param string $endTime   endTime
     *
     * @return mixed
     */
    private function getDayChannelSerialSendStatus($channel, $status, $startTime, $endTime)
    {
        $sql = "SELECT
              MONTH(sms.send_time)    AS month,
              DAY(sms.send_time)    AS day,
              COUNT(1)              AS count
            FROM
              my_sms_logs AS sms
              JOIN
              my_sms_logs_channel AS ch ON sms.id = ch.log_id
            WHERE
              sms.send_time >= :startTime
              AND sms.send_time < :endTime
              AND ch.channel = :channel
              AND sms.send_status = :status
            GROUP BY MONTH(sms.send_time), DAY(sms.send_time)";
        $params = array(
            ":startTime" => $startTime,
            ":endTime"   => $endTime,
            ":channel"   => $channel,
            ":status"    => $status,
        );
        $res = $this->pdo->findList($sql, $params);

        list($days) = $this->getDayArray($startTime, $endTime);
        foreach ($res as $key => $val)
        {
            $time = sprintf("%02d-%02d", $val["month"], $val["day"]);
            if (isset($days[$time]))
            {
                $days[$time] = $val["count"];
            }
        }

        return $days;
    }

    /**
     * @param int    $project   project
     * @param int    $status    status
     * @param string $startTime startTime
     * @param string $endTime   endTime
     *
     * @return mixed
     */
    private function getProjectSerialSendStatus($project, $status, $startTime, $endTime)
    {
        $sql = "SELECT
              DAY(send_time)    AS day,
              HOUR(send_time)   AS hour,
              minute(send_time) AS minute,
              COUNT(1)              AS count
            FROM
              my_sms_logs
            WHERE
              send_time >= :startTime
              AND send_time < :endTime
              AND pid= :project
              AND send_status = :status
            GROUP BY DAY(send_time), HOUR(send_time), Minute(send_time)";
        $params = array(
            ":startTime" => $startTime,
            ":endTime"   => $endTime,
            ":project"   => $project,
            ":status"    => $status,
        );
        $res = $this->pdo->findList($sql, $params);

        list($minutes) = $this->getMinuteArray($startTime, $endTime);
        foreach ($res as $key => $val)
        {
            $time = sprintf("%02d-%02d:%02d", $val["day"], $val["hour"], $val["minute"]);
            if (isset($minutes[$time]))
            {
                $minutes[$time] = $val["count"];
            }
        }

        return $minutes;
    }

    /**
     * @param int    $project   project
     * @param int    $status    status
     * @param string $startTime startTime
     * @param string $endTime   endTime
     *
     * @return mixed
     */
    private function getDayProjectSerialSendStatus($project, $status, $startTime, $endTime)
    {
        $sql = "SELECT
              MONTH(send_time)  AS month,
              DAY(send_time)    AS day,
              COUNT(1)          AS count
            FROM
              my_sms_logs
            WHERE
              send_time >= :startTime
              AND send_time < :endTime
              AND pid= :project
              AND send_status = :status
            GROUP BY MONTH(send_time), DAY(send_time)";
        $params = array(
            ":startTime" => $startTime,
            ":endTime"   => $endTime,
            ":project"   => $project,
            ":status"    => $status,
        );
        $res = $this->pdo->findList($sql, $params);

        list($days) = $this->getDayArray($startTime, $endTime);
        foreach ($res as $key => $val)
        {
            $time = sprintf("%02d-%02d", $val["month"], $val["day"]);
            if (isset($days[$time]))
            {
                $days[$time] = $val["count"];
            }
        }

        return $days;
    }

    /**
     * @param int    $type      type
     * @param int    $channel   channel
     * @param int    $status    status
     * @param string $startTime startTime
     * @param string $endTime   endTime
     * @param float  $rate      rate
     * @param string $compare   compare
     *
     * @return array
     */
    public function checkChannelDayWarning(
        $type,
        $channel,
        $status = 1,
        $startTime = "",
        $endTime = "",
        $rate = self::RATE,
        $compare = \Monitor\MonitorAction::CMP_ALL
    )
    {
        $dayValues = $this->getDayChannelSerialSendStatus($channel, $status, $startTime, $endTime);

        return $this->checkWarning($dayValues, $type, $channel, $status, $startTime, $endTime, $rate, $compare);
    }

    /**
     * @param int    $type      type
     * @param int    $channel   channel
     * @param int    $status    status
     * @param string $startTime startTime
     * @param string $endTime   endTime
     * @param float  $rate      rate
     * @param string $compare   compare
     *
     * @return array
     */
    public function checkChannelWarning(
        $type,
        $channel,
        $status = 1,
        $startTime = "",
        $endTime = "",
        $rate = self::RATE,
        $compare = \Monitor\MonitorAction::CMP_ALL
    )
    {
        $minuteValues = $this->getChannelSerialSendStatus($channel, $status, $startTime, $endTime);

        return $this->checkWarning($minuteValues, $type, $channel, $status, $startTime, $endTime, $rate, $compare);
    }

    /**
     * @param int    $type      type
     * @param int    $project   project
     * @param int    $status    status
     * @param string $startTime startTime
     * @param string $endTime   endTime
     * @param float  $rate      rate
     * @param string $compare   compare
     *
     *
     * @return array
     */
    public function checkProjectDayWarning(
        $type,
        $project,
        $status = 1,
        $startTime = "",
        $endTime = "",
        $rate = self::RATE,
        $compare = \Monitor\MonitorAction::CMP_ALL
    )
    {
        $dayValues = $this->getDayProjectSerialSendStatus($project, $status, $startTime, $endTime);

        return $this->checkWarning($dayValues, $type, $project, $status, $startTime, $endTime, $rate, $compare);
    }

    /**
     * @param int    $type      type
     * @param int    $project   project
     * @param int    $status    status
     * @param string $startTime startTime
     * @param string $endTime   endTime
     * @param float  $rate      rate
     * @param string $compare   compare
     *
     * @return array
     */
    public function checkProjectWarning(
        $type,
        $project,
        $status = 1,
        $startTime = "",
        $endTime = "",
        $rate = self::RATE,
        $compare = \Monitor\MonitorAction::CMP_ALL
    )
    {
        $minuteValues = $this->getProjectSerialSendStatus($project, $status, $startTime, $endTime);

        return $this->checkWarning($minuteValues, $type, $project, $status, $startTime, $endTime, $rate, $compare);
    }

    /**
     * @param array  $minuteValues minuteValue
     * @param int    $type         type
     * @param int    $project      project
     * @param int    $status       status
     * @param string $startTime    startTime
     * @param string $endTime      endTime
     * @param float  $rate         rate
     * @param string $compare      compare
     *
     * @return array
     */
    public function checkWarning(
        $minuteValues,
        $type,
        $project,
        $status = 1,
        $startTime = "",
        $endTime = "",
        $rate = self::RATE,
        $compare = \Monitor\MonitorAction::CMP_ALL
    )
    {
        $originValues = array_values($minuteValues);
        $value = array_pop($originValues);
        $monitorService = \Monitor\MonitorAction::getInstance();

        $res = $monitorService->checkLinearValue($originValues, $value, $rate, $compare);
        $info = $res["info"];
        $slope = $info["slope"];
        $offset = $info["offset"];
        $forecastValue = $info["forecastValue"];
        $max = $info["max"];
        $min = $info["min"];

        array_push($originValues, $value);
        $formatData = array();
        foreach ($originValues as $key => $val)
        {
            $formatData[] = array($key, $val);
        }

        $avg = $monitorService->avg($originValues);
        $data = array(
            "flag"          => $res["flag"],
            "isWarning"     => $res["flag"] ? "OK" : "ERROR",
            "source"        => $project,
            "status"        => $status == 1 ? "succ" : "fail",
            "origin"        => $originValues,
            "avg"           => round($avg, 2),
            "data"          => $formatData,
            "slope"         => round($slope, 2),
            "offset"        => round($offset, 2),
            "forecastValue" => round($forecastValue, 2),
            "newValue"      => $value,
            "rate"          => $rate,
            "max"           => round($max, 2),
            "min"           => round($min, 2),
            "yMin"          => min($originValues) < 0 ? min($originValues) : 0,
            "yMax"          => max($originValues) * 1.3,
            "xMin"          => min(array_keys($originValues)),
            "xMax"          => max(array_keys($originValues)),
            "startTime"     => $startTime,
            "endTime"       => $endTime,
        );

        return $data;
    }

    /**
     * @param string $startTime startTime
     * @param string $endTime   endTime
     * @param int    $gap       gap
     *
     * @return array
     */
    private function getMinuteArray($startTime, $endTime, $gap = 1)
    {
        $minutes = array();
        $formatMinutes = array();
        $endTimeStamp = strtotime($endTime);
        $startTimeStamp = strtotime($startTime);
        $min = date("i", $endTimeStamp - 1);
        $dots = intval(($endTimeStamp - $startTimeStamp) / 60 / $gap);
        for ($i = $dots; $i > 0; $i--)
        {
            $key = date("d-H:i", strtotime("-" . ($gap * ($i - 1) + $min % 3) . " minutes", $endTimeStamp));
            $formatKey = date("Y-m-d H:i", strtotime("-" . ($gap * ($i - 1) + $min % 3) . " minutes", $endTimeStamp));

            $minutes[$key] = 0;
            $formatMinutes[$formatKey] = 0;
        }

        return array($minutes, $formatMinutes);
    }

    /**
     * @param string $startTime startTime
     * @param string $endTime   endTime
     * @param int    $gap       gap
     *
     * @return array
     */
    private function getDayArray($startTime, $endTime, $gap = 1)
    {
        $days = array();
        $formatDays = array();
        $endTimeStamp = strtotime($endTime);
        $startTimeStamp = strtotime($startTime);
        $dots = intval(($endTimeStamp - $startTimeStamp) / 86400 / $gap);
        for ($i = $dots; $i > 0; $i--)
        {
            $key = date("m-d", strtotime("-" . ($gap * $i) . " days", $endTimeStamp));
            $formatKey = date("Y-m-d", strtotime("-" . ($gap * $i) . " days", $endTimeStamp));

            $days[$key] = 0;
            $formatDays[$formatKey] = 0;
        }

        return array($days, $formatDays);
    }

}
