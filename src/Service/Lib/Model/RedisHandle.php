<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：RedisHandle.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：27/08/2018 10:11
 */

namespace Service\Lib\Model;

use Service\Func\RequestFunc;
use Service\Lib\Action\KeyProvider;
use Service\Swoole\Tool\Log;

class RedisHandle
{
    protected $redis;

    /**
     * RedisHandle constructor.
     */
    public function __construct()
    {
        $this->redis = \RedisAction::connect();
    }

    /**
     * User: panj
     * 生成log_id 并且 将发送内容写入redis
     *
     * @param array $data    data
     * @param array $columns columns
     *
     * @return array
     */
    public function setSmsLogsInRedis($data, $columns)
    {
        //init log id from mysql
        $phoneLogIds = \ServiceSmsModel::getInstance()->setSmsLogs($data, $columns);
        foreach ($data as $phone => $phoneData)
        {
            $kv = [];
            foreach ($columns as $columnIndex => $column)
            {
                $kv[$column] = $phoneData[$columnIndex];
            }
            $logId = $phoneLogIds[$phone];
            $this->setSmsLogCol2Redis($logId, $kv);
        }
        //use init log id with redis, uncomment below
        /*
        $length = count($data);
        $redisKey = KeyProvider::getSmsServiceLastLogIdKey();
        $endId = $this->redis->incrBy($redisKey, $length);
        $nowId = $first_id = $endId - $length + 1;

        $phoneLogIds = [];
        foreach ($data as $phone => $phoneData)
        {
            $kv = [];
            foreach ($columns as $columnIndex => $column)
            {
                $kv[$column] = $phoneData[$columnIndex];
            }
            $this->setSmsLogCol2Redis($nowId, $kv);
            $phoneLogIds[$phone] = $nowId;
            $nowId ++;
        }
        */
        return $phoneLogIds;
    }

    /**
     * @param int   $logId logId
     * @param array $data  data
     *
     * @return null
     */
    public function setSmsLogCol2Redis($logId, $data)
    {
        $setKey = KeyProvider::getSmsServiceLogIdSetKey();
        $this->redis->zAdd($setKey, time(), json_encode(['logId' => $logId, 'phone' => $data['phone']]));
        $this->redis->expire($setKey, 86400 * 3);
        //新增archive log记录
        $isSet = $this->updateArchiveSmsLog($logId, $data);
        if ($isSet === false)
        {
            \WebLogger\Facade\LoggerFacade::info('error setSmsLogCol2Redis logid:' . $logId . 'data:' .json_encode($data) . 'msg:' . '设置失败');
        }
    }

    /**
     * User: panj
     * 更新或插入缓存表数据--sms_logs_cache 表
     *
     * @param int $logId logId
     * @param array $data data
     *
     * @return bool
     */
    public function setCacheLog($logId, $data)
    {
        $col = [
            'send_time',
            'send_status',
            'callback_time',
            'callback_status',
            'phone',
        ];
        $logCache = [];
        foreach ($data as $key => $val)
        {
            if (in_array($key, $col))
            {
                $logCache[$key] = $val;
            }
        }
        if (empty($logCache))
        {
            return false;
        }
        $redisKey = KeyProvider::getSmsLogsCacheKey($logId);

        return $this->redis->hMSet($redisKey, $logCache);
    }

    /**
     * User: panj
     * 更新或插入归档数据-- sms_logs_archive表
     *
     * @param int $logId logId
     * @param array $data data
     *
     * @return mixed
     */
    public function updateArchiveSmsLog($logId, $data)
    {
        $redisKey = KeyProvider::getSmsServiceLogIdDataKey($logId);
        $data['log_id'] = $logId;
        $isSet = $this->redis->hMSet($redisKey, $data);
        $this->redis->expire($redisKey, 86400 * 3);
        return $isSet;
    }

    /**
     * 用于发送完成后往写入与logID关联的发送结果
     *
     * @param array $data data
     *
     * @return bool
     */
    public function updateSmsLog($data)
    {
        $logId = $data['logId'];
        $update = array(
            'channel'       => $data['channel'],
            'account'       => $data['account'],
            'msg_id'        => $data['msgId'],
            'code_time'     => $data['codeTime'],
            'code_status'   => $data['codeStatus'],
            'code_desc'     => $data['codeDesc'],
            'sms_count'     => $data['smsCount'],
            "send_status"   => $data['sendStatus'],
            "send_response" => $data['sendResponse'],
        );
        $res1 = $this->updateArchiveSmsLog($logId, $update);
        if (!$res1)
        {
            \WebLogger\Facade\LoggerFacade::info('error updateArchiveSmsLog', $res1);
            return false;
        }
        return true;
    }

    /**
     * User: panj
     *
     * @param int $logId logId
     * @param array $data data
     *
     * @return mixed
     */
    public function insertSmsTagLog($logId, $data)
    {
        $tagData = array(
            'positionId' => $data['businessId'],
            'add_time'   => date('Y-m-d'),
            'mid'        => $data['mid'],
        );
        $redisKey = KeyProvider::getSmsTagLogKey($logId);

        return $this->redis->hMSet($redisKey, $tagData);
    }

    /**
     * User: panj
     *
     * @param int $logId logId
     *
     * @return mixed
     */
    public function insertVoiceLogsList($logId)
    {
        $voiceData = [
            'add_time' => date('Y-m-d'),
        ];
        $redisKey = KeyProvider::getSmsVoiceLogsList($logId);

        return $this->redis->hMSet($redisKey, $voiceData);
    }

    /**
     * User: panj
     *
     * 更新回执
     * @param array $item 更新数据
     *
     * @return bool
     */
    public function updateChannelCallbackStatus($item)
    {
        $logId = $item['log_id'];
        $update = array(
            'callback_time'    => $item['add_time'],
            'callback_status'  => $item['status'],
            'callback_message' => $item['message'],
        );
        $res1 = $this->updateArchiveSmsLog($logId, $update);
        return $res1;
//        $updateCache = array(
//            'callback_time'   => $item['add_time'],
//            'callback_status' => $item['status'],
//        );
//        $res2 = $this->setCacheLog($logId, $updateCache);
//        return $res1 && $res2;
    }

    /**
     * 处理回执状态
     *
     * @param array $item item
     * @param string $redisKey redisKey
     * @param array $originData originData
     * @return null
     */
    public function addChannelCallbackStatus($item, $redisKey, $originData)
    {
        $isOk = $this->updateChannelCallbackStatus($item);

        if ($isOk)//如果成功，则从redis中删除对应关系的key， 并置空items()
        {
            $this->redis->del($redisKey);
        }
        else //如果是失败，重新push到队列里
        {
            $this->redis->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($originData));
        }
    }

    /**
     * User: panj
     * 设置logId 是否是走的swoole发送标记
     *
     * @param int $msgId sms provider msg id
     * @param string $phone phone number
     * @param int $logId logical logId
     * @param int $storageType 存放方式
     * @return mixed
     */
    public function setCallBackLogIdTag($msgId, $phone, $logId, $storageType)
    {
//        $redisKey = KeyProvider::getCallBackLogIdTagKey();
//        $val = $msgId . ',' . $logId . ',' . $phone;
//        return $this->redis->zAdd($redisKey, $storageType, $val);
    }
}
