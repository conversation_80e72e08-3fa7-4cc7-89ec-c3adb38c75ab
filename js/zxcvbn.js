(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.zxcvbn = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
var adjacency_graphs;adjacency_graphs={qwerty:{"!":["`~",null,null,"2@","qQ",null],'"':[";:","[{","]}",null,null,"/?"],"#":["2@",null,null,"4$","eE","wW"],$:["3#",null,null,"5%","rR","eE"],"%":["4$",null,null,"6^","tT","rR"],"&":["6^",null,null,"8*","uU","yY"],"'":[";:","[{","]}",null,null,"/?"],"(":["8*",null,null,"0)","oO","iI"],")":["9(",null,null,"-_","pP","oO"],"*":["7&",null,null,"9(","iI","uU"],"+":["-_",null,null,null,"]}","[{"],",":["mM","kK","lL",".>",null,null],"-":["0)",null,null,"=+","[{","pP"],".":[",<","lL",";:","/?",null,null],"/":[".>",";:","'\"",null,null,null],0:["9(",null,null,"-_","pP","oO"],1:["`~",null,null,"2@","qQ",null],2:["1!",null,null,"3#","wW","qQ"],3:["2@",null,null,"4$","eE","wW"],4:["3#",null,null,"5%","rR","eE"],5:["4$",null,null,"6^","tT","rR"],6:["5%",null,null,"7&","yY","tT"],7:["6^",null,null,"8*","uU","yY"],8:["7&",null,null,"9(","iI","uU"],9:["8*",null,null,"0)","oO","iI"],":":["lL","pP","[{","'\"","/?",".>"],";":["lL","pP","[{","'\"","/?",".>"],"<":["mM","kK","lL",".>",null,null],"=":["-_",null,null,null,"]}","[{"],">":[",<","lL",";:","/?",null,null],"?":[".>",";:","'\"",null,null,null],"@":["1!",null,null,"3#","wW","qQ"],A:[null,"qQ","wW","sS","zZ",null],B:["vV","gG","hH","nN",null,null],C:["xX","dD","fF","vV",null,null],D:["sS","eE","rR","fF","cC","xX"],E:["wW","3#","4$","rR","dD","sS"],F:["dD","rR","tT","gG","vV","cC"],G:["fF","tT","yY","hH","bB","vV"],H:["gG","yY","uU","jJ","nN","bB"],I:["uU","8*","9(","oO","kK","jJ"],J:["hH","uU","iI","kK","mM","nN"],K:["jJ","iI","oO","lL",",<","mM"],L:["kK","oO","pP",";:",".>",",<"],M:["nN","jJ","kK",",<",null,null],N:["bB","hH","jJ","mM",null,null],O:["iI","9(","0)","pP","lL","kK"],P:["oO","0)","-_","[{",";:","lL"],Q:[null,"1!","2@","wW","aA",null],R:["eE","4$","5%","tT","fF","dD"],S:["aA","wW","eE","dD","xX","zZ"],T:["rR","5%","6^","yY","gG","fF"],U:["yY","7&","8*","iI","jJ","hH"],V:["cC","fF","gG","bB",null,null],W:["qQ","2@","3#","eE","sS","aA"],X:["zZ","sS","dD","cC",null,null],Y:["tT","6^","7&","uU","hH","gG"],Z:[null,"aA","sS","xX",null,null],"[":["pP","-_","=+","]}","'\"",";:"],"\\":["]}",null,null,null,null,null],"]":["[{","=+",null,"\\|",null,"'\""],"^":["5%",null,null,"7&","yY","tT"],_:["0)",null,null,"=+","[{","pP"],"`":[null,null,null,"1!",null,null],a:[null,"qQ","wW","sS","zZ",null],b:["vV","gG","hH","nN",null,null],c:["xX","dD","fF","vV",null,null],d:["sS","eE","rR","fF","cC","xX"],e:["wW","3#","4$","rR","dD","sS"],f:["dD","rR","tT","gG","vV","cC"],g:["fF","tT","yY","hH","bB","vV"],h:["gG","yY","uU","jJ","nN","bB"],i:["uU","8*","9(","oO","kK","jJ"],j:["hH","uU","iI","kK","mM","nN"],k:["jJ","iI","oO","lL",",<","mM"],l:["kK","oO","pP",";:",".>",",<"],m:["nN","jJ","kK",",<",null,null],n:["bB","hH","jJ","mM",null,null],o:["iI","9(","0)","pP","lL","kK"],p:["oO","0)","-_","[{",";:","lL"],q:[null,"1!","2@","wW","aA",null],r:["eE","4$","5%","tT","fF","dD"],s:["aA","wW","eE","dD","xX","zZ"],t:["rR","5%","6^","yY","gG","fF"],u:["yY","7&","8*","iI","jJ","hH"],v:["cC","fF","gG","bB",null,null],w:["qQ","2@","3#","eE","sS","aA"],x:["zZ","sS","dD","cC",null,null],y:["tT","6^","7&","uU","hH","gG"],z:[null,"aA","sS","xX",null,null],"{":["pP","-_","=+","]}","'\"",";:"],"|":["]}",null,null,null,null,null],"}":["[{","=+",null,"\\|",null,"'\""],"~":[null,null,null,"1!",null,null]},dvorak:{"!":["`~",null,null,"2@","'\"",null],'"':[null,"1!","2@",",<","aA",null],"#":["2@",null,null,"4$",".>",",<"],$:["3#",null,null,"5%","pP",".>"],"%":["4$",null,null,"6^","yY","pP"],"&":["6^",null,null,"8*","gG","fF"],"'":[null,"1!","2@",",<","aA",null],"(":["8*",null,null,"0)","rR","cC"],")":["9(",null,null,"[{","lL","rR"],"*":["7&",null,null,"9(","cC","gG"],"+":["/?","]}",null,"\\|",null,"-_"],",":["'\"","2@","3#",".>","oO","aA"],"-":["sS","/?","=+",null,null,"zZ"],".":[",<","3#","4$","pP","eE","oO"],"/":["lL","[{","]}","=+","-_","sS"],0:["9(",null,null,"[{","lL","rR"],1:["`~",null,null,"2@","'\"",null],2:["1!",null,null,"3#",",<","'\""],3:["2@",null,null,"4$",".>",",<"],4:["3#",null,null,"5%","pP",".>"],5:["4$",null,null,"6^","yY","pP"],6:["5%",null,null,"7&","fF","yY"],7:["6^",null,null,"8*","gG","fF"],8:["7&",null,null,"9(","cC","gG"],9:["8*",null,null,"0)","rR","cC"],":":[null,"aA","oO","qQ",null,null],";":[null,"aA","oO","qQ",null,null],"<":["'\"","2@","3#",".>","oO","aA"],"=":["/?","]}",null,"\\|",null,"-_"],">":[",<","3#","4$","pP","eE","oO"],"?":["lL","[{","]}","=+","-_","sS"],"@":["1!",null,null,"3#",",<","'\""],A:[null,"'\"",",<","oO",";:",null],B:["xX","dD","hH","mM",null,null],C:["gG","8*","9(","rR","tT","hH"],D:["iI","fF","gG","hH","bB","xX"],E:["oO",".>","pP","uU","jJ","qQ"],F:["yY","6^","7&","gG","dD","iI"],G:["fF","7&","8*","cC","hH","dD"],H:["dD","gG","cC","tT","mM","bB"],I:["uU","yY","fF","dD","xX","kK"],J:["qQ","eE","uU","kK",null,null],K:["jJ","uU","iI","xX",null,null],L:["rR","0)","[{","/?","sS","nN"],M:["bB","hH","tT","wW",null,null],N:["tT","rR","lL","sS","vV","wW"],O:["aA",",<",".>","eE","qQ",";:"],P:[".>","4$","5%","yY","uU","eE"],Q:[";:","oO","eE","jJ",null,null],R:["cC","9(","0)","lL","nN","tT"],S:["nN","lL","/?","-_","zZ","vV"],T:["hH","cC","rR","nN","wW","mM"],U:["eE","pP","yY","iI","kK","jJ"],V:["wW","nN","sS","zZ",null,null],W:["mM","tT","nN","vV",null,null],X:["kK","iI","dD","bB",null,null],Y:["pP","5%","6^","fF","iI","uU"],Z:["vV","sS","-_",null,null,null],"[":["0)",null,null,"]}","/?","lL"],"\\":["=+",null,null,null,null,null],"]":["[{",null,null,null,"=+","/?"],"^":["5%",null,null,"7&","fF","yY"],_:["sS","/?","=+",null,null,"zZ"],"`":[null,null,null,"1!",null,null],a:[null,"'\"",",<","oO",";:",null],b:["xX","dD","hH","mM",null,null],c:["gG","8*","9(","rR","tT","hH"],d:["iI","fF","gG","hH","bB","xX"],e:["oO",".>","pP","uU","jJ","qQ"],f:["yY","6^","7&","gG","dD","iI"],g:["fF","7&","8*","cC","hH","dD"],h:["dD","gG","cC","tT","mM","bB"],i:["uU","yY","fF","dD","xX","kK"],j:["qQ","eE","uU","kK",null,null],k:["jJ","uU","iI","xX",null,null],l:["rR","0)","[{","/?","sS","nN"],m:["bB","hH","tT","wW",null,null],n:["tT","rR","lL","sS","vV","wW"],o:["aA",",<",".>","eE","qQ",";:"],p:[".>","4$","5%","yY","uU","eE"],q:[";:","oO","eE","jJ",null,null],r:["cC","9(","0)","lL","nN","tT"],s:["nN","lL","/?","-_","zZ","vV"],t:["hH","cC","rR","nN","wW","mM"],u:["eE","pP","yY","iI","kK","jJ"],v:["wW","nN","sS","zZ",null,null],w:["mM","tT","nN","vV",null,null],x:["kK","iI","dD","bB",null,null],y:["pP","5%","6^","fF","iI","uU"],z:["vV","sS","-_",null,null,null],"{":["0)",null,null,"]}","/?","lL"],"|":["=+",null,null,null,null,null],"}":["[{",null,null,null,"=+","/?"],"~":[null,null,null,"1!",null,null]},keypad:{"*":["/",null,null,null,"-","+","9","8"],"+":["9","*","-",null,null,null,null,"6"],"-":["*",null,null,null,null,null,"+","9"],".":["0","2","3",null,null,null,null,null],"/":[null,null,null,null,"*","9","8","7"],0:[null,"1","2","3",".",null,null,null],1:[null,null,"4","5","2","0",null,null],2:["1","4","5","6","3",".","0",null],3:["2","5","6",null,null,null,".","0"],4:[null,null,"7","8","5","2","1",null],5:["4","7","8","9","6","3","2","1"],6:["5","8","9","+",null,null,"3","2"],7:[null,null,null,"/","8","5","4",null],8:["7",null,"/","*","9","6","5","4"],9:["8","/","*","-","+",null,"6","5"]},mac_keypad:{"*":["/",null,null,null,null,null,"-","9"],"+":["6","9","-",null,null,null,null,"3"],"-":["9","/","*",null,null,null,"+","6"],".":["0","2","3",null,null,null,null,null],"/":["=",null,null,null,"*","-","9","8"],0:[null,"1","2","3",".",null,null,null],1:[null,null,"4","5","2","0",null,null],2:["1","4","5","6","3",".","0",null],3:["2","5","6","+",null,null,".","0"],4:[null,null,"7","8","5","2","1",null],5:["4","7","8","9","6","3","2","1"],6:["5","8","9","-","+",null,"3","2"],7:[null,null,null,"=","8","5","4",null],8:["7",null,"=","/","9","6","5","4"],9:["8","=","/","*","-","+","6","5"],"=":[null,null,null,null,"/","9","8","7"]}},module.exports=adjacency_graphs;

},{}],2:[function(require,module,exports){
var frequency_lists;frequency_lists={male_names:"james,john,alonso".split(","),female_names:"mary,patricia,allyn".split(","),
surnames:"smith,beadling,beacher,bazar,baysmore".split(","),
passwords:"password,123456,12345678,1234,qwerty,12345,eeeee1,eyphed".split(","),
english:"you,i,to,the,a,and,that,it,of,me,what,is,a'right".split(",")
},module.exports=frequency_lists;

},{}],3:[function(require,module,exports){
var matching,scoring,time,zxcvbn;matching=require("./matching"),scoring=require("./scoring"),time=function(){return(new Date).getTime()},zxcvbn=function(n,e){var t,i,r,c,m,o,a,u;for(null==e&&(e=[]),u=time(),a=[],i=0,r=e.length;r>i;i++)t=e[i],("string"==(m=typeof t)||"number"===m||"boolean"===m)&&a.push(t.toString().toLowerCase());return matching.set_user_input_dictionary(a),c=matching.omnimatch(n),o=scoring.minimum_entropy_match_sequence(n,c),o.calc_time=time()-u,o},module.exports=zxcvbn;

},{"./matching":4,"./scoring":5}],4:[function(require,module,exports){
var DATE_MAX_YEAR,DATE_MIN_YEAR,DATE_SPLITS,GRAPHS,L33T_TABLE,RANKED_DICTIONARIES,REGEXEN,REGEX_PRECEDENCE,SEQUENCES,adjacency_graphs,build_ranked_dict,frequency_lists,matching,scoring,indexOf=[].indexOf||function(e){for(var t=0,n=this.length;n>t;t++)if(t in this&&this[t]===e)return t;return-1};frequency_lists=require("./frequency_lists"),adjacency_graphs=require("./adjacency_graphs"),scoring=require("./scoring"),build_ranked_dict=function(e){var t,n,r,i,a;for(i={},t=1,r=0,n=e.length;n>r;r++)a=e[r],i[a]=t,t+=1;return i},RANKED_DICTIONARIES={passwords:build_ranked_dict(frequency_lists.passwords),english:build_ranked_dict(frequency_lists.english),surnames:build_ranked_dict(frequency_lists.surnames),male_names:build_ranked_dict(frequency_lists.male_names),female_names:build_ranked_dict(frequency_lists.female_names)},GRAPHS={qwerty:adjacency_graphs.qwerty,dvorak:adjacency_graphs.dvorak,keypad:adjacency_graphs.keypad,mac_keypad:adjacency_graphs.mac_keypad},SEQUENCES={lower:"abcdefghijklmnopqrstuvwxyz",upper:"ABCDEFGHIJKLMNOPQRSTUVWXYZ",digits:"0123456789"},L33T_TABLE={a:["4","@"],b:["8"],c:["(","{","[","<"],e:["3"],g:["6","9"],i:["1","!","|"],l:["1","|","7"],o:["0"],s:["$","5"],t:["+","7"],x:["%"],z:["2"]},REGEXEN={alphanumeric:/[a-zA-Z0-9]{2,}/g,alpha:/[a-zA-Z]{2,}/g,alpha_lower:/[a-z]{2,}/g,alpha_upper:/[A-Z]{2,}/g,digits:/\d{2,}/g,symbols:/[\W_]{2,}/g,recent_year:/19\d\d|200\d|201\d/g},REGEX_PRECEDENCE={alphanumeric:0,alpha:1,alpha_lower:2,alpha_upper:2,digits:2,symbols:2,recent_year:3},DATE_MAX_YEAR=2050,DATE_MIN_YEAR=1e3,DATE_SPLITS={4:[[1,2],[2,3]],5:[[1,3],[2,3]],6:[[1,2],[2,4],[4,5]],7:[[1,3],[2,3],[4,5],[4,6]],8:[[2,4],[4,6]]},matching={empty:function(e){var t;return 0===function(){var n;n=[];for(t in e)n.push(t);return n}().length},extend:function(e,t){return e.push.apply(e,t)},translate:function(e,t){var n;return function(){var r,i,a,s;for(a=e.split(""),s=[],i=0,r=a.length;r>i;i++)n=a[i],s.push(t[n]||n);return s}().join("")},mod:function(e,t){return(e%t+t)%t},sorted:function(e){return e.sort(function(e,t){return e.i-t.i||e.j-t.j})},omnimatch:function(e){var t,n,r,i,a;for(i=[],r=[this.dictionary_match,this.reverse_dictionary_match,this.l33t_match,this.spatial_match,this.repeat_match,this.sequence_match,this.regex_match,this.date_match],a=0,t=r.length;t>a;a++)n=r[a],this.extend(i,n.call(this,e));return this.sorted(i)},dictionary_match:function(e,t){var n,r,i,a,s,o,h,l,c,u,_,f,d,p;null==t&&(t=RANKED_DICTIONARIES),s=[],a=e.length,l=e.toLowerCase();for(n in t)for(u=t[n],r=o=0,_=a;_>=0?_>o:o>_;r=_>=0?++o:--o)for(i=h=f=r,d=a;d>=f?d>h:h>d;i=d>=f?++h:--h)l.slice(r,+i+1||9e9)in u&&(p=l.slice(r,+i+1||9e9),c=u[p],s.push({pattern:"dictionary",i:r,j:i,token:e.slice(r,+i+1||9e9),matched_word:p,rank:c,dictionary_name:n,reversed:!1}));return this.sorted(s)},reverse_dictionary_match:function(e,t){var n,r,i,a,s,o;for(null==t&&(t=RANKED_DICTIONARIES),o=e.split("").reverse().join(""),i=this.dictionary_match(o,t),a=0,n=i.length;n>a;a++)r=i[a],r.token=r.token.split("").reverse().join(""),r.reversed=!0,s=[e.length-1-r.j,e.length-1-r.i],r.i=s[0],r.j=s[1];return this.sorted(i)},set_user_input_dictionary:function(e){return RANKED_DICTIONARIES.user_inputs=build_ranked_dict(e.slice())},relevant_l33t_subtable:function(e,t){var n,r,i,a,s,o,h,l,c,u;for(s={},o=e.split(""),a=0,r=o.length;r>a;a++)n=o[a],s[n]=!0;u={};for(i in t)c=t[i],h=function(){var e,t,n;for(n=[],t=0,e=c.length;e>t;t++)l=c[t],l in s&&n.push(l);return n}(),h.length>0&&(u[i]=h);return u},enumerate_l33t_subs:function(e){var t,n,r,i,a,s,o,h,l,c,u,_,f,d,p;a=function(){var t;t=[];for(i in e)t.push(i);return t}(),p=[[]],n=function(e){var t,n,r,a,s,o,h,l;for(n=[],s={},o=0,a=e.length;a>o;o++)h=e[o],t=function(){var e,t,n;for(n=[],l=t=0,e=h.length;e>t;l=++t)i=h[l],n.push([i,l]);return n}(),t.sort(),r=function(){var e,n,r;for(r=[],l=n=0,e=t.length;e>n;l=++n)i=t[l],r.push(i+","+l);return r}().join("-"),r in s||(s[r]=!0,n.push(h));return n},r=function(t){var i,a,s,o,h,l,c,u,_,f,d,E,g,m,y,A;if(t.length){for(a=t[0],g=t.slice(1),c=[],d=e[a],u=0,h=d.length;h>u;u++)for(o=d[u],_=0,l=p.length;l>_;_++){for(m=p[_],i=-1,s=f=0,E=m.length;E>=0?E>f:f>E;s=E>=0?++f:--f)if(m[s][0]===o){i=s;break}-1===i?(A=m.concat([[o,a]]),c.push(A)):(y=m.slice(0),y.splice(i,1),y.push([o,a]),c.push(m),c.push(y))}return p=n(c),r(g)}},r(a),d=[];for(l=0,o=p.length;o>l;l++){for(_=p[l],f={},c=0,h=_.length;h>c;c++)u=_[c],s=u[0],t=u[1],f[s]=t;d.push(f)}return d},l33t_match:function(e,t,n){var r,i,a,s,o,h,l,c,u,_,f,d,p,E,g,m;for(null==t&&(t=RANKED_DICTIONARIES),null==n&&(n=L33T_TABLE),l=[],_=this.enumerate_l33t_subs(this.relevant_l33t_subtable(e,n)),c=0,a=_.length;a>c&&(d=_[c],!this.empty(d));c++)for(E=this.translate(e,d),f=this.dictionary_match(E,t),u=0,s=f.length;s>u;u++)if(o=f[u],g=e.slice(o.i,+o.j+1||9e9),g.toLowerCase()!==o.matched_word){h={};for(p in d)r=d[p],-1!==g.indexOf(p)&&(h[p]=r);o.l33t=!0,o.token=g,o.sub=h,o.sub_display=function(){var e;e=[];for(i in h)m=h[i],e.push(i+" -> "+m);return e}().join(", "),l.push(o)}return this.sorted(l)},spatial_match:function(e,t){var n,r,i;null==t&&(t=GRAPHS),i=[];for(r in t)n=t[r],this.extend(i,this.spatial_match_helper(e,n,r));return this.sorted(i)},SHIFTED_RX:/[~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:"ZXCVBNM<>?]/,spatial_match_helper:function(e,t,n){var r,i,a,s,o,h,l,c,u,_,f,d,p,E,g;for(f=[],l=0;l<e.length-1;)for(c=l+1,u=null,g=0,E="qwerty"!==n&&"dvorak"!==n||!this.SHIFTED_RX.exec(e.charAt(l))?0:1;;){if(p=e.charAt(c-1),o=!1,h=-1,s=-1,i=t[p]||[],c<e.length)for(a=e.charAt(c),d=0,_=i.length;_>d;d++)if(r=i[d],s+=1,r&&-1!==r.indexOf(a)){o=!0,h=s,1===r.indexOf(a)&&(E+=1),u!==h&&(g+=1,u=h);break}if(!o){c-l>2&&f.push({pattern:"spatial",i:l,j:c-1,token:e.slice(l,c),graph:n,turns:g,shifted_count:E}),l=c;break}c+=1}return f},repeat_match:function(e){var t,n,r,i,a,s,o,h,l,c,u,_,f,d,p;for(d=[],a=/(.+)\1+/g,c=/(.+?)\1+/g,u=/^(.+?)\1+$/,l=0;l<e.length&&(a.lastIndex=c.lastIndex=l,s=a.exec(e),_=c.exec(e),null!=s);)s[0].length>_[0].length?(f=s,i=u.exec(f[0])[1]):(f=_,i=f[1]),p=[f.index,f.index+f[0].length-1],o=p[0],h=p[1],t=scoring.minimum_entropy_match_sequence(i,this.omnimatch(i)),r=t.match_sequence,n=t.entropy,d.push({pattern:"repeat",i:o,j:h,token:f[0],base_token:i,base_entropy:n,base_matches:r}),l=h+1;return d},sequence_match:function(e){var t,n,r,i,a,s,o,h,l,c,u,_,f;s=3,a=[];for(_ in SEQUENCES)for(u=SEQUENCES[_],l=[1,-1],h=0,i=l.length;i>h;h++)for(t=l[h],n=0;n<e.length;)if(c=e.charAt(n),indexOf.call(u,c)<0)n+=1;else{for(r=n+1,f=u.indexOf(e.charAt(n));r<e.length&&(o=this.mod(f+t,u.length),u.indexOf(e.charAt(r))===o);)r+=1,f=o;r-=1,r-n+1>=s&&a.push({pattern:"sequence",i:n,j:r,token:e.slice(n,+r+1||9e9),sequence_name:_,sequence_space:u.length,ascending:1===t}),n=r+1}return this.sorted(a)},regex_match:function(e,t){var n,r,i,a,s,o,h,l,c,u,_,f,d;null==t&&(t=REGEXEN),o=[];for(h in t)for(_=t[h],_.lastIndex=0;f=_.exec(e);)d=f[0],o.push({pattern:"regex",token:d,i:f.index,j:f.index+f[0].length-1,regex_name:h,regex_match:f});for(u={},n=function(e){return e.i+"-"+e.j},l=0,a=o.length;a>l;l++)s=o[l],i=n(s),c=REGEX_PRECEDENCE[s.regex_name],i in u&&(r=u[i],r>=c)||(u[i]=c);return this.sorted(o.filter(function(e){return u[n(e)]===REGEX_PRECEDENCE[e.regex_name]}))},date_match:function(e){var t,n,r,i,a,s,o,h,l,c,u,_,f,d,p,E,g,m,y,A,R,v,I,x,k,N,D,T,b,j,S,C,q,O;for(_=[],f=/^\d{4,8}$/,d=/^(\d{1,4})([\s\/\\_.-])(\d{1,2})\2(\d{1,4})$/,s=g=0,R=e.length-4;R>=0?R>=g:g>=R;s=R>=0?++g:--g)for(o=m=v=s+3,I=s+7;(I>=v?I>=m:m>=I)&&!(o>=e.length);o=I>=v?++m:--m)if(O=e.slice(s,+o+1||9e9),f.exec(O)){for(r=[],x=DATE_SPLITS[O.length],y=0,c=x.length;c>y;y++)k=x[y],h=k[0],l=k[1],a=this.map_ints_to_dmy([parseInt(O.slice(0,h)),parseInt(O.slice(h,l)),parseInt(O.slice(l))]),null!=a&&r.push(a);if(r.length>0){for(t=r[0],p=function(e){return Math.abs(e.year-scoring.REFERENCE_YEAR)},E=p(r[0]),N=r.slice(1),A=0,u=N.length;u>A;A++)n=N[A],i=p(n),E>i&&(D=[n,i],t=D[0],E=D[1]);_.push({pattern:"date",token:O,i:s,j:o,separator:"",year:t.year,month:t.month,day:t.day})}}for(s=C=0,T=e.length-6;T>=0?T>=C:C>=T;s=T>=0?++C:--C)for(o=q=b=s+5,j=s+9;(j>=b?j>=q:q>=j)&&!(o>=e.length);o=j>=b?++q:--q)O=e.slice(s,+o+1||9e9),S=d.exec(O),null!=S&&(a=this.map_ints_to_dmy([parseInt(S[1]),parseInt(S[3]),parseInt(S[4])]),null!=a&&_.push({pattern:"date",token:O,i:s,j:o,separator:S[2],year:a.year,month:a.month,day:a.day}));return this.sorted(_.filter(function(e){var t,n,r,i;for(t=!1,i=0,n=_.length;n>i;i++)if(r=_[i],e!==r&&r.i<=e.i&&r.j>=e.j){t=!0;break}return!t}))},map_ints_to_dmy:function(e){var t,n,r,i,a,s,o,h,l,c,u,_,f,d,p,E;if(!(e[1]>31||e[1]<=0)){for(o=0,h=0,p=0,s=0,r=e.length;r>s;s++){if(n=e[s],n>99&&DATE_MIN_YEAR>n||n>DATE_MAX_YEAR)return;n>31&&(h+=1),n>12&&(o+=1),0>=n&&(p+=1)}if(!(h>=2||3===o||p>=2)){for(c=[[e[2],e.slice(0,2)],[e[0],e.slice(1,3)]],l=0,i=c.length;i>l;l++)if(_=c[l],E=_[0],d=_[1],E>=DATE_MIN_YEAR&&DATE_MAX_YEAR>=E)return t=this.map_ints_to_dm(d),null!=t?{year:E,month:t.month,day:t.day}:void 0;for(u=0,a=c.length;a>u;u++)if(f=c[u],E=f[0],d=f[1],t=this.map_ints_to_dm(d),null!=t)return E=this.two_to_four_digit_year(E),{year:E,month:t.month,day:t.day}}}},map_ints_to_dm:function(e){var t,n,r,i,a,s;for(a=[e,e.slice().reverse()],i=0,n=a.length;n>i;i++)if(s=a[i],t=s[0],r=s[1],t>=1&&31>=t&&r>=1&&12>=r)return{day:t,month:r}},two_to_four_digit_year:function(e){return e>99?e:e>50?e+scoring.REFERENCE_YEAR-100:e+scoring.REFERENCE_YEAR}},module.exports=matching;

},{"./adjacency_graphs":1,"./frequency_lists":2,"./scoring":5}],5:[function(require,module,exports){
var adjacency_graphs,calc_average_degree,k,scoring,v;adjacency_graphs=require("./adjacency_graphs"),calc_average_degree=function(t){var e,r,n,a,i,o;e=0;for(n in t)i=t[n],e+=function(){var t,e,r;for(r=[],t=0,e=i.length;e>t;t++)a=i[t],a&&r.push(a);return r}().length;return e/=function(){var e;e=[];for(r in t)o=t[r],e.push(r);return e}().length},scoring={nCk:function(t,e){var r,n,a,i;if(e>t)return 0;if(0===e)return 1;for(a=1,r=n=1,i=e;i>=1?i>=n:n>=i;r=i>=1?++n:--n)a*=t,a/=r,t-=1;return a},lg:function(t){return Math.log(t)/Math.log(2)},minimum_entropy_match_sequence:function(t,e){var r,n,a,i,o,h,s,_,c,u,p,l,g,f,E,y,d,A,R,v,m;for(n=this.calc_bruteforce_cardinality(t),m=[],r=[],s=_=0,A=t.length;A>=0?A>_:_>A;s=A>=0?++_:--_)for(m[s]=(m[s-1]||0)+this.lg(n),r[s]=null,p=0,c=e.length;c>p;p++)g=e[p],g.j===s&&(R=[g.i,g.j],o=R[0],h=R[1],a=(m[o-1]||0)+this.calc_entropy(g),a<m[h]&&(m[h]=a,r[h]=g));for(f=[],s=t.length-1;s>=0;)g=r[s],g?(f.push(g),s=g.i-1):s-=1;for(f.reverse(),l=function(e){return function(r,a){return{pattern:"bruteforce",i:r,j:a,token:t.slice(r,+a+1||9e9),entropy:e.lg(Math.pow(n,a-r+1)),cardinality:n}}}(this),s=0,E=[],d=0,u=f.length;u>d;d++)g=f[d],v=[g.i,g.j],o=v[0],h=v[1],o-s>0&&E.push(l(s,o-1)),s=h+1,E.push(g);return s<t.length&&E.push(l(s,t.length-1)),f=E,y=m[t.length-1]||0,i=this.entropy_to_crack_time(y),{password:t,entropy:this.round_to_x_digits(y,3),match_sequence:f,crack_time:this.round_to_x_digits(i,3),crack_time_display:this.display_time(i),score:this.crack_time_to_score(i)}},round_to_x_digits:function(t,e){return Math.round(t*Math.pow(10,e))/Math.pow(10,e)},SECONDS_PER_GUESS:1e-4,entropy_to_crack_time:function(t){return.5*Math.pow(2,t)*this.SECONDS_PER_GUESS},crack_time_to_score:function(t){return t<Math.pow(10,2)?0:t<Math.pow(10,4)?1:t<Math.pow(10,6)?2:t<Math.pow(10,8)?3:4},calc_entropy:function(t){var e;return null!=t.entropy?t.entropy:(e={dictionary:this.dictionary_entropy,spatial:this.spatial_entropy,repeat:this.repeat_entropy,sequence:this.sequence_entropy,regex:this.regex_entropy,date:this.date_entropy},t.entropy=e[t.pattern].call(this,t))},repeat_entropy:function(t){var e;return e=t.token.length/t.base_token.length,t.base_entropy+this.lg(e)},sequence_entropy:function(t){var e,r;return r=t.token.charAt(0),e="a"===r||"A"===r||"z"===r||"Z"===r||"0"===r||"1"===r||"9"===r?2:r.match(/\d/)?this.lg(10):r.match(/[a-z]/)?this.lg(26):this.lg(26)+1,t.ascending||(e+=1),e+this.lg(t.token.length)},MIN_YEAR_SPACE:20,REFERENCE_YEAR:2e3,regex_entropy:function(t){var e,r;if(e={alpha_lower:26,alpha_upper:26,alpha:52,alphanumeric:62,digits:10,symbols:33},t.regex_name in e)return this.lg(Math.pow(e[t.regex_name],t.token.length));switch(t.regex_name){case"recent_year":return r=Math.abs(parseInt(t.regex_match[0])-this.REFERENCE_YEAR),r=Math.max(r,this.MIN_YEAR_SPACE),this.lg(r)}},date_entropy:function(t){var e,r;return r=Math.max(Math.abs(t.year-this.REFERENCE_YEAR),this.MIN_YEAR_SPACE),e=this.lg(31*r*12),t.has_full_year&&(e+=1),t.separator&&(e+=2),e},KEYBOARD_AVERAGE_DEGREE:calc_average_degree(adjacency_graphs.qwerty),KEYPAD_AVERAGE_DEGREE:calc_average_degree(adjacency_graphs.keypad),KEYBOARD_STARTING_POSITIONS:function(){var t,e;t=adjacency_graphs.qwerty,e=[];for(k in t)v=t[k],e.push(k);return e}().length,KEYPAD_STARTING_POSITIONS:function(){var t,e;t=adjacency_graphs.keypad,e=[];for(k in t)v=t[k],e.push(k);return e}().length,spatial_entropy:function(t){var e,r,n,a,i,o,h,s,_,c,u,p,l,g,f,E,y,d;for("qwerty"===(l=t.graph)||"dvorak"===l?(y=this.KEYBOARD_STARTING_POSITIONS,a=this.KEYBOARD_AVERAGE_DEGREE):(y=this.KEYPAD_STARTING_POSITIONS,a=this.KEYPAD_AVERAGE_DEGREE),u=0,e=t.token.length,d=t.turns,o=s=2,g=e;g>=2?g>=s:s>=g;o=g>=2?++s:--s)for(p=Math.min(d,o-1),h=_=1,f=p;f>=1?f>=_:_>=f;h=f>=1?++_:--_)u+=this.nCk(o-1,h-1)*y*Math.pow(a,h);if(i=this.lg(u),t.shifted_count)if(r=t.shifted_count,n=t.token.length-t.shifted_count,0===n)i+=1;else{for(u=0,o=c=1,E=Math.min(r,n);E>=1?E>=c:c>=E;o=E>=1?++c:--c)u+=this.nCk(r+n,o);i+=this.lg(u)}return i},dictionary_entropy:function(t){return t.base_entropy=this.lg(t.rank),t.uppercase_entropy=this.extra_uppercase_entropy(t),t.reversed_entropy=t.reversed&&1||0,t.l33t_entropy=this.extra_l33t_entropy(t),t.base_entropy+t.uppercase_entropy+t.l33t_entropy+t.reversed_entropy},START_UPPER:/^[A-Z][^A-Z]+$/,END_UPPER:/^[^A-Z]+[A-Z]$/,ALL_UPPER:/^[^a-z]+$/,ALL_LOWER:/^[^A-Z]+$/,extra_uppercase_entropy:function(t){var e,r,n,a,i,o,h,s,_,c,u,p;if(p=t.token,p.match(this.ALL_LOWER))return 0;for(_=[this.START_UPPER,this.END_UPPER,this.ALL_UPPER],i=0,o=_.length;o>i;i++)if(u=_[i],p.match(u))return 1;for(r=function(){var t,e,r,a;for(r=p.split(""),a=[],e=0,t=r.length;t>e;e++)n=r[e],n.match(/[A-Z]/)&&a.push(n);return a}().length,e=function(){var t,e,r,a;for(r=p.split(""),a=[],e=0,t=r.length;t>e;e++)n=r[e],n.match(/[a-z]/)&&a.push(n);return a}().length,s=0,a=h=1,c=Math.min(r,e);c>=1?c>=h:h>=c;a=c>=1?++h:--h)s+=this.nCk(r+e,a);return this.lg(s)},extra_l33t_entropy:function(t){var e,r,n,a,i,o,h,s,_,c,u,p,l;if(!t.l33t)return 0;i=0,c=t.sub;for(p in c)if(l=c[p],a=t.token.toLowerCase().split(""),e=function(){var t,e,r;for(r=[],t=0,e=a.length;e>t;t++)n=a[t],n===p&&r.push(n);return r}().length,r=function(){var t,e,r;for(r=[],t=0,e=a.length;e>t;t++)n=a[t],n===l&&r.push(n);return r}().length,0===e||0===r)i+=1;else{for(s=Math.min(r,e),_=0,o=h=1,u=s;u>=1?u>=h:h>=u;o=u>=1?++h:--h)_+=this.nCk(r+e,o);i+=this.lg(_)}return i},calc_bruteforce_cardinality:function(t){var e,r,n,a,i,o,h,s,_,c,u,p,l,g,f,E,y,d,A,R,v,m;for(y=function(){var t,e;for(e=[],i=t=0;6>t;i=++t)e.push(!1);return e}(),u=y[0],m=y[1],a=y[2],R=y[3],s=y[4],h=y[5],v=[],d=t.split(""),o=0,_=d.length;_>o;o++)r=d[o],f=r.charCodeAt(0),f>=48&&57>=f?a=!0:f>=65&&90>=f?m=!0:f>=97&&122>=f?u=!0:127>=f?R=!0:f>=128&&191>=f?s=!0:f>=192&&255>=f?h=!0:f>255&&v.push(f);if(e=0,a&&(e+=10),m&&(e+=26),u&&(e+=26),R&&(e+=33),s&&(e+=64),h&&(e+=64),v.length){for(g=l=v[0],A=v.slice(1),p=0,c=A.length;c>p;p++)n=A[p],g>n&&(g=n),n>l&&(l=n);E=l-g+1,40>E&&(E=40),E>100&&(E=100),e+=E}return e},display_time:function(t){var e,r,n,a,i,o,h,s,_,c;return h=60,o=60*h,n=24*o,s=31*n,c=12*s,r=100*c,_=h>t?[t,t+" second"]:o>t?(e=Math.round(t/h),[e,e+" minute"]):n>t?(e=Math.round(t/o),[e,e+" hour"]):s>t?(e=Math.round(t/n),[e,e+" day"]):c>t?(e=Math.round(t/s),[e,e+" month"]):r>t?(e=Math.round(t/c),[e,e+" year"]):[null,"centuries"],a=_[0],i=_[1],null!=a&&1!==a&&(i+="s"),i}},module.exports=scoring;

},{"./adjacency_graphs":1}]},{},[3])(3)
});
//# sourceMappingURL=zxcvbn.js.map
