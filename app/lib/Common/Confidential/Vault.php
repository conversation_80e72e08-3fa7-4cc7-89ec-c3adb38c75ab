<?php

namespace Common\Confidential;

use Cache\Adapter\Apcu\ApcuCachePool;
use Vault\CacheClientByUserPass;
use Octopus\Logger;
use Octopus\Logger\Handler\StreamHandler;

class Vault
{
    public $vaultData = [];

    public function Vault()
    {
        $config = [
            'url' => $_ENV['vault_address'],
            'username' => $_ENV['vault_username'],
            'password' => $_ENV['vault_password'],
        ];
        $cache = new ApcuCachePool();
        $logPath = APPPATH . "/logs/" . date("Ymd") . "/vault";
        $logger = new Logger("vault");
        $logger->pushHandler(new StreamHandler($logPath . "/" . date('H') . ".log"));

        $client = new CacheClientByUserPass($config, $cache, $logger);
        $data = $client->getSecrets('ywzt_kv', 'login_secret');
        $this->vaultData = $data['data'] ?? [];
    }

    /**
     * 获取mysql加解密秘钥
     * @return mixed|string
     */
    public function GetMysqlSecret()
    {
        $loadData = $this->vaultData;
        return $loadData['mysql_secret'] ?? '';
    }
}