<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/4/28
 * Time: 17:57
 */

namespace Service\UserBase\CustomRules;

use Service\UserBase\TipsMap;

class KxbbzNicknameRule extends NicknameRule
{

    /**
     * 开心步步赚默认的昵称
     * @param $passid
     * @return mixed
     */
    public static function generateNickname($passid)
    {
        $memberModel = loadModel('member');
        $phone = $memberModel->getPhoneByPassid($passid);
        if (!empty($phone)) {
            return substr_replace($phone, '****', 3, 4);
        } else {
            return '100****2345';
        }
    }

    /**
     * 不允许使用默认昵称
     * @param string $nickname 昵称
     * @return bool
     */
    public static function notAllowUseDefaultNickname($nickname)
    {
        if (parent::notAllowUseDefaultNickname($nickname)) {
            return true;
        }
        if (preg_match("/^\d{3}\*{4}\d{4}$/", $nickname)) {

            return true;
        }

        return false;
    }

}
