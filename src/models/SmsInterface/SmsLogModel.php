<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：SmsCronModel.php
 */

namespace SmsInterface;

class SmsLogModel extends \Model
{

    /**
     * 初始数据库
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    /**
     * 批量获取渠道商返回状态
     * @param array $logIds log ids
     * @return array|bool
     */
    public function getChannelCallbackStatusByLogIds($logIds = array())
    {
        $result = array();
        //cache表
        if (count($logIds) > 0)
        {
            $params = array();
            foreach ($logIds as $key => $id)
            {
                $params[":id$key"] = trim($id);
            }
            $tableName = 'sms_logs_cache';
            $sql = "SELECT log_id,phone,callback_time,callback_status FROM {$tableName}  where `log_id` in (" . implode(",", array_keys($params)) . ")";
            $logStatus = $this->pdo->findAll($sql, $params);
            foreach ($logStatus as $logLine)
            {
                $result[$logLine['log_id']] = $logLine;
            }
        }
        $noFound = array_diff_key($logIds, $result);
        //当月表
        if (count($noFound) > 0)
        {
            $params = array();
            foreach ($noFound as $key => $id)
            {
                $params[":id$key"] = trim($id);
            }
            $tableName = self::getTableName();
            $sql = "SELECT log_id,phone,callback_time,callback_status FROM {$tableName}  where `log_id` in (" . implode(",", array_keys($params)) . ")";
            $logStatus = $this->pdo->findAll($sql, $params);
            foreach ($logStatus as $logLine)
            {
                $result[$logLine['log_id']] = $logLine;
            }
        }
        $noFound = array_diff_key($logIds, $result);
        //上月表，每月前6天生效
        if (count($noFound) > 0 && date('d') <= 6)
        {
            $params = array();
            foreach ($noFound as $key => $id)
            {
                $params[":id$key"] = trim($id);
            }
            //因为只有前6天生效，所以前15天可以表示上个月
            $tableName = self::getTableName(date('Ym', time() - 86400 * 15));
            $sql = "SELECT log_id,phone,callback_time,callback_status FROM {$tableName}  where `log_id` in (" . implode(",", array_keys($params)) . ")";
            $logStatus = $this->pdo->findAll($sql, $params);
            foreach ($logStatus as $logLine)
            {
                $result[$logLine['log_id']] = $logLine;
            }
        }
        return $result;
    }

    /**
     * 获取渠道商返回状态
     * @param array $data data
     * @return array|bool
     */
    public function getRecentLogIdByPhone($phone)
    {
        $sql = "SELECT `log_id` FROM `sms_logs_cache` WHERE `phone` = :phone LIMIT 20";
        $res = $this->pdo->findAll($sql, array(':phone' => $phone));
        return $res;
    }

    /**
     * 获取渠道商返回状态
     * @param array $data data
     * @return array|bool
     */
    public function getChannelCallbackStatus($data)
    {
        $sql = "SELECT * FROM sms_channel_status WHERE log_id = :log_id";
        $logStatus = $this->pdo->find($sql, array(':log_id' => $data['log_id']));
        return $logStatus;
    }

    /**
     * 渠道商回调状态
     * */
    public function addChannelCallbackStatus($data)
    {
        $logStatus = $this->getChannelCallbackStatus($data);
        if (empty($logStatus))
        {
            $channelData = array(
                'log_id'   => $data['log_id'],
                'add_time' => date('Y-m-d H:i:s'),
                'phone'    => $data['phone'],
                'status'   => $data['status'],
                'message'  => $data['message'],
            );
            $isTrue = $this->pdo->insert('sms_channel_status', $channelData);
        }
        else
        {
            $condition = array('where' => ' log_id = :log_id ', 'params' => array('log_id' => $data['log_id']));
            $updata['status'] = $data['status'];
            $updata['message'] = $data['message'];
            $isTrue = $this->pdo->update('sms_channel_status', $updata, $condition);
        }
        $condition = array('where' => ' log_id = :log_id ', 'params' => array('log_id' => $data['log_id']));
        $updata['status'] = $data['status'];
        $updata['message'] = $data['message'];
        $isOK = $this->pdo->update(self::getTableName(), $updata, $condition);
        if (!$isOK)
        {
            $isOK = $this->pdo->update(self::getTableName(date('Ym')), $updata, $condition);
        }
        return $isTrue !== false ? true : false;
    }


    /**
     * 渠道商回调状态
     * @param array $items 数据
     * @return bool
     */
    public function addChannelCallbackStatusMulti($items)
    {
        foreach ($items as $item)
        {
            $condition = array('where' => ' log_id = :log_id ', 'params' => array('log_id' => $item['log_id']));
            $update = array(
                'callback_time'    => $item['add_time'],
                'callback_status'  => $item['status'],
                'callback_message' => $item['message'],
            );
            $isOK = $this->pdo->update(self::getTableName(), $update, $condition);
            if (!$isOK)
            {
                $this->pdo->update(self::getTableName(date('Ym', strtotime('-1 month'))), $update, $condition);
            }
            $updateCache = array(
                'callback_time'   => $item['add_time'],
                'callback_status' => $item['status'],
            );
            $this->pdo->update('sms_logs_cache', $updateCache, $condition);
        }
        return $isOK !== false ? true : false;
    }

    /**
     * 批量插入函数修改状态
     * @param string $table         表名
     * @param array  $data          批量修改的数据
     * @param array  $updateColumns 需要更改的字段
     * @return boolean
     */
    public function insertOrUpdateMulti($table, $data, $updateColumns)
    {
        $updateColumnArray = array();
        foreach ($updateColumns as $updateColumn)
        {
            array_push($updateColumnArray, "$updateColumn=VALUES($updateColumn)");
        }
        $str = implode(',', $updateColumnArray);
        $columns = array_keys($data[0]);
        $values = array();
        $bindValues = array();
        foreach ($data as $rowKey => $row)
        {
            $value = array();
            foreach ($columns as $colKey => $column)
            {
                $value[] = ":{$column}$rowKey";
                $bindValues[":{$column}$rowKey"] = $row[$column];
            }
            $values[] = "(" . implode(", ", $value) . ")";
        }
        $sql = "INSERT INTO $table (`" . implode("`, `", $columns) . "`) VALUES " . implode(",", $values) . "  ON DUPLICATE KEY UPDATE $str";
        return $this->pdo->query($sql, $bindValues);
    }


    /**
     * 获取回复短信信息
     * */
    public function getSmsReceiptInfo($data)
    {
        $sql = "SELECT * FROM `sms_receipt_info` WHERE msgId = :msgId ";
        $smsReceiptInfo = $this->pdo->find($sql, array(':msgId' => $data['msgId']));
        return $smsReceiptInfo;
    }

    /**
     * 用户回复短信回调记录
     * */
    public function addSmsReceiptInfo($data)
    {
        //$smsReceiptInfo = $this->getSmsReceiptInfo($data);
        //if (empty($smsReceiptInfo))
        //{
        $channelData = array(
            'msgId'      => $data['msgId'],
            'phone'      => $data['phone'],
            'providerId' => $data['providerId'],
            'add_time'   => date('Y-m-d H:i:s'),
            'is_unsub'   => $data['is_unsub'],
            'message'    => $data['message'],
        );
        if (isset($data['add_time']))
        {
            $channelData['add_time'] = $data['add_time'];
        }
        $isTrue = $this->pdo->insert('sms_receipt_info', $channelData);

        //}
        /*
        else
        {
        $condition = array('where' => ' msgId = :msgId ', 'params' => array('msgId' =>
        $data['msgId']));
        $updata['message'] = $data['message'];
        $updata['add_time'] = date('Y-m-d H:i:s');
        $isTrue = $this->pdo->update('sms_receipt_info', $updata, $condition);
        }*/
        return $isTrue !== false ? true : false;
    }

    public function getLogInfo($logId)
    {
        $table = "sms_logs_archive_".date('Ym');
        $sql = "SELECT * FROM `{$table}` WHERE log_id = :logID ";
        return $this->pdo->find($sql, array(':logID' => $logId));
    }
}
