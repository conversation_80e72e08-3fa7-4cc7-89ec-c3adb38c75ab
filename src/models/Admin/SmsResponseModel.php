<?php

namespace Admin;
class SmsResponseModel extends \Model
{
    /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }
    
    
    public function getSmsReceiptInfo($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col} FROM `sms_receipt_info` ";

        
        if (!empty($data['phone']))
        {
            $where .= " phone = :phone AND";
            $paramArr[':phone'] = $data['phone'];
        }
        
        if (isset($data['msgId']))
        {
            $where .= " msgId = :msgId AND";
            $paramArr[':msgId'] = $data['msgId'];
        }
        
        if (isset($data['isUnsub']) && $data['isUnsub'] >=0 )
        {
            $where .= " is_unsub = :isUnsub AND";
            $paramArr[':isUnsub'] = $data['isUnsub'];
        }
        
        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $where .= '  add_time >=:startTime AND add_time <=:endTime AND';
            $paramArr[':startTime'] = $data['startTime'] . ' 00:00:00 ';
            $paramArr[':endTime'] = $data['endTime'] .' 23:59:59';
        }
        
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by sri_id desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }
    
}
?>