<?php

namespace Admin;

class SmsTotalModel extends \Model
{

    /**
     * SmsTotalModel constructor.
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    /**
     * 供应商每日短信发送量统计
     * @param array $data data
     * @return array|bool
     */
    public function getOperatorTotal($data)
    {
        $tableName = self::getTableName(date('Ym', strtotime($data['startTime'])));
        $scsSql = "";
        if (isset($data['returnStatus']))
        {
            switch ($data['returnStatus'])
            {
                case 0:
                default:
                    break;
                case 1:
                    $scsSql = " AND callback_status = 1 ";
                    break;
                case 2:
                    $scsSql = " AND callback_status = 0 ";
                    break;
                case 9://尚无数据
                    $scsSql = " AND callback_status IS NULL ";
                    break;
            }
        }
        $sql = "SELECT COUNT(sms_count) AS itemCount, SUM(sms_count) as smsCount, channel as titleId, FROM_UNIXTIME(UNIX_TIMESTAMP(send_time), '%Y-%m-%d') AS ctime FROM `{$tableName}` WHERE send_time BETWEEN :startTime AND :endTime AND send_status >= :sendStatus {$scsSql} GROUP BY titleId";
        $paramArr['startTime'] = $data['startTime'];
        $paramArr['endTime'] = $data['endTime'];
        $paramArr['sendStatus'] = 1;
        if (!empty($data['row']))
        {
            $operatorTotal = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $operatorTotal = $this->pdo->findAll($sql, $paramArr);
        }

        return $operatorTotal;
    }

    public function getMultiOperatorTotal($data)
    {
        $sql = "SELECT sum(msl.amount) AS total, mslc.channel as titleId, FROM_UNIXTIME(UNIX_TIMESTAMP( msl.send_time) ,'%Y-%m-%d') AS ctime
            FROM `my_multi_sms_logs` AS msl , `my_multi_sms_logs_channel` AS mslc
            WHERE msl.id=mslc.`log_id` and msl.send_time>=:startTime and msl.send_time<:endTime GROUP BY titleId";
        $paramArr['startTime'] = $data['startTime'];
        $paramArr['endTime'] = $data['endTime'];
        if (!empty($data['row']))
        {
            $operatorTotal = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $operatorTotal = $this->pdo->findAll($sql, $paramArr);
        }

        return $operatorTotal;
    }

    /**
     * 每日短信发送量统计, 分项目
     *
     * @param array $data data
     *
     * @return array|bool
     */
    public function getEveryTotal($data)
    {
        $tableName = self::getTableName(date('Ym', strtotime($data['startTime'])));
        $scsSql = "";
        if (isset($data['returnStatus']))
        {
            switch ($data['returnStatus'])
            {
                case 0:
                default:
                    break;
                case 1:
                    $scsSql = " AND callback_status = 1 ";
                    break;
                case 2:
                    $scsSql = " AND callback_status = 0 ";
                    break;
                case 9:
                    $scsSql = " AND callback_status IS NULL ";
                    break;
            }
        }
        $sql = "SELECT COUNT(sms_count) AS itemCount, SUM(sms_count) as smsCount, pid as titleId, FROM_UNIXTIME(UNIX_TIMESTAMP(send_time) ,'%Y-%m-%d') AS ctime FROM `{$tableName}` WHERE send_time BETWEEN :startTime AND :endTime AND pid IS NOT NULL AND send_status >= 1 {$scsSql} GROUP BY titleId";
        $paramArr['startTime'] = $data['startTime'];
        $paramArr['endTime'] = $data['endTime'];
        if (!empty($data['row']))
        {
            $everyTotal = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $everyTotal = $this->pdo->findAll($sql, $paramArr);
        }

        return $everyTotal;
    }

    public function getEveryMultiTotal($data)
    {
        $sql = "SELECT SUM(amount) AS total, pid as titleId, FROM_UNIXTIME( UNIX_TIMESTAMP( send_time) ,'%Y-%m-%d') AS ctime
        FROM `my_multi_sms_logs` WHERE  send_time>=:startTime AND send_time<:endTime AND pid IS NOT NULL AND send_status >=0 GROUP BY titleId";
        $paramArr['startTime'] = $data['startTime'];
        $paramArr['endTime'] = $data['endTime'];
        if (!empty($data['row']))
        {
            $everyTotal = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $everyTotal = $this->pdo->findAll($sql, $paramArr);
        }

        return $everyTotal;
    }


    /**
     * 每日短信发送量统计, 分类型
     * @param array $data data
     * @return array|bool
     */
    public function getTypeTotal($data)
    {
        $tableName = self::getTableName(date('Ym', strtotime($data['startTime'])));
        $scsSql = "";
        if (isset($data['returnStatus']))
        {
            switch ($data['returnStatus'])
            {
                case 0:
                default:
                    break;
                case 1:
                    $scsSql = " AND callback_status = 1 ";
                    break;
                case 2:
                    $scsSql = " AND callback_status = 0 ";
                    break;
                case 9:
                    $scsSql = " AND callback_status IS NULL ";
                    break;
            }
        }
        $sql = "SELECT COUNT(sms_count) AS itemCount, SUM(sms_count) as smsCount, type as titleId, FROM_UNIXTIME(UNIX_TIMESTAMP(send_time) ,'%Y-%m-%d') AS ctime FROM `{$tableName}` WHERE send_time BETWEEN :startTime AND :endTime AND pid IS NOT NULL AND send_status >= 1 {$scsSql} GROUP BY titleId";
        $paramArr['startTime'] = $data['startTime'];
        $paramArr['endTime'] = $data['endTime'];
        if (!empty($data['row']))
        {
            $everyTotal = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $everyTotal = $this->pdo->findAll($sql, $paramArr);
        }

        return $everyTotal;
    }

    public function getProjectPostionTotal($data)
    {
        $tableName = self::getTableName(date('Ym', strtotime($data['startTime'])));
        //根据project查询position的group结果
        $sql = "SELECT COUNT(1) AS total, business_id AS postion_id, FROM_UNIXTIME(UNIX_TIMESTAMP(send_time), '%Y-%m-%d') AS ctime FROM `{$tableName}` WHERE send_time >=:startTime AND send_time<:endTime  AND send_status >=1 AND pid = :pid GROUP BY business_id";
        $paramArr = array(
            ':pid'       => $data['pid'],
            ':startTime' => $data['startTime'],
            ':endTime'   => $data['endTime'],
        );
        $projectPostion = $this->pdo->findAll($sql, $paramArr);
        return $projectPostion;
    }

    /**
     * 获取月统计数据
     *
     * @return array|bool
     */
    public function getStatList()
    {
        $sql = "SELECT `date`,`sjzs`,`sjlm`,`jsylm`,`jsllq`,`sjllq`,`tqw`,`yxdh` " .
            "FROM `sms_log_stat` " .
            "ORDER BY id DESC";
        $projectPostion = $this->pdo->findAll($sql);
        return $projectPostion;
    }
}
