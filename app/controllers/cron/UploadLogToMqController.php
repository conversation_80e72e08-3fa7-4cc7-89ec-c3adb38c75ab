<?php

use Service\Report\ReportToBigData;

includeBase('CronController');

class UploadLogToMqController extends CronController
{
    const KAFKA_ERROR_NUMBER = 3;// kafka报错N次后，直接走文件  vpn不稳定，错误3次后落文件，防止有队列积压
    private $debugLog;
    private $errorLog;

    /**
     * UploadLogToMqController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->debugLog = $this->getLogger("bigDataDebug");
        $this->errorLog = $this->getLogger("bigDataError");
    }

    /**
     * 将数据上报到大数据的mq
     *
     * @return void
     */
    public function actionIndex()
    {
        $currentTime = time();
        $this->debugLog->info('开始执行');
        $redis = RedisEx::getInstance();
        $kafka = new ReportToBigData(\Config::get('kafka'));

        $errorNum = 0;
        $executeTime = 600;//单次执行时间
        $success = 0;
        $flushLimit = 1000;
        while (1) {
            if ((time() - $currentTime) >= $executeTime) {
                break;
            }
            if ($success >= $flushLimit) {
                $success = 0;
                $kafka->flush(-1);
            }
            $log = $redis->rPop(BIG_DATA_REDIS_KEY);
            if ($log === false) {
                sleep(1);
                continue;
            }
            $log = unserialize($log);
            loadAction('Encoding');
            $log = EncodingAction::iteratorArray($log, 'utf-8');
            if (!is_array($log)) {
                continue;
            }
            $log = $this->formatLog($log);

            if (!$kafka->kafkaIsRunning()) {
                $this->errorLog->info(serialize($log));
                continue;
            }

            try {
                $kafka->push($log);
                $success++;
            } catch (Exception $e) {
                $errorNum++;
                if ($errorNum >= self::KAFKA_ERROR_NUMBER) {
                    $kafka->setKafkaStatus();
                }
                $this->debugLog->info('推送错误：' . $e->getMessage());
                // 如果失败存储在本地
                $this->errorLog->info(serialize($log));
            }
        }
        $kafka->flush(-1);
        $this->debugLog->info('运行结束，运行时长：' . (time() - $currentTime) . "秒。");
    }

    /**
     * 从文件导出到mq
     *
     * @return void
     */
    public function actionLoadFromFile()
    {
        global $argv;
        $currentTime = time();
        $this->debugLog->info('开始执行');
        $date = $argv[2] ?? '';
        if (!preg_match('/\d{8}/', $date)) {
            $date = date('Ymd');
        }
        $dir = APPPATH . "/logs/" . $date . "/cron/bigDataError";
        if (!is_dir($dir)) {
            exit;
        }
        $handle = opendir($dir);
        $kafka = new ReportToBigData(\Config::get('kafka'));
        $success = 0;
        $flushLimit = 1000;
        while (($file = readdir($handle)) !== false) {
            if ($file == '.' || $file == '..') {
                continue;
            }
            $logFile = fopen($dir . "/" . $file, 'r');
            while ($log = fgets($logFile)) {
                if ($success >= $flushLimit) {
                    $success = 0;
                    $kafka->flush(-1);
                }
                if (!preg_match("/.*?\: (.+\}).*/is", $log, $matches)) {
                    continue;
                }
                $log = unserialize($matches[1]);
                if (!$log) {
                    continue;
                }

                try {
                    $kafka->push($log);
                    $success++;
                } catch (Exception $e) {
                    $this->debugLog->info('运行错误，日志内容：' . serialize($log) . '  错误信息：' . $e->getMessage());
                    break 2;
                }
            }
            $this->debugLog->info("文件 {$file} 已处理完成。");
        }
        $kafka->flush(-1);
        @closedir($handle);
        $this->debugLog->info('运行结束，运行时长：' . (time() - $currentTime) . "秒。");
    }

    /**
     * 上报历史数据 /login.2345.com/app/logs/20200310/requestLog/XX.log
     *  php cron.php cron/UploadLogToMq/UploadHistory 2020-01-01 2020-01-03 /opt/case/aPplogs/183.136.203.208
     *
     * @return void
     */
    public function actionUploadHistory()
    {
        global $argv;
        if (!isset($argv[2]) || !isset($argv[3])) {
            die(mb_convert_encoding('请输入终始日期', 'utf8', 'gbk'));
        }
        $dateTmp = strtotime($argv[2]);
        $endDate = strtotime($argv[3]);
        $basePath = $argv[4] ?? APPPATH . '/logs';

        $config = \Config::get('kafka');
        $config['topic'] = RUNMODE == 'development' ? 'data_sync.user_center_source_flume' : 'data_sync.user_center_flume';
        $kafka = new ReportToBigData($config);
        $this->debugLog->info('开始执行');
        $success = 0;
        $flushLimit = 1000;
        for (; $dateTmp <= $endDate; $dateTmp += 3600 * 24) {
            $dir = $basePath . "/" . date('Ymd', $dateTmp) . "/requestLog";
            if (!is_dir($dir)) {
                $this->debugLog->info("目录 {$dir} 不存在");
                continue;
            }

            $handle = opendir($dir);
            while (($file = readdir($handle)) !== false) {
                if ($file == '.' || $file == '..') {
                    continue;
                }
                $matchError = 0;//匹配/解析失败行数
                $currentLine = 0;//记录当前行数，报错时用
                $logFile = fopen($dir . "/" . $file, 'r');
                while ($log = fgets($logFile)) {
                    $currentLine++;
                    if ($success >= $flushLimit) {
                        $success = 0;
                        $kafka->flush(-1);
                    }
                    if (empty($log)) {
                        continue;
                    }
                    if (!preg_match("/^\[([^\]]*?)\].*?: (.+\}).*/is", $log, $matches)) {
                        $matchError++;
                        continue;
                    }
                    $log = unserialize($matches[2]);
                    if (!$log) {
                        $matchError++;
                        continue;
                    }
                    $path = parse_url($log['url']);
                    $log = $this->formatLog([
                        "req" => [
                            "time" => (int)(strtotime($matches[1]) * 1000),
                            "url" => $path['path'] ?? '',
                            "param_get" => $log['param_get'] ?? '',
                            "param_post" => $log['param_post'] ?? '',
                        ],
                        "res" => [
                            "codeReal" => $log['codeReal'] ?? '',
                            "code" => $log['code'] ?? '',
                            "event_type" => '',
                            "type" => '',
                            "server_ip" => ''
                        ],
                    ]);
                    try {
                        $kafka->push($log);
                        $success++;
                    } catch (Exception $e) {
                        $msg = "运行错误，日志:" . $dir . "/" . $file . " 行数：{$currentLine} 内容：" . serialize($log) . '  错误信息：' . $e->getMessage();
                        $this->debugLog->info($msg);
                        // 发送邮件
                        $mailbody = $msg;
                        $smtpConfig = Config::get('smtp');
                        $smtp = loadVendor('smtp');
                        $smtp->init($smtpConfig['server'], $smtpConfig['port'], $smtpConfig['username'], $smtpConfig['password']);
                        $smtp->sendmail('<EMAIL>', $smtpConfig['email'], '用户中心-存量数据上报异常', $mailbody, 'HTML');
                        break 3;
                    }
                }
                $this->debugLog->info('文件 ' . $dir . "/" . $file . ' 上报完成，解析失败行数：' . $matchError);
                fclose($logFile);
            }
            @closedir($handle);
        }
        $kafka->flush(-1);
        $this->debugLog->info('运行结束');
    }

    /**
     *  日志格式化
     *
     * @param array $log 日志
     * @return array
     */
    private function formatLog($log)
    {
        // 原始格式
        /* [
            "req" => [
                "time" => (int)(microtime(true) * 1000),
                "url" => "/clientapi/nLoginConfigCloud/index",
                "param_get" => "",
                "param_post" => "appChannel=zhujteat&appName=%E7%8E%A9%E8%B5%9A%E6%98%9F%E7%90%83&appVersion=50700&device=FD10E12BC69FFA4433881DF9BFE295566CB18352A2558C794D80255F04AF62F3BE465886153560F3D7D1FB386B082850F89436E8AB125F31A44FC69628BE8BA8A7094E09808EA9E3BAF119E49714D24C7570DF400C6C5F1D1183DE153C66EA0F&mid=andxqlm&packageName=com.planet.light2345&sdkVersion=3.0.3&timestamp=1574651420&sign=aed265cdd8a7c75afb31100514b95ca7&uuid=865873034141741",
                "param_cookie" => "U=1j7fwtdyb89ws0okcwgkowg08",
            ],
            "res" => [
                "codeReal" => 2000000,
                "code" => 200,
                "event_type" => '1',
                "type" => 'phone',
                "server_ip" => '***********'
            ],
        ]*/
        parse_str($log['req']['param_get'], $paramGet);
        parse_str($log['req']['param_post'], $paramPost);
        $mid = $this->getParam('mid', $paramGet, $paramPost);
        $appChannel = $this->getParam('appChannel', $paramGet, $paramPost);
        $appVersion = $this->getParam('appVersion', $paramGet, $paramPost);
        $sdkVersion = $this->getParam('sdkVersion', $paramGet, $paramPost);
        $appName = $this->getParam('appName', $paramGet, $paramPost);
        $deviceId = $this->getParam('uuid', $paramGet, $paramPost);
        $timeMills = $log['req']['time'] ?? 0;
        $eventType = $log['res']['event_type'] ?? '';
        $type = $log['res']['type'] ?? '';
        $serverIp = $log['res']['server_ip'] ?? '';
        $typeList = [
            1 => 'phone',
            2 => 'email',
            3 => 'account',
            4 => 'qq',
            5 => 'weixin',
            6 => 'chinamobile',
            7 => 'union',
            8 => 'shanyan',
        ];
        $type = $typeList[$type] ?? '';
        $extend = [
            'code' => $log['res']['code'] ?? 0,
            'path' => $log['req']['url'] ?? '',
            'timestamp' => (int)($timeMills / 1000),
            'type' => $eventType,
            'source' => $type,
            'sdk_version' => $sdkVersion,
        ];
        return [
            "common" => [
                'channel_id' => $mid,
                'host_channel_id' => $appChannel,
                'host_version_name' => $appVersion,
                'host_name' => $appName,
                'device_id' => $deviceId,
            ],
            'data' => [
                'event_type' => '',
                'timeMills' => $timeMills,
                'type' => '',
                'extend' => $extend,
            ],
            'trackinfo' => ['server_ip' => $serverIp,],
        ];
    }

    /**
     * 获取参数
     *
     * @param string $target $target
     * @param array $get $get
     * @param array $post $post
     * @param string $default 默认值
     * @return string
     */
    private function getParam($target, &$get, &$post, $default = '')
    {
        return isset($get[$target]) ? $get[$target] : (isset($post[$target]) ? $post[$target] : $default);
    }
}
