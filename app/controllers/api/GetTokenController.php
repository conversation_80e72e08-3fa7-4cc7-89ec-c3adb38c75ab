<?php

class GetTokenController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }

        if (isset($_GET['passid']))
        {
            $passid = intval($_GET['passid']);
            $memberModel = loadModel('member');
            echo $memberModel->getToken($passid);
        }
    }

}
