{{include file = "public/mem_header.tpl.html"}}
{{include file = "public/left_nav.tpl.html}}
<style type="text/css">
    .btn-allBox{ position:fixed; left:0; top:0; z-index:9999;}
    .btn-allBox a{ display:block; color:#0C3; font-size:16px;}
</style>

<div class="account_right" style=" height:500px;">
    <!--修改邮箱 开始-->
    <div class="changeEmail-A">
        <h2 class="pf">修改手机</h2>
        <div class="styleMod"><span class="blueTxt">{{$pageArray.phone}}</span><a href="javascript:" id="showeditphone">修改</a></div>
        <div class="styleMod">绑定手机可以用来登录您的2345帐号与找回密码</div>
        <h2 class="pf mt46">绑定手机常见问题</h2>
        <dl class="questionList">
            <dt>绑定手机时，为什么会提示“手机号已注册”</dt>
            <dd>因为该手机号已经在用户中心、积分、游戏等2345产品上注册或者绑定过，请重新输入新手机号码。</dd>
        </dl>
        <dl class="questionList mt20">
            <dt>手机换号了，无法修改绑定手机怎么办？ </dt>
            <dd>请致电客服人工解决，客服电话：************</dd>
        </dl>
        <dl class="questionList mt20">
            <dt>手机无法收到验证码 </dt>
            <dd>请检测垃圾信箱，是否被手机安全软件误拦截，若检查后仍旧无法收到，请致电客服人工帮助，客服电话：************</dd>
        </dl>
    </div><!--changeEmail-A end-->
</div><!--account_right end-->
<!--主体右侧 结束-->
</div><!--main end-->
<!--主体 end-->
{{include file = "public/qrcode.tpl.html"}}

<div class="box_mark" {{if $pageArray.open}}style="display:block"{{else}}style="display: none"{{/if}}>遮罩层</div>
<div class="box_mod h404" id="verifyphone" {{if $pageArray.open}}style="display:block"{{else}}style="display: none"{{/if}}>
<div class="box_mod_tit"><span>修改手机</span><a class="btn_box_close" id="verify-close" href="javascript:"></a></div>
<div class="boxCon">
    <div class="ts-wrap">
        <p class="tsTxt">为了保证帐号安全，修改手机前请先进行安全验证</p>
        {{if $pageArray.mode == 'complex'}}
        <p class="ts-infor ts-error" id="verifyphonetip"><i></i>您已在星球联盟绑定过该手机，需要验证后才能绑定新手机</p>
        {{else}}
        <p class="ts-infor" id="verifyphonetip" style="display: none"><i></i>发送成功，请查收</p>
        {{/if}}
    </div><!--ts-wrap end-->

    <p class="yzTxt">验证方式</p>

    <ul class="conUl2">
        <li class="clearfix">
            <div class="selectWrap clearfix">
                <div class="selectDiv">
                    <div class="coninput2"><input class="getVal" type="text" autocomplete="off" readonly value="手机（{{$pageArray.phone}}）"/></div>
                    <span class="btn-select"></span>
                </div>
                <dl class="selectBox">
                    <dd><a href="javascript:">手机（{{$pageArray.phone}}）</a></dd>
                </dl>
            </div>
        </li>
        <li class="clearfix">
            <div class="coninput w261"><input class="defaultInput" type="text" name="code" id="verifycode" autocomplete="off" value="请输入验证码" title="请输入验证码" /></div>
            <a class="messCode" href="javascript:" onclick="sendCode();return false;">发送验证码</a>
            <span class="messCode-dsib" style="display:none;">重新发送（60s）</span>
        </li>
    </ul><!--conUl2 end-->
    <a class="btn-tj" id="verifyphonesubmit" href="javascript:">提交</a>
    <div class="mt17">
        <p class="ifTxt">如果您的手机无法收到验证码，请致电客服解决，客服电话：************</p>
    </div>
</div><!--boxCon end-->
</div><!--box_mod end-->
<!--修改手机弹窗2 结束-->


<!--修改手机弹窗1 开始-->
<div class="box_mod w482" id="editphone">
    <div class="box_mod_tit"><span>修改手机</span><a class="btn_box_close" id="editphone-close" href="javascript:"></a></div>
    <div class="boxCon">
        <div class="ts-wrap">
            <p class="tsTxt">温馨提示：请输入您要修改的手机号码，可用该手机登录所有2345产品</p>

            <p class="ts-infor" id="editphonetip" style="display: none"><i></i>发送成功，请查收</p>
        </div>
        <!--ts-wrap end-->

        <ul class="conUl">
            <li>
                <span class="conTit">手机号码：</span>

                <div class="coninput">
                    <input class="defaultInput" type="text" id="phone" name="phone"  onblur="checkPhone();return false;" autocomplete="off" value="请输入手机号码" title="请输入手机号码" maxlength="11"/>
                </div>
                <a class="messCode" href="javascript:" onclick="sendEditCode();return false;">发送验证码</a>
                <span id="sendingBtn" class="messCode-dsib" style="display:none;">重新发送（60s）</span>
            </li>
            <li>
                <span class="conTit">验证码：</span>
                <div class="coninput">
                    <input type="text" id="code" name="code" value="" autocomplete="off"/>
                </div>
            </li>
        </ul>
        <!--conUl end-->
        <a class="btn-tj" id="editphonesubmit" href="javascript:">提交</a>

        <div class="mt17">
            <p class="ifTxt">如果您的手机无法收到验证码，请致电客服解决，客服电话：************</p>
        </div>
    </div>
    <!--boxCon end-->
</div><!--box_mod end-->
<!--修改手机弹窗1 结束-->

<div class="box_mod h232" id="editsuccess">
    <div class="box_mod_tit"><a class="btn_box_close" href="javascript:"></a></div>
    <div class="infor-con">
        <i class="gouIcon"></i>
        <div class="infor-txt">
            <h2>修改手机成功！</h2>
            <p id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a></p>
        </div>
    </div><!--boxCon end-->
</div>
<!--[if IE 6]>
<script type="text/javascript" src="/js/DD_belatedPNG_0.0.8a-min.js"></script>
<script type="text/javascript">
    DD_belatedPNG.fix('.png24');
</script>
<![endif]-->

{{include file = "public/mem_footer.tpl.html"}}


<script>
    var bottomObjectTimeId = setInterval(function(){
        if($("#ClCache") != undefined){
            clearInterval(bottomObjectTimeId);
            $("#ClCache").parent().css("display","none");
        }
    },1000);
    var windowHeight = $(window).height();
    var documentHeight;
    {{if $pageArray.open}}
    maskBoxHeight();
    {{/if}}
    function maskBoxHeight()
    {
        var documentHeight = $(document).height();
        if (documentHeight > windowHeight) {
            $(".box_mark").css("height", documentHeight).show();
        } else {
            $(".box_mark").css("height", windowHeight).show();
        };
    };
    var messageCodeTime = $(".messCode-dsib"),messageTimeNum = 60,btnOk = $(".messCode"),windowHeight = $(window).height();
    $("#showeditphone").bind("click", function(){
//        $(".box_mark").css("height",windowHeight).show();
        maskBoxHeight()
        $("#verifyphone").show();
    });
    $("#verify-close").bind("click", function(){
        $("#verifycode").val("");
        $("#verifyphonetip").hide();
        messageCodeTime.hide();
        btnOk.text("发送验证码").show();
    });
    $("#editphone-close").bind("click", function(){
        $("#verifycode").val("");
        $("#verifyphonetip").hide();
        $("#phone").val("请输入手机号码").removeClass("colorInput");
        $("#code").val("");
        $("#editphonetip").hide();
        messageCodeTime.hide();
        btnOk.text("发送验证码").show();
    });
    //关闭弹框
    $(".btn_box_close").bind("click",function()
    {
        $(".box_mod,.box_mark").hide();
    });
    $(".selectDiv").bind("click",function()
    {
        $(this).next(".selectBox").toggle();
        return false;
    });
    $(".selectBox a").bind("click",function()
    {
        $(this).parents(".selectWrap").find(".getVal").val($(this).text());
    });

    $(document).bind("click",function()
    {
        $(".selectBox").hide();
    });
    $(".defaultInput").bind(
            {
                focus:function(){
                    $(this).parent().removeClass("inputTxtError");
                    $(this).parent().addClass("inputTxtFocus");
                    if($(this).val() == $(this).attr("title"))
                    {
                        $(this).val("");
                    }
                    $(this).addClass("colorInput");
                },
                blur:function(){
                    $(this).parent().removeClass("inputTxtFocus");
                    if($.trim($(this).val()) == "")
                    {
                        $(this).val($(this).attr("title"));
                        $(this).removeClass("colorInput");
                    };

                }
            }
    );
    function sendCode() {
        $.post("/member/editPhone/sendCode", {}, function(data) {
            if(data.indexOf("200") >= 0)
            {
                $("#verifyphone .messCode").text("重新发送").css("display","none");
                $("#verifyphone .messCode-dsib").css("display","block");
                messageTimeNum = 60;
                messageTime("#verifyphone");
            } else if (data == '300') {
                $("#phonetip").html('<i></i>请输入正确的手机号！').addClass("ts-error").show();
            } else if (data == '301') {
                $("#phonetip").html('<i></i>此手机号已被绑定，请重新输入！').addClass("ts-error").show();
            }  else if (data == '400') {
                $("#phonetip").html('<i></i>验证的太频繁了，请稍后再试！').addClass("ts-error").show();
            } else if (data == '402') {
                $("#phonetip").html('<i></i>此手机号已在其他项目绑定，请重新输入！').addClass("ts-error").show();
            } else if (data == '500') {
                $("#phonetip").html('<i></i>服务器忙，请稍后再试！').addClass("ts-error").show();
            }
        });
    }
    function sendEditCode() {
        var phone = $("#phone").val();
        var verifycode = $("#verifycode").val();
        $.post("/member/editPhone/sendCode", {"phone" : AES.encrypt(phone), "verifycode" : verifycode}, function(data) {
            if(data.indexOf("200") >= 0)
            {
                $("#editphone .messCode").text("重新发送").css("display","none");
                $("#editphone .messCode-dsib").css("display","block");
                messageTimeNum = 60;
                messageTime("#editphone");
            } else if (data == '300') {
                $("#editphonetip").html('<i></i>请输入正确的手机号！').addClass("ts-error").show();
            } else if (data == '301') {
                $("#editphonetip").html('<i></i>此手机号已被绑定，请重新输入！').addClass("ts-error").show();
            }  else if (data == '400') {
                $("#editphonetip").html('<i></i>验证的太频繁了，请稍后再试！').addClass("ts-error").show();
            } else if (data == '402') {
                $("#editphonetip").html('<i></i>此手机号已在其他项目绑定，请重新输入！').addClass("ts-error").show();
            } else if (data == '500') {
                $("#editphonetip").html('<i></i>服务器忙，请稍后再试！').addClass("ts-error").show();
            }

        });
    }
    function messageTime(box){
        $(box +" .messCode-dsib").text("重新发送（"+messageTimeNum+ "s" + "）");
        messageTimeNum --;
        if(messageTimeNum>=0){
            setTimeout('messageTime("' + box +'")',1000);
        }else{
            $(".messCode").css("display","block");
            messageCodeTime.css("display","none");
        };
    };
    $("#verifyphonesubmit").bind("click", function(){
        var codeErr = true;
        var code = $("#verifycode").val();
        if (code == "") {
            $("#verifyphonetip").html('<i></i>请输入验证码！').addClass("ts-error").show();
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/member/editPhone/step3",
            async: false,
            data: "&code=" + code,
            timeout:3000,
            success: function(data) {
                if (data == '200') {
                    codeErr = false;
                    $("#verifyphonetip").html('').hide();
                    $("#verifyphone").hide();
                    $("#editphone").show();
                } else if (data == '400') {
                    $("#verifyphonetip").html('<i></i>验证码错误，请重新输入').addClass("ts-error").show();
                }
            }
        });
        return !codeErr;
    });
    $("#editphonesubmit").bind("click", function(){
        var codeErr = true;
        var phone = $("#phone").val();
        var code = $("#code").val();
        var verifycode = $("#verifycode").val();
        if (code == "") {
            $("#editphonetip").html('<i></i>请输入验证码！').addClass("ts-error").show();
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/member/editPhone/step4",
            async: false,
            data: "&phone=" + AES.encrypt(phone) + "&code1=" + verifycode + "&code2=" + code,
            timeout:3000,
            success: function(data) {
                if (data == '200') {
                    codeErr = false;
                    $("#editphone").hide();
                    $("#editsuccess").show();
                    var times = 3;
                    setInterval(function() {
                        if (times > 0) {
                            $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
                        } else {
                            window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
                        }
                        times--;
                    }, 1000)
                } else if (data == '400') {
                    $("#editphonetip").html('<i></i>验证码错误，请重新输入').addClass("ts-error").show();
                }
            }
        });
    });
    function checkPhone() {
        var phone = $("#phone").val();
        if (!phone) {
            return false;
        }
        if (!/^1[0123456789]\d{9}$/.test(phone)) {
            $("#editphonetip").html('<i></i>请输入正确的手机号！').addClass("ts-error").show();
            return false;
        }
        $.ajax({
            url: "/api/check/jsonp",
            data: 'type=phone&value=' + phone,
            async: false,
            timeout: 3000,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(response) {
                err = response;
                if (err == 0) {
                    $("#editphonetip").html('').removeClass("ts-error").hide();
                    return true;
                } else if (err == 1) {
                    $("#editphonetip").html('<i></i>请输入正确的手机号！').addClass("ts-error").show();
                    return false;
                } else if (err == 2) {
                    $("#editphonetip").html('<i></i>此手机号已被绑定，请重新输入！').addClass("ts-error").show();
                    $("#editphonetip").show();
                } else if (err == 3) {
                    $("#editphonetip").html('<i></i>此手机号已被绑定，请重新输入！').addClass("ts-error").show();
                    return false;
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
            }
        });
    }
</script>
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
