<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：Provider.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：22/08/2018 10:21
 */

namespace Service\Lib;

use Service\Func\ServiceFunc;
use Service\Lib\Model\RedisHandle;

class Provider
{
    const STORAGE_TYPE_REDIS = 1;
    const STORAGE_TYPE_MYSQL = 2;
    const STORAGE_TYPE_REDIS_AND_MYSQL = 3;

    protected static $storageType;

    /**
     * Provider constructor.
     */
    public function __construct()
    {
//        self::$storageType = self::getStorageType();
    }

    /**
     * 获取存放类型
     * -
     * @return array|bool|string
     */
    public static function getStorageMold()
    {
        if (empty(self::$storageType))
        {
            self::$storageType = self::getStorageType();
        }
        return self::$storageType;
    }

    /**
     * 设置存放方式
     * -
     * @param int $type 类型
     * @return mixed
     */
    public static function setStorageMold($type)
    {
        return self::$storageType = $type;
    }
    

    /**
     * User: panj
     * 设置短信发送的logId 并且记录原始的发送内容
     * -
     * @param array $insertData 发送数据
     * @param array $column 发送数据列
     *
     * @return array|bool
     */
    public function setSmsLogs($insertData, $column)
    {
        switch (self::getStorageMold())
        {
            case self::STORAGE_TYPE_REDIS:
                //toto 如何保证redis和mysql logid一致性
                $logsId = (new RedisHandle())->setSmsLogsInRedis($insertData, $column);
                break;
            case self::STORAGE_TYPE_MYSQL:
                $logsId = \ServiceSmsModel::getInstance()->setSmsLogs($insertData, $column, true);
                break;
            case self::STORAGE_TYPE_REDIS_AND_MYSQL:
                $logsId = \ServiceSmsModel::getInstance()->setSmsLogs($insertData, $column, true);
                break;
            default:
                $logsId = \ServiceSmsModel::getInstance()->setSmsLogs($insertData, $column, true);
        }
        return $logsId;
    }

    /**
     * User: panj
     * 短信发送数据处理
     *
     * @param array $data data
     *
     * @return bool
     */
    public function addSmsLog($data)
    {
        switch (self::getStorageMold())
        {
            case self::STORAGE_TYPE_REDIS:
                $status = (new RedisHandle())->updateSmsLog($data);
                break;
            case self::STORAGE_TYPE_MYSQL:
                $status = \SmsModel::getInstance()->addSmsLog($data);
                break;
            case self::STORAGE_TYPE_REDIS_AND_MYSQL:
                $status = \SmsModel::getInstance()->addSmsLog($data);
                break;
            default:
                $status = \SmsModel::getInstance()->addSmsLog($data);
        }
        return $status;
    }

    /**
     * 拒绝服务更新状态
     * -
     * @param array $updateData 更新数据
     * @return bool
     */
    public function upSmsLogs($updateData)
    {
        switch (self::getStorageMold())
        {
            case self::STORAGE_TYPE_REDIS:
                if (is_array($updateData['logIds']) && !empty($updateData['logIds']))
                {
                    $logIds = $updateData['logIds'];
                    unset($updateData['logIds']);
                    foreach ($logIds as $key => $logId)
                    {
                        (new RedisHandle())->updateArchiveSmsLog($logId, $updateData);
                    }
                }
                $status = true;
                break;
            case self::STORAGE_TYPE_MYSQL:
                $status = \SmsModel::getInstance()->upSmsLogs($updateData);
                break;
            case self::STORAGE_TYPE_REDIS_AND_MYSQL:
                $status = \SmsModel::getInstance()->upSmsLogs($updateData);
                break;
            default:
                $status = \SmsModel::getInstance()->upSmsLogs($updateData);
        }



        return $status;
    }

    /**
     * 获取数据存储方式
     * User: panj
     * @return array|bool|string
     */
    public static function getStorageType()
    {
        $type = ServiceFunc::getSmsServiceStorageType();
        //如果未配置  使用混合模式
        if (empty($type))
        {
            return self::STORAGE_TYPE_REDIS_AND_MYSQL;
        }
        if ($type == self::STORAGE_TYPE_REDIS)
        {
            return self::STORAGE_TYPE_REDIS;
        }
        elseif ($type == self::STORAGE_TYPE_REDIS_AND_MYSQL)
        {
            return self::STORAGE_TYPE_REDIS_AND_MYSQL;
        }
        elseif ($type == self::STORAGE_TYPE_MYSQL)
        {
            return self::STORAGE_TYPE_MYSQL;
        }
    }
}
