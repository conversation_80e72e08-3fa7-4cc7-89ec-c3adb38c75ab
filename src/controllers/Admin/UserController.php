<?php

namespace Admin;

use Controller;

class UserController extends Controller
{
    /**
     * @return void
     */
    public function actionIndex()
    {
        loadView('Admin/index.tpl.html', array());
    }

    /**
     * 默认页面
     * */
    public function actionDefault()
    {
        loadView('Admin/user_default.tpl.html', array());
    }


    /**
     * 头部导航
     * */
    public function actionIndexTop()
    {

        $uid = UserAction::getAdminUid();
        $userMk = UserAction::getAdminUserMk();
        if ($uid > 0)
        {
            $tn = file_get_contents('http://oa.2345.cn/oaAllTop.php?from=qs&userid=' . $uid . '&sysid=180&mk=' . $userMk);
            echo mb_convert_encoding($tn, 'GBK', 'UTF-8');
        }
        else
        {
            echo "<script type='text/javascript'>parent.location.href='http://newoa.2345.cn/login.php?r=http://qs.ruichuang.net';</script>";
        }
    }

    /**
     * 左侧边栏目
     * */
    public function actionNavigate()
    {
        $UserAuthModel = UserAuthModel::getInstance();
        $uid = UserAction::getAdminUid();
        $navList = $UserAuthModel->getAuthList(array('prid' => 0));
        $SideList = array();
        foreach ($navList as $navKey => $navInfo)
        {
            $navChildList = $UserAuthModel->getUserNav($uid, $navInfo['authId']);
            $navList[$navKey]['childModel'] = $navChildList;
            if (empty($navChildList))
            {
                unset($navList[$navKey]);
            }
        }
        $pageArray['navList'] = $navList;
        loadView('Admin/navigate.tpl.html', $pageArray);
    }
}
