<!DOCTYPE HTML>
<html>
<head>
    <meta charset="gb2312" />
    <title>{{$pageArray.title}}</title>
    <meta content="" name="Description" />
    <meta content="" name="Keywords" />
    <link rel="stylesheet" type="text/css"  href="//login.2345.com/css/v3/common.css">
    <link rel="stylesheet" type="text/css"  href="//login.2345.com/css/v3/login.v3.css">
    <script src="/js/jquery-1.9.1.min.js"></script>
    <script src="/js/login.js"></script>
</head>
<body>
{{include file = "public/header.tpl.html"}}
<!--主体 begin-->
<div class="main main_position">
    <!--状态-1 开始-->
    {{if $pageArray.step == 1}}
    <div class="m-boxA minHeight mt10">
        <div class="m-boxA-hd shelvesTit">
            <h2 class="m-boxA-tit">帐号注销</h2>
        </div>
        <p class="shelvesTxt">帐号一旦被注销将不可恢复，请您在操作之前自行备份帐号相关的所有信息和数据。请您知晓，当您注销账号后：</p>
        <div class="m-boxA-bd">
            <div class="ucfnBox">
                <div class="m-form shelvesForm">
                    <div class="form-item" style="padding-left:10px">
                        <span>1、当前账号的个人信息（包括头像、昵称等）将清空且无法恢复。</span>
                    </div>
                    <div class="form-item" style="padding-left:10px">
                        <span>2、当前账号下载、浏览、评论的信息将清空且无法恢复。</span>
                    </div>
                    <div class="form-item" style="padding-left:10px">
                        <span>3、此前已关联该服务账号的相关产品与服务将不再关联参考。</span>
                    </div>
                    <div class="form-item" style="padding-left:10px">
                        <span>4、当前账号未兑换的礼包等将清空且无法恢复。</span>
                    </div>
                    {{if $pageArray.accountType == 'phone'}}
                        <div class="form-item" style="padding-left:10px">
                            <span>4、帐号所绑定的手机号将会在一定时间内限制再次注册2345帐号</span>
                        </div>
                    {{/if}}

                    <div class="form-item">
                        <a href="javascript:;" class="btn-blueA locationStep" style="width:160px">已清楚风险，确认继续</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--状态-1 结束-->
    {{/if}}

    <!--状态-2 开始-->
    {{if $pageArray.step == 2}}
        {{if $pageArray.accountType == 'phone'}}
            <div class="m-boxA minHeight mt10 step_2_class">
                <div class="m-boxA-hd shelvesTit">
                    <h2 class="m-boxA-tit">帐号注销需验证手机号</h2>
                </div>
                <p class="shelvesTxt" style="font-size:12px;color:#aaa">帐号注销后将不能恢复，请谨慎操作！</p>
                <div class="m-boxA-bd">
                    <div class="ucfnBox">
                        <div class="m-form shelvesForm">
                            <div class="form-item">
                                <span class="form-field">已绑手机：</span>
                                <strong class="yzmCon">{{$pageArray.hidePhone}}</strong><a href="javascript:void(0)" style="color:#166cbb" class="confirmCode" act="">获取校验码</a>
                            </div>
                            <div class="form-item">
                                <span class="form-field">校验码：</span>
                                <input type="text" value="" class="ipt_txt ipt_defa " style="width:99px" maxlength="6">
                                <span class="form-tips form-tips-error" style="display: none"><i class="icon-error"></i>校验码输入错误</span>
                            </div>
                            <div style="margin-top: 10px;clear: both;display: none" class="logoutMsgDivClass">
                                <img src="/images/logout_icon.png" style="width:20px;height:20px;float: left;">
                                <p style="color: #E6A23C;line-height: 20px;font-size: 12px; float: left;">您近期已成功注销过。</p>
                                <p style="color: #E6A23C;line-height: 20px;font-size: 12px; float: left;" class="logoutMsgClass">本次需要注销账号为x月x日 xx:xx新注册账号。</p>
                                <p style="color: #E6A23C;line-height: 20px;font-size: 12px; clear: both;padding-left: 20px">如需再次注销，请联系官方电话400-000-2345，我们会在15个工作日内处理。</p>
                            </div>
                            <div style="clear: both"></div>
                            <div class="form-item">
                                <a href="javascript:;" class="btn-blueA logOffClass">确认注销</a>
                            </div>
                        </div>
                    </div>
                    <p class="shelvesTxt" style="font-size:12px;color:#999">注：如果您的手机无法收到验证码，请致电客服解决，客服电话：400-000-2345</p>
                </div>
            </div>
        {{elseif $pageArray.accountType == 'email'}}
            <div class="m-boxA minHeight mt10 step_2_class_2">
                <div class="m-boxA-hd shelvesTit">
                    <h2 class="m-boxA-tit">帐号注销需验证邮箱</h2>
                </div>
                <p class="shelvesTxt" style="font-size:12px;color:#aaa">帐号注销后将不能恢复，请谨慎操作！</p>
                <div class="m-boxA-bd">
                    <div class="ucfnBox">
                        <div class="m-form shelvesForm">
                            <div class="form-item">
                                <span class="form-field">已绑邮箱：</span>
                                <strong class="yzmCon" style="width: 200px">{{$pageArray.hideEmail}}</strong><a href="javascript:void(0);" style="color:#166cbb" class="confirmCode">获取校验码</a>
                            </div>
                            <div class="form-item">
                                <span class="form-field">校验码：</span>
                                <input type="text" value="" class="ipt_txt ipt_defa" style="width:99px">
                                <span class="form-tips form-tips-error" style="display: none"><i class="icon-error"></i>校验码输入错误</span>
                                <!--<span class="form-tips form-tips-error"><i class="icon-error"></i>错误次数已达今日上限</span>-->
                            </div>
                            <div class="form-item">
                                <a href="javascript:;" class="btn-blueA logOffClass">确认注销</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {{elseif $pageArray.accountType == 'username'}}
            <div class="m-boxA minHeight mt10 step_2_class_3">
                <div class="m-boxA-hd shelvesTit">
                    <h2 class="m-boxA-tit">帐号注销需验证帐号密码</h2>
                </div>
                <p class="shelvesTxt" style="font-size:12px;color:#aaa">帐号注销后将不能恢复，请谨慎操作！</p>
                <div class="m-boxA-bd">
                    <div class="ucfnBox">
                        <div class="m-form shelvesForm">
                            <div class="form-item">
                                <span class="form-field">帐号密码：</span>
                                <input type="password" value="" class="ipt_txt ipt_defa" style="width:99px">
                                <span class="form-tips form-tips-error" style="display: none"><i class="icon-error"></i>密码输入错误</span>
                                <!--<span class="form-tips form-tips-error"><i class="icon-error"></i>错误次数已达今日上限</span>-->
                            </div>
                            <div class="form-item">
                                <a href="javascript:;" class="btn-blueA logOffClass">确认注销</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {{elseif $pageArray.accountType == 'thirdBind'}}
            <div class="m-boxA minHeight mt10 step_2_class_4">
                <div class="m-boxA-hd shelvesTit">
                    <h2 class="m-boxA-tit">帐号注销确认</h2>
                </div>
                <p class="shelvesTxt" style="font-size:12px;color:#aaa">帐号注销后将不能恢复，请谨慎操作！</p>
                <div class="m-boxA-bd">
                    <div class="ucfnBox">
                        <div class="m-form shelvesForm">
                            <div class="form-item">
                                <span class="form-field">图片验证码：</span>
                                <input type="text" value="" class="ipt_txt ipt_defa" style="width:99px" maxlength="4">
                                <img style="padding-left: 10px"  onclick="this.src = '/Captcha.php?mid=loginReg&t=' + Math.random();" alt="" title="" width="88" height="36" id="pic" src="/Captcha.php?mid=loginReg">
                                <span class="form-tips form-tips-error" style="display: none"><i class="icon-error"></i>密码输入错误</span>
                                <!--<span class="form-tips form-tips-error"><i class="icon-error"></i>错误次数已达今日上限</span>-->
                            </div>
                            <div class="form-item">
                                <a href="javascript:;" class="btn-blueA logOffClass">确认注销</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {{/if}}
    {{/if}}
    <!--状态-2 结束-->


    <!--状态-3 开始-->
    <div class="m-boxA minHeight mt10 step_3_class" style="display: none">
        <p class="weiboTxt mt90">帐号已注销</p>
        <p class="weiboTxt" style="font-size:14px;color:#888">感谢你对2345的支持，如需再次使用2345服务需重新注册。</p>
    </div><!--m-boxA end-->
    <!--状态-3 结束-->


</div><!--main end-->
<!--主体 end-->


<script>
    var accountType = '{{$pageArray.accountType}}';
    var intervalTimes = 60;
    var intervalHandle = false;
    var thirdShow = '{{$pageArray.thirdShow}}';
    $('.locationStep').click(function () {
        window.location.href = '/member/PLogOff/index/{{$pageArray.mid}}/{{$pageArray.aesStr}}?step=2&to={{$pageArray.token}}';
    });

    $("#validate").bind({
        focus: function() {
            $("#msg_validate").hide();
            if ($(this).val() == '请输入验证码')
            {
                $(this).val("");
            }
        }
    });

    function showSendMsg() {
        if (intervalTimes > 1) {
            --intervalTimes;
            $(".confirmCode").attr('act', 1);
            $(".confirmCode").text('重新发送（' + intervalTimes + '）').show();
        } else {
            $(".confirmCode").attr('act', '');
            $(".confirmCode").text('获取验证码').show();
        }
    }
    var isBindClick = false
    $('.confirmCode').bind({
        click:function(){
            if ($(".confirmCode").attr('act') == 1)
            {
                return;
            }
            intervalTimes = 60;
            clearInterval(intervalHandle);
            intervalHandle = setInterval(function() {
                showSendMsg();
            }, 1000);
            if (accountType == 'phone')
            {
                $.post('/member/PLogOff/SendCode/{{$pageArray.aesStr}}', {"to" : "{{$pageArray.to}}" }, function(data) {
                    if (data.code == '400.0')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '500.0')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '300.0')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '600.0')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '200')
                    {
                        if (!isBindClick){
                            $('.logOffClass').click(function () {
                                logOffFunc();
                            });
                            isBindClick = true
                        }
                    }
                    else if (data.code == '405')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else
                    {
                        $('.form-tips-error').html('未知错误，请联系客服！').show();
                    }
                });
            }
            else if (accountType == 'email')
            {
                $.post('/member/PLogOff/SendMailCode/{{$pageArray.aesStr}}', {"to" : "{{$pageArray.to}}" }, function(data) {
                    if (data.code == '400')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '404')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '500')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == 200)
                    {
                        if (!isBindClick){
                            $('.logOffClass').click(function () {
                                logOffFunc();
                            });
                            isBindClick = true
                        }
                    }
                });
            }
        }
    });

    function logOffFunc()
    {
        var verify_code = $('.ipt_txt').val();
        if (accountType == 'username')
        {
            verify_code = MD5(verify_code);
        }
        $('.logoutMsgDivClass').hide();
        $('.form-tips').hide();
        $.post('/member/PLogOff/Sub/{{$pageArray.mid}}/{{$pageArray.aesStr}}','verify_code='+verify_code + "&to={{$pageArray.to}}"  ,function (result) {
            if (result.code == '200')
            {
                $('.step_2_class').hide();
                $('.step_2_class_2').hide();
                $('.step_2_class_3').hide();
                $('.step_2_class_4').hide();
                $('.step_3_class').show();
                try {
                    chrome.sync.logout()
                } catch (err) {
                    console.log(err)
                }
                setTimeout("window.location.href = '/login'", 8000 )
            }
            else
            {
                if (result.code == '407')
                {
                    var src = '/Captcha.php?' + Math.random() + '&mid=loginReg';
                    $('#pic').attr('src', src)
                }
                if  (result.code == '500'){
                    $('.logoutMsgClass').html(result.msg)
                    $('.logoutMsgDivClass').show();
                    return
                }
                $('.ipt_txt').addClass('ipt_txt_error');
                $('.form-tips').html('<i class="icon-error"></i>' + result.msg).show();

            }
        },'json')
    }


    if (accountType == 'thirdBind')
    {
        // $('.step_3_class').show();
        // setTimeout("window.location.href = '/login'", 8000 )
        $('.logOffClass').click(function () {
            logOffFunc();
        });
    }
    if (accountType == 'username')
    {
        $('.logOffClass').click(function () {
            logOffFunc();
        });
    }

</script>
{{include file = "public/mem_footer.tpl.html"}}