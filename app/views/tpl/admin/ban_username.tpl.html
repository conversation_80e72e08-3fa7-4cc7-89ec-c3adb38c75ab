<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=GBK">
        <title>注册禁止用户名</title>
        <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
        <link href="/css/v3/zhanghao.css" rel="stylesheet">
        <link rel="stylesheet" href="/css/v3/top.css">
        <script type="text/javascript" src="/js/jquery.min.js"></script>
    </head>
    <body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
    <div class="mainWrap">
    
		<!--面包屑导航 开始-->
        <div class="location">位置：<a href="#" target="_blank">帐号体系</a>  &gt; <span>注册禁止用户名</span></div>
        <!--面包屑导航 结束-->

        <div class="funList mt20 clearfix">
            <form method="post" action="" id="banFromId">
            <div class="left clearfix">
            	<span class="left">用户名：</span>
                <span style=" padding-left:10px;" class="input-search"><input type="text" name="ban" autocomplete="off" placeholder="多个词请用空格‘ ’隔开"></span>
                <a href="javascript:" class="styleBtnBlue left ml10" onclick="$('#banFromId').submit();">增加</a>
            </div>
             </form>
        </div>
    
        <div class="contentWrap">
            <!--table数据列表 开始-->            
            <table cellspacing="0" cellpadding="0" class="tabMod forFixed mt10">
                <tbody>
                	<tr>
                        <th width="80">序号</th>
                        <th>禁止注册词</th>
                        <th>添加人</th>
                        <th>添加时间</th>
                        <th>操作</th>
                    </tr>
                    {{foreach item=vitem from=$pageArray.result key=key}}
                    <tr>
                        <td>{{$key+1}}</td>
                        <td>{{$vitem.username}}</td>
                        <td>{{$vitem.op_user}}</td>
                        <td>{{$vitem.op_time}}</td>
                        <td><a href="javascript:void(0);" wid="{{$vitem.id}}" class="delforbidden">删除</a></td>
                    </tr>
                    {{/foreach}}
            	</tbody>
            </table>
            <!--table数据列表 结束-->        
        </div>
    
	</div>
    
    </body>
     <script type="text/javascript" src="/js/boxfixed.js"></script>
    <script type="text/javascript">
                            
    $('.navList a').each(function (){
        if (  $(this).attr('href') == '/houtai_gl/ban_username' )
        {
            $(this).addClass('select');
        }
    });
    
    
    $('.delforbidden').boxVertical({
    		boxTitle: "是否确定删除？          ",
    		boxTxt: "是否确定删除？       ",
            callbackFun:function(obj){
                var id = obj.attr('wid');
                window.location = '?del=' + id;;
            }
        });
        
    </script>
</html>