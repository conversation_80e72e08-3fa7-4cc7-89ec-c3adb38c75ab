<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/2/25
 * Time: 15:46
 */

namespace Service\Capacity\Mode;

use WebLogger\Facade\LoggerFacade;

class Hash extends BaseTable
{
    private static $node = 128;

    /**
     * 获取切分后的节点
     * @param string $string hash字符串
     * @return int
     */
    public static function Get($string)
    {
        if (is_array($string)) {
            $string = "";
        }
        $point = hexdec(substr(md5($string), 0, 10));

        return intval(fmod($point, self::$node));
    }

    /**
     * 获取hash节点个数
     * @return int
     */
    public static function GetNode()
    {
        return self::$node;
    }
}
