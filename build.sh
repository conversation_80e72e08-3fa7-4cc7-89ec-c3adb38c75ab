#!/bin/bash

APP_PKG_NAME="$1"
BASE_DIR=$(cd "$(dirname "$0")";pwd)

function check() {
    if [ -z ${APP_PKG_NAME} ]; then
        echo "参数 APP_PKG_NAME 为空，请检查脚本入参"
        exit 1
    fi

    echo "APP_PKG_NAME: ${APP_PKG_NAME}"

    echo "参数校验成功"
}

function package() {
    [ -d "${BASE_DIR}/output_scm" ] && rm -rf "${BASE_DIR}/output_scm"
    mkdir -p "${BASE_DIR}/output_scm/${APP_PKG_NAME}"
    mkdir -p "${BASE_DIR}/app/logs"
    mkdir -p "${BASE_DIR}/app/views/tpl_c"
    rsync -av --exclude-from="build_exclude" "${BASE_DIR}/" "${BASE_DIR}/output_scm/${APP_PKG_NAME}"

    cd "${BASE_DIR}/output_scm"
    tar -zcf "${BASE_DIR}/output_scm/${APP_PKG_NAME}.tar.gz" "${APP_PKG_NAME}"
    md5sum "${BASE_DIR}/output_scm/${APP_PKG_NAME}.tar.gz" > "${BASE_DIR}/output_scm/${APP_PKG_NAME}.tar.gz.md5"
}

check
package
