<link href="/css/css.css?v=************" rel="stylesheet"
	type="text/css" />
<script>
<!--
function setUser( vUid )
{
	var url = "cmd.php?cmd=setUser&uid="+vUid;
	window.open(url);
	return true;
}
function editUser( vUid )
{
	var url = "cmd.php?cmd=editUser&uid="+vUid;
	window.open(url);
	return true;
}
-->
</script>
<table width="100%" border="0" cellpadding="5" cellspacing="0">
  <tr>
	<td>
		<table border="0" cellpadding="0" cellspacing="0">
			<tr align="right">
				<td width="97"><a href="tongji.php"><img
					src="/images/02.gif" border="0"></a></td>
				<!-- <td width="97"><a href="count_user.php"><img
					src="/images/03.gif" border="0"></a></td> -->
				<td width="97"><a href="default_list.php">默认网址</a></td>
			</tr>
		</table>
	<table width="100%" border="0" cellpadding="0" cellspacing="1" bgcolor="CCCCCC">
		<tr>
		<td bgcolor="#FFFFFF"><table width="100%" border="0" cellpadding="0" cellspacing="0">
			<tr bgcolor="#EFFFBF">
			<td width="67%" height="30" align="left" class="unnamed1 style1"> 　欢迎进入管理后台</td>
			</tr>
		  </table></td>
		</tr>
	</table>	  </td>
  </tr>
</table>
{{if $pageArray.page == "list"}}
<table width="100%" border="0" cellpadding="5" cellspacing="0">
  <tr>
	<td width="2%"><p class="unnamed2">&nbsp;</p>	</td>
	<td width="98%"><span class="style10">默认网址管理</span><a href="?action=add">[添加网址]</a> </td>
  </tr>
</table>
<table width="100%" height="109" border="0" cellpadding="0" cellspacing="0">
  <tr>
	<td valign="top"><table width="96%" height="106" border="0" align="center" cellpadding="0" cellspacing="1" bgcolor="#CCCCCC" class="unnamed1">
	  <tr align="center" bgcolor="#FFFFFF" class="unnamed1">
		<td width="4%" height="34"><span class="style2">
		  ID</span></td>
		<td width="6%"><span class="style2">网站名称</span></td>
		<td width="18%"><span class="style2">网站地址</span></td>
		<td width="4%"><span class="style2">权重排序</span></td> 
		<td width="11%"><span class="style2">管理</span></td>
		</tr>
{{foreach key=key item=item from=$pageArray.default}}
	  <tr align="center" bgcolor="#FFFFFF" class="unnamed1" onmousemove="this.bgColor='#cccccc';" onmouseout="this.bgColor='#FFFFFF';">
		<td height="34">{{$key}}</td>
		<td>{{$item.mTitle}}</td>
		<td><a href="{{$item.mUrl}}" target="_blank">{{$item.mUrl}}</a></td>
		<td>{{$item.mRemark}}</td>
		<td><span class="SelectR3">
		  <a href="?action=edit&id={{$item.id}}"><input class=c type=button value="编辑"></a>
		  <a href="?action=delete&id={{$item.id}}"><input class=c type=button value="刪除"></a>
		</span></td>
	  </tr>
{{/foreach}}
	  <tr align="center" bgcolor="#FFFFFF">
		<td height="34" colspan="9" ><table width="757" border="0" align="left" cellpadding="0" cellspacing="0">
		  <tr>
			<td width="119" align="center">&nbsp;</td>
			<td width="638" align="center">{{$pageArray.js.js}} 总数: {{$pageArray.total}} {{$pageArray.nowPage}}/{{$pageArray.totalPage}} <a href="{{$pageArray.js.backPage}}">上一页</a>　<a href="{{$pageArray.js.nextPage}}">下一页</a>　<a href="{{$pageArray.js.startPage}}">首页</a>　<a href="{{$pageArray.js.endPage}}">尾页</a>　转至{{$pageArray.js.menu}}
页</td>
		  </tr>
		</table>		  </td>
		</tr>
	</table></td>
  </tr>
</table>
{{/if}}
{{if $pageArray.page == "add"}}
<form action="" method="post">
<table width="100%" border="0" cellpadding="5" cellspacing="0">
  <tr>
	<td width="2%"><p class="unnamed2">&nbsp;</p>	</td>
	<td width="98%"><span class="style10">添加网址 <a href="default_list.php">返回</a></td>
  </tr>
</table>
<table style="margin-left:40px" border="1" cellpadding="2" cellspacing="0" >
<tr>
<td>网站名称：<input type="text" name="web_name" size="40"/></td>
</tr>
<tr>
<td>网站地址：<input type="text" name="web_url" size="40"/></td>
</tr>
<tr>
<td>权重排序：<input type="text" name="mRemark" size="20"/>(<font color="red">排序ID越大，网址排的越前</font>)</td>
</tr>
<tr>
<td><input type="submit" name="action" value="保存"/></td>
</tr>
</table>
</form>
{{/if}}
{{if $pageArray.page == "edit"}}
<form action="" method="post">
<input type="hidden" name="id" value="{{$pageArray.row.id}}"/>
<table width="100%" border="0" cellpadding="5" cellspacing="0">
  <tr>
	<td width="2%"><p class="unnamed2">&nbsp;</p>	</td>
	<td width="98%"><span class="style10">编辑网址 <a href="default_list.php">返回</a></td>
  </tr>
</table>
<table style="margin-left:40px" border="1" cellpadding="2" cellspacing="0" >
<tr>
<td>网站名称：<input type="text" name="web_name" size="40" value="{{$pageArray.row.mTitle}}" /></td>
</tr>
<tr>
<td>网站地址：<input type="text" name="web_url" size="40" value="{{$pageArray.row.mUrl}}" /></td>
</tr>
<tr>
<td>权重排序：<input type="text" name="mRemark" size="20" value="{{$pageArray.row.mRemark}}"/>(<font color="red">排序ID越大，网址排的越前</font>)</td>
</tr>
<tr>
<td><input type="submit" name="action" value="确认"/></td>
</tr>
</table>
</form>
{{/if}}