<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：MemberHook.php
 * 摘    要：
 * 作    者：杜海明
 * 修改日期：2013.10.12
 */
use Octopus\Router;

class MemberHook extends Hook
{
    private static $banAccess = array('Admin/', 'ApiController', 'SmsInterface/', 'Crontab/');

    private static $userInfo = array();

    public function __construct()
    {
        $class = str_replace('\\', '/', Router::fetchClass());
        foreach (self::$banAccess as $banInfo)
        {
            if (strpos(strtoupper($class), strtoupper($banInfo)) === 0)
            {
                switch ($banInfo)
                {
                    case 'Admin/':
                        if (!isset($_SESSION))
                        {
                            session_name('SMSPADMINSESSION');
                            session_start();
                        }
                        break;
                    case 'ApiController':
                        if (!isset($_SESSION))
                        {
                            session_name('SMSPADMINSESSION');
                            session_start();
                        }
                        break;
                    default:
                        if (!isset($_SESSION))
                        {
                            session_start();
                        }
                }
            }
        }
    }

    private function getAccessPath()
    {
        $class = str_replace('\\', '/', Router::fetchClass());
        $methodName = Router::fetchMethod();
        $parseClass = explode('Controller', $class);
        $count = count($parseClass);
        if ($count >= 2)
        {
            unset($parseClass[$count - 1]);
        }
        $className = implode('', $parseClass);


        $parseMethod = explode('action', $methodName);
        unset($parseMethod[0]);

        $className .= '/' . implode('', $parseMethod);
        $authPath = '/' . trim($className, '/');
        return $authPath;
    }

    public function mark()
    {
        $class = str_replace('\\', '/', Router::fetchClass());
        $methodName = Router::fetchMethod();
        foreach (self::$banAccess as $banInfo)
        {
            if (strpos(strtoupper($class), strtoupper($banInfo)) === 0)
            {
                switch ($banInfo)
                {
                    case 'ApiController':
                        if ($class === 'ApiController' && $methodName == 'actionOalogin')
                        {
                            return true;
                        }
                        else
                        {
                            redirect("https://oa.2345.cn/login.php");
                        }
                        break;
                    case 'SmsInterface/':
                        include APPPATH . '/config/sms/ChannelIpWhiteList.php';
                        $isAccess = ChannelIpWhiteList::isAccessAuth();
                        if (!$isAccess)
                        {
                            header('HTTP/1.1 401 Unauthorized');
                            exit;
                        }
                        break;
                    case 'Crontab/':
                        if (empty($_SERVER['argv']))
                        {
                            header('HTTP/1.1 404 Not Found');
                            exit;
                        }
                        return true;
                        break;
                    case 'Admin/':
                        if (empty($_SESSION['SMSPISLOGIN']) || !isset($_SESSION['whitePath']))
                        {
                            echo '长时间未操作，请重新从OA进入';
                            exit;
                        }
                        else
                        {
                            if (RUNMODE === 'development')
                            {
                                break;
                            }
                            $path = strtolower($this->getAccessPath());
                            if (!in_array($path, $_SESSION['whitePath']))
                            {
                                echo '你没有短信系统权限';
                                exit;
                            }
                        }
                        break;
                    default:
                        exit;
                }
            }
        }
    }
}
