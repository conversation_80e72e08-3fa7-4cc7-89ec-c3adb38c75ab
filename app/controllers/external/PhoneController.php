<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/3
 * Time: 9:36
 */
includeBase('ExternalController');
use \Common\Utils\Url;

class PhoneController extends ExternalController
{

    /**
     * 记录输入手机号验证码错误日志
     * -
     * @param string $mid mid
     * @param int $phone 手机号码
     * @param int $code code
     * @param string $callName 调用名称
     * @return void
     */
    private function checkSmslog($mid, $phone, $code, $callName)
    {
        $checkCode = md5($phone . $code);
        $checkCodeSession = md5(session_id() . $checkCode);
        $loginCodeKey = "PL:code:$mid:$phone";
        $regCodeKey = "PR:code:$mid:$phone";
        $redis = RedisEx::getInstance();
        $storeCode = $redis->get($loginCodeKey);
        $regStoreCode = $redis->get($regCodeKey);
        $msg = "callname:" . $callName . "\t" . "code:" . $code . "\t" . "phone:" . $phone . "\t" . "checkCode:" . $checkCode . "\t" . "checkCodeSession:" . $checkCodeSession . "\t" . "loginDbCode:" . $storeCode . "\t" . "regDbcode:" . $regStoreCode;
        xLog('external/checkphonecode', 'checkphonecode', $msg, 'info');
    }

    /**
     * 发送登录验证码,成功发送5次显示图形验证码
     * -
     * @return string
     */
    public function actionSendLoginPhoneCode()
    {
        $this->startSession();
        //校验必填字段及签名
        $necessaryField = array(
            'exId',
            'phone',
            'timestamp',
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check("data", $necessaryField);
        if ($aesDecodeData === false)
        {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        list($exId ,$phone) = $this->getParamAndAutoDeny($necessaryField);
//        if (empty($_SESSION['captcha_phone']) || $phone != $_SESSION['captcha_phone'])
//        {
//            unset($_SESSION['captcha_phone']);
//            unset($_SESSION['captcha_code']);
//            $this->codeResponse(304, "", "请输入正确的图片验证码！");
//        }
//        unset($_SESSION['captcha_phone']);
        $checkCode = $aesDecodeData['checkCode'];
        $ShowCaptchaAction = loadAction('ShowCaptcha');
        $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($exId, $phone, $checkCode);
        if ($showCaptcha["code"] != 200)
        {
            $this->echoResponse($showCaptcha);
        }
        $phoneAction = loadAction("phone");
        $smsFilterConfig = [
            'mNum' => 15,
            'hNum' => 15,
            'dNum' => 50
        ];
        $retCode = $phoneAction->sendLoginCode($exId, $phone, 7, 453, false, $smsFilterConfig);
        switch ($retCode)
        {
            case 200:
                $this->codeResponse(200, "", "发送成功！");
                break;
            case 300:
                $this->codeResponse(300, "", "请输入正确的手机号码！");
                break;
            case 400:
                $this->codeResponse(4100, "", "发送频繁，请稍后再试！");
                break;
            case 403:
                $this->codeResponse(4103, "", "该手机号禁止使用！");
                break;
            case 404:
                $this->codeResponse(4104, "", "该手机号不存在！");
                break;
            case 500:
            default:
                $this->codeResponse(5000, "", "服务器忙，请稍后再试！");
                break;
        }
    }

    /**
     * 登录
     * @return void
     */
    public function actionLogin()
    {
        $LoginToken = loadAction("LoginToken");
        //校验必填字段及签名
        $necessaryField = array(
            'exId',
            'phone',
            'verify_code',
            'timestamp',
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check('data', $necessaryField);
        if ($aesDecodeData === false)
        {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        list($exId ,$phone, $verifyCode) = $this->getParamAndAutoDeny($necessaryField);
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse("300", "", "请输入正确的手机号码！");
        }
        $phoneAction = loadAction("phone");
        $timeKey = "PL:time:$exId:$phone";
        $codeKey = "PL:code:$exId:$phone";
        $redis = RedisEx::getInstance();
        $checkTime = $redis->get($timeKey);
        if ($checkTime > 2)
        {
            $redis->del($codeKey);
            $this->codeResponse(4110, "", "短信验证码已失效，请重新获取");
        }
        if (!$phoneAction->checkLoginCode($exId, $phone, md5($phone . $verifyCode), false, true))
        {
            $this->checkSmslog($exId, $phone, $verifyCode, 'login');
            $this->codeResponse("303", "", "手机短信验证码错误！");
        }
        $memberModel = loadModel('member');
        $passid = $memberModel->getPassidByPhone($phone);
        if (!$passid)
        {
            //手机号,未绑定手机号, 但存在用户名
            $passid = $memberModel->getPassidByUsername($phone);
        }
        $user = [];
        if (!empty($passid))
        {
            $user = $memberModel->read($passid);
            $isProhibitLogin = $phoneAction->prohibitUsernameAsPhoneDiffBindPhoneLogin($user, $phone);
            if ($isProhibitLogin)
            {
                $this->codeResponse(400, "", "登录失败！");
            }
        }
        //收集注册登录信息
        $source = Url::getStringParam("exId", "mobile");
        $st = !!$passid;
        $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        \Service\AccountStat\AccountStat::collect($type, $clientType, $phone, $st, $source);
        if ($passid)
        {
            $phoneAction = loadAction("phone");
            $phoneAction->repairUsernameWithPhoneNum($phone, $user, "external");
            $getToken = $LoginToken->setLoginToken($exId, $passid);
            if ($getToken !== false)
            {
                $pushArr = [
                    'passid' => $passid,
                    'phone' => $phone
                ];
                $responseData = [
                    "extend" => $LoginToken->pushLoginInfo($exId, $pushArr),
                    "response" => $LoginToken->returnResponseData($exId, ['passid' => $passid], false),
                    "token" => $getToken
                ];
                $this->codeResponse(200, $responseData, "登录成功");
            }
            else
            {
                $this->codeResponse(401, "", "登录失败！");
            }
        }
        else
        {
            $this->codeResponse(400, "", "登录失败！");
        }
    }


    /**
     * 注册短信验证码
     * -
     * @param string $method quick 是否是快捷注册
     * @return string
     */
    public function actionSendRegPhoneCode($method)
    {
        $this->startSession();
        $necessaryField = array(
            'exId',
            'phone',
            'timestamp',
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check('data', $necessaryField);
        if ($aesDecodeData === false)
        {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        list($mid ,$phone) = $this->getParamAndAutoDeny($necessaryField);
//        if (empty($_SESSION['captcha_phone']) || $phone != $_SESSION['captcha_phone'])
//        {
//            unset($_SESSION['captcha_phone']);
//            unset($_SESSION['captcha_code']);
//            $this->codeResponse(304, "", "请输入正确的图片验证码！");
//        }
//        unset($_SESSION['captcha_phone']);
        $checkCode = $aesDecodeData['checkCode'];

        $ShowCaptchaAction = loadAction('ShowCaptcha');
        $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($mid, $phone, $checkCode);
        if ($showCaptcha['code'] != 200)
        {
            $this->echoResponse($showCaptcha);
        }
        $phoneAction = loadAction("phone");
        $positionId = ($method == "quick") ? 454 : 163;
        $smsFilterConfig = [
            'mNum' => 15,
            'hNum' => 15,
            'dNum' => 50
        ];
        $retCode = $phoneAction->sendRegCode($mid, $phone, 7, $positionId, false, $smsFilterConfig);
        switch ($retCode)
        {
            case 200:
                $this->codeResponse("200", "", "发送成功！");
                break;
            case 300:
                $this->codeResponse("300", "", "请输入正确的手机号码！");
                break;
            case 301:
                $this->codeResponse("301", "", "该手机号已被其他用户占用！");
                break;
            case 302:
                $this->codeResponse("302", "", "该手机号已被其他用户绑定！");
                break;
            case 400:
                $this->codeResponse("4100", "", "发送频繁，请稍后再试！");
                break;
            case 500:
            default:
                $this->codeResponse("5000", "", "服务器忙，请稍后再试！");
                break;
        }
    }


    /**
     * 手机号码注册
     * -
     * @return string
     */
    public function actionReg()
    {
        $LoginToken = loadAction("LoginToken");
        //校验必填字段及签名
        $necessaryField = array(
            'exId',
            'phone',
            'verify_code',
            'timestamp',
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check('data', $necessaryField);
        if ($aesDecodeData === false)
        {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        list($exId ,$phone, $verifyCode) = $this->getParamAndAutoDeny($necessaryField);

        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse("300", "", "请确认您的手机号是否填写正确!");
        }

        $memberModel = loadModel('member');
        $passid = $memberModel->getPassidByPhone($phone);
        if (!empty($passid))
        {
            $this->actionLogin();
            exit;
        }

        $phoneAction = loadAction("phone");
        $timeKey = "PL:time:$exId:$phone";
        $codeKey = "PL:code:$exId:$phone";
        $redis = RedisEx::getInstance();
        $checkTime = $redis->get($timeKey);
        if ($checkTime > 2)
        {
            $redis->del($codeKey);
            $this->codeResponse(4110, '', "验证码已失效");
        }
        if (!$phoneAction->checkRegCode($exId, $phone, md5($phone . $verifyCode), false, true))
        {
            $this->checkSmslog($exId, $phone, $verifyCode, 'reg');
            $this->codeResponse(400, "", "短信验证失败!");
        }
        $userClientIp = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $pwd = mt_rand() . time();
        $password = substr(md5($pwd . MD5KEY), 0, 16);

        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        $regAction = loadAction('reg');

        $gid = 300;
        // 客户端 注册
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        $source = \Common\Utils\Url::getStringParam("exId", "mobile");
        $result = $regAction->regPhone($phone, $password, $pwdStrength, $userClientIp, "login.2345.com", $gid, $source, $clientType);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            $this->codeResponse(403, "", 'errorCode:' . $msgs[$states[0]][$states[1]]);
        }
        elseif ($states[0] == 300)
        {
            $this->codeResponse($states[0] + $states[1], "", $msgs[$states[0]][$states[1]]);
        }
        elseif ($states[0] == 200)
        {
            $passid = $result[1]['passid'];
            $getToken = $LoginToken->setLoginToken($exId, $passid);
            if ($getToken !== false)
            {
                $pushArr = [
                    'passid' => $passid,
                    'phone' => $phone
                ];
                $responseData = [
                    "extend" => $LoginToken->pushLoginInfo($exId, $pushArr),
                    "response" => $LoginToken->returnResponseData($exId, ['passid' => $passid], false),
                    "token" => $getToken
                ];
                $this->codeResponse("200", $responseData, "注册成功！");
            }
            else
            {
                $this->codeResponse("502", "", "生成失败");
            }
        }
        else
        {
            $this->codeResponse("500", "", "禁止注册！");
        }
    }
}
