<?php

/**
 * 数据库配置文件
 */

//sms短信 数据库
// 注：线上从库暂未使用，只是分析数据的时候使用了 slave 113 机器
define("DATABASE_SMS", "smsp");
$config['database'][DATABASE_SMS] = array(
    'master'   => array(
/*env0001*/'host' => $_ENV['DATABASE_MASTER_HOST'],
/*env0002*/'port' => $_ENV['DATABASE_MASTER_PORT'],
    ),
    'slave'    => array(
/*env0003*/'host' => $_ENV['DATABASE_MASTER_HOST'],
/*env0004*/'port' => $_ENV['DATABASE_MASTER_PORT'],
    ),
/*env0005*/'username' => $_ENV['DATABASE_MASTER_USER'],
/*env0006*/'password' => $_ENV['DATABASE_MASTER_PASSWORD'],
/*env0007*/'charset'  => $_ENV['DATABASE_CHARSET'],
            'dbname'  => $_ENV['DATABASE_MASTER_DBNAME'],
);

$config['slave_database'][DATABASE_SMS] = array(
    'master'   => array(
/*env0008*/'host' => $_ENV['DATABASE_SLAVE_HOST'],
/*env0009*/'port' => $_ENV['DATABASE_SLAVE_PORT'],
    ),
    'slave'    => array(
/*env0010*/'host' => $_ENV['DATABASE_SLAVE_HOST'],
/*env0011*/'port' => $_ENV['DATABASE_SLAVE_PORT'],
    ),
/*env0012*/'username' => $_ENV['DATABASE_SLAVE_USER'],
/*env0013*/'password' => $_ENV['DATABASE_SLAVE_PASSWORD'],
/*env0014*/'charset'  => $_ENV['DATABASE_CHARSET'],
    'dbname'  => $_ENV['DATABASE_SLAVE_DBNAME'],
);

$config['database_sync_from'] = [
    'master' => array(
        'host' => $_ENV['DATABASE_SYNC_FROM_HOST'],
        'port' => $_ENV['DATABASE_SYNC_FROM_PORT'],
    ),
    'username' => $_ENV['DATABASE_SYNC_FROM_USER'],
    'password' => $_ENV['DATABASE_SYNC_FROM_PASSWORD'],
    'charset' => $_ENV['DATABASE_SYNC_FROM_CHARSET'],
    'dbname' => $_ENV['DATABASE_SYNC_FROM_DBNAME'],
];
$config['database_sync_to'] = [
    'master' => array(
        'host' => $_ENV['DATABASE_SYNC_TO_HOST'],
        'port' => $_ENV['DATABASE_SYNC_TO_PORT'],
    ),
    'username' => $_ENV['DATABASE_SYNC_TO_USER'],
    'password' => $_ENV['DATABASE_SYNC_TO_PASSWORD'],
    'charset' => $_ENV['DATABASE_SYNC_TO_CHARSET'],
    'dbname' => $_ENV['DATABASE_SYNC_TO_DBNAME'],
];
