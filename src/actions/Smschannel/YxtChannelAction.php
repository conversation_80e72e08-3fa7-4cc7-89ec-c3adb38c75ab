<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/4/11
 * Time: 10:26
 */

namespace Smschannel;

use FuncAction;
use Service\Swoole\Tool\Log;

class YxtChannelAction extends SmsChannelAction
{

    /**
     * 初始化
     * @param string $user   username
     * @param string $pwd    password
     * @param string $apiUrl api url
     */
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }

    /**
     * 单条发送
     * @param int    $phone 手机号码
     * @param string $msg   发送内容
     * @param string $sign  签名
     * @return int
     */
    public function send($phone, $msg, $sign)
    {
        $phoneStr = $phone;
        $multi = 0;
        if (is_array($phoneStr)) {
            $phoneStr = implode(",", $phoneStr);
            $multi = 1;
        }
        list($userId, $user) = explode(':', $this->user);
        $msg_u8 = iconv('GBK', 'UTF-8', $sign . $msg);
        $params = [
            'action'   => 'send',
            'userid'   => $userId,
            'account'  => $user,
            'password' => $this->pwd,
            'mobile'   => $phoneStr,
            'content'  => $msg_u8,
            'sendTime' => '',  //定时发送时间
            'extno'    => '',
        ];

        $response = FuncAction::postTimeout($this->apiUrl, $params, 6);

        $res = simplexml_load_string($response);
        if ($res->returnstatus == 'Success') {
            if ($multi) {
                foreach ($phone as $phoneLine) {
                    $this->msgIdList[$phoneLine] = $res->taskID;
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_SUCC,
                        'codeStatus' => 0,
                        'codeDesc'   => '成功',
                    );
                }
            } else {
                $this->setMsgId($res->taskID);
                $this->OperatorMsg = array(
                    'result'     => self::RET_SUCC,
                    'codeStatus' => 0,
                    'codeDesc'   => '成功',
                );
            }

            return self::RET_SUCC;
        } else {
            \WebLogger\Facade\LoggerFacade::info('error YxtChannelAction::send response', (array)$response);
            $codeDesc = mb_convert_encoding($res->message, 'GBK', 'UTF-8');
            if ($multi) {
                foreach ($phone as $phoneLine) {
                    $this->msgIdList[$phoneLine] = 0;
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_ERR,
                        'codeStatus' => - 1,
                        'codeDesc'   => $codeDesc,
                    );
                }
            } else {
                $this->setMsgId(0);
                $this->OperatorMsg = array(
                    'result'     => self::RET_ERR,
                    'codeStatus' => - 1,
                    'codeDesc'   => $codeDesc,
                );
            }

            return self::RET_ERR;
        }
    }

    /**
     * set msgId
     * @param string $msgId msgId
     * @return null
     */
    private function setMsgId($msgId)
    {
        $this->msgId = $msgId;
    }

    /**
     * 单条发送
     * @param int    $phone 手机号码
     * @param string $msg   发送内容
     * @param string $sign  签名
     * @return int
     */
    public function multiPhoneSend($phone, $msg, $sign)
    {
        return $this->send($phone, $msg, $sign);
    }

    /**
     * 获取 callback status 需要的配置文件, 和设定回调方法
     * @param array $channelConfig channelConfig
     * @return array
     */
    public static function callbackStatusConfig($channelConfig)
    {
        $params = array(
            'action'   => 'query',
            'userid'   => $channelConfig['userid'],
            'account'  => $channelConfig['username'],//用户名
            'password' => $channelConfig['password'],//密码
        );
        $api = $channelConfig['api_url'];
        $query = http_build_query($params);
        $finalUrl = "{$api}?{$query}";

        return array(
            'url'           => $finalUrl,
            'data'          => $params,
            'account'       => $channelConfig['username'],
            'action'        => 'post',
            'callbackClass' => '\\Smschannel\\YxtChannelAction',
            'callbackFunc'  => 'callbackStatus',
            'callback'      => '',
        );
    }

    /**
     * 获取 callback status, 是回调方法
     * @param array $data data
     * @return boolean
     */
    public static function callbackStatus($data)
    {
        $response = (array)simplexml_load_string($data);
        if (!empty($response['errorstatus'])) {
            \WebLogger\Facade\LoggerFacade::info('error YxtChannelAction::callbackStatus', (array)$data);
            return false;
        }
        $result = true;
        if (!empty($response['statusbox'])) {
            $redisAction = \RedisAction::connect();
            if (is_object($response['statusbox'])) {
                $result = self::pushResponseData((array)$response['statusbox'], $redisAction);
            } elseif (is_array($response['statusbox'])) {
                foreach ($response['statusbox'] as $res) {
                    $result &= self::pushResponseData((array)$res, $redisAction);
                }
            }
        }

        return $result;
    }

    /**
     * 载入回执信息
     * @param array  $response    载入redis数组
     * @param object $redisAction redis
     * @return mixed
     */
    private static function pushResponseData($response, $redisAction)
    {
        $dataInfo = array(
            'Mobile'    => $response['mobile'],//手机号码
            'Msg_Id'    => $response['taskid'],//订单号码
            'SendTime'  => date('Y-m-d H:i:s', strtotime($response['receivetime'])),  //发送时间
            'RecvTime'  => date('Y-m-d H:i:s', strtotime($response['receivetime'])),   //到达时间
            'TimeStamp' => date('Y-m-d H:i:s'),//到达时间格式化
        );

        //状态报告----10：发送成功，20：发送失败
        if ($response['status'] == 10) {
            $dataInfo['Status'] = 'DELIVRD';
        } else {
            $dataInfo['Status'] = $response['status'];
        }

        return $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($dataInfo));
    }

}
