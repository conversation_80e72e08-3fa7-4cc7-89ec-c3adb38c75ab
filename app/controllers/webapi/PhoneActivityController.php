<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/7/24
 * Time: 18:05
 */

class PhoneActivityController extends Controller
{

    /**
     * 记录访问次数key
     * -
     * @return string
     */
    private function getPhoneActivityCheckNumsKey()
    {
        return 'PhoneActivity:check:nums:' . str_replace('.', '_', get_client_ip());
    }

    /**
     * 获取每分钟最大访问
     * -
     * @return int
     */
    private function getLimitNumsKey()
    {
        return 20;
    }

    /**
     * 获取每天注册key
     * @param string $mid mid
     * @return string
     */
    private function getMidRegNumKey($mid)
    {
        return "PhoneActivity:Reg:" . date('Ymd') . ':' . $mid . ':nums';
    }

    /**
     * 生成xcsrftoken
     * -
     * @return void
     */
    public function actionIndex()
    {
        session_start();
        $_SESSION['X-CSRF-Token'] = md5(uniqid() . mt_rand(10000, 99999));
        $this->codeResponse(200, ['xCsrfToken' => $_SESSION['X-CSRF-Token']]);
    }

    /**
     * 检查是否已注册
     * -
     * @return string
     */
    public function actionCheck()
    {
        session_start();
        $phone = \Common\Utils\Url::getStringParam("phone");
        $mid = \Common\Utils\Url::getStringParam("mid");
        $xCsrfToken = \Common\Utils\Url::getStringParam("xCsrfToken");
        $memberModel = loadModel('member');
        $status = 0;
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(4010, "", "请确认您的手机号是否填写正确！");
        }
        else
        {
            $midArr = Config::get("mid");
            if (!isset($midArr[$mid]))
            {
                $this->codeResponse(4011, "", "项目标识不正确！");
            }

            if ($_SESSION['X-CSRF-Token'] != $xCsrfToken)
            {
                $this->codeResponse(200, ['status' => 0], "");
            }
            $redis = RedisEx::getInstance();
            $key = $this->getPhoneActivityCheckNumsKey();

            $checkNum = $redis->get($key);
            if ($checkNum < $this->getLimitNumsKey() && $_SESSION['visitDate'] < time())
            {
                $res = $memberModel->checkPhone($phone, true); //检查是否存在手机号, 包括用户名
                if ($res > 0)
                {
                    $status = 1;
                }
                $_SESSION['visitNum'] += 1;
                if ($_SESSION['visitNum'] % $this->getLimitNumsKey() == 0)
                {
                    $this->setVisitDate();
                }
                $redis->incr($key);
                $redis->expire($key, 60);
            }
            else
            {
                $status = 0;
            }
        }
        $_SESSION['captcha_phone'] = $phone;
        $data = array(
            "status" => $status,
        );
        $this->codeResponse(200, $data);
    }


    /**
     * 发送快捷注册验证码
     * -
     * @return string
     */
    public function actionSendRCode()
    {
        session_start();
        $mid = \Common\Utils\Url::getStringParam('mid');
        $phone = \Common\Utils\Url::getStringParam('phone');
        $checkCode = \Common\Utils\Url::getStringParam('captcha_code');
        $midArr = Config::get("mid");
        $lockPro = Config::get("lockpro");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        if ($lockPro && array_key_exists($mid, $lockPro))
        {
            if (empty($phone) || empty($_SESSION['captcha_phone']) || empty($_SESSION['captcha_code']) || empty($checkCode))
            {
                unset($_SESSION['captcha_code']);
                $this->codeResponse(304, "", "请输入正确的图片验证码！");
            }
            if ($_SESSION['captcha_phone'] != $phone || strtoupper($checkCode) != $_SESSION['captcha_code'])
            {
                unset($_SESSION['captcha_code']);
                $this->codeResponse(304, "", "请输入正确的图片验证码！");
            }
            unset($_SESSION['captcha_code']);
        }
//        $redis = RedisEx::getInstance();
//        $key = $this->getPhoneActivityCheckNumsKey();
//        $checkNum = $redis->get($key);
//        if ($checkNum >= $this->getLimitNumsKey() || $_SESSION['visitDate'] > time())
        if ($_SESSION['visitDate'] > time())
        {
            $this->show200();
        }
        //注意:手机联盟站点启用后,再删掉下面的代码
        if ($mid == 'SJLM')
        {
            $this->show200();
        }
        $phoneAction = loadAction("phone");
        $positionId = 201;
        $sessionVerify = true;
        $retCode = $phoneAction->sendRegCode($mid, $phone, 8, $positionId, $sessionVerify);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 301:
                $this->showError301();
                break;
            case 302:
                $this->showError302();
                break;
            case 400:
                $this->showError400();
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 验证并快捷注册
     * -
     * @return void
     */
    public function actionQuickReg()
    {
        session_start();
        $phone = \Common\Utils\Url::getStringParam("phone");
        $mid = \Common\Utils\Url::getStringParam("mid");
        $verifyCode = \Common\Utils\Url::getStringParam("verify_code");

        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(300, "", "请确认您的手机号是否填写正确!");
        }

        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(501, "", "项目标识不正确!");
        }

//        $redis = RedisEx::getInstance();
//        $key = $this->getPhoneActivityCheckNumsKey();
//        $checkNum = $redis->get($key);
//        if ($checkNum >= $this->getLimitNumsKey() || $_SESSION['visitDate'] > time())
        if ($_SESSION['visitDate'] > time())
        {
            $this->codeResponse(4101, "", "系统错误,休息一会儿,请您稍后再试!");
        }
        $sessionVerify = true;
        $phoneAction = loadAction("phone");
        if (!$phoneAction->checkRegCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify, true))
        {
            $this->codeResponse(400, "", "短信验证失败!");
        }
        $userClientIp = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $pwd = mt_rand() . time();
        $password = substr(md5($pwd . MD5KEY), 0, 16);

        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        $regAction = loadAction('reg');

        $gid = 300;
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        $source = \Service\AccountStat\Consts\AccountStat::getSourceByMid($mid);
        if (!$source)
        {
            $source = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
            if (!$source)
            {
                $source = "webapi";
            }
        }
        $result = $regAction->regPhone($phone, $password, $pwdStrength, $userClientIp, 'login.2345.com', $gid, $source, $clientType);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            $this->codeResponse(403, "", 'error:' . $msgs[$states[0]][$states[1]]);
        }
        elseif ($states[0] == 300)
        {
            $this->codeResponse($states[0] + $states[1], "", $msgs[$states[0]][$states[1]]);
        }
        elseif ($states[0] == 200)
        {
            unset($_SESSION['X-CSRF-Token']);
            $redis = RedisEx::getInstance();
            $key = $this->getMidRegNumKey($mid);
            $redis->incr($key);
            $redis->expire($key, 2592000);  //有效期30天
            $msg = "phone:{$phone} passid:{$result[1]['passid']}";
            xLog('PhoneActivityReg/' . $mid, 'PhoneActivityReg', $msg, 'info');
            $this->codeResponse(200, '', "注册成功！");
        }
        else
        {
            $this->codeResponse(500, "", "禁止注册！");
        }
    }


    /**
     * User: panj
     * 设置会话下次访问的有效时间
     * @return void
     */
    private function setVisitDate()
    {
        session_start();
        $step = 2;
        $second = 60;
        $_SESSION['visitDate'] = time() + floor($_SESSION['visitNum'] / $this->getLimitNumsKey()) * $second * $step;
    }

    /**
     * 返回成功信息
     * -
     * @param string $msg 描述信息
     * @return void
     */
    private function show200($msg = '发送成功！')
    {
        $this->codeResponse(200, "", $msg);
    }

    /**
     * 显示300错
     * -
     * @return void
     */
    private function showError300()
    {
        $this->codeResponse(300, "", "请输入正确的手机号码！");
    }

    /**
     * 显示301错
     * -
     * @return void
     */
    private function showError301()
    {
        $this->codeResponse(301, "", "该手机号已被其他用户占用！");
    }

    /**
     * 显示302错
     * -
     * @return void
     */
    private function showError302()
    {
        $this->codeResponse(302, "", "该手机号已被其他用户绑定！");
    }

    /**
     * 显示400错
     * -
     * @return void
     */
    private function showError400()
    {
        $this->codeResponse(400, "", "发送频繁，请稍后再试！！");
    }

    /**
     * 显示500错
     * -
     * @return void
     */
    private function showError500()
    {
        $this->codeResponse(500, "", "服务器忙，请稍后再试！");
    }

    /**
     * 显示501错
     * -
     * @return void
     */
    private function showError501()
    {
        $this->codeResponse(501, "", '项目标识不正确！');
    }
}
