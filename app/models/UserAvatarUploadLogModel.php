<?php
/**
 * 用户头像上传记录表
 * User: fuq
 * Date: 2020/04/29
 */

use Service\Capacity\GetRangeTable;

class UserAvatarUploadLogModel extends Model
{
    // 模板表
    const TEMPLATE_TABLE_NAME = 'template_user_avatar_upload_log';
    const TABLE_NAME = '_user_avatar_upload_log_';

    private $pdo;
    private $tableName ; // {group}_user_avatar_upload_log_{mod}

    /**
     * 初始化
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
    }

    /**
     * 添加记录
     *
     * @param string $groupName 生态名
     * @param int    $passid    用户id
     * @param array  $data      待记录数据
     *
     * @return bool
     */
    public function addLog(string $groupName, int $passid, array $data) {
        // 初始化表
        $this->createTable($groupName, $passid);

        return $this->pdo->insert($this->tableName, $data);
    }

    /**
     * 创建表
     *
     * @param string $groupName 生态名
     * @param int    $passid    用户id
     *
     * @return void
     */
    protected function createTable(string $groupName, int $passid) {
        // 更具passid设置表
        $this->tableName = GetRangeTable::UserAvatarUploadLog($groupName . '_', $passid);
        // 检查表是否存在，不存在即创建
        $this->checkTable($this->tableName);
    }

    /**
     * 检查表是否存在，不存在就创建
     *
     * @param string $tableName 表名
     *
     * @return void
     */
    private function checkTable(string $tableName)
    {
        $sql = 'CREATE TABLE IF NOT EXISTS `' . $tableName . '` LIKE `' . self::TEMPLATE_TABLE_NAME . '`';
        $this->pdo->query($sql);
    }

    /**
     * 查找单条记录
     *
     * @param string $groupName  生态名
     * @param int    $passid     passid
     * @param string $avatarName 头像名
     *
     * @return array|bool
     */
    public function findOneByAvatarName(string $groupName, int $passid, string $avatarName)
    {
        // 更具passid设置表
        $this->tableName = GetRangeTable::UserAvatarUploadLog($groupName . '_', $passid);
        $sql = "SELECT * FROM {$this->tableName} WHERE avatar_name = :avatarName";
        $condition = [':avatarName' => $avatarName];

        return $this->pdo->find($sql, $condition);
    }

    /**
     * 查找单条记录
     *
     * @param string $groupName  生态名
     * @param int    $passid     passid
     * @param string $condition  删除条件
     *
     * @return array|bool
     */
    public function delLog(string $groupName, int $passid, array $condition)
    {
        // 更具passid设置表
        $this->tableName = GetRangeTable::UserAvatarUploadLog($groupName . '_', $passid);

        return $this->pdo->delete($this->tableName, $condition);
    }
}
