<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/4
 * Time: 13:22
 */

namespace Common\Validator;

use Service\Encryption\Sign\SignManager;

class Params
{
    protected $errorInfo = [
        'code' => '',
        'msg' => ''
    ];

    /**
     * 设置错误
     * -
     * @param string $code 错误码
     * @param string $msg 错误描述
     * @return void
     */
    private function setError($code, $msg)
    {
        $this->errorInfo = [
            'code' => $code,
            'msg' => $msg
        ];
    }

    /**
     * 获取错误信息
     * -
     * @return array
     */
    public function getError()
    {
        return $this->errorInfo;
    }

    /**
     * 校验传递上来的数据
     * -
     * @param string $inputField 解析字段名称
     * @param array $necessaryField 必要字段
     * @return array|bool|mixed|string
     */
    public function check($inputField, $necessaryField)
    {
        $LoginToken = loadAction("LoginToken");
        $data = \Common\Utils\Url::getStringParam($inputField);
        if (empty($data))
        {
            $this->setError(4020, "参数不能为空");
            return false;
        }
        $dataArr = explode(':', $data);
        if (count($dataArr) != 2)
        {
            $this->setError(4022, "参数格式不正确");
            return false;
        }
        list($exId, $aesData) = $dataArr;
        if (empty($LoginToken->getExternalConfig($exId)))
        {
            $this->setError(4023, "未识别的应用");
            return false;
        }
        $aesDecodeData = $LoginToken->aes128cbcHexDecrypt($exId, $aesData);
        if (!empty($aesDecodeData['exId']) && $aesDecodeData['exId'] != $exId)
        {
            $this->setError(4024, "请检查签名一致性");
            return false;
        }
        if (empty($aesDecodeData))
        {
            $this->setError(4021, "校验失败");
            return false;
        }
        foreach ($necessaryField as $necessaryInfo)
        {
            $_REQUEST[$necessaryInfo] = $aesDecodeData[$necessaryInfo];
        }
        return $aesDecodeData;
    }


    /**
     * 生成sign
     * -
     * @param string $exId 外部项目唯一标识
     * @param array $requestArray 加密参数
     * @return string
     */
    public static function getSign($exId, $requestArray)
    {
        $paramArray = array();
        ksort($requestArray);
        foreach ($requestArray as $key => $val)
        {
            if ($key != SignManager::$paramSignName)
            {
                $paramArray[] = $key . '=' . $val;
            }
        }
        $LoginToken = loadAction("LoginToken");
        $signKey = $LoginToken->getExternalConfig($exId, "signKey");
        if (empty($signKey))
        {
            return "";
        }
        $paramStr = implode('&', $paramArray) . '&' . SignManager::$paramAppKeyName . '=' . $signKey;
        return md5($paramStr);
    }

    /**
     * 签名校验
     * -
     * @param string $signValue 签名字符串
     * @param array $requestArray 解密参数
     * @return bool|null
     */
    public static function checkSign($signValue, $requestArray)
    {
        if (self::getSign($requestArray['exId'], $requestArray) == $signValue)
        {
            return true;
        }
        else
        {
            return null;
        }
    }
}
