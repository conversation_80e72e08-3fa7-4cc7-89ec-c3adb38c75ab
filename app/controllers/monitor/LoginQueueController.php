<?php

/**
 * Class LoginQueueController
 */
class LoginQueueController extends Controller
{
    private $config;

    private $upValue = 1000;

    /**
     * RedisQueueController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->config = Config::get('redis');
        $ip = get_client_ip();
        if (!\Service\Security\Server::checkCompanyIp($ip))
        {
            header('HTTP/1.1 404 Not Found');
            exit();
        }
    }

    /**
     * 比对 数据库中 与 redis中 用户数据长度差
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $result = array();
        $loginQueueRedis = loadAction('loginQueueRedis');
        $length = $loginQueueRedis->loginQueueLength();
        if ($length === -1)
        {
            header('HTTP/1.1 404 Not Found');
            echo '用户注册数据redis连接失败';
            exit();
        }
        array_push($result, 'loginQueueRedis Length: ' . $length);

        $memory = $loginQueueRedis->loginQueueRedis->info("MEMORY");
        // 10737418240 10G
        if (isset($memory["used_memory"]) && isset($memory["used_memory_human"]))
        {
            array_push($result, 'regQueueRedis used_memory:' . $memory["used_memory"] . ', used_memory_human:' . $memory["used_memory_human"]);
        }
        echo implode("<br/>", $result);

        if ($length > $this->upValue)
        {
            header('HTTP/1.1 404 Not Found');
            echo '用户登录数据redis队列值:' . $length;
            exit();
        }
    }
}
