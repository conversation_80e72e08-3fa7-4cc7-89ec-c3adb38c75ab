document.domain = '2345.com';
function gameCallback(xiaoyouxi, game){
	//小游戏
	var _xiaoyouxi = new Array();
	_xiaoyouxi.push('<ul class="xiaoyx clearfix">');
	for(var i = 0; i < xiaoyouxi.length; i++){
		if(xiaoyouxi[i].type == "history"){
			var tuijian = "";
		}else{
			var tuijian = '<i class="tj"></i>';
		}
		if(i == 6){
			_xiaoyouxi.push('<li class="lastli"><a href="'+xiaoyouxi[i].url+'" target="_blank" onclick="cc(\'xiaoyouxi\')">'+ tuijian +'<img src="'+xiaoyouxi[i].img+'" width="80" height="60"><br>'+decodeURIComponent(xiaoyouxi[i].title)+'</a></li>');
			break;
		}else{
			_xiaoyouxi.push('<li><a href="'+xiaoyouxi[i].url+'" target="_blank" onclick="cc(\'xiaoyouxi\')">'+ tuijian +'<img src="'+xiaoyouxi[i].img+'" width="80" height="60"><br>'+decodeURIComponent(xiaoyouxi[i].title)+'</a></li>');			
		}
	}
	_xiaoyouxi.push("</ul>");
	$("xiaoyouxi_api").html(_xiaoyouxi.join(""));
	//网页游戏	
	var _game = new Array();
	_game.push('<ul class="imgli clearfix">');
	for(var i = 0; i < game.length; i++){
		if(game[i].type == "history"){
			if(i == (game.length-1)){
				_game.push('<li class="listli"><a onclick="cc(\'yeyou\')" href="'+game[i].last_server_url+'" target="_blank"><img src=http://img1.2345.com/wanimg'+game[i].img+' width="225" height="100"></a><strong><a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank">'+decodeURI(game[i].title)+'</a></strong>'
				+ '<p>最近登录：<a onclick="cc(\'yeyou\')" href="'+game[i].last_server_url+'" target="_blank">'+decodeURIComponent(game[i].last_server_title)+'</a><em class="hot"></em></p>'
				+ '<p>最新开服：<a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank">'+decodeURIComponent(game[i].new_server_title)+'</a></p>');
			}else{
				_game.push('<li class="listli"><a onclick="cc(\'yeyou\')" href="'+game[i].last_server_url+'" target="_blank"><img src=http://img1.2345.com/wanimg'+game[i].img+' width="225" height="100"></a><strong><a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank">'+decodeURI(game[i].title)+'</a></strong>'
				+ '<p>最近登录：<a onclick="cc(\'yeyou\')" href="'+game[i].last_server_url+'" target="_blank">'+decodeURIComponent(game[i].last_server_title)+'</a><em class="hot"></em></p>'
				+ '<p>最新开服：<a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank">'+decodeURIComponent(game[i].new_server_title)+'</a></p>');
			}
		}else{
			if(i == (game.length-1)){
				_game.push('<li class="listli"><a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank"><i class="tj"></i><img src=http://img1.2345.com/wanimg'+game[i].img+' width="225" height="100"></a><strong><a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank" class="entergame">进入新服</a><a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank">'+decodeURI(game[i].title)+'</a></strong>'
				+ '<p>'+decodeURI(game[i].tj_text1)+'<br />'+decodeURIComponent(game[i].tj_text2)+'</p></li>');
			}else{
				_game.push('<li><a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank"><i class="tj"></i><img src="http://img1.2345.com/wanimg'+game[i].img+'" width="225" height="100"></a><strong><a onclick="cc(\'yeyou\')" target="_blank" href="'+
				game[i].new_server_url +'" class="entergame">进入新服</a><a onclick="cc(\'yeyou\')" href="'+game[i].new_server_url+'" target="_blank">'+decodeURI(game[i].title)+'</a></strong>'
				+ '<p>'+decodeURI(game[i].tj_text1)+'<br />'+decodeURIComponent(game[i].tj_text2)+'</p></li>');
			}
		}
	}
	_game.push("</ul>");
	$("game_api").html(_game.join(""));
	
}