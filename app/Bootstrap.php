<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：Bootstrap.php
 * 摘    要：MVC框架启动文件
 * 作    者：张小虎
 * 修改日期：2013.10.12
 */
$loader = require APPPATH . '/../vendor/autoload.php';

// 2020年6月28日 10:32:52 潘健丰 改造环境配置用.env形式
// vendor不能用是因为， 一旦composer update了
// 代码会报错 Class 'Service\Func\ServiceFunc' not found
require_once __DIR__ . '/includes/phpdotenv/src/Exception/ExceptionInterface.php';
require_once __DIR__ . '/includes/phpdotenv/src/Exception/InvalidPathException.php';
require_once __DIR__ . '/includes/phpdotenv/src/Exception/InvalidCallbackException.php';
require_once __DIR__ . '/includes/phpdotenv/src/Exception/InvalidFileException.php';
require_once __DIR__ . '/includes/phpdotenv/src/Exception/ValidationException.php';
require_once __DIR__ . '/includes/phpdotenv/src/Loader.php';
require_once __DIR__ . '/includes/phpdotenv/src/Validator.php';
require_once __DIR__ . '/includes/phpdotenv/src/Dotenv.php';
$configPath = '/opt/case/config/smsp.2345.net/env';
(new \Dotenv\Dotenv($configPath, '.env'))->load();
isset($_ENV['RUN_MODE']) ? define('RUNMODE', $_ENV['RUN_MODE']) : define('RUNMODE', 'production');
// 如果不是pro环境, 则读取 .RUNMODE.env 并能覆盖.env
if (RUNMODE !== 'production' && file_exists($configPath . RUNMODE . '/.env')) {
    (new \Dotenv\Dotenv($configPath, '.' . RUNMODE . '.env'))->overload();
}

use Octopus\Router;
use Octopus\Logger\Handler\StreamHandler;
use Octopus\Logger\ErrorHandler;
use Octopus\Logger\Registry;
use Octopus\Logger;

Config::load(APPPATH . '/config/config.php');
if (RUNMODE === 'development')
{
    ini_set("display_errors", 1);
    ini_set("default_charset", 'gbk');
    ini_set("serialize_precision", -1);
    error_reporting(E_ALL);

    $logger = new Logger("debug");
    $logger->pushHandler(new StreamHandler(APPPATH . "/logs/debug.log"));
    ErrorHandler::register($logger);
    Registry::addLogger($logger);
}
$modules = Config::get("modules");
if ($modules)
{
    $moduleNames = array_keys($modules);
    foreach ($moduleNames as $moduleName)
    {
        $loader->setPsr4("$moduleName\\", array(
            SRCPATH . "/modules/$moduleName/actions",
            SRCPATH . "/modules/$moduleName/controllers",
            SRCPATH . "/modules/$moduleName/models"
        ));
    }
}
Octopus::init();
Octopus::callHook('pre_bootstrap');
Router::parseUrl();
Octopus::callHook('cache_override');
$className = Router::fetchClass();
$methodName = Router::fetchMethod();
$params = Router::fetchParams();
if ($className == "" || !class_exists($className))
{
    show404();
}
else
{
    if (!in_array($methodName, get_class_methods($className)))
    {
        show404();
    }
    else
    {
        $reflectionMethod = new ReflectionMethod($className, $methodName);
        if (!$reflectionMethod->isPublic() || $reflectionMethod->isStatic())
        {
            show404();
        }
        else
        {
            Octopus::callHook('pre_controller');
            define("VIEWPATH", preg_replace("/\\" . DIRECTORY_SEPARATOR . "controllers\\" . DIRECTORY_SEPARATOR . ".*/", DIRECTORY_SEPARATOR . "views", realpath($loader->findFile($className))));
            $class = $className::getInstance();
            Octopus::callHook('post_controller_constructor');
            call_user_func_array(array(&$class, $methodName), $params);
            Octopus::callHook('post_controller');
        }
    }
}
Octopus::callHook('post_bootstrap');
