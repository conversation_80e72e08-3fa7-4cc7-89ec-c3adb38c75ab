<?php

use WebLogger\Facade\LoggerFacade;

/**
 * Copyright (c) 2013,锟较猴拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷萍锟斤拷煞锟斤拷锟斤拷薰锟剿?
 * 锟侥硷拷锟斤拷锟狡ｏ拷Common.php
 * 摘    要锟斤拷锟斤拷锟矫猴拷锟斤拷锟斤拷
 * 锟斤拷    锟竭ｏ拷锟斤拷小锟斤拷
 * 锟睫革拷锟斤拷锟节ｏ拷2013.10.12
 */
function showError($code, $statusCode = 500, $desc = '')
{
    header("HTTP/1.1 $statusCode");
    loadView('error.tpl.html', array('error' => $code, 'desc' => $desc));
    exit;
}

function showLoginError($code, $statusCode = 500, $desc = '')
{
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
    {
        $response = array('err' => 0, 'msg' => $desc, 'forward' => '/', 'refer' => '/');
        die(json_encode($response));
    }

    header("HTTP/1.1 $statusCode");
    loadView('loginerror.tpl.html', array('error' => $code, 'desc' => $desc));
    exit;
}

function show404()
{
    header("HTTP/1.1 404");
    loadView('error.tpl.html', array('error' => 404));
    exit;
}

function includeFile($includeFile)
{
    include_once APPPATH . '/includes/' . $includeFile;
}

function includeBase($baseClass)
{
    include_once APPPATH . '/base/' . $baseClass . '.php';
}

/**
 * 锟斤拷锟斤拷redis锟斤拷台锟斤拷
 * @param $redisClass
 * @param string $directory
 * <AUTHOR>
 */
function includeRedis($redisClass, $directory = "base/")
{
    require_once APPPATH . '/redis/' . $directory . $redisClass . '.php';
}

function loadClass($classFile, $className)
{
    static $classes = array();
    if (!isset($classes[$className]))
    {
        if (file_exists($classFile))
        {
            include $classFile;
            $classes[$className] = new $className();
        }
        else
        {
            showError(500);
        }
    }
    return $classes[$className];
}

function loadFilter($filterName)
{
    $filterName = ucfirst($filterName) . 'Filter';
    $filterFile = APPPATH . "/filters/$filterName.php";
    return loadClass($filterFile, $filterName);
}

function loadAction($actionName)
{
    $actionName = ucfirst($actionName) . 'Action';
    $actionFile = APPPATH . "/actions/$actionName.php";
    return loadClass($actionFile, $actionName);
}

function loadModel($modelName)
{
    $modelName = ucfirst($modelName) . 'Model';
    $modelFile = APPPATH . "/models/$modelName.php";
    return loadClass($modelFile, $modelName);
}

function loadVendor($className)
{
    $className = ucfirst($className);
    $classFile = APPPATH . "/vendors/$className.php";
    return loadClass($classFile, $className);
}

/**
 * load 锟斤拷应 pc wap 锟斤拷模锟斤拷
 *
 * @param string $tpl    tpl
 * @param string $wapTpl wapTpl
 * @param array  $array  array
 * @param bool   $return return
 *
 * @return string
 */
function loadCompatibleView($tpl, $wapTpl, $array = array(), $return = false)
{
    if (IsMobile())
    {
        $tpl = $wapTpl;
    }
    if ($return)
    {
        return loadView($tpl, $array, $return);
    }
    else
    {
        loadView($tpl, $array, $return);
        return "";
    }
}

/**
 *
 * 锟斤拷锟截撅拷态页锟斤拷
 *
 * @param  string     $tpl  模锟斤拷路锟斤拷
 * @param array $array      页锟斤拷锟斤拷锟斤拷
 * @param bool  $return     bool
 *
 * @author锟斤拷dongx
 * @return mixed|string|void
 */
function loadView($tpl, $array = array(), $return = false)
{
    static $smarty;
    //锟斤拷锟斤拷全锟街撅拷态锟斤拷源锟斤拷锟斤拷锟斤拷锟斤拷止锟斤拷锟街撅拷态锟斤拷源锟睫改碉拷锟酵伙拷锟斤拷未锟斤拷锟斤拷
    $static = '?201711271';
    $array['static'] = $static;
    if (!$smarty)
    {
        include 'class/smarty/Smarty.class.php';
        $smarty = new Smarty();
        $smarty->template_dir = APPPATH . '/views/tpl/';
        $smarty->compile_dir = APPPATH . '/views/tpl_c/';
    }
    $smarty->assign('pageArray', $array);
    if ($return)
    {
        return $smarty->fetch($tpl);
    }
    else
    {
        $smarty->display($tpl);
    }
}

function removeInvisibleCharacters($str, $urlEncoded = TRUE)
{
    $nonDisplayables = array();
    // every control character except newline (dec 10)
    // carriage return (dec 13), and horizontal tab (dec 09)
    if ($urlEncoded)
    {
        $nonDisplayables[] = '/%0[0-8bcef]/'; // url encoded 00-08, 11, 12, 14, 15
        $nonDisplayables[] = '/%1[0-9a-f]/'; // url encoded 16-31
    }
    $nonDisplayables[] = '/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]+/S'; // 00-08, 11, 12, 14-31, 127
    do
    {
        $str = preg_replace($nonDisplayables, '', $str, -1, $count);
    }
    while ($count);
    return $str;
}

function redirect($uri = '', $method = 'location', $http_response_code = 302)
{
    if (preg_match("/^http|https/", $uri) && !in_array($_REQUEST['mid'], ['pcbs', 'JSQNLLQ'])) {
        $uri = dev::getDevDomain($uri);
    }
    header("Location: " . $uri, TRUE, $http_response_code);
    exit;
}

function closeWindow()
{
    die('<script type="text/javascript">window.opener=null;window.open("", "_self", "");window.close();</script>');
}

function get_remote_port()
{
    return getenv("REAL_REMOTE_PORT") === false ? '00000' : getenv("REAL_REMOTE_PORT") ;
}

/**
 * 锟斤拷取锟酵伙拷锟斤拷 ip
 *
 * @param bool|true $twiceProxy 锟角否经癸拷锟斤拷锟斤拷锟斤拷锟斤拷锟街?
 *
 * @return string
 */
function get_client_ip($twiceProxy = true)
{
    static $onlineIp = 0;
    if ($onlineIp !== 0) {
        return $onlineIp;
    }

    //使锟斤拷锟斤拷锟街凤拷式锟斤拷web锟斤拷nginx锟斤拷锟斤拷使锟斤拷real-ip模锟介，锟斤拷锟斤拷锟斤拷实IP写锟斤拷
    $onlineIp = $_SERVER['REMOTE_ADDR'];

    if (filter_var($onlineIp, FILTER_VALIDATE_IP)) {
        return $onlineIp;
    } else {
        xLog('getClientIP', 'getClientIP', '锟斤拷$_SERVER[REMOTE_ADDR]锟斤拷没锟叫伙拷取锟斤拷IP');
        return '0.0.0.0';
    }

    $allowProxys = array(
        '*************',
        '**************',
        '*************',
        '**************',
        '*************',
        '*************',
        '************',
        '*************',
        '***************',
        '***************',
        '*************',
        '*************',
        '************',
        '************',
        '*************',
        '*************',
        '*************',
        '***************',
        '*************',
        '***************',
        '*************',
        '***************',
        '*************',
        '***************',
        '*************',
        '*************',
        '*************',
        '*************',
        '************',
        '**************',
        '*************',
        '***************',
        '************',
        '************',
        '*************',
        '*************',
        '*************',
        '101.71.94.232',
        '42.62.12.130',
        '42.62.12.142',
        '42.62.12.138',
        '************',
        '************',
        '************',
        '************',
        '************',
        '************',
        '**************',
        '************',
        '*************',
        '**************',
        '*************',
        '**************',
        '**************',
        '*************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        //锟斤拷锟斤拷路锟斤拷ip
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        '*************',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        //https://passport.2345.org锟斤拷锟斤拷
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '43.254.221.47',
        '128.1.136.190',
        '128.1.136.229',
        '221.228.75.158',
        '117.121.132.249',
        //https://passport.ie31.com锟斤拷锟斤拷
        '221.228.75.184',
        '221.228.75.185',
        '221.228.75.187',
        '117.121.132.227',
        '117.121.132.228',
        //http://passport.2345shengqian.com Web锟斤拷
        '**************',
        '**************',
        '************',
        '************',
        '35.236.179.215',
        '35.221.174.155',
        '103.218.240.164',
        '***************',
        '**************',
        '**************',
        '**************',
        '**************',
        '***************',
        '***************',
        // 锟斤拷锟皆伙拷锟斤拷迁锟斤拷walle锟斤拷185锟斤拷锟斤拷锟斤拷锟斤拷
        '172.17.18.185',
        '14.29.109.1',
        '***********',
        '***********',
        '***********',
        '***********',
        '***********',
        '***********',
        '***********',
        '***********',
        '************',
        '************',
        '************',
        '************',
        '************',
        '************',
        '************',
        '************',
        '************',
        '************',
        '************',
    );
    if (getenv('REMOTE_ADDR'))
    {
        $onlineip = getenv('REMOTE_ADDR');
    }
    else
    {
        $onlineip = $_SERVER['REMOTE_ADDR'];
    }

    if (in_array($onlineip, $allowProxys))
    {
        $ips = "";
        if (getenv('HTTP_X_FORWARDED_FOR'))
        {
            $ips = getenv('HTTP_X_FORWARDED_FOR');
        }
        elseif ($_SERVER['HTTP_X_FORWARDED_FOR'])
        {
            $ips = $_SERVER['HTTP_X_FORWARDED_FOR'];
        }
        if ($ips)
        {
            $logIpS = $ips;
            $ips = explode(",", $ips);
            $curIP = trim(array_pop($ips));

            if ($twiceProxy)
            {
                $daoGouIpArray = array(
                    '**************',
                    '**************',
                    '************',
                    '************',
                    '**************',
                    '**************',
                    '**************',
                    '**************',
                    '***************',
                    '***************',
                );
                if (in_array($curIP, $daoGouIpArray))
                {
                    $logStr = 'REMOTE_ADDR:' . $onlineip . '; X-FORWARD-FOR:' . $logIpS;
                    xLog('daogouIP', 'daogouIP', $logStr);
                }

                $twiceProxyIps = array(
                    "***********",
                    "***********",
                    "***************",
                    "***************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "*************",
                    "**************",
                    "**************",
                    "************",
                    "************",
                    '***************',
                    '**************',
                    '**************',
                    '**************',
                    '**************',
                    '***************',
                    '***************',
                );
                if (in_array($curIP, $twiceProxyIps))
                {
                    $curIP = array_pop($ips);
                }
                $onlineip = trim($curIP);
                if (count($ips) > 0)
                {
                    //锟斤拷锟斤拷锟斤拷2锟斤拷锟斤拷锟绞憋拷锟斤拷榭词碉拷锟絏-FORWARD-FOR
                    $xforword = 'REMOTE_ADDR:' . $onlineip . '; X-FORWARD-FOR:' . $logIpS;
                    xLog('xforword', 'xforword', $xforword);
                }
            }
            else
            {
                $onlineip = trim($curIP);
            }
        }
    }
    if (filter_var($onlineip, FILTER_VALIDATE_IP))
    {
        return $onlineip;
    }
    else
    {
        return '0.0.0.0';
    }
}

function curl_get_contents($url, $options = array())
{
    $default = array(
        CURLOPT_URL => $url,
        CURLOPT_HEADER => 0,
        CURLOPT_RETURNTRANSFER => 1,
        CURLOPT_USERAGENT => "Mozilla/5.0 (Windows NT 6.1; rv:17.0) Gecko/17.0 Firefox/17.0",
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
        CURLOPT_CONNECTTIMEOUT => 3,
        CURLOPT_TIMEOUT => 5,
    );
    foreach ($options as $key => $value)
    {
        $default[$key] = $value;
    }
    $ch = curl_init();
    curl_setopt_array($ch, $default);
    $current = mSecTime();
    $result = curl_exec($ch);
    $curlInfo = curl_getinfo($ch);
    curl_close($ch);
    LoggerFacade::info("curl_get_contents", [
        "type" => "http_request",
        "path" => $url,
        "exec_time" => mSecTime() - $current,
        "http_code" => $curlInfo["http_code"],
        "result" => \Common\Utils\Encoding::transcoding($result),
    ]);

    return $result;
}

function http_get($url, $params = array(), $options = array())
{
    $paramsFMT = array();
    foreach ($params as $key => $val)
    {
        $paramsFMT[] = $key . "=" . urlencode($val);
    }
    return curl_get_contents($url . ($paramsFMT ? ( "?" . join("&", $paramsFMT)) : ""), $options);
}

function http_post($url, $params = array(), $options = array())
{
    $paramsFMT = array();
    foreach ($params as $key => $val)
    {
        if (is_array($val))
        {
            //锟斤拷止锟斤拷锟斤拷锟斤拷锟?
            $paramsFMT[] = $key . "=" . json_encode($val);
        }
        else
        {
            $paramsFMT[] = $key . "=" . urlencode($val);
        }
    }
    $options[CURLOPT_POST] = 1;
    $options[CURLOPT_POSTFIELDS] = join("&", $paramsFMT);
    return curl_get_contents($url, $options);
}

function http_post_json($url, $params = array(), $options = array())
{
    $options[CURLOPT_POST] = 1;
    $data_string = json_encode($params, JSON_UNESCAPED_SLASHES);
    if (!isset($options[CURLOPT_HTTPHEADER])) {
        $options[CURLOPT_HTTPHEADER] = [];
    }
    $options[CURLOPT_POSTFIELDS] = $data_string;
    $options[CURLOPT_HTTPHEADER] = array_merge($options[CURLOPT_HTTPHEADER] , [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data_string),
    ]);
    return curl_get_contents($url, $options);
}


function removeXSS($val)
{
    // remove all non-printable characters. CR(0a) and LF(0b) and TAB(9) are allowed  
    // this prevents some character re-spacing such as <java\0script>  
    // note that you have to handle splits with \n, \r, and \t later since they *are* allowed in some inputs  
    $val = preg_replace('/([\x00-\x08,\x0b-\x0c,\x0e-\x19])/', '', $val);

    // straight replacements, the user should never need these since they're normal characters  
    // this prevents like <IMG SRC=@avascript:alert('XSS')>  
    $search = 'abcdefghijklmnopqrstuvwxyz';
    $search .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $search .= '1234567890!@#$%^&*()';
    $search .= '~`";:?+/={}[]-_|\'\\';
    for ($i = 0; $i < strlen($search); $i++)
    {
        // ;? matches the ;, which is optional 
        // 0{0,7} matches any padded zeros, which are optional and go up to 8 chars 
        // @ @ search for the hex values 
        $val = preg_replace('/(&#[xX]0{0,8}' . dechex(ord($search[$i])) . ';?)/i', $search[$i], $val); // with a ; 
        // @ @ 0{0,7} matches '0' zero to seven times  
        $val = preg_replace('/(&#0{0,8}' . ord($search[$i]) . ';?)/', $search[$i], $val); // with a ; 
    }

    // now the only remaining whitespace attacks are \t, \n, and \r 
    $ra1 = Array('javascript', 'vbscript', 'expression', 'applet', 'meta', 'xml', 'blink', 'link', 'style', 'script', 'embed', 'object', 'iframe', 'frame', 'frameset', 'ilayer', 'layer', 'bgsound', 'title', 'base');
    $ra2 = Array('onabort', 'onactivate', 'onafterprint', 'onafterupdate', 'onbeforeactivate', 'onbeforecopy', 'onbeforecut', 'onbeforedeactivate', 'onbeforeeditfocus', 'onbeforepaste', 'onbeforeprint', 'onbeforeunload', 'onbeforeupdate', 'onblur', 'onbounce', 'oncellchange', 'onchange', 'onclick', 'oncontextmenu', 'oncontrolselect', 'oncopy', 'oncut', 'ondataavailable', 'ondatasetchanged', 'ondatasetcomplete', 'ondblclick', 'ondeactivate', 'ondrag', 'ondragend', 'ondragenter', 'ondragleave', 'ondragover', 'ondragstart', 'ondrop', 'onerror', 'onerrorupdate', 'onfilterchange', 'onfinish', 'onfocus', 'onfocusin', 'onfocusout', 'onhelp', 'onkeydown', 'onkeypress', 'onkeyup', 'onlayoutcomplete', 'onload', 'onlosecapture', 'onmousedown', 'onmouseenter', 'onmouseleave', 'onmousemove', 'onmouseout', 'onmouseover', 'onmouseup', 'onmousewheel', 'onmove', 'onmoveend', 'onmovestart', 'onpaste', 'onpropertychange', 'onreadystatechange', 'onreset', 'onresize', 'onresizeend', 'onresizestart', 'onrowenter', 'onrowexit', 'onrowsdelete', 'onrowsinserted', 'onscroll', 'onselect', 'onselectionchange', 'onselectstart', 'onstart', 'onstop', 'onsubmit', 'onunload');
    $ra = array_merge($ra1, $ra2);

    $found = true; // keep replacing as long as the previous round replaced something 
    while ($found == true)
    {
        $val_before = $val;
        for ($i = 0; $i < sizeof($ra); $i++)
        {
            $pattern = '/';
            for ($j = 0; $j < strlen($ra[$i]); $j++)
            {
                if ($j > 0)
                {
                    $pattern .= '(';
                    $pattern .= '(&#[xX]0{0,8}([9ab]);)';
                    $pattern .= '|';
                    $pattern .= '|(&#0{0,8}([9|10|13]);)';
                    $pattern .= ')*';
                }
                $pattern .= $ra[$i][$j];
            }
            $pattern .= '/i';
            $replacement = substr($ra[$i], 0, 2) . '<x>' . substr($ra[$i], 2); // add in <> to nerf the tag  
            $val = preg_replace($pattern, $replacement, $val); // filter out the hex tags  
            if ($val_before == $val)
            {
                // no replacements were made, so exit the loop  
                $found = false;
            }
        }
    }
    return $val;
}

/**
 * 锟斤拷锟斤拷ip锟斤拷锟饺★拷锟斤拷锟絠d
 */
function getCityId($ip)
{
    $ip = explode('.', $ip);
    $db_file = APPPATH . '/includes/ipdb/' . intval($ip[0]) . '/' . intval($ip[1]) . '.php';
    @include($db_file);
    $tail = $ip[2] * 255 + $ip[3];
    if ($ipdb)
    {
        foreach ($ipdb as $k => $v)
        {
            $val = $v;
            if ($tail <= $k)
                break;
        }
        return $val[2];
    }
}

/**
 * 锟斤拷锟捷筹拷锟斤拷id锟斤拷取锟斤拷锟斤拷锟斤拷
 */
function getArea($cityId)
{
    $citys = include(APPPATH . "/includes/citys.inc.php");
    if ($cityId)
    {
        if (isset($cityId) && isset($citys[$cityId]))
        {
            $area = $citys[$cityId];
        }
        else
        {
            $area = "未知";
        }
    }
    else
    {
        $area = "未知";
    }
    return $area;
}

if (!function_exists('env'))
{
    /**
     * Gets the value of an environment variable.
     *
     * @param  string  $key 锟斤拷 description
     * @param  mixed   $default 默锟斤拷值 description
     *
     * @return mixed
     */
    function env($key, $default = null)
    {
        if (isset($_ENV[$key])) {
            $value = $_ENV[$key];
        } elseif (isset($_SERVER[$key])) {
            $value = $_SERVER[$key];
        } elseif (function_exists('putenv') && function_exists('getenv')) {
            $value = getenv($key);
        } else {
            $value = false;
        }

        if ($value === false) {
            return $default;
        }

        switch (strtolower($value)) {
            case 'true':
            case '(true)':
                return true;
            case 'false':
            case '(false)':
                return false;
            case 'empty':
            case '(empty)':
                return '';
            case 'null':
            case '(null)':
                return null;
        }

        $len = strlen($value);

        if ($len > 1 && $value[0] == '"' && $value[$len - 1] == '"') {
            return substr($value, 1, -1);
        }

        return $value;
    }
}

function loadBlockConfig($key)
{
    static $conf;
    if (empty($conf[$key])) {
        if (defined('BASEPATH')) {
            $conf = include_once  BASEPATH . '/block/block_config.php';
        } else {
            LoggerFacade::error("loadBlockConfig锟斤拷锟斤拷未锟斤拷锟藉常锟斤拷", [
                'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
                'key' => $key,
            ]);
            $conf = include_once  dirname(__DIR__, 2) . '/block/block_config.php';
        }
        
    }
   if (isset($conf[$key])) {
        return $conf[$key];
   }
    LoggerFacade::error("loadBlockConfig 锟斤拷锟斤拷未锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷目", [
        'key' => $key,
        'conf' => $conf,
    ]);
   return [];
}




/**
 * Port from https://gitlab.2345.cn/support/basic/feedback.2345.com/-/blob/master/app/includes/functions.inc.php#L488
 * 
 */
function getAreaNameByIpLib($ip)
{
    $url = 'http://ip.hao184.com/v2/ip/region';
    $data = ['ip' => $ip];
    $aesKey = 'dex2glL2mexxo8ar';
    $aesIv = 'dex2glL2mexxo8ar';
    $sinKey = 'login_php_sign';
    $aesEncrypt = AesEncrypt($data, $aesKey, $aesIv);
    $req = [
        'mid' => 'login-php',
        'timestamp' => time(),
        'data' => $aesEncrypt,
    ];
    $sign = Md5Sign($req, $sinKey);
    $req['sign'] = $sign;
    $curlJson = CurlJson($url, $req, ['Mid: login-php']);
    $jsonDecode = json_decode($curlJson, true);
    if ($jsonDecode['code'] == 200)
    {
        // return iconvArray($jsonDecode['data']['city'], "UTF-8", "GBK");
        $city = iconvArray($jsonDecode['data']['city'], "UTF-8", "GBK");
        if ( $city== ''){
            return "未知";
        }else{
            return $city;
        }
    } else {
        return "未知";
    }
}

/**
 * 锟斤拷    锟杰ｏ拷锟斤拷锟斤拷锟絛ata锟斤拷锟捷硷拷锟斤拷
 * 锟睫革拷锟斤拷锟节ｏ拷2023-11-29
 * 锟斤拷    锟竭ｏ拷shim
 *
 * @param array $data 锟斤拷锟斤拷锟絘pi锟接口诧拷锟斤拷
 * @return string
 */
function AesEncrypt($data, $aesKey, $aesIv)
{
    $dataStr = json_encode(json_encode($data, JSON_UNESCAPED_UNICODE), JSON_UNESCAPED_UNICODE);
    return base64_encode(openssl_encrypt($dataStr, 'AES-128-CBC', $aesKey, OPENSSL_RAW_DATA, $aesIv));
}

function Md5Sign($params, $signKey)
{
    ksort($params);
    $combination = '';
    foreach($params as $key => $val)
    {
        $combination .= $key . '=' . $val . '&';
    }
    return md5($combination.'sign='.$signKey);
}

function CurlJson($url, $data = null, $headers = [])
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_FAILONERROR, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $postBodyString = json_encode($data, JSON_UNESCAPED_UNICODE);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postBodyString);

    $header = array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($postBodyString),
    );
    $headers = array_merge($header, $headers);

    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLINFO_HEADER_OUT, true);

    $reponse = curl_exec($ch);
    $curlInfo = curl_getinfo($ch);
    curl_close($ch);
    return $reponse;
}

function iconvArray($value, $inCharset = "gbk", $outCharset = "utf-8")
{
    if (is_array($value))
    {
        $ret = array();
        foreach ($value as $key => $one)
        {
            $ret[mb_convert_encoding($key, $outCharset, $inCharset)] = iconvArray($one, $inCharset, $outCharset);
        }
    }
    else
    {
        // $ret = iconv($inCharset, $outCharset . "//IGNORE", $value);
        $ret = mb_convert_encoding($value, $outCharset, $inCharset);
    }

    return $ret;
}

function EncodeMembersInfo($data)
{
    if (isset($data['email'])) {
        $data['email'] = Common\Confidential\Sensitive::Encode($data['email']);
    }
    if (isset($data['phone'])) {
        $data['phone'] = Common\Confidential\Sensitive::Encode($data['phone']);
    }
    if (isset($data['tel1'])) {
        $data['tel1'] = Common\Confidential\Sensitive::Encode($data['tel1']);
    }
    if (isset($data['tel2'])) {
        $data['tel2'] = Common\Confidential\Sensitive::Encode($data['tel2']);
    }
    if (isset($data['tel3'])) {
        $data['tel3'] = Common\Confidential\Sensitive::Encode($data['tel3']);
    }
    return $data;
}

function DecodeMembersInfo($data)
{
    if (isset($data['email'])) {
        $data['email'] = Common\Confidential\Sensitive::Decode($data['email']);
    }
    if (isset($data['phone'])) {
        $data['phone'] = Common\Confidential\Sensitive::Decode($data['phone']);
    }
    if (isset($data['tel1'])) {
        $data['tel1'] = Common\Confidential\Sensitive::Decode($data['tel1']);
    }
    if (isset($data['tel2'])) {
        $data['tel2'] = Common\Confidential\Sensitive::Decode($data['tel2']);
    }
    if (isset($data['tel3'])) {
        $data['tel3'] = Common\Confidential\Sensitive::Decode($data['tel3']);
    }
    return $data;
}