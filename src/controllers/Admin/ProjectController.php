<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：ProjectController.php
 * 摘    要：项目管理
 * 作    者：张超
 * 修改日期：2016.01.18
 * */

namespace Admin;
use Controller;
use SessionAction;
use PageAction;
use MsgAction;
use EncodingAction;

class ProjectController extends Controller
{
    
    public function __construct() {
        parent::__construct();
        
        $this->uid = SessionAction::getSessionUid();
        $this->userName = SessionAction::getSessionUserName();
        $this->roleId =  SessionAction::getRoleId();
    }

    /**
     * 功    能：项目列表
     */
    public function actionList()
    {
        $pageArray = array();
        $pageArray['project_name'] = trim($this->Filter->getPost('search_name'));
        $projectModel = ProjectModel::getInstance();
        //EncodingAction::transcoding($pageArray['user_name'])
        $pageAction = PageAction::getInstance();
        $pageArray['project_list'] = $projectModel->getProjectList($pageArray['project_name'], $pageAction->returnPageConfig());
        $count = $projectModel->getProjectList($pageArray['project_name'], array('count' => true));
        $pageArray['showPage'] = $pageAction->showPage($count['total'], '/project/list');

        $pageArray['project_status'] = $projectModel->project_status;
        $pageArray['userName'] = $this->userName;
        $pageArray['roleId'] = $this->roleId;
        
        loadView('project/project_list.tpl.html', $pageArray);
    }
    
    
    /**
     * 功    能：删除项目
     */
    public function actionDelProject() {
        $pro_id = $this->Filter->getPost('pro_id') ? intval($this->Filter->getPost('pro_id')) : 0;
        
        if ($pro_id == 0) {
            MsgAction::returnJsonData(array('status' => 0, 'msg' => '参数缺失'));exit;
        }
        
        //1成功 2没有权限 3数据库删除失败
        $projectModel = ProjectModel::getInstance();
        $erro = $projectModel->delProjectByProId($pro_id) ? 1 : 3; 
        
        MsgAction::returnJsonData(array('status' => $erro, 'msg' => $projectModel->erro[$erro]));exit;
    }
    
    
    /**
     * 功    能：删除项目
     */
    public function actionIfDelRole() {
        $projectModel = ProjectModel::getInstance();
        
        echo $projectModel->ifRole($this->roleId, 'del') ? 1 : 0;  //1有权限 0 无权限
        exit; 
    }
    
    
    /**
     * 功    能：项目操作
     */
    function actionSetProjectInfo() {        
        $pro_name = trim(EncodingAction::transcoding($this->Filter->getPost('pro_name')));
        $act = $this->Filter->getPost('act');
        
        $projectModel = ProjectModel::getInstance();
        
        if (empty($this->uid) || empty($pro_name) || empty($act))
        {
             MsgAction::returnJsonData(array('status' => '0', 'msg' => $projectModel->erro[0]));exit;
        }
        
        $data = array();
        $status = 2;
        switch ($act) {
            case 'add':
                $pro_status = in_array($this->Filter->getPost('pro_status'), array(0,1)) ? $this->Filter->getPost('pro_status') : 1;
                
                $data['project_name'] = $pro_name;
                $data['create_user_id'] = $this->uid;
                $data['status'] = $pro_status;
                $status = $projectModel->addProject($data) ? 1 : 4;
                break;
            case 'edit':
                $pro_id = $this->Filter->getPost('pro_id') ? intval($this->Filter->getPost('pro_id')) : 0;
                $pro_status = $this->Filter->getPost('pro_status');
                if ($pro_id && in_array($pro_status, array(0,1))) {
                    $data['pid'] = $pro_id;
                    $data['project_name'] = $pro_name;
                    $data['status'] = $pro_status;
                    
                    $status = $projectModel->editProject($data) ? 1 : 2;
                } else {
                    $status = 0;
                }
                break;
            default:
                break;
        }   
        
        MsgAction::returnJsonData(array('status' => $status, 'msg' => $projectModel->erro[$status] ));exit;
    }
    
    
    /**
     * 功    能：设置项目 短信类型
     */
    function actionSetProjectSmsType()
    {
        $pid = $this->Filter->getPost('pid') ? intval($this->Filter->getPost('pid')) : 0;
        $smstype_id = $this->Filter->getPost('smstype_id') ? intval($this->Filter->getPost('smstype_id')) : 0;
        if (empty($pid) || empty($smstype_id))
        {
            return MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '参数不全'));
        }
        
        $projectModel = ProjectModel::getInstance();
        $isTrue = $projectModel->setProjectSmsType($pid, $smstype_id);
        $msg = '操作失败';
        $status = 'P00004';
        if ($isTrue)
        {
            $msg = '操作成功';
            $status = 'P00001';
        }
        return MsgAction::returnJsonData(array('status' => $status, 'msg' => $msg));
    }
    
    
    /**
     * 功    能：一个项目的位置列表
     */
    public function actionPositionList()
    {
        $pageArray = array();
        $pageArray['pid'] = $this->Filter->getPost('pid') ? intval($this->Filter->getPost('pid')) : 0;
        $projectModel = ProjectModel::getInstance();
        //EncodingAction::transcoding($pageArray['user_name'])
        $pageAction = PageAction::getInstance();
        $pageArray['position_list'] = $projectModel->getPositionList($pageArray['pid'], $pageAction->returnPageConfig());
        $count = $projectModel->getPositionList($pageArray['pid'], array('count' => true));
        $pageArray['showPage'] = $pageAction->showPage($count['total'], '/project/list');

        $pageArray['userName'] = $this->userName;
        $pageArray['roleId'] = $this->roleId;
        
        loadView('project/project_list.tpl.html', $pageArray);
    }
    
    
    /**
     * 功    能：删除位置
     */
    public function actionDelPosition() {
        $pl_id = $this->Filter->getPost('pl_id') ? intval($this->Filter->getPost('pl_id')) : 0;
        
        if ($pl_id == 0) {
            MsgAction::returnJsonData(array('status' => 0, 'msg' => '参数缺失'));exit;
        }
        
        $projectModel = ProjectModel::getInstance();
        $erro = $projectModel->delPositionById($pl_id) ? 1 : 2; 
        
        MsgAction::returnJsonData(array('status' => $erro, 'msg' => $projectModel->erro[$erro]));exit;
    }
    
    
    /**
     * 功    能：位置操作
     */
    function actionSetPositionInfo() {        
        $position_name = trim(EncodingAction::transcoding($this->Filter->getPost('position_name')));
        $act = $this->Filter->getPost('act');
        
        $projectModel = ProjectModel::getInstance();
        
        if (empty($this->uid) || empty($pid) || empty($position_name) || empty($act))
        {
             MsgAction::returnJsonData(array('status' => '0', 'msg' => $projectModel->erro[0]));exit;
        }
        
        $data = array();
        $status = 2;
        
        $data['position_name'] = $position_name;
        switch ($act) {
            case 'add':   
                $pid = $this->Filter->getPost('pid') ? intval($this->Filter->getPost('pid')) : 0;
                if (!empty($pid)) {
                    $data['pid'] = $pid;
                    $status = $projectModel->addPosition($data) ? 1 : 4;
                } else {
                    $status = 0;
                }  
                break;
            case 'edit':
                $pl_id = $this->Filter->getPost('pl_id') ? intval($this->Filter->getPost('pl_id')) : 0;
                if ($pl_id) {
                    $data['pl_id'] = $pl_id;
                    $status = $projectModel->editPosition($data) ? 1 : 2;
                } else {
                    $status = 0;
                }                
                break;
            default:
                break;
        }   
        
        MsgAction::returnJsonData(array('status' => $status, 'msg' => $projectModel->erro[$status] ));exit;
    }
    
    
    
}
?>
