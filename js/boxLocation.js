$(function()
{
	//弹窗垂直居中
	for(var i = 0; i < $(".boxMod").length; i++)
	{
		$(".boxMod").eq(i).css("margin-top",-($(".boxMod").eq(i).height() / 2));
	};
	
	//添加位置
	var addhtml = "<li><input type='text' class='tabInput' autocomplete='off' /><div class='btnPosition'><a class='btn-Add' href='javascript:'>增加</a><a class='btn-Delet' href='javascript:'>删除</a></div></li>";
	$(".btn-Add").live("click",function()
	{
		$(addhtml).appendTo($(this).parents(".locationList"));
		if($(this).parents(".locationList").find("li").length > 1)
		{
			$(this).parents(".locationList").find(".btn-Delet").show();
		};
	});
	
	//删除项目部
	$(".btn-Delet").live("click",function()
	{
		if($(this).parents(".locationList").find("li").length <= 2)
		{
			$(this).parents(".locationList").find(".btn-Delet").hide();
		}
		$(this).parents("li").remove();
	});
});