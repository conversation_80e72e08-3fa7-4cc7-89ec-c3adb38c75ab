<?php
/**
 * Copyright (c)  上海二三四五网络科技有限公司
 * 文件名称：ApiData.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：05-22, 2017
 */


namespace Service\ThirdParty;

class ApiData
{

    const USER_CENTER_GAME_URL = "https://game.2345.com/server/wan/?c=Data&m=userCenterGames";
    const GET_GAME_LI_BAO = "https://libao.wan.2345.com/default/UserCenterReceivePackage";

    const USER_CENTER_VIDEO_URL = "https://kan.2345.com/moviecore/server/api/index.php?ctl=UserCenter";

    protected static function getApiUrl($url)
    {
        return \dev::getDevDomain($url);
    }

    /**
     * 获取游戏中心数据
     *
     * @return mixed
     */
    public function getUserCenterGames()
    {
        $accountStatCacheModel = new \Service\ThirdParty\Models\ApiDataCache();
        $data = $accountStatCacheModel->getUserCenterGames();
        if (!$data)
        {
            $params = array();
            $result = http_get(self::getApiUrl(self::USER_CENTER_GAME_URL), $params);
            $data = json_decode($result, true);
            if ($data && isset($data["status"]) && $data["status"] == 1)
            {
                $accountStatCacheModel->setUserCenterGames($data);
            }
            //todo add notice
        }

        if ($data && isset($data['status']) && $data['status'] == 1)
        {
            foreach ($data["data"]["liBao"] as $key => $item)
            {
                $data["data"]["liBao"][$key]["game_name"] = mb_convert_encoding($item["game_name"], "GBK", "UTF-8");
                $data["data"]["liBao"][$key]["url"] = $this->addPassportParams($item["url"]);
            }

            foreach ($data["data"]["lunBo"] as $key => $item)
            {
                $data["data"]["lunBo"][$key]["url"] = $this->addPassportParams($item["url"]);
            }
        }

        return $data;
    }

    /**
     * @param string $url url
     *
     * @return string
     */
    private function addPassportParams($url)
    {
        return strpos($url, "?") ? $url . "&f=passport" : $url . "?f=passport";
    }

    /**
     * 领取礼包
     * @param int $passid passid
     * @param int $gameId gameId
     *
     * @return mixed
     */
    public function getGameLiBao($passid, $gameId)
    {
        $params = array(
            'passId' => $passid,
            'gameId' => $gameId,
        );
        $result = http_post(self::getApiUrl(self::GET_GAME_LI_BAO), $params);
        $data = json_decode($result, true);
        /* todo add 状态
        if ($data && isset($data["status"]) && $data["status"] == 1)
        {

        }
        */

        return $data;
    }

    /**
     * 获取影视数据
     *
     * @return array
     */
    public function getUserCenterVideo()
    {
        $accountStatCacheModel = new \Service\ThirdParty\Models\ApiDataCache();
        $data = $accountStatCacheModel->getUserCenterVideo();
        $jsonData = "";
        if (!$data)
        {
            $params = array();
            $jsonData = http_get(self::getApiUrl(self::USER_CENTER_VIDEO_URL), $params);
            $data = json_decode($jsonData, true);
            if ($data && isset($data["code"]) && $data["code"] == 0)
            {
                foreach ($data["data"] as $key => $val)
                {
                    foreach ($val as $k => $v)
                    {
                        $data["data"][$key][$k]["score"] = "8." . (9 - $k);
                    }
                }

                $accountStatCacheModel->setUserCenterVideo($data);
            }
            //todo add notice
        }
        if (!$jsonData)
        {
            $jsonData = json_encode($data);
        }

        if ($data && isset($data['code']) && $data['code'] == 0)
        {
            array_walk_recursive($data, function (&$item, $key) {
                $item = mb_convert_encoding($item, "GBK", "UTF-8");
            });
        }

        foreach ($data["data"] as $key => $item)
        {
            foreach ($item as $ke => $val)
            {
                $data["data"][$key][$ke]["url"] = $this->addPassportParams($val["url"]);
            }
        }


        return array($data, $jsonData);
    }

}
