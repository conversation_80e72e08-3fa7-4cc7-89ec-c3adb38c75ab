<?php

namespace Crontab;
use Model;

class BingFaModel extends Model
{
    /**
     * 函数名称：__construct
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    public function getTelProject($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col} FROM `freecall_voice_code_config` ";


        if (!empty($data['channel']))
        {
            $where .= " channel = :channel AND";
            $paramArr[':channel'] = $data['channel'];
        }

        if (isset($data['number']))
        {
            $where .= " number = :number AND";
            $paramArr[':number'] = $data['number'];
        }
        
        if ( isset( $data['status']))
        {
            $where .= " `status` = :status AND";
            $paramArr[':status'] = $data['status'];
        }


        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by operate_time desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    public function addvcodeSipInfo($data)
    {
        $this->pdo->beginTransaction();
        $date = date("Ymd");
        $inserData = array(
            'num' => $data['num'],
            'date' => $date,
            'hour' => date("H"),
            'min' => date("i"),
            'addtime' => date('Y-m-d H:i:s'));
        $isTrue = $this->pdo->insert('phone_vcode_sip_info', $inserData);
        if ($isTrue !== false)
        {
            $lastVcodeId = $this->pdo->lastInsertId();
            foreach ($data['projectNums'] as $dataKey => $dataInfo)
            {
                $proData = array(
                    'pvsi_id' => $lastVcodeId,
                    'channel' => $dataKey,
                    'addtime' => date('Y-m-d H:i:s'),
                    'num' => $dataInfo,
                    'date' => $date);
                $isTrue = $this->pdo->insert('project_vcode_info', $proData);
            }
        }
        if ($isTrue !== false)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }

    }

    public function getVcodeSipInfo($date)
    {
        $sql = "SELECT SUM(`num`) AS `sum`, MAX(num) AS binfa FROM phone_vcode_sip_info where `date`=:date";
        $paramArr[':date'] = $date;
        return $this->pdo->find($sql, $paramArr);
    }

    public function getVcodeMaxDateInfo($date, $nums)
    {
        $sql = "SELECT `hour`,`min` FROM phone_vcode_sip_info WHERE `date`=:date AND `num`=:num LIMIT 0,20";
        $paramArr = array(':date' => $date, ':num' => $nums);
        return $this->pdo->findAll($sql, $paramArr);
    }

    public function getVoiceUseTotal($date, $isSms = 0, $isRang = '')
    {
        $sql = "SELECT COUNT(id) AS total FROM freecall_voice_code WHERE `date`=:date AND `is_sms`=:is_sms";
        $paramArr = array(':date' => $date, ':is_sms' => $isSms);
        if (!empty($isRang))
        {
            $sql .= ' AND is_rang=:is_rang ';
            $paramArr[':is_rang'] = $isRang;
        }
        return $this->pdo->find($sql, $paramArr);
    }

    public function addVoiceBingfaData($data)
    {
        $proData = array(
            'date' => $data['date'],
            'num' => $data['max'],
            'top_time' => $data['top_time'],
            'receive_percent' => $data['receive_percent']);
        $isTrue = $this->pdo->insert('voice_bingfa_data', $proData);
        return $isTrue === false ? false : true;
    }
    
    public function getProjectVcodeInfo($date)
    {
        $sql = "SELECT MAX(num) as pmax,channel FROM `project_vcode_info` WHERE `date` = :date GROUP BY channel";
        $paramArr = array(':date' => $date, ':date' => $date);
        return $this->pdo->findAll($sql, $paramArr);
    }
    
    public function delProjectVcodeInfo($date)
    {
        $condition = array(
            'where' => ' `date` = :date ',
            'params' => array(':date' => $date)
        );
        $isTrue = $this->pdo->delete('voice_bingfa_data',$condition);
        return $isTrue === false ? false : true;
    }
}
