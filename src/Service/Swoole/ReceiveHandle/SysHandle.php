<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/20
 * Time: 22:01
 */
namespace Service\Swoole\ReceiveHandle;

use Service\Func\ExceptionFunc;
use Service\Func\ServiceFunc;
use Service\Lib\Model\RedisHandle;
use Service\Swoole\Tool\Format;

class SysHandle
{
    /**
     * 指定操作
     * -
     * @param object $service 服务
     * @param object $fd 文件描述
     * @param string $reactor_id $reactor_id
     * @param string $data 数据
     * @return void
     */
    public function run($service, $fd, $reactor_id, $data)
    {
        if (method_exists($this, $data['cmd']))
        {
            $cmd = $data['cmd'];
            $this->$cmd($service, $fd, $reactor_id, $data);
        }
        else
        {
            $service->send($fd, Format::returnJson(-1, '未找到指定cmd'));
        }
    }

    /**
     * 当前进程状态
     * -
     * @param object $service 服务
     * @param object $fd 文件描述
     * @param string $reactor_id $reactor_id
     * @param string $data 数据
     * @return void
     */
    public function status($service, $fd, $reactor_id, $data)
    {
        unset($reactor_id);
        unset($data);
        $service->send($fd, Format::returnJson(1, '', $service->stats()));
    }

    /**
     * 重载worker
     * -
     * @param object $service 服务
     * @param object $fd 文件描述
     * @param string $reactor_id $reactor_id
     * @param string $data 数据
     * @return void
     */
    public function reload($service, $fd, $reactor_id, $data)
    {
        unset($reactor_id);
        unset($data);
        $service->reload();
        $service->send($fd, Format::returnJson(1, 'reload'));
    }

    /**
     * 关闭服务
     * -
     * @param object $service 服务
     * @param object $fd 文件描述
     * @param string $reactor_id $reactor_id
     * @param string $data 数据
     * @return void
     */
    public function shutdown($service, $fd, $reactor_id, $data)
    {
        unset($reactor_id);
        unset($data);
        $service->shutdown();
        $service->send($fd, Format::returnJson(1, 'shutdown'));
    }

    /**
     * 停止当前worker或者停止指定worker
     * -
     * @param object $service 服务
     * @param object $fd 文件描述
     * @param string $reactor_id $reactor_id
     * @param string $data 数据
     * @return void
     */
    public function stop($service, $fd, $reactor_id, $data)
    {
        if (empty($data['worker_id']) || empty($data['waitEvent']))
        {
            $data['worker_id'] = -1;
            $data['waitEvent'] = true;
        }
        unset($reactor_id);
        $service->stop($data['worker_id'], $data['waitEvent']);
        $service->send($fd, Format::returnJson(1, 'stop'));
    }

    /**
     * 停止当前worker或者停止指定worker
     * -
     * @param object $service 服务
     * @param object $fd 文件描述
     * @param string $reactor_id $reactor_id
     * @param string $data 数据
     * @return void
     */
    public function getConnectionCount($service, $fd, $reactor_id, $data)
    {
        unset($reactor_id);
        unset($data);
        $service->send($fd, Format::returnJson(1, '', ['count' => count($service->connections)]));
    }

    /**
     * 获取进程
     * -
     * @param object $service 服务
     * @param int $fd 文件描述
     * @param int $reactor_id 线程ID
     * @param array $data 数据
     * @return void
     */
    public function getSmsServicePs($service, $fd, $reactor_id, $data)
    {
        unset($reactor_id);
        unset($data);
        $process = new \swoole_process(function (\swoole_process $childProcess) {
            $param = [
                '-c',
                'ps aux |grep -E "smsSwooleService|SmsServiceController"|grep -v "grep"'
            ];
            $childProcess->exec('/bin/bash', $param);
            $childProcess->exit(0);
        }, true);
        $process->start(); // 启动子进程
        $process->setTimeout(3);
        $service->send($fd, Format::returnJson(1, '', ['msg' => $process->read()]));
    }


    /**
     * 获取僵尸进程
     * -
     * @param object $service 服务
     * @param int $fd 文件描述
     * @param int $reactor_id 线程ID
     * @param array $data 数据
     * @return void
     */
    public function getZombiePs($service, $fd, $reactor_id, $data)
    {
        unset($reactor_id);
        unset($data);
        \go(function () use ($service, $fd) {
            $ret = \Co::exec("ps -A -o stat,ppid,pid,cmd | grep -e '^[zZ]'");
            $service->send($fd, Format::returnJson(1, '', ['msg' => $ret]));
        });
    }
}
