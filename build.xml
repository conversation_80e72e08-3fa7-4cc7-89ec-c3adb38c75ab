<?xml version="1.0" encoding="UTF-8"?>
<project name="name-of-project" default="full-build">
    <property name="pdepend" value="${basedir}/vendor/bin/pdepend"/>
    <property name="phpcpd"  value="${basedir}/vendor/bin/phpcpd"/>
    <property name="phpcs"   value="${basedir}/vendor/bin/phpcs"/>
    <property name="phpdox"  value="${basedir}/vendor/bin/phpdox"/>
    <property name="phploc"  value="${basedir}/vendor/bin/phploc"/>
    <property name="phpmd"   value="${basedir}/vendor/bin/phpmd"/>
    <property name="phpunit" value="${basedir}/vendor/bin/phpunit"/>

    <target name="full-build"
            depends="prepare,static-analysis,phpdox"
            description="Performs static analysis, runs the tests, and generates project documentation"/>

    <target name="quick-build"
            depends="prepare,lint"
            description="Performs a lint check and runs the tests (without generating code coverage reports)"/>

    <target name="static-analysis"
            depends="lint,phploc-ci,pdepend,phpmd-ci,phpcs-ci,phpcpd-ci"
            description="Performs static analysis" />

    <target name="clean"
            unless="clean.done"
            description="Cleanup build artifacts">
        <delete dir="${basedir}/build/api"/>
        <delete dir="${basedir}/build/coverage"/>
        <delete dir="${basedir}/build/logs"/>
        <delete dir="${basedir}/build/pdepend"/>
        <delete dir="${basedir}/build/phpdox"/>
        <property name="clean.done" value="true"/>
    </target>
 
    <target name="composer" description="Installing composer dependencies">
        <exec executable="composer" failonerror="true">
            <arg value="require" />
            <arg value="phpunit/phpunit=*" />
        </exec>
        <exec executable="composer" failonerror="true">
            <arg value="require" />
            <arg value="squizlabs/php_codesniffer=*" />
        </exec>
        <exec executable="composer" failonerror="true">
            <arg value="require" />
            <arg value="phploc/phploc=*" />
        </exec>
        <exec executable="composer" failonerror="true">
            <arg value="require" />
            <arg value="pdepend/pdepend=*" />
        </exec>
        <exec executable="composer" failonerror="true">
            <arg value="require" />
            <arg value="phpmd/phpmd=*" />
        </exec>
        <exec executable="composer" failonerror="true">
            <arg value="require" />
            <arg value="sebastian/phpcpd=*" />
        </exec>
        <exec executable="composer" failonerror="true">
            <arg value="require" />
            <arg value="theseer/phpdox=*" />
        </exec>
    </target>

    <target name="prepare"
            unless="prepare.done"
            depends="clean,composer"
            description="Prepare for build">
        <mkdir dir="${basedir}/build/api"/>
        <mkdir dir="${basedir}/build/coverage"/>
        <mkdir dir="${basedir}/build/logs"/>
        <mkdir dir="${basedir}/build/pdepend"/>
        <mkdir dir="${basedir}/build/phpdox"/>
        <property name="prepare.done" value="true"/>
    </target>

    <target name="lint"
            unless="lint.done"
            description="Perform syntax check of sourcecode files">
        <apply executable="php" taskname="lint">
            <arg value="-l" />
            <fileset dir="${basedir}">
                <include name="**/*.php" />
                <exclude name="**/.svn/**"/>
                <exclude name="**/vendor/**"/>
                <exclude name="**/ipdb/**"/>
                <modified />
            </fileset>
        </apply>
        <property name="lint.done" value="true"/>
    </target>

    <target name="phploc"
            unless="phploc.done"
            description="Measure project size using PHPLOC and print human readable output. Intended for usage on the command line.">
        <exec executable="${phploc}" taskname="phploc">
            <arg value="--exclude" />
            <arg value="vendor" />
            <arg value="--exclude" />
            <arg value="ipdb" />
            <arg path="${basedir}" />
        </exec>
        <property name="phploc.done" value="true"/>
    </target>

    <target name="phploc-ci"
            unless="phploc.done"
            depends="prepare"
            description="Measure project size using PHPLOC and log result in CSV and XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${phploc}" taskname="phploc">
            <arg value="--exclude" />
            <arg value="vendor" />
            <arg value="--exclude" />
            <arg value="ipdb" />
            <arg value="--log-csv" />
            <arg path="${basedir}/build/logs/phploc.csv" />
            <arg value="--log-xml" />
            <arg path="${basedir}/build/logs/phploc.xml" />
            <arg path="${basedir}" />
        </exec>
        <property name="phploc.done" value="true"/>
    </target>
 
    <target name="phpcs"
            unless="phpcs.done"
            description="Find coding standard violations using PHP_CodeSniffer and print human readable output. Intended for usage on the command line before committing.">
        <exec executable="${phpcs}" taskname="phpcs">
            <arg value="--standard=/opt/lib/OSR/ruleset.xml" />
            <arg value="--extensions=php" />
            <arg value="--encoding=utf-8" />
            <arg value="--ignore=*/vendor/*,*/ipdb/*" />
            <arg path="${basedir}" />
        </exec>
        <property name="phpcs.done" value="true"/>
    </target>

    <target name="phpcs-ci"
            unless="phpcs.done"
            depends="prepare"
            description="Find coding standard violations using PHP_CodeSniffer and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${phpcs}" output="/dev/null" taskname="phpcs">
            <arg value="--report=checkstyle" />
            <arg value="--report-file=${basedir}/build/logs/checkstyle.xml" />
            <arg value="--standard=/opt/lib/OSR/ruleset.xml" />
            <arg value="--extensions=php" />
            <arg value="--encoding=utf-8" />
            <arg value="--ignore=*/vendor/*,*/ipdb/*" />
            <arg path="${basedir}" />
        </exec>
        <property name="phpcs.done" value="true"/>
    </target>

    <target name="pdepend"
            unless="pdepend.done"
            depends="prepare"
            description="Calculate software metrics using PHP_Depend and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${pdepend}" taskname="pdepend">
            <arg value="--jdepend-xml=${basedir}/build/logs/jdepend.xml" />
            <arg value="--jdepend-chart=${basedir}/build/pdepend/dependencies.svg" />
            <arg value="--overview-pyramid=${basedir}/build/pdepend/overview-pyramid.svg" />
            <arg value="--ignore=vendor/,ipdb/" />
            <arg path="${basedir}" />
        </exec>
        <property name="pdepend.done" value="true"/>
    </target>

    <target name="phpmd"
            unless="phpmd.done"
            description="Perform project mess detection using PHPMD and print human readable output. Intended for usage on the command line before committing.">
        <exec executable="${phpmd}" taskname="phpmd">
            <arg path="${basedir}" />
            <arg value="text" />
            <arg value="cleancode,codesize,controversial,design,naming,unusedcode" />
            <arg value="--exclude" />
            <arg value="vendor/,ipdb/" />
        </exec>
        <property name="phpmd.done" value="true"/>
    </target>

    <target name="phpmd-ci"
            unless="phpmd.done"
            depends="prepare"
            description="Perform project mess detection using PHPMD and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${phpmd}" taskname="phpmd">
            <arg path="${basedir}" />
            <arg value="xml" />
            <arg value="cleancode,codesize,controversial,design,naming,unusedcode" />
            <arg value="--exclude" />
            <arg value="vendor/,ipdb/" />
            <arg value="--reportfile" />
            <arg path="${basedir}/build/logs/pmd.xml" />
        </exec>
        <property name="phpmd.done" value="true"/>
    </target>

    <target name="phpcpd"
            unless="phpcpd.done"
            description="Find duplicate code using PHPCPD and print human readable output. Intended for usage on the command line before committing.">
        <exec executable="${phpcpd}" taskname="phpcpd">
            <arg value="--exclude" />
            <arg value="vendor" />
            <arg value="--exclude" />
            <arg value="ipdb" />
            <arg path="${basedir}" />
        </exec>
        <property name="phpcpd.done" value="true"/>
    </target>

    <target name="phpcpd-ci"
            unless="phpcpd.done"
            depends="prepare"
            description="Find duplicate code using PHPCPD and log result in XML format. Intended for usage within a continuous integration environment.">
        <exec executable="${phpcpd}" taskname="phpcpd">
            <arg value="--exclude" />
            <arg value="vendor" />
            <arg value="--exclude" />
            <arg value="ipdb" />
            <arg value="--log-pmd" />
            <arg path="${basedir}/build/logs/pmd-cpd.xml" />
            <arg path="${basedir}" />
        </exec>
        <property name="phpcpd.done" value="true"/>
    </target>

    <target name="phpdox"
            unless="phpdox.done"
            depends="phploc-ci,phpcs-ci,phpmd-ci"
            description="Generate project documentation using phpDox">
        <exec executable="${phpdox}" dir="${basedir}/build" taskname="phpdox"/>
        <property name="phpdox.done" value="true"/>
    </target>
</project>

