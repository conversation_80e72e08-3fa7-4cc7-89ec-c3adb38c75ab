<?php

/**
 * 默认控制器
 */
class DefaultController extends Controller
{

    /**
     * 默认方法
     */
    public function actionIndex()
    {
        \Service\Monitor\ApiCallHookMonitor::Filter();
        $loginAction = loadAction("login");
        if ($loginAction->checkAuthCookie())
        {
            redirect('/member/edit_info.php');
        }
        else
        {
            redirect('/login');
        }
    }

}
