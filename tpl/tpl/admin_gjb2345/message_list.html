<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gbk" />
<title>管理中心-2345网址导航高级版</title>

<link href="css/gaoji.css" rel="stylesheet" type="text/css" />
</head>

<body>

<!-- wrap -->
<div class="wrap">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td width="200" class="nav_border">
	<div class="left_nav">
    {{include file="admin_gjb2345/menu.html"}}
    </div></td>
    <td><div class="right_cnt">
<style>
*,body{ font-size:12px; }
.mytable{ background-color:#9cc; padding:0 0 0 0; text-align:center; padding:1px 1px 1px 1px;}
.mytable td {background-color:#FFF; padding:1px 1px 1px 1px;}
</style>
    <h4>通知列表</h4>
    <br />
        

	<div style="width:80%; margin-left:30px; border:1px solid #CCC;">
	<form action="" method="post">
	<p style="text-align: right;"><input type="text" name="title" />&nbsp;<input type="submit" name="s" value="搜索" /></p>
	</form>
	<form id="myForm" action="" method="post">
		<table class="mytable" border="0" cellspacing="1" cellpadding="0" width="100%">
		  <tr>
		  	<th width="5%">选择</th>
			<th>用户名</th>
			<th>标题</th>
			<th>通知状态</th>
			<th>发布人</th>
			<th>时间</th>
		  </tr>
{{foreach key=key item=item from=$pageArray.list}}
		  <tr>
		  	<td><input name="user_id[]" id="user_id" value="{{$item.id}}" type="checkbox" /></td>
			<td>{{$item.uname}}</td>
			<td><a href="message_edit.php?id={{$item.id}}">{{$item.title}}</a></td>
			<td>
			{{if $item.new==1}}未读{{else}}已读{{/if}}
			/{{if $item.del==0}}<a href="?action=del&id={{$item.id}}">未删</a>{{/if}}
			</td>
			<td>{{$item.cuser}}</td>
			<td>{{$item.times}}</td>
		  </tr>
{{/foreach}}
		</table>
		{{$pageArray.page.nowPage}} / {{$pageArray.page.totalPage}}
		<a href="{{$pageArray.page.startPage}}">第一页</a>
		<a href="{{$pageArray.page.backPage}}">上一页</a>
		<a href="{{$pageArray.page.nextPage}}">下一页</a>
		<a href="{{$pageArray.page.endPage}}">最后页</a>
		<p><input type="submit" name="cmd" value="批量删除" /></p>
		</form>
	</div>
	
{{include file="admin_gjb2345/footer.html"}}