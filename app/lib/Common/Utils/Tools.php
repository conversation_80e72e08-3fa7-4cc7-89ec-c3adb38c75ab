<?php

/**
 * Copyright (c) 2015,上海二三四五网络科技有限公司
 * 文件名称：tools.cls.php
 * 摘    要：工具类（修订版）
 * 作    者：张小虎 <<EMAIL>>
 * 修改日期：2015.09.17
 */
namespace Common\Utils;

class Tools
{

    /**
     * 系统操作windows 对话框
     * @param string $p_msg 显示对话内容
     * @param string $p_mode 转向状态控制 close:关闭窗口 ,back:返回上级页面 ,别的字符为转向该字符URL
     * @return void
     */
    public static function showMessage($p_msg, $p_mode)
    {
        ob_start();
        if ($p_mode == "close")
        {
            $cmd = "top.close();";
        }
        elseif ($p_mode == "back")
        {
            $cmd = "history.go(-1)";
        }
        else
        {
            $cmd = "location.href = '" . $p_mode . "';";
        }
        echo '<script language="javascript">
			<!--
				alert("' . $p_msg . '");' . $cmd . '
			-->
				</script>';
        ob_end_flush();
        die;
    }

    /**
     * cookie设置
     * @global array $_CFG 全局配置
     * @param string $p_ckName cookie名
     * @param string $p_ckVar cookie值
     * @param int $p_ckTime cookie作用时间
     * @return void
     */
    public static function setCookie($p_ckName, $p_ckVar, $p_ckTime)
    {
        global $_CFG;
        setcookie($p_ckName, $p_ckVar, $p_ckTime, $_CFG["COOKIE"]["PATH"], $_CFG["COOKIE"]["DOMAIN"]);
    }

    /**
     * 截取字串双字节处理函数
     * @param string $p_Str 字符串
     * @param int $p_Len 需截取字串长度
     * @param string $p_vStr 超出长度补位符，默认为 "..."
     * @return string
     */
    public static function subStr($p_Str, $p_Len, $p_vStr = "...")
    {
        if (strlen($p_Str) <= $p_Len)
        {
            return $p_Str;
        }
        $ls_Str = "";
        $xi = 0;
        while ($xi < $p_Len)
        {
            $ls_Str .= $p_Str[$xi];
            if (ord($p_Str[$xi]) > 127)
            {
                $xi ++;
                $ls_Str .= $p_Str[$xi];
            }
            $xi ++;
        }
        return $ls_Str . $p_vStr;
    }

    /**
     * 取出物理后缀名
     * @param string $p_fileName 物理文件名
     * @return string
     */
    public static function typeStr($p_fileName)
    {
        $num = strrpos($p_fileName, ".");
        return substr($p_fileName, $num + 1, strlen($p_fileName) - $num);
    }

    /**
     * 数组打包特定字符串
     * @param array $p_arrayRs PHP数组
     * @return string
     */
    public static function zipArray($p_arrayRs)
    {
        return base64_encode(serialize($p_arrayRs));
    }

    /**
     * 特定字符串还原数组
     * @param string $p_ArrayStr 特定字符串
     * @return array
     */
    public static function unZipArray($p_ArrayStr)
    {
        return unserialize(base64_decode($p_ArrayStr));
    }

    /**
     * html 双空格 回车 转换
     * @param string $p_str 需转化双空格回车 html字符串
     * @return string
     */
    public static function strToHtml($p_str)
    {
        return str_replace("  ", "&nbsp; ", nl2br($p_str));
    }

    /**
     * 对应JS中的escape函数,即将字符串转换成%+十六进制数
     * @param string $p_str 需要编码的字串
     * @return string
     */
    public static function phpEscape($p_str)
    {
        $sublen = strlen($p_str);
        $retrunString = "";
        for ($i = 0; $i < $sublen; $i++)
        {
            if (ord($p_str[$i]) >= 127)
            {
                $tmpString = bin2hex(iconv("gb2312", "ucs-2", substr($p_str, $i, 2)));
                //$tmpString=substr($tmpString,2,2).substr($tmpString,0,2); //window下可能要打开此项 
                $retrunString .= "%u" . $tmpString;
                $i ++;
            }
            else
            {
                $retrunString .= "%" . dechex(ord($p_str[$i]));
            }
        }
        return $retrunString;
    }

    /**
     * 对应JS中的unescape函数,%+十六进制数转换成中文字符
     * @param string $p_str 需要解码的字串
     * @return string
     */
    public static function phpUnescape($p_str)
    {
        $p_str = rawurldecode($p_str);
        preg_match_all("/%u.{4}|&#x.{4};|&#d+;|.+/U", $p_str, $r);
        $ar = $r[0];
        foreach ($ar as $k => $v)
        {
            if (substr($v, 0, 2) == "%u")
            {
                $ar[$k] = iconv("UCS-2", "GBK", pack("H4", substr($v, - 4)));
            }
            elseif (substr($v, 0, 3) == "&#x")
            {
                $ar[$k] = iconv("UCS-2", "GBK", pack("H4", substr($v, 3, - 1)));
            }
            elseif (substr($v, 0, 2) == "&#")
            {
                $ar[$k] = iconv("UCS-2", "GBK", pack("n", substr($v, 2, - 1)));
            }
        }
        return join("", $ar);
    }

    /**
     * 传回,开始至点号后第n个字符,字符串
     * @param string $p_str 带点字串
     * @param int $p_posNum 截取点后几位 默认1位
     * @return string
     */
    public static function getDotNStr($p_str, $p_posNum = 1)
    {
        $fStr = strpos($p_str, ".");
        if ($fStr)
        {
            return substr($p_str, 0, $fStr + 1 + $p_posNum);
        }
        else
        {
            return $p_str;
        }
    }



    /**
     * 创建目录
     * @param string $p_dirName 需创建的目录名
     * @param mix $p_mode 该目录权限,默认0777
     * @return boolean
     */
    public static function makeDir($p_dirName, $p_mode = 0777)
    {
        if (!is_dir($p_dirName))
        {
            return mkdir($p_dirName, $p_mode);
        }
    }

    /**
     * 写入文件
     * @param string $p_fileBody 文件内容
     * @param string $p_filePath 文件路径
     * @param string $p_mode 写入文件状态, fopen参数 , 默认w 重新写入
     * @return boolean
     */
    public static function writeFile($p_fileBody, $p_filePath, $p_mode = "w")
    {
        //打开文件清空
        $fRs = fopen($p_filePath, $p_mode);
        $htmlFile = fwrite($fRs, $p_fileBody);
        fclose($fRs);
        return true;
    }

    /**
     * 获取等比图片宽度高度
     * @param array $imgInfo 原图大小
     * @param int $simgW 最大宽度
     * @param int $simgH 最大高度
     * @return array
     */
    public static function getImgWH($imgInfo, $simgW, $simgH)
    {
        if ($imgInfo[0] == $imgInfo[1])
        {
            $imgInfo[1] = $simgH;
            $imgInfo[0] = $simgH;
        }
        if ($imgInfo[0] > $imgInfo[1])
        {
            if ($imgInfo[0] > $simgW)
            {
                $imgInfo[1] = floor($imgInfo[1] * ($simgW / $imgInfo[0]));
                $imgInfo[0] = $simgW;
            }
        }
        else
        {
            if ($imgInfo[1] > $simgH)
            {
                $imgInfo[0] = floor($imgInfo[0] * ($simgH / $imgInfo[1]));
                $imgInfo[1] = $simgH;
            }
        }
        return $imgInfo;
    }

    /**
     * 生成等比JPG缩图
     * @param string $imgName 图片句柄
     * @param array $imgInfo 原图参数
     * @param array $nImgInfo 新图参数
     * @param string $imgPath 新图路径
     * @return void
     */
    public static function createJPG($imgName, $imgInfo, $nImgInfo, $imgPath)
    {
        $im = @imagecreatefromjpeg($imgName);
        $ni = imagecreatetruecolor($nImgInfo[1], $nImgInfo[0]);
        imagecopyresampled($ni, $im, 0, 0, 0, 0, $nImgInfo[1], $nImgInfo[0], $imgInfo[1], $imgInfo[0]);
        ImageJpeg($ni, $imgPath, 999);
    }

    /**
     * 检察缓存文件有效期
     * @param string $p_fileName 缓存文件路径
     * @param int $p_caeTime 有效期时间截 (当前时间 减 文件缓存时间)
     * @return boolean
     */
    public static function checkCaeFile($p_fileName, $p_caeTime)
    {
        if (filemtime($p_fileName) > $p_caeTime)
        {
            return true;
        }
    }


    /**
     * 传回时间截
     * @param string $p_timeStr 标准时间格式 (2006-02-08 11:32)
     * @return string
     */
    public static function dateToTimeStr($p_timeStr)
    {
        $p_dateArray = explode(" ", $p_timeStr);
        $date1 = explode("-", $p_dateArray[0]);
        $date2 = @explode(":", $p_dateArray[1]);
        return mktime($date2[0], $date2[1], $date2[2], $date1[1], $date1[2], $date1[0]);
    }

    /**
     * 分页
     * @param int $total 总记录数
     * @param int $num 每页记录数
     * @param int $nowPage 当前页
     * @param string $pageName uri路径
     * @return string
     */
    public static function jumpPageFun($total, $num, $nowPage, $pageName)
    {
        if ($nowPage == "" or $nowPage <= 0)
        {
            $nowPage = 0;            //最小页
        }
        $pageToTal = ceil($total / $num);
        if ($nowPage > $pageToTal)
        {
            $nowPage = $pageToTal;       //最大页
        }
        //---------------------------------------------------------------------------------
        //get变量处理
        if (is_array($_GET))
        {
            $JsGet = '';
            foreach ($_GET as $key => $var)
            {
                if ($key != "p")
                {
                    $JsGet .= "&$key=$var";
                }
            }
        }
        //---------------------------------------------------------------------------------
        //跳转javascript
        $jumpPageVar['js'] = '
			<script language="JavaScript">
			<!--
			function changePage(jPageNum)
			{
				if(jPageNum<1)
				{
					alert("最小页码为 1.");
					return false;
				}
				if(jPageNum>' . $pageToTal . ')
				{
					alert("最大页码为 ' . $pageToTal . '.");
					return false;
				}
				if(jPageNum=="")
				{
					alert("请输入要跳转的页码..");
					return false;
				}
				location.href = "' . $pageName . '?p="+jPageNum+"' . $JsGet . '";
			}
			function newinfo_form_onkeyup(vNum)
			{
				key=window.event.keyCode;
				if(key==0xD)
				{
					changePage(vNum);
				}
			}
			-->
			</script>
			';
        //---------------------------------------------------------------------------------
        //选项菜单
        $jumpPageVar['menu'] .= "
			<input type=\"text\" name=\"classPageNum\" id=\"classPageNum\" style=\"border:1px solid #7D9CB9;\" size=\"2\" maxlength=\"4\" onKeyPress=\"newinfo_form_onkeyup(this.value);\">
			<input type=\"button\" name=\"goPage\" value=\"跳转\" onClick=\"changePage(document.all(" . "'classPageNum'" . ").value)\" class=\"sspu\">
			";
        $jumpPageVar['total'] = $total;
        $jumpPageVar['nowPage'] = $nowPage;
        $jumpPageVar['totalPage'] = $pageToTal;
        //首页
        $jumpPageVar['startPage'] = $pageName . "?p=1" . $JsGet;
        //上一页
        (($nowPage - 1) < 1) ? $backPage = 1 : $backPage = $nowPage - 1;
        $jumpPageVar['backPage'] = $pageName . "?p=" . $backPage . $JsGet;
        //下一页
        (($nowPage + 1) > $pageToTal) ? $nextPage = $pageToTal : $nextPage = $nowPage + 1;
        $jumpPageVar['nextPage'] = $pageName . "?p=" . $nextPage . $JsGet;
        //末页
        $jumpPageVar['endPage'] = $pageName . "?p=" . $pageToTal . $JsGet;
        //页码标记返回
        $jumpPageVar['s_tar'] = 1;
        $jumpPageVar['b_tar'] = $backPage;
        $jumpPageVar['n_tar'] = $nextPage;
        $jumpPageVar['e_tar'] = $pageToTal;
        return $jumpPageVar;
    }

}
