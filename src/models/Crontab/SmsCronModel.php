<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：SmsCronModel.php
 */

namespace cron;
use Model;

class SmsCronModel extends Model
{
    /**
     * 函数名称：__construct
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }
    
    
    /*
     * 插入退订数据
     */
    public function addSmsTdPhone($insertDate)
    {
        if (!empty($insertDate)) {
            return $this->pdo->insert('my_sms_td', $insertDate) ? $this->pdo->lastInsertId() : false;
        }
        
        return false;
    }
    
    
}


?>
