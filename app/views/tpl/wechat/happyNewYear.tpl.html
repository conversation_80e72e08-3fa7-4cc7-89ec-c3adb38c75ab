<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="gbk">
    <title>用户中心贺新春</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="/css/wechat/happNewYear/style.css?aaa">
</head>
<body style="background-color: #f14d4d">
<div class="newyear">
    <div class="tit">
        <img src="{{$pageArray.headimg}}" alt="用户头像"/>
        <p>尊敬的 <span class="username">{{$pageArray.nickname}}</span>新春快乐！</p>
        <span class="share">点击分享</span>
    </div>
    <div class="item1">
        <p>您今日还有<span class="num" id="lotteryNum">{{$pageArray.has_privilege}}</span>次抽奖机会</p>
    </div>
    <div class="item2">
        <div id="box" class="newyearBox">
            <div class="outer KinerLottery KinerLotteryContent"><img src="/images/wechat/happyNewYear/lanren.png"></div>
            <!-- 三种状态 no-start/start/completed-->
            <div class="inner KinerLotteryBtn start"></div>
        </div>
    </div>
    <div class="item3">
        <a  href="/wechat/HappyNewYear/bag"  class="HrefMyBag tit" onclick="cc('N2')">我的礼物背包&nbsp;&gt;&gt;</a>
        <div class="getGiftList">
            <div class="titBg">获奖名单</div>
            <div class="listBox" id="fxList">
                <ul class="list">
                    {{foreach from=$pageArray.md item=domain  key=key}}
                    <li>
                        <p>{{$domain}}</p>
                        <p>获得奖品</p>
                        {{if $key%6 eq 0}}
                        <p>kindle</p>
                        {{elseif $key%6 eq 1}}
                        <p>2345章鱼公仔</p>
                        {{elseif $key%6 eq 2}}
                        <p>电子贺卡</p>
                        {{elseif $key%6 eq 3}}
                        <p>纪念银币</p>
                        {{elseif $key%6 eq 4}}
                        <p>祝福短信</p>
                        {{elseif $key%6 eq 5}}
                        <p>手机指环</p>
                        {{/if}}
                    </li>
                    {{/foreach}}
                </ul>
            </div>
        </div>
    </div>
    <div class="item4">
        <div class="getGiftList">
            <div class="titBg">活动规则</div>
            <div class="rule">
                <p>1. 新用户关注2345用户中心微信公众号参与抽奖3次；</p>
                <p>2. 分享邀请好友成功关注2345用户中心微信公众号后，可再获得2次抽奖机会；</p>
                <p>3. 每日抽奖次数不超过9次；</p>
                <p>4. 活动时间：2月6日—2月10日，请在活动时间内完成收货地址信息的填写</p>
            </div>
        </div>
        <p class="explain">活动最终解释权归<br/>二三四五网络科技有限公司用户中心所有</p>
    </div>
</div>

<!-- 关注公众号二维码 -->
<div class="pop js_gz" style="display: none;">
    <div class="coverBg"></div>
    <div class="popBox ewm-pop ">
        <p class="info">长按识别二维码<br>关注“2345用户中心”后领取</p>
        <p class="pop-ewm"><img src="/images/wechat/happyNewYear/pop-ewm.png"></p>
        <a href="javascript:;" class="colsePop js_colsePop"></a>
    </div>
</div>

<!-- 抽中奖品 -->
<div class="pop js_getprize" style="display: none">
    <div class="coverBg"></div>
    <div class="prize popBox">
        <div class="title">获得<span class="name js_prizeName"></span></div>
        <div class="imgBg">
            <img src="" class="js_prizeImg" alt=""/>
        </div>
        <div class="btnBox">
            <a class="wBtn inner KinerLotteryBtn start" onclick="cc('N3')" >继续抽奖</a>
            <a class="oBtn" href="/wechat/HappyNewYear/bag" onclick="cc('N4')">查看背包</a>
        </div>
        <a href="javascript:;" class="colsePop js_colsePop"></a>
    </div>
</div>

<!-- 抽奖次数已用完 -->
<div class="pop js_zeroTime" style="display: none">
    <div class="coverBg"></div>
    <div class="time popBox">
        <div class="title">抽奖次数已用完</div>
        <p class="text"><span class="cOra">分享</span>给好友关注后可获得额外<br/>2次抽奖机会</p>
        <div class="btnBox">
            <a href="javascript:;" onclick="$(this).parents('.pop').hide();$('.js_shareR').show()" class="wBtn">分享出去</a>
            <a href="/wechat/HappyNewYear/bag" class="oBtn" onclick="cc('N4')">查看背包</a>
        </div>
        <a href="javascript:;" class="colsePop js_colsePop"></a>
    </div>
</div>

<!-- 点击分享按钮提示 -->
<div class="pop js_shareR" style="display: none;">
    <div class="coverBg"></div>
    <div class="shareBox">
        <!-- 打开右上角分享可分享给好友哦~ -->
        <span class="share">点击右上角可以分享给好友哦~</span>
    </div>
</div>

<!-- 活动未开始/已结束 -->
<div class="pop js_actTime" style="display: none">
    <div class="coverBg"></div>
    <div class="time popBox">
        <div class="title"></div>
        <p class="text"></p>
        <a href="javascript:;" class="redBtn js_colsePop">我知道了</a>
        <a href="javascript:;" class="colsePop js_colsePop"></a>
    </div>
</div>
<script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
<script src="/js/wechat/happyNewYear/zepto.min.js"></script>
<script src="/js/wechat/happyNewYear/kinerLottery.js"></script>
<script src="/js/wechat/happyNewYear/fx.js"></script>
<script>
    function cc(a) {
        var b = arguments,
            web = "ajax54",
            a2,
            i1 = document.cookie.indexOf("uUiD="),
            i2;
        if (b.length > 1)
            web = b[1];
        if (i1 != -1) {
            i2 = document.cookie.indexOf(";", i1);
            a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
        }
        if (!a2) {
            a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
            document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
        }
        if (a.length > 0) {
            var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
            $.get(c);
        }
        return true;
    }
    /**
     * 根据转盘旋转角度判断获得什么奖品
     * @param deg
     */
    var whichAward = function (deg) {
        if ((deg == 0)) {
            return "kindle";
        } else if ((deg == 60)) {
            return "章鱼公仔";
        } else if (deg == 120) {
            return "电子贺卡";
        } else if (deg == 180) {
            return "纪念银币";
        } else if (deg == 240) {
            return "祝福短信";
        } else if (deg == 300) {
            return "手机指环";
        }
    }
    var getReg = function (data) { //转动角度
        switch (data) {
            case 1:
                return 0;
            case 2:
                return 60;
            case 3:
                return 120;
            case 4:
                return 180;
            case 5:
                return 240;
            case 6:
                return 300;
        }
    }
    var KinerLottery = new KinerLottery({
        rotateNum: 8,
        body: "#box",
        direction: 1, //0为顺时针转动,1为逆时针
        disabledHandler: function (key) {
            switch (key) {
                case "noStart":
                    $(".js_actTime").show().find(".time .title").html("活动未开始!").next(".text").html("活动时间：2月6日~2月10日");
                    $(".js_actTime")
                    break;
                case "completed":
                    $(".js_actTime").show().find(".time .title").html("活动已结束!").next(".text").html("感谢您对本次活动的关注！");
                    break;
            }
        },
        clickCallback: function () { //点击按钮，请求数据
            cc('N1')
            $(".pop").hide();
            var _this = this
            $.ajax({
                type: "GET",
                url: "/wechat/HappyNewYear/apply",
                success: function (data) {
                    if (data.code != "400") {

                        $.post('/wechat/HappyNewYear/HasPrivilege', {unionid: "{{$pageArray.unionid}}"}, function (ret) {
                            if(ret.code==-1){
                                $(".js_actTime").show().find(".time .title").html("活动已结束!").next(".text").html("感谢您对本次活动的关注！");
                                return false
                            }
                            ret = ret.data;
                            var lotteryNum = ret.num; // 接口数据，抽奖次数
                            lotteryNum=lotteryNum>=0?lotteryNum:0

                            var sj=(lotteryNum>ret.maxCount?ret.maxCount:lotteryNum);

                            $("#lotteryNum").html(sj);
                            if (lotteryNum >= 0 && ret.lucklyDog > 0) {
                                var data = ret.lucklyDog; // 接口数据，中奖号码

                                var reg = getReg(data);
                                storage = window.localStorage;
                                storage.setItem("reg", reg);
                                storage.setItem("dataId", data);

                                _this.goKinerLottery(reg);
                            }else if(ret.maxCount <= 0){
                                $(".js_actTime").show().find(".time .title").html("尊敬的用户:").next(".text").html("您今日已达到最大抽奖次数！");
                            } else if (ret.num < 0) {
                                $(".js_zeroTime").show();
                            } else {
                                $(".js_actTime").show().find(".time .title").html("活动已结束!").next(".text").html("感谢您对本次活动的关注！");

                            }
                        })

                    } else {
                        $(".js_gz").show();
                    }
                }
            });


        },
        KinerLotteryHandler: function (getReg) {
            //alert(whichAward(storage.getItem("reg")));
            $(".js_getprize").show();
            $(".js_prizeName").html(whichAward(storage.getItem("reg")));
            $(".js_prizeImg").attr('src', '/images/wechat/happyNewYear/gift' + storage.getItem("dataId") + '.png');

        }
    });

    // 关闭弹窗
    $(".js_colsePop").on("touchend", function () {
       cc('N5')
        $(this).parents(".pop").hide();
    });

    //分享弹窗，触摸灰色层关闭
    $(".js_shareR").on("touchend", function () {
        $(this).hide();
    });
</script>

<script>
    $(function () {
        $("#fxList").textSlider({
            speed: 40, //数值越大，速度越慢
            line: 10    //触摸翻滚的条数
        });
    });

    /*
    * 表格滚动
    */
    (function ($) {
        $.fn.textSlider = function (options) {
            //默认配置
            var defaults = {
                speed: 40,
                line: 1
            };

            var opts = $.extend({}, defaults, options);

            var $timer;

            function marquee(obj, _speed) {
                var top = 0;
                var margintop;
                $timer = setInterval(function () {
                    top++;
                    margintop = 0 - top;
                    obj.find("ul").animate({
                        marginTop: margintop
                    }, 0, function () {
                        var s = Math.abs(parseInt($(this).css("margin-top")));
                        if (s >= 28) {
                            top = 0;
                            $(this).css("margin-top", 0);
                            $(this).find("li").slice(0, 1).appendTo($(this));
                        }
                    });
                }, _speed);
            }

            this.each(function () {
                var speed = opts["speed"], line = opts["line"], _this = $(this);
                var $ul = _this.find("ul");
                if ($ul.height() > _this.height()) {
                    marquee(_this, speed);
                }

                //触摸开始
                _this.on('touchend', function (ev) {
                    ev.preventDefault();
                    clearInterval($timer);
                });

                //向上滑动
                _this.on('swipeup', function (ev) {
                    ev.preventDefault();
                    clearInterval($timer);
                    if ($ul.height() > _this.height()) {
                        for (i = 0; i < opts.line; i++) {
                            $ul.find("li").first().appendTo($ul);
                        }
                        $ul.css("margin-top", 0);
                    }
                });

                //向下滑动
                _this.on('swipedown', function (ev) {
                    ev.preventDefault();
                    clearInterval($timer);
                    if ($ul.height() > _this.height()) {
                        for (i = 0; i < opts.line; i++) {
                            $ul.find("li").first().before($ul.find("li").last());
                        }
                        $ul.css("margin-top", 0);
                    }
                });

                //触摸结束
                _this.on('touchend', function (ev) {
                    ev.preventDefault();
                    if ($ul.height() > _this.height()) {
                        marquee(_this, speed);
                    }
                });
            });
        }
    })(Zepto);
</script>

<script>
    wx.config({
        debug: false,
        appId: '{{$pageArray.appid}}',
        timestamp:'{{$pageArray.signUse.timestamp}}',
        nonceStr: '{{$pageArray.signUse.noncestr}}',
        signature: '{{$pageArray.signRes}}',
        jsApiList: ['onMenuShareTimeline','onMenuShareAppMessage','hideMenuItems']
    });
    wx.ready(function () {
        wx.onMenuShareTimeline({
            title: '2345用户中心贺新春：新年拼手气 大奖抽不停',
            link: '{{$pageArray.url}}?shareUrl={{$pageArray.shareUrl}}',
            imgUrl: 'https://passport.2345.com/images/wechat/happyNewYear/shareYou.png',
            success: function () {
            }
        });
        wx.hideMenuItems({
            menuList: ['menuItem:share:timeline','menuItem:originPage','menuItem:copyUrl','menuItem:openWithSafari','menuItem:share:qq','menuItem:share:QZone']
        });
        wx.onMenuShareAppMessage({
            title: '2345用户中心贺新春：新年拼手气 大奖抽不停',
            link: '{{$pageArray.url}}?shareUrl={{$pageArray.shareUrl}}',
            desc: '关注2345用户中心微信二维码、大奖等你来拿',
            imgUrl: 'https://passport.2345.com/images/wechat/happyNewYear/shareYou.png',
            success: function () {
            }
        });
    });
//    $('.HrefMyBag').on('touchend',function () {
//        $(this).attr('href','/wechat/HappyNewYear/bag');
//    });
</script>
</body>
</html>
