<html xmlns:o="urn:schemas-microsoft-com:office:office"  xmlns:x="urn:schemas-microsoft-com:office:excel"  xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta http-equiv="expires" content="Mon, 06 Jan 1999 00:00:01 GMT">
    <meta http-equiv=Content-Type content="text/html; charset=gb2312">
    <!--[if gte mso 9]><xml> <x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name></x:Name><x:WorksheetOptions><x:DisplayGridlines/>
</x:WorksheetOptions></x:ExcelWorksheet> </x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]-->
</head>
<body>
<table border="1" cellspacing="0" cellpadding="0" width="100%">
	<tr style="text-align: center;">
		<th width="10%" rowspan="2">日期</th>
		{{foreach item=domains key=groupKey from=$pageArray.domainGroups name=domainGroups}}
		<th{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:greenyellow"{{/if}} colspan="{{$domains|@count}}">{{$groupKey}}</th>
		{{/foreach}}
	</tr>
	<tr style="text-align: center;">
		{{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
		{{foreach item=domain_name key=domain from=$domains}}
		<th{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:greenyellow"{{/if}}>
		<span title="{{$domain}}">{{if $domain_name}}{{$domain_name}}{{else}}{{$domain}}{{/if}}</span>
		</th>
		{{/foreach}}
		{{/foreach}}
	</tr>
	{{foreach item=item key=date from=$pageArray.data}}
	<tr style="text-align: center">
		<td rowspan="1">{{$date}}</td>
		{{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
		{{foreach item=domain_name key=domain from=$domains}}
		<td{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:greenyellow"{{/if}}>
            <span style="color:blue;">{{$item.succ.$domain|default:0}}</span>
		</td>
		{{/foreach}}
		{{/foreach}}
	</tr>
	{{/foreach}}

</table>
</body>
</html>