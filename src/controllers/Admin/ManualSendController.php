<?php

namespace Admin;

use Controller;

class ManualSendController extends Controller
{
    /**
     * @return void
     */
    public function actionIndex()
    {
        loadView('Admin/manual_send.tpl.html', [], false, ['[|', '|]']);
    }

    /**
     * @return void
     */
    public function actionApi()
    {
        $func = $_GET['func'] ?? '';
        if (method_exists($this, $func))
        {
            echo $this->$func();
        }
        else
        {
            echo json_encode(['err' => -999, 'msg' => "Method '{$func}' not exists"]);
        }
    }

    /**
     * @return void
     */
    public function actionUpload()
    {
        if (!is_array($_FILES) || count($_FILES) === 0)
        {
            echo json_encode(['err' => -1, 'msg' => 'no file input']);
            return;
        }
        $file = $_FILES['file'];
        if ($file['error'] !== 0 || $file['size'] > 2 * 1000 * 1000)
        {
            echo json_encode(['err' => -1, 'msg' => 'size should within 2MB']);
            return;
        }
        if (
            ($file['type'] !== 'text/csv' && $file['type'] !== 'application/vnd.ms-excel') ||
            substr($file['name'], -4, 4) !== '.csv'
        ) {
            echo json_encode(['err' => -1, 'msg' => 'mine type should be text/csv']);
            return;
        }
        $res = [];
        $content = file_get_contents($file['tmp_name']);
        $content = \EncodingAction::convertEncoding($content, 'utf-8');
        $lines = explode("\n", $content);
        $linesCount = count($lines);
        if ($linesCount > 10000)
        {
            echo json_encode(['err' => -1, 'msg' => \EncodingAction::convertEncoding('短信条数最多1w条', 'utf-8')]);
            return;
        }
        if ($linesCount < 5)
        {
            echo json_encode(['err' => -1, 'msg' => \EncodingAction::convertEncoding('短信条数最少5条', 'utf-8')]);
            return;
        }
        $phoneDict = [];
        foreach ($lines as $idx => $line)
        {
            $patternChina = '%(^1[3-9][\d]{9})%';
            $patternGlobal = '%(^00[\d]*$)%';
            $item = explode(',', $line);
            $item[0] = trim($item[0]);
            if (count($item) > 2)
            {
                echo json_encode(['err' => -1, 'msg' => 'invaild array size at line ' . $idx]);
                return;
            }
            if ((strlen($item[0]) !== 11 && !preg_match($patternChina, $item[0])) && !preg_match($patternGlobal, $item[0]))
            {
                echo json_encode(['err' => -1, 'msg' => 'invaild phone number: ' . $item[0] . ' at line' . $idx]);
                return;
            }
            if (isset($phoneDict[$item[0]]))
            {
                echo json_encode(['err' => -1, 'msg' => 'duplicated phone number: ' . $item[0] . ' at line' . $idx]);
                return;
            }

            if (isset($item[1]) && strpos($item[1], '|') !== false)
            {
                echo json_encode(['err' => -1, 'msg' => 'invaild char "|" at line ' . $idx]);
                return;
            }
            $phoneDict[$item[0]] = 1;
            $res[] = $item;
        }
        echo json_encode(['err' => 0, 'msg' => 'ok', 'data' => $res]);
    }

    /**
     * @return void
     */
    public function actionDownload()
    {
        $type = $_GET['type'];
        if ($type === 'normal')
        {
            $content = <<<CSV
13800013800
18812312345
13845645678
13812345678
13823452345
CSV;
            $name = 'manualSend_example.csv';
        }
        elseif ($type === 'batch')
        {
            $content = <<<CSV
13800013800,这显然是第一条测试短信
18812312345,这也许是第二条测试短信
13845645678,这大概是第三条测试短信
13812345678,这难道是第四条测试短信
13823452345,编这个我都快精神分裂了
CSV;
            $name = 'manualSendBatch_example.csv';
        }
        else
        {
            exit;
        }
        $size = strlen($content);
        header("Accept-Ranges:bytes");
        header('cache-control:public');
        header('content-type:text/csv');
        header('content-disposition:attachment;filename=' . $name);
        header('HTTP/1.1 200 OK');
        header('content-length:' . $size);
        echo $content;
    }

    /**
     * @return string
     */
    private function candidateOperator()
    {
        $phone = $_POST['phone'];
        $type = $_POST['type'];
        $getRedisAction = \GetRedisAction::getInstance();
        if (count($phone) === 1)
        {
            if (\FuncAction::isMPhone($phone[0]))
            {
                $operatorPrefix = 'mobile';
            }
            elseif (\FuncAction::isChinaTelecomPhone($phone[0]))
            {
                $operatorPrefix = 'telecom';
            }
            elseif (\FuncAction::isChinaUnicomPhone($phone[0]))
            {
                $operatorPrefix = 'unicom';
            }
            else
            {
                return json_encode(['err' => -1, 'msg' => 'invaild phone number']);
            }
        }
        else
        {
            $patternChina = '%(^1[3-9][\d]{9})%';
            $patternGlobal = '%(^00[\d]*$)%';
            foreach ($phone as $p)
            {
                if ((strlen($p) !== 11 && !preg_match($patternChina, $p)) && !preg_match($patternGlobal, $p))
                {
                    return json_encode(['err' => -1, 'msg' => 'invaild phone number']);
                }
            }
            $operatorPrefix = 'mobile';
        }
        $smsConfigs = $getRedisAction->getSmsTypeConfig($type);
        $operatorIds = [];
        foreach ($smsConfigs as $smsConfig)
        {
            $operatorIds[$smsConfig[$operatorPrefix . '_provider_id']] = $smsConfig['percent'];
        }
        $operatorConfigModel = OperatorConfigModel::getInstance();
        $operatorStr = '';
        foreach ($operatorIds as $operatorId => $percent)
        {
            $operatorInfo = $operatorConfigModel->getOperatorList(['id' => $operatorId, 'row' => 1]);
            if (count($operatorIds) === 1)
            {
                $operatorStr = $operatorInfo['id'] . '-' . \EncodingAction::transcoding($operatorInfo["provider_name"], 'utf-8');
            }
            else
            {
                $operatorStr .= "{$percent}% {$operatorInfo['id']}-" . \EncodingAction::transcoding($operatorInfo["provider_name"], 'utf-8') . "\n";
            }
        }
        $operatorStr = trim($operatorStr, "\n");
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => $operatorStr]);
    }

    /**
     * @return string
     */
    private function projectList()
    {
        //$projectModel = \Admin\ProjectModel::getInstance();
        //$projectList = $projectModel->getProjectList('', []);
        $userAuthModel = UserAuthModel::getInstance();
        $uid = UserAction::getAdminUid();
        $projectList = $userAuthModel->getUserProjectList($uid);
        $projects = [];
        foreach ($projectList as $projectInfo)
        {
            if ($projectInfo['status'] == 1)
            {
                $projects[] = [
                    'value' => (int)$projectInfo['pid'],
                    'label' => $projectInfo['pid'] . '-' . \EncodingAction::transcoding($projectInfo["project_name"], 'utf-8'),
                ];
            }
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($projects)]);
    }

    /**
     * @return string
     */
    private function typeList()
    {
        $smsTypes = [];
        $smsTypeModel = \Admin\SmsTypeModel::getInstance();
        $smsTypeList = $smsTypeModel->getSmsTypeList();
        if (!empty($_POST['project']))
        {
            $smsProjectModel = SmsProjectModel::getInstance();
            $projectInfo = $smsProjectModel->getPojectList(['pid' => $_POST['project'], 'row' => true]);
            if (empty($projectInfo))
            {
                return json_encode(['err' => -1, 'msg' => 'no such project']);
            }
            $projectSmsTypeList = $smsProjectModel->getPojectSmsTypeList(['pid' => $_POST['project']]);
            foreach ($projectSmsTypeList as $projectSmsTypeKey => $projectSmsTypeInfo)
            {
                foreach ($smsTypeList as $smsTypeKey => $smsTypeInfo)
                {
                    if ($smsTypeInfo['id'] === $projectSmsTypeInfo['tid'])
                    {
                        $smsTypes[] = [
                            'value' => (int)$smsTypeInfo['sms_type'],
                            'label' => $smsTypeInfo['sms_type'] . '-' . \EncodingAction::transcoding($smsTypeInfo["sms_name"], 'utf-8'),
                        ];
                        break;
                    }
                }
            }
        }
        else
        {
            foreach ($smsTypeList as $smsTypeKey => $smsTypeInfo)
            {
                $smsTypes[] = [
                    'value' => (int)$smsTypeInfo['sms_type'],
                    'label' => $smsTypeInfo['sms_type'] . '-' . \EncodingAction::transcoding($smsTypeInfo["sms_name"], 'utf-8'),
                ];
            }
        }

        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($smsTypes)]);
    }

    /**
     * @return string
     */
    private function positionList()
    {
        $position = [];
        $smsProjectModel = SmsProjectModel::getInstance();
        if (!empty($_POST['project']))
        {
            $projectInfo = $smsProjectModel->getPojectList(['pid' => $_POST['project'], 'row' => true]);
            if (empty($projectInfo))
            {
                return json_encode(['err' => -1, 'msg' => 'no such project']);
            }
            $positionList = $smsProjectModel->getPojectPositionList(['pid' => $_POST['project']]);
        }
        else
        {
            $positionList = $smsProjectModel->getPojectPositionList([]);
        }
        foreach ($positionList as $k => $v)
        {
            $position[] = [
                'value' => (int)$v['pl_id'],
                'label' => $v['pl_id'] . '-' . \EncodingAction::transcoding($v["position_name"], 'utf-8'),
            ];
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($position)]);
    }
}
