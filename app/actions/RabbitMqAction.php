<?php

/**
 * Class ImageUploadAction
 */

use Service\Amqp\Amqp;

class RabbitMqAction extends Action
{
    /**
     * 广播用户更新数据
     * 注意：如果mid绑定了两个group，会接收两个exchange的广播数据
     *
     * @param int    $passid     用户id
     * @param string $mid        项目名用于设置queue名
     * @param string $group      生态名用于设置exchange名
     * @param array  $updateInfo 更新内容：avatar 头像 nickName 昵称
     *
     * @return bool 发送结果
     * @throws Exception
     */
    public static function broadcastMsg (int $passid, string $mid, string $group, array $updateInfo = ['avatar', 'nickName'])
    {
        // 必填校验
        if (empty($mid) || empty($group) || empty($updateInfo)) {
            return false;
        }
        // 项目是否开启推送
        if (!in_array($mid, Config::get('rabbitMq')['notice_used_mid'], true)) {
            return true;
        }

        // 数据组装
        $noticeA = [
            'requestTime' => time(),
            'passid' => $passid,
            'mid' => $mid,
            'notice' => $updateInfo,
        ];
        $noticeA['sign'] = self::dataSign($noticeA);

        // 更新推送
        try {
            $pushData = json_encode($noticeA);
            $mqConfig = Config::get('rabbitMq');
            $pushTimeStart = mSecTime();
            $objAmqp = Amqp::getInstance($mqConfig['default']);
            $ret = $objAmqp->putMsg($pushData, $mid, $group, AMQP_EX_TYPE_FANOUT);
            $pushTimeUsed = mSecTime() - $pushTimeStart;
            $mqLogData = [
                'mid' => $mid,
                'passid' => $passid,
                'class' => __CLASS__,
                'method' => __METHOD__,
                'line' => __LINE__,
                'used_time' => $pushTimeUsed,
                'group' => $group,
                'level' => 'info',
                'dir_type' => 'p_rabbitMqAction',
            ];
        } catch (Exception $e) {
            // 推送失败记录日志上报统一日志
            $mqLogData = [
                'level' => 'error',
                'dir_type' => 'e_rabbitMqAction',
                'error_code' => $e->getCode(),
                'error_msg' => $e->getMessage(),
                'mid' => $mid,
                'group' => $group,
                'passid' => $passid,
                'push_data' => $pushData,
                'class' => __CLASS__,
                'method' => __METHOD__,
                'line' => __LINE__,
            ];
        }
        loadAction('Warning')->record($mqLogData);

        return $ret;
    }

    /**
     * 数据加签
     *
     * @param array $data 待验签数据
     *
     * @return string 数据签名
     */
    private static function dataSign (array $data)
    {
        $signKey = Config::get('rabbitMq')['sign_key'];
        ksort($data);
        $signStr = md5(http_build_query($data) . $signKey);

        return $signStr;
    }
}
