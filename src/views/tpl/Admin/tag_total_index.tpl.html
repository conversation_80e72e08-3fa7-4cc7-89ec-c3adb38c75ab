<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务平台-标记统计</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a>  >  <span>标记统计</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a href="/Admin/SmsTotal/Index">按日短信统计</a>
        <a class="currTabMenu" href="/Admin/TagTotal/Index">用户中心发送量</a>
        <a href="/Admin/TagTotal/TagPostionTotal">用户中心发送细分</a>
        <a href="/Admin/SmsTotal/stat">按月短信统计</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->

    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="/Admin/TagTotal/Index" method="post" id="queryFormDiv">
            <div class="dataWrap clearfix">
            	<em>日期：</em>
                <div class="dataList w130">
                	<div class="getTime"><input type="text" value="{{$pageArray.startTime}}" autocomplete="off" name="startTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->
            <span class="dataTxt">至</span>
            <div class="dataWrap clearfix">
                <div class="dataList w130">
                	<div class="getTime"><input type="text" value="{{$pageArray.endTime}}" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->
            <a href="javascript:onclick=chooseMonth(1);" class="styleBtn_s styleBtnBlue left ml10 ">当月</a>
            <a href="javascript:onclick=chooseMonth(2);" class="styleBtn_s styleBtnBlue left ml10 ">上月</a>
            <div class="dataWrap clearfix ml20">
                <input type="checkbox" name="useCache" id="useCache" value="1" {{if $pageArray.useCache}}checked{{/if}}/>
                <!--本方法用于处理浏览器自带serialize方法处理radio和checkbox时的不选中无提交现象-->
                <input type="hidden" value="1" name="fromClick"/>
                <label for="useCache">使用缓存</label>
            </div>
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 querySubmit">查询</a>
            <!--<a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 exportChannelData">导出</a>-->
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->

    <!--主体内容 开始-->
    <div class="contentWrap" style="overflow-x: scroll;">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
                    <tr>
                        <th style="width: 100px">日期</th>
                        {{foreach from=$pageArray.tagList item=tagListInfo}}
                            <th style="width: 60px">{{$tagListInfo}}</th>
                        {{/foreach}}
                        <th style="width: 60px">总计</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->

        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
            <tr>
                <th style="width: 100px">日期</th>
                {{foreach from=$pageArray.tagList item=tagListInfo}}
                    <th style="width: 60px">{{$tagListInfo}}</th>
                {{/foreach}}
                <th style="width: 60px">总计</th>
            </tr>
            {{if !empty($pageArray.tagTotalList)}}
            {{foreach from=$pageArray.tagTotalList key=tagTotalDate item=tagTotalInfo}}
                <tr>
                    <td {{if $tagTotalInfo.useCache eq 1}} class="lightYellowBg"{{/if}}>{{$tagTotalDate}}</td>
                    {{foreach from=$pageArray.tagList key=tagListKey item=tagListInfo}}
                        {{if isset($tagTotalInfo[$tagListKey].total)}}<td>{{$tagTotalInfo[$tagListKey].total}}</td>{{else}}<td>-</td>{{/if}}
                    {{/foreach}}
                    <td>{{$pageArray.total[$tagTotalDate]}}</td>
                </tr>
            {{/foreach}}
            <tr>
                <td></td>
                {{foreach from=$pageArray.tagList key=tagTotalDate item=tagListInfo}}
                    {{if isset($pageArray.totalCount[$tagTotalDate])}}<td>{{$pageArray.totalCount[$tagTotalDate]}}</td>{{else}}<td>-</td>{{/if}}
                {{/foreach}}
                <td>{{$pageArray.totalCount.total}}</td>
            </tr>
            {{/if}}
        </table>
        <!--table数据列表 结束-->
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->
<script src="/style/js/jquery-1.8.3.min.js"></script>
<script src="/style/js/tableFixed.js"></script>
<script>
    function chooseMonth(month) {
        month = parseInt(month);
        var date = new Date();
        var year = date.getFullYear();
        var thisMonth = date.getMonth() + 1;
        var lastMonth =  thisMonth - 1;
        var yesterday = date.getDate() - 1;
        switch (month)
        {
            //当月startTime endTime
            case 1:
                if (thisMonth < 10) {
                    thisMonth = '0' + thisMonth;
                }
                if (yesterday < 10) {
                    yesterday = '0' + yesterday;
                }
                var start = year + '-' +  thisMonth + '-01';
                var end = year + '-' +  thisMonth + '-' + yesterday;
                break;
            //上月
            case 2:
                var lastDay = new Date(year, lastMonth, 0).getDate();
                if (lastMonth < 10) {
                    lastMonth = '0' + lastMonth;
                }
                var start = year + '-' +  lastMonth + '-01';
                var end = year + '-' +  lastMonth + '-' + lastDay;
                break;
        }
        $("input[name='startTime']").val(start);
        $("input[name='endTime']").val(end);
    }

    $('.querySubmit').click(function () {
        $('#queryFormDiv').submit();
    });

    $('.exportChannelData').click(function () {
        var link = '/Admin/TagTotal/Index?act=export&' + $('#queryFormDiv').serialize();
        window.open(link);
    });
</script>
</body>
</html>