<?php

namespace SmsInterface;
class SmsChannelAction extends \Action
{
    /**
     * 检查退订
     * */
    public static function checkTdTag($content)
    {
        $filterArr = array(
            't',
            'td',
            '退',
            '退订');
        $isUnsub = false;
        foreach ($filterArr as $filterArrInfo)
        {
            $unsubStatus = stripos($content, $filterArrInfo);
            if ($unsubStatus !== false)
            {
                $isUnsub = true;
                continue;
            }
        }
        return $isUnsub;
    }
    
    /**
     * 
     * */
    public static function microtime()
    {
        return microtime(true) * 10000;
    }
    
    /**
	 * 生成随机数
	 * @return 
	 */
    public static function _getRandom()
	{
		for ($i = 0; $i < 4; $i++) {
			
			$rand .=  (string)mt_rand(0, 9);;
		}
		return $rand;
	}
}
