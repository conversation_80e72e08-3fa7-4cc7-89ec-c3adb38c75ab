<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/10/11
 * Time: 14:58
 */
namespace Service\Monitor;

class ApiCallHookMonitor
{

    private static $expireDate = 259200;


    /**
     * 获取每天的访问统计KEY
     * -
     * @return string
     */
    public static function getApiDateCallKey()
    {
        return 'APICALLED:' . date('Ymd');
    }

    /**
     * 需要统计的列表
     * 在写目录的时候，请小写  ,一定按照标准的API地址写，否则统计不到
     * [
     *    'browser/' => [],
     *    'browser2/' => [],
     *    'browser3/' => [],
     *    'chrome/' => [],
     *    'AllLogin' => [
     *         'Check'
     *     ],
     *  ];
     * -
     * @return array
     */
    private static function getTotalList()
    {
        return [
            'browser/' => [],
            'browser2/' => [],
            'browser3/' => [],
            'chrome/' => [],
            'mbrowser/' => [],
            'mobile/' => [],
            'oauth/' => [],
        ];
    }


    /**
     * 统计有那些接口调用
     * -
     * @return void
     */
    public static function writeAccessNum()
    {
        try
        {
            $directory = strtolower(\Router::fetchDirectory());
            $className = substr(\Router::fetchClass(), 0, -10); //替换controller
            $methodName = substr(\Router::fetchMethod(), 6, strlen(\Router::fetchMethod())); //action;
            $totalList = self::getTotalList();
            //如果访问的目录存在则进行统计
            if (isset($totalList[$directory]))
            {
                //如果没有设置统计的Controller，则统计整个目录API地址
                if (empty($totalList[strtolower($directory)]))
                {
                    self::set($directory, $className, $methodName);
                }
                else
                {
                    //如果指定了统计的Controller
                    if (isset($totalList[$directory][$className]))
                    {
                        //如果没有指定具体的action  就统计整个受访问的action
                        if (empty($totalList[$directory][$className]))
                        {
                            self::set($directory, $className, $methodName);
                        }
                        else
                        {
                            //如果指定具体的action 则进行统计具体的地址
                            if (in_array($methodName, $totalList[$directory][$className]))
                            {
                                self::set($directory, $className, $methodName);
                            }
                        }
                    }
                }
            }
        }
        catch (\Exception $exception )
        {
            $getError = $exception->getMessage() . PHP_EOL;
            $getError .= $exception->getCode() . PHP_EOL;
            $getError .= $exception->getFile() . PHP_EOL;
            $getError .= $exception->getLine() . PHP_EOL;
            xLog('apiCallHook', 'apiCallHook', $getError);
        }
    }

    /**
     * 设置访问统计
     * -
     * @param string $directory 目录
     * @param string $className 类
     * @param string $methodName 方法
     * @return void
     */
    private static function set($directory, $className, $methodName)
    {
        $uri = '/' . $directory . $className . '/' . $methodName;
        loadAction('LoadRedis');
        $redis = \LoadRedisAction::getInstance('dlx_log', 'hash1');
        $redis->hIncrBy(self::getApiDateCallKey(), $uri, 1);
        if ($redis->ttl(self::getApiDateCallKey()) < 0)
        {
            $redis->expire(self::getApiDateCallKey(), self::$expireDate); // 保留3天数据
        }
    }

    /**
     * 过滤不正规的链接
     * -
     * @return void
     */
    public static function Filter()
    {
        if (!empty($_SERVER['HTTP_HOST']) && md5(strtolower($_SERVER['HTTP_HOST'])) == '23ae8999a159beec417d3633818acfe6')
        {
            header("HTTP/1.0 404 Not Found");
            exit;
        }
    }
}
