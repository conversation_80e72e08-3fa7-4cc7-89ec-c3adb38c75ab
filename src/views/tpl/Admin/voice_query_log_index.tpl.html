<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务平台-语音发送记录</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
  <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a>  >  <span>语音发送记录</span></div>
    <!--面包屑导航 结束-->
    
    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="javascript:">语音发送记录</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    
    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="" method="post" id="queryFromId">
            <div class="dataWrap clearfix">
        	<em>日期：</em>
            <div class="dataList w130">
            	<div class="getTime"><input type="text" value="{{$pageArray.startTime}}" autocomplete="off" name="startTime" onclick="WdatePicker()"/><i>日历icon</i></div>
            </div>
            </div><!--dataWrap end-->
            <span class="dataTxt">至</span>
            <div class="dataWrap clearfix">
                <div class="dataList w130">
                	<div class="getTime"><input type="text" value="{{$pageArray.endTime}}" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->
        	<select class="selectMod ml10 w90" name="queryType">
            	<option value="0">请选择</option>
            	<option value="1" {{if  $pageArray.queryType eq 1 }} selected="selected"{{/if}} {{if empty($pageArray.queryType)}} selected="selected"{{/if}} >手机号码</option>
                <option value="2" {{if  $pageArray.queryType eq 2 }} selected="selected"{{/if}} >passid</option>
            </select>
            <span class="input-sp w130"><input type="text" autocomplete="off" name="queryValue" value="{{$pageArray.queryValue}}" /></span>
            <!--div class="dataWrap ml10 clearfix">
            	<em>状态码：</em>
                <select class="selectMod w90" name="statusCode">
                    <option value="">全部</option>
                    {{foreach from=$pageArray.statusCode key=statusCodeKey item=statusCodeInfo}}
                    <option value="{{$statusCodeKey}}" >{{$statusCodeInfo}}</option>
                    {{/foreach}}
                </select>
            </div--><!--dataWrap end-->
            <div class="dataWrap ml10 clearfix">
            	<em>项目：</em>
                <select class="selectMod w90 selectProject" name="projectId">
                    <option value="0">全部</option>
                    {{foreach from=$pageArray.projectList key=projectKey item=projectInfo}}
                        <option value="{{$projectKey}}" {{if $projectKey eq $pageArray.projectId}}selected="selected" {{/if}} >{{$projectInfo}}</option>
                    {{/foreach}}
                </select>
            </div>
            <div class="dataWrap ml10 clearfix showPostionDiv" style="display: none;">
            	<em>位置：</em>
                <select class="selectMod w90 " name="plId" id="selectPostionId">
                    
                </select>
            </div>
            
            <!--dataWrap end-->
            <div class="dataWrap ml10 clearfix">
            	<em>供应商：</em>
                <select class="selectMod w90" name="operatorId">
                    <option value="0">全部</option>
                    {{foreach from=$pageArray.OperatorList item=OperatorInfo}}
                    <option value="{{$OperatorInfo.id}}" {{if $pageArray.operatorId eq $OperatorInfo.id  }}selected="selected"{{/if}}  >{{$OperatorInfo.provider_name}}</option>
                    {{/foreach}}
                </select>
            </div><!--dataWrap end-->
            <input type="hidden" name="queryAction" value="1"/>
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 queryFromSubmit">查询</a>
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 exportProjectData">导出</a>
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th width="60">passid</th>
                        <th width="100">手机</th>
                        <th width="140">发送时间</th>
                        <th>短信内容</th>
                        <th>发送状态</th>
                        <th>项目</th>
                        <th>类型</th>
                        <th>位置</th>
                        <th>供应商</th>
                        <th>提交状态描述</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th width="60">passid</th>
                <th width="100">手机</th>
                <th width="140">发送时间</th>
                <th>短信内容</th>
                <th>发送状态</th>
                <th>项目</th>
                <th>类型</th>
                <th>位置</th>
                <th>供应商</th>
                <th>提交状态描述</th>
            </tr>
            {{foreach from=$pageArray.logList item=logInfo}}
            <tr>
                <td>{{$logInfo.passid}}</td>
                <td>{{$logInfo.phone}}</td>
                <td>{{$logInfo.send_time}}</td>
                <td>{{$logInfo.text}}</td>
                <td title="{{$logInfo.voiceMessage}}">{{$logInfo.voiceStatusDesc}}</td>
                <td>{{$logInfo.projectName}}</td>
                <td>{{$logInfo.smsName}}</td>
                <td>{{$logInfo.postionName}}</td>
                <td>{{$logInfo.channelName}}</td>
                <td>{{$logInfo.codeDesc}}</td>
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
        {{$pageArray.showPage}}
        
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->

<script src="/style/js/tableFixed.js"></script>
<script>
$('.queryFromSubmit').click(function (){
    $('#queryFromId').submit();
});
function showPostionFunc (projectId)
{
    if (projectId==0)
    {
        $('.showPostionDiv').hide();
        $('#selectPostionId').html('');
        return false;
    }
    $.post('/Admin/SmsProject/GetProjectPostion','projectId='+projectId,function(data){
        $('.showPostionDiv').show();
        $('#selectPostionId').html(data.data);
        if ( '{{$pageArray.plId}}' !='')
        {
            $('#selectPostionId').val('{{$pageArray.plId}}');   
        }
    })
}
$('.selectProject').change(function (){
    var projectId = $(this).val();
    showPostionFunc(projectId);
})
if ('{{$pageArray.projectId}}' != '')
{
    showPostionFunc('{{$pageArray.projectId}}');
    
}

$('.exportProjectData').click(function (){
    var startTime = $('input[name="startTime"]').val();
    var endTime = $('input[name="endTime"]').val();
    if (startTime != endTime)
    {
        alert('只允许导出一天的数据');
        return false;
    }
    var link = '/Admin/VoiceQuery/Index?act=export&' + $('#queryFromId').serialize();
    window.open(link);
})

</script>
</body>
</html>