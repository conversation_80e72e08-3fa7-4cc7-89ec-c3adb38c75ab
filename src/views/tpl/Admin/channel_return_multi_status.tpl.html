<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务平台-群发短信状态</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
  <script src="/style/js/tableFixed.js"></script>
  <script src="/style/date/WdatePicker.js"></script>

    <style>
        .boxMod {
            left: 50%;
            top: 10%;
        }
        .scrollWrap {
            height: 400px;
            overflow-y: scroll;
        }
    </style>

</head>



<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a>  >  <span>群发短信状态</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a href="/Admin/ChannelReturnStatus/Index">短信发送状态</a>
        <a class="currTabMenu" href="javascript:">群发短信状态</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    
     <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="" method="post" id="queryFromId">
            <div class="dataWrap clearfix">
        	<em>日期：</em>
            <div class="dataList w130">
            	<div class="getTime"><input type="text" value="{{$pageArray.startTime}}" autocomplete="off" name="startTime" onclick="WdatePicker()"/><i>日历icon</i></div>
            </div>
            </div><!--dataWrap end-->
            <span class="dataTxt">至</span>
            <div class="dataWrap clearfix">
                <div class="dataList w130">
                	<div class="getTime"><input type="text" value="{{$pageArray.endTime}}" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->

            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 queryFromSubmit" >查询</a>
        
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    
    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>短信ID</th>
                        <th>手机号码</th>
                        <th>不合法手机号</th>
                        <th>服务器IP</th>
                        <th>服务商</th>
                        <th>项目提交时间</th>
                        <th>项目提交状态</th>
                        <th>发送状态</th>
                        <th>服务商回执状态</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th>短信ID</th>
                <th>手机号码</th>
                <th>不合法手机号</th>
                <th>服务器IP</th>
                <th>服务商</th>
                <th>项目提交时间</th>
                <th>项目提交状态</th>
                <th>发送状态</th>
                <th>服务商回执状态</th>
            </tr>
            {{foreach from=$pageArray.receipList item=receipInfo}}
            <tr title="{{$receipInfo.message}}">
                <td>{{$receipInfo.id}}</td>
                <td class="phone" data-phone="{{$receipInfo.phone}}">{{$receipInfo.shortPhone}}</td>
                <td class="phone" data-phone="{{$receipInfo.illegal_phone}}">{{$receipInfo.illegalShortPhone}}</td>
                <td>{{$receipInfo.server_ip}}</td>
                <td>{{ $pageArray.operatorList[$receipInfo.channel]}}</td>
                <td>{{$receipInfo.send_time}}</td>
                <td>{{ if $receipInfo.send_status > 0}}提交成功{{if $receipInfo.send_status ==2}}({{$receipInfo.send_response}}){{/if}} {{else}}失败({{$receipInfo.send_response}}){{/if}}</td>
                <td>{{$receipInfo.codeDesc}}</td>
                <td>{{if !empty($receipInfo.scs_id)}}{{if $receipInfo.status ==1}}成功{{else}}失败{{/if}}{{/if}}</td>
               
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->     
        {{$pageArray.showPage}}
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->

<div class="boxMask" style="display:none">透明遮罩层</div>
<!--添加用户 弹窗 开始-->
<div class="boxMod w800 showAllPhoneDiv">
    <div class="boxTop"><h2 class="boxTit divSetUserClass"></h2><a class="BoxClose" href="javascript:">关闭按钮</a>
    </div>

    <div class="scrollWrap" id="modal">


    </div>

</div><!--boxMod end-->
<!--添加用户 弹窗 结束-->


<script>
$('.queryFromSubmit').click(function (){
    $('#queryFromId').submit();
});


$('.BoxClose').click(function (){
    $('.boxMask').hide();
    $('.boxMod').hide();
});

$('.phone').click(function (){
    $("#modal").html($(this).attr("data-phone"));
    $('.boxMask').show();
    $('.showAllPhoneDiv').show();
});

</script>
</body>
</html>