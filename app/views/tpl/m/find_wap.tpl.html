{{include file = "m/head.tpl.html"}}
{{if $pageArray.step == '1'}}
    <article class="g-user-bd">
        <header>
            <h1 class="g-user -title">找回密码</h1>
        </header>
        <form class="g-user-form form-retrieveway" id="find-form" action="/find/step2" method="POST">
            <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
            <fieldset>
                <div class="form-item">
	                <input type="text" class="form-ipt" id="username"  placeholder="手机/邮箱/用户名">
                    <input type="hidden" id="username1" name="username">
	                <p class="form-tip-error accountError" style="display: none;"><i></i></p>
	            </div>
	            <div class="form-item">
	                <div class="form-vcode">
	                    <input type="text" class="form-ipt" placeholder="请输入验证码" name="check_code" id="yzmcode">
                        <!--img src 不能为#,不然会请求当前url，造成数据不对-->
	                    <span class="form-vcode-num"><img src="" alt="" class="clickImg" width="100%"></span>
	                    <a href="javascript:;" class="form-lnk clickImg">换一换</a>
	                </div>
                    <p class="form-tip-error codeError" style="display: none;"><i></i>2345帐号或密码错误，均区分大小写</p>
	            </div>
	            
	            <div class="btn-group">
	               <a href="javascript:void(0);" class="btn-confirm nextCommit">下一步</a>
	            </div>
            </fieldset>
        </form>
        
    </article>
    <script type="text/javascript">
    if (!("onpageshow" in window)) {
        onload = function() {
            $(".clickImg").attr('src', '/Captcha?' + Math.random());
            $("#yzmcode").val('');
        }
    } else {
        onpageshow = function() {
            $(".clickImg").attr('src', '/Captcha?' + Math.random());
            $("#yzmcode").val('');
        }
    }

    var emailErr = 1, phoneErr = 1, usernameErr = 1;
    $(function() {
        $(".form-ipt").bind({
            focus: function() {
                $(".form-item-error").removeClass('form-item-error');
                $(".form-tip-error").hide();
                $(this).parent('.form-item').find("input[name='retrieveway']").prop("checked",true);
                $("#from").val($(this).prop('name'));
            },
        });
        $(".radio-item").click(function() {
            var form_ipt = $(this).parent().find(".form-ipt");
            if(form_ipt.size()){
                $(".form-item-error").removeClass('form-item-error');
                $(".form-tip-error").hide();
                $("#from").val(form_ipt.prop('name'));
            }
        });
    });
    
    $('.nextCommit').click(function (){
        var username = $("#username").val();
        var yzmcode = $("#yzmcode").val();
        if (username == '' )
        {
            $('.accountError').parent().addClass('form-item-error');
            $('.accountError').html('<i class="icon-error"></i>请输入要找回的帐号').show();
            return false;
        }
        if (yzmcode == '')
        {
            $('.codeError').parent().addClass('form-item-error');
            $('.codeError').html('<i class="icon-error"></i>请输入正确的四位数字').show();
            return false;
        }
        $.ajax({
            url: "/webapi/check/jsonp",
            data: 'value=' + username + '&with=0',
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(status) {
                if(status == 2)
                {
                    $('.accountError').parent().addClass('form-item-error');
                    $('.accountError').html('<i class="icon-error"></i>帐号不存在').show();
                }
                if(status == 1)
                {
                    $("#username1").val(AES.encrypt(username));
                    $('#find-form').submit();
                }     
            },
            error: function(jqXHR, textStatus, errorThrown) {
                phoneErr = 1;
            },
            timeout: 3000
        });
        
    })
    if ( '{{$pageArray.yzmError}}' != '' )
    {
        $('.codeError').parent().addClass('form-item-error');
        
        $('.codeError').html('<i class="icon-error"></i>{{$pageArray.yzmError}}').show();
        $("#username").val('{{$pageArray.userName}}');
    }
    $('.clickImg').click(function (){
        var d = new Date();
        $('img[class="clickImg"]').prop('src','/Captcha?d=' + d.getTime() );
    })
</script>
{{elseif $pageArray.step == '2'}}
    {{if $pageArray.repeat == 1}}
    <article class="g-user-bd">
        <header>
            <h1 class="g-user-title">找回密码</h1>
        </header>
        <form id="find-form" action="/find/step2" method="POST" onsubmit="return checkAll();"class="g-user-form form-retrieveway">
        <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
        <input type="hidden" name="from" id="from" value="username"/>
        <div class="form-item form-radio">
            <label for="retrieveway1">请输入用户名</label>
            <input type="text" id="username"  class="form-ipt" onblur="checkUsername();" placeholder="请输入用户名">
            <input type="hidden" id="username1" name="username">
            <p class="form-tip-error" id="usernameMsg" style="display:none"></p>
        </div>
        <div class="btn-group">
           <a href="#" onclick="$('#username1').val(AES.encrypt($('#username').val()));$('#find-form').submit();return false;" class="btn-confirm">下一步</a>
        </div>
        </form>
    </article>
    <script type="text/javascript">
    var usernameErr = 1;
    $(function() {
        $(".form-ipt").bind({
            focus: function() {
                $(".form-item-error").removeClass('form-item-error');
                $(".form-tip-error").hide();
                $(this).parent('.form-item').find("input[name='retrieveway']").prop("checked",true);
            },
        });
    });
    function checkUsername() {
        var username = $("#username").val();
        if (!username) {
            usernameErr = 1;
            $("#username").parent().addClass('form-item-error');
            $("#usernameMsg").html('<i></i>请输入正确的用户名！');
            $("#usernameMsg").show();
            return false;
        }
        if (/[^\u4E00-\u9FA5\w_@\.\-]/.test(username)) {
            usernameErr = 1;
            $("#username").parent().addClass('form-item-error');
            $("#usernameMsg").html('<i></i>请输入正确的用户名！');
            $("#usernameMsg").show();
            return false;
        }
        $.ajax({
            url: "/api/check/jsonp",
            data: 'type=username&value=' + username,
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(response) {
                err = response;
                if (err == 0)
                {
                    usernameErr = 1;
                    $("#username").parent().addClass('form-item-error');
                    $("#usernameMsg").html('<i></i>此用户名不存在！');
                    $("#usernameMsg").show();
                    return false;
                }
                else if (err == 2)
                {
                    usernameErr = 1;
                    $("#username").parent().addClass('form-item-error');
                    $("#usernameMsg").html('<i></i>请输入正确的用户名！');
                    $("#usernameMsg").show();
                    return false;
                }
                usernameErr = 0;
            },
            error: function(jqXHR, textStatus, errorThrown) {
                usernameErr = 1;
            },
            timeout: 3000
        });
    }
    function checkAll() {
        checkUsername();
        return !usernameErr;
    }
    </script>
    {{elseif $pageArray.userinfo.email||$pageArray.userinfo.phone}}
    <article class="g-user-bd">
        <header>
            <h1 class="g-user-title">找回密码</h1>
        </header>
        <form id="find-form" action="
                            {{if $pageArray.from != 'username'}}
                                /find/step3
                            {{else}}
                                /find/step2_5
                            {{/if}}" method="GET" class="g-user-form form-retrieveway">
        <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
            <fieldset>
                <legend class="form-title">选择找回方式</legend>
                <ul class="uc-list form-radio">
                {{if $pageArray.userinfo.phone && $pageArray.userinfo.email}}
                    <li id="phone" hash="{{$pageArray.md5phone}}">
                        <input type="radio" checked name="retrieveway" id="retrieveway1">
                        <label for="retrieveway1" class="vtel"><i class="uc-list-icon"></i><span>选择手机: <em>{{$pageArray.userinfo.phone}}</em></span></label>
                    </li>
                    <li id="email" hash="{{$pageArray.md5email}}">
                        <input type="radio" name="retrieveway" id="retrieveway2">
                        <label for="retrieveway2"class="vemail"><i class="uc-list-icon"></i><span>选择邮箱: <em>{{$pageArray.userinfo.email}}</em></span></label>
                    </li>
					<input type="hidden" name="from" id="from" value="phone"/>
					{{if $pageArray.from != 'username'}}
						<input type="hidden" name="csrf" value="{{$pageArray.csrf}}" />
						<input type="hidden" name="code" id="code" value="{{$pageArray.md5phone}}" />
					{{/if}}
					{{elseif $pageArray.userinfo.phone}}
                    <li id="phone" hash="{{$pageArray.md5phone}}">
                        <input type="radio" checked name="retrieveway" id="retrieveway1">
                        <label for="retrieveway1" class="vtel"><i class="uc-list-icon"></i><span>选择手机: <em>{{$pageArray.userinfo.phone}}</em></span></label>
                    </li>
					<input type="hidden" name="from" id="from" value="phone"/>
					{{if $pageArray.from != 'username'}}
						<input type="hidden" name="csrf" value="{{$pageArray.csrf}}" />
						<input type="hidden" name="code" id="code" value="{{$pageArray.md5phone}}" />
					{{/if}}
                    {{else}}
                    <li id="email" hash="{{$pageArray.md5email}}">
                        <input type="radio" name="retrieveway" id="retrieveway2">
                        <label for="retrieveway2"class="vemail"><i class="uc-list-icon"></i><span>选择邮箱: <em>{{$pageArray.userinfo.email}}</em></span></label>
                    </li>
					<input type="hidden" name="from" id="from" value="email"/>
					{{if $pageArray.from != 'username'}}
						<input type="hidden" name="csrf" value="{{$pageArray.csrf}}" />
						<input type="hidden" name="code" id="code" value="{{$pageArray.md5email}}" />
					{{/if}}
                    {{/if}}
                </ul>
                <div class="btn-group">
                   <a href="#" onclick="$('#find-form').submit();return false;"class="btn-confirm">下一步</a> 
                </div>
            </fieldset>
        </form>
    </article>
    <script type="text/javascript">
        $(function() {
            $(".uc-list > li").bind({
                click: function() {
                $("#from").val($(this).attr('id'));
                $("#code").val($(this).attr('hash'));
                }
            });
        });
    </script>
    {{else}}
    <article class="g-user-bd">
    <header>
            <h1 class="g-user-title">找回密码</h1>
            <p class="g-user-tips">您的帐号没有绑定过手机或邮箱！<br/>请致电客服 400-000-2345</p>
        </header>
    </article>
    {{/if}}
{{elseif $pageArray.step == '2_5'}}
<article class="g-user-bd">
    <header>
        <h1 class="g-user-title">找回密码</h1>
    </header>
    <form id="find-form" action="/find/step3" method="GET" class="g-user-form">
        <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
        <input type="hidden" id="from" name="from" value="{{$pageArray.from}}" />
        <input type="hidden" id="csrf" name="csrf" value="{{$pageArray.csrf}}" />
		<input type='hidden' id='realcode' name='code' value='' />
        {{if $pageArray.from == 'phone'}}
        <div class="form-item">
            <p class="form-txt">手机号: <em>{{$pageArray.userinfo.phone}}</em></p>
        </div>
        <div class="form-item">
            <div class="form-vcode">
                <input type="text" id="code" class="form-ipt" placeholder="请输入你的手机号码,以确保是本人操作">
            </div>
            <p class="form-tip-error" id="checkMsg" style="display: none;"><i></i>验证码错误</p>
        </div>
        {{else}}
        <div class="form-item" id="email-div">
            <p class="form-txt">邮箱地址: <em>{{$pageArray.userinfo.email}}</em></p>
            <p class="form-tip-error" id="sendMsg" style="display: none;"></p>
        </div>
        <div class="form-item">
            <div class="form-vcode">
                <input type="text" id='code' class="form-ipt" placeholder="请输入你的验证邮箱,以确保是本人操作">
            </div>
            <p class="form-tip-error" id="checkMsg" style="display: none;" style='display:none'><i></i>验证码错误</p>
        </div>
        {{/if}}
        <div class="btn-group">
            <a href="#" class="btn-confirm" onclick="return checkUsername();">下一步</a>
        </div>
    </form>
</article>
<script type="text/javascript" src="/js/login.js"></script>
<script type="text/javascript">
    if( !('placeholder' in document.createElement('input')) ){

        $('input[placeholder],textarea[placeholder]').each(function(){
            var that = $(this),
                    text= that.attr('placeholder');
            if(that.val()===""){
                that.val(text).addClass('placeholder');
            }
            that.focus(function(){
                if(that.val()===text){
                    that.val("").removeClass('placeholder');
                }
            })
            .blur(function(){
                if(that.val()===""){
                    that.val(text).addClass('placeholder');
                }
            })
            .closest('form').submit(function(){
                if(that.val() === text){
                    that.val('');
                }
            });
        });
    }

    function checkUsername() {
        var username = $.trim($("#code").val());
        var from = $.trim($('#from').val());
        var msg = 'email' == from ? '请您输入验证邮箱' : '请您输入验证手机';
        if (!username) {
            $("#code").addClass('ipt_txt_error');
            $("#checkMsg").html('<i class="icon-error"></i>'+msg);
            $("#checkMsg").show();
            return false;
        }
        if (/[^\u4E00-\u9FA5\w_@\.\-]/.test(username)) {
            $("#code").addClass('ipt_txt_error');
            $("#checkMsg").html('<i class="icon-error"></i>'+msg);
            $("#checkMsg").show();
            return false;
        }
        $.ajax({
            url: "/webapi/check/jsonCode",
            data: 'value=' + username + '&type='+$('#from').val(),
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(status) {
                if (status == 0)
                {
                    $("#code").addClass('ipt_txt_error');
                    $('#checkMsg').html('<i class="icon-error"></i>帐号不存在').show();
                    return false;
                }
                else if (status == 2)
                {
                    var msg = 'email' == from ? '邮箱不匹配' : '手机号不匹配';
                    $("#code").addClass('ipt_txt_error');
                    $('#checkMsg').html('<i class="icon-error"></i>'+msg).show();
                    return false;
                }
                $("#realcode").val(MD5(username));
                $('#find-form').submit();
            },
            error: function(jqXHR, textStatus, errorThrown) {
                phoneErr = 1;
            },
            timeout: 3000
        });
    }
</script>
{{elseif $pageArray.step == '3'}}
<article class="g-user-bd">
    <header>
        <h1 class="g-user-title">找回密码</h1>
    </header>
    <form id="find-form" action="/find/step4" method="GET" onsubmit="return checkCode();"class="g-user-form">
        <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
        <input type="hidden" id="from" name="from" value="{{$pageArray.from}}"/>
        {{if $pageArray.from == 'phone'}}
        <div class="form-item">
            <p class="form-txt">手机号: <em>{{$pageArray.userinfo.phone}}</em></p>
        </div>
        <div class="form-item">
            <div class="form-vcode">
                <input type="tel" id="code" name="code" class="form-ipt" placeholder="请输入验证码">
                <a href="javascript:;" id="sendBtn" onclick="sendCode();return false;" class="btn-getvcode">发送验证码</a>
                <span id="sendingBtn" class="btn-getvcode btn-disabled" style = "display :none">重新发送(60)</span>
            </div>
            <p class="form-tip-error" id="checkMsg" style="display: none;"><i></i>验证码错误</p>
        </div>
        {{else}}
        <div class="form-item" id="email-div">
            <p class="form-txt">邮箱地址: <em>{{$pageArray.userinfo.email}}</em></p>
            <p class="form-tip-error" id="sendMsg" style="display: none;"></p>
        </div>
        <div class="form-item">
            <div class="form-vcode">
                <input type="tel" name='code' id='code' class="form-ipt" placeholder="请输入验证码">
                <a id="sendBtn" class="btn-getvcode" href="#" onclick="sendCode();return false;">发送验证码</a>
                <a id="sendingBtn" class="btn-getvcode btn-disabled" style="display:none">重新发送(57)</a>
            </div>
            <p class="form-tip-error" id="checkMsg" style="display: none;" style='display:none'><i></i>验证码错误</p>
        </div>
        {{/if}}
        <div class="btn-group">
           <a href="#" class="btn-confirm" onclick="$('#find-form').submit();return false;">下一步</a>
        </div>
    </form>
</article>
<script type="text/javascript">
    $(function() {
		/**
		 * {{if $pageArray.rstep}}
		 */
			sendCode();
		 /**
		  * {{/if}}
		  */
	
        $("#code").bind({
            focus: function() {
                $(this).parent('.form-vcode').removeClass("form-item-error");
                $(".form-tip-error").hide();
            },
        });
    });
    function sendCode() {
        var from = $("#from").val();
        $.post("/find/sendCode", {'from': from}, function(data) {
            if (data == '400.0') {
                $("#sendMsg").addClass('form-tip-error').html('<i></i>验证太频繁，请稍后再试！');
                $("#sendMsg").show();
            } else if (data == '500.0') {
                $("#sendMsg").addClass('form-tip-error').html('<i></i>服务器忙，请稍后再试！');
                $("#sendMsg").show();
            } else if (from == 'email') {
                var emailExp = '{{$pageArray.userinfo.email}}'.split('@');
                if (emailExp[1] == 'gmail.com') {
                    var emailHost = 'http://www.' + emailExp[1];
                } else {
                    var emailHost = 'http://mail.' + emailExp[1];
                }
                $('#sendMsg').removeClass('form-tip-error').html('验证邮件已发送');
                $("#sendMsg").show();
            }
        });
        $("#sendBtn").text('重新发送');
        showSendMsg(60);
    }
    function showSendMsg(times) {
        $("#sendBtn").hide();
        $("#sendingBtn").text('重新发送（' + times + '）');
        $("#sendingBtn").show();
        if (times > 1) {
            times--;
            setTimeout(function() {
                showSendMsg(times);
            }, 1000);
        } else {
            $("#sendingBtn").hide();
            $("#sendBtn").show();
        }
    }
    function checkCode() {
        var codeErr = true;
        var code = $("#code").val();
        var from = $("#from").val();
        $.ajax({
            type: "POST",
            url: "/find/checkCode",
            async: false,
            data: "from=" + from + "&code=" + code,
            success: function(data) {
                if (data == '200.0') {
                    codeErr = false;
                } else if (data == '400.0') {
                    $('.form-vcode').addClass('form-item-error');
                    $("#checkMsg").html('<i></i>验证码错误！');
                    $("#checkMsg").show();
                }
            }
        });
        return !codeErr;
    }
</script>
{{elseif $pageArray.step == '4'}}
<article class="g-user-bd">
    <header>
        <h1 class="g-user-title">找回密码</h1>
    </header>
    <form id="find-form" action="/find/step5" method="POST" onsubmit="return checkPwd(true);" class="g-user-form">
        <input type="hidden" name="from" value="{{$pageArray.from}}"/>
        <input type="hidden" name="code" value="{{$pageArray.code}}"/>
        <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
        <input type="hidden" name="pwd_strength" id="pwd_strength"/>
        {{if $pageArray.username && $pageArray.username != ''}}
        <div class="form-item">
            <p class="form-txt">用户名: <em>{{$pageArray.username}}</em></p>
        </div>
        {{/if}}
        <div class="form-item">
            <input type="password" id="password" name="password" onblur="checkPwd();" class="form-ipt" placeholder="输入新密码">
            <p id="pwdStr" class="form-psw-tip" style="display: none;" ></p>
            <p id="pwdMsg" class="form-tip">6-16个字符</p>
        </div>
        <div class="form-item">
            <input type="password" id="repassword" onblur="checkPwd();" class="form-ipt" placeholder="重复新密码">
            <p class="form-tip" id="repwdMsg" style="display:none">最少6个字符</p>
        </div>
        <div class="btn-group">
           <a href="#" class="btn-confirm" onclick="$('#find-form').submit();return false;">下一步</a>
        </div>
</article>
        <script type="text/javascript" src="/js/client/zxcvbn.js"></script>
        <script type="text/javascript">
        $(function() {
        $(".form-ipt").bind({
            focus: function() {
                $(this).parent('.form-item').removeClass("form-item-error");
                $(this).parent().find(".form-tip-error").hide();
            },
        });
        });
        var retFalg = "";
            function checkPwd(submit) {
                if ($("#password").val() == '')
                {
                    $("#password").parent().addClass('form-item-error');
                    $('#pwdMsg').addClass('form-tip-error').html('<i></i>密码不能为空');
                    $('#pwdMsg').show();
                    $("#pwdStr").hide();
                    retFalg = false;
                }
                if ($("#password").val().length < 6)
                {
                    $("#password").parent().addClass('form-item-error');
                    $('#pwdMsg').addClass('form-tip-error').html('<i></i>最少6个字符');
                    $('#pwdMsg').show();
                    $("#pwdStr").hide();
                    retFalg = false;
                }
                if ($("#password").val().length > 16)
                {
                    $("#password").parent().addClass('form-item-error');
                    $('#pwdMsg').addClass('form-tip-error').html('<i></i>最多16个字符');
                    $('#pwdMsg').show();
                    $("#pwdStr").hide();
                    retFalg = false;
                }
                if ($("#password").val() == '123456' || $("#password").val() == '654321' || $("#password").val() == '111222' || checkPassSame($("#password").val()) == false)
                {
                    $("#password").parent().addClass('form-item-error');
                    $('#pwdMsg').addClass('form-tip-error').html('<i></i>您的密码过于简单，请重新输入');
                    $('#pwdMsg').show();
                    $("#pwdStr").hide();
                    retFalg = false;
                }
                var passscore = zxcvbn($("#password").val());
                if (passscore.score <= 0)
                {
                    $('#pwd_strength').val(0);
                    $("#password").parent().addClass('form-item-error');
                    $('#pwdMsg').addClass('form-tip-error').html('<i></i>您的密码过于简单，请重新输入');
                    $('#pwdMsg').show();
                    $("#pwdStr").html('密码强度: <span class="weak"><s></s><s></s><s></s><s></s>弱</span>').show();
                    retFalg = false;
                }
                else if (passscore.score == 1)
                {
                    $('#pwd_strength').val(1);
                    $('#pwdMsg').hide();
                    $("#pwdStr").html('密码强度: <span class="medium"><s></s><s></s><s></s><s></s>中</span>').show();
                    retFalg = true;
                }
                else if (passscore.score == 2)
                {
                    $('#pwd_strength').val(2);
                    $('#pwdMsg').hide();
                    $("#pwdStr").html('密码强度: <span class="strong"><s></s><s></s><s></s><s></s>强</span>').show();
                    retFalg = true;
                }
                else if ( passscore.score >= 3)
                {
                    $('#pwd_strength').val(3);
                    $('#pwdMsg').hide();
                    $("#pwdStr").html('密码强度: <span class="stronger"><s></s><s></s><s></s><s></s>极强</span>').show();
                    retFalg = true;
                }
                if ($("#repassword").val() != $("#password").val())
                {
                    $("#repassword").parent().addClass('form-item-error');
                    $('#repwdMsg').addClass('form-tip-error').html('<i></i>您两次输入的密码不一致');
                    $('#repwdMsg').show();
                    retFalg = false;
                }
                if (retFalg === true && submit === true)
                {
                    var pwd = $("#password").val();
                    var repwd = $("#repassword").val();
                    $("#password").val(AES.encrypt(pwd));
                    $("#repassword").val(AES.encrypt(repwd));
                }
                return retFalg;
            }
            function checkPassSame(pass)
            {
                var first = pass.substring(0, 1);
                var exp = new RegExp('^' + first + '+$');
                if (exp.test(pass))
                {
                    return false;
                }

                if (first == 'a' || first == 'A')
                {
                    f = pass.charCodeAt(0);
                    for (i = 1; i < pass.length; i++)
                    {
                        tmp = pass.charCodeAt(i);
                        if (tmp - f != i)
                        {
                            return true;
                        }
                    }
                    return false;
                }
                return true;
            }
            
</script>
{{elseif $pageArray.step == '5'}}
<article class="g-user-bd">
    <div class="g-user-result bind-sec">
        <h1><i></i>修改密码成功</h1>
        <p id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/edit_info.php{{/if}}">返回上级页面</a></p>
    </div>
</article>
<script type="text/javascript">
    var times = 3;
    $(function() {
        setInterval(function() {
            if (times > 0) {
                $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/edit_info.php{{/if}}">返回上级页面</a>');
            } else {
                window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/edit_info.php{{/if}}";
            }
            times--;
        }, 1000);
    });
</script>
{{/if}}
{{include file = "public/footer_m.tpl.html"}}
<script type="text/javascript">
document.body.addEventListener('touchstart', function () {});  
</script>
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
</body>
</html>