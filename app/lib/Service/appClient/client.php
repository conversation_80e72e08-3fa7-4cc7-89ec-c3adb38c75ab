<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/12/29
 * Time: 15:44
 */

namespace Service\appClient;


/**
 * Class client
 * @package Service\appClient
 * @method response getAppList()
 */
class client
{
    private static $host = "http://127.0.0.1";
    private static $port = 3399;
    private static $instances = array();

    private function __construct(array $config)
    {
        if (RUNMODE == "development") {
            if (!empty($_ENV["GOSERVERIP"]) && filter_var($_ENV["GOSERVERIP"], FILTER_VALIDATE_IP)) {
                self::$host = 'http://' . $_ENV["GOSERVERIP"];
            }
        }
    }

    const API_INFO_SCHEME = [
        'getAppList'         => [
            'uri'    => '/api/admin/AppList',
            'method' => 'get',
            'param'  => ["keywords" => ""],
        ],
        'saveAppInfo'        => [
            'uri'    => '/api/admin/saveAppInfo',
            'method' => 'post',
            'param'  => ['id' => 0, 'package_id' => 0, 'mid' => "", 'name' => "", 'package_name' => "", 'sign' => ""],
        ],
        'getEcoRuleInfoList' => [
            'uri'    => '/api/admin/getEcoRuleInfoList',
            'method' => 'get',
            'param'  => ["keywords" => ""],
        ],
        'getEcoConfigApp'    => [
            'uri'    => '/api/admin/getEcoConfigApp',
            'method' => 'get',
            'param'  => ['groupName' => ""],
        ],
        'getEcoList'         => [
            'uri'    => '/api/admin/getEcoList',
            'method' => 'get',
            'param'  => [],
        ],
        'saveEcoConfig'      => [
            'uri'    => '/api/admin/saveEcoConfig',
            'method' => 'post',
            'param'  => ['group_name' => "", 'mid' => "", 'config' => [], 'isEdit' => 0, 'created_by' => ""],
        ],
        'getAppUcKey'        => [
            'uri'    => '/api/admin/getAppUcKey',
            'method' => 'post',
            'param'  => ['mid' => ""],
        ],
    ];

    public static function getInstances(array $config)
    {
        $className = get_called_class();
        $classNameMd5 = $config ? md5($className . ':' . serialize($config)) : $className;
        if (!isset(self::$instances[$classNameMd5])) {
            self::$instances[$classNameMd5] = new static($config);
        }

        return self::$instances[$classNameMd5];
    }

    /**
     * @param $apiScheme
     * @return bool
     */
    private static function schemeCheck($apiScheme)
    {
        if (!array_key_exists('uri', $apiScheme)) {
            return false;
        }
        if (!array_key_exists('method', $apiScheme)) {
            return false;
        }
        if (!array_key_exists('param', $apiScheme)) {
            return false;
        }

        return true;
    }

    /**
     * 获取用户传递的请求参数
     * @param       $schemeParam
     * @param array $param
     * @return array
     */
    private static function getArgs($schemeParam, $param)
    {
        $data = [];
        $index = 0;
        foreach ($schemeParam as $key => $value) {
            // 所有请求参数必须在scheme定义的参数列表中，并严格按照定义顺序传递，并不能为null
            if (isset($param[$index])) {
                $data[$key] = $param[$index];
            } else {
                !is_null($value) && $data[$key] = $value;
            }
            $index ++;
        }

        return $data;
    }

    private static function getApi($api)
    {
        return self::$host . ":" . self::$port . $api;
    }

    /**
     * @param $method
     * @param $arguments
     * @return response
     * @throws \Exception
     */
    public function __call($method, $arguments)
    {
        // scheme一些基本的检查验证
        if (!array_key_exists($method, static::API_INFO_SCHEME)) {
            throw new \Exception('scheme: api not found');
        }
        $apiScheme = static::API_INFO_SCHEME[$method];
        // 检查api scheme是否符合规范
        if (!static::schemeCheck($apiScheme)) {
            throw new \Exception('scheme: check failed');
        }
        // 获取参数（用户输入参数，有些时候不一定就等于请求参数）
        $param = static::getArgs($apiScheme['param'], $arguments);
        $options = [];

        if ($apiScheme["method"] == "post") {
            $jsonData = json_encode($param);
            $options[CURLOPT_HTTPHEADER] = [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($jsonData),
            ];
            $options[CURLOPT_POST] = 1;
            $options[CURLOPT_POSTFIELDS] = $jsonData;
            $requestApi = self::getApi($apiScheme["uri"]);
        } else {
            $requestApi = self::getApi($apiScheme["uri"]) . "?" . http_build_query($param);
        }
        $authData = [
            "requestTime" => (string)time(),
        ];
        if (empty($_COOKIE['admin_user'])) {
            $authData["admin_user"] = mb_convert_encoding($_COOKIE['admin_user'], "UTF-8", "GBK");
        }
        $clientId = md5(uniqid() . (microtime(true) * 10000));
        ksort($authData);
        $authData['sign'] = md5(urldecode(http_build_query($authData)) . $clientId . "fMfCCniFEOKYT06vsun762dmmR8DSFFe");
        $options[CURLOPT_HTTPHEADER][] = "auth-data:" . base64_encode(json_encode($authData));
        $options[CURLOPT_HTTPHEADER][] = 'client-id:' . $clientId;
        $response = curl_get_contents($requestApi, $options);

        return new response($response);
    }


    private function __clone()
    {
        // TODO: Implement __clone() method.
    }
}
