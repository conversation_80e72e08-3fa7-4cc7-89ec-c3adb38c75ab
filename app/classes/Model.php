<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：Model.php
 * 摘    要：Model基类
 * 作    者：张小虎
 * 修改日期：2013.10.12
 */
use Octopus\PdoEx;

class Model extends BaseClass
{

    protected $pdo, $dbName, $tableName, $pkName, $attrs, $id, $data = array();
    protected $redis;
    protected static $nowMonth = null;


    public function __construct()
    {
        parent::__construct();
        if ($this->dbName)
        {
            $dbConfig = Config::get("database");
            $this->pdo = PdoEx::getInstance($this->dbName, $dbConfig[$this->dbName]);
        }
        $this->redis = RedisAction::connect();
    }

    public function reopen()
    {
        if ($this->pdo === null && $this->dbName)
        {
            $dbConfig = Config::get("database");
            $this->pdo = PdoEx::getInstance($this->dbName, $dbConfig[$this->dbName]);
        }
    }

    public function close()
    {
        PdoEx::delInstance($this->dbName);
        $this->pdo = null;
    }

    public function save()
    {
        return $this->edit($this->id, $this->data);
    }

    public function drop()
    {
        return $this->del($this->id);
    }

    public function get($id)
    {
        return $this->pdo->find("SELECT * FROM " . $this->tableName . " WHERE " . $this->pkName . " = :id", array(":id" => $id));
    }

    public function add($data)
    {
        return $this->pdo->insert($this->tableName, $data);
    }

    public function edit($id, $data)
    {
        $condition = array(
            "where"  => $this->pkName . " = :id",
            "params" => array(":id" => $id),
        );
        return $this->pdo->update($this->tableName, $data, $condition);
    }

    public function del($id)
    {
        $condition = array(
            "where"  => $this->pkName . " = :id",
            "params" => array(":id" => $id),
        );
        return $this->pdo->delete($this->tableName, $condition);
    }

    public function __get($name)
    {
        return $this->data[$name];
    }

    public function __set($name, $value)
    {
        $this->data[$name] = $value;
    }

    /**
     * @param ?string $month 年月
     * @return string
     */
    public static function getTableName($month = null)
    {
        if (PHP_SAPI !== 'cli')
        {
            if (self::$nowMonth === null)
            {
                self::$nowMonth = date('Ym');
            }
            if ($month === null)
            {
                $month = self::$nowMonth;
            }
        }
        else
        {
            if ($month === null)
            {
                $month = date('Ym');
            }
        }

        if (substr($month, 0, 2) != '20' || substr($month, 4, 2) > 12)
        {
            return 'sms_logs_archive';
        }
        return "sms_logs_archive_{$month}";
    }

    /**
     * @param string $tableName 表名
     * @return bool
     */
    public function checkTableExist($tableName)
    {
        $sql = "SELECT table_name FROM information_schema.TABLES WHERE table_name = :tableName";
        return $this->pdo->find($sql, array(':tableName' => $tableName)) ? true : false;
    }

    /**
     * @param null|string $month 年月
     * @return bool
     */
    public function createSmsLogTable($month = null)
    {
        $tableName = self::getTableName($month);
        if ($this->redis->get('smsp:table:' . $tableName))
        {
            return true;
        }
        elseif ($this->checkTableExist($tableName))
        {
            $this->redis->set('smsp:table:' . $tableName, true);
            return true;
        }
        $sql = "CREATE TABLE IF NOT EXISTS $tableName LIKE `sms_logs_archive`";
        $kkk = $this->pdo->query($sql);
        if ($kkk === 0)
        {
            $this->redis->set('smsp:table:' . $tableName, true);
            return true;
        }
        return false;
    }

    /**
     * @param string $tableName $tableName
     * @param array $where $where
     * @param bool $useWritableDB $useWritableDB
     * @return bool|array
     */
    public function getTotalByWhere($tableName = '', $where = array(), $useWritableDB = false)
    {
        $data = $this->getByWhere($tableName, 'count(*) as num', $where, $useWritableDB);
        return $data['num'] ?? 0;
    }

    /**
     * @param string $tableName $tableName
     * @param string $filed $filed
     * @param array $where $where
     * @param bool $useWritableDB $useWritableDB
     * @return array|bool
     */
    public function getByWhere($tableName = '', $filed = "*", $where = array(), $useWritableDB = false)
    {
        if (!is_array($where)) {
            return false;
        }
        $tableName = !empty($tableName) ? $tableName : $this->tableName;
        $sql = " SELECT $filed FROM  $tableName";

        $where = $this->getWhere($where);
        $sql .= ' WHERE ' . $where['queryString'];
        return $this->pdo->find($sql, $where['whereArr'], $useWritableDB);
    }

    /**
     * @param string $tableName $tableName
     * @param string $filed $filed
     * @param array $where $where
     * @param int $limit $limit
     * @param int $offset $offset
     * @param string $orderBy $orderBy
     * @param string $groupBy $groupBy
     * @param bool $useWritableDB $useWritableDB
     * @return array|bool
     */
    public function getListByWhere($tableName = '', $filed = "*", $where = array(), $limit = 50, $offset = 0, $orderBy = " ", $groupBy = '', $useWritableDB = false)
    {
        empty($where) && $where = array();
        if (!is_array($where)) {
            return false;
        }
        $tableName = !empty($tableName) ? $tableName : $this->tableName;
        $sql = " SELECT $filed FROM $tableName";

        $where = $this->getWhere($where);
        $sql .= ' WHERE ' . $where['queryString'] . ' ' . $groupBy . ' ' . $orderBy;

        return $this->pdo->findList($sql, $where['whereArr'], $limit, $offset, $useWritableDB);
    }

    /**
     * @param string $tableName $tableName
     * @param string $filed $filed
     * @param array $where $where
     * @param string $groupBy $groupBy
     * @param string $orderBy $orderBy
     * @param bool $useWritableDB $useWritableDB
     * @return bool|array
     */
    public function getAllByWhere($tableName = '', $filed = "*", $where = array(), $groupBy = " ", $orderBy = " ", $useWritableDB = false)
    {
        empty($where) && $where = array();
        if (!is_array($where)) {
            return false;
        }
        $tableName = !empty($tableName) ? $tableName : $this->tableName;
        $sql = " SELECT $filed FROM $tableName";

        $where = $this->getWhere($where);
        $sql .= ' WHERE ' . $where['queryString'] . ' ' . $groupBy . ' ' . $orderBy;
        return $this->pdo->findAll($sql, $where['whereArr'], $useWritableDB);
    }

    /**
     * add function
     *
     * @param array $data data
     * @param string $tableName $tableName
     *
     * @return integer
     */
    public function addByTable($data, $tableName = '')
    {
        $tableName = !empty($tableName) ? $tableName : $this->tableName;
        return $this->pdo->insert($tableName, $data);
    }

    /**
     * edit function
     *
     * @param array $where $where
     * @param array $data data
     * @param string $tableName $tableName
     *
     * @return mixed
     */
    public function editByTable($where, $data, $tableName = '')
    {
        if (empty($where)) {
            return false;
        }
        $where = $this->getWhere($where);
        $condition = array(
            "where" => $where['queryString'],
            "params" => $where['whereArr']
        );
        $tableName = !empty($tableName) ? $tableName : $this->tableName;
        return $this->pdo->update($tableName, $data, $condition);
    }

    /**
     * del function
     *
     * @param array $where $where
     * @param string $tableName $tableName
     *
     * @return mixed
     */
    public function delByTable($where, $tableName = '')
    {
        if (empty($where)) {
            return false;
        }
        $where = $this->getWhere($where);
        $condition = array(
            "where" => $where['queryString'],
            "params" => $where['whereArr']
        );
        $tableName = !empty($tableName) ? $tableName : $this->tableName;
        return $this->pdo->delete($tableName, $condition);
    }

    /**
     * Name: 批量插入
     * User: tianyc
     * Date: 2018/7/5
     * @param array $data data
     * @param array $columns columns
     * @param string $tableName tableName
     * @return bool
     */
    public function batchInsert($data, $columns, $tableName = '')
    {
        if (empty($data)) {
            return false;
        }
        $tableName = !empty($tableName) ? $tableName : $this->tableName;
        return $this->pdo->batch($tableName, $columns, $data);
    }

    /**
     * 获取where条件
     *
     * example :  $whereArr["xxx"] = array("field", "xxx", "<=");
     *            $whereArr["xxx"] = array('field', "%xxx", 'like');
     *            $whereArr["xxx"] = array('field', "xxx", 'IN');
     *            $whereArr['field'] = "xxx";
     *
     * @param array $where $where
     * @return array
     */
    public function getWhere($where = array())
    {
        if (empty($where)) {
            return array('queryString' => 1, 'whereArr' => array());
        } else {
            $result = array();
            $whereArr = array();

            foreach ($where as $key => $val) {
                if (is_array($val)) {
                    if (isset($val[2]) && $val[2]) {
                        if (in_array(strtoupper($val[2]), array("IN", "NOT IN"))) {
                            $inKeyArr = array();
                            foreach ($val[1] as $inKey => $inItem) {
                                $inKeyArr[] = ':param' . $key . $inKey;
                                $result[':param' . $key . $inKey] = $inItem;
                            }
                            $whereArr[$key] = "{$val[0]} {$val[2]} ( " . implode(',', $inKeyArr) . " )";
                        } elseif (strtoupper($val[2]) == 'LIKE') {
                            $whereArr[$key] = $val[0] . " LIKE :param" . $key . " ";
                            $result[':param' . $key] = "%" . $val[1] . "%";
                        } elseif (in_array($val[2], array('>=', '<=', '>', '<', '!=', '<>'))) {
                            $whereArr[$key] = $val[0] . ' ' . $val[2] . ' :param' . $key;
                            $result[':param' . $key] = $val[1];
                        }
                    } else {
                        $whereArr[$key] = $val[0] . ' = :param' . $key;
                        $result[':param' . $key] = $val[1];
                    }
                } else {
                    $whereArr[$key] = $key . ' = :param' . $key;
                    $result[':param' . $key] = $val;
                }
            }
            return array('queryString' => implode(" and ", $whereArr), 'whereArr' => $result);
        }
    }

    /**
     * @return null
     */
    public function begin()
    {
        return $this->pdo->beginTransaction();
    }

    /**
     * @return null
     */
    public function commit()
    {
        return $this->pdo->commit();
    }

    /**
     * @return null
     */
    public function rollback()
    {
        return $this->pdo->rollBack();
    }
}
