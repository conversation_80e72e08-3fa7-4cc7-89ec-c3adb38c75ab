<?php

class RegController extends Controller
{

    public function actionIndex()
    {
        session_start();
        if ($_SERVER["REQUEST_METHOD"] == "POST")
        {
            if (!isset($_POST["validate"]) || $_POST["validate"] == '' || $_POST["validate"] !== '' . $_SESSION["checkIMGCode_new"])
            {
                unset($_SESSION["checkIMGCode_new"]);
                die("{'err':1,'msg':'验证码输入错误'}");
            }
            unset($_SESSION["checkIMGCode_new"]);
            $client_ip = get_client_ip();
            $msgs = array(
                300 => array(
                    0 => '2345帐号最少2个字符',
                    1 => '2345帐号请不要超过24个字符',
                    2 => '2345帐号请输入汉字，字母，数字，或邮箱地址',
                    3 => '密码最少6个字符',
                    4 => '密码最多16个字符',
                    5 => '请输入正确的邮箱',
                    6 => '此帐号已被注册，请修改2345帐号',
                    7 => '此邮箱已被注册，请换一个',
                ),
                400 => array(
                    0 => 400, //非法域名调用
                    1 => 401, //非法IP调用
                    2 => 402, //批量刷CHECK
                    3 => 403, //IP段被禁止
                    4 => 404, //IP被禁止
                    5 => 405, //未验证通过（缺少isValidate）
                )
            );
            $username = trim(mb_convert_encoding($_POST['username'], "GBK", "UTF-8"));
            if (!isset($_POST['pwd_strength']))
            {
                $pwd_strength = 2;
            }
            else
            {
                $pwd_strength = $_POST['pwd_strength'];
            }
            $regAction = loadAction('reg');

            $source = \Service\AccountStat\Consts\AccountStat::SRC_IE_BROWSER2;
            $result = $regAction->reg($username, trim($_POST['password']), strtolower(trim($_POST['email'])), $pwd_strength, '', 'browser.login.2345.com', $client_ip, $source);
            $states = explode(".", $result[0]);
            if ($states[0] == 400)
            {
                die("{'err':1,'msg':'error:{$msgs[$states[0]][$states[1]]}'}");
            }
            else if ($states[0] == 300)
            {
                die("{'err':1,'msg':'{$msgs[$states[0]][$states[1]]}'}");
            }
            else if ($states[0] == 200)
            {
                if ($states[1] == 1)
                {
                    die("{'err':2,'url':'" . PASSPORT_HOST . "/active.html'}");
                }
                else
                {
                    die("{'err':0,'uid':'{$result[1]['uid']}','sec':'" . get_sec_browser($result[1]['uid']) . "','passid':'{$result[1]['passid']}','token':'{$result[1]['token']}'}");
                }
            }
        }
        else
        {
            if (isset($_GET['ver']))
            {
                loadView('browser2/reg3.tpl.html');
            }
            else
            {
                loadView('browser2/reg.tpl.html');
            }
        }
    }

}