<?php
class TouristAction extends Action
{
     /**
     * 是否运行游客模式
     * @param string $channel 渠道
     * @param string $version 版本号
     * @param string $conf 游客配置
     * @return array
     */
    public function allowTouristMode($channel, $version, $conf)
    {
        $versionAllow = true;
        if (! empty($conf['version_op']) && $conf['version_op'] != UnionLoginRedisAction::TOURISTS_VERSION_UNLIMIT) {
            switch ($conf['version_op']) {
                case UnionLoginRedisAction::TOURISTS_VERSION_EQUAL:
                    $versionArr =  explode(',', $conf['version']);
                    $versionAllow = in_array($version, $versionArr);
                    break;
                case UnionLoginRedisAction::TOURISTS_VERSION_NOTEQUAL:
                    $versionArr =  explode(',', $conf['version']);
                    $versionAllow = ! in_array($version, $versionArr);
                    break;
                case UnionLoginRedisAction::TOURISTS_VERSIONL_LARGE:
                    $versionAllow = $version > $conf['version'];
                    break;
                case UnionLoginRedisAction::TOURISTS_VERSIONL_LARGE_EQUAL:
                    $versionAllow = $version >= $conf['version'];
                    break;
                case UnionLoginRedisAction::TOURISTS_VERSIONL_SMALL:
                    $versionAllow = $version < $conf['version'];
                    break;
                case UnionLoginRedisAction::TOURISTS_VERSIONL_SMALL_EQUAL:
                    $versionAllow = $version <= $conf['version'];
                    break;
                default:
                    //需要记录日志
                    break;
            }
        }
        $channelAllow = true;
        if (! empty($conf['channel_op']) && $conf['channel_op'] != UnionLoginRedisAction::TOURISTS_CHANNEL_UNLIMIT) {
            $confChannel = $conf['channel'];
            $in = false;
            
            if ($channel == $conf['channel'] ||
                stripos($confChannel, ",{$channel}") !== false ||
                stripos($confChannel, "{$channel},") !== false
            ) {
                $in = true;
            }
            switch ($conf['channel_op']) {
                case UnionLoginRedisAction::TOURISTS_CHANNEL_EQUAL:
                    $channelAllow = $in;
                    break;
                case UnionLoginRedisAction::TOURISTS_CHANNEL_NOTEQUAL:
                    $channelAllow = ! $in;
                    break;
                default:
                    //需要记录日志
                    break;
            }
        }
        if ($conf['relation'] == 'and') {
            return $versionAllow && $channelAllow;
        }
        return $versionAllow || $channelAllow;
    }
    /**
     * 获取游客配置
     * @param string $condition 用户参数数组
     * @return array
     */
    public function getTouristConf($condition)
    {
        $redisAction = loadAction('UnionLoginRedis');
        $conf = $redisAction->getConfigCache($condition['mid'], UnionLoginRedisAction::TOURISTS_MODE);
        $allow = $this->allowTouristMode($condition['channel'], $condition['version'], $conf);
        if (! $allow) {
            return;
        }
        $loginType = $this->filterLoginType($condition['channel'], $condition['version'], $conf);
        if (empty($loginType)) {
            return;
        }

        $ret = [
            // 'refreshTime' => $conf['next_request_interval'] * 3600,
            'protocolStatus' => isset($conf['protocol_status']) ? $conf['protocol_status'] : false,
            'isNeedShanyan'     => $conf['is_need_shanyan'],
            // 'isQuietLogin'     => isset($conf['is_quiet_login']) ? $conf['is_quiet_login'] : false,
            // 'fastLoginServer'     => isset($conf['shanyan_type']) ? $conf['shanyan_type'] : "1",
            'loginType' => $loginType,
        ];
        return $ret;
    }
    /**
     * 提取符合条件的登录方式
     * @param string $channel 渠道
     * @param string $version 版本号
     * @param string $conf 游客配置
     * @return array
     */
    public function filterLoginType($channel, $version, $conf)
    {
        $loginType = [];
        if (empty($conf['detail_data'])) {
            return $loginType;
        }
        // $judge = function($haystack, $needle) {
        //     $exist = true;
        //     if (!empty($haystack) && $haystack != 'all') {
        //         if ($haystack != '?0?2?0?7') {
        //             $arr = explode(',', $haystack);
        //             if (in_array($needle, $arr)) {
        //                 $exist = true;
        //             }
        //         } else {
        //             $exist = false;
        //         }
                
        //     }
        //     return $exist;
        // };
        foreach ($conf['detail_data'] as $key => $value) {
            $flag = true;
            $channelType = "";
            if (!empty($value['channelWhite'])) {
                $channelType = 'white';
                $channelWhite = explode(',', $value['channelWhite']);
                $channelWhite = array_flip($channelWhite);
                if (!array_key_exists($channel, $channelWhite)) {
                    $flag = false;
                }
            } elseif (!empty($value['channelBlack'])) {
                $channelType = 'black';
                $channelBlack = explode(',', $value['channelBlack']);
                $channelBlack = array_flip($channelBlack);
                if (array_key_exists($channel, $channelBlack)) {
                    $flag = false;
                }
            }

            if (!empty($value['versionWhite'])) {
                if ($channelType == 'white') {
                    if (!$flag) {
                        $versionWhite = explode(',', $value['versionWhite']);
                        $versionWhite = array_flip($versionWhite);
                        if (array_key_exists($version, $versionWhite)) {
                            $flag = true;
                        }
                    }
                } else {
                    $versionWhite = explode(',', $value['versionWhite']);
                    $versionWhite = array_flip($versionWhite);
                    if (array_key_exists($version, $versionWhite)) {
                        $flag = true;
                    } else {
                        $flag = false;
                    }
                }
            } elseif (!empty($value['versionBlack'])) {
                if ($channelType != 'white') {
                    $versionBlack = explode(',', $value['versionBlack']);
                    $versionBlack = array_flip($versionBlack);
                    if (!$flag || array_key_exists($version, $versionBlack)) {
                        $flag = false;
                    }
                }
            }

            if ($flag) {
                $loginType[] = $value['short_name'];
            }
        }

        return $loginType;
    }
}
