package main

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"strings"

	"login-service/utils"
)

func main() {
	// Set up environment
	os.Setenv("HTTP_HOST", "login.2345.com")
	defer os.Unsetenv("HTTP_HOST")

	fmt.Println("=== GetForwardUrl Function Demo ===")
	fmt.Println()

	// Test cases
	testCases := []struct {
		name        string
		method      string
		getParam    string
		postData    string
		description string
	}{
		{
			name:        "Valid 2345.com URL",
			method:      "GET",
			getParam:    "http://www.2345.com/search",
			description: "Valid URL from trusted domain",
		},
		{
			name:        "Valid hao774.com URL",
			method:      "GET",
			getParam:    "https://www.hao774.com/games",
			description: "Valid URL from hao774.com domain",
		},
		{
			name:        "Invalid external domain",
			method:      "GET",
			getParam:    "http://www.baidu.com",
			description: "External domain - should return default redirect",
		},
		{
			name:        "Invalid URL format",
			method:      "GET",
			getParam:    "not-a-valid-url",
			description: "Invalid URL format - should return empty",
		},
		{
			name:        "URL with spaces",
			method:      "GET",
			getParam:    "http://www.2345.com/search page",
			description: "URL with spaces - should be handled properly",
		},
		{
			name:        "URL with quotes",
			method:      "GET",
			getParam:    "http://www.2345.com/test\"'page",
			description: "URL with quotes - should be sanitized",
		},
		{
			name:        "POST parameter",
			method:      "POST",
			postData:    "forward=http://www.2345.cn/news",
			description: "Forward URL from POST data",
		},
		{
			name:        "Empty parameter",
			method:      "GET",
			getParam:    "",
			description: "Empty forward parameter",
		},
	}

	for i, tc := range testCases {
		fmt.Printf("%d. %s\n", i+1, tc.name)
		fmt.Printf("   Description: %s\n", tc.description)

		// Create request
		var req *http.Request
		if tc.method == "POST" {
			body := strings.NewReader(tc.postData)
			req = httptest.NewRequest("POST", "/", body)
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
		} else {
			requestURL := "/"
			if tc.getParam != "" {
				requestURL = "/?forward=" + url.QueryEscape(tc.getParam)
			}
			req = httptest.NewRequest("GET", requestURL, nil)
		}

		// Call the function
		result := utils.GetForwardUrl(req)

		// Display results
		fmt.Printf("   Input: %s\n", getInputDisplay(tc))
		fmt.Printf("   Result: %s\n", getResultDisplay(result))
		fmt.Printf("   Analysis: %s\n\n", analyzeResult(tc.getParam, tc.postData, result))
	}

	fmt.Println("=== Security Features Demo ===")
	fmt.Println()

	// Demonstrate security features
	securityTests := []struct {
		input       string
		description string
	}{
		{
			input:       "javascript:alert('xss')",
			description: "JavaScript injection attempt",
		},
		{
			input:       "http://malicious.com/steal-data",
			description: "Malicious external domain",
		},
		{
			input:       "http://www.2345.com/page\"onclick=alert(1)\"",
			description: "HTML injection with quotes",
		},
		{
			input:       "ftp://www.2345.com/file",
			description: "Non-HTTP protocol",
		},
	}

	for i, test := range securityTests {
		req := httptest.NewRequest("GET", "/?forward="+url.QueryEscape(test.input), nil)
		result := utils.GetForwardUrl(req)
		
		fmt.Printf("%d. %s\n", i+1, test.description)
		fmt.Printf("   Input: %s\n", test.input)
		fmt.Printf("   Result: %s\n", getResultDisplay(result))
		fmt.Printf("   Status: %s\n\n", getSecurityStatus(result))
	}
}

func getInputDisplay(tc struct {
	name        string
	method      string
	getParam    string
	postData    string
	description string
}) string {
	if tc.method == "POST" && tc.postData != "" {
		return fmt.Sprintf("POST %s", tc.postData)
	}
	if tc.getParam != "" {
		return fmt.Sprintf("GET forward=%s", tc.getParam)
	}
	return "GET (no forward parameter)"
}

func getResultDisplay(result string) string {
	if result == "" {
		return "(empty string)"
	}
	return result
}

func analyzeResult(getParam, postData, result string) string {
	input := getParam
	if postData != "" {
		// Extract forward value from POST data
		if strings.HasPrefix(postData, "forward=") {
			input = strings.TrimPrefix(postData, "forward=")
		}
	}

	if result == "" {
		if input == "" {
			return "✅ Correctly returned empty for no input"
		}
		return "✅ Invalid URL filtered out (security feature)"
	}

	if result == "http://www.2345.com" {
		return "✅ Redirected to default domain (untrusted domain blocked)"
	}

	if strings.Contains(result, "2345.com") || strings.Contains(result, "2345.cn") ||
		strings.Contains(result, "hao774.com") || strings.Contains(result, "7255.com") {
		return "✅ Valid trusted domain URL accepted"
	}

	return "ℹ️  URL processed and returned"
}

func getSecurityStatus(result string) string {
	if result == "" {
		return "🛡️  BLOCKED - Invalid or malicious URL filtered"
	}
	if result == "http://www.2345.com" {
		return "🛡️  REDIRECTED - Untrusted domain redirected to safe default"
	}
	return "⚠️  ALLOWED - URL passed validation"
}
