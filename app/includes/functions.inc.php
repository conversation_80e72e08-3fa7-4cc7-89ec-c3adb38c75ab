<?php

use Octopus\PdoEx;
use PHPMailer\PHPMailer\PHPMailer;

/**
 * 消息函数
 *
 * @param string $p_msg msg
 * @param string $p_mode mode
 */
function showMessage($p_msg, $p_mode)
{
    //ob_start();
    if ($p_mode == "close")
    {
        $cmd = "top.close();";
    }
    else
    {
        if ($p_mode == "back")
        {
            if (IsMobile())
            {
                $cmd = "location.href = '" . $_SERVER["HTTP_REFERER"] . "';";
            }
            else
            {
                $cmd = "history.go(-1)";
            }
        }
        else
        {
            $cmd = 'window.location.href = "' . $p_mode . '";';
        }
    }

    $a = '<script language="javascript">
            <!--
                alert("' . $p_msg . '");' . $cmd . '
            -->
                </script>';
    echo $a;
    //ob_end_flush();
    die;
}

function buildForm($action, $parma)
{
    echo '<form name="jsfm" id="jsfm" method="post" action="' . $action . '">';
    foreach ($parma as $key => $val)
    {
        if (is_array($val))
        {
            foreach ($val as $k1 => $v1)
            {
                $key2 = $key . '[' . $k1 . ']';
                $val = $v1;
                echo '<input type="hidden" name="' . $key2 . '" value="' . $val . '">';
            }
        }
        else
        {
            echo '<input type="hidden" name="' . $key . '" value="' . $val . '">';
        }
    }
    echo '</form>';
    echo '<script>';
    echo 'document.getElementById("jsfm").submit()';
    echo '</script>';
}

function getDomainType($url)
{
    $hostArr = parse_url($url);
    if (strpos($hostArr['host'], 'duote') !== false)
    {
        return 2;
    }
    else
    {
        return 1;
    }
}

function writeFile($p_fileBody, $p_filePath, $p_mode = "w")
{
    $fRs = fopen($p_filePath, $p_mode);
    fwrite($fRs, $p_fileBody);
    fclose($fRs);
    return true;
}

/**
 * 中文字符串截取
 *
 * @param type $p_Str
 * @param type $p_Len
 * @param type $p_vStr
 * @return type
 */
function cutSubStr($p_Str, $p_Len, $p_vStr = "")
{
    if (strlen($p_Str) <= $p_Len)
    {
        return $p_Str;
    }
    $ls_Str = "";
    $xi = 0;
    while ($xi < $p_Len)
    {
        $ls_Str .= $p_Str[$xi];
        if (ord($p_Str[$xi]) > 127)
        {
            $xi++;
            $ls_Str .= $p_Str[$xi];
        }
        $xi++;
    }
    return $ls_Str . $p_vStr;
}

/**
 * 服务端验证
 *
 * @param type $loginSid
 * @param type $loginVersion
 * @return type
 */
function checkLoginSession($loginSid, $loginVersion = '1.0')
{
    $options = array(
        CURLOPT_URL => "http://login.2345.com/api/checkSession",
        CURLOPT_HEADER => 0,
        CURLOPT_RETURNTRANSFER => 1,
        CURLOPT_USERAGENT => "login.2345.com",
        CURLOPT_CONNECTTIMEOUT => 1,
        CURLOPT_TIMEOUT => 1,
        CURLOPT_POST => 1,
        CURLOPT_POSTFIELDS => "domain=login.2345.com&sid=$loginSid&ver=$loginVersion",
    );
    $ch = curl_init();
    curl_setopt_array($ch, $options);
    $result = curl_exec($ch);
    curl_close($ch);
    return intval($result);
}

/**
 * 获取登录信息
 *
 * @return boolean
 */
function getLoginInfo()
{
    $loginInfo = array();
    if (isset($_COOKIE['I']))
    {
        $infos = explode('&', $_COOKIE['I']);
        foreach ($infos as $info)
        {
            $info = explode("=", $info);
            if ($info[0] == 'n')
            {
                $loginInfo[$info[0]] = urldecode($info[1]);
            }
            else
            {
                $loginInfo[$info[0]] = $info[1];
            }
        }
        if (md5($loginInfo['i'] . $loginInfo['u'] . $loginInfo['n'] . $loginInfo['t'] . SESSIONKEY) == $loginInfo['s'])
        {
            return $loginInfo;
        }
    }
    return false;
}

/**
 *
 * @param $string
 * @param string $operation
 * @param string $key
 * @param int $expiry
 * @return string
 * <AUTHOR>
 */
function uc_authcode($string, $operation = 'DECODE', $key = '', $expiry = 0)
{

    if ($operation == 'DECODE')
    {
        $string = base64_decode($string);
    }

    $ckey_length = 4; //note 随机密钥长度 取值 0-32;
    //note 加入随机密钥，可以令密文无任何规律，即便是原文和密钥完全相同，加密结果也会每次不同，增大破解难度。
    //note 取值越大，密文变动规律越大，密文变化 = 16 的 $ckey_length 次方
    //note 当此值为 0 时，则不产生随机密钥

    $key = md5($key);
    $keya = md5(substr($key, 0, 16));
    $keyb = md5(substr($key, 16, 16));
    $keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length) : substr(md5(microtime()), -$ckey_length)) : '';

    $cryptkey = $keya . md5($keya . $keyc);
    $key_length = strlen($cryptkey);

    $string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
    $string_length = strlen($string);

    $result = '';
    $box = range(0, 255);

    $rndkey = array();
    for ($i = 0; $i <= 255; $i++)
    {
        $rndkey[$i] = ord($cryptkey[$i % $key_length]);
    }

    for ($j = $i = 0; $i < 256; $i++)
    {
        $j = ($j + $box[$i] + $rndkey[$i]) % 256;
        $tmp = $box[$i];
        $box[$i] = $box[$j];
        $box[$j] = $tmp;
    }

    for ($a = $j = $i = 0; $i < $string_length; $i++)
    {
        $a = ($a + 1) % 256;
        $j = ($j + $box[$a]) % 256;
        $tmp = $box[$a];
        $box[$a] = $box[$j];
        $box[$j] = $tmp;
        $result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
    }

    if ($operation == 'DECODE')
    {
        if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26) . $keyb), 0, 16))
        {
            return substr($result, 26);
        }
        else
        {
            return '';
        }
    }
    else
    {
        return base64_encode($keyc . str_replace('=', '', base64_encode($result)));
    }
}

/**
 *
 * @param $str
 * @return string
 * <AUTHOR>
 */
function str2unicode($str)
{
    $ret = "";
    $len = mb_strlen($str, "GBK");
    for ($i = 0; $i < $len; $i++)
    {
        $char = mb_substr($str, $i, 1, "GBK");
        if (!(strlen($char) > 1))
        {
            $dec = ord($char);
            if ($dec > 16)
            {
                $ret .= "%" . $dec;
            }
            else
            {
                $ret .= "%" . $dec;
            }
        }
        else
        {
            $temp = base_convert(bin2hex(iconv("GBK", "UTF-8", $char)), 16, 2);
            $temp = substr($temp, 4, 4) . substr($temp, 10, 6) . substr($temp, 18);
            $ret .= "%" . hexdec(strtoupper(base_convert($temp, 2, 16)));
        }
    }
    return $ret;
}

/**
 *
 * @param $redis
 * @return mixed
 * <AUTHOR>
 */
function getAllServerIps(&$redis)
{
    $apcuExpire = $_ENV['APCU_IP_LIST_EXPIRE'] ?? 600;   //默认10分钟
    if (function_exists('apcu_enabled') && apcu_enabled()) {
        $key = "IP:WHITE:LIST";
        $apcuCache = apcu_fetch($key);
        if (!empty($apcuCache) && is_array($apcuCache)) {

            return $apcuCache;
        } else {
            $ipList = call_user_func_array(array(&$redis, "sUnion"), Config::get("domainServerIPs"));
            if (!empty($ipList) && is_array($ipList)) {
                $isStore = apcu_store($key, $ipList, $apcuExpire);
                if (!$isStore) {
                    WebLogger\Facade\LoggerFacade::error("apcu设置ip白名单缓存失败");
                }
            }

            return $ipList;
        }
    } else {
        return call_user_func_array(array(&$redis, "sUnion"), Config::get("domainServerIPs"));
    }
}

function get_sec_browser($uid)
{
    $key = md5(SECKEY);
    $t = time();
    $d = md5(date("dYmiHsi", $t)); //加密时间戳,保证每次登陆得到的加密串是不一样的.
    $str = $key . $uid . $d;
    return $d . '|' . $t . '|' . md5($str);
}

function array_mb_convert_encoding($array, $to_encoding, $from_encoding)
{
    $result = array();
    foreach ($array as $key => $value)
    {
        if (is_array($value))
        {
            $result[is_string($key) ? mb_convert_encoding($key, $to_encoding, $from_encoding) : $key] = array_mb_convert_encoding($value, $to_encoding, $from_encoding);
        }
        else
        {
            $result[is_string($key) ? mb_convert_encoding($key, $to_encoding, $from_encoding) : $key] = is_string($value) ? mb_convert_encoding($value, $to_encoding, $from_encoding) : $value;
        }
    }
    return $result;
}

function getPagination($page, $limit, $total, $search)
{
    $pageLink = str_replace('index.php/', '', $_SERVER['PHP_SELF']) . "?page={{page}}";
    if (!empty($search) && is_array($search))
    {
        foreach ($search as $key => $val)
        {
            $pageLink .= "&$key=$val";
        }
    }
    $totalPages = 0;
    if ($total > 0)
    {
        $totalPages = intval(ceil($total / $limit));
    }
    $page = min($totalPages, $page);
    $totalRecords = $total;
    if ($totalPages <= 1)
    {
        return;
    }
    $prep = max(1, ($page - 1));
    $nextp = min($totalPages, ($page + 1));
    $firstLink = str_replace("{{page}}", "1", $pageLink);
    $lastLink = str_replace("{{page}}", $totalPages, $pageLink);
    $preLink = str_replace("{{page}}", $prep, $pageLink);
    $nextLink = str_replace("{{page}}", $nextp, $pageLink);
    $str = "共" . $totalRecords . "条记录&nbsp;";
    $str .= "<a target='_self' href='$firstLink'>首页</a>&nbsp;";
    if ($page > 1)
    {
        $str .= "<a target='_self' href='$preLink'>上一页</a>&nbsp;";
    }
    if ($totalPages > 1)
    {
        $n = $totalPages;
        $istart = $page - 3;
        $istart = max($istart, 1);
        $iend = $istart + 10;
        $iend = min($iend, $n);
        if (($iend - $istart) < 10)
        {
            $istart = $iend - 10;
            $istart = max($istart, 1);
        }
        for ($i = $istart; $i <= $iend; $i++)
        {
            $bookLink = str_replace("{{page}}", $i, $pageLink);
            $a = "href='$bookLink'";
            if ($i == $page)
            {
                $a = "href='###' class='page_a'";
            }
            $str .= "<a $a target='_self'>" . $i . "</a>&nbsp;";
        }
    }
    else
    {
        $str .= "<a href='#' target='_self' class='page_a'>1</a>&nbsp;";
    }
    if ($page < $totalPages)
    {
        $str .= "<a target='_self' href='$nextLink'>下一页</a>&nbsp;";
    }
    $str .= "<a target='_self' href='$lastLink'>最后一页</a>&nbsp;";
    return $str;
}

/**
 * 获取过滤后的跳转地址
 * @return string
 */
function getForwardUrl()
{
    $inputForward = !empty($_POST['forward']) ? $_POST['forward'] : (!empty($_GET['forward']) ? $_GET['forward'] : '');
    if (preg_match("/\s/", $inputForward)) {
        $inputForward = str_replace(" ", "%20", $inputForward);
    }
    $inputForward = filter_var($inputForward, FILTER_VALIDATE_URL);
    if (!empty($inputForward)) {
        $forward = str_replace('%20', ' ', filter_var($inputForward, FILTER_VALIDATE_URL));
    } else {
        $forward = "";
    }
    $forward = str_replace(array("\"", "'"), "", $forward);
    if ($forward != '' &&
        !preg_match("/^(http|https):\/\/[^\/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|hao774\.com|7255\.com|km\.com|2345\.cc|2345\.com\.cn)$/", $forward) &&
        !preg_match("/^(http|https):\/\/[^\/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|hao774\.com|7255\.com|km\.com|2345\.cc|2345\.com\.cn)(\/|\?)/", $forward))
    {
        $forward = "http://www." . DOMAIN;
    }
//    if ($_COOKIE['is_need_redirect'] == 1)
//    {
//        $forward = $_COOKIE['redirect_url'];
//    }
    return $forward;
}

/**
 * 发送过滤
 *
 * @param string $key redis的key
 * @param int $mNum 每分钟的发送个数
 * @param int $hNum 每小时的发送个数
 * @param int $dNum 每天的发送个数
 * @return int
 */
function sendTimeFilter($key, $mNum = 2, $hNum = 15, $dNum = 50)
{
    /**
     * 公司IP白名单
     */

    if ($key == "send:email:180_168_34_146" || $key == "send:phone:180_168_34_146" || $key == "send:voice:180_168_34_146"
        || $key == "send:email:116_228_6_140" || $key == "send:phone:116_228_6_140" || $key == "send:voice:116_228_6_140"
        || $key == "send:email:180_167_67_10" || $key == "send:phone:180_167_67_10" || $key == "send:voice:180_167_67_10"
        || $key == "send:phone:128_1_133_204" || $key == "send:phone:128_1_133_196")
    {
        return true;
    }
    /**
     * 公司IP白名单
     */
    if (\Service\Security\Server::checkCompanyIp(get_client_ip()))
    {
        return true;
    }

    $nowTime = time();
    $redis = RedisEx::getInstance();
    $sendTimeRange = $redis->lRange($key, 0, $dNum - 1);
    if ($sendTimeRange)
    {
        if (isset($sendTimeRange[$dNum - 1]))
        {
            if (($nowTime - $sendTimeRange[$dNum - 1]) < 86400)
            {
                return false;
            }
        }
        if (isset($sendTimeRange[$hNum - 1]))
        {
            if (($nowTime - $sendTimeRange[$hNum - 1]) < 3600)
            {
                return false;
            }
        }
        if (isset($sendTimeRange[$mNum - 1]))
        {
            if (($nowTime - $sendTimeRange[$mNum - 1]) < 60)
            {
                return false;
            }
        }
    }
    $redis->lPush($key, $nowTime);
    $redis->lTrim($key, 0, $dNum - 1);
    $redis->expireAt($key, $nowTime + 86400);
    return true;
}

/**
 * 通过email发送验证码
 *
 * @param string $email email
 * @param string $code code
 * @return type
 */
function sendCodeFromEmail($email, $code)
{
    $ip = get_client_ip();
    $ipKey = "send:email:" . str_replace(".", "_", $ip);
    if (!sendTimeFilter($ipKey))
    {
        return 2;
    }
    $emailKey = "send:email:" . str_replace(".", "_", $email);
    if (!sendTimeFilter($emailKey))
    {
        return 2;
    }
    $smtpConfig = Config::get('smtp');

    $mail = new PHPMailer(true);
    //        $mail->SMTPDebug = SMTP::DEBUG_SERVER;                      // Enable verbose debug output
    $mail->isSMTP();                                            // Send using SMTP
    $mail->Host = $smtpConfig['server'];                    // Set the SMTP server to send through
    $mail->SMTPAuth = true;                                   // Enable SMTP authentication
    $mail->Username = $smtpConfig['username'];                     // SMTP username
    $mail->Password = $smtpConfig['password'];
    $mail->CharSet = "GBK";   // 这里指定字符集！
    $mail->Encoding = "base64";
    $mail->SMTPAutoTLS = false;
    $mail->Port = $smtpConfig['port'];
    $mail->SMTPSecure = "ssl";
    $mail->setFrom($smtpConfig['email'], $smtpConfig['email']);
    $mail->addAddress($email, $email);     //Add a recipient
//    $mail->isHTML(true);                                  //Set email format to HTML
    $mail->Subject = "2345网址导航用户中心邮箱验证服务";
    $mail->Body    = "亲爱的用户：您好！感谢您使用2345服务，您正在进行邮箱验证，本次请求的验证码为：$code(为了保障您帐号的安全性，请在半小时内完成验证。)";
//    $mail->AltBody = 'This is the body in plain text for non-HTML mail clients';
    $isSend = $mail->send();
    if ($isSend) {
        return 1;
    } else {
        return 0;
    }
}

/**
 * 新短信发送验证码方法
 * -
 * @param int $phone 手机号码
 * @param string $code code
 * @param string $sysType 发送短信类型
 * @param string $positionId 短信发送位置
 * @param string $mid 用户中心唯一标识
 * @param array $smsFilterConfig 发送短信频率限制配置
 * @param string $smsMid 短信中心mid
 * @return int
 * <AUTHOR>
 */
function sendCodeUsePhone($phone, $code, $sysType, $positionId, $mid = 'login', $smsFilterConfig = array(), $smsMid = '')
{
    if ('' == $smsMid) {
        $smsMid = $mid;
    }
    $mNum = isset($smsFilterConfig['mNum']) ? (int)$smsFilterConfig['mNum'] : 2;
    $hNum = isset($smsFilterConfig['hNum']) ? (int)$smsFilterConfig['hNum'] : 15;
    $dNum = isset($smsFilterConfig['dNum']) ? (int)$smsFilterConfig['dNum'] : 50;
    $ip = get_client_ip();
    $ipKey = "send:phone:" . str_replace(".", "_", $ip);
    if (!sendTimeFilter($ipKey, $mNum, $hNum, $dNum))
    {
        xLog('sendTimeFilter1', 'sendTimeFilter1', $ipKey . '---' . sendTimeFilter($ipKey, $mNum, $hNum, $dNum));
        return 2;
    }

    /** 公司IP白名单 */
    if (!\Service\Security\Server::checkCompanyIp($ip))
    {
        $phoneKey = "send:phone:" . str_replace(".", "_", $phone);
        if (!sendTimeFilter($phoneKey, $mNum, $hNum, $dNum))
        {
            xLog('sendTimeFilter2', 'sendTimeFilter2', $phoneKey . '---' . sendTimeFilter($phoneKey, $mNum, $hNum, $dNum));
            return 2;
        }
    }
    $txt = "您的验证码是：{$code}，请勿告诉他人，半小时内有效！";
    if (in_array($mid, array("JF", "SJLM", "iossjlm", "andsjlm")) || in_array($positionId, array(164, 206)))
    {
        // 快捷登录 200 webapi 162 clientapi 206 mobile
        if ($positionId == 200 || $positionId == 162 || $positionId == 206)
        {
            $txt = "欢迎回来，您的验证码是：{$code}，请勿告诉他人，半小时内有效！";
        }
        // 快捷注册 201 webapi 199 clientapi 164 mobile
        elseif ($positionId == 201 || $positionId == 199 || $positionId == 164)
        {
            $txt = "感谢注册，您的验证码是：{$code}，请勿告诉他人，半小时内有效! ";
        }
    }
    elseif (($mid == "login" || $mid == "loginReg") && in_array($positionId, array(149, 167, 152, 153, 154, 155, 159)))
    {
        $txt = "您的验证码是：{$code}，请勿告诉他人，半小时内有效。";
    }
    $tmpMid = $smsMid == 'XCTEST' ? 'TEST' : $smsMid;
    if (in_array($mid, array('ioszyxq', 'andzyxq')))
    {
        //章鱼兴修短信类别变更为131
        $sysType = 131;
        //章鱼兴修特定文案
        $txt = "您的验证码是：{$code}，请勿告诉他人，30分钟内有效。";
    }
    elseif (in_array($mid, array('XQ', 'andxqlm', 'iosxqlm')))
    {
        //兴修连梦
        $sysType = '168';
        $txt = "您的验证码是：{$code}，请勿告诉他人，半小时内有效。";
    }
    elseif ($mid == 'andgwb')
    {
        $sysType = '186';
        $txt = "您的验证码是：{$code}，请勿告诉他人，半小时内有效。";
    }
    if ($sysType == 285 && $positionId == 465) {
        $txt = "验证码：{$code}（15分钟内有效，请勿泄露）。您正在注销2345账号，注销后将无法登录2345旗下所有账号，请谨慎操作。";
        if ($mid == 'JSQNLLQ') {
            $txt = "验证码:{$code}, 您正在使用短信验证码注销青鸟浏览器账号，请勿转发或泄露。";
        }
    }

    getSMSText($code, $mid, $sysType, $txt);
    if ($mid == 'JSQNLLQ') {
        $sysType = 287;
        $positionId = 249;
    }

    $params = array(
        'phone' => $phone, // 手机号码
        'msg' => $txt, // 短信内容
        'smsType' => $sysType, // 短信类别（固定值，1为验证码类，2为通知类）
        'pid' => 14,
        'clientIp' => $ip,
        'positionId' => $positionId, // 业务类别（固定值14）
        'mid' => $tmpMid,
        'useQueue' => ($mid == 'XQ' || $mid == 'XCTEST' ? 1 : 0),
    );
    RedisEx::delInstance();
    PdoEx::delInstance(DB_PASSPORT);
    $info = json_decode(http_post(SMS_CENTER_DOMAIN . "/Api/Sms/Send", $params), true);
    // 添加预警埋点
    $code = (isset($info['status']) && $info['status'] == 1) ? $info['status'] : 0;
    loadAction('Warning')->record([
        'dir_type' => "w_sms",
        'type' => "sms",
        'code' => $code,
        'status' => $code == 1 ? true : false,
    ]);

    if ($mid == 'XQ' || $tmpMid == 'TEST')
    {
        if (empty($info) || !is_array($info) || !isset($info['status']))
        {
            return 3; //短信服务器出错
        }
        else
        {
            xLog('smsp', 'smsp', json_encode($params) . json_encode($info));
            //其他code 都认为是发送频繁
            return intval($info['status']) > 0 ? 1 : 2;
        }
    }
    else
    {
        if ($info['status'] != '1')
        {
            return 2;
        }
        else
        {
            return $info['status'];
        }
    }
}

/**
 * getSMSText
 * -
 * @param string $code Code
 * @param string $mid Mid
 * @param int $sysType 短信类型
 * @param string $txt 短信内容
 * @return void
 * <AUTHOR>
 */
function getSMSText($code, $mid, &$sysType, &$txt)
{
    if ($mid == 'TGPT') {
        //易品平台
        $sysType = 153;
        $txt = "您的验证码是：{$code}，请勿告诉他人，半小时内有效。";
    } elseif ($mid == 'andgsq' || $mid == 'iosgsq') {
        //购省钱
        $sysType = 154;
        $txt = "您的验证码是：{$code}，30分钟内有效。";
    } elseif (in_array($mid, array('andwnl'))) {
        $sysType = '176';
        $txt = "您的验证码是：{$code}，请勿告诉他人，半小时内有效。";
    } elseif ($mid == "andllqoem") {
        $sysType = '185';
        $txt = "您的验证码是：{$code}，请勿告诉他人，半小时内有效。";
    } elseif ($mid == "pcbs") {
        $txt = "您的验证码是：{$code}，半小时内有效，请勿告诉他人噢~";
    } elseif ($mid == "KALLQ") {
        $sysType = '283';
    } elseif ($mid == "WZLYAPP") {
        $sysType = '282';
    } elseif ($mid == "JF") {
        $sysType = '301';
        $txt = "您的王牌技术员联盟的验证码为 $code 请勿告诉他人, 15分钟内有效!";
    }
}

/**
 * 通过手机号发送语音验证码
 * @param type $phone
 * @param type $code
 */
function sendCodeFromVoice($phone)
{
    $ip = get_client_ip();
    $ipKey = "send:voice:" . str_replace(".", "_", $ip);
    if (!sendTimeFilter($ipKey))
    {
        return array(2);
    }
    $voiceKey = "send:voice:" . str_replace(".", "_", $phone);
    if (!sendTimeFilter($voiceKey, 2, 3, 6))
    {
        return array(2);
    }
    $result = http_get("http://voice.2345.cn/index.php?passid=0&from=login&phone=$phone");
    $result = json_decode($result, true);
    if ($result['status'] == 200)
    {
        return array(1, $result['code']);
    }
    else
    {
        if ($result['status'] == -3 || $result['status'] == -4)
        {
            //来源被禁止或服务器IP被禁止
            //$redis = RedisEx::getInstance();
            //$redis->del('VoiceCodeStatus');
            return array(2);
        }
        else
        {
            return array(2);
        }
    }
}

/**
 * 并发curl请求
 * @param type $requests
 */
function rollingCurl($requests)
{
    PdoEx::delInstance(DB_PASSPORT);
    RedisEx::delInstance();
    $queue = curl_multi_init();
    $map = array();
    foreach ($requests as $request)
    {
        $ch = curl_init();
        $default = array(
            CURLOPT_URL => $request['url'],
            CURLOPT_HEADER => 0,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_SSL_VERIFYPEER => 0,
            CURLOPT_SSL_VERIFYHOST => 0,
            CURLOPT_USERAGENT => "Mozilla/5.0 (Windows NT 6.1; rv:17.0) Gecko/17.0 Firefox/17.0",
            CURLOPT_CONNECTTIMEOUT => 2,
            CURLOPT_TIMEOUT => 3,
            CURLOPT_NOSIGNAL => true,
        );
        foreach ($request['options'] as $key => $value)
        {
            $default[$key] = $value;
        }
        curl_setopt_array($ch, $default);
        curl_multi_add_handle($queue, $ch);
        $map[(string) $ch] = $request;
    }
    do
    {
        while (($code = curl_multi_exec($queue, $active)) == CURLM_CALL_MULTI_PERFORM);
        if ($code != CURLM_OK)
        {
            break;
        }
        while ($done = curl_multi_info_read($queue))
        {
            $info = curl_getinfo($done['handle']);
            $error = curl_error($done['handle']);
            $results = curl_multi_getcontent($done['handle']);
            $request = $map[(string) $done['handle']];
            $params = $request['callbackParams'];
            $params[] = $results;
            $params[] = $error;
            $params[] = $info;
            call_user_func_array($request['callback'], $params);
            curl_multi_remove_handle($queue, $done['handle']);
            curl_close($done['handle']);
        }
        if ($active > 0)
        {
            curl_multi_select($queue);
        }
    }
    while ($active);
    curl_multi_close($queue);
}

/**
 * 解绑或修改手机号时,同时删除redis中该手机号码
 * -
 * @param string $phone Phone
 * @return mixed
 * <AUTHOR>
 */
function changeRegPhoneRedis($phone)
{
    $memberRedis = loadAction('memberRedis');
    $changeResult = $memberRedis->hashDel($phone, REG_PHONE_INFO_REDIS_KEY, $phone);
    $memberRedis->closeUserDataRedis();
    return $changeResult;
}

/**
 * existsRegPhoneRedis
 * -
 * @param string $phone Phone
 * @return mixed
 * <AUTHOR>
 */
function existsRegPhoneRedis($phone)
{
    $memberRedis = loadAction('memberRedis');
    return $memberRedis->hashHExists($phone, REG_PHONE_INFO_REDIS_KEY, $phone);
}

/**
 * 通知修改信息
 *
 * @param int $passid passid
 * @param string $noticeType noticeType
 * @param mixed $params params
 *
 * @return void
 */
function noticeToChange($passid, $noticeType = "changeUsername", $params = array())
{
//    if (RUNMODE != 'production')
//    {
//        return;
//    }
    $pushData = array(
        'passid' => $passid,
        'noticeType' => $noticeType,
        'params' => $params
    );
    $noticeHandle = loadAction('NoticeHandle');
    //获取当前回调使用回调方式
    $useNoticeType = $noticeHandle->useNoticeType($noticeHandle->getUseRedisNoticeKey());
    // 1 使用redis 2使用mysql 5.7新数据库  如果为空或不是1和2，则使用老数据库
    WebLogger\Facade\LoggerFacade::Info('推送排查', ['useNoticeType' => $useNoticeType]);
    if ($useNoticeType == 1)
    {
        //使用 redis
        $ret = $noticeHandle->setNoticeList($pushData);
        WebLogger\Facade\LoggerFacade::Info('推送排查', ['pushData' => $pushData, 'ret' => $ret]);
        return;
    }
    elseif ($useNoticeType == 2)
    {
        //5.7数据
        $noticeHandle->handleData($pushData);
        return;
    }
    $dbConfig = Config::get("database");
    $pdo = PdoEx::getInstance(DB_PASSPORT, $dbConfig[DB_PASSPORT]);
    $update = array('notice_status' => 2);
    $condition = array(
        'where' => 'passid = :passid AND notice_type = :notice_type AND notice_status = :notice_status',
        'params' => array(
            ':passid' => $passid,
            ':notice_type' => $noticeType,
            ':notice_status' => 0
        )
    );
    $pdo->update('notification_schedule', $update, $condition);
    $requests = array();
    switch($noticeType)
    {
        case "changeEmail":
            $apiUrls = Config::get('noticeChangeEmail');
            break;
        case "changePhone":
            $apiUrls = Config::get("noticeChangePhone");
            break;
        case "changeNickname":
            $apiUrls = Config::get("noticeChangeNickname");
            break;
        case "changePassword":
            $loginAction = loadAction('login');
            $loginAction->clearLoginSessionByPassId($passid);
            $apiUrls = Config::get("noticeChangePassword");
            break;
        case "unbindQQ":
            $loginAction = loadAction('login');
            $loginAction->clearLoginSessionByPassId($passid);
            $apiUrls = Config::get("noticeUnBindQQ");
            break;
        case "changeLogout":
            $apiUrls = Config::get("noticeChangeLogout");
            break;
        default:
            $apiUrls = Config::get("noticeChangeUser");
            break;
    }
    foreach ($apiUrls as $apiUrl)
    {
        $pdo->insert('notification_schedule', array(
            'passid' => $passid,
            'notice_type' => $noticeType,
            'api_url' => $apiUrl,
            'params' => serialize($params)
        ), false);
        $noticeId = $pdo->lastInsertId();
        $options = array();
        $paramsFMT = array();
        foreach ($params as $key => $val)
        {
            $paramsFMT[] = $key . "=" . urlencode($val);
        }
        $options[CURLOPT_POST] = 1;
        $options[CURLOPT_POSTFIELDS] = join("&", $paramsFMT);
        $requests[] = array(
            'url' => $apiUrl,
            'options' => $options,
            'callback' => 'noticeToChangeCallback',
            'callbackParams' => array($noticeId)
        );
    }
    rollingCurl($requests);
}

/**
 * 通知修改信息回调
 * @param type $noticeId
 * @param type $results
 * @param type $error
 * @param type $info
 */
function noticeToChangeCallback($noticeId, $results, $error, $info)
{
    $dbConfig = Config::get("database");
    $pdo = PdoEx::getInstance(DB_PASSPORT, $dbConfig[DB_PASSPORT]);
    $condition = array(
        'where' => 'id = :id',
        'params' => array(
            ':id' => $noticeId,
        )
    );
    if (noticeCallbackSuccess($results) === true)
    {
        $update = array('notice_status' => 1);
    }
    else
    {
        $update = array(
            'notice_error' => "$error:$results",
            'notice_info' => serialize($info)
        );
    }
    $pdo->update('notification_schedule', $update, $condition);
}

/**
 * noticeCallbackSuccess
 * -
 * @param object $result Result
 * @return bool
 * <AUTHOR>
 */
function noticeCallbackSuccess($result)
{
    $tmpResult = trim($result);
    if (!empty($tmpResult))
    {
        if ($tmpResult == 'success')
        {
            return true;
        }
        else
        {
            $decodeResult = json_decode($tmpResult, true);
            if (is_array($decodeResult) && (isset($decodeResult['code']) || isset($decodeResult['msg'])))
            {
                if ($decodeResult['code'] == 200 || $decodeResult['msg'] == 'success')
                {
                    return true;
                }
            }
        }
    }
    return $tmpResult;
}

/**
 * fsockPostOnly
 * @param type $url
 * @return boolean
 */
function fsockPostOnly($url, $post)
{
    extract(parse_url($url));
    $postFMT = array();
    foreach ($post as $key => $value)
    {
        $postFMT[] = $key . "=" . urlencode($value);
    }
    $query = implode("&", $postFMT);
    $fp = fsockopen($host, 80, $errno, $errstr, 3);
    if (!$fp)
    {
        return false;
    }
    stream_set_blocking($fp, 0);
    $header = "POST $path HTTP/1.0\r\n";
    $header .= "Host: $host\r\n";
    $header .= "Content-type: application/x-www-form-urlencoded\r\n";
    $header .= "Content-Length: " . strlen(trim($query)) . "\r\n";
    $header .= "Connection: Close\r\n\r\n";
    $header .= trim($query);
    fwrite($fp, $header);
    fclose($fp);
}

/**
 * 递归创建目录
 * @param type $target
 * @return boolean
 */
function deepMkdir($pathname)
{
    if (!is_dir($pathname))
    {
        deepMkdir(dirname($pathname));
        @mkdir($pathname);
    }
}

/**
 * 判断是否是手机访问
 *
 * @param
 * @param
 */
function IsMobile()
{
    $_SERVER['ALL_HTTP'] = isset($_SERVER['ALL_HTTP']) ? $_SERVER['ALL_HTTP'] : '';
    $mobile_browser = '0';
    if (preg_match('/(up.browser|up.link|mmp|symbian|smartphone|midp|wap|phone|iphone|ipad|ipod|android|xoom)/i', strtolower($_SERVER['HTTP_USER_AGENT'])))
    {
        $mobile_browser++;
    }
    if ((isset($_SERVER['HTTP_ACCEPT'])) and (strpos(strtolower($_SERVER['HTTP_ACCEPT']), 'application/vnd.wap.xhtml+xml') !== false))
    {
        $mobile_browser++;
    }
    if (isset($_SERVER['HTTP_X_WAP_PROFILE']))
    {
        $mobile_browser++;
    }
    if (isset($_SERVER['HTTP_PROFILE']))
    {
        $mobile_browser++;
    }
    $mobile_ua = strtolower(substr($_SERVER['HTTP_USER_AGENT'], 0, 4));
    $mobile_agents = array(
        'w3c ', 'acs-', 'alav', 'alca', 'amoi', 'audi', 'avan', 'benq', 'bird', 'blac', 'blaz', 'brew', 'cell', 'cldc', 'cmd-', 'dang', 'doco', 'eric', 'hipt', 'inno', 'ipaq', 'java', 'jigs', 'kddi', 'keji', 'leno', 'lg-c', 'lg-d', 'lg-g', 'lge-', 'maui', 'maxo', 'midp', 'mits', 'mmef', 'mobi', 'mot-', 'moto', 'mwbp', 'nec-', 'newt', 'noki', 'oper', 'palm', 'pana', 'pant', 'phil', 'play', 'port', 'prox', 'qwap', 'sage', 'sams', 'sany', 'sch-', 'sec-', 'send', 'seri', 'sgh-', 'shar', 'sie-', 'siem', 'smal', 'smar', 'sony', 'sph-', 'symb', 't-mo', 'teli', 'tim-', 'tosh', 'tsm-', 'upg1', 'upsi', 'vk-v', 'voda', 'wap-', 'wapa', 'wapi', 'wapp', 'wapr', 'webc', 'winw', 'winw', 'xda', 'xda-'
    );
    if (in_array($mobile_ua, $mobile_agents))
    {
        $mobile_browser++;
    }
    if (strpos(strtolower($_SERVER['ALL_HTTP']), 'operamini') !== false)
    {
        $mobile_browser++;
    }
    // Pre-final check to reset everything if the user is on Windows
    if (strpos(strtolower($_SERVER['HTTP_USER_AGENT']), 'windows') !== false)
    {
        $mobile_browser = 0;
    }
    // But WP7 is also Windows, with a slightly different characteristic
    if (strpos(strtolower($_SERVER['HTTP_USER_AGENT']), 'windows phone') !== false)
    {
        $mobile_browser++;
    }
    if ($mobile_browser > 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}

/**
 * 检测浏览器
 * @param $ua
 * @return null|string
 * <AUTHOR>
 */
function detectBrowser($ua)
{
    $browserName = null;
    $ua = preg_replace("/FunWebProducts/i", "", $ua);
    if (preg_match('#CometBrowser/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '彗星浏览器';
    }
    elseif (preg_match('#(Firefox|Phoenix|Firebird|BonEcho|GranParadiso|Minefield|Iceweasel)/4([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '火狐浏览器';
    }
    elseif (preg_match('#(Firefox|Phoenix|Firebird|BonEcho|GranParadiso|Minefield|Iceweasel)/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '火狐浏览器';
    }
    elseif (preg_match('#Minimo/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Minimo';
    }
    elseif (preg_match('#MultiZilla/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'MultiZilla';
    }
    elseif (preg_match('#SE 2([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '搜狗浏览器';
    }
    elseif (preg_match('#baidubrowser ([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '百度浏览器';
    }
    elseif (preg_match('#BIDUBrowser ([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '百度浏览器';
    }
    elseif (preg_match('#360(.*)\(version(\s*)([0-9\.]+)\)$#isU', $ua, $matches))
    {
        $browserName = '360手机浏览器';
    }
    elseif (preg_match('#360([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '360安全浏览器';
    }
    elseif (preg_match('#MQQBrowser/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '手机QQ浏览器';
    }
    elseif (preg_match('#QQBrowser/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'QQ浏览器';
    }
    elseif (preg_match('#TheWorld#i', $ua, $matches))
    {
        $browserName = '世界之窗浏览器';
    }
    elseif (preg_match('#LBBROWSER#i', $ua, $matches))
    {
        $browserName = '猎豹浏览器';
    }
    elseif (preg_match('#ChromePlus/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '枫树极速浏览器';
    }
    elseif (preg_match('#CoolNovo/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '枫树极速浏览器';
    }
    elseif (preg_match('#BIDUBrowser/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '百度浏览器';
    }
    elseif (preg_match('#JuziBrowser#i', $ua, $matches))
    {
        $browserName = '桔子浏览器';
    }
    elseif (preg_match('#UCBrowser/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'UC浏览器';
    }
    elseif (preg_match('#UBrowser/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'UC浏览器';
    }
    else if (strpos($ua, 'QingniaoChrome') !== false)
    {
        $browserName = '青鸟浏览器';
    }
    elseif (preg_match('#OPR/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Opera Next浏览器';
    }
    elseif (preg_match('#KChrome/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '快快浏览器';
    }
    elseif (preg_match('#AirView([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '云游浏览器';
    }
    elseif (preg_match('#ChaoSu([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '超速浏览器';
    }
    elseif (preg_match('#TaoBrowser/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '淘宝浏览器';
    }
    elseif (preg_match('#Alibrowser ([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '阿云浏览器';
    }
    elseif (preg_match('#Silk/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'PSVita Network';
    }
    elseif (preg_match('#SaaYaa#i', $ua, $matches))
    {
        $browserName = '闪游浏览器';
    }
    elseif (preg_match('#2345Explorer#i', $ua, $matches))
    {
        #$browserName = '2345王牌浏览器';
        $browserName = '2345加速浏览器';
    }
    elseif (preg_match('#2345Chrome#i', $ua, $matches))
    {
        $browserName = '2345加速浏览器';
    }
    elseif (preg_match('#Browser2345-iPhone#i', $ua, $matches))
    {
        $browserName = '2345手机浏览器';
    }
    elseif (preg_match('#Mb2345Browser/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '2345手机浏览器';
    }
    elseif (preg_match('#Edge/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '微软Edge浏览器';
    }
    elseif (preg_match('#Maxthon\/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '傲游浏览器';
    }
    elseif (preg_match('#Maxthon ([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '傲游浏览器';
    }
    elseif (preg_match('#Galeon/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Galeon';
    }
    elseif (preg_match('#iCab/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'iCab';
    }
    elseif (preg_match('#K-Meleon/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'K-Meleon';
    }
    elseif (preg_match('#Lynx/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Lynx';
    }
    elseif (preg_match('#Links \\(([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Links';
    }
    elseif (preg_match('#ELinks[/ ]([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'ELinks';
    }
    elseif (preg_match('#ELinks \\(([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'ELinks';
    }
    elseif (preg_match('#Konqueror/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Konqueror';
    }
    elseif (preg_match('#NetPositive/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'NetPositive';
    }
    elseif (preg_match('#OmniWeb#i', $ua))
    {
        $browserName = 'OmniWeb';
    }
    elseif (preg_match('#Chrome/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '谷歌浏览器';
    }
    elseif (preg_match('#Arora/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Arora';
    }
    elseif (preg_match('#CriOS/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '谷歌浏览器iOS';
    }
    elseif (preg_match('#Safari/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Safari';
    }
    elseif (preg_match('#opera mini#i', $ua))
    {
        $browserName = 'Opera Mini';
    }
    elseif (preg_match('#Opera.(.*)Version[ /]([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Opera';
    }
    elseif (preg_match('#Opera/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Opera Mini';
    }
    elseif (preg_match('#WebPro/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'WebPro';
    }
    elseif (preg_match('#WebPro#i', $ua, $matches))
    {
        $browserName = 'WebPro';
    }
    elseif (preg_match('#Netfront/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Netfront';
    }
    elseif (preg_match('#Xiino/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Xiino';
    }
    elseif (preg_match('#Blazer[ /]?([a-zA-Z0-9.]*)#i', $ua, $matches))
    {
        $browserName = "Blazer";
    }
    elseif (preg_match('/GreenBrowser/i', $ua))
    {
        $browserName = 'GreenBrowser';
    }
    elseif (preg_match('#TencentTraveler ([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '腾讯TT浏览器';
    }
    elseif (preg_match('#UCWEB([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'UCWEB';
    }
    elseif (strpos($ua, "MSIE 10.0"))
    {
        $browserName = "IE 10";
    }
    elseif (strpos($ua, "MSIE 9.0"))
    {
        $browserName = "IE 9.0";
    }
    elseif (strpos($ua, "MSIE 8.0"))
    {
        $browserName = "IE 8.0";
    }
    elseif (strpos($ua, "MSIE 7.0"))
    {
        $browserName = "IE 7.0";
    }
    elseif (strpos($ua, "MSIE 6.0"))
    {
        $browserName = "IE 6.0";
    }
    elseif (preg_match('#Trident/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'IE浏览器';
    }
    elseif (preg_match('#Universe/([0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Universe';
    }
    elseif (preg_match('#Netscape[0-9]?/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Netscape';
    }
    elseif (preg_match('#^Mozilla/5.0#i', $ua) && preg_match('#rv:([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Mozilla';
    }
    elseif (preg_match('#^Mozilla/5.0#i', $ua) && preg_match('#MicroMessenger#i', $ua, $matches))
    {
        $browserName = '微信浏览器';
    }
    elseif (preg_match('#^Mozilla/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = 'Netscape Navigator';
    }
    elseif (preg_match('#^CinemaForiPad_Enterprise/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '影视大全iPad';
    }
    elseif (preg_match('#^CinemaForiPhone_Enterprise/([a-zA-Z0-9.]+)#i', $ua, $matches))
    {
        $browserName = '影视大全iPhone';
    }
    elseif (preg_match('#^Android#i', $ua, $matches))
    {
        $browserName = '安卓';
    }
    else
    {
        $browserName = '未知';
    }
    return $browserName;
}

/**
 * 获取用户系统版本
 * @param type $ua
 * @return type
 */
function detectSystem($ua)
{
    if (preg_match('/Windows/i', $ua) || preg_match('/Win/i', $ua))
    {
        $os = windowsDetect($ua);
    }
    else
    {
        $os = unixDetect($ua);
    }
    return $os;
}

/**
 * 获取用户windows系统版本
 * @param type $ua
 * @return type
 */
function windowsDetect($ua)
{
    if (preg_match('/Windows 95/i', $ua) || preg_match('/Win95/', $ua))
    {
        $osCode = "Windows 95";
    }
    elseif (preg_match('/Windows NT 5.0/i', $ua) || preg_match('/Windows 2000/i', $ua))
    {
        $osCode = "Windows 2000";
    }
    elseif (preg_match('/Win 9x 4.90/i', $ua) || preg_match('/Windows ME/i', $ua))
    {
        $osCode = "Windows ME";
    }
    elseif (preg_match('/Windows.98/i', $ua) || preg_match('/Win98/i', $ua))
    {
        $osCode = "Windows 98";
    }
    elseif (preg_match('/Windows NT 6.0/i', $ua))
    {
        $osCode = "Windows Vista";
    }
    elseif (preg_match('/Windows NT 6.1/i', $ua))
    {
        $osCode = "Windows 7";
    }
    elseif (preg_match('/Windows NT 6.2/i', $ua))
    {
        $osCode = "Windows 8";
    }
    elseif (preg_match('/Windows NT 6.3/i', $ua))
    {
        $osCode = "Windows 8.1";
    }
    elseif (preg_match('/Windows NT 10.0/i', $ua))
    {
        $osCode = "Windows 10";
    }
    elseif (preg_match('/Windows NT 5.1/i', $ua))
    {
        $osCode = "Windows XP";
    }
    elseif (preg_match('/Windows NT 5.2/i', $ua))
    {
        if (preg_match('/Win64/i', $ua))
        {
            $osCode = " Windows XP 64位";
        }
        else
        {
            $osCode = "Windows Server 2003";
        }
    }
    elseif (preg_match('/Mac_PowerPC/i', $ua))
    {
        $osCode = "Mac OS";
    }
    elseif (preg_match('/Windows Phone/i', $ua))
    {
        $osCode = "Windows Phone 7";
    }
    elseif (preg_match('/Windows NT 4.0/i', $ua) || preg_match('/WinNT4.0/i', $ua))
    {
        $osCode = "Windows NT 4.0";
    }
    elseif (preg_match('/Windows NT/i', $ua) || preg_match('/WinNT/i', $ua))
    {
        $osCode = "Windows NT";
    }
    elseif (preg_match('/Windows CE/i', $ua))
    {
        $osCode = "Windows CE";
    }
    else
    {
        $osCode = '其他';
    }
    return $osCode;
}

/**
 * 获取用户unix系统版本
 * @param type $ua
 * @return type
 */
function unixDetect($ua)
{
    if (preg_match('/Linux/i', $ua))
    {
        $osCode = "linux";
        if (preg_match('#Debian#i', $ua))
        {
            $osCode = "Debian GNU/Linux";
        }
        elseif (preg_match('#Mandrake#i', $ua))
        {
            $osCode = "Mandrake Linux";
        }
        elseif (preg_match('#Kindle Fire#i', $ua))
        {
            $osCode = "kindle";
        }
        elseif (preg_match('#Android#i', $ua))
        {//Android
            $osCode = "android";
        }
        elseif (preg_match('#SuSE#i', $ua))
        {
            $osCode = "SuSE Linux";
        }
        elseif (preg_match('#Novell#i', $ua))
        {
            $osCode = "Novell Linux";
        }
        elseif (preg_match('#Ubuntu#i', $ua))
        {
            $osCode = "Ubuntu Linux";
        }
        elseif (preg_match('#Red ?Hat#i', $ua))
        {
            $osCode = "RedHat Linux";
        }
        elseif (preg_match('#Gentoo#i', $ua))
        {
            $osCode = "Gentoo Linux";
        }
        elseif (preg_match('#Fedora#i', $ua))
        {
            $osCode = "Fedora Linux";
        }
        elseif (preg_match('#MEPIS#i', $ua))
        {
            $osCode = "MEPIS Linux";
        }
        elseif (preg_match('#Knoppix#i', $ua))
        {
            $osCode = "Knoppix Linux";
        }
        elseif (preg_match('#Slackware#i', $ua))
        {
            $osCode = "Slackware Linux";
        }
        elseif (preg_match('#Xandros#i', $ua))
        {
            $osCode = "Xandros Linux";
        }
        elseif (preg_match('#Kanotix#i', $ua))
        {
            $osCode = "Kanotix Linux";
        }
    }
    elseif (preg_match('/FreeBSD/i', $ua))
    {
        $osCode = "FreeBSD";
    }
    elseif (preg_match('/NetBSD/i', $ua))
    {
        $osCode = "NetBSD";
    }
    elseif (preg_match('/OpenBSD/i', $ua))
    {
        $osCode = "OpenBSD";
    }
    elseif (preg_match('/IRIX/i', $ua))
    {
        $osCode = "SGI IRIX";
    }
    elseif (preg_match('/SunOS/i', $ua))
    {
        $osCode = "Solaris";
    }
    elseif (preg_match('#iPod(.*)CPU([^(\);)]*)#i', $ua, $matches))
    {
        $osCode = "iPod";
    }
    elseif (preg_match('#iPhone(.*)CPU([^(\);)]*)#i', $ua, $matches))
    {
        $osCode = "iPhone";
    }
    elseif (preg_match('#iPad(.*)CPU([^(\);)]*)#i', $ua, $matches))
    {
        $osCode = "iPad";
    }
    elseif (preg_match('/Mac OS X.([0-9. _]+)/i', $ua, $matches))
    {
        $osCode = "Mac OS";
    }
    elseif (preg_match('/Macintosh/i', $ua))
    {
        $osCode = "Mac OS";
    }
    elseif (preg_match('/Unix/i', $ua))
    {
        $osCode = "UNIX";
    }
    elseif (preg_match('/CrOS/i', $ua))
    {
        $osCode = "Google Chrome OS";
    }
    elseif (preg_match('/PlayStation Vita ([a-zA-Z0-9]++)/i', $ua, $matches))
    {
        $osCode = "PlayStation Vita";
    }
    elseif (preg_match('/Fedor.([0-9. _]+)/i', $ua, $matches))
    {
        $osCode = "Fedora";
    }
    else
    {
        $osCode = '其他';
    }
    return $osCode;
}

/**
 * 获取来源域名
 *
 * @return string
 */
function getReferDomain()
{
    if (isset($_SERVER['HTTP_REFERER']))
    {
        $referDomain = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
        preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $referDomain, $matches);
        if ($matches[0] != "2345.com" && $matches[0] != "2345.cn" && $matches[0] != "2345jr.com" && $matches[0] != "ym.com" && $matches[0] != "km.com")
        {
            $referDomain = '';
        }
    }
    else
    {
        $referDomain = '';
    }
    return $referDomain;
}

/**
 * 判断是否是https协议
 * @return bool
 * <AUTHOR>
 */
function isHttps()
{
    if (!empty($_SERVER['HTTPS']) && strtolower($_SERVER['HTTPS']) !== 'off')
    {
        return TRUE;
    }
    elseif (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')
    {
        return TRUE;
    }
    elseif (!empty($_SERVER['HTTP_FRONT_END_HTTPS']) && strtolower($_SERVER['HTTP_FRONT_END_HTTPS']) !== 'off')
    {
        return TRUE;
    }

    return FALSE;
}

/**
 * 公用日志处理函数
 * 主要是增加了redis异步处理日志；目前日志有两种处理方式：直接文件、redis异步入文件
 * 采用redis异步处理日志引擎时，需要同步在后台打开【redis异步处理日志】，否则还是，默认处理方式
 *
 * @param string $path    日志生成路径
 * @param string $intro   日志行首介绍
 * @param string $content 日志内容
 * @param string $level   日志级别( 'emergency';'alert';'critical';'error';'warning';'notice';'info';'debug';)
 *
 * @author：dongx
 * @return void
 */
function xLog($path, $intro, $content, $level = 'info')
{
    /* -----接口埋点----- */
    $logPath = APPPATH . "/logs/" . date("Ymd") . "/" . $path;
    deepMkdir($logPath);
    $logger = new Octopus\Logger($intro);
    $redis = \RedisEx::getInstance();
    $logHandler = $redis->get('LOG_HANDLER');
    if ($logHandler === 'RedisStreamHandler')
    {
        $logger->pushHandler(new \Common\Utils\RedisStreamHandler($logPath . "/" . date('H') . ".log"));
    }
    else
    {
        $logger->pushHandler(new \Octopus\Logger\Handler\StreamHandler($logPath . "/" . date('H') . ".log"));
    }
    $logger->$level($content);
}

/**
 * calcHashRedis
 * -
 * @param $pmKey
 * @param int $partCount PartCount
 * @return string
 * <AUTHOR>
 */
function calcHashRedis($pmKey, $partCount = 128)
{
    $h = sprintf("%u", crc32($pmKey));
    $h1 = intval(fmod($h, $partCount)) + 1;
    return 'hash' . $h1;
}

/**
 * 根据mid返回登录错误次数
 * -
 * @param string $mid
 * @return int|mixed
 * <AUTHOR>
 */
function getLoginExpireCountByMid($mid = '')
{
    $midExpireArray = array(
        'andsy' => 0,
        'andsjzs' => 1,
    );
    if (isset($midExpireArray[$mid]))
    {
        return $midExpireArray[$mid];
    }
    return 3;
}

/**
 * 报警，给业务监控系统发送警告信息
 * <AUTHOR>
 * @DateTime 2018-05-11T11:06:07+0800
 * @param    [type]                   $title   [description]
 * @param    [type]                   $content [description]
 * @return   [type]                            [description]
 */
function sendCcserWarn($title, $content)
{
    $title = mb_convert_encoding($title, "UTF-8", "GBK");
    $content = mb_convert_encoding($content, "UTF-8", "GBK");
    $SendMessage = new Support\MonitorSdk\SendMessage("login.2345.com");
    return $SendMessage->sendError($title, $content);
}

/**
 * 获取唯一用户名：10位
 * @param string $prefix 前缀
 *
 * @author：dongx
 * @return string
 */
function getUniqueUsername($prefix = '手机用户_')
{
    //10位
    $timeStamps = str_replace('.', '', microtime(true));
    $rand = mt_rand(0, 99);
    $rand = str_pad($rand, 2, 0, STR_PAD_LEFT);
    //8位
    //并发请求1000，没问题
    $hex = substr(dechex($timeStamps), -8);
    $username = $prefix . $hex . $rand;
    $redis = \RedisEx::getInstance();
    if ($redis->get($username))
    {
        //重复则重新生成:时间戳变为(30-48)年前(最早1970)的
        $lastTimeStamps = strtotime('-' . mt_rand(30, 48) . ' year');
        $lastHex = dechex($lastTimeStamps);
        $newRand = mt_rand(0, 99);
        $newRand = str_pad($newRand, 2, 0, STR_PAD_LEFT);
        $newUsername = $prefix . str_pad($lastHex . $newRand, 10, mt_rand(0, 9), STR_PAD_LEFT);
        xLog('getUniqueUsername', 'getUniqueUsername', 'old:' . $username . ' new:' . $newUsername);
        $username = $newUsername;
    }
    $redis->setex($username, 1, 1);
    return $username;
}

/**
 * 对cookie I进行加密
 * -
 * @param string $cookieI 登录下发cookie I
 * @param string $outChat 写入交互的字符编码,默认UFT-8的
 * @param string $key  对称加密密钥
 * @param string $iv   初始偏移量
 * @return String
 */
function encryptCookieIByAes128cbc($cookieI, $outChat = 'utf-8', $key = '', $iv = '')
{
    $aes = new Service\Encryption\Aes\AesManager($key, $iv);
    loadAction('Encoding');
    return $aes->aes128cbcEncrypt(EncodingAction::transcoding($cookieI, $outChat));
}

/**
 * 获取注销链接上的MD5值
 * -
 * @param int $passid passid
 * @param string $sid 身份令牌
 * @return string
 */
function getLogoffSign($passid, $sid)
{
    loadAction('LogOff');
    return LogOffAction::getAllowMd5($passid, $sid);
}

/**
 * 获取设置第三方回调同步cookieI的接口
 * @param string $forward 回调地址
 * @return void
 */
function getForwardDomainSetThirdCallback($forward)
{
    $domain = parse_url($forward, PHP_URL_HOST);
    $domainThirdCallback = [
        'ruanjian.2345.cc' => 'passport.2345.cc',
        'pinyin.2345.cc'   => 'passport.2345.cc',
        'ie.2345.cc'       => 'passport.2345.cc',
        'docs.2345.cc'     => 'passport.2345.cc',
        'docs.2345.com.cn' => 'passport.2345.com.cn',
    ];
    if (!empty($domainThirdCallback[$domain])) {
        if (isHttps()) {
            $http = 'https://';
        } else {
            $http = 'http://';
        }

        return $http . $domainThirdCallback[$domain] . '/webapi/sso/login';
    }

    return '';
}

if (!function_exists('mSecTime')) {
    /**
     * 获取当前毫秒时间戳
     *
     * @return float
     */
    function mSecTime()
    {
        list($mSec, $sec) = explode(' ', microtime());
        $msectime = (float)sprintf('%.0f', (floatval($mSec) + floatval($sec)) * 1000);

        return $msectime;
    }
}

function checkMid($mid)
{
    $mids = explode(",", $_ENV["EDIT_NICKNAME"]);
    if (in_array($mid, $mids)) {
        return $mid;
    } else {
        return "";
    }
}

function IsSameSite($userAgent) {
    $userAgent = strtolower($userAgent);
    if (strpos($userAgent,'chrome/') !== false && isHttps()) {
        $explode = explode('chrome/', $userAgent);
        if (count($explode) > 1) {
            if (intval($explode[1]) >= 80) {
                return true;
            }
        }
    }
    return false;
}