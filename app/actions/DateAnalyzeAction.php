<?php

// REG_DATE_SLICE = 'reg_date_slice_';

class DateAnalyzeAction extends Action 
{
    
    const DATEANAKYZEKEY = 'DATEANAKYZEKEY:';     //时间片 管理KEY
    
    const DATEANAKYZLISTKEY = 'DATEANAKYZLISTKEY';     //时间信息队列
     
    const REGCOUNT = 'REGCOUNT:';   //注册送统计  有无验证码  是否成功统计
    
    private $regToken = '';
    
    private $regTotalKey = '';
    
    private static $requestType = array (
        'captcha',
        'rename',
        'submit'
    );
    
    //private $recordData = array();
    
    /**
     * 返回当前微妙数
     * */
    private static function getDateMicroseconds()
    {
        return microtime(true) * 10000;
    }
    
    /**
     * 获取时间片KEY
     * */
    public static function getDateSliceKey ($tag)
    {
        return self::DATEANAKYZEKEY.$tag .'_' . \Common\Utils\Cookie::getUuid();
    }
    
    /**
     * 设置时间片段  记录当前session下面的时间
     * $tag  注册 OR 登录
     * $requestType  请求添加时间片的请求类型
     * */
    public function setDateSlice($tag,$requestType)
    {
        $redis = RedisEx::getInstance();
        $isTrue = $redis->hSet( self::getDateSliceKey($tag) ,$requestType ,self::getDateMicroseconds() );
        $redis->expire(self::getDateSliceKey($tag),3600);
    }
    
    /**
     * 删除当前seesion 记录时间片
     * */
    public function delDateSlice ($tag)
    {
        $redis = RedisEx::getInstance();
        return $redis->del(self::getDateSliceKey($tag));
    }
    
    /**
     * 计算时间点
     * */
    public function isNormalAccess ($tag)
    {
        
        $userClientIp = get_client_ip();
        $regAction = loadAction('reg');
        $isShowCode = $regAction->regCheckShowCode($userClientIp);
        //如果没有验证码  就统计时间片
        if ( !$isShowCode )
        {
            $this->setDateSlice($tag,'submit');
            $this->regCount ('regcountNoCapAccess');
        }
        else
        {
            $this->regCount ('regcountHaveCapAccess');
        }
        
        return $this->analysisDateSlice($tag);
    }
    
    /**
     * 分析时间参数
     * */
    private function analysisDateSlice($tag)
    {
        $redis = RedisEx::getInstance();
        $getDateAnalyze = $redis->hGetAll (self::getDateSliceKey($tag)); 
        
        //如果没有记录到3个时间点中的任意一个  那么就显示验证码
        if ( empty ($getDateAnalyze['captcha']) || empty ($getDateAnalyze['rename']) || empty ($getDateAnalyze['submit']) )
        {
            return false;
        }

        $regDate = ($getDateAnalyze['submit'] - $getDateAnalyze['captcha']) / 10000;
        // 总时间小于等于5秒的出图片验证码
        $time13 = \Service\Security\CaptchaRules::getDateSliceOneToThree();
        if ($regDate < $time13)
        {
            return false;
        }
        // 判断验证码 到 判断重名 小于等于1秒的出图片验证码
        $regDate1 = ($getDateAnalyze['rename'] - $getDateAnalyze['captcha']) / 10000;
        $time12 = \Service\Security\CaptchaRules::getDateSliceOneToTwo();
        if (abs($regDate1) < $time12)
        {
            return false;
        }
        return true;
    }
    
    public function setRegToken($identityId)
    {
        $this->regToken = $identityId;
    }
    
    /**
     * 写入数据
     * */
    public function writeRedisData ($tag, $data)
    {
        $redis = RedisEx::getInstance();
        $getDateAnalyze = $redis->hGetAll (self::getDateSliceKey($tag)); 
        $getDateAnalyze['uuid'] = \Common\Utils\Cookie::getUuid();
        $getDateAnalyze['clientIp'] = get_client_ip();
        $getDateAnalyze['passid'] = $data['passid'];
        
        $SecurityAction = loadAction('Security');
        $getDateAnalyze['token'] = $this->regToken;
        
        
        $userClientIp = get_client_ip();
        $regAction = loadAction('reg');
        $isShowCode = $regAction->regCheckShowCode($userClientIp);
        if ( $isShowCode )
        {
            $getDateAnalyze['isShowCap'] = 1;
        }
        $data = json_encode ($getDateAnalyze);
        $redis->lPush (self::DATEANAKYZLISTKEY ,$data);
        
        if ( ! $isShowCode )
        {
            $this->regSucceedCount('regcountNoCapSucceed');
        }
        else
        {
            $this->regSucceedCount('regcountHaveCapSucceed');
        }
        $this->delDateSlice($tag);
        return true;
    }
    
    /**
     * 记录总的注册数注册数
     * */
    public function regCount ($key)
    {
        $redis = RedisEx::getInstance();
        $redis->hIncrBy( $this->getRegTotalKey() , $key ,1);
        if ($redis->ttl( $this->getRegTotalKey() ) < 0 )
        {
            $datec = 30 * 24 * 60 * 60;
            $redis->expire($this->getRegTotalKey(), $datec);
        }
    }
    
    /**
     * 成功数
     * */
    public function regSucceedCount ($key)
    {
        $redis = RedisEx::getInstance();
        $redis->hIncrBy( $this->getRegTotalKey() , $key ,1);
        if ($redis->ttl( $this->getRegTotalKey() ) < 0 )
        {
            $datec = 30 * 24 * 60 * 60;
            $redis->expire($this->getRegTotalKey(), $datec);
        }
    }
    
    /**
     * 获取统计注册数KEY
     * */
     public function getRegTotalKey ()
     {
        if ( empty ( $this->regTotalKey) )
        {
            $this->regTotalKey = self::REGCOUNT.'_'.date('Y-m-d');
        }
        return $this->regTotalKey;
     }
     
     /**
     * 设置是否显示验证码
     * */
    public function setShowCode($tag)
    {
        $redis = RedisEx::getInstance();
        $isTrue = $redis->hSet( self::getDateSliceKey($tag) ,'isShowCap' , 1 );
        $redis->expire(self::getDateSliceKey($tag),3600);
    }
    
    /**
     * 获取是否需要显示验证码
     * */
     /**
     * 设置是否显示验证码
     * */
    public function getShowCode($tag)
    {
        $redis = RedisEx::getInstance();
        $isShowCap = $redis->hGet( self::getDateSliceKey($tag) ,'isShowCap' );
        if ( $isShowCap )
        {
            return true;
        }
        else
        {
            $ip = get_client_ip();
            //白名单
            $isWrite = $redis->sIsMember('REG:WHITE:IP',$ip);
            if($isWrite)
            {
                //如果是白名单则进行下面的黑名单检查
                return false;
            }
            $isShowCap = $redis->sIsMember('REG:BLACK:IP',$ip);
            if ($isShowCap)
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }
}