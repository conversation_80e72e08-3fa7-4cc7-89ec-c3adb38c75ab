<?php
/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 摘    要:ActiveController.php
 * 作    者:<EMAIL>
 * 修改日期: 2015/9/25
 */
class ActiveController extends Controller
{
    private $pageArray = array();
    public function actionIndex()
    {
        loadView("active_error.html", $this->pageArray);
    }
    public function actionLogin()
    {
        $isUseRedis = Config::get("isUseRedis");
        $passId = filter_input(INPUT_GET, 'passid', FILTER_VALIDATE_INT);
        $memberModel = loadModel('member');
        $result = $memberModel->read($passId);
        if ($result)
        {
            if ($isUseRedis)
            {
                $redis = RedisEx::getInstance();
                $keyname = "activeNum:$passId";
                $nowTime = time();
                $activeNum = $redis->get($keyname);
                if ($activeNum > 5)
                {
                    $this->pageArray['msg'] = "邮件发送过于频繁！请联系客服！";
                    loadView("active_error.html", $this->pageArray);
                    die();
                }
                $redis->incr($keyname);
                $redis->expireAt($keyname, $nowTime + 3600);
            }
            $bind = unserialize($result['m_uid']);
            $returnArr = array($bind[1]);
            $username = $result['username'];
            $verify = uc_authcode($passId, 'ENCODE', '23455432unlock');
            $lockedUrl = PASSPORT_HOST . "/unlock?verify=" . $verify . "&u=" . $returnArr[0] . "&n=" . urlencode($username);
            $smtpConfig = Config::get('smtp');
            $smtpemailto = $result['email'];
            $mailsubject = "2345通行证激活邮件";
            $mailbody = "<pre>亲爱的2345网址大全用户，您好！<br>";
            $mailbody .= "<br>感谢您注册2345网址大全：<br><br>";
            $mailbody .= "请您点击下面的链接，完成邮箱验证：<br><br><a href='" . $lockedUrl . "' target='_blank'>" . $lockedUrl . "</a><br><br>";
            $mailbody .= "如果以上链接无法点击，请将它拷贝到浏览器（例如IE）的地址栏中：<br><br>" . $lockedUrl . "</p><br><br>";
            $mailbody .= "如果你错误的收到了本电子邮件，请无需理会该邮件。<br><br>感谢您一直以来对2345网址大全关注与支持！</pre>";
            $mailtype = "HTML";
            $smtp = loadVendor('smtp');
            $smtp->init($smtpConfig['server'], $smtpConfig['port'], $smtpConfig['username'], $smtpConfig['password']);
            $smtp->sendmail($smtpemailto, $smtpConfig['email'], $mailsubject, $mailbody, $mailtype);
            setcookie('active_passid', $passId, 0, '/');
            setcookie('active_email', $result['email'], 0, '/');
            loadView("active_error.html", $this->pageArray);
        }

    }
}