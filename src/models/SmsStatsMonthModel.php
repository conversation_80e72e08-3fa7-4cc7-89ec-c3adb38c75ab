<?php
/**
 * Copyright (c) 2022,上海二三四五网络科技有限公司
 * 摘    要：月度短信数据统计相关模型
 * 作    者：朱锋锦
 * 日    期：2022-01-25
 */

class SmsStatsMonthModel extends Model
{
    public $strSmsLogTable = 'sms_logs_archive_'; //短信日志表
    public $strSmsProjectTable = 'project_list'; //短信项目表
    public $strSmsProviderTable = 'my_sms_provider'; //短信供应商表
    public $strSmsTagTable = 'sms_tag_list'; //短信mid表
    public $strSmsTagLogTable = 'sms_tag_log'; //短信mid日志表
    public $strSmsStatsSumTable = 'sms_stats_month_sum'; //短信数据统计汇总表
    public $strSmsStatsConfigTable = 'sms_stats_month_config'; //短信统计配置表

    const PROJECT_USER_CENTER = 14; //用户中心项目ID
    const STATS_MIN_MONTH = '2021-12'; //进行统计的最小月份
    const STATS_TYPE_PROJECT = 1; //统计类型：按照项目统计
    const STATS_TYPE_MID = 2; //统计类型：按照MID统计
    const STATS_TYPE_TYPE = 3; //统计类型：按照类型统计
    const REDIS_KEY_SMSDATA = 'yfzt:api:smsdata'; //接口数据Redis缓存

    /**
     * SmsStatsMonthModel constructor.
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        $this->tableName = 'sms_stats_month';
        parent::__construct();
    }

    /**
     * 功    能: 清理相同月份的数据库数据
     * 作    者: 朱锋锦
     * 日    期: 2022-01-25
     *
     * @param string $strMonth 月份
     * @return boolean
     */
    public function clearDbData($strMonth)
    {
        return $this->delByTable(['month' => $strMonth]);
    }

    /**
     * 功    能: 按照项目统计数据，用户中心(项目ID为14)数据除外
     * 作    者: 朱锋锦
     * 日    期: 2022-01-25
     *
     * @sql select pid as project, channel as provider, count(*) as send_count, sum(sms_count) as charge_count from sms_logs_archive_202112 where send_status >= 1 group by pid, channel;
     *
     * @param string $strMonth 月份
     * @return boolean
     */
    public function statsByProject($strMonth)
    {
        $strSmsTable = $this->strSmsLogTable . $strMonth;
        $arrWhere = [
            ['send_status', 1, '>='],
        ];
        $arrDbList = $this->getAllByWhere($strSmsTable, 'pid as project, channel as provider, count(*) as send_count, sum(sms_count) as charge_count', $arrWhere, 'group by pid, channel');
        if (!$arrDbList) {
            return false;
        }

        $arrProjectList = $this->getAllByWhere($this->strSmsProjectTable, '*');
        $arrProjectList = array_column($arrProjectList, null, 'pid');

        $arrProviderList = $this->getAllByWhere($this->strSmsProviderTable, '*');
        $arrProviderList = array_column($arrProviderList, null, 'id');

        $arrInsert = [];
        foreach ($arrDbList as $arrDbInfo) {
            if ($arrDbInfo['project'] == self::PROJECT_USER_CENTER) {
                //按项目统计数据时跳过用户中心的数据
                continue;
            }

            $arrProject = $arrProjectList[$arrDbInfo['project']] ?? [];
            $intOAProductId = $arrProject['oa_product_id'] ?? 0;
            $arrOAProject = OAProductAction::getInstance()->getProjectByProduct($intOAProductId);
            $arrProvider = $arrProviderList[$arrDbInfo['provider']] ?? [];

            $arrInsert[] = [
                'month' => $strMonth,
                'oa_project_id' => $arrOAProject['project_id'] ?? 0,
                'oa_project_name' => $arrOAProject['project_name'] ?? '未知项目',
                'oa_product_id' => $intOAProductId,
                'oa_product_name' => $arrOAProject['product_list'][$intOAProductId]['product_name'] ?? '未知产品',
                'stats_type' => self::STATS_TYPE_PROJECT,
                'stats_id' => $arrDbInfo['project'],
                'stats_name' => $arrProject['project_name'] ?? '',
                'provider_id' => $arrProvider['id'] ?? 0,
                'provider_cate' => $arrProvider['provider_cate'] ?? 0,
                'send_count' => $arrDbInfo['send_count'],
                'charge_count' => $arrDbInfo['charge_count'],
            ];
        }
        if ($arrInsert) {
            $arrColumn = array_keys($arrInsert[0]);
            $arrColumn = array_combine($arrColumn, $arrColumn);
            return $this->batchInsert($arrInsert, $arrColumn);
        }
        return true;
    }

    /**
     * 功    能: 将用户中心(项目ID为14)的数据按照mid拆分至项目
     * 作    者: 朱锋锦
     * 日    期: 2022-01-25
     *
     * @sql select mid, channel as provider, count(*) as send_count, sum(sms_count) as charge_count from
     *      (
     *          select a.log_id, channel, sms_count, mid from sms_logs_archive_202112 as a right join
     *          (
     *              select log_id, mid from sms_tag_log where log_id >= 744855247 and log_id <= 746143050
     *          )
     *          as b on a.log_id = b.log_id where a.send_status >= 1 and a.pid = 14
     *      )
     *      as c group by mid, channel;
     *
     * @param string $strMonth 月份
     * @return boolean
     */
    public function statsByMid($strMonth)
    {
        $strSmsTable = $this->strSmsLogTable . $strMonth;
        $arrWhere = [
            ['send_status', 1, '>='],
        ];
        $arrIdList = $this->getByWhere($strSmsTable, 'min(log_id) as min_log_id, max(log_id) as max_log_id', $arrWhere);
        if (!$arrIdList) {
            return false;
        }
        $intMinLogId = $arrIdList['min_log_id'];
        $intMaxLogId = $arrIdList['max_log_id'];

        $strSql = <<<SQL
select mid, channel as provider, count(*) as send_count, sum(sms_count) as charge_count from 
(
    select a.log_id, channel, sms_count, mid from {$strSmsTable} as a right join 
    (
        select log_id, mid from {$this->strSmsTagLogTable} where log_id >= :min_log_id and log_id <= :max_log_id
    ) 
    as b on a.log_id = b.log_id where a.send_status >= :send_status and a.pid = :project_user_center
) 
as c group by mid, channel
SQL;
        $arrParam = [
            ':min_log_id' => $intMinLogId,
            ':max_log_id' => $intMaxLogId,
            ':send_status' => 1,
            ':project_user_center' => self::PROJECT_USER_CENTER,
        ];
        $arrDbList = $this->pdo->findAll($strSql, $arrParam);


        $arrTagList = $this->getAllByWhere($this->strSmsTagTable, '*');
        $arrTagList = array_column($arrTagList, null, 'mid');

        $arrProviderList = $this->getAllByWhere($this->strSmsProviderTable, '*');
        $arrProviderList = array_column($arrProviderList, null, 'id');

        $arrInsert = [];
        foreach ($arrDbList as $arrDbInfo) {
            $arrTag = $arrTagList[$arrDbInfo['mid']] ?? [];
            $intOAProductId = $arrTag['oa_product_id'] ?? 0;
            $arrOAProject = OAProductAction::getInstance()->getProjectByProduct($intOAProductId);
            $arrProvider = $arrProviderList[$arrDbInfo['provider']] ?? [];

            $arrInsert[] = [
                'month' => $strMonth,
                'oa_project_id' => $arrOAProject['project_id'] ?? 0,
                'oa_project_name' => $arrOAProject['project_name'] ?? '未知项目',
                'oa_product_id' => $intOAProductId,
                'oa_product_name' => $arrOAProject['product_list'][$intOAProductId]['product_name'] ?? '未知产品',
                'stats_type' => self::STATS_TYPE_MID,
                'stats_id' => $arrDbInfo['mid'],
                'stats_name' => $arrTag['tag_name'] ?? '',
                'provider_id' => $arrProvider['id'] ?? 0,
                'provider_cate' => $arrProvider['provider_cate'] ?? 0,
                'send_count' => $arrDbInfo['send_count'],
                'charge_count' => $arrDbInfo['charge_count'],
            ];
        }
        if ($arrInsert) {
            $arrColumn = array_keys($arrInsert[0]);
            $arrColumn = array_combine($arrColumn, $arrColumn);
            return $this->batchInsert($arrInsert, $arrColumn);
        }
        return true;
    }

    /**
     * 功    能: 将用户中心(项目ID为14)中部分没有mid的数据按照类型拆分至项目（此处拆分无后台配置，需要在代码中写明规则）
     * 作    者: 朱锋锦
     * 日    期: 2022-01-25
     *
     * @sql select type, channel as provider, count(*) as send_count, sum(sms_count) as charge_count from sms_logs_archive_202112 where pid = 14 and send_status >= 1 and log_id not in
     *      (
     *          select log_id from sms_tag_log where log_id >= 744855247 and log_id <= 746143050
     *      )
     *      group by type, channel;
     *
     * @param string $strMonth 月份
     * @return boolean
     */
    public function statsByType($strMonth)
    {
        $strSmsTable = $this->strSmsLogTable . $strMonth;
        $arrWhere = [
            ['send_status', 1, '>='],
        ];
        $arrIdList = $this->getByWhere($strSmsTable, 'min(log_id) as min_log_id, max(log_id) as max_log_id', $arrWhere);
        if (!$arrIdList) {
            return false;
        }
        $intMinLogId = $arrIdList['min_log_id'];
        $intMaxLogId = $arrIdList['max_log_id'];

        $strSql = <<<SQL
select `type`, channel as provider, count(*) as send_count, sum(sms_count) as charge_count 
from {$strSmsTable} where pid = :project_user_center and send_status >= :send_status and log_id not in 
(
    select log_id from {$this->strSmsTagLogTable} where log_id >= :min_log_id and log_id <= :max_log_id
) 
group by type, channel;
SQL;
        $arrParam = [
            ':min_log_id' => $intMinLogId,
            ':max_log_id' => $intMaxLogId,
            ':send_status' => 1,
            ':project_user_center' => self::PROJECT_USER_CENTER,
        ];
        $arrDbList = $this->pdo->findAll($strSql, $arrParam);

        $arrTypeList = [
            100 => ['oa_product_id' => 36, 'type_name' => '线上测试'], //关联用户中心
            185 => ['oa_product_id' => 6, 'type_name' => '浏览器点集验证码'], //关联手机浏览器
            275 => ['oa_product_id' => 1, 'type_name' => '点集星球计步赚'], //关联玩赚星球
            277 => ['oa_product_id' => 1, 'type_name' => '星球2345开心果园'], //关联玩赚星球
        ];//这里是为了统计手浏和星球项目错误使用的用户中心项目的短信，没有后台可以配置，直接把OA产品关联关系写在代码中

        $arrProviderList = $this->getAllByWhere($this->strSmsProviderTable, '*');
        $arrProviderList = array_column($arrProviderList, null, 'id');

        $arrInsert = [];
        foreach ($arrDbList as $arrDbInfo) {
            $arrType = $arrTypeList[$arrDbInfo['type']] ?? [];
            $intOAProductId = $arrType['oa_product_id'] ?? 0;
            $arrOAProject = OAProductAction::getInstance()->getProjectByProduct($intOAProductId);
            $arrProvider = $arrProviderList[$arrDbInfo['provider']] ?? [];

            $arrInsert[] = [
                'month' => $strMonth,
                'oa_project_id' => $arrOAProject['project_id'] ?? 0,
                'oa_project_name' => $arrOAProject['project_name'] ?? '未知项目',
                'oa_product_id' => $intOAProductId,
                'oa_product_name' => $arrOAProject['product_list'][$intOAProductId]['product_name'] ?? '未知产品',
                'stats_type' => self::STATS_TYPE_TYPE,
                'stats_id' => $arrDbInfo['type'],
                'stats_name' => $arrType['type_name'] ?? '',
                'provider_id' => $arrProvider['id'] ?? 0,
                'provider_cate' => $arrProvider['provider_cate'] ?? 0,
                'send_count' => $arrDbInfo['send_count'],
                'charge_count' => $arrDbInfo['charge_count'],
            ];
        }
        if ($arrInsert) {
            $arrColumn = array_keys($arrInsert[0]);
            $arrColumn = array_combine($arrColumn, $arrColumn);
            return $this->batchInsert($arrInsert, $arrColumn);
        }
        return true;
    }

    /**
     * 功    能: 汇总运算数据
     * 作    者: 朱锋锦
     * 日    期: 2022-01-25
     *
     * @sql select oa_project_id, oa_project_name, provider_cate, sum(send_count) as send_count, sum(charge_count) as charge_count from sms_stats_month where month = '202112' group by oa_project_id, provider_cate;
     *
     * @param string $strMonth 月份
     * @return boolean
     */
    public function sumData($strMonth)
    {
        $arrDbList = $this->getAllByWhere('', 'oa_project_id, oa_project_name, provider_cate, sum(send_count) as send_count, sum(charge_count) as charge_count', ['month' => $strMonth], 'group by oa_project_id, provider_cate', '', true);
        if (!$arrDbList) {
            return false;
        }

        $arrInsert = [];
        foreach ($arrDbList as $arrDbInfo) {
            $arrInsert[] = [
                'month' => $strMonth,
                'oa_project_id' => $arrDbInfo['oa_project_id'] ?? 0,
                'oa_project_name' => $arrDbInfo['oa_project_name'] ?? '未知项目',
                'provider_cate' => $arrDbInfo['provider_cate'] ?? 0,
                'send_count' => $arrDbInfo['send_count'],
                'charge_count' => $arrDbInfo['charge_count'],
                'settle_count' => $arrDbInfo['charge_count'],
                'operator' => '系统',
            ];
        }
        if ($arrInsert) {
            //先将历史数据设置为删除状态
            $this->editByTable(['month' => $strMonth], ['del' => 1], $this->strSmsStatsSumTable);

            $arrColumn = array_keys($arrInsert[0]);
            $arrColumn = array_combine($arrColumn, $arrColumn);
            return $this->batchInsert($arrInsert, $arrColumn, $this->strSmsStatsSumTable);
        }
        return true;
    }

    /**
     * 功    能: 获取配置信息
     * 作    者: 朱锋锦
     * 日    期: 2022-01-26
     *
     * @param string $strMonth 月份
     * @return array
     */
    public function getConfigData($strMonth)
    {
        $arrData = $this->getAllByWhere($this->strSmsStatsConfigTable, '*', ['month' => $strMonth], '', '', true);
        return array_column($arrData, null, 'provider_cate');
    }

    /**
     * 功    能: 获取汇总数据信息
     * 作    者: 朱锋锦
     * 日    期: 2022-01-26
     *
     * @param string $strMonth 月份
     * @return array
     */
    public function getSumData($strMonth)
    {
        $arrData = $this->getAllByWhere($this->strSmsStatsSumTable, '*', ['month' => $strMonth, 'del' => 0], '', ' order by oa_project_id, provider_cate', true);
        return $arrData;
    }

    /**
     * 功    能: 进行数据校对的数据库操作
     * 作    者: 朱锋锦
     * 日    期: 2022-01-27
     *
     * @param string $strMonth 月份
     * @param array $arrConfig 校对配置信息
     * @param array $arrData 校对数据
     * @return array
     */
    public function doCheck($strMonth, $arrConfig, $arrData)
    {
        try {
            $this->begin();

            //查询原始数据，进行数据备份
            $arrBack = [
                'month' => $strMonth,
                'source_config' => $this->getConfigData($strMonth),
                'source_data' => $this->getSumData($strMonth),
                'new_config' => $arrConfig,
                'new_data' => $arrData,
            ];
            \WebLogger\Facade\LoggerFacade::info('短信对账数据校对备份', $arrBack);

            //存储数据
            foreach ($arrConfig as $arrInfo) {
                $intId = $arrInfo['id'];
                unset($arrInfo['id']);
                if ($intId > 0) {
                    $blnConfig = $this->editByTable(['id' => $intId], $arrInfo, $this->strSmsStatsConfigTable);
                } else {
                    $blnConfig = $this->addByTable($arrInfo, $this->strSmsStatsConfigTable);
                }
                if (!$blnConfig) {
                    throw new Exception('对账配置数据入库失败', 601);
                }
            }

            foreach ($arrData as $arrInfo) {
                $intId = $arrInfo['id'];
                unset($arrInfo['id']);
                $blnData = $this->editByTable(['id' => $intId], $arrInfo, $this->strSmsStatsSumTable);
                if (!$blnData) {
                    throw new Exception('对账校对数据入库失败', 602);
                }
            }
            $this->commit();
        } catch (Exception $objEx) {
            $this->rollback();
            return ['code' => $objEx->getCode() ?: 600, 'msg' => $objEx->getMessage()];
        }

        //每次校对完成后清除redis缓存
        $objRedis = \RedisAction::connect();
        $strRedisKey = self::REDIS_KEY_SMSDATA;
        $objRedis->del($strRedisKey);
        return ['code' => 200, 'msg' => '成功'];
    }

    /**
     * 功    能: 获取短信账单汇总数据
     * 作    者: 朱锋锦
     * 日    期: 2022-01-27
     *
     * @param int $intOaProjectId OA项目ID
     * @param string $strStartMonth 开始月份
     * @param string $strEndMonth 结束月份
     * @return array
     */
    public function getSumDataForApi($intOaProjectId, $strStartMonth, $strEndMonth)
    {
        $arrWhere = [
            ['month', $strStartMonth, '>='],
            ['month', $strEndMonth, '<='],
            'del' => 0,
            'status' => 1,
        ];
        if ($intOaProjectId > 0) {
            $arrWhere['oa_project_id'] = $intOaProjectId;
        }

        $objRedis = \RedisAction::connect();
        $strRedisKey = self::REDIS_KEY_SMSDATA;
        $strHashKey = md5(json_encode($arrWhere));
        $strRedisData = $objRedis->hGet($strRedisKey, $strHashKey);
        if ($strRedisData === false) {
            $arrData = $this->getAllByWhere($this->strSmsStatsSumTable, 'month, oa_project_id, oa_project_name, sum(send_count) as send_count, sum(charge_count) as charge_count, sum(settle_count) as settle_count, sum(cost) as cost', $arrWhere, 'group by month, oa_project_id', 'order by month desc, oa_project_id asc');
            $arrFormat = [];
            foreach ($arrData as $arrInfo) {
                $arrFormat[] = [
                    'month' => date('Y-m', strtotime($arrInfo['month'] . '01')),
                    'oa_project_id' => intval($arrInfo['oa_project_id']),
                    'oa_project_name' => $arrInfo['oa_project_name'],
                    'send_count' => intval($arrInfo['send_count']),
                    'charge_count' => intval($arrInfo['charge_count']),
                    'settle_count' => intval($arrInfo['settle_count']),
                    'cost' => $arrInfo['cost'],
                ];
            }
            $objRedis->hSet($strRedisKey, $strHashKey, serialize($arrFormat));
            $objRedis->expire($strRedisKey, 86400);
        } else {
            $arrFormat = unserialize($strRedisData);
        }
        return $arrFormat;
    }
}
