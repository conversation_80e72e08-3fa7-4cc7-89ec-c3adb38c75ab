<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/10/17
 * Time: 9:21
 */
use Octopus\PdoEx;
use Common\Confidential\Sensitive;

class LogOffModel extends Model
{
    private $pdo;
    protected $table;

    /**
     * LogOffModel constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->table = 'logoff_log';
        $this->pdo = PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
    }

    /**
     * 插入注销日志
     * -
     * @param int $passid 用户ID
     * @param int $logoffInfo 注销信息
     * @return bool|int
     */
    public function addLogoffLog($passid, $logoffInfo)
    {
        loadAction('Encoding');
        $data = json_decode($logoffInfo, true);
        $insert = array(
            'passid' => $passid,
            'logoffInfo' => $logoffInfo,
            'phone' => Sensitive::Encode($data['phone']),
            'email' => Sensitive::Encode($data['email']),
            'username' => EncodingAction::transcoding($data['username'], 'gbk'),
            'client_ip' => get_client_ip(true),
            'add_time' => date('Y-m-d H:i:s')
        );
        return $this->pdo->insert($this->table, $insert, false);
    }

    /**
     * 注销账号
     * -
     * @param int $passid passid
     * @return bool
     */
    public function unbind($passid)
    {
        $memberModel = loadModel('member');
        $userInfo = $memberModel->read($passid);
        $isDel = true;

        //如果有手机号码则进行解绑
        if (!empty($userInfo['phone']))
        {
            //解绑手机号码
            $phoneModel = loadModel('phone');
            $isDel = $phoneModel->del($passid);
        }

        if ($isDel !== false)
        {
            //如果邮箱进行校验过 则进行解绑
            if (!empty($userInfo['email']) && $userInfo['email_status'] == 1)
            {
                $isDel = $memberModel->editEmail($passid, '');
            }
        }

        $unBindList = [];
        if ($isDel !== false)
        {
            $oauthModel = loadModel('oauth');
            //第三方绑定 进行解绑操作
            $bindList = $oauthModel->getBindByPassid($passid);
            if (!empty($bindList) && is_array($bindList))
            {
                foreach ($bindList as $bindInfo)
                {
                    $unBindList[$bindInfo['type']] = $bindInfo['openid'];
                    $checkCode = md5($userInfo['username'] . time() . $passid . "checkcode");
                    $oauthModel->setUnbindCode($bindInfo['type'], $passid, $checkCode);
                    $isDel = $oauthModel->delBind($bindInfo['type'], $passid, $checkCode);
                    //如果删除失败,就退出 不再进行删除
                    if ($isDel === false)
                    {
                        break;
                    }
                }
            }
        }
        //冻结账号
        if ($isDel !== false)
        {
            $isDel = $memberModel->setMemberLock($passid, 2);
        }

        loadAction('Encoding');
        if ($isDel !== false)
        {
            //更新掉用户名信息
            $username = getUniqueUsername('注销用户_');
            $isUp = $memberModel->updateUsername($passid, $username);
            if (!$isUp)
            {
                xLog('logOffLog/fail', 'logOffLog', "注销用户_用户名修改失败：passid=" . $passid, 'info');
            }
            $uid = $memberModel->getUid($passid);
            $logoffData = array(
                'passid' => $passid,
                'uid'    => $uid,
                'type'   => 'Logout',
                'value'  => '',
            );
            if (!empty($userInfo['phone'])) {
                $extendData = [
                    'phone' => $userInfo['phone'],
                ];
                $logoffData['extend'] = base64_encode(json_encode($extendData));
            }
            noticeToChange($passid, 'changeLogout', $logoffData);

            //日志行为打点:用户注销; type:USER_LOGOFF; sub_type:PASSID;;
            $userInfo['passid'] = $passid;
            $objLogV2Action = loadAction('logV2');
            $objLogV2Action->report('ALL', 'USER_LOGOFF', 'PASSID', $userInfo, '', '');

            $logOffInfo = [
                'phone' => !empty($userInfo['phone']) ? $userInfo['phone'] : '',
                'email' => !empty($userInfo['email']) ? $userInfo['email'] : '',
                'username' => !empty($userInfo['username']) ? $userInfo['username'] : '',
                'unBindList' => $unBindList
            ];
            loadAction('Encoding');
            $this->addLogoffLog($passid, json_encode(EncodingAction::transcoding($logOffInfo, 'utf-8')));
            $msg = "注销用户_成功：passid=" . $passid . ';info=' . json_encode(EncodingAction::transcoding($userInfo, 'utf-8')) . ';openid=';
            $msg .= json_encode(EncodingAction::transcoding($unBindList, 'utf-8'));
            xLog('logOffLog/seccess', 'logOffLog', $msg, 'info');
            return true;
        }
        else
        {
            $msg = "注销用户_解绑失败：passid=" . $passid . ';info=' . json_encode(EncodingAction::transcoding($userInfo, 'utf-8'));
            xLog('logOffLog/fail', 'logOffLog', $msg, 'info');
            return false;
        }
    }

    /**
     * 获取注销次数
     * -
     * @param int $phone 手机号码
     * @return array|bool
     */
    public function getLogoffByPhone($phone)
    {
        $sql = "SELECT id,passid,username,phone,email,client_ip,add_time  FROM {$this->table} where phone = :phone or phone= :aesPhone order by id desc";
        return $this->pdo->findAll($sql, array(":phone" => $phone, ":aesPhone" => Sensitive::Encode($phone)));
    }

    /**
     * 获取注销日志列表
     * -
     * @param  array $data 查询数据
     * @param array $limit 分页
     * @return array|bool
     */
    public function getLogoffLogList($data, $limit = [])
    {
        $field = 'id,passid,username,phone,email,client_ip,add_time,logoffInfo';
        if (!empty($data['count']) && $data['count'])
        {
            $field = ' count(id) as total ';
        }
        $sql = "SELECT {$field}  FROM {$this->table}";
        $params = [];
        if (!empty($data['userType']))
        {
            switch ($data['userType'])
            {
                case "phone":
                    $sql .= " where phone=:phone";
                    $params[':phone'] = $data['userName'];
                    break;
                case "email":
                    $sql .= " where  email=:email";
                    $params[':email'] = $data['userName'];
                    break;
                case "username":
                    $sql .= " where  username=:username";
                    $params[':username'] = $data['userName'];
                    break;
                default:
                    break;
            }
        }
        $sql .= ' order by id desc ';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }

        if (!empty($data['count']) && $data['count'])
        {
            return $this->pdo->find($sql, $params);
        }
        else
        {
            return $this->pdo->findAll($sql, $params);
        }
    }
}
