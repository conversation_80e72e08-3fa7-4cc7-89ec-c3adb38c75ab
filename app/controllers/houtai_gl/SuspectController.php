<?php

use Octopus\PdoEx;

includeBase('AdminController');

class SuspectController extends AdminController
{

    protected $authName = '查询限制信息';

    public function actionIps()
    {
        $redis = RedisEx::getInstance();
        $ips = $redis->zRevRange("loginSuspectIps", 0, -1, true);
        foreach ($ips as $ip => $num)
        {
            if (!$redis->exists("LSP:$ip"))
            {
                $redis->zRem('loginSuspectIps', $ip);
                unset($ips[$ip]);
            }
        }
        $pageArray['data'] = $ips;
        loadView('admin/suspect_ips.tpl.html', $pageArray);
    }

    public function actionPassids()
    {
        $ip = $_GET['ip'];
        $redis = RedisEx::getInstance();
        $passids = $redis->sMembers("LSP:$ip");
        $pageArray['ip'] = $ip;
        $pageArray['data'] = $passids;
        loadView('admin/suspect_passids.tpl.html', $pageArray);
    }

    public function actionRegips()
    {
        $redis = RedisEx::getInstance();
        if (isset($_GET["action"]) && $_GET["action"] == "delete" && isset($_GET["ip"]))
        {
            $redis->sRem('regSuspectIps', $_GET["ip"]);
        }
        $ips = $redis->sMembers("regSuspectIps");
        sort($ips);
        $dbConfig = Config::get("database");
        $pdo = PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        foreach ($ips as $key => $ip)
        {
            if ($key < 100)
            {
                $pdo->insert("reg_suspect_ip", array("ip" => $ip), false);
                $redis->sRem('regSuspectIps', $ip);
            }
            else
            {
                break;
            }
        }
        $pageArray['data'] = $ips;
        loadView('admin/suspect_regips.tpl.html', $pageArray);
    }

    /**
     * 功  能：
     * @return void
     */
    public function actionGatherRegIps()
    {
        $ip = $_GET['ip'];
        $redis = RedisEx::getInstance();
        $passids = $redis->sMembers("LSP:$ip");
        if ($passids && sizeof($passids)) {
            $ipList = [];
            $result = loadModel('member')->getNewMemberByPassIdList($passids, ['reg_ip']);
            foreach ($result as $item) {
                if (!isset($ipList[$item['reg_ip']])) {
                    $ipList[$item['reg_ip']] = 1;
                    if (filter_var($item['reg_ip'], FILTER_VALIDATE_IP)) {
                        $redis->sAdd("regSuspectIps", $item['reg_ip']);
                    }
                }
            }
            unset($ipList);
            foreach ($passids as $passid) {
                $redis->sRem("LSP:$ip", $passid);
            }
        }
        $redis->zRem('loginSuspectIps', $ip);
        redirect("/houtai_gl/suspect/regips");
    }

    /**
     * 清理可疑帐号登录状态
     */
    public function actionClearLogins()
    {
//        $ip = $_GET['ip'];
//        $redis = RedisEx::getInstance();
//        $passids = $redis->sMembers("LSP:$ip");
//        if ($passids && sizeof($passids))
//        {
//            foreach ($passids as $passid)
//            {
//                //删除登录session
//                $sids = $redis->zRange("LoginSession:$passid", 0, -1);
//                foreach ($sids as $tmpSid)
//                {
//                    $redis->zRem('LoginSessions', $tmpSid);
//                }
//                //删除登录session
//                $redis->del("LoginSession:$passid");
//                //删除ip记录
//                $redis->del("LSSP:$passid");
//            }
//        }
        redirect("/houtai_gl/suspect/ips");
    }

    /**
     * 删除正常IP
     */
    public function actionDel()
    {
        $ip = $_GET['ip'];
        $redis = RedisEx::getInstance();
        $redis->del("LSP:$ip");
        redirect("/houtai_gl/suspect/ips");
    }

}
