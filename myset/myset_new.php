<?php
/**
 * Copyright (c) 2013,上海瑞创网络科技股份有限公司
 * 摘　　要：保存网址导航设置中的内容
 * 作　　者：熊小明
 * 修改日期：2013.07.19
 */

header("Content-type:text/html;charset=gb2312");

define("BASE_PATH", realpath(dirname(__FILE__) . "/../") . "/");
require_once BASE_PATH . "/include/init.php";
require_once BASE_PATH . "/config.inc.php";

if (!IS_LOGIN)
{
    die('uid_error');
}

if ($_GET['cmd'] == "save" || $_GET['cmd'] == 'savewc')
{
    $pdo = \Octopus\PdoEx::getInstance(DB_MY_2345, $g_db_config[DB_MY_2345]);
    $sql = "select uid from fav_myset where uid= :uid limit 1";
    $result = $pdo->find($sql, array(":uid" => UID));

    $mystyle_display = 'zhuti';
    $theme_id = intval($_GET['themeId']);
    $skin = intval($_GET['skin']);
    $ggbd = intval($_GET['ggbd']) == 0 ? 0 : 1;

    $wc = intval($_COOKIE['wc']);
    $lc = intval($_COOKIE['lc']);
    $wcn = $_COOKIE['wc_n'] ? $_COOKIE['wc_n'] : "";
    $wsf = intval($_COOKIE['wc_sf']);

    $data = array(
        'wc' => $wc,
        'wcn' => $wcn,
        'lc' => $lc,
        'wsf' => $wsf,
    );
    if ($_GET['cmd'] == 'save')
    {
        $data['skin'] = $skin;
        $data['ggbd'] = $ggbd;
        $data['mystyle_display'] = $mystyle_display;
        $data['theme_id'] = $theme_id;
    }

    if ($result)
    {
        $condition = array(
            "where" => " uid = :uid ",
            "params" => array(
                ":uid" => UID,
            ),
        );
        $res = $pdo->update('fav_myset', $data, $condition);
    }
    else
    {
        $data['uid'] = UID;
        $res = $pdo->insert('fav_myset', $data, false);
    }
} 

