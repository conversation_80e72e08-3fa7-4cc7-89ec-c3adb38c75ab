<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=GBK">
    <script type="text/javascript" src="/js/datepicker/WdatePicker.js"></script>
    <title>注册禁止IP列表</title>
    <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
    <link href="/css/v3/zhanghao.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/v3/top.css">
</head>
<body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
<div class="mainWrap">

    <div class="location">位置：<a href="#" target="_blank">登录注册管理</a> &gt; <span> {{if $pageArray.type=="black"}}注册IP黑名单管理{{else}}注册IP白名单管理{{/if}}</span></div>

    <div class="TabMenu mt10">
        <a href="/houtai_gl/suspect/ips">可疑登录IP</a>
        <a href="/houtai_gl/suspect/regips">可疑注册IP</a>
        <a href="/houtai_gl/login_ip_log">最近登录IP日志</a>
        <a href="/houtai_gl/limit_ip/reg_limit_ip_list/black" {{if $pageArray.type=="black"}}class="currTabMenu"{{/if}}>注册IP黑名单管理</a>
        <a href="/houtai_gl/limit_ip/reg_limit_ip_list/white" {{if $pageArray.type!="black" }}class="currTabMenu" {{/if}}>注册IP白名单管理</a>
    </div>

    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
        <form action="" method="get" id="search-form">

            <!--
            <div class="dataWrap clearfix">
                <em>日期：</em>

                <div class="dataList w130">
                    <div class="getTime"><input type="text" name="start" autocomplete="off"
                                                onClick="javascript:WdatePicker();" value="{{$smarty.get.start}}"
                                                readonly><i>日历icon</i></div>
                </div>
            </div>

            <span class="dataTxt">至</span>

            <div class="dataWrap clearfix">
                <div class="dataList w130">
                    <div class="getTime"><input type="text" name="end" autocomplete="off"
                                                onclick="javascript:WdatePicker();" value="{{$smarty.get.end}}"
                                                readonly><i>日历icon</i></div>
                </div>
            </div>
            -->

            <div class="selectWrap left w170 ml10">
                <div class="selectInput">
                    <input type="text" class="getListVal" autocomplete="off" value="正常"
                           readonly=""><i>下拉箭头</i></div>
                <dl style="display: none;" class="selectList">
                    <dd><a href="javascript:" value="1" {{if $pageArray.status=="1"}}selected{{/if}}>正常</a></dd>
                    <dd><a href="javascript:" value="2" {{if $pageArray.status=="2"}}selected{{/if}}>已删除</a></dd>
                </dl>
                <input type="hidden" value="1" name="status"/>
            </div><!--selectWrap end-->

            <div class="left ml10 clearfix">
                <span class="input-search"><i>搜索Icon</i>
                    <input type="text" id="ip-input" autocomplete="off" name="ip" type="text"
                           value="{{$pageArray.ip}}">
                </span>
                <a class="styleBtnBlue left ml10" href="javascript:" id="search-btn">查询</a>

                {{if $pageArray.status=='1'}}
                <a class="styleBtnBlue left ml10" href="javascript:" id="add">添加</a>
                {{/if}}
            </div>

        </form>
    </div>
    <!--日期和下拉菜单 结束-->

    <div class="contentWrap">
        <!--table数据列表 开始-->
        <table cellspacing="0" cellpadding="0" class="tabMod forFixed mt10">
            <tbody>
            <tr>
                <th><input type="checkbox" id="select-all"/>全选</th>
                <th>id</th>
                {{if $pageArray.sortBy == "ip"}}
                <th>
                    {{if $pageArray.sort == "ASC"}}
                    <a href="?ip={{$pageArray.ip}}&status={{$pageArray.status}}&sortBy=ip&sort=DESC">IP <font color="green">↑</font></a>
                    {{else}}
                    <a href="?ip={{$pageArray.ip}}&status={{$pageArray.status}}&sortBy=ip&sort=ASC">IP <font color="green">↓</font></a>
                    {{/if}}
                </th>
                <th><a href="?ip={{$pageArray.ip}}&status={{$pageArray.status}}&sortBy=last_update&sort=ASC">添加时间</a></th>
                {{else}}
                <th><a href="?ip={{$pageArray.ip}}&status={{$pageArray.status}}&sortBy=ip&sort=ASC">IP</a></th>
                <th>
                    {{if $pageArray.sort == "ASC"}}
                    <a href="?ip={{$pageArray.ip}}&status={{$pageArray.status}}&sortBy=last_update&sort=DESC">添加时间 <font color="green">↑</font></a>
                    {{else}}
                    <a href="?ip={{$pageArray.ip}}&status={{$pageArray.status}}&sortBy=last_update&sort=ASC">添加时间 <font color="green">↓</font></a>
                    {{/if}}
                </th>
                {{/if}}
                <th>操作</th>
                <th>添加人</th>
            </tr>
            {{foreach from=$pageArray.data item=vitem key=key}}
            <tr>
                <td><input name="groupCheckbox" type="checkbox" class="select-item" data-id="{{$vitem.id}}" data-ip="{{$vitem.ip}}"/>
                </td>
                <td>{{$vitem.id}}</td>
                <td><a href="/houtai_gl/reg_query?regip={{$vitem.ip}}&type=username" target="_blank">{{$vitem.ip}}</a></td>
                <td>{{$vitem.last_update}}</td>

                {{if $pageArray.status=='1'}}
                <td><a href="javascript:void(0)" class="limit-delete" data-id="{{$vitem.id}}" data-ip="{{$vitem.ip}}">删除</a></td>
                {{else}}
                <td><a href="javascript:void(0)" class="limit-revert" data-id="{{$vitem.id}}" data-ip="{{$vitem.ip}}">还原</a></td>
                {{/if}}

                <td>{{$vitem.op_user}}</td>
            </tr>
            {{/foreach}}
            </tbody>
        </table>
        <!--table数据列表 结束-->
    </div><!--contentWrap end-->

    {{if $pageArray.data }}
    <div class="funList clearfix">
        <div class="left ">
            {{if $pageArray.status=='1'}}
            <a href="javascript:" class="styleBtnBlue left ml10" id="delete-multi">批量删除</a>
            {{else}}
            <a href="javascript:" class="styleBtnBlue left ml10" id="revert-multi">批量还原</a>
            {{/if}}
        </div>
    </div>
    {{/if}}

    {{$pageArray.listPage}}

    <div class="boxMask" style="display: none;">透明遮罩层</div>
    <div class="boxMod w740">

        <div class="boxTop"><h2 class="boxTit">用户信息详情</h2><a href="javascript:" class="BoxClose">关闭按钮</a></div>
        <div class="scrollWrap" style="height: 300px;">
            <div class="tabModA mtb20">
                <div class="mtrbl10">
                    <textarea id="ips" rows="14" cols="115"></textarea>

                    <div class="funList clearfix">
                        <div class="left ">
                            <p>逗号分隔,或者一行一个ip</p>
                        </div>
                        <div class="right">
                            <a id="add-commit" class="styleBtnBlue right ml10" href="javascript:">添加</a>
                        </div>
                    </div>
                </div>
            </div><!--tabModA end-->
        </div>
    </div>


</div>


<script type="text/javascript" src="/js/jquery.min.js"></script>
<script type="text/javascript" src="/js/boxfixed.js"></script>
<script>

    var limit_type = '{{$pageArray.type}}';

    $("#select-all").click(function () {
        var checkboxElements = $(".select-item");
        for (var i = 0; i < checkboxElements.length; i++) {
            var checkbox = checkboxElements[i];
            if (checkbox.name === "groupCheckbox" && checkbox.type === "checkbox") {
                if (document.getElementById('select-all').checked) {
                    checkbox.checked = true;
                } else {
                    checkbox.checked = false;
                }
            }
        }
    });

    $('#search-btn').click(function () {
        $('#search-form').submit();
    });

    $('#ip-input').keyup(function (event) {
        if (event.keyCode == 13) {
            $('#search-form').submit();
        }
    });

    $('.selectList dd a').click(function () {
        $('input[name="status"]').val($(this).attr('value'));
    });

    $('.selectList dd a').each (function (a, b) {
        var selectArr = new Array;
        selectArr['1'] = '正常';
        selectArr['2'] = '已删除';
        if ($(this).attr('value') == '{{$pageArray.status}}') {
            $(this).parents(".selectWrap").find(".getListVal").val(selectArr[$(this).attr('value')]);
            $('input[name="status"]').val($(this).attr('value'));
        }
    });

    $('.BoxClose').click(function () {
        $('.boxMask').hide();
        $('.boxMod').hide();
    });

    $('#subSure').click(function () {
        $('.boxMask').hide();
        $('.boxMod').hide();
    });

    $('#add').click(function () {
        $('.boxMask').show();
        $('.boxMod').show();
        $('.boxMod').css("margin-top", -($('.boxMod').height() / 2));
    });

    $('#add-commit').click(function () {
        var ips = $("#ips").val();
        var type = getQuery("type", "white");

        var posting = $.post("/houtai_gl/limit_ip/add_reg_limit_ips/" + limit_type, {ips: ips});
        posting.success(function (response) {
            if (response[0] == 1) {
                if (response["illegalIps"].length>0) {
                    alert("部分成功, 这些ip错误:" + response['illegalIps'].join("  "));
                    window.location = location.href;
                } else {
                    alert("成功");
                    window.location = location.href;
                }
                return;
            }
            if (response["illegalIps"].length>0) {
                alert("ip错误:" + response['illegalIps'].join("  "));
            }
        }).error(function () {

        });
    });

    $('#delete-multi').click(function () {
        var checkboxElements = $(".select-item");

        var ids = [];
        var ips = [];
        for (var i = 0; i < checkboxElements.length; i++) {
            var checkbox = checkboxElements[i];
            if (checkbox.name === "groupCheckbox" && checkbox.type === "checkbox") {
                if (checkbox.checked === true) {
                    ids.push($(checkbox).attr("data-id"));
                    ips.push($(checkbox).attr("data-ip"));
                }
            }
        }

        if (ids.length == 0) {
            return;
        }
        if (window.confirm("确认批量删除" + ids.length + "条记录?")) {
            ids = ids.join(",");
            ips = ips.join(",");
            var posting = $.post("/houtai_gl/limit_ip/del_reg_limit_ips/" + limit_type, {ids: ids, ips: ips});
            posting.success(function (response) {
                console.log(response);
                window.location = location.href;
            }).error(function () {
                window.location = location.href;
            });
        }
    });

    $('.limit-delete').click(function () {
        var id = $(this).attr("data-id");
        var ip = $(this).attr("data-ip");
        var type = getQuery("type", "white");

        if (window.confirm("确认删除?")) {
            var posting = $.post("/houtai_gl/limit_ip/del_reg_limit_ips/" + limit_type, {ids: id, ips: ip});
            posting.success(function (response) {
                console.log(response);
                window.location = location.href;
            }).error(function () {
                window.location = location.href;
            });
        }
    });

    $('#revert-multi').click(function () {
        var checkboxElements = $(".select-item");

        var ids = [];
        var ips = [];
        for (var i = 0; i < checkboxElements.length; i++) {
            var checkbox = checkboxElements[i];
            if (checkbox.name === "groupCheckbox" && checkbox.type === "checkbox") {
                if (checkbox.checked === true) {
                    ids.push($(checkbox).attr("data-id"));
                    ips.push($(checkbox).attr("data-ip"));
                }
            }
        }

        if (ids.length == 0) {
            return;
        }
        if (window.confirm("确认批量还原" + ids.length + "条记录?")) {
            ids = ids.join(",");
            ips = ips.join(",");
            var posting = $.post("/houtai_gl/limit_ip/revert_reg_limit_ips/" + limit_type, {ids: ids, ips: ips});
            posting.success(function (response) {
                console.log(response);
                window.location = location.href;
            }).error(function () {
                window.location = location.href;
            });
        }
    });

    $('.limit-revert').click(function () {
        var id = $(this).attr("data-id");
        var ip = $(this).attr("data-ip");
        var type = getQuery("type", "white");

        if (window.confirm("确认还原?")) {
            var posting = $.post("/houtai_gl/limit_ip/revert_reg_limit_ips/" + limit_type, {ids: id, ips: ip});
            posting.success(function (response) {
                console.log(response);
                window.location = location.href;
            }).error(function () {
                window.location = location.href;
            });
        }
    });


    var parseQuery = function (a) {
        var b = {};
        if (!a || a.length < 1) {
            return b;
        }
        var c = a.split("&");
        for (var i = 0; i < c.length; i++) {
            var q = c[i];
            if (q) {
                var d = q.split("=");
                if (d.length == 2) {
                    b[d[0]] = decodeURIComponent(d[1]);
                }
            }
        }
        return b
    };

    queryMap = -1;
    var getQuery = function (a, defaultValue) {
        if (this.queryMap === -1) {
            var b = location.href.split("?");
            this.queryMap = this.parseQuery(b[1])
        }
        if (!this.queryMap[a]) {
            return defaultValue;
        } else {
            return this.queryMap[a];
        }
    };

    $('.navList a').each(function (){
        if ($(this).attr('href') == '/houtai_gl/suspect/ips' )
        {
            $(this).addClass('select');
        }
    });

</script>
<script src="/js/global.js"></script>
</body>
</html>
