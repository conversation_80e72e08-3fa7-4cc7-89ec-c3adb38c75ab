<?php

namespace Admin;

class TagListModel extends \Model
{
    /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    public function getTagList($data = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col} FROM `sms_tag_list` ";

        if (!empty($data['mid']))
        {
            $where .= " mid = :mid AND";
            $paramArr[':mid'] = $data['mid'];
        }

        if (!empty($data['id']))
        {
            $where .= " id = :id AND";
            $paramArr[':id'] = $data['id'];
        }

        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by add_time desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }


    public function setTagInfo($data)
    {
        $tagInfo = $this->getTagList(array('mid' => $data['mid']));
        if (!empty($tagInfo))
        {
            $isTrue = $this->editTagInfo($data);
        }
        else
        {
            $isTrue = $this->addTagInfo($data);
        }
        return $isTrue;
    }


    public function addTagInfo($data)
    {
        $addData = array(
            'mid'      => $data['mid'],
            'tag_name' => $data['tagName'],
            'oa_product_id' => $data['oa_product_id'],
            'add_time' => date('Y-m-d H:i:s'),
        );
        $isTrue = $this->pdo->insert('sms_tag_list', $addData);
        return $isTrue !== false ? true : false;
    }

    public function editTagInfo($data)
    {
        $condition = array('where' => ' `mid` = :mid ', 'params' => array(':mid' => $data['mid']));
        $updata = array(
            'tag_name' => $data['tagName'],
            'oa_product_id' => $data['oa_product_id'],
            'add_time' => date('Y-m-d H:i:s'),
        );
        $isTrue = $this->pdo->update('sms_tag_list', $updata, $condition);
        return $isTrue === false ? false : true;
    }

    /**
     * 发送量
     *
     * @param array $data data
     *
     * @return array|bool
     */
    public function getTagLogTotal($data)
    {
        $postion = '';
        if (!empty($data['pos']))
        {
            $postion = ',stl.positionId';
        }
        $tableName = self::getTableName(date('Ym', strtotime($data['endTime'])));
        $sqlIndex = "SELECT MAX(log_id) as id_max, MIN(log_id) as id_min from {$tableName} WHERE `send_time` BETWEEN :startTime AND :endTime";
        $idIndex = $this->pdo->find($sqlIndex, [
            ':startTime' => $data['startTime'],
            ':endTime'   => $data['endTime'],
        ]);
        $sql = "SELECT COUNT(stl_id) AS total, mid{$postion}, add_time 
            FROM sms_tag_log as stl 
            JOIN {$tableName} as msl on stl.log_id = msl.log_id 
            WHERE stl.log_id >= :idMin AND stl.log_id <= :idMax AND msl.send_status >= :sendStatus 
            GROUP BY stl.add_time,stl.mid{$postion}";
        $paramArr = [
            ':idMin'      => $idIndex['id_min'],
            ':idMax'      => $idIndex['id_max'],
            ':sendStatus' => 1,
        ];

        return $this->pdo->findAll($sql, $paramArr);
    }

    public function getPositionInfo($data)
    {
        $where = '';
        $paramArr = array();
        $sql = "SELECT * FROM `position_list` ";

        if (isset($data['pid']))
        {
            $where .= " pid = :pid ";
            $paramArr[':pid'] = $data['pid'];
        }

        if (!empty($data['pl_id']))
        {
            $where .= " pl_id = :pl_id ";
            $paramArr[':pl_id'] = $data['pl_id'];
        }
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by pid desc';
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    /**
     * @param $logIds
     *
     * @return array|bool
     */
    public function getTagInfoByLogIds($logIds)
    {
        if (count($logIds) == 0)
        {
            return array();
        }
        $params = array();
        foreach ($logIds as $key => $id)
        {
            if (!empty($id))
            {
                $params[":id$key"] = trim($id);
            }
        }
        $sql = "SELECT * FROM `sms_tag_log` WHERE `log_id` IN (" . implode(", ", array_keys($params)) . ") ;";
        $res = $this->pdo->findAll($sql, $params);
        $data = array();
        if (is_array($res))
        {
            foreach ($res as $key => $val)
            {
                $data[$val["log_id"]] = $val;
            }
        }
        return $data;
    }
}
