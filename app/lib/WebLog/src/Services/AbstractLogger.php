<?php
/**
 * Created on 2019-10-29 11:37
 * FileName AbstractLogger.php
 *
 * 日志抽象类
 * <AUTHOR>
 * @email <EMAIL>
 */

namespace WebLogger\Services;

use Psr\Log\LogLevel;

abstract class AbstractLogger
{
    /**
     * @param string $msg 日志消息
     * @param array  $context 上下文
     *
     * @return mixed
     */
    public function error($msg, $context = array())
    {
        return $this->log($msg, LogLevel::ERROR, $context);
    }

    /**
     * @param string $msg 日志消息
     * @param array  $context 上下文
     *
     * @return mixed
     */
    public function info($msg, $context = array())
    {
        return $this->log($msg, LogLevel::INFO, $context);
    }

    /**
     * @param string $msg 日志消息
     * @param array  $context 上下文
     *
     * @return mixed
     */
    public function debug($msg, $context = array())
    {
        if (defined('RUNMODE') && (RUNMODE != 'prod')) {
            return $this->log($msg, LogLevel::DEBUG, $context);
        }
    }

    /**
     * @param string $msg 日志消息
     * @param array  $context 上下文
     *
     * @return mixed
     */
    public function warning($msg, $context = array())
    {
        return $this->log($msg, LogLevel::WARNING, $context);
    }

    /**
     * @param string $msg 日志信息
     * @param array  $context 上下文
     *
     * @return bool
     */
    public function notice($msg, $context = array())
    {
        return $this->log($msg, LogLevel::NOTICE, $context);
    }

    /**
     * @param string $msg 日志信息
     * @param array  $context 上下文
     *
     * @return bool
     */
    public function critical($msg, $context = array())
    {
        return $this->log($msg, LogLevel::CRITICAL, $context);
    }

    /**
     * @param string $msg 日志信息
     * @param array  $context 上下文
     *
     * @return bool
     */
    public function alert($msg, $context = array())
    {
        return $this->log($msg, LogLevel::ALERT, $context);
    }

    /**
     * @param string $msg 日志信息
     * @param array  $context 上下文
     *
     * @return bool
     */
    public function emergency($msg, $context = array())
    {
        return $this->log($msg, LogLevel::EMERGENCY, $context);
    }


    /**
     * 功    能：转换编码。 数组key转码或自动识别转码，需使用arraymyicov方法
     * 修改日期：2019/7/26
     *
     * @param mixed       $array 待转码数据
     * @param string      $in 输入编码
     * @param string|null $out 输出编码，默认UTF-8和GBK编码互转
     *
     * @return array|string
     */
    protected function arrayIconv4GBK($array, $in = 'GBK', $out = null)
    {
        if (is_null($out)) {
            $out = strtoupper($in) == 'GBK' ? 'UTF-8' : 'GBK';
        }
        $tempArray = array();
        if (is_array($array)) {
            foreach ($array as $key => $value) {
                if (is_array($value)) {
                    $value = $this->arrayIconv4GBK($value, $in, $out);
                } elseif (is_string($value)) {
                    $value = mb_convert_encoding($value, $out, $in);
                }
                $tempArray[$key] = $value;
            }
        } else {
            if (is_string($array)) {
                $tempArray = mb_convert_encoding($array, $out, $in);
            } else {
                $tempArray = $array;
            }
        }

        return $tempArray;
    }

    /**
     * 获取日志级别
     *
     * @return array
     */
    public static function getLogLevels()
    {
        return array(
            LogLevel::DEBUG,
            LogLevel::INFO,
            LogLevel::NOTICE,
            LogLevel::WARNING,
            LogLevel::ERROR,
            LogLevel::CRITICAL,
            LogLevel::ALERT,
            LogLevel::EMERGENCY,
        );
    }

    /**
     * 执行写入
     *
     * @param string $msg 信息
     * @param string $level 级别
     * @param array  $context 上下文
     *
     * @return mixed
     */
    abstract public function log($msg, $level, $context = array());
}
