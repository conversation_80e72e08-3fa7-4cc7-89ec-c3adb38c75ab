<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/1/3
 * Time: 13:32
 */
includeBase('ExternalController');

use \Common\Utils\Url;

class QuickController extends ExternalController
{
    /**
     * 快捷发送短信
     * -
     * @return void
     */
    public function actionSendQuickCode()
    {
        $this->startSession();
        $necessaryField = array(
            'exId',
            'phone',
            'timestamp',
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check("data", $necessaryField);
        if ($aesDecodeData === false) {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        //$aesDecodeData['appkey']   后期如果刷短信，可以使用
        list($exId, $phone) = $this->getParamAndAutoDeny($necessaryField);

        $QuickAction = loadAction("Quick");
        if ($QuickAction->isLockRepeatRequest($phone)) {
            $this->codeResponse("4117", "", "请求过快，稍后再试！");
        }

        $checkCode = $aesDecodeData['checkCode'];
        $ShowCaptchaAction = loadAction('ShowCaptcha');
        //通过exid可以调节是否显示验证码，现在图片验证码接口必须传exid，如果存在刷短信，可以换成动态图片
        $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($exId, $phone, $checkCode);
        if ($showCaptcha["code"] != 200) {
            $this->echoResponse($showCaptcha);
        }
        $memberModel = loadModel('member');
        $phoneAction = loadAction("phone");
        $smsFilterConfig = [
            'mNum' => 15,
            'hNum' => 15,
            'dNum' => 50,
        ];
        if ($memberModel->checkPhone($phone, true) != 0) {
            $retCode = $phoneAction->sendLoginCode($exId, $phone, 7, 453, false, $smsFilterConfig);
            switch ($retCode) {
                case 200:
                    $this->codeResponse(200, "", "发送成功！");
                    break;
                case 300:
                    $this->codeResponse(300, "", "请输入正确的手机号码！");
                    break;
                case 400:
                    $this->codeResponse(4100, "", "发送频繁，请稍后再试！");
                    break;
                case 403:
                    $this->codeResponse(4103, "", "该手机号禁止使用！");
                    break;
                case 404:
                    $this->codeResponse(4104, "", "该手机号不存在！");
                    break;
                default:
                    $this->codeResponse(5000, "", "服务器忙，请稍后再试！");
                    break;
            }
        } else {
            $retCode = $phoneAction->sendRegCode($exId, $phone, 7, 454, false, $smsFilterConfig);
            switch ($retCode) {
                case 200:
                    $this->codeResponse("200", "", "发送成功！");
                    break;
                case 300:
                    $this->codeResponse("300", "", "请输入正确的手机号码！");
                    break;
                case 301:
                    $this->codeResponse("301", "", "该手机号已被其他用户占用！");
                    break;
                case 302:
                    $this->codeResponse("302", "", "该手机号已被其他用户绑定！");
                    break;
                case 400:
                    $this->codeResponse("4100", "", "发送频繁，请稍后再试！");
                    break;
                default:
                    $this->codeResponse("5000", "", "服务器忙，请稍后再试！");
                    break;
            }
        }
    }

    /**
     * 快捷登录
     * -
     * @return void
     */
    public function actionQuickLogin()
    {
        $LoginToken = loadAction("LoginToken");
        //校验必填字段及签名
        $necessaryField = array(
            'exId',
            'phone',
            'verify_code',
            'extendData',
            'timestamp',
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check('data', $necessaryField);
        if ($aesDecodeData === false) {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        //key app密钥
        //extendData
        list($exId, $phone, $verifyCode, $extendData) = $this->getParamAndAutoDeny($necessaryField);
        $extendData = htmlspecialchars_decode($extendData);
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone)) {
            $this->codeResponse("300", "", "请输入正确的手机号码！");
        }
        $QuickAction = loadAction("Quick");
        if ($QuickAction->isLockRepeatRequest($phone)) {
            $this->codeResponse("4117", "", "请求过快，稍后再试！");
        }
        $phoneAction = loadAction("phone");
        $memberModel = loadModel('member');
        if ($memberModel->checkPhone($phone, true) != 0) {
            if (!$QuickAction->checkCodeInvalid('login', $exId, $phone)) {
                $this->codeResponse(4110, "", "短信验证码已失效，请重新获取");
            }
            //登录验证码校验
            if (!$phoneAction->checkLoginCode($exId, $phone, md5($phone . $verifyCode), false, true)) {
                $this->codeResponse("303", "", "手机短信验证码错误！");
            }

            $memberModel = loadModel('member');
            $passid = $memberModel->getPassidByPhone($phone);
            if (!$passid) {
                //手机号,未绑定手机号, 但存在用户名
                $passid = $memberModel->getPassidByUsername($phone);
            }
            $user = [];
            if (!empty($passid)) {
                $user = $memberModel->read($passid);
                $isProhibitLogin = $phoneAction->prohibitUsernameAsPhoneDiffBindPhoneLogin($user, $phone);
                if ($isProhibitLogin) {
                    $this->codeResponse(400, "", "登录失败！");
                }
            }
            //收集注册登录信息
            $source = Url::getStringParam("exId", "mobile");
            $st = !!$passid;
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
            \Service\AccountStat\AccountStat::collect($type, $clientType, $phone, $st, $source);
            if ($passid) {
                $phoneAction = loadAction("phone");
                $phoneAction->repairUsernameWithPhoneNum($phone, $user, "external");
                $getToken = $LoginToken->setLoginToken($exId, $passid);
                if ($getToken !== false) {
                    $user["m_uid"] = unserialize($user['m_uid']);
                    $uid = $user['m_uid']['1'];
                    $pushArr = [
                        'passid' => $passid,
                        'phone'  => $phone,
                        'uid' => $uid,
                        'extendData' => $extendData
                    ];
                    $responseData = [
                        "extend"   => $LoginToken->pushLoginInfo($exId, $pushArr),
                        "response" => $LoginToken->returnResponseData($exId, ['passid' => $passid, 'uid' => $uid], false),
                        "token"    => $getToken,
                    ];
                    $this->codeResponse(200, $responseData, "登录成功");
                } else {
                    $this->codeResponse(401, "", "登录失败！");
                }
            } else {
                $this->codeResponse(400, "", "登录失败！");
            }
        } else {
            if (!$phoneAction->checkRegCode($exId, $phone, md5($phone . $verifyCode), false, true)) {
                $this->codeResponse(400, "", "短信验证失败!");
            }
            $userClientIp = get_client_ip();
            $msgs = array(
                300 => array(
                    0 => '请输入正确的手机号码',
                    1 => '密码最少6个字符',
                    2 => '密码最多16个字符',
                    3 => '此手机号已被注册',
                ),
                400 => array(
                    0 => 400, // 非法域名调用
                    1 => 401, // 非法IP调用
                    2 => 402, // 批量刷CHECK
                    3 => 403, // IP段被禁止
                    4 => 404, // IP被禁止
                    5 => 405 // 未验证通过（缺少isValidate）
                ),
            );
            $pwd = mt_rand() . time();
            $password = substr(md5($pwd . MD5KEY), 0, 16);

            $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
            $pwdScore = $Zxcvbn->passwordStrength($password);
            $pwdStrength = $pwdScore['score'];
            $regAction = loadAction('reg');

            $gid = 300;
            // 客户端 注册
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
            $source = \Common\Utils\Url::getStringParam("exId", "mobile");
            $result = $regAction->regPhone(
                $phone,
                $password,
                $pwdStrength,
                $userClientIp,
                "login.2345.com",
                $gid,
                $source,
                $clientType
            );
            $states = explode(".", $result[0]);
            if ($states[0] == 400) {
                $this->codeResponse(403, "", 'errorCode:' . $msgs[$states[0]][$states[1]]);
            } elseif ($states[0] == 300) {
                $this->codeResponse($states[0] + $states[1], "", $msgs[$states[0]][$states[1]]);
            } elseif ($states[0] == 200) {
                $passid = $result[1]['passid'];
                $uid = $result[1]['uid'];
                $getToken = $LoginToken->setLoginToken($exId, $passid);
                if ($getToken !== false) {
                    $pushArr = [
                        'passid' => $passid,
                        'phone'  => $phone,
                        'uid' => $uid,
                        'extendData' => $extendData
                    ];
                    $responseData = [
                        "extend"   => $LoginToken->pushLoginInfo($exId, $pushArr),
                        "response" => $LoginToken->returnResponseData($exId, ['passid' => $passid, 'uid' => $uid], false),
                        "token"    => $getToken,
                    ];
                    $this->codeResponse("200", $responseData, "注册成功！");
                } else {
                    $this->codeResponse("502", "", "生成失败");
                }
            } else {
                $this->codeResponse("500", "", "禁止注册！");
            }
        }
    }
}
