<?php

includeBase('CronController');

class QueueDealLoginController extends CronController
{
    private $sleepTimeLength = 5;
    private $maxExecuteTime = 55;

    /**
     * RegQueueController constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * actionIndex
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $startTime = time();
        $logger = $this->getLogger('loginQueue');
        $loginQueueRedis = loadAction('loginQueueRedis');
        $queueEmptyCount = 0;
        $regCount = 0;
        $regSuccessCount = 0;
        $regFailureCount = 0 ;
        $memberModel = loadModel('member');
        $passId = false;
        $memberRedis = loadAction('memberRedis');

        while (time() - $startTime < $this->maxExecuteTime)
        {
            $loginQueueLength = $loginQueueRedis->loginQueueLength();
            if ($loginQueueLength <= 0)
            {
                if ($loginQueueLength == -1)
                {
                    $logger->info('登录队列检查队列长度时,redis连接返回-1');
                }
                $queueEmptyCount++;
                if ($queueEmptyCount > 10000)
                {
                    $logger->info('登录队列本分钟内第' . $queueEmptyCount . '次长度为0,循环间隔时间为:' . $this->sleepTimeLength);
                    sleep($this->sleepTimeLength);
                }
                continue;
            }
            else
            {
                if ($queueEmptyCount > 0)
                {
                    $queueEmptyCount = 0;
                }
            }

            $redisValue = '';
            try
            {
                $redisValue = $loginQueueRedis->popLoginQueue();
                $loginUserInfo = unserialize($redisValue);

                $regCount++;
                if (!isset($loginUserInfo['phone']) || !isset($loginUserInfo['passId']))
                {
                    $logger->notice('passId为空');
                    $loginQueueRedis->pushToLoginQueue($redisValue);
                    $regFailureCount++;
                    continue;
                }
                $passId = $loginUserInfo['passId'];
                $phone = $loginUserInfo['phone'];

                $user = $memberModel->read($passId);
                $phoneAction = loadAction("phone");
                $newUserName = $phoneAction->repairUsernameWithPhoneNum($phone, $user, "webapi");
                if (!empty($newUserName) && $newUserName != $user['username'])
                {
                    $newUserInfo = $memberModel->getSaveInfoMemberRedis($passId, $user['m_uid'], $user['gid'], $newUserName);
                    $memberRedis->hashSet($phone, REG_PHONE_INFO_REDIS_KEY, $phone, serialize($newUserInfo));
                }
                $regSuccessCount++;
            }
            catch (Error $er)
            {
                $regFailureCount++;
                $logger->notice('注册队列执行注册逻辑时发生Error');
                $loginQueueRedis->pushToLoginQueue($redisValue);
            }
        }

        $memberRedis->closeUserDataRedis();
        $loginQueueRedis->closeLoginQueueRedis();
        PdoEx::delInstance(DB_PASSPORT);
        $logger->info('本分钟注册队列执行结束,共处理[' . $regCount . ']条数据,成功[' . $regSuccessCount . ']条,失败[' . $regFailureCount . ']条');
        exit();
    }
}
