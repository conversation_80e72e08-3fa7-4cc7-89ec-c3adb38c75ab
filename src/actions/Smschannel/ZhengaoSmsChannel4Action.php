<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 摘    要：正奥4短信通道
 * 作    者：hw
 * 修改日期：2016-01-12
 */

namespace Smschannel;

use FuncAction;
use WebLogger\Facade\LoggerFacade;

class ZhengaoSmsChannel4Action extends SmsChannelAction
{
    private static $commitCode = array(
        0  => '成功',
        1  => '用户鉴权错误(服务器)',
        2  => 'IP鉴权错误(服务器)',
        3  => '手机号码在黑名单',
        4  => '手机号码格式错误',
        5  => '短信内容有误',
        7  => '手机号数量超限',
        8  => '账户已停用',
        9  => '未知错误9',
        10 => '时间戳已过期',
        11 => '同号码同模板发送频率过快',
        12 => '同号码同模板发送次数超限',
        13 => '短信内容包含运营商敏感词',
        14 => '扩展号不合法',
        15 => '扩展信息长度过长',
        16 => '手机号重复',
        17 => '批量发送文件为空',
        18 => 'json解析错误',
        19 => '用户已退订',
        99 => '账户余额不足',
        -128 => '请求接口失败',
    );

    /**
     * ZhengaoSmsChannel4Action constructor.
     *
     * @param string $user   username
     * @param string $pwd    password
     * @param string $apiUrl api url
     */
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }
    
    /**
     * 发送短信，适用
     *
     * @param string $phone 手机号
     * @param string $msg   短信内容
     *
     * @return int
     */
    public function send($phone, $msg)
    {
        // 备用通道
        $accountMap = $this->backupConf();
        if (isset($accountMap[$this->user])) {
            // 2025-02-09 2点到9点，点集服务器迁移，使用备用通道
            $nowTime = time();
            LoggerFacade::info("backupSend : now " . $nowTime . " start: " . strtotime('2025-02-15 02:00:00'));
            if ($nowTime > strtotime('2025-02-15 02:00:00') && $nowTime < strtotime('2025-02-15 09:00:00')) {
                return $this->backupSend($phone, $msg);
            }
        }
        
        $msgUtf = iconv('GBK', 'UTF-8', $msg);
        $timeStamp = time() * 1000;
        $params = array(
            'account'    => $this->user,
            'password'   => md5($this->pwd . $phone . $timeStamp),
            'content'    => $msgUtf,
            'mobile'     => $phone,
            'timestamps' => $timeStamp,
        );
        if (strpos($params['mobile'], '00') === 0)
        {
            $params['extInfo'] = 'earth';
        }
        $json = FuncAction::postTimeout($this->apiUrl, $params, 10);
        if (!$json) {
            LoggerFacade::error("request " . $this->apiUrl . " response is empty");
            $this->setMsgArray(-128);
            $this->setMsgId(0);
            return self::RET_ERR;
        } else {
            $res = json_decode($json, true);
            //由于可能存在curl超时的情况，没有返回值，但是发送成功了， 所以这儿判断失败
            $returnObj = $res['Rets'][0];
            if (!isset($returnObj['Rspcode'])) {
                LoggerFacade::error("Rspcode is empty " . var_export($returnObj, true));
            } else if (isset($returnObj['Rspcode']) && !isset(self::$commitCode[$returnObj['Rspcode']])) {
                LoggerFacade::error("unknow Rspcode:" . $returnObj['Rspcode']);
            }
            $this->setMsgArray($returnObj['Rspcode']);
            $this->setMsgId($returnObj['Msg_Id']);
        }

        return $returnObj['Rspcode'] === 0 ? self::RET_SUCC : self::RET_ERR;
    }

    public function backupConf(): array
    {
        return [
            'ersan106' => ['user' => 'dianji2345', 'pwd' => 'oKuXJwGnrADC', 'sign' => '【2345网络】'],
            'wpzmt1062TXw' => ['user' => 'dianjiwpzm', 'pwd' => 'SWMDMIFpvMIx', 'sign' => '【王牌智媒】'],
        ];
    }
    /**
     * 备用通道
     * @return void
     */
    public function backupSend($phone, $msg)
    {
        $url = 'http://47.121.193.125:8001/sms/api/sendMessageMass';
        //账号映射
        $accountMap = $this->backupConf();
        $user = $accountMap[$this->user]['user'];
        $pwd = $accountMap[$this->user]['pwd'];
        $sign = $accountMap[$this->user]['sign'];
        
        $msgUtf = iconv('GBK', 'UTF-8', $sign.$msg);
        $timeStamp = time() * 1000;
        $params = array(
            'userName'    => $user,
            'content'    => $msgUtf,
            'phoneList'     => [$phone],
            'timestamp' => $timeStamp,
            'sign' => md5($user.$timeStamp.md5($pwd)),
        );

        $json = FuncAction::postTimeout($url, json_encode($params, JSON_UNESCAPED_UNICODE), 10, false, true);
        LoggerFacade::info("backupSend : request " . $url . " phone: ". $phone. "ret:" .$json);
        if (!$json) {
            $this->setMsgArray(-128);
            $this->setMsgId(0);
            return self::RET_ERR;
        } else {
            $returnObj = json_decode($json, true);
            $this->setMsgArray($returnObj['code']);
            $this->setMsgId($returnObj['msgId']);
        }
        return $returnObj['code'] === 0 ? self::RET_SUCC : self::RET_ERR;
    }
    
    /**
     * 相同内容多个手机号群发
     *
     * @param array  $phone phone1,phone2,phone3
     * @param string $msg   message
     *
     * @return int
     */
    public function multiPhoneSend($phone, $msg)
    {
        $msgUtf = iconv('GBK', 'UTF-8', $msg);
        $phone = implode(",", $phone);
        $timeStamp = time() * 1000;
        $params = array(
            'account'    => $this->user,
            'password'   => md5($this->pwd . $phone . $timeStamp),
            'content'    => $msgUtf,
            'mobile'     => $phone,
            'timestamps' => $timeStamp,
        );
        $json = FuncAction::postTimeout($this->apiUrl, $params, 10);
        $res = json_decode($json, true);
        //由于可能存在curl超时的情况，没有返回值，但是发送成功了， 所以这儿判断失败

        foreach ($res['Rets'] as $key => $value)
        {
            $mobile = $value['Mobile'];
            $this->msgIdList[$mobile] = $value['Msg_Id'];
            $rspCode = $value['Rspcode'];
            $this->operatorMsgList[$mobile] = array(
                'codeStatus' => $rspCode,
                'codeDesc'   => !empty(self::$commitCode[$rspCode]) ? self::$commitCode[$rspCode] : '未知错误',
                'result'     => $rspCode === 0 ? self::RET_SUCC : self::RET_ERR,
            );
        }

        return is_array($res['Rets']) ? true : false;
    }

    /**
     * 不同手机号不同内容无变量群发
     *
     * @param array $multiContent multiContent
     *
     * @return int
     */
    public function batchSend($multiContent)
    {
        $mobileParams = array();
        foreach ($multiContent as $key => $value)
        {
            list($phone, $msg) = $value;
            $mobileParams[] = array(
                "parms"  => array($msg),
                "mobile" => $phone,
            );
        }
        $content = "#code#";
        $objJson = array(
            "content"     => $content,
            "mobileParms" => \EncodingAction::iteratorArray($mobileParams, "UTF-8", "GBK"),
        );

        $timeStamp = time() * 1000;
        $data = array(
            'account'    => $this->user,
            'password'   => md5($this->pwd . $timeStamp),
            "objjson"    => json_encode($objJson),
            'timestamps' => $timeStamp,
        );

        $url = $this->apiUrl . "/msgHttp/json/multimt";
        $response = FuncAction::postTimeout($url, $data, 10, false);
        $response = json_decode($response, true);

        if ($response && isset($response['Rets']))
        {
            $oneSuccess = false;
            $oneFailed = false;
            foreach ($response['Rets'] as $ret)
            {
                if (isset($ret['Rspcode']) && $ret['Rspcode'] == 0)
                {
                    $this->msgIdList[$ret['Mobile']] = $ret['Msg_Id'];
                    $this->SetMultiMsgArray($ret['Mobile'], 0, self::$commitCode[0]);
                    $oneSuccess = true;
                }
                elseif (isset($ret['Rspcode']) && $ret['Rspcode'] > 0)
                {
                    if (in_array($ret['Rspcode'], array(3, 4, 11, 12, 19))) // 可能的单条错误
                    {
                        $this->msgIdList[$ret['Mobile']] = $ret['Msg_Id'];
                        $this->SetMultiMsgArray($ret['Mobile'], $ret['Rspcode'], self::$commitCode[$ret['Rspcode']]);

                        // 部分成功
                        $oneFailed = true;
                    }
                    else
                    {
                        foreach ($multiContent as $key => $value)
                        {
                            list($phone) = $value;
                            $this->SetMultiMsgArray($phone, $ret['Rspcode'], self::$commitCode[$ret['Rspcode']]);
                        }

                        return self::RET_ERR;
                    }
                }
            }

            return $oneSuccess ? ($oneFailed ? self::RET_SUCC_PART : self::RET_SUCC) : self::RET_ERR;
        }

        return self::RET_ERR;
    }

    /**
     * 变量短信
     *
     * @param array  $multiContent multiContent
     * @param string $template     模板
     *
     * @return int
     */
    public function variableMsgSend($multiContent, $template)
    {
        $mobileParams = array();
        foreach ($multiContent as $key => $value)
        {
            list($phone, $variables) = $value;
            $mobileParams[] = array(
                "parms"  => $variables,
                "mobile" => $phone,
            );
        }
        $content = str_replace("{\$var}", "#code#", $template);
        $objJson = array(
            "content"     => mb_convert_encoding($content, 'UTF-8', 'GBK'),
            "mobileParms" => \EncodingAction::iteratorArray($mobileParams, "UTF-8"),
        );

        $timeStamp = time() * 1000;
        $data = array(
            'account'    => $this->user,
            'password'   => md5($this->pwd . $timeStamp),
            "objjson"    => json_encode($objJson),
            'timestamps' => $timeStamp,
        );

        $url = $this->apiUrl . "/msgHttp/json/multimt";
        $response = FuncAction::postTimeout($url, $data, 10, false);
        $response = json_decode($response, true);
        $response = \EncodingAction::iteratorArray($response, "GBK");

        if ($response && isset($response['Rets']))
        {
            $oneSuccess = false;
            $oneFailed = false;
            foreach ($response['Rets'] as $ret)
            {
                if (isset($ret['Rspcode']) && $ret['Rspcode'] == 0)
                {
                    $this->msgIdList[$ret['Mobile']] = $ret['Msg_Id'];
                    $this->SetMultiMsgArray($ret['Mobile'], 0, self::$commitCode[0]);
                    $oneSuccess = true;
                }
                elseif (isset($ret['Rspcode']) && $ret['Rspcode'] > 0)
                {
                    if (in_array($ret['Rspcode'], array(3, 4, 11, 12, 19))) // 可能的单条错误
                    {
                        $this->msgIdList[$ret['Mobile']] = $ret['Msg_Id'];
                        $this->SetMultiMsgArray($ret['Mobile'], $ret['Rspcode'], self::$commitCode[$ret['Rspcode']]);

                        // 部分成功
                        $oneFailed = true;
                    }
                    else
                    {
                        foreach ($multiContent as $key => $value)
                        {
                            list($phone) = $value;
                            $this->SetMultiMsgArray($phone, $ret['Rspcode'], self::$commitCode[$ret['Rspcode']]);
                        }

                        return self::RET_ERR;
                    }
                }
            }

            return $oneSuccess ? ($oneFailed ? self::RET_SUCC_PART : self::RET_SUCC) : self::RET_ERR;
        }

        return self::RET_ERR;
    }

    /**
     * 设置响应
     *
     * @param string $phone commit phone
     * @param string $code  commit response code
     * @param string $desc  commit response cde desc
     *
     * @return void
     */
    private function setMultiMsgArray($phone, $code, $desc)
    {
        $this->operatorMsgList[$phone] = array(
            'codeStatus' => $code,
            'codeDesc'   => !empty(self::$commitCode[$code]) ? self::$commitCode[$code] : $desc,
            'result'     => $code === "0" ? self::RET_SUCC : self::RET_ERR,
        );
    }


    /**
     *set msgId
     *
     * @param string $msgId msgId
     *
     * @return null
     */
    private function setMsgId($msgId)
    {
        $this->msgId = $msgId;
    }

    /**
     * set msg array
     *
     * @param int $rspCode response code
     *
     * @return null
     */
    private function setMsgArray($rspCode)
    {
        $this->OperatorMsg = array(
            'codeStatus' => $rspCode,
            'codeDesc'   => !empty(self::$commitCode[$rspCode]) ? self::$commitCode[$rspCode] : '未知错误',
        );
    }

}
