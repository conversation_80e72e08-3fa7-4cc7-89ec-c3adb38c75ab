<?php

namespace Monitor;

use Controller;

class SmsController extends Controller
{
    // private static $channelList = [
    //     'dianji' => ['url' => 'http://sapi.appsms.cn:8088/msgHttp/json/mt', 'timeout' => 3],
    //     'yunfeng' => ['url' => 'http://api.zhuanxinyun.com/api/v2/sendSms.json', 'timeout' => 3],
    // ];

    private static $channelList = [
        'dianji' => ['url' => 'http://139.129.128.71:8086/msgHttp/json/mt', 'timeout' => 3],
        'yunfeng' => ['url' => 'http://api.zhuanxinyun.com/api/v2/sendSms.json', 'timeout' => 3],
    ];

    /**
     * 功  能：检测运营商接口可用性
     *  http://smsp.2345.net/Monitor/sms/valid/yunfeng
     *
     * @param string $channel channel
     * @return void
     */
    public function actionValid($channel = '')
    {
        if (!isset(self::$channelList[$channel])) {
            echo '指定运营商不存在！';
            exit();
        }

        $request = self::$channelList[$channel];
        $return = \FuncAction::request($request['url'], null, [
            'timeout' => $request['timeout']
        ]);
        if ($return[0] === false) {
            header('HTTP/1.1 404 Not Found');
            echo "请求 {$request['url']} 失败，超时时间：{$request['timeout']}";
            if ($return[1]) {
                echo serialize($return[1]);
            }
            exit();
        }
        echo '{}';// 预警平台，在成功是需要输出空对象，否则有问题
    }
}
