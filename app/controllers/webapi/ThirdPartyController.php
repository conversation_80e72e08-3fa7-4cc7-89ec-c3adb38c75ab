<?php

class ThirdPartyController extends Controller
{

    private $pdo = null;
    private $redis = null;
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $this->pdo = PdoEx::getInstance(DB_MY_2345);
        $this->redis = RedisEx::getInstance();
    }

    /**
     * 获取礼包
     *
     * @return void
     */
    public function actionGetLiBao()
    {
        $loginAction = loadAction('login');

        if (!$loginInfo = $loginAction->checkAuthCookie())
        {
            $this->codeResponse(- 1, "", "您已退出登录，请重新登录！");
        }
        $passid = $loginInfo["i"];
        $gameId = \Common\Utils\Url::getIntParam("gameId", 0);
        if ($gameId && $passid)
        {
            $apiDataService = new \Service\ThirdParty\ApiData();
            $libao = $apiDataService->getGameLiBao($passid, $gameId);
            if ($libao && isset($libao["status"]) && $libao["status"] == 1)
            {
                $data = array(
                    'name'         => $libao['data']['name'],
                    'libao'        => $libao['data']['packs'],
                    'game_url'     => $libao['data']['game_url'],
                    'official_url' => $libao['data']['official_url'],
                );
                $this->codeResponse(1, $data, "");
            }
            else
            {
                if ($libao && isset($libao["status"]) && $libao["status"] == - 3)
                {
                    $this->codeResponse(- 2, "", "礼包已经被抢光啦，请尝试其他游戏！");
                }
            }
        }
        $this->codeResponse(- 3, "", "网络异常，请重试！");
    }
}
