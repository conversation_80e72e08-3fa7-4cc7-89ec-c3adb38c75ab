<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/1/14
 * Time: 18:59
 */
use \Service\AccountStat\Consts\AccountStat;
use Common\Utils\Url as Requester;

class WeixinRelationUserController extends Controller
{
    /**
     * WeixinRelationUserController constructor.
     * -
     * @return void
     */
    public function __construct()
    {
        if (RUNMODE != 'development' && !HTTPS) {
            $this->codeResponse(4000, "", '请求拒绝！');
        }
    }

    /**
     * 微信app登录查询
     *
     * @return mixed
     */
    public function actionCheckUserInfo()
    {
        $this->startSession();
        $necessaryField = array(
            'appid',
            'mid',
            'wxCode',
            'clientVer',
            'timestamp',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );
        list($appid, $mid, $wxCode) = $this->getParamAndAutoDeny($necessaryField);

        $checkLogin = loadAction('checkLoginForClient');
        //验证fToken
        /** @see CheckLoginForClientAction::checkFToken() */
        if (!$checkLogin->checkFToken()) {
            $checkLogin->clearFInfo();
            $this->codeResponse(4004, "", "请求拒绝！");
        }

        $oauthAction = loadAction('oauth');
        /** @see OauthAction::weixinCallback() */
        $result = $oauthAction->weixinCallback($wxCode, $appid);
        //微信是否授权
        if ($result) {
            //注意这里需要使用unionid, 同一个开发者账号旗下不同的app
            $openid = $result['unionid'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'weixin');
            $passid = !empty($return["passid"]) ? $return["passid"] : '';

            // 这个微信账号没有被绑定，提示进行绑定操作
            $aes = new Service\Encryption\Aes\AesManager();
            $data = [
                'unId'     => $openid,
                'gender'   => $result['gender'],
                'nickname' => $result['nickname'],
            ];
            $response = [
                'data' => $aes->aes128cbcEncrypt(json_encode(\Common\Utils\Encoding::transcoding($data, 'gbk', 'utf-8'))),
            ];
            //如果passid不为空，说明已绑定
            if (!empty($passid)) {
                $userInfo = $memberModel->read($passid);
                //微信已经绑定了，没有绑定手机号码
                if (empty($userInfo['phone'])) {
                    $this->codeResponse(6001, $response, '微信已绑定，未绑定手机号码！');
                } else {
                    //微信绑定 手机号码绑定 直接登录
                    //收集登录信息
                    $st = true;
                    $source = $mid;
                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_LOGIN,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_WEIXIN,
                        $st,
                        $source
                    );
                    $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                    $userInfo["m_uid"] = unserialize($userInfo['m_uid']);
                    $uid = $userInfo['m_uid']['1'];
                    $username = $userInfo['username'];
                    $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;
                    $loginAction = loadAction('login');
                    $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                    $data = array(
                        'I' => $cookie['I'],
                    );
                    $this->codeResponse(200, $data, '登录成功！');
                }
            } else {
                $this->codeResponse(6002, $response, '该微信账号未进行绑定！');
            }
        } else {
            $this->codeResponse(201, "", '获取微信授权信息失败！');
        }
    }

    /**
     * 发送绑定短信
     * -
     * @return void
     */
    public function actionSendCode()
    {
        $this->startSession();
        $necessaryField = array(
            'mid',
            'phone',
//            'checkCode',
            'timestamp',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );
        list($mid, $phone) = $this->getParamAndAutoDeny($necessaryField);
        $QuickAction = loadAction("Quick");
        if ($QuickAction->isLockRepeatRequest($phone)) {
            $this->codeResponse("4117", "", "请求过快，稍后再试！");
        }

        $checkCode = Requester::getStringParam('checkCode');

        $ShowCaptchaAction = loadAction('ShowCaptcha');
        //通过mid可以调节是否显示验证码，现在图片验证码接口必须传mid，如果存在刷短信，可以换成动态图片
        $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($mid, $phone, $checkCode);
        if ($showCaptcha["code"] != 200) {
            $this->echoResponse($showCaptcha);
        }
        $memberModel = loadModel('member');
        $phoneAction = loadAction("phone");
        //        $smsFilterConfig = [
        //            'mNum' => 15,
        //            'hNum' => 15,
        //            'dNum' => 50,
        //        ];
        if ($memberModel->checkPhone($phone, true) != 0) {
            $retCode = $phoneAction->sendLoginCode($mid, $phone, 178, 528, false);
            switch ($retCode) {
                case 200:
                    $this->codeResponse(200, "", "发送成功！");
                    break;
                case 300:
                    $this->codeResponse(300, "", "请输入正确的手机号码！");
                    break;
                case 400:
                    $this->codeResponse(4100, "", "发送频繁，请稍后再试！");
                    break;
                case 403:
                    $this->codeResponse(4103, "", "该手机号禁止使用！");
                    break;
                case 404:
                    $this->codeResponse(4104, "", "该手机号不存在！");
                    break;
                default:
                    $this->codeResponse(5000, "", "服务器忙，请稍后再试！");
                    break;
            }
        } else {
            $retCode = $phoneAction->sendRegCode($mid, $phone, 178, 528, false, $smsFilterConfig);
            switch ($retCode) {
                case 200:
                    $this->codeResponse("200", "", "发送成功！");
                    break;
                case 300:
                    $this->codeResponse("300", "", "请输入正确的手机号码！");
                    break;
                case 301:
                    $this->codeResponse("301", "", "该手机号已被其他用户占用！");
                    break;
                case 302:
                    $this->codeResponse("302", "", "该手机号已被其他用户绑定！");
                    break;
                case 400:
                    $this->codeResponse("4100", "", "发送频繁，请稍后再试！");
                    break;
                default:
                    $this->codeResponse("5000", "", "服务器忙，请稍后再试！");
                    break;
            }
        }
    }


    /**
     * 微信快捷登录
     * -
     * @return void
     */
    public function actionBindLogin()
    {
        //校验必填字段及签名
        $necessaryField = array(
            'mid',
            'phone',
            'verify_code',
            'pushData',
            'timestamp',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );
        list($mid, $phone, $verifyCode, $pushData) = $this->getParamAndAutoDeny($necessaryField);

        $QuickAction = loadAction("Quick");
        if ($QuickAction->isLockRepeatRequest($phone)) {
            $this->codeResponse("4117", "", "请求过快，稍后再试！");
        }

        $phoneAction = loadAction("phone");
        $memberModel = loadModel('member');

        //解析微信授权信息
        $aes = new Service\Encryption\Aes\AesManager();
        $openInfo = $aes->aes128cbcHexDecrypt($pushData);
        $openInfo = json_decode($openInfo, true);
        $nickname = !empty($openInfo['nickname']) ? $openInfo['nickname'] : '';
        loadAction('Encoding');
        $nickname = \EncodingAction::transcoding($nickname, 'gbk');
        $oauthModel = loadModel('oauth');
        //根据unionid查询绑定信息
        $bindInfo = $oauthModel->getBind($openInfo['unId'], 'weixin');
        $openid = $openInfo['unId'];

        //查看注册的手机号码是否已注册
        if ($memberModel->checkPhone($phone, true) != 0) {
            $phoneStatus = 1; //已注册  老账号
        } else {
            $phoneStatus = 2; //未注册 新账号
        }

        //绑定过了微信 且绑定过手机号码 或 绑定的手机号码已存在 返回错误
        if (!empty($bindInfo)) {
            $userInfo = $memberModel->read($bindInfo['passid']);
            //已经绑定过了，返回错误
            if (!empty($userInfo['phone'])) {
                $this->codeResponse(6007, '', '微信已绑定过手机号码！');
            }
            //注册的手机号码是个老账号， 微信注册的前提下，不能再注册
            if ($phoneStatus == 1) {
                $this->codeResponse(6008, '', '微信已绑定，不能绑定老账号！');
            }
        }

        if ($phoneStatus == 1) {
            //老账号
            if (!$QuickAction->checkCodeInvalid('login', $mid, $phone)) {
                $this->codeResponse("4110", "", "短信验证码已失效，请重新获取");
            }
            //登录验证码校验
            if (!$phoneAction->checkLoginCode($mid, $phone, md5($phone . $verifyCode), false, true)) {
                $this->codeResponse("6006", "", "手机短信验证码错误！");
            }
            //老手机  已绑定微信   返回错误
            if (!empty($bindInfo)) {
                $this->codeResponse(6008, '', '该微信不能绑定老账号！');
            }
            $passid = $memberModel->getPassidByPhone($phone);

            $oauthList = $oauthModel->getBindByPassid($passid);
            !$oauthList && $oauthList = [];
            foreach ($oauthList as $oauthInfo) {
                if ($oauthInfo['type'] == 'weixin') {
                    $this->codeResponse(6009, '', '该手机已绑定过微信账号！');
                }
            }
            $userInfo = $memberModel->read($passid);
            $username = $userInfo['username'];
            $isSet = $oauthModel->setBind('weixin', $passid, $openid, $nickname, $username);
            if ($isSet === false) {
                $this->codeResponse("6003", '', "微信绑定失败！");
            }
            $userInfo["m_uid"] = unserialize($userInfo['m_uid']);
            $uid = $userInfo['m_uid']['1'];
            $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;
            $loginAction = loadAction("login");
            //收集登录信息
            $st = true;
            $source = $mid;
            \Service\AccountStat\AccountStat::collect(
                AccountStat::TP_LOGIN,
                AccountStat::CTP_APP,
                AccountStat::AC_TP_PHONE,
                $st,
                $source
            );
            $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
            $data = array(
                'I' => $cookie['I'],
            );
            $this->codeResponse(200, $data, '绑定成功！');
        } else {
            if (!$QuickAction->checkCodeInvalid('reg', $mid, $phone)) {
                $this->codeResponse(4110, "", "短信验证码已失效，请重新获取！");
            }
            if (!$phoneAction->checkRegCode($mid, $phone, md5($phone . $verifyCode), false, true)) {
                $this->codeResponse(6005, "", "短信验证失败!");
            }
            //未注册手机，  先生成微信账号，再绑定手机号码
            if (empty($bindInfo)) {
                $clientIp = get_client_ip();
                $result = $memberModel->regOAuth('weixin', $openid, $nickname, $clientIp, '', $openInfo['gender']);
                if ($result === false) {
                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_REG,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_PHONE,
                        0,
                        $mid
                    );
                    $this->codeResponse("6010", '', "微信注册失败！");
                }
                $passid = $result['passid'];
            } else {
                $passid = $bindInfo['passid'];
            }
            //微信已经注册了， 绑定新手机号码
            $retCode = $phoneAction->bind($passid, $phone);
            switch ($retCode) {
                case '200.0':
                    $userInfo = $memberModel->read($passid);
                    $username = $userInfo['username'];
                    $userInfo["m_uid"] = unserialize($userInfo['m_uid']);
                    $uid = $userInfo['m_uid']['1'];
                    $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;
                    $loginAction = loadAction("login");
                    //收集登录信息
                    $st = true;
                    $source = $mid;
                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_LOGIN,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_PHONE,
                        $st,
                        $source
                    );
                    $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                    $data = array(
                        'I' => $cookie['I'],
                    );
                    $this->codeResponse("200", $data, "绑定成功！");
                    break;
                case '300.0':
                    $this->codeResponse("300", "", "请输入正确的手机号码！");
                    break;
                case '300.1':
                    $this->codeResponse("301", "", "该手机号已被其他用户占用！");
                    break;
                case '300.2':
                    $this->codeResponse("302", "", "该手机号已被其他用户绑定！");
                    break;
                case '300.3':
                    $this->codeResponse("402", "", "已绑定过手机号！");
                    break;
                default:
                    $this->codeResponse("500", "", "服务器忙，请稍后再试！");
                    break;
            }
        }
    }
}
