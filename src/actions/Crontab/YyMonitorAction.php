<?php

namespace Crontab;
class YyMonitorAction extends \Action
{
    const SMS_TIME = 300;
    const VOICE_TIME = 600;
    const LAST_COUNT_SMS_KEY = "VoiceCode:LastCountSms";
    const LAST_COUNT_VOICE_KEY = "VoiceCode:LastCountVoice";

    public function __construct()
    {
        $this->redis = \RedisAction::connect();
    }

    public function check()
    {
        $time = time();//'1459217269'; //
        $minute = date("i", $time);
        $stime = $time - self::SMS_TIME;
        $YyMonitorModel = YyMonitorModel::getInstance();
        $smsStatusTotal = $YyMonitorModel->getSmsStatusTotal($stime);
        $smsTotal = array('error' => 0, 'success' => 0);
        foreach ($smsStatusTotal as $smsStatusInfo)
        {
            if ($smsStatusInfo['status_sms'] > 0)
            {
                $smsTotal['success'] += $smsStatusInfo['c'];
            }
            else
            {
                $smsTotal['error'] += $smsStatusInfo['c'];
            }
        }

        $smsCount = array_sum($smsTotal);
        $lastCountSms = $this->redis->get(self::LAST_COUNT_SMS_KEY); // 获取最后一次剩余次数
        // 判断前一次次数是否存在，存在则累加计算
        if (strpos($lastCountSms, ",") !== false)
        {
            $tmpSms = explode(',', $lastCountSms);
            $smsCount += $tmpSms[0];
            $smsTotal['success'] += $tmpSms[1];
        }

        $smsPer = round($smsTotal['success'] / $smsCount * 100);
        $smsFlag = $smsPer . " = round(" . $smsTotal['success'] . " / " . $smsCount .
            " * 100)";
        if ($smsCount > 10)
        {
            if ($smsPer < 80)
            {
                //发短信
                $date = " [" . date("Y-m-d H:i:s", $time) . "] ";
                $msg = $date . "短信验证码发送有问题，送达率低于80%，" . "当前送达率为：" . $smsFlag . "% 。";
                \SendNoticeAction::sendSmsNotice($msg, 6);
                $smsFlag = "error";
            }
            $this->redis->set(self::LAST_COUNT_SMS_KEY, 0); // 更新最后一次剩余次数
        }
        else
        {
            $tmpStr = $smsCount > 0 ? $smsCount . ',' . $smsTotal['success'] : 0;
            $this->redis->set(self::LAST_COUNT_SMS_KEY, $tmpStr); // 更新最后一次剩余次数
        }
        //echo $smsFlag;
        // 语言验证码 间隔10分钟跑一次
        if ($minute % 10 == 0  )
        {
            $stime = $time - self::VOICE_TIME;
            $voiceTotalList = $YyMonitorModel->getVoiceRangTotal($stime);
            $voiceTotal = array('error' => 0, 'success' => 0);
            foreach ($voiceTotalList as $voiceTotalInfo)
            {
                if ($voiceTotalInfo['is_rang'] > 0)
                {
                    $voiceTotal['success'] += $voiceTotalInfo['c'];
                }
                else
                {
                    $voiceTotal['error'] += $voiceTotalInfo['c'];
                }
            }

            $voiceCount = $voiceCountCur = array_sum($voiceTotal);
            $lastCountVoice = $this->redis->get(self::LAST_COUNT_VOICE_KEY); // 获取最后一次剩余次数
            // 判断前一次次数是否存在，存在则累加计算
            if (strpos($lastCountVoice, ",") !== false)
            {
                $tmpVoice = explode(',', $lastCountVoice);
                $voiceCount += $tmpVoice[0];
                $voiceTotal['success'] += $tmpVoice[1];
            }
            $voicePer = round($voiceTotal['success'] / $voiceCount * 100);
            $voiceFlag = $voicePer . " = round(" . $voiceTotal['success'] . " / " . $voiceCount .
                " * 100)";
                
            if ($voiceCount > 20)
            {
                if ($voicePer < 80)
                {
                    $date = " [" . date("Y-m-d H:i:s", $time) . "] ";
                    $msg = $date . "语音验证码线路有问题，接通率低于80%，" . "当前接通率为：" . $voiceFlag . "% 。";
                    \SendNoticeAction::sendSmsNotice($msg, 6);
                    $voiceFlag = "error";
                }
                $this->redis->set(self::LAST_COUNT_VOICE_KEY, 0); // 更新最后一次剩余次数
            }
            else
            {
                $tmpStr = $voiceCount > 0 ? $voiceCount . ',' . $voiceTotal['success'] : 0;
                $this->redis->set(self::LAST_COUNT_VOICE_KEY, $tmpStr); // 更新最后一次剩余次数
            }
            
            // 时间段比较
            $qtime = strtotime('-1 days'); // 前一天的当前时间
            $qstime = $qtime - self::SMS_TIME; // 前一天当前时间的前 self::SMS_TIME 时间
            $voiceDateInfo = $YyMonitorModel->getVoiceDateTotal($qstime, $qtime);
            $qcount = $voiceDateInfo['c']; // 前一天当前时间段总数
            $vs = $voiceCountCur . ' : ' . ($qcount * 2);
            if ($voiceCountCur > 100 && $voiceCountCur > ($qcount * 2))
            {
                $vs = $voiceCountCur . ' 大于 ' . ($qcount * 2);
                //$this->sendSms($vs, 2);
                $date = " [" . date("Y-m-d H:i:s", $time) . "] ";
                $msg = $date . "请求率过高，当前时间段大于前一天时间段，" . "当前比例为：" . $vs . " 。";
                \SendNoticeAction::sendSmsNotice($msg, 6);
            }
        }

    }
}

?>