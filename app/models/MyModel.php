<?php
use Octopus\PdoEx;
/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:MyModel.php
 * 摘    要:
 * 作    者:<EMAIL>
 * 修改日期: 2015/12/2
 */
class MyModel extends Model
{

    private $pdo;

    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_MY_2345, $dbConfig[DB_MY_2345]);
    }

    /**
     *
     * @param $uid
     * @return mixed
     * <AUTHOR>
     */
    public function favset($uid)
    {
        if ($uid > 0)
        {
            $mysetSql = "select * from fav_myset WHERE uid = :uid";
            $mysetRrs = $this->pdo->find($mysetSql, array(":uid" => $uid));
            return $mysetRrs;
        }
    }
}