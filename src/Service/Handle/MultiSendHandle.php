<?php
/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：MultiSendHandle.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：31/08/2018 15:19
 */
namespace Service\Handle;

use Service\Func\RequestFunc;
use Service\Lib\Model\ConfigModel;
use Service\Lib\Provider;
use Service\Swoole\Tool\Format;
use Service\Func\SmsFunc;

class MultiSendHandle
{
    protected $provider;

    /**
     * User: panj
     * @return Provider
     */
    protected function getProvider()
    {
        if (is_null($this->provider))
        {
            $this->provider = new Provider();
        }

        return $this->provider;
    }
    /**
     * 服务商推送消息
     * -
     * @param array $data 推送数据
     * @return array
     */
    public function run($data)
    {
        //前置处理已经过滤过非法的手机号和重复的手机号，发送操作这里只处理multiContent
        $smsType = $data['smsType'];
        $multiContent = $data['multiContent'];
        $illegalPhoneList = $data['illegalPhoneList'];
        $mid = !empty($data['mid']) ? $data['mid'] : '';
        $appType = !empty($data['appType']) ? $data['appType'] : '';
        $positionId = $data['positionId'];
        $configModel = new ConfigModel();
        //获取类型配置
        $smsConfigs = $configModel->getSmsTypeConfig($smsType);
        $smsConfig = SmsFunc::randomTypeConfig($smsConfigs);
        //通道关闭检测
        $check = SmsFunc::checkChannelStatus($smsConfig);
        if ($check[0] < 0)
        {
            return Format::returnArray($check[0], $check[1]);
        }

        if (isset($smsConfig['mobile_timeQuantum']) && !SmsFunc::checkTimeQuantum($smsConfig['mobile_timeQuantum']))
        {
            return [RequestFunc::TIME_NOT_ALLOWED_FOR_CHANNEL, RequestFunc::$errText[RequestFunc::TIME_NOT_ALLOWED_FOR_CHANNEL]];
        }
        // 通道类名
        $class = $smsConfig['mobile_action_name'];
        if (empty($class) || !class_exists($class))
        {
            return [RequestFunc::WRONG_CHANNEL_CONFIG_CLASS, RequestFunc::$errText[RequestFunc::WRONG_CHANNEL_CONFIG_CLASS]];
        }

        $sign = $smsConfig['mobile_sign_type'] == 0 ? $smsConfig['mobile_sign'] : "";
        // 群发使用相同的 移动的通道
        $channel = new $class(
            $smsConfig['mobile_username'], $smsConfig['mobile_password'], $smsConfig['mobile_api_url']
        );
        $channelId = $smsConfig['mobile_id'];
        if (!method_exists($channel, 'batchSend'))
        {
            return [RequestFunc::MULTI_SEND_SMSTYPE_MULTI_CONTENT_NOT_SUPPORT, RequestFunc::$multiErrStr[RequestFunc::MULTI_SEND_SMSTYPE_MULTI_CONTENT_NOT_SUPPORT]];
        }

        // 发送
        $ret = $channel->batchSend($multiContent, $sign);
        if (!empty($illegalPhoneList))
        {
            $ret = $ret == 1 ? 2 : $ret;
            $sendResponse = $ret ? RequestFunc::$multiErrStr[2] : RequestFunc::$errText[0];
        }
        else
        {
            if ($ret == 2)
            {
                $sendResponse = $ret ? RequestFunc::$multiErrStr[2] : RequestFunc::$errText[0];
            }
            else
            {
                $sendResponse = $ret ? RequestFunc::$errText[1] : RequestFunc::$errText[0];
            }
        }

        $failResponse = $data['failResponse'];
        $successResponse = $data['successResponse'];
        foreach ($multiContent as $packageId => $value)
        {
            list($phone, $msg, $logId) = $value;
            $msgId = $channel->getMultiReturnMsgId($phone);
            $msgStatusArr = $channel->getMultiReturnMsg($phone);
            $smsCount = SmsFunc::smsCount($msg . $smsConfig['mobile_sign']);
            $returnMsg = $sendResponse;
            if (isset($msgStatusArr['codeDesc']))
            {
                $returnMsg = $msgStatusArr['codeDesc'];
            }
            if (($ret == 1 || $ret == 2) && $msgId)
            {
                $successResponse[$packageId] = array("logId" => (string)$logId, "phone" => $phone, "msg" => $returnMsg);
            }
            else
            {
                $failResponse[$packageId] = array("logId" => (string)$logId, "phone" => $phone, "msg" => $returnMsg);
            }
            $insert = array(
                "channel"      => $channelId,
                "account"      => $channel->user ?? '',
                "sendStatus"   => $ret,
                "sendResponse" => $sendResponse,
                'businessId'   => $positionId,
                'msgId'        => $msgId,
                'codeTime'     => date('Y-m-d H:i:s'),
                'codeStatus'   => $msgStatusArr['codeStatus'],
                'codeDesc'     => $msgStatusArr['codeDesc'],
                'smsCount'     => $smsCount,
                'mid'          => $mid,
                'appType'      => $appType,
                'logId'        => $logId,
            );
            $isTrue = $this->getProvider()->addSmsLog($insert);
            $configModel->setexMsgLogAndLogId($phone, 1728000, $msgId, $logId);

            if ($isTrue)
            {
                //发送短信后 设置该手机该短信类型发送数量加1
                if ($ret)
                {
                    $configModel->setSmsNumByPhoneType($phone, $smsType, date("Y-m-d"));
                }
            }
        }

        $response = [
            'success' => $successResponse,
            'fail' => $failResponse,
        ];
        if (empty($illegalPhoneList))
        {
            if ($ret == 2)
            {
                $sendResponse = $ret ? RequestFunc::$multiErrStr[2] : RequestFunc::$errText[0];
            }
            else
            {
                $sendResponse = $ret ? RequestFunc::$errText[1] : RequestFunc::$errText[0];
            }

            return Format::returnArray($ret, $sendResponse, $response);
        }
        else
        {
            $msg = $ret ? RequestFunc::$multiErrStr[2] : RequestFunc::$errText[0];
            return Format::returnArray($ret, $msg, $response);
        }
    }
}
