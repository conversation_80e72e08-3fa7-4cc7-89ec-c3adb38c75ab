<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：SmsTypeController.php
 * 摘    要：sms短信类型管理
 * 作    者：张超
 * 修改日期：2016.01.18
 * */

namespace Admin;

use Controller;

class SmsTypeController extends Controller
{
    /**
     * @return void
     */
    public function actionIndex()
    {
        $pageArray = array();
        $orderBy = $this->Filter->getPost('orderBy');
        $orderByType = $this->Filter->getPost('type');
        if (!in_array(strtolower($orderBy), ['id', 'display_order']))
        {
            $orderBy = 'display_order';
        }

        if (!in_array(strtolower($orderByType), ['asc', 'desc']))
        {
            $orderByType = 'desc';
        }

        $SmsTypeModel = SmsTypeModel::getInstance();
        $OperatorConfigModel = OperatorConfigModel::getInstance();
        $smsTypeList = $SmsTypeModel->getSmsTypeList([], $orderBy, $orderByType);
        $smsTypeChildList = $SmsTypeModel->getSmsTypeList(array('child' => 'only'));


        $OperatorList = $OperatorConfigModel->getOperatorList();

        $operatorArr = array();
        foreach ($OperatorList as $OperatorKey => $OperatorInfo)
        {
            $operatorArr[$OperatorInfo['id']] = $OperatorInfo;
            if ($OperatorInfo['status'] == 1)
            {
                $pageArray['OperatorList'][] = $OperatorInfo;
            }
        }

        foreach ($smsTypeList as $smsTypeKey => $smsTypeInfo)
        {
            $smsTypeList[$smsTypeKey]['mobileProvider'] = $operatorArr[$smsTypeInfo['mobile_provider_id']]['provider_name'];
            $smsTypeList[$smsTypeKey]['telecomProvider'] = $operatorArr[$smsTypeInfo['telecom_provider_id']]['provider_name'];
            $smsTypeList[$smsTypeKey]['unicomProvider'] = $operatorArr[$smsTypeInfo['unicom_provider_id']]['provider_name'];
        }
        foreach ($smsTypeChildList as $smsTypeKey => $smsTypeInfo)
        {
            $smsTypeChildList[$smsTypeKey]['mobileProvider'] = $operatorArr[$smsTypeInfo['mobile_provider_id']]['provider_name'];
            $smsTypeChildList[$smsTypeKey]['telecomProvider'] = $operatorArr[$smsTypeInfo['telecom_provider_id']]['provider_name'];
            $smsTypeChildList[$smsTypeKey]['unicomProvider'] = $operatorArr[$smsTypeInfo['unicom_provider_id']]['provider_name'];
        }
        foreach ($smsTypeList as $smsType)
        {
            $smsTypeList2[$smsType['id']] = $smsType;
        }
        foreach ($smsTypeChildList as $smsTypeChild)
        {
            $smsTypeList2[$smsTypeChild['sms_type']]['child'][] = $smsTypeChild;
        }
        $pageArray['smsTypeList'] = $smsTypeList2;
        $pageArray['orderBy'] = $orderBy;
        $pageArray['orderByType'] = $orderByType;

        $pageArray['OperatorList'] = $OperatorConfigModel->getOperatorList(array('status' => 1));
        loadView('Admin/sms_type_index.tpl.html', $pageArray);
    }


    /**
     * @return null
     */
    public function actionGetSmsTypeInfo()
    {
        $type = (int)$this->Filter->getPost('type');
        if (empty($type))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        $smsTypeModel = SmsTypeModel::getInstance();
        $smsTypeInfo = $smsTypeModel->getSmsTypeList(array('type' => $type, 'child' => 'show'));

        return \MsgAction::returnJsonData(array('status' => 'P00001', 'data' => $smsTypeInfo));
    }


    /**
     * @return mixed
     */
    public function actionSetSmsTypeConfig()
    {
        $act = $this->Filter->getPost('act');
        $data = $this->Filter->getPost('data');
        if (empty($act))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        else
        {
            if ($act == 'edit')
            {
                $typeId = $this->Filter->getPost('typeId');
                if (empty($typeId))
                {
                    return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
                }
            }
            else
            {
                $typeId = 0;
            }
        }
        if ($act == 'add' && sizeof($data) != 1)
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '新建时只允许添加主类型'));
        }
        //字段完整性校验，百分比校验
        $totalPercent = 0;
        foreach ($data as $dataId => $dataLine)
        {
            $totalPercent += (float)$dataLine['percent'];
            $data[$dataId]['is_restrict'] = $dataLine['restrict_nums'] == 0 ? 0 : 1;
            $data[$dataId]['sms_name'] = \EncodingAction::transcoding($dataLine['sms_name']);
            if (!isset($dataLine['sms_name']) || !is_numeric($dataLine['mobile_provider_id']) ||
                !is_numeric($dataLine['telecom_provider_id']) || !is_numeric($dataLine['unicom_provider_id']) ||
                !is_numeric($dataLine['restrict_nums'])
            )
            {
                return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '参数不正确'));
            }
            $monitor_config = json_decode($dataLine['monitor_config'], true);
            if (!isset($monitor_config['stepTime'], $monitor_config['offsetTime'], $monitor_config['commitLimitRate'], $monitor_config['receiveLimitRate']))
            {
                return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '预警信息必须是合法的json'));
            }
        }
        if ($totalPercent != 100)
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '分流百分比之和不为100'));
        }
        //数据模型
        $smsTypeModel = SmsTypeModel::getInstance();
        $isTrue = 1;
        //删除操作
        if ($act === 'edit')
        {
            $smsTypeInfo = $smsTypeModel->getSmsTypeList(array('type' => $typeId, 'child' => true));
            foreach ($smsTypeInfo as $smsTypeItem)
            {
                if (!isset($data[$smsTypeItem['id']]))
                {
                    $isTrue &= $smsTypeModel->delSmsType($smsTypeItem['id']);
                }
            }
        }
        foreach ($data as $dataId => $dataLine)
        {
            if ($dataLine['id'] == 0)
            {
                $dataLine['sms_type'] = $typeId;
                $isTrue &= $smsTypeModel->addSmsType($dataLine);
            }
            else
            {
                $isTrue &= $smsTypeModel->editSmsType($dataLine);
                $redisKey = \KeyAction::getSmsTypeConfigKey($typeId);
                $redis = \RedisAction::connect();
                $redis->del($redisKey);
            }
        }
        if ($isTrue)
        {
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '设置成功'));
        }
        else
        {
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '设置失败'));
        }
    }

    /**
     * User: panj
     * @return null
     */
    public function actionChangeDisplayOrder()
    {
        $displayOrder = $this->Filter->getPost('displayOrder');
        if (empty($displayOrder))
        {
            return \MsgAction::returnJsonData(
                [
                    'status' => 'P00002',
                    'msg'    => '缺少参数',
                ]
            );
        }
        parse_str($displayOrder, $orderArray);
        if (!isset($orderArray['displayOrder']) || empty($orderArray['displayOrder']))
        {
            return \MsgAction::returnJsonData(
                [
                    'status' => 'P00002',
                    'msg'    => '缺少参数',
                ]
            );
        }
        $orderArray = $orderArray['displayOrder'];
        $smsTypeModel = SmsTypeModel::getInstance();
        $isTrue = $smsTypeModel->changeDisplayOrder($orderArray);
        $response = $isTrue ? [
            'status' => 'P00001',
            'msg'    => '设置成功',
        ] : [
            'status' => 'P00001',
            'msg'    => '设置失败',
        ];

        return \MsgAction::returnJsonData($response);
    }
}
