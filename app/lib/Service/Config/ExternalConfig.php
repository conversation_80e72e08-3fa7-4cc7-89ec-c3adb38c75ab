<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/11
 * Time: 9:52
 */
namespace Service\Config;

class externalConfig
{
    public static $expired = 1728000;  //默认有效时间
    public static $validTokenNum = 10;  //默认的token队列长度

    /**
     * 获取配置信息
     * -
     * @param string $exId 唯一标识
     * @return array
     */
    public static function get($exId)
    {
        list($prefix, $id) = explode("_", $exId);
        $prefix = ucfirst($prefix);
        $id = ucfirst($id);
        $namespace = '\Service\\Config\\Conf\\' . $prefix . $id;
        if (class_exists($namespace, 'get'))
        {
            return $namespace::get();
        }
        else
        {
            return [];
        }
    }
}
