<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/3/20
 * Time: 11:03
 */

class AvatarZoomAction extends Action
{
    //临时文件目录
    private static $savePath = '/tmp';

    /**
     * 获取图片现象
     * @param string $avatarFileTmpName 图片路径
     * @return array|false
     */
    public static function getImgInfo($avatarFileTmpName)
    {
        $imgInfo = getimageSize($avatarFileTmpName);//获取图片的基本信息

        return [
            $imgInfo[0],
            $imgInfo[1],
            $imgInfo[2],
        ];
    }


    /**
     * 根据前端缩放图片大小进行截取图片
     * @param string $picName 图片路径
     * @param int    $maxX    缩放最大宽度
     * @param int    $maxY    缩放最大高度
     * @return bool|string
     */
    public function getImg($picName, $maxX = 300, $maxY = 300)
    {
        list($w, $h,) = self::getImgInfo($picName);
        $zoomMaxX = $maxX;
        $zoomMaxY = $maxY;
        $postionX = $postionY = 0;
        if ($h > $w) {
            $scale = 300 / $h;
            $zoomMaxX = 300 - ((300 - $w * $scale) / 2) * 2;
        } else {
            $scale = 300 / $w;
            $zoomMaxY = 300 - ((300 - $h * $scale) / 2) * 2;
        }

        $zoomImgFile = $this->scaleImg($picName, $zoomMaxX, $zoomMaxY);
        if ($zoomImgFile !== false) {
            $zoomAvatarInfo = self::getImgInfo($zoomImgFile);//获取图片的基本信息
            if ($h > $w) {
                $postionX = (300 - $zoomAvatarInfo[0]) / 2;
            } else {
                $postionY = (300 - $zoomAvatarInfo[1]) / 2;
            }

            return $this->synthesisZoomImg($zoomImgFile, $postionX, $postionY, $maxX, $maxY);
        } else {
            return false;
        }
    }


    /**
     * 根据大小等比例缩放图片
     * @param string $picName  图片路径
     * @param int    $maxx     缩放最大宽度
     * @param int    $maxy     缩放最大高度
     * @return bool|string
     */
    public function scaleImg($picName, $maxx = 300, $maxy = 300)
    {
        list($w, $h, $imgType) = self::getImgInfo($picName);
        //获取图片的类型并为此创建对应图片资源
        switch ($imgType) {
            case 1://gif
                $im = imagecreatefromgif($picName);
                break;
            case 2://jpg
                $im = imagecreatefromjpeg($picName);
                break;
            case 3://png
                $im = imagecreatefrompng($picName);
                break;
            default:
                return false;
        }
        //计算缩放比例
        if (($maxx / $w) > ($maxy / $h)) {
            $b = $maxy / $h;
        } else {
            $b = $maxx / $w;
        }
        //计算出缩放后的尺寸
        $nw = floor($w * $b);
        $nh = floor($h * $b);
        //创建一个新的图像源（目标图像）
        $nim = imagecreatetruecolor($nw, $nh);
        //透明背景变黑处理
        //2.上色
        $color = imagecolorallocate($nim, 255, 255, 255);
        //3.设置透明
        imagecolortransparent($nim, $color);
        imagefill($nim, 0, 0, $color);
        //执行等比缩放
        imagecopyresampled($nim, $im, 0, 0, 0, 0, $nw, $nh, $w, $h);
        //输出图像（根据源图像的类型，输出为对应的类型）
        $picInfo = pathinfo($picName);//解析源图像的名字和路径信息
        $savePath = self::$savePath . "/" . $picInfo["basename"];
        switch ($imgType) {
            case 1:
                imagegif($nim, $savePath);
                break;
            case 2:
                imagejpeg($nim, $savePath);
                break;
            case 3:
                imagepng($nim, $savePath);
                break;
        }
        //释放图片资源
        imagedestroy($im);
        imagedestroy($nim);

        //返回结果
        return $savePath;
    }


    /**
     * 组合缩放图片
     * @param string $picName  图片路径
     * @param int    $postionX 合成X坐标
     * @param int    $postionY 合成Y坐标
     * @param int    $maxx     缩放最大宽度
     * @param int    $maxy     缩放最大高度
     * @return bool|string
     */
    public function synthesisZoomImg($picName, $postionX, $postionY, $maxx = 300, $maxy = 300)
    {
        list($w, $h, $imgType) = self::getImgInfo($picName);
        $im = imagecreatefromstring(file_get_contents($picName));
        //创建一个新的图像源（目标图像）
        $nim = imagecreatetruecolor($maxx, $maxy);
        $color = imagecolorallocate($nim, 255, 255, 255);
        imagecolortransparent($nim, $color);
        imagefill($nim, 0, 0, $color);
        imagecopymerge($nim, $im, $postionX, $postionY, 0, 0, $w, $h, 100);
        $savePath = $this->saveImgInfo($picName, $imgType, $nim);
        if ($savePath === false) {
            return false;
        }
        //释放图片资源
        imagedestroy($im);
        imagedestroy($nim);

        return $savePath;
    }

    /**
     * 保存图片信息
     * @param string   $picName 图片路径
     * @param string   $imgType 图片类型
     * @param resource $nim     图片保存资源
     * @return bool|string
     */
    private function saveImgInfo($picName, $imgType, $nim)
    {
        $picInfo = pathinfo($picName);
        $savePath = self::$savePath . "/" . $picInfo["basename"];
        $isSave = true;
        switch ($imgType) {
            case 1:
                $isSave = imagegif($nim, $savePath);
                break;
            case 2:
                $isSave = imagejpeg($nim, $savePath);
                break;
            case 3:
                $isSave = imagepng($nim, $savePath);
                break;
            default:
                $isSave = false;
        }
        if ($isSave) {
            return $savePath;
        } else {
            return false;
        }
    }
}
