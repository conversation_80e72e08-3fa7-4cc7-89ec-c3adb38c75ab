<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：class.zhengaoSmsChannel.php
 * 摘    要：
 * 作    者：陆祖恒
 * 修改日期：2014-12-2
 */

namespace Smschannel;
use Action;
use FuncAction;

class ZhengaoSmsChannel3Action extends SmsChannelAction
{
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }

    /**
     * 发送短信，适用
     * @param string $phone 手机号
     * @param string $msg   短信内容
     * @return RET_SUCC 发送成功    RET_ERR 发送失败
     */
    public function send($phone, $msg)
    {
        $msg_u8 = iconv('GBK', 'UTF-8', $msg);
        
        $params = array(
            'userid' => '491',
            'account' => $this->user,
            'password' => $this->pwd,
            'content' => $msg_u8,
            'mobile' => $phone,
            'sendTime' => '',
            'extno' => '');
        $xml = FuncAction::postTimeout($this->apiUrl, $params);
        $res = simplexml_load_string($xml);
        $this->SetMsgId( $res->taskID);
        $this->SetMsgArray( $res);
        //由于可能存在curl超时的情况，没有返回值，但是发送成功了， 所以这儿判断失败
        if ($res->returnstatus == "Faild")
        {
            return self::RET_ERR;
        }
        else
        {
            return self::RET_SUCC;
        }
    }
    
     private function SetMsgId ($msgId)
    {
        $this->msgId = $msgId;
    }
    
    private function SetMsgArray($res)
    {
        $this->OperatorMsg = array('codeStatus' => $res->returnstatus, 'codeDesc' => \EncodingAction::transcoding($res->message) );
    }

}

?>