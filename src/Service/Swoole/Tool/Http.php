<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/24
 * Time: 10:01
 */
namespace Service\Swoole\Tool;

class Http
{
    /**
     * @param string $url     url
     * @param array  $post    postArr
     * @param int    $timeout timeout
     * @param array $httpHeader 头部信息
     * @return bool|string
     */
    public static function post(string $url, $post = array(), $timeout = 5, $httpHeader = [])
    {
        $ci = curl_init();
        curl_setopt($ci, CURLOPT_POST, true);
        curl_setopt($ci, CURLOPT_POSTFIELDS, $post);
        curl_setopt($ci, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ci, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($ci, CURLOPT_URL, $url);
        curl_setopt($ci, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ci, CURLOPT_SSL_VERIFYPEER, false);
        if (!empty($httpHeader))
        {
            curl_setopt($ci, CURLOPT_HTTPHEADER, $httpHeader);
        }
        $response = curl_exec($ci);

        $httpCode = curl_getinfo($ci, CURLINFO_HTTP_CODE);
        if ($httpCode == 200 && !empty($response))
        {
            $res = $response;
        }
        else
        {
            $res = false;
        }

        return $res;
    }


    /**
     * @param string $url     url
     * @param array  $post    postArr
     * @param int    $timeout timeout
     *
     * @return bool|string
     */
    public static function postJson(string $url, $post = [], $timeout = 5)
    {
        $ci = curl_init();
        $raw = json_encode($post);
        curl_setopt($ci, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ci, CURLOPT_POSTFIELDS, $raw);
        curl_setopt($ci, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ci, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($ci, CURLOPT_URL, $url);
        curl_setopt($ci, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ci, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ci, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($raw),
        ]);

        $response = curl_exec($ci);

        $httpCode = curl_getinfo($ci, CURLINFO_HTTP_CODE);
        if ($httpCode == 200 && !empty($response))
        {
            $res = $response;
        }
        else
        {
            $res = false;
        }

        return $res;
    }
}
