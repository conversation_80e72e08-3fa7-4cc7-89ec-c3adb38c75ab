<?php
/**
 * 每月统计上个月的计费短信数据量
 */

namespace Crontab;

use Controller;

class TempController extends Controller
{
    /**
     * TempController constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 发送2022年司庆短信
     * @return void
     */
    public function actionSendSqSms()
    {
        exit(0);
        $ydPhoneList = [
            '13122119626'
        ];
        foreach ($ydPhoneList as $key => $val) {
            self::sendYdSms($val);
        }
        $dxPhoneList = [
            '13122119626'
        ];
        foreach ($dxPhoneList as $key => $val) {
            self::sendDxSms($val);
        }
    }

    // 移动联通
    public function sendYdSms($phone)
    {
        $msg = '【岩山科技】 亲爱的岩山科技老战友：

中秋国庆双节将至，衷心祝福你节日快乐！合家团圆！

向你汇报下公司近期动态，二三四五已更名为岩山科技，在原有业务持续稳健运营的同时，公司已大力投入进军人工智能领域。

感恩你曾经为岩山科技（原二三四五）付出的努力、智慧和汗水！真诚祝福你在新的征程中一切顺利，大放异彩！

有空的时候，想会会老同事、老朋友的时候，欢迎随时回来看看，也欢迎关注岩山科技新的事业机会。

为搭建老战友与公司之间沟通交流的桥梁，方便大家及时了解公司动态和日常交流连接，邀请你点击链接加岩山科技HR为好友，领取拼手气红包哟~
https://postimg.cc/pmHVNSWz

如不想再收到此类信息，请回复T退订';
        self::sendSqSms($phone, $msg);
    }

    //电信用户
    public function sendDxSms($phone)
    {
        $msg = '【岩山科技】 亲爱的岩山科技老战友：

中秋国庆双节将至，衷心祝福你节日快乐！合家团圆！

向你汇报下公司近期动态，二三四五已更名为岩山科技，在原有业务持续稳健运营的同时，公司已大力投入进军人工智能领域。

感恩你曾经为岩山科技（原二三四五）付出的努力、智慧和汗水！真诚祝福你在新的征程中一切顺利，大放异彩！

有空的时候，想会会老同事、老朋友的时候，欢迎随时回来看看，也欢迎关注岩山科技新的事业机会。';
        self::sendSqSms($phone, $msg);
    }

    /**
     * @param $phone
     */
    public function sendSqSms($phone, $msg)
    {
        $url = 'http://smsp.2345.net/Api/Sms/Send';

        $postArray = [
            'phone' => $phone,
            'pid' => 142,
            'smsType' => 290,
            'positionId' => 752,
            'clientIp' => '127.0.0.1',
            'appType' => 1,
            'msg' => $msg
        ];
        $result = self::post($url, $postArray);
        $jsonDecode = json_decode($result, true);
        if ($jsonDecode['status'] === '1') {
            echo "发送成功：$phone\n";
        } else {
            echo "发送失败：$phone\n";
        }
    }

    /**
     * @param $url
     * @param $postData
     * @return bool|string
     */
    public function post($url, $postData)
    {
        $o = '';
        foreach ($postData as $k => $v) {
            $o .= "$k=" . urlencode($v) . '&';
        }
        $post_data = substr($o, 0, -1);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
}