<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" >
<head>
<title>loginProxy</title>
<script src="/js/jquery-1.8.3.min.js"></script>
<script src="/js/client/base.js"></script>
<script type="text/javascript" src="/js/client/geaes.js"></script>
<script type="text/javascript">
try {
    var proxyHash = location.hash ? location.hash.substring(1) : '';
} catch(e) {};
var code = new Base64 ();
if (window.applicationCache)
{
    var parentwin = window.parent;
    window.addEventListener("message", function(e){
        $.post('/webapi/Login', {data:AES.encrypt(e.data)}, function (data){
            parentwin.postMessage(data, code.decode(proxyHash)  );
        });
    },false);
}
else
{
    $.post('/webapi/Login', {data:AES.encrypt(window.name)}, function (data){
        window.name = data;
        window.location = code.decode(proxyHash) +  '#callbackLocation';
    });
}
</script>
</head>
<body>
</body>
</html>