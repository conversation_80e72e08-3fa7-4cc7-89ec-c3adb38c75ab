<?php

namespace Admin;
use Controller;
use Service\Swoole\Tool\Encoding;

class SmsProjectController extends Controller
{

    public function actionIndex()
    {
        $pageArray = array();

        $SmsTypeModel = SmsTypeModel::getInstance();
        $pageArray['smsTypeList'] = $SmsTypeModel->getSmsTypeList();
        $SmsProjectModel = SmsProjectModel::getInstance();

        $objOAProductAction = \OAProductAction::getInstance();
        $pageArray['allProjectProduct'] = $objOAProductAction->getAllProjectProduct();

        $smsTypeListInfo = $SmsTypeModel->getSmsTypeList();
        $smsTypeArr = array();
        foreach ($smsTypeListInfo as $typeListInfo)
        {
            $smsTypeArr[$typeListInfo['sms_type']] = $typeListInfo;
        }

        $PageAction = \PageAction::getInstance();
        $pageArray['projectName'] = $this->Filter->getPost('projectName');
        $projectName = !empty($pageArray['projectName']) ? \EncodingAction::transcoding($pageArray['projectName']) : '';
        $queryData = array('projectName' => $projectName);

        $projectList = $SmsProjectModel->getPojectList($queryData, $PageAction->returnPageConfig(30));
        foreach ($projectList as $projectKey => $projectInfo)
        {
            $intOAProductId = $projectInfo['oa_product_id'] ?? 0;
            $arrOAProject = $objOAProductAction->getProjectByProduct($intOAProductId);
            $projectList[$projectKey]['oa_project_id'] = $arrOAProject['project_id'] ?? 0;
            $projectList[$projectKey]['oa_project_name'] = $arrOAProject['project_name'] ?? '未知项目';
            $projectList[$projectKey]['oa_product_name'] = $arrOAProject['product_list'][$intOAProductId]['product_name'] ?? '未知产品';

            $positionList = $SmsProjectModel->getPojectPositionList(array('pid' => $projectInfo['pid']));
            foreach ($positionList as $positionInfo)
            {
                $projectList[$projectKey]['positionName'][] = $positionInfo['pl_id'] . '-' . $positionInfo['position_name'];
            }

            $smsTypeList = $SmsProjectModel->getPojectSmsTypeList(array('pid' => $projectInfo['pid']));
            foreach ($smsTypeList as $smsTypeInfo)
            {
                $projectList[$projectKey]['smsTypeName'][] = $smsTypeInfo['tid'] . '-' . $smsTypeArr[$smsTypeInfo['tid']]['sms_name'];
            }
            if (!empty($projectList[$projectKey]['positionName']))
            {
                $projectList[$projectKey]['positionName'] = implode(',', $projectList[$projectKey]['positionName']);
            }
            else
            {
                $projectList[$projectKey]['positionName'] = '';
            }
            if (!empty($projectList[$projectKey]['smsTypeName']))
            {
                $projectList[$projectKey]['smsTypeName'] = implode(',', $projectList[$projectKey]['smsTypeName']);
            }
            else
            {
                $projectList[$projectKey]['smsTypeName'] = '';
            }
        }
        $pageArray['projectList'] = $projectList;
        $queryData['row'] = true;
        $queryData['count'] = true;
        $projecCount = $SmsProjectModel->getPojectList($queryData);
        $pageArray['showPage'] = $PageAction->showPage($projecCount['total'], '/Admin/SmsProject/Index');
        loadView('Admin/sms_project_index.tpl.html', $pageArray);
    }

    public function actionSetProjectInfo()
    {
        $projectArr = $this->Filter->getPost('projectArr');
        if (empty($projectArr['projectName']))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        $projectArr['letter'] = !empty($projectArr['letter']) ? strtoupper($projectArr['letter']) : '';
        $SmsProjectModel = SmsProjectModel::getInstance();
        $projectArr['createUid'] = UserAction::getAdminUid();
        $isTrue = $SmsProjectModel->setProjectInfo($projectArr);
        if ($isTrue)
        {
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '添加成功'));
        }
        else
        {
            return \MsgAction::returnJsonData(array('status' => 'P00007', 'msg' => '添加失败'));
        }
    }

    public function actionGetProjectInfo()
    {
        $projectId = $this->Filter->getPost('projectId');
        if (empty($projectId))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        $projectArr = array();
        $SmsProjectModel = SmsProjectModel::getInstance();
        $projectInfo = $SmsProjectModel->getPojectList(array('pid' => $projectId, 'row' => true));
        if (empty($projectInfo))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00008', 'msg' => '不存在的项目'));
        }
        $projectArr['projectName'] = $projectInfo['project_name'];
        $projectArr['letter'] = $projectInfo['letter'];

        $intOAProductId = $projectInfo['oa_product_id'] ?? 0;
        $arrOAProject = \OAProductAction::getInstance()->getProjectByProduct($intOAProductId);
        $projectArr['oa_project_id'] = $arrOAProject['project_id'] ?? 0;
        $projectArr['oa_product_id'] = $intOAProductId;

        $positionList = $SmsProjectModel->getPojectPositionList(array('pid' => $projectId));
        $smsTypeList = $SmsProjectModel->getPojectSmsTypeList(array('pid' => $projectId));

        $str = '';
        foreach ($positionList as $positionInfo)
        {
            $str .= '<li><input type="text" name="projectArr[positionName][]" plid="' . $positionInfo['pl_id'] .
                '" value="' . $positionInfo['position_name'] .
                '" autocomplete="off" class="tabInput">
                <div class="btnPosition">
                    <a href="javascript:" class="btn-Add">增加</a>
                    <a href="javascript:" class="btn-Delet">删除</a>
                </div></li>';
        }
        if (empty($str))
        {
            $str = '<li><input type="text" name="projectArr[positionName][]" autocomplete="off" class="tabInput">
                <div class="btnPosition">
                    <a href="javascript:" class="btn-Add">增加</a>
                    <a href="javascript:" class="btn-Delet">删除</a>
                </div></li>';
        }
        $projectArr['positionList'] = $str;
        $projectArr['smsTypeList'] = $smsTypeList;
        return \MsgAction::returnJsonData(array('status' => 'P00001', 'data' => $projectArr));
    }

    public function actionGetProjectPostion()
    {
        $projectId = $this->Filter->getPost('projectId');
        if (empty($projectId))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        $SmsProjectModel = SmsProjectModel::getInstance();
        $projectInfo = $SmsProjectModel->getPojectList(array('pid' => $projectId, 'row' => true));
        if (empty($projectInfo))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00008', 'msg' => '不存在的项目'));
        }
        $positionList = $SmsProjectModel->getPojectPositionList(array('pid' => $projectId));
        $html = '<dd><a href="javascript:" value="0">全部</a></dd>';
        foreach ($positionList as $positionInfo)
        {
            $html .= '<dd><a href="javascript:void(0)" value="' . $positionInfo['pl_id'] . '">' . $positionInfo['pl_id'] . '-' . $positionInfo['position_name'] . '</a></dd>';
        }
        return \MsgAction::returnJsonData(array('status' => 'P00001', 'data' => $html));
    }

    public function actionProjectListInfo()
    {
        $pageArray = array();
        //是否可以使用缓存
        $useCache = $this->Filter->getPost('useCache');
        $fromClick = $this->Filter->getPost('fromClick');
        if ($fromClick)
        {
            $pageArray['useCache'] = $useCache;
        }
        else
        {
            $pageArray['useCache'] = 1;
        }

        $SmsTypeModel = SmsTypeModel::getInstance();
        if ($useCache)
        {
            $redis = \RedisAction::connect();
            if ($redis->exists('smsTypeList'))
            {
                $pageArray['smsTypeList'] = unserialize($redis->get('smsTypeList'));
            }
            else
            {
                $pageArray['smsTypeList'] = $SmsTypeModel->getSmsTypeList();
                $redis->setex('smsTypeList', 86400 * 2, serialize($pageArray['smsTypeList']));
            }
        }
        else
        {
            $pageArray['smsTypeList'] = $SmsTypeModel->getSmsTypeList();
            $redis = \RedisAction::connect();
            $redis->setex('smsTypeList', 86400 * 2, serialize($pageArray['smsTypeList']));
        }

        $SmsProjectModel = SmsProjectModel::getInstance();

        $smsTypeListInfo = $pageArray['smsTypeList'];
        $smsTypeArr = array();
        foreach ($smsTypeListInfo as $typeListInfo)
        {
            $smsTypeArr[$typeListInfo['sms_type']] = $typeListInfo;
        }
        $SmsTotalModel = SmsTotalModel::getInstance();
        $UserAuthModel = UserAuthModel::getInstance();
        $uid = UserAction::getAdminUid();

        $pageArray['startTime'] = $this->Filter->getPost('startTime');
        $pageArray['endTime'] = $this->Filter->getPost('endTime');
        $pageArray['projectId'] = $this->Filter->getPost('projectId');

        //如果没有时间  默认7天
        $pageArray['startTime'] = !empty($pageArray['startTime']) ? $pageArray['startTime'] : date(
            'Y-m-d',
            strtotime(date('Y-m-d') . '-7 day')
        );
        $pageArray['endTime'] = !empty($pageArray['endTime']) ? $pageArray['endTime'] : date('Y-m-d', strtotime('-1 day'));
        if (strtotime($pageArray['startTime']) > strtotime($pageArray['endTime']))
        {
            $pageArray['startTime'] = date('Y-m-d', strtotime(date('Y-m-d') . '-6 day'));
            $pageArray['endTime'] = date('Y-m-d');
        }
        if ($pageArray['endTime'] > date('Y-m-d'))
        {
            $pageArray['endTime'] = date('Y-m-d');
        }
        $act = $this->Filter->getPost('act');

        $startTime = strtotime($pageArray['startTime']);
        $endTime = strtotime($pageArray['endTime']);
        $dateArr = range($startTime, $endTime, 86400);

        if ($act == 'export')
        {
            $limitPage = array();
            AutoMakeCsvAction::init();
            $fieldArr = array('日期', '项目名称');
        }


        if ($useCache)
        {
            $redis = \RedisAction::connect();
            if ($redis->exists('projectList:'.$uid))
            {
                $projectList = unserialize($redis->get('projectList:'.$uid));
            }
            else
            {
                $projectList = $UserAuthModel->getUserProjectList($uid);
                $redis->setex('projectList:'.$uid, 86400 * 2, serialize($projectList));
            }
        }
        else
        {
            $projectList = $UserAuthModel->getUserProjectList($uid);
            $redis = \RedisAction::connect();
            $redis->setex('projectList:'.$uid, 86400 * 2, serialize($projectList));
        }


        $pageArray['projectList'] = $projectList;
        if (!empty($projectList))
        {
            //计算时间段内的短信量
            foreach ($dateArr as $dateKey => $dateInfo)
            {
                $nowDate = date('Y-m-d', $dateInfo);
                $everyListTotal[$nowDate] = $nowDate;
            }
            krsort($everyListTotal);
            $typeTotalList = array();
            $allTotal = 0;
            //新需求：默认选择【-请选择-】，列表为空
            if ($pageArray['projectId'] == 0)
            {
                $everyListTotal = array();
            }

            $queryData = array('row' => true);
            if (!empty($pageArray['projectId']))
            {
                $queryData['projectId'] = $pageArray['projectId'];
            }

            $middle = $UserAuthModel->getUserProjectList($uid, $queryData);
            $smsTypeListMiddle = $SmsProjectModel->getPojectSmsTypeList(array('pid' => $middle['pid']));
            $positionListMiddle = $SmsProjectModel->getPojectPositionList(array('pid' => $middle['pid']));
            foreach ($everyListTotal as $nowKey => $dateInfo)
            {
                $projectList = $middle;
                if (!empty($projectList))
                {
                    $smsTypeList = $smsTypeListMiddle;
                    foreach ($smsTypeList as $smsTypeInfo)
                    {
                        $projectList['smsTypeName'][] = $smsTypeInfo['tid'] . '-' . $smsTypeArr[$smsTypeInfo['tid']]['sms_name'];
                    }
                    $projectList['smsTypeName'] = implode(',', $projectList['smsTypeName']);


                    //现在配置的位置
                    $positionList = $positionListMiddle;
                    foreach ($positionList as $positionListInfo)
                    {
                        $pageArray['postionList'][$positionListInfo['pl_id']] = $positionListInfo['position_name'];

                        if (!isset($typeTotalList[$positionListInfo['pl_id']]))
                        {
                            $typeTotalList[$positionListInfo['pl_id']] = 0;
                        }
                    }
                    //计算后可能新增位置
                    $totolData = array(
                        'pid'       => $projectList['pid'],
                        'startTime' => $nowKey,
                        'endTime'   => date('Y-m-d', strtotime($nowKey . '+1 day')),
                    );
                    $smsProjectInfoTotalKey = "SMS:PROJECTINFO:TOTAL:" . $projectList['pid'] . ":" . str_replace("-", "", $nowKey);
                    $isCache = true;
                    if ($useCache)
                    {
                        if ($redis->exists($smsProjectInfoTotalKey))
                        {
                            $postionList = Encoding::transcoding(json_decode($redis->get($smsProjectInfoTotalKey), true), 'gbk');
                        }
                        else
                        {
                            $postionList = $SmsTotalModel->getProjectPostionTotal($totolData);
                            $redis->setex($smsProjectInfoTotalKey, 180 * 86400, json_encode(Encoding::transcoding($postionList, 'utf-8')));
                            $isCache = false;
                        }
                    }
                    else
                    {
                        $postionList = $SmsTotalModel->getProjectPostionTotal($totolData);
                        $redis->setex($smsProjectInfoTotalKey, 180 * 86400, json_encode(Encoding::transcoding($postionList, 'utf-8')));
                        $isCache = false;
                    }
                    $postionList = $postionList ?: [];
                    foreach ($postionList as $postionInfo)
                    {
                        $projectList['postionList'][$postionInfo['postion_id']] = $postionInfo;
                        if (!isset($pageArray['postionList'][$postionInfo['postion_id']]))
                        {
                            $pageArray['postionList'][$postionInfo['postion_id']] = $postionInfo['postion_id'];
                        }
                        if (!isset($projectList['useSmsTotal']))
                        {
                            $projectList['useSmsTotal'] = 0;
                        }
                        $projectList['useSmsTotal'] = isset($projectList['useSmsTotal']) ? $projectList['useSmsTotal'] : 0;
                        $projectList['useSmsTotal'] += $postionInfo['total'];
                        if (!isset($typeTotalList[$postionInfo['postion_id']]))
                        {
                            $typeTotalList[$postionInfo['postion_id']] = 0;
                        }
                        $typeTotalList[$postionInfo['postion_id']] += $postionInfo['total'];
                    }
                    $projectList['isCache'] = $isCache;

                    $everyListTotal[$nowKey] = $projectList;

                    $allTotal += isset($projectList['useSmsTotal']) ? $projectList['useSmsTotal'] : 0;
                }
            }
            $pageArray['postionList'] = $pageArray['postionList'] ?? [];
            if ($act == 'export')
            {
                foreach ($pageArray['postionList'] as $postionListKey => $postionListInfo)
                {
                    //print_r($postionListKey);exit;
                    $fieldArr[] = $postionListInfo;
                }
                $fieldArr[] = '统计';
                AutoMakeCsvAction::setField($fieldArr);
                foreach ($everyListTotal as $everyListKey => $everyListInfo)
                {
                    $writeArr[$everyListKey] = array('date' => $everyListKey, 'project_name' => $everyListInfo['project_name']);

                    foreach ($pageArray['postionList'] as $postionListKey => $postionListInfo)
                    {
                        if (!empty($everyListInfo['postionList'][$postionListKey]))
                        {
                            $writeArr[$everyListKey][$postionListKey] = $everyListInfo['postionList'][$postionListKey]['total'];
                            //$writeArr[$everyListKey]['useSmsTotal_'.$postionListKey] = $everyListInfo['useSmsTotal'];
                        }
                        else
                        {
                            $writeArr[$everyListKey][$postionListKey] = 0;
                        }
                    }
                    if (!empty($everyListInfo['useSmsTotal']))
                    {
                        $writeArr[$everyListKey]['useSmsTotal'] = $everyListInfo['useSmsTotal'];
                    }
                    else
                    {
                        $writeArr[$everyListKey]['useSmsTotal'] = 0;
                    }
                    if (!empty($writeArr[$everyListKey]))
                    {
                        AutoMakeCsvAction::writeData($writeArr[$everyListKey]);
                    }
                }
                $totalWrite = array('','');
                foreach ($pageArray['postionList'] as $postionListKey => $postionListInfo)
                {
                    $totalWrite[] = !empty($typeTotalList[$postionListKey]) ? $typeTotalList[$postionListKey] : 0;
                }
                $totalWrite[] = $allTotal;
                AutoMakeCsvAction::writeData($totalWrite);
                AutoMakeCsvAction::headerLocationCsv();
                exit;
            }
            $pageArray['typeTotalList'] = $typeTotalList;
            $pageArray['everyListTotal'] = $everyListTotal;
            $pageArray['allTotal'] = $allTotal;
        }
        loadView('Admin/project_list_info.tpl.html', $pageArray);
    }


    /**
     * 后台写入手机黑名名单KEY
     * */
    public function actionSmsSyncBlackPhoneKey()
    {
        $SmsSetKeyAction = \Crontab\SmsSetKeyAction::getInstance();
        $isSucc = $SmsSetKeyAction->SmsSyncBlackPhoneKey();
        if ($isSucc)
        {
            echo '更新成功';
            exit;
        }
        else
        {
            $errorList = $SmsSetKeyAction->getBlackError();
            print_r($errorList);
            exit;
        }

    }


    /**
     * 设置IP白名单
     * */
    public function actionSmsSyncIpWriteList()
    {
        $SmsSetKeyAction = \Crontab\SmsSetKeyAction::getInstance();
        $isSucc = $SmsSetKeyAction->SmsSyncIpWriteList();
        if ($isSucc)
        {
            echo '更新成功';
            exit;
        }
        else
        {
            $errorList = $SmsSetKeyAction->getIpWriteError();
            print_r($errorList);
            exit;
        }
    }

    /**
     * 更新项目状态
     * */
    public function actionUpdateProjectStatus ()
    {
        $projectId = (int)$this->Filter->getPost('projectId');
        //$status = (int)$this->Filter->getPost('status');
        if (empty($projectId))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        $projectArr = array();
        $SmsProjectModel = SmsProjectModel::getInstance();
        $projectInfo = $SmsProjectModel->getPojectList(array('pid' => $projectId, 'row' => true));
        if (empty($projectInfo))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00008', 'msg' => '不存在的项目'));
        }
        if ( !empty($projectInfo['status']) )
        {
            if ( $projectInfo['status'] == 1)
            {
                $status = 2;
            }
            else
            {
                $status = 1;
            }
        }
        else
        {
            $status = 1;
        }

        $isTrue = $SmsProjectModel->upProjectInfo(array('status' => $status,'pid' => $projectId));
        if ($isTrue)
        {
            $SetRedisAction = SetRedisAction::getInstance();
            $isTrue = $SetRedisAction->setProjectType($projectId);
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '更新成功'));
        }
        else
        {
            return \MsgAction::returnJsonData(array('status' => 'P00007', 'msg' => '更新失败'));
        }
    }

    /**
     * 功    能: 获取OA产品信息列表
     * 作    者: 朱锋锦
     * 日    期: 2022-01-24
     *
     * @return void
     */
    public function actionGetOAProduct()
    {
        $intOAProjectId = intval($this->Filter->getPost('oa_project_id'));
        if ($intOAProjectId < 0) {
            return \MsgAction::returnJsonData(['status' => 'P00002', 'msg' => '缺少参数']);
        }

        $objOAProductAction = \OAProductAction::getInstance();
        $arrProject = $objOAProductAction->getProductByProject($intOAProjectId);
        if (!$arrProject) {
            return \MsgAction::returnJsonData(['status' => 'P00003', 'msg' => '没有数据']);
        }

        $strHtml = '<option value="-1">--请选择关联产品--</option>';
        foreach ($arrProject['product_list'] as $arrProduct) {
            $strHtml .= '<option value="' . $arrProduct['product_id'] . '">' . $arrProduct['product_id'] . '-' . $arrProduct['product_name'] . '</option>';
        }
        return \MsgAction::returnJsonData(['status' => 'P00001', 'data' => $strHtml]);
    }
}
