<?php

class GetRedisAction extends Action
{
    public function __construct()
    {
        $this->redis = RedisAction::connect();
    }

    /**
     * 查看是否存在黑名单    后台更新key  前台不读数据库
     */
    public function isInBlackList($phone)
    {
        $key = KeyAction::getBlackPhoneKey($phone);
        $isPhoneBlack = $this->redis->get($key);
        if (!empty($isPhoneBlack))
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     * 获取手机白名单
     * */
    public function isPhoneWhiteList($phone)
    {
        $key = KeyAction::getWritePhoneKey($phone);
        $isPhoneWrite = $this->redis->get($key);
        if (!empty($isPhoneWrite))
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     * 获取配置的类型
     * */
    public function getSmsConfigProjectTypeList($pid)
    {
        $redisKey = KeyAction::getProjectTypeKey($pid);
        $smsTypes = $this->redis->hGetAll($redisKey);
        if (empty($smsTypes))
        {
            $smsModel = SmsModel::getInstance();
            $list = $smsModel->getSmsConfigProjectTypeList($pid);
            $smsTypes = array();
            if (!empty($list))
            {
                foreach ($list as $ele)
                {
                    $smsTypes[] = $ele['tid'];
                }
            }
            $this->redis->hMSet($redisKey, $smsTypes);
        }
        return $smsTypes;
    }


    /**
     * 获取ip白名单     后台更新key  前台不读数据库
     */
    public function isIpWhiteList($ip)
    {
        $redisKey = KeyAction::getProjectIpWriteListKey($ip);
        $ips = $this->redis->get($redisKey);
        return !empty($ips) ? true : false;
    }


    /**
     * 获取类型配置
     * */
    public function getSmsTypeConfig($smsType)
    {
        $redisKey = KeyAction::getSmsTypeConfigKey($smsType);
        $smsTypeData = unserialize($this->redis->get($redisKey));
        if ($smsTypeData == false)
        {
            $smsModel = SmsModel::getInstance();
            $smsTypeData = $smsModel->getSmsConfig($smsType);
            $this->redis->set($redisKey, serialize($smsTypeData));
        }
        return $smsTypeData;
    }

    /**
     * 获取回执配置
     * */
    public function getSmsCallbackConfig()
    {
        $callbackData = $this->redis->hGetAll("sms:config:callback");
        if ($callbackData === false || count($callbackData) === 0)
        {
            $smsModel = SmsModel::getInstance();
            $callBackConfigs = $smsModel->getSmsCallbackConfig();
            $callbackData = [];
            foreach ($callBackConfigs as $configRaw)
            {
                //gbk编码的中文可能导致json_encode跪掉
                unset($configRaw['name']);
                $callbackData[$configRaw['id']] = json_encode($configRaw);
            }
            $this->redis->hMSet("sms:config:callback", $callbackData);
        }
        return $callbackData;
    }


    /**
     * 删除回执配置
     * */
    public function delSmsCallbackConfig()
    {
        $this->redis->del("sms:config:callback");
    }

    /**
     * 手机号 短信类型 短信数量
     *
     * */
    public function getSmsNumByPhoneType($phone, $type, $date)
    {
        $redisKey = KeyAction::getSendTypeNumsKey($phone, $date, $type);
        $nums = $this->redis->get($redisKey);
        //如果取出的是一个空值
        if ($nums != '0' && empty($nums))
        {
            //查库 根据库中的数量确定该手机该类型发短信的当天数量
            $smsModel = SmsModel::getInstance();
            $nums = $smsModel->getSmsNumByPhoneType($phone, $type, $date);
            $this->redis->set($redisKey, $nums);
        }
        return $nums;
    }

    /**
     * 发送短信增加
     * */
    public function setSmsNumByPhoneType($phone, $type, $date)
    {
        $redisKey = KeyAction::getSendTypeNumsKey($phone, $date, $type);
        $isTrue = $this->redis->incr($redisKey);
        $expireTime = strtotime(date('Y-m-d')) + 86400 - time();
        $isTrueTwo = $this->redis->expire($redisKey, $expireTime);
        return $isTrue !== false && $isTrueTwo !== false ? true : false;
    }

    /**
     *
     * 查询是否移动MAS退订手机号且为验证码短信
     * */
    public function checkTDPhone($phone, $type)
    {
        return false;
    }

    /**
     * 设置手机黑名单
     * */
    public function setBlackPhoneList($phone)
    {
        $key = KeyAction::getBlackPhoneKey($phone);
        $isSet = $this->redis->set($key, $phone);
        return $isSet !== false ? true : false;
    }

    /**
     * 设置手机白名单
     * */
    public function setWritePhoneList($phone)
    {
        $key = KeyAction::getWritePhoneKey($phone);
        $isSet = $this->redis->set($key, $phone);
        return $isSet !== false ? true : false;
    }

    /**
     * 设置IP白名单
     * */
    public function setIpWriteList($ip)
    {
        $redisKey = KeyAction::getProjectIpWriteListKey($ip);
        $isSet = $this->redis->set($redisKey, $ip);
        return $isSet !== false ? true : false;
    }

    /**
     * 精确查询
     * */
    public function queryKeysInfo($key)
    {
        $type = $this->redis->type($key);
        $keyValue = '';
        //none(key不存在) int(0)
        //string(字符串) int(1)
        //list(列表) int(3)
        //set(集合) int(2)
        //zset(有序集) int(4)
        //hash(哈希表) int(5)
        switch ($type)
        {
            case 1:  //string
                $keyValue = $this->redis->get($key);
                break;
            case 2:  //set
                $keyValue = $this->redis->sMembers($key);
                break;
            case 3: //list
                $keyValue = $this->redis->lRange($key, 0, -1);
                break;
            case 4: //zset
                $keyValue = $this->redis->zRange($key, 0, -1);
                break;
            case 5: // hash
                $keyValue = $this->redis->hGetAll($key);
                break;
            default:
        }
        return $keyValue;
    }

    /**
     * 清除短信日限
     * */
    public function clearTodayLimit($keyword)
    {
        $keyValue = $this->redis->get($keyword);
        if (!empty ($keyValue))
        {
            $isTrue = $this->redis->set($keyword, 0);
            $expireTime = strtotime(date('Y-m-d')) + 86400 - time();
            $isTrueTwo = $this->redis->expire($keyword, $expireTime);
            return $isTrue !== false && $isTrueTwo !== false ? true : false;
        }
        else
        {
            return true;
        }
    }

    /**
     * 记录每10min每接口的发送量
     * @param int    $num       num
     * @param string $apiType   apiType
     * @param int    $smsType   smsType
     * @param int    $projectId projectId
     * @return void
     */
    public function incrMonitor($num, $apiType, $smsType, $projectId)
    {
        $day = date("Y-m-d");
        $hour = date('H') * 6 + floor(date('i') / 10);
        $this->incrKeySum("sms:monitor:all:{$day}", $hour, $num);
        $this->incrKeySum("sms:monitor:api:{$apiType}:{$day}", $hour, $num);
        $this->incrKeySum("sms:monitor:type:{$smsType}:{$day}", $hour, $num);
        $this->incrKeySum("sms:monitor:proj:{$projectId}:{$day}", $hour, $num);
        $this->incrKeySum("sms:monitor:typeSum:{$day}", $smsType, $num);
        $this->incrKeySum("sms:monitor:projSum:{$day}", $projectId, $num);
    }

    /**
     * @param string $key   key
     * @param string $field field
     * @param int    $num   num
     * @return void
     */
    private function incrKeySum($key, $field, $num)
    {
        if (!$this->redis->exists($key))
        {
            $this->redis->hSet($key, $field, 1);
            $this->redis->expire($key, 86400 * 15);
        }
        $this->redis->hIncrBy($key, $field, $num);
    }

    /**
     * @param array $dataArr data
     * @return string
     */
    public function add2Queue($dataArr)
    {
        $redisKey = "sms:queue:send";
        $dataArr['ticketId'] = uniqid() . mt_rand(1000, 9999);
        $this->redis->lPush($redisKey, serialize($dataArr));
        return $dataArr['ticketId'];
    }
}
