<?php

use Octopus\PdoEx;

/**
 * Copyright (c)  上海二三四五网络科技有限公司
 * 文件名称：AdminAction.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：04-01, 2017
 */
class AdminAction extends Action
{

    /**
     * 添加管理员操作日志
     *
     * @param int    $passid    passid
     * @param array  $logs      logs
     * @param string $adminUser adminUser
     *
     * @return boolean|int
     */
    public function operationLog($passid, $logs, $adminUser = "")
    {
        $insert = array(
            'passid'  => $passid,
            'log'     => implode(',', $logs),
            'op_user' => $adminUser ? $adminUser : @$_COOKIE['admin_user'],
            'op_time' => date('Y-m-d H:i:s'),
        );
        $logModel = loadModel('Log');
        return $logModel->addAdminLog($insert);
    }

}
