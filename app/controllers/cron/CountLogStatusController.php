<?php

use Octopus\PdoEx;

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:CountLogStatusController.php
 * 摘    要:
 * 作    者:<EMAIL>
 * 修改日期: 2015/12/17
 */
includeBase('CronController');

class CountLogStatusController extends CronController
{

    public function actionIndex()
    {
        $redis = RedisEx::getInstance();
        $domains = array_keys(Config::get("domainServerIPs"));
        $LSCN = array();
        $LSUN = array();
        foreach ($domains as $domain)
        {
            $lscnkey = 'LSCN:' . str_replace('.', '_', $domain);
            $lsunkey = 'LSUN:' . str_replace('.', '_', $domain);
//            $LSCN[$domain] = intval($redis->get($lscnkey));
//            $LSUN[$domain] = intval($redis->get($lsunkey));
            $redis->set($lscnkey, 0);
            $redis->set($lsunkey, 0);
        }

    }

}
