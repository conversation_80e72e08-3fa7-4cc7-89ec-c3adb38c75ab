<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：DataStatisticModel.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：23/08/2018 10:51
 */

namespace Crontab;

use Model;

class DataStatisticModel extends Model
{
    protected $dbName = DATABASE_SMS;
    protected $tableName = 'my_sms_provider';
    protected $pkName = 'id';

    /**
     * User: panj
     *
     * @param array $data data
     *
     * @return array|bool
     */
    public function getSmsProvider($data)
    {
        $sql = "select id, status, sign, action_name, unit_price from {$this->tableName} where sign in ('" . implode("','", $data) . "')";

        $res = $this->pdo->findAll($sql);

        return $res !== false ? $res : false;
    }

    public function getSmsProviderById($id)
    {
        return $this->pdo->find("select * from {$this->tableName} where id = :id", [':id' => $id]);
    }

    public function getProviderNameMap()
    {
        return [
            '\Smschannel\ZhengaoSmsChannel4Action' => '点集',
            '\Smschannel\DahanTricomChannelAction' => '大汉三通',
            '\Smschannel\AliSmsChannelAction' => '阿里云',
        ];
    }
}
