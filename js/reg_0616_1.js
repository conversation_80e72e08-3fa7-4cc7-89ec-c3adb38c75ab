var enableCheck = true;
var intervalHandle = false;
var intervalTimes = 60;
jQuery(function($) {
    $(".ipt-txt").bind({
        focus: function() {
            $(this).addClass("ipt-txt-current");
        },
        blur: function() {
            $(this).removeClass("ipt-txt-current");
        }
    });
    $("#validate").bind({
        focus: function() {
            $("#msg_validate").hide();
            if ($(this).val() == '请输入算式结果')
            {
                $(this).val("");
                $(this).css('color', 'black');
            }
        },
        blur: function() {
            if ($(this).val() == '')
            {
                $(this).val("请输入算式结果");
                $(this).css('color', 'gray');
            }
            else
            {
                $(this).css('color', 'black');
            }
            $(this).removeClass("ipt-txt-current");
            checkValidate_sj();
        }
    });
    $("#validate_phone").bind({
        focus: function() {
            $("#msg_validate_phone").css('visibility', 'hidden');
            if ($(this).val() == '请输入验证码')
            {
                $(this).val("");
                $(this).css('color', 'black');
            }
        },
        blur: function() {
            if ($(this).val() == '')
            {
                $(this).val("请输入验证码");
                $(this).css('color', 'gray');
            }
            else
            {
                $(this).css('color', 'black');
            }
            $(this).removeClass("ipt-txt-current");
            checkValidatePhone_sj();
        }
    });
    $('#btn_yzm_phone_sms').bind({
        click: function() {
            $("#validate_phone").attr('action', '');
            var phone = $('#usernametxt').val().trim();
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('请填写正确手机号！').show();
                return false;
            }
            val = $("#validate").val();
            if (val === '' || val == '请输入算式结果')
            {
                $("#validate").addClass("ipt-txt-error").next('.i-retok').hide();
                $("#msg_validate").addClass('lg_form-tips form-tips-error').html('请输入正确的计算结果').show();
                return false;
            }
            jQuery.ajax({
                url: "/api/check",
                data: 'type=validate&val=' + val,
                type: 'POST',
                async: false,
                success: function(response) {
                    err = response;
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR.status);
                },
                timeout: 3000
            });
            $(".g-mask").css({"display": "none"});
            $("#demo-pop-wx").css({"display": "none"});
            $('#form-tips-voice-msg').html('系统已将短信发送到您的手机，请注意查收。');
            $('#btn_yzm_phone').attr('action', 'sms');
            if (err == 1)
            {
                intervalTimes = 60;
                clearInterval(intervalHandle);
                intervalHandle = setInterval(function() {
                    showSendMsg('sms');
                }, 1000);
                $.post("/reg/sendPhoneCode", {'phone': phone, 'validate': val}, function(data) {
                    if (data == '400.0')
                    {
                        showSendBtn('sms');
                        $("#validate_phone").attr('action', 'frequently');
                        $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('发送频繁 ，请稍后再试！').show();
                    }
                    else if (data == '500.0')
                    {
                        showSendBtn('sms');
                        $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('服务器繁忙，请稍后再试！').show();
                    }
                    else if (data == '300.0')
                    {
                        showSendBtn('sms');
                        $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('手机号码格式错误，请稍后再试！').show();
                    }
                });
            }
            else
            {
                $("#pic").attr('src', '/randCaptcha.php?J' + Math.random());
            }
        }
    });
    $('#btn_yzm_phone').bind({
        click: function() {
            var phone = $('#usernametxt').val().trim();
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('请填写正确手机号！').show();
                return false;
            }
            val = $("#validate").val();
            if (val === '' || val == '请输入算式结果')
            {
                $("#validate").addClass("ipt-txt-error").next('.i-retok').hide();
                $("#msg_validate").addClass('lg_form-tips form-tips-error').html('请输入正确的计算结果').show();
                return false;
            }
            jQuery.ajax({
                url: "/api/check",
                data: 'type=validate&val=' + val,
                type: 'POST',
                async: false,
                success: function(response) {
                    err = response;
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR.status);
                },
                timeout: 3000
            });
            if (err == 1)
            {
                $('#form-tips-voice-msg').show();
                if ($('#btn_yzm_phone').attr('action') == 'voice')
                {
                    intervalTimes = 60;
                    clearInterval(intervalHandle);
                    intervalHandle = setInterval(function() {
                        showSendMsg('voice');
                    }, 1000);
                    $.post("/reg/sendPhoneVoiceCode", {'phone': phone, 'validate': val}, function(data) {
                        if (data == '400.0')
                        {
                            showSendBtn('voice');
                            $("#validate_phone").attr('action', 'frequently');
                            $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('验证太频繁，请稍后再试！').show();
                        }
                        else if (data == '500.0')
                        {
                            showSendBtn('voice');
                            $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('服务器繁忙，请稍后再试！').show();
                        }
                        else if (data == '300.0')
                        {
                            showSendBtn('voice');
                            $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('手机号码格式错误，请稍后再试！').show();
                        }
                        else if (data == '600.0')
                        {
                            showSendBtn('voice');
                            $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('图片验证码错误，请重新输入！').show();
                        }
                    });
                }
                else
                {
                    intervalTimes = 60;
                    clearInterval(intervalHandle);
                    intervalHandle = setInterval(function() {
                        showSendMsg('sms');
                    }, 1000);
                    $.post("/reg/sendPhoneCode", {'phone': phone, 'validate': val}, function(data) {
                        if (data == '400.0')
                        {
                            showSendBtn('sms');
                            $("#validate_phone").attr('action', 'frequently');
                            $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('验证太频繁，请稍后再试！').show();
                        }
                        else if (data == '500.0')
                        {
                            showSendBtn('sms');
                            $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('服务器繁忙，请稍后再试！').show();
                        }
                        else if (data == '300.0')
                        {
                            showSendBtn('sms');
                            $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('您输入手机号码格式错误！').show();
                        }
                        else if (data == '600.0')
                        {
                            showSendBtn('sms');
                            $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('图片验证码错误，请重新输入！').show();
                        }
                    });
                }
            }
            else
            {
                $("#pic").attr('src', '/randCaptcha.php?J' + Math.random());
            }
        }
    });

    $(".btn-submit").bind({
        mousedown: function(e) {
            checkAll();
            return false;
        },
        click: function() {
            return false;
        }
    });
    $("#demo-pop-wx .logreg-close").click(function() {
        $(".g-mask").css({"display": "none"});
        $("#demo-pop-wx").css({"display": "none"});
    });
    $("#demo-pop-reg .logreg-close").click(function() {
        $(".g-mask").css({"display": "none"});
        $("#demo-pop-reg").css({"display": "none"});
    });
});
function showPopWx() {
    $("#demo-pop-wx").css({"display": "block"});
    $(".g-mask").css({
        "display": "block",
        "height": $(document).height()
    });
}
;
function showSendMsg(from) {
    if (intervalTimes > 1) {
        --intervalTimes;
        $("#btn_yzm_phone").hide();
        $("#btn_yzmed_phone").text('重新发送（' + intervalTimes + '）').show();
    } else {
        showSendBtn(from);
    }
}
function showSendBtn(from) {
    clearInterval(intervalHandle);
    intervalTimes = 60;
    if (from == 'voice') {
        $("#btn_yzmed_phone").hide();
        $("#btn_yzm_phone").text('获取语音验证码').show();
    } else {
        $("#btn_yzmed_phone").hide();
        $("#btn_yzm_phone").text('发送验证短信').show();
    }
}
function checkUser(n)
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    var username = $("#usernametxt").val().trim();
    var isvalid = true;
    var msg_username = '';

    if (username.length < 2)
    {
        isvalid = false;
        msg_username = '最少2个字符';
    }
    if (username.replace(/[^\x00-\xff]/g, "**").length > 24)
    {
        isvalid = false;
        msg_username = '请不要超过24个字符';
    }

    if (/[^\u4E00-\u9FA5\w_@\.\-]/.test(username))
    {
        isvalid = false;
        msg_username = '请输入汉字，字母，数字';
    }

    if (!isvalid)
    {
        $("#msg_username").addClass('form-tips form-tips-error').html(msg_username).show();
        $("#usernametxt").addClass("ipt-txt-error").next('.i-retok').hide();
        return false;
    }

    var type = 'username';
    var typeName = '用户名';
    if (/^1[0123456789]\d{9}$/.test(username)) {
        type = 'phone';
        typeName = '手机号码';
    }
    if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username)) {
        type = 'email';
        typeName = '邮箱';
    }

    if (type == 'phone') {
        jQuery.ajax({
            url: "/api/check/jsonp",
            data: 'type=' + type + '&value=' + username + '&region=1',
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(response) {
                if (response == -1) {
                    $('#btn_yzm_phone').attr('action', 'sms');
                    $('#form-tips-voice-msg').html('系统已将短信发送到您的手机，请注意查收。');
                    showSendBtn('sms');
                    err = 0;
                } else {
                    $('#btn_yzm_phone').attr('action', 'voice');
                    $('#form-tips-voice-msg').html('系统将在1分钟内<strong>拨打您的手机</strong>（来电号码可能显示为<em class="c-style">未知号码</em>或<em class="c-style">4000633481</em>）<span class="pos-r">【<a href="javascript:;" onclick="showPopWx();return false;" class="trig-link">我未收到来电</a>】</span>');
                    showSendBtn('voice');
                    err = response;
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(jqXHR.status);
            },
            timeout: 3000
        });
    } else if (type == 'email') {
        jQuery.ajax({
            url: "/api/check/jsonp",
            data: 'type=' + type + '&value=' + username + '&status=0',
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(response) {
                err = response;
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(jqXHR.status);
            },
            timeout: 3000
        });
    } else {
        jQuery.ajax({
            url: "/api/check/jsonp",
            data: 'type=' + type + '&value=' + username,
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(response) {
                err = response;
            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(jqXHR.status);
            },
            timeout: 3000
        });
    }

    if (typeof err === "undefined")
    {
        $("#msg_agree").addClass('form-tips-error').html('您的注册可能过于频繁，若有疑问请联系客服！').show();
        return false;
    }

    if (type == 'username')
    {
        if (err == 1)
        {
            isvalid = false;
            msg_username = '这个' + typeName + '已经被使用，换一个吧';

        }
        else if (err == 2)
        {
            isvalid = false;
            msg_username = '这个2345帐号不适合您，换一个吧';

        }
    }
    else
    {
        if (err != 0) {
            isvalid = false;
            msg_username = '这个' + typeName + '已经被使用，换一个吧';
        }

    }

    if (isvalid === true)
    {
        if (username !== username.toLowerCase())
        {
            msg_username = '登录区分大小写，请牢记您的帐号';
        }
        else
        {
            msg_username = '';
        }
        $("#msg_username").removeClass('form-tips-error').html('').hide();
        $("#usernametxt").removeClass("ipt-txt-error").next('.i-retok').show();

        if (/^1[0123456789]\d{9}$/.test(username)) {
            $("#yzm_phone").show();
            $('#reg_type').val('phone');
        } else if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username)) {
            $("#yzm_phone").hide();
            $('#reg_type').val('email');
        } else {
            $("#yzm_phone").hide();
            $('#reg_type').val('username');
        }

        return true;
    }
    else
    {
        $("#yzm_phone").hide();
        $("#msg_username").addClass('form-tips form-tips-error').html(msg_username).show();
        $("#usernametxt").addClass("ipt-txt-error").next('.i-retok').hide();
        return false;
    }

}
function checkUserLength()
{
    if ($("#usernametxt").val().cnSize() > 24)
    {
        $("#msg_username").addClass('form-tips form-tips-error').html('请不要超过24个字符').show();
        $("#usernametxt").addClass("ipt-txt-error");
        return false;
    }
    $("#msg_username").removeClass('form-tips-error').html('2~24个字符，汉字，字母，数字').show();
    $("#usernametxt").removeClass("ipt-txt-error");
    return true;
}

function safe(eName)
{
    var isvalid = true;
    var msg_pwd = '';

    $("#pwd_strong").show();
    var pass = $("#password").val();
    if (pass.length > 16)
    {
        msg_pwd = '最多16个字符';
        isvalid = false;
    }

    var score = getPwdScore(pass);
    if (score <= 10)
    {
        $('.form-pwd-tips').attr('class', 'form-pwd-tips').addClass('form-pwd-tips-1').find('i').each(function(index, ele) {
            if (index <= 0) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('弱');
        $("#pwd_strength").val('1');

        pass == '' ? msg_pwd = '密码不能为空' : msg_pwd = '密码强度不能为弱';
        isvalid = false;
    }
    else if (score >= 11 && score <= 20)
    {
        $('.form-pwd-tips').attr('class', 'form-pwd-tips').addClass('form-pwd-tips-2').find('i').each(function(index, ele) {
            if (index <= 1) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('中');
        $("#pwd_strength").val('2');
        isvalid = true;
    }
    else if (score >= 21 && score <= 30)
    {
        $('.form-pwd-tips').attr('class', 'form-pwd-tips').addClass('form-pwd-tips-3').find('i').each(function(index, ele) {
            if (index <= 2) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('强');
        $("#pwd_strength").val('3');
        isvalid = true;
    }
    else
    {
        $('.form-pwd-tips').attr('class', 'form-pwd-tips').addClass('form-pwd-tips-3').find('i').each(function(index, ele) {
            if (index <= 3) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('很强');
        $("#pwd_strength").val('4');
        isvalid = true;
    }

    if (eName === 'keyup')
    {
        $('#msg_pwd').hide().removeClass('form-tips-error').text('');
        $('#pwd_strong').show();
        $("#password").removeClass("ipt-txt-error");

    }

    if (eName === 'blur')
    {
        if (isvalid === true)
        {
            $('#msg_pwd').hide().removeClass('form-tips-error').text('');
            $('#pwd_strong').show();
            $("#password").removeClass("ipt-txt-error").next('.i-retok').show();

            if ($("#password").val() != $("#repassword").val() && $("#repassword").val() != '')
            {
                $("#repassword").addClass("ipt-txt-error").next('.i-retok').hide();
                $("#msg_repassword").addClass('form-tips form-tips-error').html('两次输入密码不一致').show();
            }

        }
        else
        {
            $('#msg_pwd').show().addClass('form-tips-error').html(msg_pwd);
            $('#pwd_strong').hide();
            $("#password").addClass("ipt-txt-error").next('.i-retok').hide();
            $("#repassword").next('.i-retok').hide();

            if (pass == '')
            {
                $("#repassword").removeClass("ipt-txt-error").val('').next('.i-retok').hide();
                $("#msg_repassword").removeClass('form-tips-error').html('').hide();

            }
        }


    }

    return isvalid;


}
function checkRepass()
{
    if ($("#password").val() == '')
    {
        return;
    }

    if ($("#repassword").val() != $("#password").val())
    {
        $("#msg_repassword").addClass('form-tips form-tips-error').html('两次输入密码不一致').show();
        $("#repassword").addClass("ipt-txt-error").next('.i-retok').hide();
        return false;
    }
    else
    {
        $("#msg_repassword").removeClass('form-tips-error').html('').hide();
        $("#repassword").removeClass("ipt-txt-error").next('.i-retok').show();
        return true;
    }

}


function checkValidate_sj()
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    val = $("#validate").val();
    if (val === '' || val == '请输入算式结果')
    {
        $("#validate").addClass("ipt-txt-error").next('.i-retok').hide();
        $("#msg_validate").addClass('lg_form-tips form-tips-error').html('请输入正确的计算结果').show();
        return false;
    }
    jQuery.ajax({
        url: "/api/check",
        data: 'type=validate&val=' + val,
        type: 'POST',
        async: false,
        success: function(response) {
            err = response;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    if (err == 1)
    {
        $("#msg_validate").removeClass('form-tips-error').html('').hide();
        $("#validate").removeClass("ipt-txt-error").next('.i-retok').show();
        return true;
    } else {
        $("#validate").addClass("ipt-txt-error").next('.i-retok').hide();
        $("#msg_validate").addClass('form-tips form-tips-error').html('请输入正确的计算结果').show();
        $("#pic").attr('src', '/randCaptcha.php?J' + Math.random());
        return false;
    }

}
function checkValidatePhone_sj()
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    if ($("#validate_phone").attr('action') == 'frequently')
    {
        $("#msg_validate_phone").css('visibility', 'visible').addClass('lg_form-tips form-tips-error').html('发送频繁 ，请稍后再试！').show();
        return false;
    }

    var code = $("#validate_phone").val().trim();
    if (code === '' || code === '请输入验证码')
    {
        $("#msg_validate_phone").css('visibility', 'visible').addClass('lg_form-tips form-tips-error').html('请输入手机验证码').show();
        return false;
    }
    jQuery.ajax({
        url: "/reg/checkPhoneCode",
        data: 'code=' + code + '&phone=' + $("#usernametxt").val().trim(),
        async: false,
        type: 'POST',
        success: function(response) {
            response === '200.0' ? err = 0 : err = 1;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    if (err)
    {
        $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('您输入的验证码错误，请重新输入').show();
        $("#validate_phone").addClass("ipt-txt-error").next('.i-retok').hide();
        return false;
    }
    else
    {
        $("#msg_validate_phone").css('visibility', 'visible').removeClass('form-tips-error').html('').hide();
        $("#validate_phone").removeClass("ipt-txt-error").next('.i-retok').show();
        return true;
    }

}


function checkAgree()
{
    if (!$("#agree").is(':checked'))
    {
        $("#msg_agree").addClass('form-tips-error').html('请同意2345服务协议').show();
        return false;
    }
    else
    {
        $("#msg_agree").removeClass('form-tips-error').html('').hide();
        return true;
    }
}

function checkAll()
{
    if (!checkUser(1))
    {
        return false;
    }
    if (!safe('blur'))
    {
        return false;
    }
    if (!checkRepass())
    {
        return false;
    }

    if ($('#reg_type').val() !== 'phone' && !checkValidate_sj())
    {
        return false;
    }

    if ($('#reg_type').val() === 'phone' && !checkValidatePhone_sj())
    {
        return false;
    }
    if (!checkAgree())
    {
        return false;
    }
    enableCheck = false;
    var msgs = {
        "300": {
            'username': {
                '0': '2345帐号最少2个字符 @msg_username',
                '1': '2345帐号请不要超过24个字符 @msg_username',
                '2': '2345帐号请输入汉字，字母，数字，或邮箱地址 @msg_username',
                '3': '密码最少6个字符 @msg_pwd',
                '4': '密码最多16个字符 @msg_pwd',
                '5': '请输入正确的邮箱 @msg_username',
                '6': '此帐号已被注册，请修改2345帐号 @msg_username',
                '7': '此邮箱已被注册，请换一个 @msg_username'
            },
            'phone': {
                '0': '请输入正确的手机号码 @msg_username',
                '1': '密码最少6个字符 @msg_pwd',
                '2': '密码最多16个字符 @msg_pwd',
                '3': '此手机号已被注册 @msg_username'
            },
            'email': {
                '0': '请输入正确的邮箱地址 @msg_username',
                '1': '密码最少6个字符 @msg_pwd',
                '2': '密码最多16个字符 @msg_pwd',
                '3': '此邮箱号已被注册 @msg_username'
            }
        },
        "400": {
            '0': '非法域名调用',
            '1': '非法IP调用',
            '2': '批量刷CHECK',
            '3': 'IP段被禁止',
            '4': 'IP被禁止',
            '5': '未验证通过（缺少isValidate）'
        }
    };

    $.ajax({
        url: "/reg.php",
        data: $("#myForm").serialize(),
        type: 'POST',
        async: false,
        success: function(response) {
            res = eval('(' + response + ')');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });

    if (typeof res.msgCode !== "undefined" && res.msgCode !== '')
    {
        var regType = $('#reg_type').val();
        var codeArr = res.msgCode.split('.');
        if (codeArr[0] == '400')
        {
            $("#msg_agree").addClass('form-tips-error').html(msgs['400'][codeArr[1]] + '，若有疑问请联系客服！').show();
        }
        else if (codeArr[0] == '300')
        {
            msgArr = msgs['300'][regType][codeArr[1]].split('@');
            $("#" + msgArr[1]).addClass('form-tips-error').html(msgArr[0]).show().prev('div').children('input').addClass("ipt-txt-error").next('.i-retok').hide();
        }

        return false;
    }

    if (typeof res.msg !== "undefined" && res.msg !== '')
    {
        msgArr = res.msg.split('@');
        if (typeof msgArr[1] === "undefined")
        {
            console.log(msgArr[0]);
        }
        else
        {
            $("#" + msgArr[1]).addClass('form-tips-error').html(msgArr[0]).show().prev('div').children('input').addClass("ipt-txt-error").next('.i-retok').hide();
        }
        return false;
    }

    if (typeof res.forwardPage === "undefined" || res.forwardPage === '')
    {
        res.forwardPage = '/reg.php';
    }

    if (typeof res.loadPage !== "undefined" && res.loadPage !== '')
    {
        setTimeout(function() {
            window.location = res.forwardPage;
        }, 1000);
        $.getScript(res.loadPage, function() {
            window.location = res.forwardPage;
        });
    }
    else
    {
        window.location = res.forwardPage;
    }
    return false;


}


/*****************   until functoins   ****************/
function checkPassSame(pass)
{
    var first = pass.substring(0, 1);
    var exp = new RegExp('^' + first + '+$');
    if (exp.test(pass))
    {
        return false;
    }
    if (first == 'a' || first == 'A')
    {
        f = pass.charCodeAt(0);
        for (i = 1; i < pass.length; i++)
        {
            tmp = pass.charCodeAt(i);
            if (tmp - f != i)
            {
                return true;
            }
        }
        return false;
    }
    return true;
}
function passwordGrade(pwd)
{
    var score = 0;
    var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
    var repeatCount = 0;
    var prevChar = '';
    //check length
    var len = pwd.length;
    score += len > 18 ? 18 : len;
    //check type
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (eval('/' + regexArr[i] + '/').test(pwd))
            score += 4;
    }
    //bonus point
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
            score += 2;
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
            score += 2;
    }
    //deduction
    for (var i = 0, num = pwd.length; i < num; i++) {
        if (pwd.charAt(i) == prevChar)
            repeatCount++;
        else
            prevChar = pwd.charAt(i);
    }
    score -= repeatCount * 1;
    return score;
}
function login(n, username)
{
    if (n == 2)
    {
        window.location.href = "http://login.duote.com/login.php";
    }
    else if (n == 3)
    {
        window.location.href = "http://bbs.haozip.com/login.php";
    }
    else if (n == 4)
    {
        window.location.href = "http://bbs.shanhu99.com/logging.php?action=login";
    }
    else if (n == 5)
    {
        window.location.href = "http://ie.2345.com/bbs/logging.php?action=login";
    }
    else
    {
        if (username)
        {
            window.location.href = "http://login.2345.com/login.php?username=" + username;
        }
        else
        {
            window.location.href = "http://login.2345.com/login.php";
        }
    }
}
function getPwdScore(pass)
{
    var score = 0;
    if (pass.length < 6)
    {
        score = 0;
    }
    else if (pass == $('#usernametxt').val())
    {
        score = 0;
    }
    else if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
    {
        score = 0;
    }
    else
    {
        score = passwordGrade(pass);
    }
    return score;

}

String.prototype.trim = function()
{
    return this.replace(/(^\s*)|(\s*$)/g, "");
};

String.prototype.cnSize = function()
{
    //换算中文字长
    var arr = this.match(/[^\x00-\xff]/ig);
    return this.length + (arr == null ? 0 : arr.length);
};