<!DOCTYPE HTML>
<html>
    <head>
        <meta charset="gb2312">
        <title>加速浏览器弹窗</title>
        <link href="/chrome/css/jiasu0313.css" rel="stylesheet" type="text/css">
        <style type="text/css">
            body > :not(.wrap){display:none;}
        </style>
    </head>
    <body>
        <div class="wrap">
            <div class="container">
                <a id="closeA" tabindex="-1" class="close" href="#"></a>
                <div class="content">

                    <div class="mod_box">
                        <div class="hd">合作帐号登录</div>
                        <div class="bd account">
                            <a id="qqA" tabindex="5" href="#" class="qq" title="用QQ帐号登录"></a>
                        </div>
                    </div>
                    <div class="line"></div>
                    <div class="mod_box">
                        <div class="hd">2345帐号登录</div>
                        <div class="bd">
                            <form onsubmit="return form_submit();">
                                <ul class="form_login">
                                    <li>
                                        <div id="J_op" class="con pos">
                                            <div class="sel">
                                                <input id="usernameHD" tabindex="-1" class="inp" type="text" autocomplete="off"/>
                                                <input id="username" tabindex="1" class="inp" type="text" autocomplete="off" placeholder="手机/已验证邮箱/用户名" style="position: absolute; left: 0px; top: 0px;"/>
                                                <a id="dropbtn" tabindex="-1" class="btn_arrow" href="#"></a>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="con">
                                            <input id="password" tabindex="2" class="inp" type="password" placeholder="密码"/>
                                            <input id="realPassword" type="hidden"/>
                                        </div>
                                    </li>
                                    <li id="errorMsg" class="msg" style="display:none;"></li>
                                    <li id="yzm" class="yz_area" style="display:{{$pageArray.display}};">
                                        <div class="con">
                                            <input id="check_code"{{if $pageArray.display!='none'}} tabindex="3"{{/if}} class="inp inp_s" type="text" placeholder="输入验证码"/>
                                                   <img id="checkImg" tabindex="-1" class="pic" src="/check_code.php"/>
                                            <a id="checkA" tabindex="-1" href="#">看不清，换一张</a>
                                        </div>
                                    </li>
                                    <li>
                                        <label class="remember"><input id="remember" class="checkbox" type="checkbox" checked="checkbox"/>记住密码</label>
                                        <a id="findA" class="find" tabindex="-1" href="#">找回密码</a>
                                    </li>
                                    <li class="btn_area">
                                        <input id="submitButton" tabindex="4" type="submit" class="btn_main" value="登 录"/>
                                    </li>
                                    <li class="desc">还没有2345帐号，<a id="regA" tabindex="-1" href="#">立即注册 &gt;&gt;</a></li>
                                </ul>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script type="text/javascript" src="/chrome/js/login.js"></script>
        <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
        <script type="text/javascript" src="/js/jquery.autocomplete.min.js"></script>
        <script type="text/javascript">
                                var user, users;
                                function setAutoComplete() {
                                    $('#usernameHD').bind("input.autocomplete", function() {
                                        $(this).trigger('keydown.autocomplete');
                                    });
                                    $("#usernameHD").autocomplete(users.sort(), {resultsClass: 'suggest_box', inputClass: '', highlight: false, matchCase: true, formatItem: function(row) {
                                            return '<p title="' + row[0] + '">' + row[0] + '</p><span class="del" onclick="delUser(\'' + row[0] + '\');return false;"></span>';
                                        }, minChars: 0, scroll: true, width: 240});
                                    $("#usernameHD").result(function(data, value) {
                                        $(this).val('');
                                        $("#username").val(value.toString().replace(/<span[^>]*?>[^<]*?<\/span>/g, '').replace(/<[^>]*?>/g, ''));
                                        var realPassword = chrome.sync.getLoginUserPwd($("#username").val());
                                        if (realPassword) {
                                            $("#realPassword").val(realPassword);
                                            $("#password").val("******");
                                        } else {
                                            $("#password").val("");
                                        }
                                        setTimeout(function() {
                                            $("#username").focus();
                                        }, 50);
                                    });
                                }
                                function delUser(username) {
                                    chrome.sync.delUser(username);
                                    if ($("#username").val() == username) {
                                        $("#username").val("");
                                        $("#password").val("");
                                    }
                                    for (i in users) {
                                        if (users[i] == username) {
                                            users.splice(i, 1);
                                        }
                                    }
                                    $("#usernameHD").unautocomplete();
                                    setAutoComplete();
                                }
                                function showError(msg) {
                                    $("#errorMsg").html("<p>" + msg + "</p>");
                                    $("#errorMsg").show();
                                    $("#submitButton").removeAttr("disabled");
                                    $("#submitButton").val("登 录");
                                }
                                function hideError() {
                                    $("#errorMsg").html("");
                                    $("#errorMsg").hide();
                                }
                                function form_submit() {
                                    if ($("#submitButton").attr("disabled") == "disabled") {
                                        return false;
                                    }
                                    $("#submitButton").attr("disabled", "disabled");
                                    $("#submitButton").val("登录中...");
                                    cc("login_all");
                                    if ($("#username").val() == "")
                                    {
                                        showError("请输入2345帐号");
                                        $("#username").focus();
                                        return false;
                                    }
                                    if ($("#password").val() == "")
                                    {
                                        showError("请先输入密码");
                                        $("#password").focus();
                                        return false;
                                    }
                                    if ($("#password").val() != "******") {
                                        $("#realPassword").val(MD5($("#password").val()));
                                    }
                                    hideError();
                                    var username = $.trim($("#username").val());
                                    chrome.sync.onLogin(username, "", 1, "", "", "", "", "");
                                    $.ajax({
                                        type: "POST",
                                        url: "/chrome/sign",
                                        data: "username=" + username + "&password=" + $("#realPassword").val() + "&check_code=" + $("#check_code").val(),
                                        success: function(data) {
                                            data = eval("(" + data + ")");
                                            switch (data["sts"]) {
                                                case -3:
                                                    cc("login_fail");
                                                    chrome.sync.onLogin(username, "", 3, "帐号或密码不正确，请重新输入", "", "", "", "");
                                                    showError("帐号或密码不正确，请重新输入");
                                                    break;
                                                case -1:
                                                    cc("login_fail");
                                                    chrome.sync.onLogin(username, "", 3, "帐号未激活", "", "", "", "");
                                                    chrome.sync.openUrl("/active_error.html", "");
                                                    break;
                                                case 0:
                                                    cc("login_success");
                                                    chrome.sync.onLogin(username, "", 0, "", data["uid"], data["sec"], data["passid"], data["token"]);
                                                    if ($("#remember").is(':checked')) {
                                                        chrome.sync.setLoginUserInfo(username, $("#realPassword").val());
                                                        chrome.sync.setIsAutoLogin(true);
                                                    } else {
                                                        chrome.sync.setLoginUserInfo(username, "");
                                                        chrome.sync.setIsAutoLogin(false);
                                                    }
                                                    chrome.sync.closeDlg();
                                                    break;
                                                case 1:
                                                    cc("login_fail");
                                                    chrome.sync.onLogin(username, "", 3, "帐号或密码不正确，请重新输入", "", "", "", "");
                                                    $("#password").val("");
                                                    $("#username").focus();
                                                    $("#username").select();
                                                    showError("帐号或密码不正确，请重新输入");
                                                    break;
                                                case 3:
                                                    cc("login_fail");
                                                    chrome.sync.onLogin(username, "", 3, "验证码输入错误", "", "", "", "");
                                                    showError("验证码输入错误");
                                                    break;
                                                case 4:
                                                    cc("login_fail");
                                                    chrome.sync.onLogin(username, "", 3, "验证码不能为空", "", "", "", "");
                                                    showError("验证码不能为空");
                                                    break;
                                                case 1004:
                                                    cc("login_fail");
                                                    chrome.sync.onLogin(username, "", 3, "登录有误，错误代码1004，请联系客服", "", "", "", "");
                                                    showError("登录有误，错误代码1004，请联系客服");
                                                    break;
                                                case 1005:
                                                    cc("login_fail");
                                                    chrome.sync.onLogin(username, "", 3, "登录有误，错误代码1005，请联系客服", "", "", "", "");
                                                    showError("登录有误，错误代码1005，请联系客服");
                                                    break;
                                                case 1006:
                                                    cc("login_fail");
                                                    chrome.sync.onLogin(username, "", 3, "登录有误，错误代码1006，请联系客服", "", "", "", "");
                                                    showError("登录有误，错误代码1006，请联系客服");
                                                    break;
                                            }
                                            if (data["display"] === "") {
                                                $("#check_code").attr({"tabindex": "3"});
                                                $("#yzm").show();
                                                $('#checkImg').attr({'src': '/check_code.php?' + new Date()});
                                            } else {
                                                $("#check_code").attr({"tabindex": "-1"});
                                                $("#yzm").hide();
                                            }
                                            if ($("#yzm").css("display") != "none") {
                                                $("#check_code").val("");
                                            } else {
                                                $("#password").val("");
                                            }
                                        },
                                        error: function(XMLHttpRequest, textStatus, errorThrown) {
                                            if (XMLHttpRequest.readyState == 0) {
                                                showError('您的网络情况异常，请检查网络配置');
                                                chrome.sync.onLogin(username, "", 3, "您的网络情况异常，请检查网络配置", "", "", "", "");
                                            } else {
                                                showError('无法连接到2345通行证，请稍后再试');
                                                chrome.sync.onLogin(username, "", 3, "无法连接到2345通行证，请稍后再试", "", "", "", "");
                                            }
                                        }
                                    });
                                    return false;
                                }
                                function cc(a) {
                                    var b = arguments,
                                            web = "ajax93",
                                            a2,
                                            i1 = document.cookie.indexOf("uUiD="),
                                            i2;
                                    if (b.length > 1)
                                        web = b[1];
                                    if (i1 != -1) {
                                        i2 = document.cookie.indexOf(";", i1);
                                        a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
                                    }
                                    if (!a2) {
                                        a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                                        document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
                                    }
                                    if (a.length > 0) {
                                        var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                                        $.getScript(c);
                                    }
                                    return true;
                                }
                                $(document).ready(function() {
                                    document.ondragstart = function() {
                                        return false;
                                    };
                                    $(this).bind("contextmenu", function(event) {
                                        return $(event.target).is("input") && ($(event.target).attr("type") == "text" || $(event.target).attr("type") == "password");
                                    });
                                    chrome.sync.setDlgSize(684, 422);
                                    $("#usernameHD").keydown(function(event) {
                                        if (event.keyCode == 13) {
                                            return false;
                                        }
                                    });
                                    $("#closeA").click(function() {
                                        if (!$("#submitButton").attr("disabled")) {
                                            chrome.sync.closeDlg();
                                        }
                                        return false;
                                    });
                                    $("#qqA").click(function() {
                                        cc('login_qq');
                                        chrome.sync.openUrl('{{$smarty.const.LOGIN_HOST}}/chrome/qq/', '');
                                        return false;
                                    });
                                    $("#weiboA").click(function() {
                                        cc('login_weibo');
                                        chrome.sync.openUrl('/chrome/weibo/', '');
                                        return false;
                                    });
                                    $('#dropbtn').click(function() {
                                        if ($(".suggest_box").length && $(".suggest_box:first").css("display") != "none") {
                                            $('#usernameHD').blur();
                                        } else {
                                            $('#usernameHD').trigger('input.autocomplete');
                                            $('#usernameHD').focus();
                                            $('#usernameHD').click();
                                        }
                                        return false;
                                    });
                                    $("#username").bind("input propertychange", function() {
                                        var realPassword = chrome.sync.getLoginUserPwd($.trim($(this).val()));
                                        if (realPassword) {
                                            $("#realPassword").val(realPassword);
                                            $("#password").val("******");
                                        } else {
                                            $("#password").val("");
                                        }
                                    });
                                    $("#username").keydown(function(event) {
                                        if (event.keyCode == 40) {
                                            $('#usernameHD').trigger('input.autocomplete');
                                            $('#usernameHD').focus();
                                            $('#usernameHD').click();
                                            return false;
                                        }
                                    }).focus(function() {
                                        if ($(this).val() == user["username"]) {
                                            this.focused = true;
                                            this.select();
                                        }
                                    }).mouseup(function() {
                                        if ($(this).val() == user["username"]) {
                                            if (this.focused) {
                                                this.focused = false;
                                                return false;
                                            }
                                        }
                                    });
                                    $("#password").focus(function() {
                                        if ($(this).val() == "******") {
                                            $(this).val("");
                                            $(this).data("default", "******");
                                        } else {
                                            this.focused = true;
                                            this.select();
                                        }
                                    }).mouseup(function() {
                                        if (this.focused) {
                                            this.focused = false;
                                            return false;
                                        }
                                    }).blur(function() {
                                        if ($(this).val() == "" && $(this).data("default")) {
                                            $(this).val("******");
                                            $(this).data("default", false);
                                        }
                                    });
                                    $("#check_code").focus(function() {
                                        this.focused = true;
                                        this.select();
                                    }).mouseup(function() {
                                        if (this.focused) {
                                            this.focused = false;
                                            return false;
                                        }
                                    });
                                    $("#checkImg,#checkA").click(function() {
                                        $('#checkImg').attr({src: '/check_code.php?' + new Date()});
                                        return false;
                                    });
                                    $("#findA").click(function() {
                                        chrome.sync.openUrl('/find?type=password', '');
                                        return false;
                                    });
                                    $("#regA").click(function() {
                                        chrome.sync.showRegDlg();
                                        return false;
                                    });
                                    user = chrome.sync.getLastActiveUserInfo();
                                    if (user != "") {
                                        user = eval("(" + user + ")");
                                        $("#username").val(user["username"]);
                                        $("#realPassword").val(user["password"]);
                                        if ($("#username").val() == "") {
                                            $("#username").focus();
                                        } else if ($("#realPassword").val() != "") {
                                            $("#password").val("******");
                                            $("#remember").attr("checked", "checked");
                                            $("#submitButton").focus();
                                        } else {
                                            $("#remember").removeAttr("checked");
                                            $("#password").focus();
                                        }
                                    } else {
                                        $("#username").focus();
                                    }
                                    users = chrome.sync.getLoginAllUserName();
                                    if (users != "") {
                                        users = eval(users);
                                    } else {
                                        users = [];
                                    }
                                    setAutoComplete();
                                });
        </script>
    </body>
</html>
