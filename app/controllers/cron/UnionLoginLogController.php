<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/7/19
 * Time: 13:29
 */

includeBase('CronController');
class UnionLoginLogController extends CronController
{

    private static $maxLine = 3000;   //最大插入条数

    private $startLine = '';   //开始读取行

    private $endLine = '';   //最后行

    private $msgStr = '';

    private $runTime = '';

    /**
     * 匹配登录IP
     * -
     * @param string $str 日志记录
     * @return string
     */
    private function getLoginIp($str)
    {
        $ip = '';
        if (preg_match('/ip:(.*) /iU', $str, $statusArr))
        {
            $ip = !empty($statusArr[1]) ? trim($statusArr[1]) : '';
        }
        return $ip;
    }

    /**
     * 获取登录状态
     * -
     * @param string $str 日志记录
     * @return int|string
     */
    private function getLoginStatus($str)
    {
        $loginStatus = 0;
        if (preg_match('/statusCode:(.*) /iU', $str, $codeArr))
        {
            $loginStatus = !empty($codeArr[1]) ? trim($codeArr[1]) : 0;
        }
        return $loginStatus;
    }

    /**
     * 获取请求时间
     * @param string $str 日志记录
     * @return false|string
     */
    private function getRequestDate($str)
    {
        $date = date('Y-m-d H:i:s');
        if (preg_match('/^\[(.*)\] /iU', $str, $dateArr))
        {
            $date = !empty($dateArr[1]) ? trim($dateArr[1]) : $date;
        }
        return $date;
    }

    /**
     * 解析request 数据
     * -
     * @param string $str 日志记录
     * @return string
     */
    private function getRequestData($str)
    {
        $request = '';
        if (preg_match('/request:(\{.*\}) /iU', $str, $requestArr))
        {
            $request = !empty($requestArr[1]) ? $requestArr[1] : '';
        }
        return $request;
    }

    /**
     * 开启定时
     * -
     * @param int $time 时间戳
     * @return void
     */
    private function startTimer($time)
    {
        $this->runTime = time() + $time;
        $this->setLogMsg("开启定时执行,时间为：" . date('Y-m-d H:i:s', $this->runTime));
    }

    /**
     * 判断是否过期
     * -
     * @return bool
     */
    private function isExpire()
    {
        if ($this->runTime >= time())
        {
            return true;
        }
        else
        {
            $this->setLogMsg("脚本允许已达定时时间，退出脚本。");
            return false;
        }
    }

    /**
     * 分析联合登录日志
     * -
     * @param string $type hour小时执行
     * @param string $time 需要加载的时间
     * @return void
     */
    public function actionIndex($type = '', $time = '')
    {
        $this->msgStr = str_repeat('-', 40) . PHP_EOL;
        $this->setLogMsg('开始执行');
        $now = strtotime("-1 hour");  //获取前一个小时
        if ($type == 'hour' && !empty($time))
        {
            $now = $time;
        }
//        elseif ($type == 'minute' && !empty($time))
//        {
//            $now = $now - ($time * 60);  //时间偏移
//        }
        $this->startTimer(3000); // 小时执行，最多允许执行50分钟
        $timeDir = date('Ymd', $now);
        $hour = date('H', $now); //指定执行小时
        $UnionLoginLogModel = loadModel('UnionLoginLog');
        $uFilter = new \Common\Utils\UFilter();
        //获取指定时间的文件偏移量
        $logInfo = $UnionLoginLogModel->getUnionLoginLog(date('Y-m-d H:00:00', $now));  //主要是断点重试加载数据
        $set_offset_size = !empty($logInfo['offset_size']) ? $logInfo['offset_size'] : 0; //文件偏移量
        $logDir = APPPATH . "/logs/" . $timeDir . "/unionLoginLog/";
        if (RUNMODE == 'development')
        {
            $logPath = $logDir . $hour . ".log";
        }
        else
        {
            $logPath = $logDir . $hour . ".logredis";
        }

        if (!file_exists($logPath))
        {
            $this->setLogMsg('文件不存在:' . "{$logPath}", true);
            echo 'faild';
            exit;
        }
        $this->setLogMsg("读取文件:{$logPath}");
        $fileHandle = fopen($logPath, 'r');
        $insertData = [];
        $readLine = 0;
        while (!feof($fileHandle))
        {
            //如果已过期则退出当次循环,继续执行下面
            if (!$this->isExpire())
            {
                break;
            }
            if (!empty($set_offset_size))
            {
                fseek($fileHandle, $set_offset_size);
                unset($set_offset_size);
            }
            //如果起始行没有值， 获取下当前的起始行
            if (empty($this->startLine))
            {
                $this->startLine = ftell($fileHandle);
            }
            $str = fgets($fileHandle);
            $offsetSize = $this->endLine = ftell($fileHandle);
            if (!empty(trim($str)))
            {
                $requestArr = $this->getRequestData($str);
                if (!empty($requestArr))
                {
                    $ip = $this->getLoginIp($str);
                    $loginStatus = $this->getLoginStatus($str);
                    $requestArr = json_decode($requestArr, true);
                    $mid = trim($requestArr['mid']);
                    $oldMid = trim($requestArr['oldMid']);
                    $authInfo = $uFilter->sourceFilter(trim($requestArr['source']));
                    //如果解析失败passid就为0
                    if (empty($authInfo))
                    {
                        $authInfo['i'] = 0;
                    }
                    $insertData[] = [
                        'passid' => $authInfo['i'],
                        'mid' => $mid,
                        'old_mid' => $oldMid,
                        'request_time' => $this->getRequestDate($str),
                        'login_ip' => $ip,
                        'login_status' => $loginStatus,
                        'offset_size' => $offsetSize,
                        'add_time' => date('Y-m-d H:i:s')
                    ];
                    ++$readLine;
                }
                else
                {
                    $this->setLogMsg('解析request失败:' . $offsetSize);
                    continue;
                }
            }
            //如果读取到指定行  进行db写入
            if ($readLine >= self::$maxLine)
            {
                $this->addLogToDb($UnionLoginLogModel, $insertData);
                $UnionLoginLogModel->delInstance();
                $this->startLine = '';
                $insertData = [];
                $this->setLogMsg("休息1秒钟");
                sleep(1);
                $readLine = 0;
            }
        }
        //文件读取完毕，并且还有数据未入库的情况
        if (!empty($insertData))
        {
            $this->addLogToDb($UnionLoginLogModel, $insertData);
            $this->setLogMsg('不满' . self::$maxLine . '行,只有' .count($insertData). '行,补全数据');
            $UnionLoginLogModel->delInstance();
        }
        $this->setLogMsg("解析文件结束", true);
        fclose($fileHandle);
        echo 'Success';
    }

    /**
     * log 入DB
     * -
     * @param object $UnionLoginLogModel model实例
     * @param array $insertData log
     * @return bool
     */
    private function addLogToDb($UnionLoginLogModel, $insertData)
    {
        $isAdd = $UnionLoginLogModel->addUnionLoginLog($insertData);
        if ($isAdd !== false)
        {
            $this->setLogMsg('插入数据成功,数据从' . $this->startLine . ' - ' . $this->endLine);
            return true;
        }
        else
        {
            $this->setLogMsg('插入数据失败,数据从' . $this->startLine . ' - ' . $this->endLine);
            return false;
        }
    }

    /**
     * 写log
     * -
     * @param string $msg 消息内容
     * @param bool $isWrite 是否写入
     * @return void
     */
    private function setLogMsg($msg, $isWrite = false)
    {
        $this->msgStr .= date('Y-m-d H:i:s') . ' ' . $msg . PHP_EOL;
        if ($isWrite)
        {
            $logDir = APPPATH . "/logs/" . date('Ymd') . "/unionLoginLog/";
            deepMkdir($logDir);
            $logErrorPath = $logDir . date('H') . '_run.log';
            file_put_contents($logErrorPath, $this->msgStr .  str_repeat('-', 40). PHP_EOL, FILE_APPEND);
        }
    }
}
