#!/bin/bash

cmdPhp="/opt/app/php/bin/php -d max_execution_time=60"

curDate=$(date +"%Y%m%d")
logDir="/opt/case/login.2345.com/app/logs/$curDate/cron"
if [ ! -d "$logDir" ];then
    mkdir -p $logDir
fi

cd /opt/case/login.2345.com/

#通知异步回调处理一分钟执行一次
$cmdPhp ./cron.php cron/NoticeHandle/index >> ${logDir}/NoticeCallback.log 2>&1 &

#通知重试1分钟执行一次
$cmdPhp ./cron.php cron/NoticeHandle/Repeat >> ${logDir}/NoticeRepeat.log 2>&1 &

#通知错误监控  1分钟执行一次
$cmdPhp ./cron.php cron/NoticeHandle/Warn >> ${logDir}/NoticeMonitor.log 2>&1 &

#外部登录注册 异步通知项目
$cmdPhp ./cron.php cron/ExternalPushLoginInfo/index >> ${logDir}/ExternalPushError.log 2>&1 &

#接口健康检查，异常报警
$cmdPhp ./cron.php cron/api/warn >> ${logDir}/Api_warn.log 2>&1 &

#登录日志处理
$cmdPhp ./cron.php cron/log/index >> ${logDir}/log_index.log 2>&1 &

#新库登录日志处理
$cmdPhp ./cron.php cron/log/LoginLogSetDb >> ${logDir}/log_new_index.log 2>&1 &