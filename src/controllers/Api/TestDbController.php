<?php
/**
 * Copyright (c)  上海二三四五网络科技有限公司
 * 文件名称：TestDbController.php
 * 摘    要：
 * 作    者：刘俊杰 <<EMAIL>>
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：06-07, 2017
 */


namespace Api;

use Controller;

class TestDbController extends Controller
{
    private $whiteIP = array(
        '127.0.0.1'       => 1,
        '************'    => 1,
        '************'    => 1,
        '************'    => 1,
        '**************'  => 1,
        '*************'   => 1,
        '*************'   => 1,
        '***************' => 1,
        '***************' => 1,
        '***************' => 1,
        '***************' => 1,
        '***************' => 1,
        '***************' => 1,
        '***************' => 1,
        '***************' => 1,
        '*************'   => 1,
        '*************'   => 1,
        //环科路新ip
        '*************'   => 1,
        '*************'   => 1,
        '*************'   => 1,
        '*************'   => 1,
        '*************'   => 1,
        '*************'   => 1,
        '*************'   => 1,
        '*************'   => 1,
        '*************'   => 1,
        '**************'  => 1,
        '**************'  => 1,
        '**************'  => 1,
        '**************'  => 1,
        '**************'  => 1,
        '**************'  => 1,
        '**************'  => 1,
        '**************'  => 1,
        '************' => 1,
        '**********' => 1,
        '**********' => 1,
        '**********' => 1,
        '**********' => 1,
        '**********' => 1,
        //无锡新机房出口IP-xuec
        '*************' => 1,
        '*************' => 1,
        '*************' => 1,
        '*************' => 1,
        '*************' => 1,
        '*************' => 1,
        '*************' => 1,
        '*************' => 1,
    );

    private $msg = array();
    private $redis;
    private $mysql;
    private $pdo;

    /**
     * @return int
     */
    public function actionIndex()
    {
        //$this->verifyIP();
        $this->verifyRedis();
        $this->actionTestDb57();
        //RUNMODE === 'production' && $this->actionTest57Slave();
//        $this->msg[] = 'x-forward-for:' . $_SERVER['HTTP_X_FORWARDED_FOR'] . '<br/>';
//        $this->msg[] = 'getIp:' . self::getIp();
        $this->sendResult();
    }

    /**
     * verify IP
     * User: panj
     * @return null
     */
    private function verifyIP()
    {
        if (!isset($this->whiteIP[self::getIp()]))
        {
            header("HTTP/1.1 403 Forbidden");
            header("Status: 403 Forbidden");
            exit("Access Denied !");
        }
    }

    /**
     * mysql testDb
     * User: panj
     * @return null
     */
    private function verifyMySQL()
    {
        $redisConf = \Config::get('database');
        $conf = $redisConf['smsp'];

        $dbname = 'smsp';
        try
        {
            $dsn = 'mysql:host=' . $conf['master']['host'] . ';port=' . $conf['master']['port'] . ';dbname=' . $dbname . ';charset=' . $conf['charset'];
            $username = $conf['username'];
            $password = $conf['password'];
            $options = array(
                \PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES ' . $conf['charset'],
                \PDO::ATTR_TIMEOUT            => 5,
            );
            $this->mysql = new \PDO($dsn, $username, $password, $options);

            $dbConfig = \Config::get("database");
            $this->pdo = \Octopus\PdoEx::getInstance("smsp", $dbConfig["smsp"]);
            $res = $this->pdo->find("SELECT * FROM project_list limit 1");

            if (!$res || !is_array($res) || (isset($res['pid']) && ($res['pid'] != 11)))
            {
                $this->msg[] = "mysql[master]查询失败";

                return;
            }
        }
        catch (\Exception $e)
        {
            $this->msg[] = $e->getMessage();
            $this->mysql = null;
        }
        if (!$this->mysql)
        {
            $this->msg[] = "mysql[master]连接失败";

            return;
        }
    }

    /**
     * mysql slave testDb
     * User: panj
     * @return null
     */
    private function verifySlave()
    {
        $conf = array(
            'slave'    => array(
                'host' => '*************',
                'port' => '3306',
            ),
            'username' => 'rc_status',
            'password' => 'rc_status',
            'charset'  => 'gbk',
        );
        $dbname = 'smsp';
        $mysql = null;
        try
        {
            $dsn = 'mysql:host=' . $conf['slave']['host'] . ';port=' . $conf['slave']['port'] . ';dbname=' . $dbname . ';charset=' . $conf['charset'];
            $username = $conf['username'];
            $password = $conf['password'];
            $options = array(
                \PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES ' . $conf['charset'],
                \PDO::ATTR_TIMEOUT            => 5,
            );
            $mysql = new \PDO($dsn, $username, $password, $options);
        }
        catch (\Exception $e)
        {
            $this->msg[] = "mysql[slave]连接失败: " . $e->getMessage();
            return;
        }
        try
        {
            $stmt = $mysql->prepare('show slave status;');
            $stmt->execute();
            $row = $stmt->fetch(\PDO::FETCH_ASSOC);

            if (empty($row))
            {
                $this->msg[] = "测试主从同步失败:show slave status 失败";
            }
            $nowHour = date('G');
            if ($nowHour > 6 && $row['Seconds_Behind_Master'] > 15)
            {
                $this->msg[] = "从库同步数据时间大于15秒，为: " . $row['Seconds_Behind_Master'];
                $this->msg[] = var_export($row, true);
            }
            elseif ($row["Slave_IO_Running"] !== "Yes")
            {
                $this->msg[] = "Slave_IO_Running值不为Yes, 为: " . $row["Slave_IO_Running"];
                $this->msg[] = var_export($row, true);
            }
            elseif ($row["Slave_SQL_Running"] !== "Yes")
            {
                $this->msg[] = "Slave_SQL_Running值不为Yes, 为: " . $row["Slave_SQL_Running"];
                $this->msg[] = var_export($row, true);
            }
        }
        catch (\PDOException $e)
        {
            $this->msg[] = '测试主从同步连接失败: ' . $e->getMessage();
        }
    }

    /**
     *
     * 测试mysql 5.7 读写功能
     *
     * User: panj
     * @return  null
     */
    public function actionVerifyDb()
    {
        $conf = array(
            'master'    => array(
/*env0023*/     'host' => $_ENV['DATABASE_MASTER_HOST'],
/*env0024*/     'port' => $_ENV['DATABASE_MASTER_PORT'],
            ),
/*env0025*/ 'username' => $_ENV['DATABASE_MASTER_USER'],
/*env0026*/ 'password' => $_ENV['DATABASE_MASTER_PASSWORD'],
/*env0027*/ 'charset'  => $_ENV['DATABASE_CHARSET'],
        );

        $dbname = 'smsp';
        try
        {
            $dsn = 'mysql:host=' . $conf['master']['host'] . ';port=' . $conf['master']['port'] . ';dbname=' . $dbname . ';charset=' . $conf['charset'];
            $username = $conf['username'];
            $password = $conf['password'];
            $options = array(
                \PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES ' . $conf['charset'],
                \PDO::ATTR_TIMEOUT            => 5,
            );
            $this->mysql = new \PDO($dsn, $username, $password, $options);

            $dbConfig = \Config::get("database");
            $this->pdo = \Octopus\PdoEx::getInstance("smsp", $dbConfig["smsp"]);
            $res = $this->pdo->find("SELECT * FROM project_list limit 1");

            if (!$res || !is_array($res) || (isset($res['pid']) && ($res['pid'] != 11)))
            {
                var_export($conf);
                echo "mysql[master]查询失败";

                return;
            }
            else
            {
                var_export($res);
            }
        }
        catch (\Exception $e)
        {
            echo $e->getMessage();
            $this->mysql = null;
        }
        if (!$this->mysql)
        {
            echo "mysql[master]连接失败";
            return;
        }
    }

    /**
     * 测试redis 4.0 读写功能
     * User: panj
     * @return null
     */
    public function actionVerifyTest()
    {
        $redisConf = \Config::get('redis');
        $conf = $redisConf['test'];
        try
        {
            $this->redis = new \Redis();
            $this->redis->connect($conf['master']['host'], $conf['master']['port'], 5);
            $this->redis->auth($conf['auth']);
        }
        catch (\Exception $e)
        {
            echo $e->getMessage();
            $this->redis = null;
        }
        if (!$this->redis)
        {
            echo  $conf['master']['host']. "redis连接失败 <br/>";

            return;
        }
        $now = time();
        $this->redis->set("smsp:testDB:testConn", $now);
        if ($this->redis->get("smsp:testDB:testConn") != $now)
        {
            echo "redis读写校验失败<br/>";
        }
        else
        {
            echo $this->redis->get("smsp:testDB:testConn");
            echo "redis set and get success <br/>";
        }

        $cacheKey = "smsp:testDB:testHash";
        $hashKey = 'justTime';

        $this->redis->hSet($cacheKey, $hashKey, $now);

        if ($this->redis->hGet($cacheKey, $hashKey) != $now)
        {
            echo $conf['master']['host'] . "redis hash 读写校验失败</br>";
        }
        else
        {
            echo $this->redis->hGet($cacheKey, $hashKey);
            echo "redis hSet and hGet success";
        }
    }


    /**
     * redis testDB
     * User: panj
     * @return null
     */
    private function verifyRedis()
    {
        $redisConf = \Config::get('redis');
        $conf = $redisConf['default'];
        try
        {
            $this->redis = new \Redis();
            $this->redis->connect($conf['master']['host'], $conf['master']['port'], 5);
            $this->redis->auth($conf['auth']);
        }
        catch (\Exception $e)
        {
            $this->msg[] = $e->getMessage();
            $this->redis = null;
        }
        if (!$this->redis)
        {
            $this->msg[] = "redis连接失败";

            return;
        }
        $now = time();
        $this->redis->set("smsp:testDB:testConn", $now);
        if ($this->redis->get("smsp:testDB:testConn") != $now)
        {
            $this->msg[] = "redis读写校验失败";
        }
    }

    /**
     * send result
     * User: panj
     * @return null
     */
    private function sendResult()
    {
        if (sizeof($this->msg) != 0)
        {
            header("HTTP/1.1 404 Not Found");
            header("Status: 404 Not Found");
        }
        else
        {
            echo 'OK';
        }
        foreach ($this->msg as $errorMsg)
        {
            echo '<p>' . $errorMsg . "</p>\n";
        }
        if (sizeof($this->msg) != 0)
        {
            echo "<br/><p>中台业务开发</p>\n";
            echo "<p>联系人：吕泉峰(<EMAIL>, 15921260120)、杜海明(<EMAIL>, 18321690870)、薛超(<EMAIL>，13816081342)</p>\n";
        }
    }

    /**
     * get Ip
     * User: panj
     * @return mixed
     */
    private static function getIp()
    {
        if (!empty($_SERVER['HTTP_CLIENT_IP']))
        {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        }
        elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))
        {
            // to check ip is pass from proxy
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
            if (strpos($ip, ",") !== false)
            {
                $ips = explode(",", $ip);
                $ip = $ips[0];
            }
        }
        elseif (!empty($_SERVER['HTTP_X_FORWARDED']))
        {
            $ip = $_SERVER['HTTP_X_FORWARDED'];
        }
        elseif (!empty($_SERVER['HTTP_X_CLUSTER_CLIENT_IP']))
        {
            $ip = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
        }
        elseif (!empty($_SERVER['HTTP_FORWARDED_FOR']))
        {
            $ip = $_SERVER['HTTP_FORWARDED_FOR'];
        }
        elseif (!empty($_SERVER['HTTP_FORWARDED']))
        {
            $ip = $_SERVER['HTTP_FORWARDED'];
        }
        else
        {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        return $ip;
    }

    /**
     *
     * 测试mysql 5.7 读写功能
     *
     * User: panj
     * @return  null
     */
    public function actionTestDb57()
    {
        $conf = array(
            'master'    => array(
/*env0028*/     'host' => $_ENV['DATABASE_MASTER_HOST'],
/*env0029*/     'port' => $_ENV['DATABASE_MASTER_PORT'],
            ),
            'slave'    => array(
/*env0030*/     'host' => $_ENV['DATABASE_SLAVE_HOST'],
/*env0031*/     'port' => $_ENV['DATABASE_SLAVE_PORT'],
            ),
/*env0032*/ 'username' => $_ENV['DATABASE_SLAVE_USER'],
/*env0033*/ 'password' => $_ENV['DATABASE_SLAVE_PASSWORD'],
/*env0034*/ 'charset'  => $_ENV['DATABASE_CHARSET'],
            'dbname'  => $_ENV['DATABASE_MASTER_DBNAME'],
        );

        $dbname = 'smsp';
        try
        {
            $dsn = 'mysql:host=' . $conf['master']['host'] . ';port=' . $conf['master']['port'] . ';dbname=' . $dbname . ';charset=' . $conf['charset'];
            $username = $conf['username'];
            $password = $conf['password'];
            $options = array(
                \PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES ' . $conf['charset'],
                \PDO::ATTR_TIMEOUT            => 5,
            );
            $this->mysql = new \PDO($dsn, $username, $password, $options);

            $this->pdo = \Octopus\PdoEx::getInstance("smsp", $conf);
            $res = $this->pdo->find("SELECT count(*) FROM sms_logs_archive limit 1");

            if (!$res || !is_array($res))
            {
                $this->msg[] = "mysql[master]查询失败";
                return;
            }
            else
            {
                echo '连接成功<br/>';
                var_export($res);
            }
        }
        catch (\Exception $e)
        {
            $this->msg[] = $e->getMessage();
            $this->mysql = null;
        }
        if (!$this->mysql)
        {
            $this->msg[] = $dsn . "mysql[master]连接失败";
            return;
        }
    }

    /**
     * User: panj
     * 测试mysql5.7主从
     * @return null
     */
    public function actionTest57Slave()
    {
        $conf = array(
            'slave'    => array(
/*env0035*/     'host' => $_ENV['DATABASE_SLAVE_HOST'],
/*env0036*/     'port' => $_ENV['DATABASE_SLAVE_PORT'],
            ),
/*env0037*/ 'username' => $_ENV['DATABASE_SHOW_STATUS_USER'],
/*env0038*/ 'password' => $_ENV['DATABASE_SHOW_STATUS_PASSWORD'],
/*env0039*/ 'charset'  => $_ENV['DATABASE_CHARSET'],
        );
        $dbname = 'smsp';
        $mysql = null;
        try
        {
            $dsn = 'mysql:host=' . $conf['slave']['host'] . ';port=' . $conf['slave']['port'];
            $username = $conf['username'];
            $password = $conf['password'];
            $mysql = new \PDO($dsn, $username, $password);
        }
        catch (\Exception $e)
        {
            $this->msg[] = "mysql[slave]连接失败: " . $e->getMessage();
            return;
        }
        try
        {
            $stmt = $mysql->prepare('show slave status;');
            $stmt->execute();
            $row = $stmt->fetch(\PDO::FETCH_ASSOC);

            if (empty($row))
            {
                $this->msg[] = "测试主从同步失败:show slave status 失败";
            }
            $nowHour = date('G');
            if ($nowHour > 6 && $row['Seconds_Behind_Master'] > 15)
            {
                $this->msg[] = "从库同步数据时间大于15秒，为: " . $row['Seconds_Behind_Master'];
                $this->msg[] = var_export($row, true);
            }
            elseif ($row["Slave_IO_Running"] !== "Yes")
            {
                $this->msg[] = "Slave_IO_Running值不为Yes, 为: " . $row["Slave_IO_Running"];
                $this->msg[] = var_export($row, true);
            }
            elseif ($row["Slave_SQL_Running"] !== "Yes")
            {
                $this->msg[] = "Slave_SQL_Running值不为Yes, 为: " . $row["Slave_SQL_Running"];
                $this->msg[] = var_export($row, true);
            }
        }
        catch (\PDOException $e)
        {
            $this->msg[] = '测试主从同步连接失败: ' . $e->getMessage();
        }
    }

    /**
     * 测试227写功能
     * User: panj
     * @return null
     */
    public function actionTest227Write()
    {
        $conf = array(
            'master'    => array(
/*env0040*/     'host' => $_ENV['DATABASE_MASTER_HOST'],
/*env0041*/     'port' => $_ENV['DATABASE_MASTER_PORT'],
            ),
            'slave'    => array(
/*env0042*/     'host' => $_ENV['DATABASE_SLAVE_HOST'],
/*env0043*/     'port' => $_ENV['DATABASE_SLAVE_PORT'],
            ),
/*env0044*/ 'username' => $_ENV['DATABASE_SLAVE_USER'],
/*env0045*/ 'password' => $_ENV['DATABASE_SLAVE_PASSWORD'],
/*env0046*/ 'charset'  => $_ENV['DATABASE_CHARSET'],
        );

        $dbname = 'smsp';
        try
        {
            $this->pdo = \Octopus\PdoEx::getInstance($dbname, $conf);
            $sql = "CREATE TABLE IF NOT EXISTS test227 LIKE `voice_logs_list`";
            $res = $this->pdo->query($sql);
            var_export($res);
            $data = [
                'log_id' => 1,
                'add_time' => date('Y-m-d H:i:s')
            ];
            $res2 = $this->pdo->insert('test227', $data);
            var_export($res2);
            sleep(1);
            $sql = 'select * from test227 where 1';
            $row = $this->pdo->query($sql);

            var_export($row);
        }
        catch (\Exception $e)
        {
            echo $e->getMessage();
            $this->mysql = null;
        }
    }
}
