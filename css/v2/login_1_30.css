@charset "gb2312";
/**
* @name : login
* @update : 2015.01.30
* <AUTHOR> liux
**/
.g-login .form-item, .g-reg .form-item, .g-reg .form-con, .uc_loginreg .uc_loginreg_col, .bind-panel { *zoom: 1; }
.g-login .form-item:after, .g-reg .form-item:after, .g-reg .form-con:after, .uc_loginreg .uc_loginreg_col:after, .bind-panel:after { content: "."; display: block; height: 0; clear: both; visibility: hidden; line-height: 0; font-size: 0; }

/*modules login*/
.g-login .icon-user, .g-login .icon-pwd, .g-login .i-reterror, .g-login .i-retok, .g-login .btn-qq i, .g-login .btn-wb i { background-image: url(../../images/v2/loginreg.png); background-repeat: no-repeat; }

.g-login .login-hd, .g-login .login-bd { padding: 0 20px; }
.g-login .login-hd { height: 50px; line-height: 50px; }
.g-login .login-hd .titname { font-weight: normal; font-size: 18px; font-family: \5FAE\8F6F\96C5\9ED1; }
.g-login .icon-user, .g-login .icon-pwd { position: absolute; left: 10px; top: 6px; width: 22px; height: 22px; overflow: hidden; }
.g-login .icon-user { background-position: -28px -61px; }
.g-login .icon-pwd { background-position: -28px -99px; }
.g-login .ipt-txt { width: 250px; height: 18px; padding: 7px 0 7px 10px; border: 1px solid #c6cfd6; background-color: #ffffff; vertical-align: top; font-family: Arial,SimSun; color: #333333; -moz-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; -webkit-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; *margin-top: -1px; *margin-bottom: -1px; }
.g-login .ipt-txt-defa { color: #999999; }
.g-login .ipt-txt-error { border-color: #fc4343; -moz-transition: all 0.5s ease 0s; -webkit-transition: all 0.5s ease 0s; transition: all 0.5s ease 0s; }
.g-login .ipt-txt-current { border-color: #499fe6; }
.g-login .ipt-txt-user { padding-left: 32px; }
.g-login .ipt-txt-pwd { padding-left: 32px; }
.g-login .ipt-txt-yzm { width: 110px; }
.g-login .ipt-tips { position: absolute; height: 34px; line-height: 34px; left: 34px; top: 0px; color: #999999; white-space: nowrap; }
.g-login .ipt-check { margin-right: 5px; width: 13px; height: 13px; overflow: hidden; vertical-align: -2px; *vertical-align: middle; }
.g-login .form-item { position: relative; margin-bottom: 16px; }
.g-login .trig-link { color: #2371c8; }
.g-login .trig-link:hover { color: #ff3300; }
.g-login .i-reterror, .g-login .i-retok { float: left; display: inline; margin: 2px 7px 0 0; width: 13px; height: 13px; overflow: hidden; }
.g-login .i-reterror { background-position: -23px 0; }
.g-login .form-tips { margin-top: -5px; height: 18px; overflow: hidden; }
.g-login .form-tips-error { color: #fc4343; }
.g-login .form-item-yzm .img-yzm { margin: 0 10px; cursor: pointer; }
.g-login .form-item-yzm .trig-link { position: relative; top: 7px; }
.g-login .form-item-fg { margin: 8px 0; text-align: right; }
.g-login .form-item-fg .auto { float: left; color: #888888; }
.g-login .form-item-go { margin-top: 10px; padding-bottom: 10px; text-align: right; color: #999999; }
.g-login .btn-submit { display: block; width: 284px; height: 36px; text-align: center; line-height: 36px; background-color: #3990d7; color: #ffffff !important; font-size: 16px; font-family: \5FAE\8F6F\96C5\9ED1; outline: none; border: 0 none; cursor: pointer; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; -moz-box-shadow: 0 -2px 2px #3383cf inset; -webkit-box-shadow: 0 -2px 2px #3383cf inset; box-shadow: 0 -2px 2px #3383cf inset; }
.g-login .btn-submit:hover { background-color: #1C7DCD; text-decoration: none; -moz-box-shadow: 0 -2px 2px #156cbc inset; -webkit-box-shadow: 0 -2px 2px #156cbc inset; box-shadow: 0 -2px 2px #156cbc inset; }
.g-login .btn-qq, .g-login .btn-wb { position: relative; *zoom: 1; display: inline-block; height: 28px; line-height: 28px; margin-right: 12px; vertical-align: middle; border: 1px solid #d5d5d5; cursor: pointer; -moz-border-radius: 2px; -webkit-border-radius: 2px; border-radius: 2px; background-image: url("about:block"); }
.g-login .btn-qq i, .g-login .btn-wb i { position: absolute; top: 5px; width: 18px; height: 18px; overflow: hidden; }
.g-login .btn-qq { width: 100px; padding-left: 36px; }
.g-login .btn-qq i { left: 10px; background-position: 0 -23px; }
.g-login .btn-wb { width: 30px; }
.g-login .btn-wb i { left: 6px; background-position: -28px -23px; }
.g-login .form-item-other { padding-bottom: 18px; border-top: 1px dashed #e3e7ea; }
.g-login .form-item-other .titname { height: 32px; line-height: 32px; color: #666666; }

/*modules reg*/
.g-reg .i-reterror, .g-reg .i-retok { background-image: url(../../images/v2/loginreg.png); background-repeat: no-repeat; }

.g-reg .c-style { color: #ff6600; }
.g-reg .form-field { position: absolute; left: 0; width: 120px; height: 34px; line-height: 34px; text-align: right; font-size: 14px; }
.g-reg .ipt-txt { width: 235px; height: 18px; padding: 7px 25px 7px 10px; border: 1px solid #c6cfd6; vertical-align: middle; background-color: #ffffff; font-family: Arial,SimSun; color: #333333; -moz-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; -webkit-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; }
.g-reg .ipt-txt-error { border-color: #fc4343; -moz-transition: border-color 0.5s ease 0s; -webkit-transition: border-color 0.5s ease 0s; transition: border-color 0.5s ease 0s; }
.g-reg .ipt-txt-current { border-color: #499fe6; }
.g-reg .ipt-txt-yzm { width: 85px; }
.g-reg .ipt-check { margin-right: 5px; width: 13px; height: 13px; overflow: hidden; vertical-align: -2px; *vertical-align: middle; }
.g-reg .i-reterror, .g-reg .i-retok { display: inline-block; width: 13px; height: 13px; overflow: hidden; }
.g-reg .i-reterror { background-position: -23px 0; }
.g-reg .i-retok { background-position: 0 0; }
.g-reg .form-item { position: relative; *zoom: 1; padding: 0 0 28px 124px; }
.g-reg .form-con { position: relative; *zoom: 1; }
.g-reg .form-con .i-retok { position: absolute; top: 11px; right: 12px; }
.g-reg .trig-link { color: #2371c8; }
.g-reg .trig-link:hover { color: #ff3300; }
.g-reg .form-tips { position: absolute; bottom: 4px; height: 24px; line-height: 24px; overflow: hidden; color: #999999; white-space: nowrap; }
.g-reg .form-tips-error { color: #fc4343; }
.g-reg .form-tips-ok { color: #4dc214; }
.g-reg .form-tips-voice { position: relative; margin: 4px 0 -18px; color: #999999; height: 32px; line-height: 16px; overflow: hidden; *zoom: 1; }
.g-reg .form-tips-voice .pos-r { position: absolute; right: 0; bottom: 0; }
.g-reg .form-pwd-tips { color: #ff3f00; display: inline-block; margin-left: 10px; height: 24px; }
.g-reg .form-pwd-tips i { float: left; margin: 9px 2px 0; width: 17px; height: 4px; overflow: hidden; border: 1px solid #ff3f00; }
.g-reg .form-pwd-tips i.on { background-color: #ff3f00; }
.g-reg .form-pwd-tips em { margin-left: 5px; }
.g-reg .form-pwd-tips-2 { color: #ffc600; }
.g-reg .form-pwd-tips-2 i { border-color: #ffc600; }
.g-reg .form-pwd-tips-2 i.on { background-color: #ffc600; }
.g-reg .form-pwd-tips-3 { color: #4dc214; }
.g-reg .form-pwd-tips-3 i { border-color: #4dc214; }
.g-reg .form-pwd-tips-3 i.on { background-color: #4dc214; }
.g-reg .form-item-yzm .img-yzm { display: inline-block; margin: 0 10px; cursor: pointer; vertical-align: middle; }
.g-reg .form-item-yzm .trig-link { display: inline-block; margin-top: 7px; }
.g-reg .form-item-yzm .btn-yzm, .g-reg .form-item-yzm .btn-yzmed { margin-left: 8px; display: inline-block; width: 140px; height: 32px; line-height: 32px; text-align: center; font-size: 14px; background-color: #f6f6f6; border: 1px solid #c6ced6; vertical-align: middle; }
.g-reg .form-item-yzm .btn-yzm { color: #666666; }
.g-reg .form-item-yzm .btn-yzm:hover { background-color: #ffffff; text-decoration: none; }
.g-reg .form-item-yzm .btn-yzmed { color: #999999; }
.g-reg .form-item-yzmphone .form-tips { position: static; }
.g-reg .form-item-fg { padding-bottom: 0; margin: 0 0 8px; color: #666666; }
.g-reg .form-item-fg .auto { float: left; color: #888888; }
.g-reg .form-item-fg .form-tips { position: static; }
.g-reg .form-item-btn { padding-bottom: 10px; }
.g-reg .form-item-go { text-align: right; color: #999999; }
.g-reg .btn-submit { display: block; width: 272px; height: 36px; text-align: center; line-height: 36px; background-color: #3990d7; color: #ffffff !important; font-size: 16px; font-family: \5FAE\8F6F\96C5\9ED1; outline: none; border: 0 none; cursor: pointer; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; -moz-box-shadow: 0 -2px 2px #3383cf inset; -webkit-box-shadow: 0 -2px 2px #3383cf inset; box-shadow: 0 -2px 2px #3383cf inset; }
.g-reg .btn-submit:hover { background-color: #1C7DCD; text-decoration: none; -moz-box-shadow: 0 -2px 2px #156cbc inset; -webkit-box-shadow: 0 -2px 2px #156cbc inset; box-shadow: 0 -2px 2px #156cbc inset; }

/*modules pop-logreg*/
.g-pop-logreg .logreg-close { background-image: url(../../images/v2/loginreg.png); background-repeat: no-repeat; }

.g-pop-logreg { display: none; width: 324px; z-index: 999; position: absolute; top: 50%; left: 50%; margin-left: -162px; background-color: #ffffff; border: 1px solid #e7e7e7; }
.g-pop-logreg .logreg-hd { position: relative; padding: 0 20px; height: 43px; line-height: 43px; border-bottom: 1px solid #e7e7e7; *zoom: 1; }
.g-pop-logreg .logreg-tit { color: #333333; font-size: 18px; font-family: "Microsoft YaHei"; font-weight: normal; }
.g-pop-logreg .logreg-close { position: absolute; top: 50%; margin-top: -6px; right: 12px; width: 12px; height: 12px; overflow: hidden; background-position: 0 -61px; }
.g-pop-logreg .logreg-close:hover { background-position: 0 -83px; }
.g-pop-logreg .logreg-bd { padding: 0 20px; *zoom: 1; }
.g-pop-logreg .trig-link { color: #2371c8; }
.g-pop-logreg .trig-link:hover { color: #ff3300; }
.g-pop-logreg .wx_tips { padding: 14px 0; line-height: 24px; font-size: 14px; }
.g-pop-logreg .g-login { margin-top: 18px; }
.g-pop-logreg .g-login .ipt-txt { width: 250px; }
.g-pop-logreg .g-login .ipt-txt-yzm { width: 110px; }
.g-pop-logreg .g-reg { margin-top: 18px; margin-left: 20px; margin-right: 32px; *zoom: 1; }
.g-pop-logreg .g-reg .ipt-txt { width: 235px; }
.g-pop-logreg .g-reg .ipt-txt-yzm { width: 95px; }

.g-mask { display: none; z-index: 998; position: absolute; top: 0; left: 0; background-color: #000000; width: 100%; height: 100%; opacity: 0.4; filter: alpha(opacity=40); }

input[type="button"]::-moz-focus-inner, input[type="reset"]::-moz-focus-inner, input[type="submit"]::-moz-focus-inner, input[type="file"] > input[type="button"]::-moz-focus-inner, button::-moz-focus-inner { border: 0 none; padding: 0; }

.pheader { margin-bottom: 0; border-bottom: 1px solid #e7e7e7; }

.pfooter { border-top: 2px solid #f3f3f3; }

.uc_loginreg { background-color: #f5fbfd; }
.uc_loginreg .uc_loginreg_col { margin: 0 auto; padding: 20px 0; width: 1000px; }
.uc_loginreg .uc_login { height: 404px; background: url(../../images/v2/login_welcome.jpg) no-repeat left 20px; }
.uc_loginreg .uc_login .trig_gopro { float: left; display: inline; margin: 10px 0 0 70px; width: 500px; height: 360px; }
.uc_loginreg .uc_login .g-login { float: right; width: 324px; border: 1px solid #e1e9ec; background-color: #ffffff; }
.uc_loginreg .g-reg { padding-top: 20px; margin: 0 auto; width: 398px; }

.uc_login_trans { height: 404px; }

.loginloading { width: 16px; height: 16px; margin-right: 5px; display: inline-block; vertical-align: middle; background: url(../../images/v2/loading.gif) no-repeat; }

.trans-tit { margin: 140px 0 10px; text-align: center; font-size: 14px; }

.trans-tips { text-align: center; color: #666666; }
.trans-tips a { color: #188ad3; text-decoration: underline; }
.trans-tips a:hover { color: #ff3300; }

.uc_login_bind { height: 404px; }

.bind-wel { margin-left: 150px; color: #666666; }

.bind-panel { margin-top: 50px; }
.bind-panel .btn-LblueA { display: block; width: 284px; height: 36px; text-align: center; line-height: 36px; background-color: #3990d7; color: #ffffff !important; font-size: 18px; font-family: \5FAE\8F6F\96C5\9ED1; outline: none; border: 0 none; cursor: pointer; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; -moz-box-shadow: 0 -2px 2px #3383cf inset; -webkit-box-shadow: 0 -2px 2px #3383cf inset; box-shadow: 0 -2px 2px #3383cf inset; }
.bind-panel .btn-LblueA:hover { background-color: #1C7DCD; text-decoration: none; -moz-box-shadow: 0 -2px 2px #156cbc inset; -webkit-box-shadow: 0 -2px 2px #156cbc inset; box-shadow: 0 -2px 2px #156cbc inset; }
.bind-panel .btn-LblueB { display: block; width: 284px; height: 36px; text-align: center; line-height: 36px; background-color: #CFEAFF; color: #3a90d7 !important; font-size: 18px; font-family: \5FAE\8F6F\96C5\9ED1; outline: none; border: 0 none; cursor: pointer; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; -moz-box-shadow: 0 -2px 2px #9fcbf0 inset; -webkit-box-shadow: 0 -2px 2px #9fcbf0 inset; box-shadow: 0 -2px 2px #9fcbf0 inset; }
.bind-panel .btn-LblueB:hover { background-color: #AFDBFF; text-decoration: none; -moz-box-shadow: 0 -2px 2px #6eade1 inset; -webkit-box-shadow: 0 -2px 2px #6eade1 inset; box-shadow: 0 -2px 2px #6eade1 inset; }
.bind-panel .bind-hasArea { float: left; display: inline; width: 286px; height: 200px; margin-left: 150px; padding-right: 67px; border-right: 1px solid #f0f1f2; }
.bind-panel .bind-hasArea .field { margin-bottom: 10px; color: #333333; font-size: 14px; font-weight: bold; }
.bind-panel .bind-nohasArea { float: right; width: 286px; display: inline; margin-right: 150px; }
.bind-panel .bind-nohasArea .field { margin-bottom: 10px; color: #333333; font-size: 14px; font-weight: bold; }
.bind-panel .g-login .form-item-btn { margin-top: 6px; }
