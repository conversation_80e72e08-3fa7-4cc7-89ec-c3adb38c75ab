<?php

/**
 * 外部调用，发送短信，以后其他部门发送短信，请统一调用这个接口
 * <AUTHOR> 2014/3/5
 */

namespace Api;

use Controller;
use Service\Func\RequestFunc;
use Service\Func\ServiceFunc;
use Service\Handle\RequestHandle;

class SmsController extends Controller
{
    /**
     * SmsController constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 相同内容短信发送和群发， 手机号 逗号 分隔
     * @return bool
     */
    public function actionSend()
    {
        $funcAction = \FuncAction::getInstance();
        $funcAction->setHttpError();
        $funcAction->setRequest();

        $phone = $this->Filter->post('phone');  //手机号
        $msg = (string)($this->Filter->post('msg'));//短信内容
        $smsType = (int)$this->Filter->post('smsType');//sms短信类型
        $pid = (int)$this->Filter->post('pid');//项目id
        $clientIp = $this->Filter->post('clientIp');//客户端ip
        $positionId = (int)$this->Filter->post('positionId');//项目位置
        $passid = (int)$this->Filter->post('passid');//用户id
        $mid = $this->Filter->post('mid'); //标记
        $appType = (int)$this->Filter->post('appType'); //应用类型
        $appType = empty($appType) ? 1 : $appType;  //1 短信 2 语音

        $voiceType = (int)$this->Filter->post('voiceType'); //语音类型
        $voiceTpl = (int)$this->Filter->post('voiceTpl'); //语音模板
        $useQueue = (int)$this->Filter->post('useQueue');
        $useQueue = empty($useQueue) ? 0 : $useQueue;

        /**********必填参数**********/

        $phone = preg_replace("/[^,0-9]/", ",", $phone);
        $phoneList = explode(",", $phone);
        $phoneList = array_filter($phoneList);
        $phoneList = array_merge($phoneList);
        $isMulti = count($phoneList) > 1 ? 1 : 0;
        $phoneList = array_unique($phoneList);
        $msg = \EncodingAction::transcoding($msg);

        if (ServiceFunc::useSwoolePid($pid))
        {
            $requestSwooleData = [
                'phone' => $phone,  //手机号码
                'msg' => $msg,   //需要发送的消息
                'smsType' => $smsType,  //短信类型
                'pid' => $pid,    //项目ID
                'clientIp' => $clientIp,   //客户端的ip
                'positionId' => $positionId,  //位置id
                'passId' => $passid,
                'mid' => $mid,
                'appType' => $appType,
                'voiceType' => $voiceType,
                'voiceTpl' => $voiceTpl,
                'isMulti' => $isMulti,   //多发状态
                'phoneList' => $phoneList,
            ];
            $requestSwooleData = array_merge($requestSwooleData, ['sendType' => RequestFunc::SMS_API_SEND_TYPE_COMMON]);
            $response = (new RequestHandle())->run($requestSwooleData);
            return \MsgAction::returnJsonData(array('status' => $response['status'], 'msg' => $response['msg'], 'data' => $response['data']));
        }

        // 手机号数量
        if (count($phoneList) > 5000)
        {
            return \MsgAction::returnJsonData(array('status' => -31, 'msg' => \SmsAction::$multiErrStr[-31]));
        }

        // 短信内容长度，字符1000以内
        if (mb_strlen($msg, "GBK") > 1000)
        {
            return \MsgAction::returnJsonData(array('status' => -25, 'msg' => '短信内容超出限制!',));
        }

        $requiredParamList = array(
            'phone'    => $phoneList,
            'msg'      => $msg,
            'smsType'  => $smsType,
            'pid'      => $pid,
            'clientIp' => $clientIp,
            'appType'  => $appType,
        );
        $additionalParamList = array(
            'send_id'    => 1,//普通发送方式
            'positionId' => $positionId,
            'passid'     => $passid,
            'mid'        => $mid,
            'voiceType'  => $voiceType,
            'voiceTpl'   => $voiceTpl,
            'multi'      => $isMulti,
        );
        /**********必填参数**********/

        /**********正式提交数据**********/
        $arrParam = array_merge($requiredParamList, $additionalParamList);
        $getRedisAction = \GetRedisAction::getInstance();

        //使用队列方式发送，建议当且仅当高并发场景时使用
        if (0 && $useQueue)
        {
            $additionalParamList['send_id'] = 11;//队列普通发送
            $additionalParamList['serverIp'] = get_client_ip();
            $additionalParamList['sendTime'] = date("Y-m-d H:i:s");
            $ticketId = $getRedisAction->add2Queue(array($requiredParamList, $additionalParamList));
            $errorData = array(
                'status' => 3,
                'msg'    => '已入队列',
                'data'   => array('ticketId' => $ticketId),
            );
            return \MsgAction::returnJsonData($errorData);
        }

        $apiSmsSenderAction = \ApiSmsSenderAction::getInstance();
        $apiSmsSenderAction->setSubmitData($arrParam);
        /**********正式提交数据**********/

        $isLegal = $apiSmsSenderAction->checkParams($requiredParamList);
        if ($isLegal === true)
        {
            $getRedisAction->incrMonitor(count($phoneList), 'send', $smsType, $pid);

            $insertData = array();

            //sessionId = 定义一个唯一的sessionid； 用来表示该群发为同一个会话.
            $sessionId = uniqid();
            $column = array(
                "phone",
                "encrypt_phone",
                "text",
                "sessionid",
                "type",
                "send_time",
                "send_status",
                "send_response",
                "passid",
                "pid",
                'business_id',
                'client_ip',
                'server_ip',
            );
            foreach ($phoneList as $phone)
            {
                $insertData[$phone] = array(
                    $phone,
                    $phone,
                    $msg,
                    $sessionId,
                    $smsType,
                    date("Y-m-d H:i:s"),
                    -22,
                    '等待发送',
                    $passid,
                    $pid,
                    $positionId,
                    $clientIp,
                    get_client_ip(),
                );
            }
            $smsModel = \SmsModel::getInstance();
            $phoneLogIds = $smsModel->setSmsLogs($insertData, $column);
            $smsModel->addSmsLog2Redis($insertData, $column);
            if ($phoneLogIds)
            {
                $arrParam['phoneLogIds'] = $phoneLogIds;
                $arrParam['sessionId'] = $sessionId;
                $ret = $apiSmsSenderAction->apiSend($arrParam);
                return $ret;
            }
            else
            {
                return \MsgAction::returnJsonData(array('status' => -21, 'msg' => \SmsAction::$errStr[-21]));
            }
        }
        else
        {
            $errorData = array(
                'status' => $isLegal['status'],
                'msg'    => $isLegal['msg'],
            );

            return \MsgAction::returnJsonData($errorData);
        }
    }

    /**
     * 不同内容群发短信
     * @return bool
     */
    public function actionMultiMsgSend()
    {
        $funcAction = \FuncAction::getInstance();
        $funcAction->setHttpError();

        $phoneMsg = (string)($this->Filter->post('phoneMsg'));
        /*
         * 处理特殊字符,这段代码有点奇怪，历史原因
         * substr_count('屯R','?');//1
         * substr_count('?','R');//1
         * substr_count('?','|');//1
         *
         */
        if (substr_count($phoneMsg, ",") * 2 + 2 != substr_count($phoneMsg, "|"))
        {
            \MsgAction::returnJsonData(array('status' => -33, 'msg' => \SmsAction::$multiErrStr[-33]));

            return true;
        }
        $phoneMsg = \EncodingAction::transcoding($phoneMsg, 'gbk');
        $pid = (int)$this->Filter->post('pid');
        $smsType = (int)$this->Filter->post('smsType');
        $positionId = (int)$this->Filter->post('positionId');
        $clientIp = $this->Filter->post('clientIp');

        if (ServiceFunc::useSwoolePid($pid))
        {
            $requestSwooleData = [
                'phoneMsg'   => $phoneMsg,  //phoneMsg字段需要传入消息编号、手机号、内容这三项内容，使用|字符分隔；短信之间使用半角逗号,分隔
                'smsType'    => $smsType,  //短信类型
                'pid'        => $pid,    //项目ID
                'clientIp'   => $clientIp,   //客户端的ip
                'positionId' => $positionId,  //位置id
            ];
            $requestSwooleData = array_merge($requestSwooleData, ['sendType' => RequestFunc::SMS_API_SEND_TYPE_MULTI]);
            $response = (new RequestHandle())->run($requestSwooleData);
            return \MsgAction::returnJsonData(array('status' => $response['status'], 'msg' => $response['msg'], 'data' => $response['data']));
        }

        // 检查 数据
        // 注，此处client_ip有误，应为clientIp，但是不知道是否有业务没有提交，只能不改
        $requiredParamList = array(
            "smsType"   => $smsType,
            "pid"       => $pid,
            'client_ip' => $clientIp,
        );
        $apiSmsMultiSenderAction = \ApiSmsMultiSenderAction::getInstance();
        $isLegal = $apiSmsMultiSenderAction->checkParams($requiredParamList);
        if ($isLegal === true)
        {
            $multiContent = array();
            $phoneMsgArr = explode(",", $phoneMsg);
            $phoneList = array();


            if (count($phoneMsgArr) > 100)
            {
                \MsgAction::returnJsonData(array('status' => -31, 'msg' => \SmsAction::$multiErrStr[-31]));

                return true;
            }

            foreach ($phoneMsgArr as $value)
            {
                list($packageId, $phone, $msg) = explode("|", $value);

                if (mb_strlen($msg, "UTF-8") > 1000)
                {
                    $errorData = array(
                        'status' => "-25",
                        'msg'    => "短信内容超出限制!",
                    );
                    \MsgAction::returnJsonData($errorData);

                    return true;
                }
            }

            $getRedisAction = \GetRedisAction::getInstance();
            $getRedisAction->incrMonitor(count($phoneMsgArr), 'batchSend', $smsType, $pid);

            $column = array(
                "phone",
                "encrypt_phone",
                "text",
                "sessionid",
                "type",
                "send_time",
                "send_status",
                "send_response",
                "passid",
                "pid",
                'business_id',
                'client_ip',
                'server_ip',
            );
            //sessionId = 定义一个唯一的sessionid； 用来表示该群发为同一个会话.
            $sessionId = uniqid();
            $insertData = array();
            foreach ($phoneMsgArr as $value)
            {
                list($packageId, $phone, $msg) = explode("|", $value);
                $phoneList[] = $phone;
                if (isset($insertData[$phone]))
                {
                    continue;
                }
                $insertData[$phone] = array(
                    $phone,
                    $phone,
                    $msg,
                    $sessionId,
                    $smsType,
                    date("Y-m-d H:i:s"),
                    -22,
                    '等待发送',
                    "",
                    $pid,
                    $positionId,
                    $clientIp,
                    get_client_ip(),
                );
            }
            $smsModel = \SmsModel::getInstance();
            $phoneLogIds = $smsModel->setSmsLogs($insertData, $column);
            $smsModel->addSmsLog2Redis($insertData, $column);
            if ($phoneLogIds)
            {
                foreach ($phoneMsgArr as $value)
                {
                    list($packageId, $phone, $msg) = explode("|", $value);
                    $logId = $phoneLogIds[$phone];
                    $multiContent[$packageId] = array($phone, $msg, $logId);
                }
            }
            else
            {
                $errorData = array(
                    'status' => 0,
                    'msg'    => \SmsAction::$errStr[0],
                );

                \MsgAction::returnJsonData($errorData);
            }

            $data = array(
                "phone"        => $phoneList,
                "multiContent" => $multiContent,
                "pid"          => $pid,
                "clientIp"     => $clientIp,
                "positionId"   => $positionId,
                "smsType"      => $smsType,
                "appType"      => 1,
                "phoneLogIds"  => $phoneLogIds,
            );
            $apiSmsMultiSenderAction->apiSend($data);

            return true;
        }
        else
        {
            $errorData = array(
                'status' => $isLegal[0],
                'msg'    => $isLegal[1],
            );

            \MsgAction::returnJsonData($errorData);

            return true;
        }
    }

    /**
     * 模板变量群发短信
     * @return bool
     */
    public function actionVariableMsgSend()
    {
        $funcAction = \FuncAction::getInstance();
        $funcAction->setHttpError();

        $template = strval($this->Filter->post('template'));
        $template = \EncodingAction::transcoding($template, 'gbk');
        $phoneMsg = strval($this->Filter->post('phoneMsg'));
        $phoneMsg = \EncodingAction::transcoding($phoneMsg, 'gbk');

        $pid = (int)$this->Filter->post('pid');
        $smsType = (int)$this->Filter->post('smsType');
        $positionId = (int)$this->Filter->post('positionId');
        $clientIp = $this->Filter->post('clientIp');

        $varCount = mb_substr_count($template, '{$var}');

        if ((mb_substr_count($phoneMsg, ",") + 1) * ($varCount + 1) != substr_count($phoneMsg, "|"))
        {
            \MsgAction::returnJsonData(array('status' => -36, 'msg' => \SmsAction::$multiErrStr[-36]));

            return true;
        }

        if (ServiceFunc::useSwoolePid($pid))
        {
            $requestSwooleData = [
                'template' => $template,
                'phoneMsg'   => $phoneMsg,  //phoneMsg字段需要传入消息编号、手机号、内容这三项内容，使用|字符分隔；短信之间使用半角逗号,分隔
                'smsType'    => $smsType,  //短信类型
                'pid'        => $pid,    //项目ID
                'clientIp'   => $clientIp,   //客户端的ip
                'positionId' => $positionId,  //位置id
            ];
            $requestSwooleData = array_merge($requestSwooleData, ['sendType' => RequestFunc::SMS_API_SEND_TYPE_VARIABLE]);
            $response = (new RequestHandle())->run($requestSwooleData);
            return \MsgAction::returnJsonData(array('status' => $response['status'], 'msg' => $response['msg'], 'data' => $response['data']));
        }

        // 检查 数据
        $requiredParamList = array(
            "smsType"   => $smsType,
            "pid"       => $pid,
            'client_ip' => $clientIp,
        );
        $apiSmsMultiSenderAction = \ApiSmsMultiSenderAction::getInstance();
        $isLegal = $apiSmsMultiSenderAction->checkParams($requiredParamList);
        if ($isLegal === true)
        {
            $multiContent = array();
            $phoneMsgArr = explode(",", $phoneMsg);
            $phoneList = array();

            if (count($phoneMsgArr) > 100)
            {
                \MsgAction::returnJsonData(array('status' => -31, 'msg' => \SmsAction::$multiErrStr[-31]));

                return true;
            }

            foreach ($phoneMsgArr as $value)
            {
                $variables = explode("|", $value);
                $packageId = array_shift($variables);
                $phone = array_shift($variables);
                $msg = $template . "," . implode("|", $variables);

                if (mb_strlen($msg, "UTF-8") > 1000)
                {
                    $errorData = array(
                        'status' => "-25",
                        'msg'    => "短信内容超出限制!",
                    );
                    \MsgAction::returnJsonData($errorData);

                    return true;
                }
            }

            $getRedisAction = \GetRedisAction::getInstance();
            $getRedisAction->incrMonitor(count($phoneMsgArr), 'varSend', $smsType, $pid);

            $column = array(
                "phone",
                "encrypt_phone",
                "text",
                "sessionid",
                "type",
                "send_time",
                "send_status",
                "send_response",
                "passid",
                "pid",
                'business_id',
                'client_ip',
                'server_ip',
            );
            //sessionId = 定义一个唯一的sessionid； 用来表示该群发为同一个会话.
            $sessionId = uniqid();
            $insertData = array();
            foreach ($phoneMsgArr as $value)
            {
                $variables = explode("|", $value);
                $packageId = array_shift($variables);
                $phone = array_shift($variables);
                $msg = $template . "," . implode("|", $variables);

                $phoneList[] = $phone;
                if (isset($insertData[$phone]))
                {
                    continue;
                }
                $insertData[$phone] = array(
                    $phone,
                    $phone,
                    $msg,
                    $sessionId,
                    $smsType,
                    date("Y-m-d H:i:s"),
                    -22,
                    '等待发送',
                    "",
                    $pid,
                    $positionId,
                    $clientIp,
                    get_client_ip(),
                );
            }
            $smsModel = \SmsModel::getInstance();
            $phoneLogIds = $smsModel->setSmsLogs($insertData, $column);
            $smsModel->addSmsLog2Redis($insertData, $column);
            if ($phoneLogIds)
            {
                foreach ($phoneMsgArr as $value)
                {
                    $variables = explode("|", $value);
                    $packageId = array_shift($variables);
                    $phone = array_shift($variables);
                    $logId = $phoneLogIds[$phone];
                    $multiContent[$packageId] = array($phone, $variables, $logId);
                }
            }
            else
            {
                $errorData = array(
                    'status' => 0,
                    'msg'    => \SmsAction::$errStr[0],
                );

                \MsgAction::returnJsonData($errorData);
            }

            $data = array(
                "phone"        => $phoneList,
                "template"     => $template,
                "multiContent" => $multiContent,
                "pid"          => $pid,
                "clientIp"     => $clientIp,
                "positionId"   => $positionId,
                "smsType"      => $smsType,
                "appType"      => 1,
                "phoneLogIds"  => $phoneLogIds,
            );
            $apiSmsMultiSenderAction->apiVariableMsgSend($data);

            return true;
        }
        else
        {
            $errorData = array(
                'status' => $isLegal[0],
                'msg'    => $isLegal[1],
            );

            \MsgAction::returnJsonData($errorData);

            return true;
        }
    }
}
