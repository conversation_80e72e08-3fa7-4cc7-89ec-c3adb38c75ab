<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/17
 * Time: 16:07
 */

namespace Service\Swoole\Event;

use Service\Swoole\Tool\Log;

class Connect
{

    /**
     * 连接
     * -
     * @param object $service 服务
     * @param object $fd 文件句柄
     * @param int $reactorId 线程ID
     * @return void
     */
    public static function OnConnect($service, $fd, $reactorId)
    {
//        Log::setLogMsg('OnConnect：' . var_export($service->connection_info($fd), true) . '线程ID：' . $reactorId);
    }
}
