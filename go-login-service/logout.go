package main

import (
	"fmt"
	"login-service/dev"
	"login-service/utils"
	"net/http"
	"net/url"
	"os"
	"strings"
)

// logout function - Direct Go translation of the PHP logout() function
// 退出函数 - PHP logout() 函数的直接 Go 语言翻译
func logout(w http.ResponseWriter, r *http.Request) {
	// Create cookie manager
	cookieManager := utils.NewCookieManager()
	httpClient := utils.NewHTTPClient()

	// mySetCookie('u_sec', "", -1);
	cookieManager.SetCookie(w, "u_sec", "", -1)
	
	// mySetCookie('passid', "", -1);
	cookieManager.SetCookie(w, "passid", "", -1)
	
	// mySetCookie('name', "", -1);
	cookieManager.SetCookie(w, "name", "", -1)
	
	// mySetCookie('uid', "", -1);
	cookieManager.SetCookie(w, "uid", "", -1)
	
	// mySetCookie('iden', "", -1);
	cookieManager.SetCookie(w, "iden", "", -1)
	
	// mySetCookie("name_ie", "", -1);
	cookieManager.SetCookie(w, "name_ie", "", -1)
	
	// mySetCookie("user_info", "", -1);
	cookieManager.SetCookie(w, "user_info", "", -1)
	
	// mySetCookie("I", "", -1);
	cookieManager.SetCookie(w, "I", "", -1)

	// $forward = $_REQUEST['forward'];
	forward := getRequestParam(r, "forward")

	// $vDomain = get_domain();
	vDomain := utils.GetDomain()

	// $url = 'https://passport.2345.com/v3/user/sso/clearToken';
	ssoURL := "https://passport.2345.com/v3/user/sso/clearToken"

	// Build cookies string equivalent to PHP logic:
	// $cookies = "";
	// foreach ($_COOKIE as $k => $v) {
	//     $cookies .= ($k . "=" . $v . ";");
	// }
	// $cookies = "cookie:" . rtrim($cookies, ";");
	cookiesStr := buildCookiesString(r)

	// curl_get_contents($url . '?mid=login', [CURLOPT_COOKIE => $cookies]);
	options := map[string]interface{}{
		"cookies": cookiesStr,
	}
	_, _ = httpClient.Get(ssoURL+"?mid=login", options)

	// if ($_ENV['RUNMODE'] == "development") {
	if os.Getenv("RUNMODE") == "development" {
		// dev::getInstance($_ENV['RUNMODE']);
		devInstance := dev.GetInstance(os.Getenv("RUNMODE"))
		
		// $headerUrl = "http://passport.2345.com/login_for_index_new.php?action=logout&forward=" . rawurlencode($forward);
		headerURL := fmt.Sprintf("http://passport.2345.com/login_for_index_new.php?action=logout&forward=%s", 
			url.QueryEscape(forward))
		
		// $devDomain = dev::getDevDomain($headerUrl);
		devDomain := devInstance.GetDevDomain(headerURL)
		
		// header("Location: {$devDomain}");
		// exit;
		http.Redirect(w, r, devDomain, http.StatusFound)
		return
	}

	// if ($vDomain == '2345')
	if vDomain == "2345" {
		// if (strpos($forward, "hao774.com") > 0)
		if strings.Contains(forward, "hao774.com") {
			// header("Location: http://passport.hao774.com/webapi/sso/logoutCrossDomain?forward=" . rawurlencode($forward));
			redirectURL := fmt.Sprintf("http://passport.hao774.com/webapi/sso/logoutCrossDomain?forward=%s", 
				url.QueryEscape(forward))
			http.Redirect(w, r, redirectURL, http.StatusFound)
			return
		}
		// elseif (strpos($forward, "7255.com") > 0)
		if strings.Contains(forward, "7255.com") {
			// header("Location: http://passport.7255.com/webapi/sso/logoutCrossDomain?forward=" . rawurlencode($forward));
			redirectURL := fmt.Sprintf("http://passport.7255.com/webapi/sso/logoutCrossDomain?forward=%s", 
				url.QueryEscape(forward))
			http.Redirect(w, r, redirectURL, http.StatusFound)
			return
		}
		// else
		// header("Location: http://bbs." . $vDomain . ".cn/api/passport.php?action=logout&forward=" . rawurlencode($forward));
		redirectURL := fmt.Sprintf("http://bbs.%s.cn/api/passport.php?action=logout&forward=%s", 
			vDomain, url.QueryEscape(forward))
		http.Redirect(w, r, redirectURL, http.StatusFound)
		return
	}
	
	// elseif ($forward)
	if forward != "" {
		// header("Location: https://www.2345.com");
		http.Redirect(w, r, "https://www.2345.com", http.StatusFound)
		return
	}
	
	// else
	// header("Location: http://my." . $vDomain . ".com/");
	redirectURL := fmt.Sprintf("http://my.%s.com/", vDomain)
	http.Redirect(w, r, redirectURL, http.StatusFound)
	// die; (equivalent to return in Go)
}

// getRequestParam gets parameter from both GET and POST (equivalent to $_REQUEST in PHP)
func getRequestParam(r *http.Request, key string) string {
	// Try GET parameter first
	if value := r.URL.Query().Get(key); value != "" {
		return value
	}
	// Try POST parameter
	return r.FormValue(key)
}

// buildCookiesString builds the cookies string equivalent to PHP logic
func buildCookiesString(r *http.Request) string {
	var cookiePairs []string
	
	// foreach ($_COOKIE as $k => $v) {
	//     $cookies .= ($k . "=" . $v . ";");
	// }
	for _, cookie := range r.Cookies() {
		cookiePairs = append(cookiePairs, fmt.Sprintf("%s=%s", cookie.Name, cookie.Value))
	}
	
	// $cookies = "cookie:" . rtrim($cookies, ";");
	if len(cookiePairs) > 0 {
		return strings.Join(cookiePairs, "; ")
	}
	return ""
}
