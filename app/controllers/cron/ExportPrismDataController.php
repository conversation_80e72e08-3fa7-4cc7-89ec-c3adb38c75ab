<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：ExportPrismDataController.php
 * 摘    要：导出账户信息和第三方绑定关系
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：06-27, 2017
 */

includeBase('CronController');
use Octopus\PdoEx;

class ExportPrismDataController extends CronController
{

    const DEFAULT_LAST_ID = ********;
    const DEFAULT_LIMIT = 300;
    const LOOP = 20;

    private $exportFilePath = "";

    /**
     * ExportPrismDataController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->exportFilePath = APPPATH . "/prism_data";
    }

    /**
     * # 导出最近新增用户数据
     * * / 20 * * * *  /opt/app/php5/bin/php  /opt/case/login.2345.com/cron.php cron/ExportPrismData/account
     *
     * @return bool
     */
    public function actionAccount()
    {
        $lastId = self::DEFAULT_LAST_ID;
        $statusFile = 0;
        if (file_exists($this->exportFilePath . "/account_status"))
        {
            $statusFile = file_get_contents($this->exportFilePath . "/account_status");
        }

        if (is_numeric($statusFile) && $statusFile > time() - 86400)
        {
            echo "running\n";
            // running exit;
            exit;
        }
        $statusFile = time();
        file_put_contents($this->exportFilePath . "/account_status", $statusFile);

        if (file_exists($this->exportFilePath . "/account_passid"))
        {
            $lastId = file_get_contents($this->exportFilePath . "/account_passid");
        }
        else
        {
            if (time() > **********)
            {
                // 已经开始一段时间了, 就不能从一开始 的 DEFAULT_LAST_ID 开始跑了.
                echo "get account passid error\n";
                exit;
            }
        }

        for ($i = 0; $i < self::LOOP; $i++)
        {
            $data = $this->exportAccount($lastId, self::DEFAULT_LIMIT);
            if (!$data)
            {
                // 无数据 break
                echo "no more data\n";
                break;
            }
            else
            {
                $count = count($data);
                $lastId = $data[$count - 1]['id'];
                file_put_contents($this->exportFilePath . "/account_passid", $lastId);

                if ($count != self::DEFAULT_LIMIT)
                {
                    // 无更多数据 break
                    echo "no more data\n";
                    break;
                }
            }
            sleep(5);
        }

        file_put_contents($this->exportFilePath . "/account_status", '0');
    }

    /**
     * @param int      $startId    startID
     * @param int      $limitCount limit
     *
     * @return bool
     */
    private function exportAccount($startId = 0, $limitCount = 400)
    {
        $rows = loadModel('member')->getUserListById($startId, $limitCount);

        $currentTime = time();
        $date = date("Ymd", $currentTime);
        $hour = date("H", $currentTime);
        if ($hour == "01")
        {
            return false;
        }

        if ($rows)
        {
            $fileName = "ACCOUNT_" . $date . "_" . $currentTime . ".log";
            $bindInfoFilePath = $this->exportFilePath . "/" . $date . "/" . $hour . "/";
            deepMkdir($bindInfoFilePath);
            if (file_exists($bindInfoFilePath . $fileName))
            {
                echo "log file is already exist: " . $bindInfoFilePath . $fileName . "\n";

                return false;
            }

            $gender = array(
                '0' => '未知',
                '1' => '男',
                '2' => '女',
            );
            $fpBanSame = fopen($bindInfoFilePath . $fileName, 'w');
            if ($fpBanSame)
            {
                foreach ($rows as $key => $row)
                {
                    foreach ($row as $k => $r)
                    {
                        if (mb_strtolower($row[$k]) == "null" || mb_strtolower($row[$k]) == null)
                        {
                            $row[$k] = "";
                        }
                    }

                    $row['avatar'] = '';
                    $row['gender'] = isset($gender[$row['gender']]) ? $row['gender'] : '0';
                    $row = \Common\Utils\Encoding::iteratorArray($row, "gbk", "utf-8");
                    $row["email"] = strpos($row["email"], "@") > 0 ? $row["email"] : '';
                    $row["reg_time"] = strtotime($row["reg_time"]) > 0 ? strtotime($row["reg_time"]) : '';
                    $row["reg_ip"] = ip2long($row["reg_ip"]);
                    fwrite($fpBanSame, json_encode(array_values($row)) . "\n");
                }
                fclose($fpBanSame);
            }
        }

        return $rows;
    }

    /**
     * # 导出最近新增绑定用户绑定数据
     * * / 10 * * * *  /opt/app/php5/bin/php  /opt/case/login.2345.com/cron.php cron/ExportPrismData/bindInfo
     *
     * @return bool
     */
    public function actionBindInfo()
    {
        $lastId = self::DEFAULT_LAST_ID;
        $statusFile = 0;
        if (file_exists($this->exportFilePath . "/bind_status"))
        {
            $statusFile = file_get_contents($this->exportFilePath . "/bind_status");
        }

        if (is_numeric($statusFile) && $statusFile > time() - 86400)
        {
            echo "running\n";
            // running exit;
            exit;
        }
        $statusFile = time();
        file_put_contents($this->exportFilePath . "/bind_status", $statusFile);

        if (file_exists($this->exportFilePath . "/bind_passid"))
        {
            $lastId = file_get_contents($this->exportFilePath . "/bind_passid");
        }
        else
        {
            if (time() > **********)
            {
                // 已经开始一段时间了, 就不能从一开始 的 DEFAULT_LAST_ID 开始跑了.
                echo "get bind passid error\n";
                exit;
            }
        }

        for ($i = 0; $i < self::LOOP; $i++)
        {
            $data = $this->exportBindInfo($lastId, self::DEFAULT_LIMIT);
            if (!$data)
            {
                // 无数据 break
                echo "no more data\n";
                break;
            }
            else
            {
                $count = count($data);
                $lastId = $data[$count - 1]['passid'];
                file_put_contents($this->exportFilePath . "/bind_passid", $lastId);

                if ($count != self::DEFAULT_LIMIT)
                {
                    // 无更多数据 break
                    echo "no more data\n";
                    break;
                }
            }
            sleep(10);
        }

        file_put_contents($this->exportFilePath . "/bind_status", '0');
    }

    /**
     * @param int      $startId    startId
     * @param int      $limitCount limit
     *
     * @return bool
     */
    private function exportBindInfo($startId = 0, $limitCount = 400)
    {
        $rows = loadModel('member')->getBindListById($startId, $limitCount);

        $currentTime = time();
        $date = date("Ymd", $currentTime);
        $hour = date("H", $currentTime);
        if ($hour == "01")
        {
            return false;
        }

        if ($rows)
        {
            $fileName = "RELATIONACCOUNTINFO_" . $date . "_" . $currentTime . ".log";
            $bindInfoFilePath = $this->exportFilePath . "/" . $date . "/" . $hour . "/";
            deepMkdir($bindInfoFilePath);
            if (file_exists($bindInfoFilePath . $fileName))
            {
                echo "log file is already exist: " . $bindInfoFilePath . $fileName . "\n";

                return false;
            }

            $bindTypeList = array(
                'qq'     => 1030001,
                'weixin' => 1030036,
                'weibo'  => 1330001,
            );
            $fpBanSame = fopen($bindInfoFilePath . $fileName, 'w');
            if ($fpBanSame)
            {
                foreach ($rows as $row)
                {
                    foreach ($row as $k => $r)
                    {
                        if (mb_strtolower($row[$k]) == "null" || mb_strtolower($row[$k]) == null)
                        {
                            $row[$k] = "";
                        }
                    }
                    if (isset($bindTypeList[$row['type']]))
                    {
                        $row['type'] = $bindTypeList[$row['type']];
                    }
                    else
                    {
                        // 不是qq weixin weibo 略过
                        continue;
                    }
                    $row = \Common\Utils\Encoding::iteratorArray($row, "gbk", "utf-8");

                    fwrite($fpBanSame, json_encode(array_values($row)) . "\n");
                }
                fclose($fpBanSame);
            }
        }

        return $rows;
    }

}
