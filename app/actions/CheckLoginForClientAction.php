<?php

includeBase('BaseCheckLoginAction');

class CheckLoginForClientAction extends BaseCheckLoginAction
{
    /**
     * CheckLoginForClientAction constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * initFLoginInfo
     * -
     * @param string $mid  用户中心唯一标识
     * @return array
     * <AUTHOR>
     */
    public function initFLoginInfo($mid = "")
    {
        $data = array(
            'unf' => $this->getRandUserName(),
            'flToken' => md5(rand(1, 999999) . time()),
            'showCaptcha' => 0,
            'openCMLogin' => 1,
            'openWXLogin' => 1,
            'openQQLogin' => 1,
            'kv' => 1,
            'openUnionLogin' => 1,
        );

        if (!isset($_SESSION['expire']))
        {
            $_SESSION['expire'] = 0;
        }
        elseif ($_SESSION['expire'] > $this->showCodeLimitCount)
        {
            $data['showCaptcha'] = 1;
        }
        else
        {
            $loginAction = loadAction('login');
            if ($loginAction->decideCode($this->IP))
            {
                $data['showCaptcha'] = 1;
            }
        }

        if ($data['showCaptcha'] != 1)
        {
            loadAction('checkIP');
            if (!CheckIPAction::checkISChinaIP($this->IP))
            {
                $data['showCaptcha'] = 1;
            }
        }
        if (in_array($mid, ['andsjzs'])) {
            $data['openCMLogin'] = 0;
        }
        $this->saveFLoginInfo($data);
        return $data;
    }

    /**
     * getRandUserName
     * -
     * @return string
     * <AUTHOR>
     */
    private function getRandUserName()
    {
        $result = '';
        $length = mt_rand(5, 9);
        $charStr = '123546798abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charLength = strlen($charStr);
        for ($i = 0; $i < $length; $i++)
        {
            $result .= $charStr[mt_rand(0, $charLength - 1)];
        }
        return $result;
    }

    /**
     * saveFLoginInfo
     * -
     * @param array $factionData FactionData
     * @return void
     * <AUTHOR>
     */
    public function saveFLoginInfo($factionData)
    {
        $factionData['loginInit'] = time();
        $_SESSION[FLOGIN_SESSION_KEY] = serialize($factionData);
    }

    /**
     * getFLoginSessionInfo
     * -
     * @return array|mixed
     * <AUTHOR>
     */
    public function getFLoginSessionInfo()
    {
        $fLoginSessionInfo = array();
        if (isset($_SESSION[FLOGIN_SESSION_KEY]))
        {
            $fLoginSessionInfo = unserialize($_SESSION[FLOGIN_SESSION_KEY]);
        }
        return $fLoginSessionInfo;
    }

    /**
     * isNeedCheckCaptcha
     * -
     * @return bool
     * <AUTHOR>
     */
    public function isNeedCheckCaptcha()
    {
        $checkResult = false;
        $fLoginSessionInfo = $this->getFLoginSessionInfo();
        if ($fLoginSessionInfo['showCaptcha'] > 0)
        {
            $checkResult = true;
        }
        else
        {
            $loginAction = loadAction('login');
            $expire = intval($_SESSION['expire']);
            if ($expire > $this->showCodeLimitCount || $loginAction->decideCode($this->IP))
            {
                $checkResult = true;
            }
            else
            {
                loadAction('checkIP');
                if (!CheckIPAction::checkISChinaIP($this->IP))
                {
                    $checkResult = true;
                }
            }
        }

        return $checkResult;
    }

    /**
     * checkFToken
     * -
     * @return bool
     * <AUTHOR>
     */
    public function checkFToken()
    {
        $requestToken = $_SERVER['HTTP_X_UC_FTOKEN'];
        $info = $this->getFLoginSessionInfo();
        if (isset($info['flToken']) && md5($info['flToken']) == $requestToken)
        {
            return true;
        }
        return false;
    }

    /**
     * checkFLoginInfo
     * -
     * @param array $postData PostData
     * @param string $errInfoStr ErrInfoStr
     * @param bool $checkCaptcha 是否检测验证码
     * @return bool
     * <AUTHOR>
     */
    public function checkFLoginInfo($postData, &$errInfoStr, $checkCaptcha = true)
    {
        $errInfoStr = '';
        $checkResult = true;
        $fLoginSessionInfo = $this->getFLoginSessionInfo();
        $fieldArray = array(
            'flToken',
            'loginInit',
            'unf',
        );
        foreach ($fieldArray as $key => $fName)
        {
            if (!isset($fLoginSessionInfo[$fName]) || empty($fLoginSessionInfo[$fName]))
            {
                $errInfoStr = $fName . '没有-服务端';
                $checkResult = false;
                break;
            }
        }
        if ($checkResult && $checkCaptcha)
        {
            if ($this->isNeedCheckCaptcha())
            {
                if (!isset($postData['check_code']) || empty($postData['check_code']))
                {
                    $errInfoStr = '缺少验证码';
                    $checkResult = false;
                }
                if (!isset($_SESSION['captcha_code']))
                {
                    $errInfoStr = '缺少验证码数据-服务端';
                    $checkResult = false;
                }
            }
        }


        if ($checkResult)
        {
            $userNameField = $fLoginSessionInfo['unf'];
            if (!isset($postData['flToken']) || empty($postData['flToken']) || $postData['flToken'] != md5($fLoginSessionInfo['flToken']))
            {
                if (!isset($postData['flToken']) || empty($postData['flToken']))
                {
                    $errInfoStr = '缺少token参数或为空';
                }
                elseif ($postData['flToken'] != md5($fLoginSessionInfo['flToken']))
                {
                    $errInfoStr = 'token不一致';
                }
                $checkResult = false;
            }
            elseif (!isset($postData[$userNameField]) || empty($postData[$userNameField]))
            {
                $errInfoStr = '缺少用户名字段参数';
                $checkResult = false;
            }
        }

        return $checkResult;
    }

    /**
     * clearFInfo
     * -
     * @return void
     * <AUTHOR>
     */
    public function clearFInfo()
    {
        unset($_SESSION[FLOGIN_SESSION_KEY]);
    }

    /**
     * getUserNameField
     * -
     * @return mixed
     * <AUTHOR>
     */
    public function getUserNameField()
    {
        $result = "";
        if (isset($_SESSION[FLOGIN_SESSION_KEY]) && !empty($_SESSION[FLOGIN_SESSION_KEY]))
        {
            $fLoginSessionInfo = unserialize($_SESSION[FLOGIN_SESSION_KEY]);
            return isset($fLoginSessionInfo['unf']) ? $fLoginSessionInfo['unf'] : "";
        }
        return $result;
    }

    /**
     * clearCaptcha
     * -
     * @return void
     * <AUTHOR>
     */
    public function clearCaptcha()
    {
        unset($_SESSION['captcha_code']);
    }
}
