var enableCheck = true;
var intervalHandle = false;
var intervalTimes = 60;
var phoneValidate = false;
var time = 10;
function sendCode(obj,showObj)
{
    obj.hide();
    showObj.show();
    var timego = setInterval(function()
    {
        if(time == 0)
        {
            clearInterval(timego);
            time = 10;
            obj.show();
            showObj.find("em").text(time);
            showObj.hide();
        }else
        {
            time --;
            showObj.find("em").text(time);
        }
    },1000);
};

jQuery(function($) {

    $("#validate").bind({
        focus: function() {
            $("#msg_validate span").hide();
            if ($(this).val() == '请输入验证码')
            {
                $(this).val("");
            }
        }
    });
    $('#confirmCode').bind({
        click:function(){

            if ( !checkValidatephone() )
            {
                return false;
            }
            var val = $("#validate").val();
            intervalTimes = 60;
            clearInterval(intervalHandle);
            intervalHandle = setInterval(function() {
                showSendMsg('sms');
            }, 1000);

            var phone = $('#username_phone').val().trim();
            $.post("/browser3/reg/sendPhoneCode", {'phone': phone, 'validate': val}, function(data) {

                if (data == '400.0')
                {
                    showSendBtn('sms');

                    $("#validate_phone").attr('action', 'frequently');
                    $('#validate').parent().addClass("l-inputFocus");
                    $("#msg_validate_phone span").html('验证太频繁，请稍后再试！');
                    $("#msg_validate_phone span").show();

                }
                else if (data == '500.0')
                {
                    showSendBtn('sms');
                    $('#validate').parent().addClass("l-inputFocus");
                    $("#msg_validate_phone span").html('服务器繁忙，请稍后再试！');
                    $("#msg_validate_phone span").show();

                }
                else if (data == '300.0')
                {
                    showSendBtn('sms');
                    $('#validate').parent().addClass("l-inputFocus");
                    $("#msg_validate_phone span").html('手机号码格式错误，请稍后再试！');
                    $("#msg_validate_phone span").show();

                }
                else if (data == '600.0')
                {
                    showSendBtn('sms');
                    $('#validate').parent().addClass("l-inputFocus");
                    $("#msg_validate_phone span").html('图片验证码错误，请重新输入！');
                    $("#msg_validate_phone span").show();

                }
            });

        }
    });


    $("#validate_email").bind({
        focus: function() {
            if ($(this).val() == '请输入验证码')
            {
                $(this).val("");
            }
        },
        blur: function() {
            checkValidate_sj('email');
        }
    });
    $("#validate_phone").bind({
        blur: function() {
           checkValidatePhone_sj();
        }
    });


    $('#btn_yzm_phone_sms').bind({
        click: function() {
            $("#validate_phone").attr('action', '');
            var phone = $('#username_phone').val().trim();
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                $("#msg_validate_phone span").addClass('inputTxtError').html('请填写正确手机号！');
                $("#msg_validate_phone").show();

                return false;
            }
            val = $("#validate").val();
            if (val === '' || val == '请输入验证码')
            {
                $("#validate").parent().addClass("inputTxtError").find('.icon').hide();
                $("#msg_validate span").html('请输入验证码');
                $("#msg_validate").show()
                return false;
            }
            jQuery.ajax({
                url: "/api/check",
                data: 'type=captcha&val=' + val,
                type: 'POST',
                async: false,
                success: function(response) {
                    err = response;
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR.status);
                },
                timeout: 3000
            });
            $("#demo-pop-wx").css({"display": "none"});
            $('#msg_validate_phone span').html('已将短信发送到您的手机，请注意查收。');
            $('#msg_validate_phone').show().children('span').prop('class', 'sDes');
            $('#btn_yzm_phone').attr('action', 'sms');
            if (err == 1)
            {
                intervalTimes = 60;
                clearInterval(intervalHandle);
                intervalHandle = setInterval(function() {
                    showSendMsg('sms');
                }, 1000);
                $.post("/browser3/reg/sendPhoneCode", {'phone': phone, 'validate': val}, function(data) {
                    if (data == '400.0')
                    {
                        showSendBtn('sms');
                        $("#validate_phone").attr('action', 'frequently');
                        $("#msg_validate_phone span").prop('class', 'sError').html('发送频繁 ，请稍后再试！');
                        $("#msg_validate_phone").show();
                    }
                    else if (data == '500.0')
                    {
                        showSendBtn('sms');
                        $("#msg_validate_phone span").prop('class', 'sError').html('服务器繁忙，请稍后再试！');
                        $("#msg_validate_phone").show();
                    }
                    else if (data == '300.0')
                    {
                        showSendBtn('sms');
                        $("#msg_validate_phone span").prop('class', 'sError').html('手机号码格式错误，请稍后再试！');
                        $("#msg_validate_phone").show();

                    }
                });
            }
            else
            {
                $("#Pic").attr('src', '/Captcha?J' + Math.random());
            }
        }
    });

    $('#btn_yzm_phone').bind({
        click: function() {
            var phone = $('#username_phone').val().trim();
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                $("#msg_username_phone span").html('请填写正确手机号！');
                $("#msg_username_phone span").show();
                $('#username_phone').parent().focus()
            }
            checkPhone(1);
            if (phoneValidate == true) {
                $("#Pic").attr('src', '/Captcha?J' + Math.random());
                $(".l-codeBox").show();
            }
        }
    });


});

function showSendMsg(from) {
    if (intervalTimes > 1) {
        --intervalTimes;
        $("#btn_yzm_phone").hide();
        $("#btn_yzmed_phone").text('重新发送（' + intervalTimes + '）').show();
    } else {
        showSendBtn(from);
    }
}
function showSendBtn(from) {
    clearInterval(intervalHandle);
    intervalTimes = 60;
    $("#btn_yzmed_phone").hide();
    $("#btn_yzm_phone").text('获取验证码').show();
}

function checkPhone()
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    var username = $("#username_phone").val().trim();
    var isvalid = true;
    var msg_username_phone = '';
    if(username == '')
    {
        isvalid = false;
        msg_username_phone = '请输入手机号';
    }
    if(username.length != 11 || !/^1[0123456789]\d{9}$/.test(username))
    {
        isvalid = false;
        msg_username_phone = '请输入正确的手机号';
    }
    else
    {
        type = 'phone';
        typeName = '手机号码';
    }
    if (!isvalid)
    {
        $("#msg_username_phone span").html(msg_username_phone);
        $("#msg_username_phone span").show();
        $('#username_phone').parent().addClass("l-inputFocus");
        return false;
    }
    jQuery.ajax({
        url: "/api/check/jsonp",
        data: 'type=' + type + '&value=' + username + '&region=1',
        async: false,
        dataType: 'jsonp',
        jsonp: 'callback',
        success: function(response) {
            if (response == -1) {
                showSendBtn('sms');
                err = 0;
            }
            else
            {
                showSendBtn('voice');
                err = response;
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    if (typeof err === "undefined")
    {
        $("#msg_agree span").html('您的注册可能过于频繁，若有疑问请联系客服！');
        $("#msg_agree").show();
        return false;
    }
    if (err != 0) {
        isvalid = false;
        msg_username_phone = '这个' + typeName + '已经被使用，换一个吧';
    }
    if (isvalid === true)
    {
        $("#msg_username_phone span").html('');
        $("#msg_username_phone span").hide();

        $("#msg_validate_phone span").html('');
        $("#msg_validate_phone span").hide();

        if (/^1[0123456789]\d{9}$/.test(username)) {
            $('#reg_type_phone').val('phone');
        }
        phoneValidate = true;
        return true;
    }
    else
    {
        $('#username_phone').parent().addClass("l-inputFocus");
        $("#msg_username_phone span").html(msg_username_phone);
        $("#msg_username_phone span").show();

        phoneValidate = false;
        return false;
    }
}
function checkEmail()
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    var username_email = $("#username_email").val().trim();
    var isvalid = true;
    var msg_username_email = '';
    if(username_email == '')
    {
        isvalid = false;
        msg_username_email = '请输入邮箱帐号';
    }
    if(!/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username_email))
    {
        isvalid = false;
        msg_username_email = '请输入正确的邮箱帐号';
    }
    else
    {
        type = 'email';
        typeName = '邮箱';
    }

    if (!isvalid)
    {
        $("#msg_username_email span").html(msg_username_email);
        $("#msg_username_email span").show();
        $('#username_email').parent().addClass("l-inputFocus");
        return false;
    }

    jQuery.ajax({
        url: "/api/check/jsonp",
        data: 'type=' + type + '&value=' + username_email + '&status=0',
        async: false,
        dataType: 'jsonp',
        jsonp: 'callback',
        success: function(response) {
            err = response;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });

    if (typeof err === "undefined")
    {
        $("#msg_username_email span").html('');
        $("#checkCodeId span").html('您的注册可能过于频繁，若有疑问请联系客服！');
        $("#checkCodeId span").show();
        $('#username_email').parent().addClass("l-inputFocus");
        return false;
    }

    if (err != 0) {
        isvalid = false;
        msg_username = '这个' + typeName + '已经被使用，换一个吧';
    }
    if (isvalid === true)
    {
        $("#msg_username_email span").html('');
        $("#msg_username_email span").hide();

        if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username_email)) {
            $('#reg_type_email').val('email');
        }
        return true;
    }
    else
    {
        $("#msg_username_email span").html(msg_username);
        $("#msg_username_email span").show();
        $('#username_email').parent().addClass("l-inputFocus");
        return false;
    }
}


function safe(eName, type)
{
    var isvalid = false;
    var msg_pwd = '';

    var pass = $("#password_" + type).val();
    var tipId = 'msg_pass_' + type;

    if (pass.length > 16)
    {
        msg_pwd = '最多16个字符';
        isvalid = false;
    }
    if ($.browser.msie && ($.browser.version == "6.0") && !$.support.style)
    {
        var longScore = getPwdScore(pass);
        if(longScore <= 10)
        {
            score = 0;
        }
        else if(longScore >= 11 && longScore <= 20)
        {
            score = 1;
        }
        else if(longScore >= 21 && longScore <= 30)
        {
            score = 2;
        }
        else if(longScore >= 31 && longScore <= 40)
        {
            score = 3;
        }
        else if(longScore >= 41)
        {
            score = 4;
        }
    }
    else
    {
        try{
            var passscore = zxcvbn(pass);
            var score = passscore.score;
        }
        catch(err)
        {
            var longScore = getPwdScore(pass);
            if(longScore <= 10)
            {
                score = 0;
            }
            else if(longScore >= 11 && longScore <= 20)
            {
                score = 1;
            }
            else if(longScore >= 21 && longScore <= 30)
            {
                score = 2;
            }
            else if(longScore >= 31 && longScore <= 40)
            {
                score = 3;
            }
            else if(longScore >= 41)
            {
                score = 4;
            }
        }
    }

    if (score <= 0)
    {
        $('#' + tipId + ' .l-pwdHard').show();
        $('#' + tipId + ' .l-pwdHard').removeClass().addClass('l-pwdHard l-level1');
        pass == '' ? msg_pwd = '密码不能为空' : msg_pwd = '密码强度不能为弱';
        $('#' + tipId + ' .l-pwdHard').find('em').html('弱');
        $('#' + tipId +' .l-errorTxt').html('').hide();
        isvalid = false;
    }
    else if (score == 1)
    {
        $('#' + tipId + ' .l-pwdHard').show();
        $('#' + tipId + ' .l-pwdHard').removeClass().addClass('l-pwdHard l-level2');
        $('#' + tipId + ' .l-pwdHard').find('em').html('中');
        $('#' + tipId +' .l-errorTxt').html('').hide();
        isvalid = true;
    }
    else if (score == 2)
    {
        $('#' + tipId + ' .l-pwdHard').show();
        $('#' + tipId + ' .l-pwdHard').removeClass().addClass('l-pwdHard l-level3');
        $('#' + tipId + ' .l-pwdHard').find('em').html('强');
        $('#' + tipId +' .l-errorTxt').html('').hide();
        isvalid = true;
    }
    else if(score >= 3)
    {
        $('#' + tipId + ' .l-pwdHard').show();
        $('#' + tipId + ' .l-pwdHard').removeClass().addClass('l-pwdHard l-level4');
        $('#' + tipId + ' .l-pwdHard').find('em').html('极强');
        $('#' + tipId +' .l-errorTxt').html('').hide();
        isvalid = true;
    }


    if (eName === 'keyup' && pass != '')
    {
        $('#' + tipId + ' .l-errorTxt').html('');
        $('#' + tipId + ' span').show();
    }

    if (eName === 'blur')
    {
        $('#' + tipId + ' .l-pwdHard').hide();
        if (isvalid === true)
        {
            if (type == "email" && $('#repassword_email').val() != $('#password_email').val() )
            {
                $("#msg_pass_email_one span").html('两次输入密码不一致').show();
            }
            else
            {

                $('#' + tipId + ' .l-errorTxt').html('');
                $('#' + tipId + ' span').show();
                $('#' + tipId + ' .l-pwdHard').hide();
            }

        }
        else
        {

            $('#' + tipId + ' .l-errorTxt').html(msg_pwd);
            $('#' + tipId + ' .l-errorTxt').show();
            $('#' + tipId + ' .l-pwdHard').hide();

        }
        $("#password_" + type).parent().addClass("l-inputFocus");

    }
    return isvalid;
}
function checkPassSame(pass)
{
    var first = pass.substring(0, 1);
    var exp = new RegExp('^' + first + '+$');
    if (exp.test(pass))
    {
        return false;
    }
    if (first == 'a' || first == 'A')
    {
        f = pass.charCodeAt(0);
        for (i = 1; i < pass.length; i++)
        {
            tmp = pass.charCodeAt(i);
            if (tmp - f != i)
            {
                return true;
            }
        }
        return false;
    }
    return true;
}
function passwordGrade(pwd)
{
    var score = 0;
    var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
    var repeatCount = 0;
    var prevChar = '';
    //check length
    var len = pwd.length;
    score += len > 18 ? 18 : len;
    //check type
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (eval('/' + regexArr[i] + '/').test(pwd))
            score += 4;
    }
    //bonus point
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
            score += 2;
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
            score += 2;
    }
    //deduction
    for (var i = 0, num = pwd.length; i < num; i++) {
        if (pwd.charAt(i) == prevChar)
            repeatCount++;
        else
            prevChar = pwd.charAt(i);
    }
    score -= repeatCount * 1;
    return score;
}
function getPwdScore(pass)
{
    var score = 0;
    if (pass.length < 6)
    {
        score = 0;
    }
    else if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
    {
        score = 0;
    }
    else
    {
        score = passwordGrade(pass);
    }
    return score;

}
function checkRepass(type)
{
    if (type == "phone")
    {
        return true;
    }
    if ($("#password_" + type).val() == '')
    {
        return;
    }

    if ($("#repassword_" + type).val() != $("#password_" + type).val() && $("#repassword_" + type).val() != '')
    {
        $("#msg_pass_email_one span").html('两次输入密码不一致');
        $("#msg_pass_email_one span").html('两次输入密码不一致');

        return false;
    }
    else if($("#repassword_" + type).val() == $("#password_" + type).val() && $("#repassword_" + type).val() != '')
    {
        $("#msg_pass_" + type + " .l-errorTxt").html('');
        $("#msg_pass_" + type + " span").show();
        $("#msg_pass_" + type + ' .l-pwdHard').hide();
        $('#msg_pass_email_one .l-errorTxt').html('');
        return true;
    }

}

function checkValidatephone()
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    var val = $("#validate").val();
    if (val === '' || val == '请输入验证码')
    {
        $('#validate').parent().addClass("l-inputFocus");
        $("#msg_validate span").html('请输入验证码');
        $("#msg_validate span").show();
        return false;
    }
    jQuery.ajax({
        url: "/api/check",
        data: 'type=captcha&val=' + val,
        type: 'POST',
        async: false,
        success: function(response) {
            err = response;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    if (err == 1)
    {
        $("#msg_validate span").html('');
        $("#msg_validate span").hide();
        $(".l-codeBox").hide();
        return true;
    } else {
        $('#validate').parent().addClass("l-inputFocus");
        $("#msg_validate span").html('请输入正确的四位数字');
        $("#msg_validate span").show();
        $("#Pic").attr('src', '/Captcha?J' + Math.random());
        return false;
    }

}
function checkValidate_sj(type)
{
    if(type == 'email')
    {
        var type = "_email";
    }
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    var val = $("#validate" + type ).val();

    if (val === '' || val == '请输入验证码')
    {
        $('#checkCodeId span').html('请输入验证码');
        $('#checkCodeId span').show();
        return false;
    }
    jQuery.ajax({
        url: "/api/check",
        data: 'type=captcha&val=' + val,
        type: 'POST',
        async: false,
        success: function(response) {
            err = response;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    if (err == 1)
    {
        $('#checkCodeId span').html('');
        $('#checkCodeId span').show();
        return true;
    } else {
        $('#checkCodeId span').html('请输入正确的四位数字');
        $('#checkCodeId span').show();
        $('#validate_email').parent().addClass("l-inputFocus");
        $("#Pic" + type ).attr('src', '/Captcha?J' + Math.random());
        return false;
    }

}
function checkValidatePhone_sj()
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    if ($("#validate_phone").attr('action') == 'frequently')
    {
        $('#validate_phone').parent().addClass("l-inputFocus");
        $("#msg_validate_phone span").html('发送频繁 ，请稍后再试！');
        $("#msg_validate_phone span").show();
        return false;
    }

    var code = $("#validate_phone").val().trim();
    if (code === '' || code === '请输入验证码')
    {
        $('#validate_phone').parent().addClass("l-inputFocus");
        $("#msg_validate_phone span").html('请输入手机验证码');
        $("#msg_validate_phone span").show();
        return false;
    }
    jQuery.ajax({
        url: "/reg/checkPhoneCode",
        data: 'code=' + code + '&phone=' + $("#username_phone").val().trim(),
        async: false,
        type: 'POST',
        success: function(response) {
            response === '200.0' ? err = 0 : err = 1;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });

    if (err)
    {
        $('#validate_phone').parent().addClass("l-inputFocus");
        $("#msg_validate_phone span").html('您输入的验证码错误，请重新输入');
        $("#msg_validate_phone span").show();
        return false;
    }
    else
    {
        $("#msg_validate_phone span").html('');
        $("#msg_validate_phone span").hide();
        return true;
    }

}


function checkAgree(type)
{
    if (type == 'phone')
    {
        if (!$('#agreePhoneId').is(':checked'))
        {
            alert('请同意2345服务协议、隐私政策');
            return false;
        }

    }
    else if (type == 'email')
    {
        if (!$('#agreeEmailId').is(':checked'))
        {
            alert('请同意2345服务协议、隐私政策');
            return false;
        }

    }
    else
    {
        return false;
    }
    return true;
}

function checkAll(type)
{
    if(type == "phone")
    {
        if (!checkPhone())
        {
            return false;
        }
    }
    else if(type == "email")
    {
        if (!checkEmail())
        {
            return false;
        }
    }
    if ( $('#validate_' + type).val == '' )
    {
        $('#msg_validate_phone span').html('验证码不能为空').show();
        return false;
    }

    if (!safe('blur', type))
    {
        return false;
    }
    if (!checkRepass(type))
    {
        return false;
    }
    if ($('#reg_type_'+ type).val() !== 'phone' && !checkValidate_sj(type))
    {
        return false;
    }

    if ($('#reg_type_'+type).val() === 'phone' && !checkValidatePhone_sj())
    {
        return false;
    }
    if (type =='phone' && !checkAgree('phone'))
    {
        return false;
    }
    if (type =='email' && !checkAgree('email'))
    {
        return false;
    }
    enableCheck = false;
    var msgs = {
        "300": {
            'username': {
                '0': '2345帐号最少2个字符 @msg_username',
                '1': '2345帐号请不要超过24个字符 @msg_username',
                '2': '2345帐号请输入汉字，字母，数字，或邮箱地址 @msg_username',
                '3': '密码最少6个字符 @msg_pwd',
                '4': '密码最多16个字符 @msg_pwd',
                '5': '请输入正确的邮箱 @msg_username',
                '6': '此帐号已被注册，请修改2345帐号 @msg_username',
                '7': '此邮箱已被注册，请换一个 @msg_username'
            },
            'phone': {
                '0': '请输入正确的手机号码 @msg_username',
                '1': '密码最少6个字符 @msg_pwd',
                '2': '密码最多16个字符 @msg_pwd',
                '3': '此手机号已被注册 @msg_username'
            },
            'email': {
                '0': '请输入正确的邮箱地址 @msg_username',
                '1': '密码最少6个字符 @msg_pwd',
                '2': '密码最多16个字符 @msg_pwd',
                '3': '此邮箱号已被注册 @msg_username',
                '4': '邮箱长度不能超过24个字符 @msg_username'
            }
        },
        "400": {
            '0': '非法域名调用',
            '1': '非法IP调用',
            '2': '批量刷CHECK',
            '3': 'IP段被禁止',
            '4': 'IP被禁止',
            '5': '未验证通过（缺少isValidate）'
        }
    };
    if(type == "phone")
    {
        var postData = $("#myFormPhone").serialize();
    }
    else
    {
        var postData = $("#myFormEmail").serialize();
    }

    $.ajax({
        url: "/browser3/reg",
        data: postData,
        type: 'POST',
        async: false,
        success: function(response) {
            res = eval('(' + response + ')');
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    var tipId = 'msg_pass_' + type;
    if (typeof res.msgCode !== "undefined" && res.msgCode !== '')
    {
        var regType = $('#reg_type_' + type).val();
        var codeArr = res.msgCode.split('.');
        if (codeArr[0] == '400')
        {
            $('#'+ tipId +' span').html(msgs['400'][codeArr[1]] + '，若有疑问请联系客服！');
            $('#'+ tipId +' span').show();

        }
        else if (codeArr[0] == '300')
        {
            msgArr = msgs['300'][regType][codeArr[1]].split('@');
            $('#'+ tipId +' span').html(msgArr[0]);
            $('#'+ tipId +' span').show();
        }
        return false;
    }

    if (typeof res.msg !== "undefined" && res.msg !== '')
    {
        msgArr = res.msg.split('@');
        if (typeof msgArr[1] === "undefined")
        {
            console.log(msgArr[0]);
        }
        else
        {
            $('#'+ tipId +' span').html(msgArr[0]);
            $('#'+ tipId +' span').show();
        }
        return false;
    }

    if (typeof res.forwardPage === "undefined" || res.forwardPage === '')
    {
        res.forwardPage = '/browser3/reg';
    }
    var passid = $('#password_' + type ).val();
    if (typeof res.loadPage !== "undefined" && res.loadPage !== '' && res.loadPage.length > 0)
    {

        var loadObj = res.loadPage;
        var loadLen = loadObj.length;
        var loadCount = 0;
        for (var loadIndex in  loadObj)
        {
            $.getScript(loadObj[loadIndex], function() {
                ++loadCount;
                if (loadCount == loadLen)
                {
                    if ( typeof chrome == 'object')
                    {
                        var userName = $('#username_' + type).val();
                        var nickName  = '';
                        chrome.sync.onLogin(userName, nickName , 0, "", res.I );
                        chrome.sync.setLoginUserInfo( userName , MD5(passid) );

                        if (chrome.sync.hasOwnProperty('setLoginUserPhoneNumber')) {
                            if(res.phone) {
                                chrome.sync.setLoginUserPhoneNumber(res.phone);
                            }
                        }

                        chrome.sync.setIsAutoLogin(true);
                        chrome.sync.closeDlg();
                    }
                    else
                    {
                        window.location.href = res.forwardPage;
                    }
                }
            });
        }
    }
    else
    {
        if ( typeof chrome == 'object')
        {
            var userName = $('#username_' + type).val();
            var nickName  = '';
            chrome.sync.onLogin(userName, nickName , 0, "", res.I );
            chrome.sync.setLoginUserInfo( userName , MD5(passid)  );

            if (chrome.sync.hasOwnProperty('setLoginUserPhoneNumber')) {
                if(res.phone) {
                    chrome.sync.setLoginUserPhoneNumber(res.phone);
                }
            }

            chrome.sync.setIsAutoLogin(true);
            chrome.sync.closeDlg();
        }
        else
        {
            window.location.href = res.forwardPage;
        }
    }
    return false;


}


/*****************   until functoins   ****************/
function checkPassSame(pass)
{
    var first = pass.substring(0, 1);
    var exp = new RegExp('^' + first + '+$');
    if (exp.test(pass))
    {
        return false;
    }
    if (first == 'a' || first == 'A')
    {
        f = pass.charCodeAt(0);
        for (i = 1; i < pass.length; i++)
        {
            tmp = pass.charCodeAt(i);
            if (tmp - f != i)
            {
                return true;
            }
        }
        return false;
    }
    return true;
}
function passwordGrade(pwd)
{
    var score = 0;
    var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
    var repeatCount = 0;
    var prevChar = '';
    //check length
    var len = pwd.length;
    score += len > 18 ? 18 : len;
    //check type
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (eval('/' + regexArr[i] + '/').test(pwd))
            score += 4;
    }
    //bonus point
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
            score += 2;
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
            score += 2;
    }
    //deduction
    for (var i = 0, num = pwd.length; i < num; i++) {
        if (pwd.charAt(i) == prevChar)
            repeatCount++;
        else
            prevChar = pwd.charAt(i);
    }
    score -= repeatCount * 1;
    return score;
}
function login(n, username)
{
    if (n == 2)
    {
        window.location.href = "http://login.duote.com/login.php";
    }
    else if (n == 3)
    {
        window.location.href = "http://bbs.haozip.com/login.php";
    }
    else if (n == 4)
    {
        window.location.href = "http://bbs.shanhu99.com/logging.php?action=login";
    }
    else if (n == 5)
    {
        window.location.href = "http://ie.2345.com/bbs/logging.php?action=login";
    }
    else
    {
        if (username)
        {
            window.location.href = "http://login.2345.com/login.php?username=" + username;
        }
        else
        {
            window.location.href = "http://login.2345.com/login.php";
        }
    }
}

String.prototype.trim = function()
{
    return this.replace(/(^\s*)|(\s*$)/g, "");
};

String.prototype.cnSize = function()
{
    //换算中文字长
    var arr = this.match(/[^\x00-\xff]/ig);
    return this.length + (arr == null ? 0 : arr.length);
};