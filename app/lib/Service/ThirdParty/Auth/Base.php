<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/5/23
 * Time: 17:42
 */

namespace Service\ThirdParty\Auth;

class Base
{

    private static $necessaryField = [];

    /**
     * 设置必要参数
     * @param array $data 设置必要参数
     * @return array
     */
    public static function SetNecessaryField($data)
    {
        return array_merge(self::$necessaryField, $data);
    }

    /**
     * 获取必要参数
     * @return array
     */
    public static function GetNecessaryField()
    {
        return self::$necessaryField;
    }


    /**
     * 功  能： 发送短信验证码
     *
     * @param string $mid mid
     * @param int $phone phone
     * @return mixed
     */
    public static function SendCode($mid, $phone)
    {
        $memberModel = loadModel("member");
        $phoneAction = loadAction("phone");
        $smsFilterConfig = [
            'mNum' => 15,
            'hNum' => 15,
            'dNum' => 50
        ];
        $smsType = $phoneAction->getSmsType($mid);
        if ($memberModel->checkPhone($phone, true) != 0) {// 手机已存在，发送登录验证码
            $retCode = $phoneAction->sendLoginCode($mid, $phone, $smsType, 453, true, $smsFilterConfig);
        } else {
            $retCode = $phoneAction->sendRegCode($mid, $phone, $smsType, 454, true, $smsFilterConfig);
        }
        return $retCode;
    }
}
