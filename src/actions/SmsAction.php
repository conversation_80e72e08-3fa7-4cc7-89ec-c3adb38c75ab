<?php

/**
 * Copyright (c) 2014,上海二三四五网络科技股份有限公司
 * 摘    要：短信发送类
 * 作    者：wangyi <<EMAIL>>
 * 修 改  者： fansichi<<EMAIL>>
 * 修改日期：2014.01.08
 *
 * == 说明 ==
 *
 * QS：http://qs.2345.net/action/tasks.php?act=detail&id=18963
 * 根据短信的功能以及时效性要求，将短信分为2种类型：验证码、非验证码（通知类）
 * 需要做的：
 * 1.建立2个短信通道，每个通道可以独立配置移动、联通、电信号码使用的短信供应商接口。
 *
 * 各通道供应商配置：
 * (1)验证码：
 * 移动：MAS
 * 联通：建州
 * 电信：建州
 *
 * (2)非验证码：（注意：非验证码通道不允许使用MAS）
 * 移动：建州
 * 联通：建州
 * 电信：建州
 *  具体的文档见 http://blog.jifen.2345.com/?p=150
 */


class SmsAction extends Action
{

    protected $multi = 0;
    protected $redis;
    protected $phone;
    protected $msg;
    protected $smsType;
    protected $appType;
    protected $mid;
    protected $pid;
    protected $clientIp;
    protected $positionId;
    protected $passid;
    protected $logId;
    protected $submitParam = array();
    protected $isInit = false;
    protected $phoneLogIds = array();
    protected $sessionId;
    protected $forceReturnJson = false;

    //const CHECKCODE_LIMIT = 8;
    const CHECKCODE_LIMIT = 4;

    const CHECKCODE_WORD = '验证码';

    public static $errStr = array(
        1   => '发送成功',
        0   => '发送失败',
        -1  => '通道不支持发送移动短信',
        -2  => '通道不支持发送电信短信',
        -3  => '通道不支持发送联通短信',
        -4  => '移动通道不可用',
        -5  => '电信通道不可用',
        -6  => '联通通道不可用',
        -7  => '手机号码不正确',
        -8  => '短信内容太短',
        -9  => '非法的短信类型',
        -10 => '达到日发送上限',
        -11 => '远程IP地址验证失败',
        -12 => '手机号在黑名单内',
        -13 => '项目id为空',
        -14 => 'sms短信类型为空',
        -15 => '通道类名配置错误',
        -18 => '短信内容不能为空',
        -19 => '用户客户端IP不能为空',
        -20 => '此用户已退订',
        -21 => '短信请求接收失败',
        -22 => '等待发送',
        -24 => '通道不在可用时段内',
        -25 => '短信内容超出限制',
        -26 => '通道不支持普通发送',
        -27 => '重复发送',
        -28 => '流水号不可用',
    );

    /*
    * 不同内容群发，不能超过100个。
    * 群发短信,单次请求手机号限制请控制在5000个以内(建议500个)!'
    */
    public static $multiErrStr = array(
        2   => '部分成功',
        -31 => '群发短信手机号数量超过限制',
        -32 => '手机号不正确或者存在黑名单或者超过日累计条数限制',
        -33 => '群发数据格式不正确',
        -34 => 'smsType 不支持群发',
        -35 => 'smsType 不支持不同内容群发',
        -36 => '变量群发数据格式不正确',
        -37 => 'smsType 不支持变量短信群发',
    );

    public static $errno = 0; //错误编号

    /**
     * SmsAction constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->redis = RedisAction::connect();
    }

    /**
     * 短信内容检测
     *
     * @param string $content 短信内容
     *
     * @return boolean
     */
    private function checkContent($content)
    {
        if (!$content || strlen(trim($content)) < 2)
        {
            return false;
        }

        return true;
    }

    /**
     * 手机号码
     *
     * @param string $phone 手机号码
     *
     * @return boolean
     */
    private function checkPhone($phone)
    {
        $res = preg_match('#^1[3,4,5,6,7,8,9]{1}[\d]{9}$#', $phone) || preg_match('#^[0]{2}[\d]*$#', $phone);
        return $res;
    }

    /**
     * 检查手机号合法和黑名单
     *
     * @return array
     */
    private function checkMultiPhone()
    {
        $getRedisAction = GetRedisAction::getInstance();
        $legalPhoneList = $illegalPhoneList = array();
        foreach ($this->phone as $phone)
        {
            if (!trim($phone))
            {
                continue;
            }
            if ($this->checkPhone($phone))
            {
                if ($getRedisAction->isInBlackList($phone))
                {
                    $illegalPhoneList[] = $phone;
                }
                else
                {
                    $legalPhoneList[] = $phone;
                }
            }
            else
            {
                $illegalPhoneList[] = $phone;
            }
        }

        return array($legalPhoneList, $illegalPhoneList);
    }

    /**
     * 访问权限控制
     * @return bool
     */
    private function accessCheck()
    {
        $GetRedisAction = GetRedisAction::getInstance();
        if (PHP_SAPI !== 'cli')
        {
            $this->serverIp = get_client_ip();
        }
        elseif (!isset($this->serverIp))
        {
            $this->serverIp = "";
        }
        //获取ip白名单
        $ips = $GetRedisAction->isIpWhiteList($this->serverIp);
        if ($ips == true)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     * 发送接口
     *
     * @return mixed
     */
    public function send()
    {
        $getRedisAction = GetRedisAction::getInstance();

        // 为了兼容返回值... 手机号码格式不正确和黑名单
        $illegalPhoneList = array();
        if (!$this->multi)
        {
            //是否存在黑名单
            if ($getRedisAction->isInBlackList($this->phone[0]))
            {
                return $this->formatReturn(array(-12, self::$errStr[-12]));
            }
            //检查是否是合法的手机号码
            if (!$this->checkPhone($this->phone[0]))
            {
                return $this->formatReturn(array(-7, self::$errStr[-7]));
            }
        }
        else
        {
            //是否存在黑名单和非法手机号
            list($legalPhoneList, $illegalPhoneList) = $this->checkMultiPhone();
            if (empty($legalPhoneList))
            {
                // 全部不合法
                return $this->formatReturn(array(-32, self::$multiErrStr[-32]));
            }

            // phone 赋值为合法手机号列表
            $this->phone = $legalPhoneList;
        }

        //短信内容检查  类型为2时(语音)可以不传msg
        if (!$this->checkContent($this->msg) && $this->appType !== 2)
        {
            return $this->formatReturn(array(-8, self::$errStr[-8]));
        }

        //查看传来的sms短信类型 是否在该项目短信类型之中
        if (!in_array($this->smsType, [251, 252, 253, 254, 255, 256])) {
            $smsTypes = $getRedisAction->getSmsConfigProjectTypeList($this->pid);
            if (!is_array($smsTypes) || !in_array($this->smsType, $smsTypes))
            {
                return $this->formatReturn(array(-9, self::$errStr[-9]));
            }
        }

        //ip白名单 检测
        if (!$this->accessCheck())
        {
            return $this->formatReturn(array(-11, self::$errStr[-11]));
        }

        //获取类型配置
        $smsConfigs = $getRedisAction->getSmsTypeConfig($this->smsType);
        //对于配置了多个运营商的类型，随机分配运营商
        $smsConfig = $this->randomTypeConfig($smsConfigs);
        $restrictPhoneList = array();

        foreach ($this->phone as $key => $phone) {
            $getPhoneWhite = $getRedisAction->isPhoneWhiteList($phone);
            //看该type是否是 验证码类型
            if (empty($getPhoneWhite) && $smsConfig['is_restrict'] == 1)
            {
                $smsLimit = $getRedisAction->getSmsNumByPhoneType(
                    $phone,
                    $this->smsType,
                    date("Y-m-d")
                );
                $limitNums = !empty($smsConfig['restrict_nums']) ? $smsConfig['restrict_nums'] : self::CHECKCODE_LIMIT;
                if ($smsLimit >= $limitNums)
                {
                    if ($this->multi)
                    {
                        unset($this->phone[$key]);
                        $restrictPhoneList[] = $phone;
                    }
                    else
                    {
                        return $this->formatReturn(array(-10, self::$errStr[-10]));
                    }
                }
            }
        }

        // 不合规号码 只记录
        if (!empty($illegalPhoneList))
        {
            $smsModel = SmsModel::getInstance();
            $logIds = array();
            foreach ($illegalPhoneList as $phone)
            {
                $logIds[] = $this->phoneLogIds[$phone];
            }
            $updateData = array(
                "logIds"        => $logIds,
                "send_status"   => -32,
                "send_response" => self::$multiErrStr[-32],
            );
            $isTrue = $smsModel->upSmsLogs($updateData);
        }

        // 超限制 只记录
        if (!empty($restrictPhoneList))
        {
            $smsModel = SmsModel::getInstance();
            $logIds = array();
            foreach ($restrictPhoneList as $phone)
            {
                $logIds[] = $this->phoneLogIds[$phone];
            }
            $updateData = array(
                "logIds"        => $logIds,
                "send_status"   => -32,
                "send_response" => self::$multiErrStr[-32],
            );
            $isTrue = $smsModel->upSmsLogs($updateData);
        }

        if (empty($this->phone))
        {
            // 已经没有有效的手机号了, 返回错误
            return \MsgAction::returnJsonData(array('status' => -32, 'msg' => \SmsAction::$multiErrStr[-32]), $this->forceReturnJson);
        }
        $this->phone = array_merge($this->phone);

        /*
        退订的 预留接口  以后优化
        if ($getRedisAction->checkTDPhone($this->phone, $this->smsType))
        {
            return $this->formatReturn(array(-20, self::$errStr[-20]));
        }
        */

        // 通道关闭检测
        $check = $this->checkChannelStatus($smsConfig);
        if ($check[0] < 0)
        {
            return $this->formatReturn($check);
        }
        //发送移动短信
        if ($this->multi || $this->isMobilePhone($this->phone[0]))
        {
            //通道不支持
            if ($smsConfig['mobile_support_mobile'] == 0)
            {
                return $this->formatReturn(array(-1, self::$errStr[-1]));
            }
            if (isset($smsConfig['mobile_timeQuantum']) && !$this->checkTimeQuantum($smsConfig['mobile_timeQuantum']))
            {
                return $this->formatReturn(array(-24, self::$errStr[-24]));
            }
            $sign = $smsConfig['mobile_sign_type'] == 0 ? $smsConfig['mobile_sign'] : "";
            $smsCount = $this->smsCount($this->msg . $smsConfig['mobile_sign']);
            //通道类名放到数据库中
            $class = $smsConfig['mobile_action_name'];
            if (empty($class) || !class_exists($class))
            {
                return $this->formatReturn(array(-15, self::$errStr[-15]));
            }
            $channel = new $class(
                $smsConfig['mobile_username'],
                $smsConfig['mobile_password'],
                $smsConfig['mobile_api_url']
            );
            $channelId = $smsConfig['mobile_id'];
            //如果配置类型是语音且传递的非语音类型,强制转换成语音
            if (!empty($smsConfig['mobile_appType']) && $smsConfig['mobile_appType'] == 2 && $this->appType != 2)
            {
                $this->appType = 2;
            }
        } //电信
        elseif ($this->isTelecomPhone($this->phone[0]))
        {
            //通道不支持
            if ($smsConfig['telecom_support_telecom'] == 0)
            {
                return $this->formatReturn(array(-2, self::$errStr[-2]));
            }
            if (isset($smsConfig['telecom_timeQuantum']) && !$this->checkTimeQuantum($smsConfig['telecom_timeQuantum']))
            {
                return $this->formatReturn(array(-24, self::$errStr[-24]));
            }
            $sign = $smsConfig['telecom_sign_type'] == 0 ? $smsConfig['telecom_sign'] : "";
            $smsCount = $this->smsCount($this->msg . $smsConfig['telecom_sign']);
            //通道类名放到数据库中
            $class = $smsConfig['telecom_action_name'];
            if (empty($class) || !class_exists($class))
            {
                return $this->formatReturn(array(-15, self::$errStr[-15]));
            }

            $channel = new $class(
                $smsConfig['telecom_username'],
                $smsConfig['telecom_password'],
                $smsConfig['telecom_api_url']
            );

            $channelId = $smsConfig['telecom_id'];
            //如果配置类型是语音且传递的非语音类型,强制转换成语音
            if (!empty($smsConfig['telecom_appType']) && $smsConfig['telecom_appType'] == 2 && $this->appType != 2)
            {
                $this->appType = 2;
            }
        } //其他的都认为是联通手机
        elseif ($this->isUnicomPhone($this->phone[0]))
        {
            //通道不支持
            if ($smsConfig['unicom_support_unicom'] == 0)
            {
                return $this->formatReturn(array(-3, self::$errStr[-3]));
            }
            if (isset($smsConfig['unicom_timeQuantum']) && !$this->checkTimeQuantum($smsConfig['unicom_timeQuantum']))
            {
                return $this->formatReturn(array(-24, self::$errStr[-24]));
            }
            $sign = $smsConfig['unicom_sign_type'] == 0 ? $smsConfig['unicom_sign'] : "";
            $smsCount = $this->smsCount($this->msg . $smsConfig['unicom_sign']);
            //通道类名放到数据库中
            $class = $smsConfig['unicom_action_name'];
            if (empty($class) || !class_exists($class))
            {
                return $this->formatReturn(array(-15, self::$errStr[-15]));
            }

            $channel = new $class(
                $smsConfig['unicom_username'],
                $smsConfig['unicom_password'],
                $smsConfig['unicom_api_url']
            );

            $channelId = $smsConfig['unicom_id'];
            //如果配置类型是语音且传递的非语音类型,强制转换成语音
            if (!empty($smsConfig['unicom_appType']) && $smsConfig['unicom_appType'] == 2 && $this->appType != 2)
            {
                $this->appType = 2;
            }
        }
        else
        {
            return $this->formatReturn(array(-7, self::$errStr[-7]));
        }

        if ($this->appType == 2)
        {
            $apiData = array(
                'voiceType' => $this->voiceType,
                'voiceTpl'  => $this->voiceTpl,
            );
            $channel->setApiData($apiData);
        }
        \Octopus\PdoEx::delInstance(DATABASE_SMS);

        $timeoutMonitorAction = \TimeoutMonitorAction::getInstance();
        $timeData = array(
            'channelId' => $channelId,
            'pid'       => $this->pid,
        );
        $phone = $this->phone[0];
        //set missionNum and batchNum
        if (method_exists($channel, 'setApiData'))
        {
            $apiData = array(
                'sessionId' => $this->sessionId,
                'phoneLogIds'  => $this->phoneLogIds,
            );
            $channel->setApiData($apiData);
        }
        //执行发送操作
        $timeoutMonitorAction->setLogStartTime($this->phoneLogIds[$phone], $phone, $timeData);
        if ($this->multi)
        {
            if (!method_exists($channel, 'multiPhoneSend'))
            {
                return $this->formatReturn(array(-34, self::$multiErrStr[-34]), "", true);
            }
            $ret = $channel->multiPhoneSend($this->phone, $this->msg, $sign);
        }
        else
        {
            if (!method_exists($channel, 'send'))
            {
                return $this->formatReturn(array(-26, self::$errStr[-26]), "", true);
            }
            $ret = $channel->send(implode(",", $this->phone), $this->msg, $sign);
        }
        $timeoutMonitorAction->setLogEndTime($this->phoneLogIds[$phone], $phone, $timeData);

        if ($this->multi)
        {
            $failCount = 0;
            foreach ($this->phone as $key => $phone)
            {
                //todo 批量入库和更新
                $msgId = $channel->getMultiReturnMsgId($phone);
                $msgStatusArr = $channel->getMultiReturnMsg($phone);
                $result = $msgStatusArr['result'];
                $logId = $this->phoneLogIds[$phone];
                $insert = array(
                    "channel"      => $channelId,
                    "account"      => $channel->user ?? '',
                    "sendStatus"   => $result,
                    "sendResponse" => $result ? self::$errStr[1] : self::$errStr[0],
                    'businessId'   => $this->positionId,
                    'msgId'        => $msgId,
                    'codeTime'     => date('Y-m-d H:i:s'),
                    'codeStatus'   => $msgStatusArr['codeStatus'],
                    'codeDesc'     => $msgStatusArr['codeDesc'],
                    'smsCount'     => $smsCount,
                    'mid'          => $this->mid,
                    'appType'      => $this->appType,
                    'logId'        => $logId,
                );

                if ($msgId)
                {
                    //将msgId:phone => logId的对应关系写入redis的set，减轻数据库查询的压力
                    $msgIdAndPhoneKey = "CHANNEL_DATA:" . $msgId . ':' . $phone;
                    $this->redis->setex($msgIdAndPhoneKey, 1728000, $logId);
                }
                $smsModel = SmsModel::getInstance();
                $isTrue = $smsModel->addSmsLog($insert, $channelId);
                $smsModel->setSmsLog2Redis($insert['logId'], $insert);
                if ($isTrue)
                {
                    if (!$msgId || !$msgStatusArr || !$result)
                    {
                        $failCount++;
                        continue;
                    }
                    else
                    {
                        //发送短信后 设置该手机该短信类型发送数量加1
                        $getRedisAction->setSmsNumByPhoneType($phone, $this->smsType, date("Y-m-d"));
                    }
                }
                else
                {
                    $failCount++;
                    $illegalPhoneList[] = $phone;
                }
            }
            if (count($this->phone) == $failCount)
            {
                return $this->formatReturn(array(0, self::$errStr[0]), "", true);
            }
            else
            {
                if (count($illegalPhoneList) || count($restrictPhoneList) || $failCount)
                {
                    $phoneLogIds = array();
                    foreach ($this->phone as $key => $val)
                    {
                        if (isset($this->phoneLogIds[$val]))
                        {
                            $phoneLogIds[$val] = $this->phoneLogIds[$val];
                        }
                    }

                    $data = array(
                        "illegal_phone" => array_merge($illegalPhoneList, $restrictPhoneList),
                        "phoneLogIds"   => $phoneLogIds,
                    );

                    return $this->formatReturn(array(2, self::$multiErrStr[2]), $data, true);
                }
                else
                {
                    $data = array(
                        "phoneLogIds" => $this->phoneLogIds,
                    );

                    return $this->formatReturn(array(1, self::$errStr[1]), $data, true);
                }
            }
        }
        else
        {
            $msgStatusArr = $channel->RetrunMsg();
            if ($this->appType == 2)
            {
                $this->msg = $channel->getRandomCode();
            }
            $phone = $this->phone[0];
            $logIds = array($this->phoneLogIds[$phone]);
            $insert = array(
                "channel"      => $channelId,
                "account"      => $channel->user ?? '',
                "sendStatus"   => $ret,
                "sendResponse" => $ret ? self::$errStr[1] : self::$errStr[0],
                'businessId'   => $this->positionId,
                'msgId'        => $channel->returnMsgId(),
                'codeTime'     => date('Y-m-d H:i:s'),
                'codeStatus'   => $msgStatusArr['codeStatus'],
                'codeDesc'     => $msgStatusArr['codeDesc'],
                'smsCount'     => $smsCount,
                'mid'          => $this->mid,
                'appType'      => $this->appType,
                'logId'        => $this->phoneLogIds[$phone],
            );
            if ($channel->returnMsgId())
            {
                //将msgId:phone => logId的对应关系写入redis的set，减轻数据库查询的压力
                $msgIdAndPhoneKey = "CHANNEL_DATA:" . $channel->returnMsgId() . ':' . $phone;
                $this->redis->setex($msgIdAndPhoneKey, 1728000, $this->phoneLogIds[$phone]);
            }

            $smsModel = SmsModel::getInstance();
            $isTrue = $smsModel->addSmsLog($insert, $channelId);
            $smsModel->setSmsLog2Redis($insert['logId'], $insert);
            if ($isTrue)
            {
                //发送短信后 设置该手机该短信类型发送数量加1
                if ($ret)
                {
                    $getRedisAction->setSmsNumByPhoneType($phone, $this->smsType, date("Y-m-d"));
                }
            }
            $phoneLogIds = array();
            if ($ret)
            {
                //历史遗留问题，约定的json返回中此处应为字符串
                $phoneLogIds[$phone] = (string)$this->phoneLogIds[$phone];
            }

            return $this->formatReturn(
                array($ret, $ret ? self::$errStr[1] : self::$errStr[0]),
                array("phoneLogIds" => $phoneLogIds),
                true
            );
        }
    }

    /**
     * 不同内容群发
     *
     * @return bool
     */
    public function batchSend()
    {
        ///error_reporting(0);
        $getRedisAction = GetRedisAction::getInstance();
        list($legalPhoneList, $illegalPhoneList) = $this->checkMultiPhone();
        if (empty($legalPhoneList))
        {
            return $this->formatReturn(array(-32, self::$multiErrStr[-32]));
        }
        $failResponse = array();
        $successResponse = array();
        $duplicatePhoneLogId = array();
        foreach ($this->multiContent as $packageId => $value)
        {
            list($phone, $msg, $logId) = $value;
            if (in_array($phone, $illegalPhoneList))
            {
                //todo 更新 my_sms_log 手机号不合法！
                unset($this->multiContent[$packageId]);
                $failResponse[$packageId] = array("logId" => "", "phone" => $phone, "msg" => "手机号黑名单或者不合法");
            }
            else
            {
                if (isset($duplicatePhoneLogId[$phone]))
                {
                    unset($this->multiContent[$packageId]);
                    $failResponse[$packageId] = array(
                        "logId" => $duplicatePhoneLogId[$phone],
                        "phone" => $phone,
                        "msg"   => "手机号重复",
                    );
                }
                else
                {
                    $duplicatePhoneLogId[$phone] = $logId;
                }
            }
        }

        // 查看smsType 是否在 pid 短信类型之中
        $smsTypes = $getRedisAction->getSmsConfigProjectTypeList($this->pid);
        if (!is_array($smsTypes) || !in_array($this->smsType, $smsTypes))
        {
            return $this->formatReturn(array(-9, self::$errStr[-9]));
        }

        // ip白名单 检测
        if (!$this->accessCheck())
        {
            return $this->formatReturn(array(-11, self::$errStr[-11]));
        }

        //获取类型配置
        $smsConfigs = $getRedisAction->getSmsTypeConfig($this->smsType);
        $smsConfig = $this->randomTypeConfig($smsConfigs);
        //通道关闭检测
        $check = $this->checkChannelStatus($smsConfig);
        if ($check[0] < 0)
        {
            return $this->formatReturn($check);
        }
        if (isset($smsConfig['mobile_timeQuantum']) && !$this->checkTimeQuantum($smsConfig['mobile_timeQuantum']))
        {
            return $this->formatReturn(array(-24, self::$errStr[-24]));
        }
        $sign = $smsConfig['mobile_sign_type'] == 0 ? $smsConfig['mobile_sign'] : "";
        $smsCount = $this->smsCount($this->msg . $smsConfig['mobile_sign']);
        $class = $smsConfig['mobile_action_name'];
        // 通道类名
        if (empty($class) || !class_exists($class))
        {
            return $this->formatReturn(array(-15, self::$errStr[-15]));
        }
        // 群发使用相同的 移动的通道
        $channel = new $class(
            $smsConfig['mobile_username'], $smsConfig['mobile_password'], $smsConfig['mobile_api_url']
        );
        $channelId = $smsConfig['mobile_id'];
        \Octopus\PdoEx::delInstance(DATABASE_SMS);
        if (!method_exists($channel, 'batchSend'))
        {
            return $this->formatReturn(array(-35, self::$multiErrStr[-35]));
        }
        // 发送
        $ret = $channel->batchSend($this->multiContent, $sign);
        if (empty($illegalPhoneList))
        {
            if ($ret == 2)
            {
                $sendResponse = $ret ? self::$multiErrStr[2] : self::$errStr[0];
            }
            else
            {
                $sendResponse = $ret ? self::$errStr[1] : self::$errStr[0];
            }
        }
        else
        {
            $ret = $ret == 1 ? 2 : $ret;
            $sendResponse = $ret ? self::$multiErrStr[2] : self::$errStr[0];
        }

        $smsModel = SmsModel::getInstance();

        foreach ($this->multiContent as $packageId => $value)
        {
            list($phone, $msg, $logId) = $value;
            $msgId = $channel->getMultiReturnMsgId($phone);
            $msgStatusArr = $channel->getMultiReturnMsg($phone);
            $returnMsg = $sendResponse;
            if (isset($msgStatusArr['codeDesc']))
            {
                $returnMsg = $msgStatusArr['codeDesc'];
            }
            if (($ret == 1 || $ret == 2) && $msgId)
            {
                $successResponse[$packageId] = array("logId" => (string)$logId, "phone" => $phone, "msg" => $returnMsg);
            }
            else
            {
                $failResponse[$packageId] = array("logId" => (string)$logId, "phone" => $phone, "msg" => $returnMsg);
            }
            $insert = array(
                "channel"      => $channelId,
                "account"      => $channel->user ?? '',
                "sendStatus"   => $ret,
                "sendResponse" => $sendResponse,
                'businessId'   => $this->positionId,
                'msgId'        => $msgId,
                'codeTime'     => date('Y-m-d H:i:s'),
                'codeStatus'   => $msgStatusArr['codeStatus'],
                'codeDesc'     => $msgStatusArr['codeDesc'],
                'smsCount'     => $smsCount,
                'mid'          => $this->mid,
                'appType'      => $this->appType,
                'logId'        => $logId,
            );
            $isTrue = $smsModel->addSmsLog($insert, $channelId);
            $smsModel->setSmsLog2Redis($insert['logId'], $insert);
            $msgIdAndPhoneKey = "CHANNEL_DATA:" . $msgId . ':' . $phone;
            $this->redis->setex($msgIdAndPhoneKey, 1728000, $logId);

            if ($isTrue)
            {
                //发送短信后 设置该手机该短信类型发送数量加1
                if ($ret)
                {
                    $getRedisAction->setSmsNumByPhoneType($phone, $this->smsType, date("Y-m-d"));
                }
            }
        }

        if (empty($illegalPhoneList))
        {
            if ($ret == 2)
            {
                $sendResponse = $ret ? self::$multiErrStr[2] : self::$errStr[0];
            }
            else
            {
                $sendResponse = $ret ? self::$errStr[1] : self::$errStr[0];
            }

            $this->formatReturn(
                array($ret, $sendResponse),
                array(
                    "success" => $successResponse,
                    "fail"    => $failResponse,
                ),
                true
            );
        }
        else
        {
            $this->formatReturn(
                array(
                    $ret,
                    $ret ? self::$multiErrStr[2] : self::$errStr[0],
                ),
                array(
                    "success" => $successResponse,
                    "fail"    => $failResponse,
                ),
                true
            );
        }

        return true;
    }

    /**
     * 不同内容群发
     *
     * @return bool
     */
    public function variableMsgSend()
    {
        $getRedisAction = GetRedisAction::getInstance();
        list($legalPhoneList, $illegalPhoneList) = $this->checkMultiPhone();
        if (empty($legalPhoneList))
        {
            return $this->formatReturn(array(-32, self::$multiErrStr[-32]));
        }
        $failResponse = array();
        $successResponse = array();
        $duplicatePhoneLogId = array();
        foreach ($this->multiContent as $packageId => $value)
        {
            list($phone, $variables, $logId) = $value;
            if (in_array($phone, $illegalPhoneList))
            {
                //todo 更新 my_sms_log 手机号不合法！
                unset($this->multiContent[$packageId]);
                $failResponse[$packageId] = array("logId" => "", "phone" => $phone, "msg" => "手机号黑名单或者不合法");
            }
            else
            {
                if (isset($duplicatePhoneLogId[$phone]))
                {
                    unset($this->multiContent[$packageId]);
                    $failResponse[$packageId] = array(
                        "logId" => $duplicatePhoneLogId[$phone],
                        "phone" => $phone,
                        "msg"   => "手机号重复",
                    );
                }
                else
                {
                    $duplicatePhoneLogId[$phone] = $logId;
                }
            }
        }

        // 查看smsType 是否在 pid 短信类型之中
        $smsTypes = $getRedisAction->getSmsConfigProjectTypeList($this->pid);
        if (!is_array($smsTypes) || !in_array($this->smsType, $smsTypes))
        {
            return $this->formatReturn(array(-9, self::$errStr[-9]));
        }

        // ip白名单 检测
        if (!$this->accessCheck())
        {
            return $this->formatReturn(array(-11, self::$errStr[-11]));
        }

        //获取类型配置
        $smsConfigs = $getRedisAction->getSmsTypeConfig($this->smsType);
        $smsConfig = $this->randomTypeConfig($smsConfigs);
        //通道关闭检测
        $check = $this->checkChannelStatus($smsConfig);
        if ($check[0] < 0)
        {
            return $this->formatReturn($check);
        }
        if (isset($smsConfig['mobile_timeQuantum']) && !$this->checkTimeQuantum($smsConfig['mobile_timeQuantum']))
        {
            return $this->formatReturn(array(-24, self::$errStr[-24]));
        }
        $sign = $smsConfig['mobile_sign_type'] == 0 ? $smsConfig['mobile_sign'] : "";
        $smsCount = $this->smsCount(str_replace('{$var}', '', $this->template) . implode('', $variables) . $smsConfig['mobile_sign']);
        $class = $smsConfig['mobile_action_name'];
        // 通道类名
        if (empty($class) || !class_exists($class))
        {
            return $this->formatReturn(array(-15, self::$errStr[-15]));
        }
        // 群发使用相同的 移动的通道
        $channel = new $class(
            $smsConfig['mobile_username'], $smsConfig['mobile_password'], $smsConfig['mobile_api_url']
        );
        $channelId = $smsConfig['mobile_id'];
        \Octopus\PdoEx::delInstance(DATABASE_SMS);
        if (!method_exists($channel, 'variableMsgSend'))
        {
            return $this->formatReturn(array(-37, self::$multiErrStr[-37]));
        }
        // 发送
        $ret = $channel->variableMsgSend($this->multiContent, $this->template, $sign);
        if (empty($illegalPhoneList))
        {
            if ($ret == 2)
            {
                $sendResponse = $ret ? self::$multiErrStr[2] : self::$errStr[0];
            }
            else
            {
                $sendResponse = $ret ? self::$errStr[1] : self::$errStr[0];
            }
        }
        else
        {
            $ret = $ret == 1 ? 2 : $ret;
            $sendResponse = $ret ? self::$multiErrStr[2] : self::$errStr[0];
        }

        $smsModel = SmsModel::getInstance();

        foreach ($this->multiContent as $packageId => $value)
        {
            list($phone, $variables, $logId) = $value;
            $msgId = $channel->getMultiReturnMsgId($phone);
            $msgStatusArr = $channel->getMultiReturnMsg($phone);
            if (($ret == 1 || $ret == 2) && $msgId)
            {
                $successResponse[$packageId] = array("logId" => strval($logId), "phone" => $phone, "msg" => "");
            }
            else
            {
                $returnMsg = $sendResponse;
                if ($msgStatusArr['codeDesc'])
                {
                    $returnMsg = $msgStatusArr['codeDesc'];
                }
                $failResponse[$packageId] = array("logId" => strval($logId), "phone" => $phone, "msg" => $returnMsg);
            }
            $insert = array(
                "channel"      => $channelId,
                "account"      => $channel->user ?? '',
                "sendStatus"   => $ret,
                "sendResponse" => $sendResponse,
                'businessId'   => $this->positionId,
                'msgId'        => $msgId,
                'codeTime'     => date('Y-m-d H:i:s'),
                'codeStatus'   => $msgStatusArr['codeStatus'],
                'codeDesc'     => $msgStatusArr['codeDesc'],
                'smsCount'     => $smsCount,
                'mid'          => $this->mid,
                'appType'      => $this->appType,
                'logId'        => $logId,
            );
            $isTrue = $smsModel->addSmsLog($insert, $channelId);
            $smsModel->setSmsLog2Redis($insert['logId'], $insert);
            $msgIdAndPhoneKey = "CHANNEL_DATA:" . $msgId . ':' . $phone;
            $this->redis->setex($msgIdAndPhoneKey, 1728000, $logId);

            if ($isTrue)
            {
                //发送短信后 设置该手机该短信类型发送数量加1
                if ($ret)
                {
                    $getRedisAction->setSmsNumByPhoneType($phone, $this->smsType, date("Y-m-d"));
                }
            }
        }

        if (empty($illegalPhoneList))
        {
            if ($ret == 2)
            {
                $sendResponse = $ret ? self::$multiErrStr[2] : self::$errStr[0];
            }
            else
            {
                $sendResponse = $ret ? self::$errStr[1] : self::$errStr[0];
            }

            $this->formatReturn(
                array($ret, $sendResponse),
                array(
                    "success" => $successResponse,
                    "fail"    => $failResponse,
                ),
                true
            );
        }
        else
        {
            $this->formatReturn(
                array(
                    $ret,
                    $ret ? self::$multiErrStr[2] : self::$errStr[0],
                ),
                array(
                    "success" => $successResponse,
                    "fail"    => $failResponse,
                ),
                true
            );
        }

        return true;
    }

    /**
     * 通道检测
     *
     * @param array $config config
     *
     * @return array|bool
     */
    private function checkChannelStatus($config)
    {
        if ($config['mobile_status'] == 0)
        {
            return $this->formatReturn(array(-4, self::$errStr[-4]));
        }
        if ($config['telecom_status'] == 0)
        {
            return $this->formatReturn(array(-5, self::$errStr[-5]));
        }
        if ($config['unicom_status'] == 0)
        {
            return $this->formatReturn(array(-6, self::$errStr[-6]));
        }

        return array(1, 'statusOK');
    }

    /**
     * 统一的格式返回
     *
     * @param array  $arr   array
     * @param string $data  data
     * @param bool   $noTag noTag
     *
     * @return bool
     */
    public function formatReturn($arr, $data = "", $noTag = false)
    {
        //历史遗留问题，约定的返回json此处应为字符串
        $errorData = array('status' => (string)$arr[0], 'msg' => (string)$arr[1]);
        if (!empty($data))
        {
            $errorData["data"] = $data;
        }
        if (!$noTag)
        {
            $smsModel = SmsModel::getInstance();
            if (!$this->isInit)
            {
                $this->init($this->getSubmitData());
                $sessionId = uniqid();
                $insertData = array();
                $column = array(
                    "phone",
                    "text",
                    "sessionid",
                    "type",
                    "send_time",
                    "send_status",
                    "send_response",
                    "passid",
                    "pid",
                    'business_id',
                    'client_ip',
                    'server_ip',
                );
                foreach ($this->phone as $phone)
                {
                    $insertData[$phone] = array(
                        $phone,
                        $this->msg,
                        $sessionId,
                        $this->smsType,
                        date("Y-m-d H:i:s"),
                        $arr[0],
                        $arr[1],
                        $this->passid,
                        $this->pid,
                        $this->positionId,
                        $this->clientIp,
                        !empty($this->serverIp) ? $this->serverIp : get_client_ip(),
                    );
                }
                $isTrue = $smsModel->setSmsLogs($insertData, $column);
            }
            else
            {
                $updateData = array(
                    "logIds"        => array_values($this->phoneLogIds),
                    "send_status"   => $arr[0],
                    "send_response" => $arr[1],
                );
                $isTrue = $smsModel->upSmsLogs($updateData);
            }
        }

        //手机号码不正确(-7) 记录日志
        if ($this->phone && $errorData['status'] == \Service\Func\RequestFunc::PHONE_NUMBER_NOT_CORRECT) {
            \WebLogger\Facade\LoggerFacade::info('短信发送失败(' . $errorData['status'] . ')', ['phone' => $this->phone]);
        }

        return \MsgAction::returnJsonData($errorData, $this->forceReturnJson);
    }

    /**
     * 判断是否移动手机号
     *
     * @param string $phone 电话号码
     *
     * @return bool
     */
    private function isMobilePhone($phone)
    {
        return FuncAction::isMPhone($phone);
    }

    /**
     * 判断是否电信手机号
     *
     * @param string $phone 电话号码
     *
     * @return bool
     */
    private function isTelecomPhone($phone)
    {
        return FuncAction::isChinaTelecomPhone($phone);
    }

    /**
     * 判断是否联通手机号
     *
     * @param string $phone 电话号码
     *
     * @return bool
     */
    private function isUnicomPhone($phone)
    {
        return FuncAction::isChinaUnicomPhone($phone);
    }


    /**
     * 判断是否联通手机号
     *
     * @param string $msg msg
     *
     * @return int
     */
    private function smsCount($msg)
    {
        $msgLen = mb_strlen($msg, 'GBK');
        if ($msgLen <= 70)
        {
            return 1;
        }
        else
        {
            return ceil($msgLen / 67);
        }
    }

    /**
     * 校验通道可使用时间段
     * @param string $timeStr timeQuantum
     * @return int
     */
    private function checkTimeQuantum($timeStr)
    {
        preg_match('/^([0-2][\d]):([0-5][\d])([+-])([0-2][\d]):([0-5][\d])$/', $timeStr, $timeMatch);
        if (!isset($timeMatch[0]))
        {
            return true;
        }
        $startTime = $timeMatch[1] * 60 + $timeMatch[2];
        $endTime = $timeMatch[4] * 60 + $timeMatch[5];
        $nowTime = date('H') * 60 + date('i');
        if ($timeMatch[3] === '-' && ($nowTime < $startTime || $nowTime > $endTime))
        {
            return false;
        }
        if ($timeMatch[3] === '+' && ($nowTime > $startTime && $nowTime < $endTime))
        {
            return false;
        }
        return true;
    }

    public function GetOneConfig($smsConfigs)
    {
        return $this->randomTypeConfig($smsConfigs);
    }
    /**
     * @param array $smsConfigs smsConfigs
     *
     * @return array
     */
    private function randomTypeConfig($smsConfigs)
    {
        $list = array();
        $sum = 0;
        foreach ($smsConfigs as $index => $smsConfig)
        {
            $list[$index] = $smsConfig['percent'];
            $sum += $smsConfig['percent'];
        }
        $randNum = rand(1, $sum);
        foreach ($list as $k => $v)
        {
            $randNum -= $v;
            if ($randNum <= 0)
            {
                return $smsConfigs[$k];
            }
        }
        return $smsConfigs[0];
    }

    /**
     * @param mixed $items items
     *
     * @return bool
     */
    public static function addChannelCallbackStatusMulti($items)
    {
        $SmsLogModel = \SmsInterface\SmsLogModel::getInstance();

        return $SmsLogModel->addChannelCallbackStatusMulti($items);
    }
}
