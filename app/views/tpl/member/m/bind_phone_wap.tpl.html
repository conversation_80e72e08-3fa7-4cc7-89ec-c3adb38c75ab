{{include file = "member/m/head.tpl.html"}}
    {{if $pageArray.info.phone||$pageArray.info.phone_redundancy}}
    <article class="g-user-bd">
        <div class="g-user-result bind-sec">
            <h1><i></i>绑定手机成功</h1>
            <p id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a></p>
        </div>
    </article>
    <script type="text/javascript">
        var times = 3;
        setInterval(function() {
            if (times > 0) {
                $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
            } else {
                window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
            }
            times--;
        }, 1000);
    </script>
    {{else}}
    <article class="g-user-bd">
        <header>
            <h1 class="g-user-title">绑定手机</h1>
        </header>
        <form action="/member/bindPhone/do" id="bind-form" class="g-user-form" method="post" onsubmit="return checkAll();">
            <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
            <div class="form-item" id="phone-item">
                <input type="tel" id="phone" name="phone" class="form-ipt" placeholder="请输入手机号">
                <p class="form-tip-error" id="sendMsg" style="display: none;"><i></i>验证码错误</p>
            </div>
            <div class="form-item">
                <div class="form-vcode">
                    <input type="tel" id="code" name="code" class="form-ipt" placeholder="请输入验证码">
                    <a href="javascript:;" id="sendBtn"class="btn-getvcode" onclick="checkAndSend();return false;">发送验证码</a>
                    <span id="sendingBtn" class="btn-getvcode btn-disabled" style = "display :none">重新发送(60)</span>
                </div>
                <p class="form-tip-error" id="checkMsg" style="display: none;"><i></i>验证码错误</p>
            </div>
            <div class="btn-group">
               <a href="#" class="btn-confirm">下一步</a>
            </div>
        </form>
        <a href="#phone" class="linkPhone" style="display:none"></a>
        <a href="#code" class="linkCode" style="display:none"></a>
        <footer class="g-user-help">
            <p>如果您的手机无法收到验证码,请致电客服解决:<br>客服电话:************</p>
        </footer>
    </article>
    <script type="text/javascript">
    $(function() {
        $(".form-ipt").bind({
            focus: function() {
                $(".form-item-error").removeClass("form-item-error");
                $(".form-tip-error").hide();
            },
        });
    });
    {{if $pageArray.screen == 1}}
    $(function() {
        $("#phone").on('focus', function(){
            window.location.hash = "#phone";
        });
        $("#code").on('focus', function(){
            window.location.hash = "#code";
            $(".g-user-help").css("margin-bottom", "80px");
        });
    });
    {{/if}}
        var phoneErr = 1;
        function checkAndSend() {
            var phone = $("#phone").val();
            if (!phone) {
                phoneErr = 1;
                return false;
            }
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                phoneErr = 1;
                $("#sendMsg").html('<i></i>请输入正确的手机号！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            $.ajax({
                url: "/api/check/jsonp",
                data: 'type=phone&value=' + phone,
                async: false,
                dataType: 'jsonp',
                jsonp: 'callback',
                success: function(response) {
                    err = response;
                    if (err == 1)
                    {
                        phoneErr = 1;
                        $("#sendMsg").html('<i></i>请输入正确的手机号！');
                        $("#sendMsg").parent().addClass('form-item-error');
                        $("#sendMsg").show();
                        return false;
                    }
                    else if (err == 2)
                    {
                        phoneErr = 1;
                        $("#sendMsg").html('<i></i>此手机号已被绑定，请重新输入！');
                        $("#sendMsg").parent().addClass('form-item-error');
                        $("#sendMsg").show();
                        return false;
                    }
                    else if (err == 3)
                    {
                        phoneErr = 1;
                        $("#sendMsg").html('<i></i>此手机号已被绑定，请重新输入！');
                        $("#sendMsg").parent().addClass('form-item-error');
                        $("#sendMsg").show();
                        return false;
                    }
                    phoneErr = 0;
                    sendCode();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    phoneErr = 1;
                },
                timeout: 3000
            });
        }
        function sendCode()
        {
            var phone = $("#phone").val();
            if (phone == '') {
                $("#sendMsg").html('<i></i>请先输入手机号！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            if (phoneErr == 1) {
                return false;
            }
            $.post("/member/bindPhone/sendCode", {'phone': AES.encrypt(phone)}, function(data) {
                if (data == '300') {
                    $("#sendMsg").html('<i></i>请输入正确的手机号！');
                    $("#sendMsg").parent().addClass('form-item-error');
                    $("#sendMsg").show();
                } else if (data == '400') {
                    $("#sendMsg").html('<i></i>验证的太频繁了，请稍后再试！');
                    $("#sendMsg").parent().addClass('form-item-error');
                    $("#sendMsg").show();
                } else if (data == '500') {
                    $("#sendMsg").html('<i></i>服务器忙，请稍后再试！');
                    $("#sendMsg").parent().addClass('form-item-error');
                    $("#sendMsg").show();
                }
            });
            $("#sendBtn").text('重新发送');
            showSendMsg(60);
        }
        function showSendMsg(times)
        {
            $("#sendBtn").hide();
            $("#sendingBtn").text('重新发送(' + times + ')');
            $("#sendingBtn").show();
            if (times > 1) {
                times--;
                setTimeout(function() {
                    showSendMsg(times);
                }, 1000);
            } else {
                $("#sendingBtn").hide();
                $("#sendBtn").show();
            }
        }
        function checkAll()
        {
            var phone = $("#phone").val();
            if (!phone) {
                phoneErr = 1;
                $("#sendMsg").html('<i></i>请先输入手机号！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                phoneErr = 1;
                $("#sendMsg").html('<i></i>请输入正确的手机号！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            else
            {
                phoneErr = 0;
            }
            var codeErr = true;
            var phone = $("#phone").val();
            var code = $("#code").val();
            if (phoneErr == 1) {
                return false;
            }
            if (!code) {
                $("#checkMsg").html('<i></i>请输入验证码！');
                $("#checkMsg").parent().addClass('form-item-error');
                $("#checkMsg").show();
                return false;
            }
            $.ajax({
                type: "POST",
                url: "/member/bindPhone/checkCode",
                async: false,
                data: "phone=" + phone + "&code=" + code,
                success: function(data) {
                    if (data == '200') {
                        codeErr = false;
                    } else if (data == '300') {
                        $("#checkMsg").html('<i></i>请输入正确的手机号！');
                        $("#checkMsg").parent().addClass('form-item-error');
                        $("#checkMsg").show();
                    } else if (data == '400') {
                        $("#checkMsg").html('<i></i>验证码错误！');
                        $("#checkMsg").parent().addClass('form-item-error');
                        $("#checkMsg").show();
                    }
                }
            });
            if (codeErr === false) {
                $("#phone").val(AES.encrypt(phone));
            }
            return !codeErr;
        }
        $(".btn-confirm").on("click", function(e)
        {
            $("#bind-form").submit();
            e.preventDefault();
        });
    </script>
    <script type="text/javascript">
        document.body.addEventListener('touchstart', function () {});  
    </script>
    {{/if}}
{{include file = "public/footer_m.tpl.html"}}
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
</body>
</html>