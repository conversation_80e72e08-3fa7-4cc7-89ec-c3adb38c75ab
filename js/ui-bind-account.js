
//IE6 fix hover
    $.fn.ie6hover = function(cls) {
        return this.each(function(){
            if(!!window.ActiveXObject&&!window.XMLHttpRequest){
                var $this = $(this);
                $this.hover(function(){
                    $(this).addClass(cls);
                },function(){
                    $(this).removeClass(cls);
                })
            }
        }) 
    };
    //tab
    $.fn.tabPlug = function (options) {
        var opts = $.extend({
        }, $.fn.tabPlug.defaults, options);
        return this.each(function (i) {
            var _this = $(this);
            var $menus = _this.children(opts.menuChildSel);
            var $container = $(opts.cntSelect).eq(i);
            var $timer;
            if (!$container)
            return;
            $menus.on(opts.eventName,function () {
                clearTimeout($timer);
                var index = $menus.index($(this));
                var _this = $(this);
                $timer = setTimeout(function(){
                    _this.addClass(opts.onStyle).siblings().removeClass(opts.onStyle);
                    if (!($container.children(opts.cntChildSel).eq(index).is(':animated'))) {
                            $container.children(opts.cntChildSel).eq(index).siblings().css('display', 'none').end().stop(true, true).fadeIn(opts.aniSpeed);
                    }
                }, 50);
            });
        });
    };
    $.fn.tabPlug.defaults = {
        cntSelect: '',
        aniSpeed: 'fast',
        onStyle: 'selected',
        menuChildSel: '*',
        cntChildSel: '*',
        eventName: null
    };

    $(".tab-nav").tabPlug({
        cntSelect: '.tab-com',
        menuChildSel: 'li',
        onStyle: 'cur',
        cntChildSel: '.tab-view',
        eventName: 'click'
    });


// var inputTxtError = $('.inputTxtError'),
//    inputTxtFocus = $('.inputTxtFocus');
// $(inputTxtError).parents('.item-input,.item-code').css('border-color','#f30');
// $(inputTxtFocus).parents('.item-input,.item-code').css('border-color','#b3e6ff');