
### 第三方接口
* 配置接口、用户名、密码等可以通过现有积分短信管理后台查询到
* 第三方接口说明文档可以咨询：“李梅兰”

### MAS系统
* MAS系统是移动公司将其程序(JAVA+MySQL)安装到咱们自己的服务器(183.136.203.46)发短信之用
* 咱们自己用PHP开发了一套程序，用来操作读取MAS系统数据，可以通duanxin.2345.com访问
* 同时有一个接口用来通过MAS发短信：http://duanxin.2345.com/SMSApi

#### 接口参数

|参数|参数值|说明|
|-----|-----|-----|
|Phone|手机号|　|
|Content|短信内容| |
|Sessionid|按照一定规则生成的唯一ID|规则可以自己定义|

<hr>

### 积分系统短信接口


| 接口地址 | 操作方法 |
|-----|-----|
|http://jifen.2345.com/api/sms_sender.php|GET / POST|

#### 接口参数

| 参数 | 参数值 | 说明 |
|-----|-----|-----|
| phone | 手机号码 | |
| msg | 手机号码 | |
| sms_type | 短信类别 | 固定值，1为验证码类，2为通知类，3为营销类。 <br>注意，非验证码类型的短信内容<br>务必设置为2通知类或者3营销类，以免短信通道被封 |
| business_type | 业务类别 |  （分配固定值），具体清单见下面表格 |


#### 接口返回值格式

```
状态代码|状态信息

样例：

1|发送成功

```
#### 接口状态代码和对应的状态信息

|状态码|状态信息|
|-----|-----|
|1|发送成功|
|1| 发送成功|
|0| 发送失败|
|-1| 通道不支持发送移动短信|
|-2| 通道不支持发送电信短信|
|-3| 通道不支持发送联通短信|
|-4| 移动通道不可用|
|-5| 电信通道不可用|
|-6| 联通通道不可用|
|-7| 手机号码不正确|
|-8| 短信内容太短|
|-9| 非法的短信类型|
|-10| 超出手机验证码数量限制|
|-11| 远程IP地址验证失败|

##### 业务类别清单

|业务代码|业务|
|-----|-----|
|11|网络电话-PC|
|12| 网络电话-MOBILE|
|13| 2345携手平安送保险活动 |
|14| 用户中心|
|16| 彩票|
|100| 影视|
|101| 2014年会 |
|102| 小说频道 |
|103| 浏览器|
|104| 测试组|
|105| 50bang/运维监控报警|
|106| 手机浏览器/手机助手|
|107| 阅读王 |
|108| 游戏中心　|
|109|客户端开发组|
|110|分类金融|
|111|数据开发组|
|112|金融超市|
|113|语音验证码|
|114|域名贷|
