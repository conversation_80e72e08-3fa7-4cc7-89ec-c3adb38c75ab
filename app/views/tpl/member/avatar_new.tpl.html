{{include file = "public/mem_header.tpl.html"}}
{{include file = "public/left_nav.tpl.html}}
<style type="text/css">
    .nickname-tip {
        margin: 2px 0 10px 94px;
    }
</style>
<!--主体左侧 结束-->
<!--主体右侧 开始-->
<div class="account_right" style="display: none" id="ie678Flash">
    <h2 class="pf" style=" margin-bottom:30px;">修改头像</h2>
    <div id="altContent"></div>
    <script type="text/javascript" src="/js/FaustCplus/swfobject.js"></script>
    <script type="text/javascript">
    </script></div><!--account_right end-->
<!--main end-->
<!--主体 end-->

<link rel="stylesheet" href="/css/mycropper.css?v={{$smarty.const.STATIC_FILE_VERSION}}">

<div class="account_right" style="display: none" id="baseJsUpload">
    <h2 class="pf" style=" margin-bottom:30px;">基础信息昵称</h2>
    <ul class="conUl">
        <li>
            <span class="conTit">昵称：</span>
            <div class="coninput w318">
                <input class="defaultInput" type="text" id="nickname" name="nickname" onblur="checkNickname();return false;"
                       autocomplete="off" value="请设置昵称，可输入汉字或数字，限20位"  title="请设置昵称，可输入汉字或数字，限20位" maxlength="20"/>
            </div>
        </li>
    </ul>
    <div class="nickname-tip" id="nickname-tip">
        温馨提示：<br/>
        1、昵称和用户名是2个不同属性，昵称只用于信息展示不用于账号登录，修改昵称不会导致用户名被修改；您修改昵称后，仍可用原用户名或绑定手机登录该账号；<br/>
        2、设置或修改昵称后，将会进行违规检测，若涉及不正当用词或违反国家规定，设置内容将不会生效；<br/>
    </div>
    <h2 class="pf" style=" margin-bottom:30px;">基础信息头像</h2>
    <div class="choose-img">
        <div class="a-uplod">
            <span href="javascript:;">选择图片</span>
            <input type="file" value="选择图片" id="uploadFile" value="">
        </div>
        <div class="error-tip">
            仅支持png、jpg格式且不超过1M大小的图片
        </div>
    </div>
    <div class="crop">
        <div class="crop-content">
            <div class="crop_image_content" id="cropBox">
                <div>
                    <img src="" class="crop-images" id="cropImagesId">
                </div>
                <!-- 剪切框 -->
                <div class="cropper_modal" style="display:none"></div>
                <div class="crop_box" id="mainBox" style="display:none">
                    <div class="crop_box_image">
                        <img src="" alt="" width="300px" id="cropImageModal">
                    </div>
                    <div class="crop_box-jshack" id="mainBoxJsHack">
                        <span id="right-down" class="minBox right-down" move-to="right-down"></span>
                    </div>
                </div>
            </div>
            <div class="preview-box clearfix" style="display:none">
                <div class="preview-title">
                    您上传的头像会自动生成三种尺寸<br/>
                    请注意中小尺寸的头像是否清晰
                </div>
                <div class="preview-list ">
                    <div class="preview-item preview-item-1">

                    </div>
                    <div class="preview-item preview-item-2">

                    </div>
                    <div class="preview-item preview-item-3">

                    </div>
                </div>
                <span class="tip tip1">
            200*200像素
          </span>
                <span class="tip tip2">
            120*120像素
          </span>
                <span class="tip tip3">
            48*48像素
          </span>
            </div>
        </div>
        <div class="crop-submit">
            <button id="crop-sure">保存</button>
            <button id="crop-cancel">取消</button>
        </div>

    </div>
</div>
{{include file = "public/qrcode.tpl.html"}}
<script src="/js/jquery-ui-1.10.4.custom.min.js"></script>
<script src="/js/mycropper_new.js"></script>
<!--[if IE 6]>
<script src="/js/DD_belatedPNG_0.0.8a-min.js"></script>
<script type="text/javascript">
    DD_belatedPNG.fix('.png24');
</script>
<![endif]-->

<script>
    var beV3 = "/v3/user"
    var mid = getUrlParam("mid")
    var isclick = true;
    // 初始化获取用户数据
    var postData = {
        "mid": mid,
    }
    var forward = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}"
    var defaultInput = "请设置昵称，可输入汉字或数字，限20位"
    $("#nickname").attr("title", defaultInput)
    $.ajax({
        url:beV3 + "/members/GetNicknameAvatar",
        async:false,
        data:JSON.stringify(postData),
        type:"post",
        dataType:"json",
        contentType: 'application/json',
        success: function(res) {
            if (res.code == 200) {
                defaultInput = "请设置昵称，可输入汉字或数字，限" + res.data.nicknameLengthMin + "~" + res.data.nicknameLengthMax + "位"
                $("#nickname").attr("title", defaultInput)
                $("#nickname").val(defaultInput)
                $("#nickname").attr("maxlength", res.data.nicknameLengthMax)
                if (res.data.nickname != "") {
                    $("#nickname").val(res.data.nickname)
                    $("#nickname").attr("text", res.data.nickname)
                    $("#nickname").addClass("colorInput");
                }
                if (res.data.avatar != "") {
                    //$("#uploadFile").val(res.data.avatar)
                    $("#cropImagesId").attr("src",res.data.avatar)
                    $("#cropImageModal").attr("src",res.data.avatar)
                } else {
                    $("#cropImagesId").attr("src", "/pic/avatar/default_v2.jpg")
                }
            }
        }
    });

    function checkNickname(n) {
        var nickname = $("#nickname").val()

        if (nickname != defaultInput && nickname != "" && nickname != $("#nickname").attr("text")) {
            $.ajax({
                url: beV3 + "/members/checkNickname",
                type: "POST",
                data: JSON.stringify({"mid": mid, "nickname": nickname}),
                dataType: "json",
                /**  *必须false才会自动加上正确的Content-Type  */
                contentType: 'application/json',
                success: function (data) {
                    if (data.code != "200") {
                        $("#nickname-tip").html(data.message)
                        $("#nickname-tip").addClass("ts-error")
                    } else {
                        $("#nickname-tip").html("请注意：设置或修改昵称后，将会进行违规检测，若涉及不正当用词或违反国家规定，设置内容将不会生效。")
                        $("#nickname-tip").removeClass("ts-error")
                    }
                }
            });
        } else {
            $("#nickname-tip").html("请注意：设置或修改昵称后，将会进行违规检测，若涉及不正当用词或违反国家规定，设置内容将不会生效。")
            $("#nickname-tip").removeClass("ts-error")
        }
    }

    $('#crop-sure').on('click', function (e) {
        var mainBoxElem = document.getElementById("mainBox"); // 裁剪框
        if (isclick) {
            isclick = false;
            var file = $('.a-uplod input')[0].files[0];
            if (file) {
                var reader = new FileReader();
                reader.readAsDataURL(file)
                var offsetHeight = mainBoxElem.offsetHeight,
                    offsetWidth = mainBoxElem.offsetWidth,
                    offsetTop = mainBoxElem.offsetTop,
                    offsetLeft = mainBoxElem.offsetLeft;
                reader.onload = function (){
                    var nickname = $("#nickname").val() == defaultInput? "" : $("#nickname").val()
                    var subIndex = reader.result.indexOf(",")
                    var base64Str = reader.result.substring(subIndex + 1)
                    var formData = new FormData();
                    formData.append('x', offsetLeft);
                    formData.append('y', offsetTop);
                    formData.append('w', offsetWidth);
                    formData.append('h', offsetHeight);
                    var file = $('.a-uplod input')[0].files[0];
                    formData.append("avatar_file", file);

                    var nickname = $("#nickname").val() == $("#nickname").attr("title")? "" : $("#nickname").val()
                    formData.append("nickname", nickname);
                    formData.append("mid", mid);
                    $.ajax({
                        url: "/member/avatar/cutImg",
                        type: "POST",
                        data: formData,
                        /**  *必须false才会自动加上正确的Content-Type  */
                        contentType: false,
                        /**  * 必须false才会避开jQuery对 formdata 的默认处理  * XMLHttpRequest会对 formdata 进行正确的处理  */
                        processData: false,
                        dataType: 'json',
                        success: function (data) {
                            if (data.status == 1) {
                                if (data.msg != "") {
                                    alert(data.msg);
                                } else {
                                    alert("保存成功");
                                }
                                location.href = forward;
                            }
                            if (data.status != 1) {
                                alert(data.msg);
                            }
                        },
                        error: function () {
                            alert("修改失败！");
                        }
                    });
                    setTimeout(function () {
                        isclick = true;
                    }, 2000)
                }
            } else {
                var nickname = $("#nickname").val() == "请设置昵称，可输入汉字或数字，限20位"? "" : $("#nickname").val()
                var postData = {
                    "mid": mid,
                    "nickname": nickname,
                    "avatarBase64": "",
                    "avatarName": "",
                }
                $.ajax({
                    url: beV3 + "/members/UpdateNicknameAvatar",
                    type: "POST",
                    data: JSON.stringify(postData),
                    dataType: 'json',
                    contentType: 'application/json',
                    success: function (data) {
                        if (data.code == "200") {
                            alert("修改成功!")
                            location.href = forward;
                        } else {
                            alert(data.message)
                        }
                    },
                    error: function () {
                        alert("修改失败！");
                    }
                });
                setTimeout(function () {
                    isclick = true;
                }, 2000)
            }
        }

    });
    $('#crop-cancel').on('click', function (e) {
        location.href = '/member/edit_info.php';
    });

    if (safariVersion != 'undefined' && safariVersion <= 9) {
        $('#baseJsUpload').hide();
        $('#ie678Flash').show();
    } else {
        $('#ie678Flash').hide();
        $('#baseJsUpload').show();

    }
    $(function()
    {
        //用户头像hover
        $(".user_pic").mouseover(function()
        {
            $(this).find("span").show();
        }).mouseleave(function()
        {
            $(this).find("span").hide();
        });

        //关闭提示信息
        $(".btn-close").bind("click",function()
        {
            $(this).parent(".remark").hide();
        });
    });//JQ
    $(".defaultInput").bind(
        {
            focus: function () {
                $(this).parent().removeClass("inputTxtError");
                $(this).parent().addClass("inputTxtFocus");
                if ($(this).val() == $(this).attr("title")) {
                    $(this).val("");
                }
                $(this).addClass("colorInput");
            },
            blur: function () {
                $(this).parent().removeClass("inputTxtFocus");
                if ($.trim($(this).val()) == "") {
                    $(this).val($(this).attr("title"));
                    $(this).removeClass("colorInput");
                }
                ;

            }
        }
    );
    $("#uploadFile").change(function(){
        $(".preview-box").show()
        $(".cropper_modal").show()
        $(".crop_box").show()
    })
</script>
<!--底部 begin-->
</div>
{{include file = "public/footer.tpl.html"}}
