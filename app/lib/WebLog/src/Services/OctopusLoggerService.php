<?php
/**
 * Copyright (c) 2019,2345
 * 摘    要：
 * 作    者：liupeng
 * 修改日期：2019.04.10
 */
namespace WebLogger\Services;

use Octopus\Logger\Handler\StreamHandler;
use Octopus\Logger;
use Psr\Log\LogLevel;
use WebLogger\Formatter\LineFormatter;

class OctopusLoggerService extends AbstractLogger
{
    private static $instances = array();
    /**
     * @var Logger $logger
     */
    private $logger;
    private $requestId;

    /**
     * LoggerService constructor
     * @param string $path 日志目录
     */
    private function __construct($path)
    {
        if ( !is_dir($path)) {
            mkdir($path, 0755, true);
        }
        $this->logger = self::getLogger($path);
        $this->requestId = uniqid();
    }

    /**
     * getInstance function
     * @return static|mixed
     */
    public static function getInstance()
    {
        if (!defined('APPPATH')) {
            error_log("log4php need the file path");
            return false;
        }

        $logDirPath = isset($_ENV['LOG4PHP_FILE_DIR']) ? $_ENV['LOG4PHP_FILE_DIR'] : (getenv('LOG4PHP_FILE_DIR') ? getenv('LOG4PHP_FILE_DIR') : APPPATH . '/logs');
        // 单例方式获取参数
        $currentFlag = date('YmdH');
        $classNameMd5  = md5($logDirPath . $currentFlag);
        if (!isset(static::$instances[$classNameMd5])) {
            // delete last object
            $lastFlag = date('YmdH', strtotime('last hour'));
            $className4LastFlag  = md5($logDirPath . $lastFlag);
            unset(static::$instances[$className4LastFlag]);
            static::$instances[$className4LastFlag] = null;

            // new instance
            static::$instances[$classNameMd5] = new static($logDirPath);
        }
        return static::$instances[$classNameMd5];
    }


    /**
     * @param string $logDirPath 日志文件路径
     * @return mixed
     */
    private static function getLogger($logDirPath)
    {
        $logger = new Logger($logDirPath);
        $day = date('YmdH');
        foreach (self::getLogLevels() as $level) {
            $file = $day . '.log';
            $filepath = $logDirPath . "/" . $file;
            $handler = new StreamHandler($filepath, $level, false);
            $handler->setFormatter(new LineFormatter(null, 'Y-m-d H:i:s.u'));
            $logger->pushHandler($handler);
        }

        return $logger;
    }

    /**
     * @param string $msg 日志消息
     * @param string $level 日志等级
     * @param array $context 上下文
     * @return void
     */
    public function log($msg, $level, $context = array())
    {
        if (!$this->logger) {
            return false;
        }

        if ( !in_array($level, self::getLogLevels())) {
            $debug_info = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5);
            throw new \Exception("log4php 当前不支持的level：" . $level . ", debug_trace is: " . json_encode($debug_info));
        }

        //当且仅当error级别时记录context信息
        if ($level == LogLevel::ERROR) {
            $debug_info = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5);
            $context = array('requestId' => $this->requestId, 'data' => $context, 'debug_info' => $debug_info);
        } else {
            $context = array('requestId' => $this->requestId, 'data' => $context);
        }
        $this->logger->log($level, mb_convert_encoding($msg, 'UTF-8', 'GBK'), $this->arrayIconv4GBK($context, 'GBK', 'UTF-8'));
    }
}
