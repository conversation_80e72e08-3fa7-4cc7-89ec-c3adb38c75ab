<?php

/**
 * Copyright (c) 2014,上海二三四五网络科技股份有限公司
 * 摘    要：绑定接口
 * 作    者：<EMAIL>
 * 修改日期：2014.12.24
 */

use Octopus\Logger;
use Octopus\Logger\Handler\StreamHandler;

class PhoneAction extends Action
{
    private static $smsTypeConfig = [
        'default' => 7,//默认
        'andxqlm' => 178,//兴修连梦
        'andwnl' => 179,//万年历
        'andgsq' => 180,//购省钱
        'andhtt' => 234,//好头条
        'andbscan' => 265,//可岸浏览器
        'andtqwfz' => 266,//开心天气
        'andkxgy' => 267,//开心果园
        'and2345jsbrowser' => 269, //手浏马甲包js
        'andllqoem' => 269, //手机浏览器oem版
        'andkxbbz' => 270, //开心步步赚
        'KXNCAPP' => 272,  //开心农场
        'KXYTAPP' => 274,  //开心鱼塘
        'JBZAPP' => 275, //计步赚
        '2345KXGYAPP' => 277, //2345开心果园
        'SDGAPP' => 278, // 省电狗
        'ZONE2345APP' => 260,  //可映
        'HJAPP' => 281,  //会剪
        'AMYAPP' => 284,  //
    ];

    /**
     * 参    数：
     * 作    者：wangl
     * 功    能：获取手机绑定
     * 日    期：2014-12-24
     * desc:该函数兼容加解密
     */
    public function get($passid)
    {
        $phoneModel = loadModel('phone');
        $result = $phoneModel->get($passid);
        return $result;
    }

    /**
     * 判断手机号是否可用
     * @param type $passid
     * @param type $phone
     * @return string
     * desc:该函数兼容加解密
     */
    public function check($passid, $phone)
    {
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            return "300.0";
        }
        $memberModel = loadModel("member");
        $phonePassid = $memberModel->getPassidByUsername($phone);
        if ($phonePassid && $phonePassid != $passid)
        {
            return "300.1";
        }
        $phonePassid = $memberModel->getPassidByPhone($phone);
        if ($phonePassid)
        {
            return "300.2";
        }
        return "200.0";
    }

    /**
     * 绑定手机号
     * @param type $passid
     * @param type $phone
     * @return type
     */
    public function bind($passid, $phone)
    {
        return $this->bindSimple($passid, $phone);
    }

    /**
     * 简单绑定
     * @param type $passid
     * @param type $phone
     * @return string
     */
    public function bindSimple($passid, $phone)
    {
        $memberRedis = loadAction('memberRedis');
        $memberRedis->delMemberCache($passid);

        if ($this->get($passid)) {
            return '500.0';
        }
        $checkCode = $this->check($passid, $phone);
        if ($checkCode != "200.0")
        {
            return $checkCode;
        }
        $phoneModel = loadModel('phone');
        $result = $phoneModel->add($passid, $phone);
        if ($result !== false)
        {
            $this->usernameAsPhoneRename($passid, $phone);
            $memberModel = loadModel('member');
            $uid = $memberModel->getUid($passid);
            noticeToChange($passid, 'changePhone', array(
                'passid' => $passid,
                'uid' => $uid,
                'type' => 'phone',
                'value' => $phone
            ));
            return '200.0';
        }
        else
        {
            return '500.0';
        }
    }

    /**
     * 手机作为用户名类型，重置用户名
     * -
     * @param int $passid passid
     * @param int $phone 手机号码
     * @param array $userInfo 用户信息
     * @return string
     */
    public function phoneAsUsernameRegResetBind($passid, $phone, $userInfo)
    {
        //如果暗库里面没有这个手机号码  或者暗库的手机号码等于当前登录的
        if (empty($userInfo['phone_redundancy']) || $userInfo['phone_redundancy'] == $phone)
        {
            $phoneModel = loadModel('phone');
            //绑定手机号码
            $result = $phoneModel->add($passid, $phone);
            if ($result !== false)
            {
                $memberModel = loadModel('member');
                $uid = $memberModel->getUid($passid);
                noticeToChange($passid, 'changePhone', array(
                    'passid' => $passid,
                    'uid' => $uid,
                    'type' => 'phone',
                    'value' => $phone
                ));
                return '200.0';
            }
            else
            {
                return '500.0';
            }
        }
        return '300.2';
    }

    /**
     * 绑定手机
     * -
     * @param int $passid 用户ID
     * @param int $phone 手机号
     * 作    者：wangl
     * 功    能：绑定手机
     * 日    期：2014.12.24
     * @return string
     */
    public function edit($passid, $phone)
    {
        $memberRedis = loadAction('memberRedis');
        $memberRedis->delMemberCache($passid);

        $checkCode = $this->check($passid, $phone);
        if ($checkCode != "200.0")
        {
            return $checkCode;
        }
        $phoneModel = loadModel('phone');
        $result = $phoneModel->edit($passid, $phone);
        if ($result !== false)
        {
            $memberModel = loadModel('member');
            $uid = $memberModel->getUid($passid);
            noticeToChange($passid, 'changePhone', array(
                'passid' => $passid,
                'uid' => $uid,
                'type' => 'phone',
                'value' => $phone
            ));
            return '200.0';
        }
        else
        {
            return '500.0';
        }
    }

    /**
     * 获取冗余手机号
     * @param type $passid
     * @return type
     */
    public function getComplex($passid)
    {
        $phoneModel = loadModel('phone');
        return $phoneModel->get($passid);
    }

    /**
     * 发送注册验证码
     * -
     * @param string $mid 用户中心唯一标识
     * @param int $phone 手机号码
     * @param string $smsType 短信发送类型
     * @param string $positionId 短信发送位置
     * @param bool $sessionVerify 是否开启session校验
     * @param array $smsFilterConfig 发送短信频率限制配置
     * @param string $smsMid 短信中心mid
     * @return int|string
     * <AUTHOR>
     */
    public function sendRegCode($mid, $phone, $smsType, $positionId, $sessionVerify = false, $smsFilterConfig = array(), $smsMid = '')
    {
        $phoneStatus = $this->check(0, $phone);
        if ($phoneStatus == '300.0')
        {
            return 300;
        }
        elseif ($phoneStatus == '300.1')
        {
            return 301;
        }
        elseif ($phoneStatus == '300.2')
        {
            return 302;
        }
        $timeKey = "PR:time:$mid:$phone";
        $codeKey = "PR:code:$mid:$phone";
        if ('' == $smsMid) {
            $smsMid = $mid;
        }
        return $this->sendCode($phone, $timeKey, $codeKey, $smsType, $positionId, $mid, $sessionVerify, $smsFilterConfig, $smsMid);
    }

    /**
     * 检验注册验证码
     * @param string $mid
     * @param string $phone
     * @param string $checkCode
     * @param boolean $sessionVerify
     * @param boolean $once
     * @param boolean $showErrorDetail 是否返回错误细节
     * @return boolean
     */
    public function checkRegCode($mid, $phone, $checkCode, $sessionVerify = false, $once = false, $showErrorDetail = false)
    {
        $timeKey = "PR:time:$mid:$phone";
        $codeKey = "PR:code:$mid:$phone";
        return $this->checkCode($checkCode, $timeKey, $codeKey, $sessionVerify, $once, $mid, $showErrorDetail);
    }

    /**
     * 发送登录验证码
     * -
     * @param string $mid 用户中心唯一标识
     * @param int $phone 手机号码
     * @param string $smsType 短信发送类型
     * @param string $positionId 短信发送位置
     * @param bool $sessionVerify 是否开启session校验
     * @param array $smsFilterConfig 发送短信频率限制配置
     * @param string $smsMid 短信中心mid
     * @return int|string
     * <AUTHOR>
     */
    public function sendLoginCode($mid, $phone, $smsType, $positionId, $sessionVerify = false, $smsFilterConfig = array(), $smsMid = '')
    {
        $phoneStatus = $this->check(0, $phone);
        if ($phoneStatus == '300.0')
        {
            return 300;
        }
        elseif ($phoneStatus == '200.0')
        {
            return 404;
        }
        $timeKey = "PL:time:$mid:$phone";
        $codeKey = "PL:code:$mid:$phone";
        if ('' == $smsMid) {
            $smsMid = $mid;
        }
        return $this->sendCode($phone, $timeKey, $codeKey, $smsType, $positionId, $mid, $sessionVerify, $smsFilterConfig, $smsMid);
    }

    /**
     * 发送第三方绑定验证码
     * -
     * @param string $mid 用户中心唯一标识
     * @param int $phone 手机号码
     * @param string $smsType 短信发送类型
     * @param string $positionId 短信发送位置
     * @param bool $sessionVerify 是否开启session校验
     * @param array $smsFilterConfig 发送短信频率限制配置
     * @return int|string
     * <AUTHOR>
     */
    public function sendThirdPartyBindCode($mid, $phone, $smsType, $positionId, $sessionVerify = false, $smsFilterConfig = array())
    {
        $timeKey = "PTB:time:$mid:$phone";
        $codeKey = "PTB:code:$mid:$phone";
        return $this->sendCode($phone, $timeKey, $codeKey, $smsType, $positionId, $mid, $sessionVerify, $smsFilterConfig);
    }

    /**
     * 检验第三方绑定用验证码
     *
     * @param string $mid mid
     * @param string $phone phone
     * @param string $checkCode checkCode
     * @return boolean
     */
    public function checkThirdPartyBindCode($mid, $phone, $checkCode)
    {
        $timeKey = "PTB:time:$mid:$phone";
        $codeKey = "PTB:code:$mid:$phone";
        return $this->checkCode($checkCode, $timeKey, $codeKey, true);
    }

    /**
     * 检验登录验证码
     * @param string $mid
     * @param string $phone
     * @param string $checkCode
     * @param boolean $sessionVerify
     * @param boolean $once
     * @param boolean $showErrorDetail 是否返回错误细节
     * @return boolean
     */
    public function checkLoginCode($mid, $phone, $checkCode, $sessionVerify = false, $once = false, $showErrorDetail = false)
    {
        $timeKey = "PL:time:$mid:$phone";
        $codeKey = "PL:code:$mid:$phone";
        return $this->checkCode($checkCode, $timeKey, $codeKey, $sessionVerify, $once, $mid, $showErrorDetail);
    }

    /**
     * 发送验证用验证码
     * @param $mid
     * @param $passid
     * @param $smsType
     * @param $positionId
     * @param $phone
     * @return int|string
     * <AUTHOR>
     */
    /**
     * 功  能：发送验证用验证码
     *
     * @param string $mid $mid
     * @param int $passid $passid
     * @param string $smsType $smsType
     * @param int $positionId $positionId
     * @param string $phone $phone
     * @return int
     */
    public function sendVerifyCode($mid, $passid, $smsType, $positionId, $phone = '')
    {
        if (!$phone) {
            $phoneInfo = $this->getComplex($passid);
            if (!$phoneInfo) {
                return 404;
            }
            $phone = $phoneInfo['phone'];
        }
        $timeKey = "PV:time:$mid:$passid";
        $codeKey = "PV:code:$mid:$passid";
        return $this->sendCode($phone, $timeKey, $codeKey, $smsType, $positionId, $mid);
    }

    /**
     * 检验验证用验证码
     * @param string $mid
     * @param string $passid
     * @param string $checkCode
     * @return boolean
     */
    public function checkVerifyCode($mid, $passid, $checkCode)
    {
        $timeKey = "PV:time:$mid:$passid";
        $codeKey = "PV:code:$mid:$passid";
        return $this->checkCode($checkCode, $timeKey, $codeKey);
    }

    /**
     * 发送绑定用验证码
     * @param $mid
     * @param $passid
     * @param $newPhone
     * @param $smsType
     * @param $positionId
     * @return int
     * <AUTHOR>
     */
    public function sendBindCode($mid, $passid, $newPhone, $smsType = 5, $positionId = 149)
    {
        $retCode = $this->check($passid, $newPhone);
        if ($retCode == '300.0')
        {
            return 300;
        }
        else if ($retCode == '300.1')
        {
            return 301;
        }
        else if ($retCode == '300.2')
        {
            return 302;
        }
        $phoneInfo = $this->getComplex($passid);
        if ($phoneInfo)
        {
            return 402;
        }
        $timeKey = "PB:time:$mid:$passid";
        $codeKey = "PB:code:$mid:$passid";
        return $this->sendCode($newPhone, $timeKey, $codeKey, $smsType, $positionId, $mid);
    }

    /**
     * 检验绑定用验证码
     * @param string $mid
     * @param string $passid
     * @param string $checkCode
     * @return boolean
     */
    public function checkBindCode($mid, $passid, $checkCode)
    {
        $timeKey = "PB:time:$mid:$passid";
        $codeKey = "PB:code:$mid:$passid";
        return $this->checkCode($checkCode, $timeKey, $codeKey);
    }

    /**
     * 发送修改用验证码
     * @param $mid
     * @param $passid
     * @param $newPhone
     * @param $verifyCode
     * @param $smsType
     * @param $positionId
     * @return int|string
     * <AUTHOR>
     */
    public function sendEditCode($mid, $passid, $newPhone, $verifyCode, $smsType = 5, $positionId = 141)
    {
        $retCode = $this->check($passid, $newPhone);
        if ($retCode == '300.0')
        {
            return 300;
        }
        else if ($retCode == '300.1')
        {
            return 301;
        }
        else if ($retCode == '300.2')
        {
            return 302;
        }
        $phoneInfo = $this->getComplex($passid);
        if (!$phoneInfo)
        {
            return 404;
        }
        $phone = $phoneInfo['phone'];
        if (!$this->checkVerifyCode($mid, $passid, md5($phone . $verifyCode)))
        {
            return 303;
        }
        $timeKey = "PE:time:$mid:$passid";
        $codeKey = "PE:code:$mid:$passid";
        return $this->sendCode($newPhone, $timeKey, $codeKey, $smsType, $positionId, $mid);
    }

    /**
     * 功  能：
     *
     * @param string $mid mid
     * @param int $passid passid
     * @param string $newPhone newPhone
     * @param int $smsType smsType
     * @param int $positionId positionId
     * @return int
     */
    public function sendEditCodeNew($mid, $passid, $newPhone, $smsType = 5, $positionId = 141)
    {
        $retCode = $this->check($passid, $newPhone);
        if ($retCode == '300.0') {
            return 300;
        } else if ($retCode == '300.1') {
            return 301;
        } else if ($retCode == '300.2') {
            return 302;
        }
        $phoneInfo = $this->getComplex($passid);
        if (!$phoneInfo) {
            return 404;
        }
        $timeKey = "PE:time:$mid:$passid";
        $codeKey = "PE:code:$mid:$passid";
        return $this->sendCode($newPhone, $timeKey, $codeKey, $smsType, $positionId, $mid);
    }

    /**
     * 检验修改用验证码
     * @param string $mid
     * @param string $passid
     * @param string $checkCode
     * @return boolean
     */
    public function checkEditCode($mid, $passid, $checkCode)
    {
        $timeKey = "PE:time:$mid:$passid";
        $codeKey = "PE:code:$mid:$passid";
        return $this->checkCode($checkCode, $timeKey, $codeKey);
    }

    /**
     * 发送验证码
     * @param int $phone 发送短信的手机号码
     * @param string $timeKey 时间key
     * @param string $codeKey codekey
     * @param string $smsType 短信类型
     * @param string $positionId 短信发送位置
     * @param string $mid  用户中心唯一标识
     * @param bool $sessionVerify  是否session检验
     * @param array $smsFilterConfig 发送短信频率限制配置
     * @param string $smsMid 短信中心mid
     * @return int
     * <AUTHOR>
     */
    private function sendCode($phone, $timeKey, $codeKey, $smsType, $positionId, $mid = 'login', $sessionVerify = false, $smsFilterConfig = array(), $smsMid = '')
    {
        if ('' == $smsMid) {
            $smsMid = $mid;
        }
        $context = [
            'logUsage' => 'usageStatistics',
            'system' => '用户中心',
            'product' => $smsMid,
            'app' => $smsMid,
            'module' => 'PhoneAction.php',
            'function' => 'sendCode',
            'version' => '',
            'count' => 1,
        ];
        WebLogger\Facade\LoggerFacade::info('用户中心短信调用次数统计', $context);

        $code = rand(100000, 999999);
        if (RUNMODE == 'testing' || RUNMODE == 'development') {
            $code = 123456;
            $result = 1;
        } else {
            $result = sendCodeUsePhone($phone, $code, $smsType, $positionId, $mid, $smsFilterConfig, $smsMid);
        }

        if ($result == 1)
        {
            $storeCode = md5($phone . $code);
            if ($sessionVerify)
            {
                $sid = session_id();
                if (!$sid)
                {
                    session_start();
                    $sid = session_id();
                }
                $storeCode = md5($sid . $storeCode);
                WebLogger\Facade\LoggerFacade::info('debug', [
                    "sid" => $sid, "checkCode" => $storeCode, "redisCodeKey" => $codeKey]);
            }
            $redis = RedisEx::getInstance();
            $redis->setex($timeKey, 1800, 0);
            $isSetTime = $redis->setex($codeKey, 1800, $storeCode);
            if ($isSetTime !== true)
            {
                $msg = $phone . ' ' . $code . ' ' . $storeCode;
                xLog('sendCodeFail', 'sendCodeFail', $msg, 'info');
            }
            //发送成功
            return 200;
        }
        else if ($result == 2)
        {
            //发送频繁
            return 400;
        }
        else
        {
            //服务器忙
            return 500;
        }
    }

    /**
     * 检验验证码
     * @param string $checkCode
     * @param string $timeKey
     * @param string $codeKey
     * @param boolean $sessionVerify
     * @param boolean $once
     * @param boolean $mid
     * @param boolean $showErrorDetail 是否返回错误细节
     * @return boolean|int
     */
    private function checkCode($checkCode, $timeKey, $codeKey, $sessionVerify = false, $once = false, $mid = '', $showErrorDetail = false)
    {
        $redis = RedisEx::getInstance();
        $checkTime = $redis->get($timeKey);
        if ($checkTime > 2) {
            $redis->del($timeKey);
            $redis->del($codeKey);
            if ($showErrorDetail) {
                return -2;
            }
            return false;
        }
        if ($checkCode)
        {
            if ($sessionVerify)
            {
                $sid = session_id();
                if (!$sid)
                {
                    session_start();
                    $sid = session_id();
                }
                $checkCode = md5($sid . $checkCode);
                WebLogger\Facade\LoggerFacade::info('debug', [
                    "sid" => $sid, "checkCode" => $checkCode, "redisCode" => $redis->get($codeKey), "redisCodeKey" => $codeKey]);
            }
            //测试环境
            if (false && RUNMODE === 'development')
            {
                return true;
            }
            if ($mid == 'XCTEST' || $checkCode == $redis->get($codeKey))
            {
                if($once)
                {
                    $redis->del($timeKey);
                    $redis->del($codeKey);
                }
                return true;
            }
        }
        $redis->setex($timeKey, 1800, ++$checkTime);
        if ($showErrorDetail) {
            return -1;
        }
        return false;
    }

    /**
     * 修复 使用手机号作为用户名的账户 2016-07-25
     *
     * @param $phone
     * @param $userInfo
     * @param $clientType
     */
    public function repairUsernameWithPhoneNum($phone, $userInfo, $clientType)
    {
        $username = $userInfo['username'];
        $bindPhone = $userInfo['phone'];
        $passid = $userInfo['id'];

        $res1 = $res2 = $res3 = 0;
        $newUsername = "";
        $regex = Config::get('regex');
        if (preg_match($regex['phone'], $username) && $bindPhone == $phone && $phone != $username)
        {
            $type = "A";
            //A类 用户名和绑定手机号不一样
            $memberModel = loadModel('member');
            $ignoreGid = true;
            $newUsername = $username;
            $res2 = $memberModel->setUsername($passid, $newUsername, $ignoreGid);
            $res1 = 1;

            $this->logRepairUsernameRes($type, $clientType, $passid, $username, $phone, $bindPhone, $res1, $res2, $res3);
        }
        else
        {
            // 只处理用户名为手机号的帐号
            if ($phone == $username)
            {
                $memberModel = loadModel('member');
                if (empty($bindPhone))
                {
                    //C类 用户名是手机号, 绑定手机号为空
                    // 添加绑定手机号, 当前绑定
                    $type = "C";
                    $res3 = $this->phoneAsUsernameRegResetBind($passid, $phone, $userInfo);
                    $res3 = ($res3 == "200.0" ? 1 : 0);
                    if ($res3)
                    {
                        for ($i = 1;$i < 6;$i++)
                        {
                            $newUsername = $username . "_$i";
                            $res = $memberModel->getPassidByUsername($newUsername);
                            if (!$res)
                            {
                                $ignoreGid = true;
                                $res2 = $memberModel->setUsername($passid, $newUsername, $ignoreGid);
                                $res1 = 1;
                                break;
                            }
                        }
                    }

                    $this->logRepairUsernameRes($type, $clientType, $passid, $username, $phone, $bindPhone, $res1, $res2, $res3);
                }
                elseif ($bindPhone == $phone)
                {
                    //B类 用户名和绑定手机号一样
                    $type = "B";
                    for ($i = 1;$i < 6;$i++)
                    {
                        $newUsername = $username . "_$i";
                        $res = $memberModel->getPassidByUsername($newUsername);
                        if (!$res)
                        {
                            $ignoreGid = true;
                            $res2 = $memberModel->setUsername($passid, $newUsername, $ignoreGid);
                            $res1 = 1;
                            break;
                        }
                    }

                    $this->logRepairUsernameRes($type, $clientType, $passid, $username, $phone, $bindPhone, $res1, $res2, $res3);
                }
            }
        }

        if($res2)
        {
            $bind = unserialize($userInfo["m_uid"]);
            $uid = $bind[1];
            noticeToChange($passid, 'changeUsername', array(
                'passid' => $passid,
                'uid' => $uid,
                'type' => 'username',
                'value' => $newUsername,
            ));
            return $newUsername;
        }
        return "";
    }

    private function logRepairUsernameRes($type, $clientType, $passid, $username, $phone, $bindPhone, $res1, $res2, $res3)
    {
        // 观察1个月, 2016年8月30 后可删除, 日志
        $logPath = APPPATH . "/logs/" . date("Ymd") . "/phone_type_username";
        deepMkdir($logPath);
        $logger = new Logger($clientType);
        $logger->pushHandler(new StreamHandler($logPath . "/" . date('H') . ".log"));
        $logger->info(implode(" ", array(
            $type,
            $passid,
            $username,
            $phone,
            intval($bindPhone),
            intval($res1),
            intval($res2),
            intval($res3),
        )));
    }

    /**
     * 禁止用户名是手机号码并且绑定了手机号码&&手机号码不等于用户名
     * -
     * @param array $userInfo 用户信息
     * @param int $loginPhone 手机号码
     * @return bool
     */
    public function prohibitUsernameAsPhoneDiffBindPhoneLogin($userInfo, $loginPhone)
    {
        $username = $userInfo['username'];   //用户名
        $bindPhone = $userInfo['phone'];   //绑定的手机号码
        $passid = $userInfo['id'];
        $regex = Config::get('regex');
        //用户名和手机号码不为空并且用户名是手机号码的用户
        if (!empty($username) && !empty($bindPhone) && preg_match($regex['phone'], $username))
        {
            //如果登录的手机号码是用户名并且用户名和绑定的手机号码不一样 reset username && sendSmsCode
            if ( $loginPhone == $username && $username != $bindPhone)
            {
                $key = 'UsernameAsPhoneDiff:' . $passid;
                $usernameHide = substr_replace($username, '****', 3, 4);
                $bindPhoneHide = substr_replace($bindPhone, '****', 3, 4);
                $sendMsg = "您好，检测到您的帐号{$usernameHide}绑定手机{$bindPhoneHide}，请使用绑定手机进行短信验证码登录，如有疑问，联系客服：************。";
                $this->setUsernameEditableStatus($passid, $username, $key, $username, $sendMsg, $returnMsg, 6, 476);
                xLog('usernameAsPhoneLogin', 'usernameAsPhoneLogin', $returnMsg, 'info');
                return true;
            }
        }
        return false;
    }

    /**
     * 用户名作为手机号码,且进行绑定手机号码操作后，进行rename操作
     * -
     * @param int $passid passid
     * @param int $bindPhone 绑定手机号码
     * @return bool
     */
    public function usernameAsPhoneRename($passid, $bindPhone)
    {
        $memberModel = loadModel("member");
        $userInfo = $memberModel->read($passid);
        if (empty($userInfo))
        {
            xLog('noReadUser', 'noReadUser', $passid, 'info');
            return false;
        }
        $username = $userInfo['username'];
        $passid = $userInfo['id'];
        $regex = Config::get('regex');
        //如果有用户名
        if (!empty($username) && preg_match($regex['phone'], $username))
        {
            $newUsername = $this->getResetUsername($username);
            $key = 'UsernameAsPhoneRename:' . $passid;
            $usernameHide = substr_replace($username, '****', 3, 4);
            $sendMsg = "您好，由于2345帐号的用户名格式升级，您原来的用户名{$usernameHide}已被重置，请使用绑定的手机号登录passport.2345.com重新设置用户名。";
            $this->setUsernameEditableStatus($passid, $newUsername, $key, $bindPhone, $sendMsg, $returnMsg, 6, 475);
            xLog('bindPhoneRename', 'bindPhoneRename', $returnMsg, 'info');
        }
        return true;
    }

    /**
     * 生成重置用户名
     * -
     * @param string $username 用户名
     * @return string
     */
    private function getResetUsername($username)
    {
        $memberModel = loadModel('member');
        $newUsername = '';
        for ($i = 1; $i < 6; $i++)
        {
            $newUsername = $username . "_$i";
            $res = $memberModel->getPassidByUsername($newUsername);
            if (!$res)
            {
                break;
            }
        }
        return $newUsername;
    }

    /**
     * 设置用户名可编辑状态
     * -
     * @param int $passid passid
     * @param string $newUsername username
     * @param string $key key
     * @param int $sendPhone 发送短信的手机号码
     * @param string $sendMsg 发送记录
     * @param string $returnMsg 调用返回的内容
     * @param int $smsType 短信类型
     * @param int $postionId 短信位置
     * @return bool
     */
    private function setUsernameEditableStatus($passid, $newUsername, $key, $sendPhone, $sendMsg, &$returnMsg, $smsType = 6, $postionId = 476)
    {
        $returnMsg .= 'passid=' . $passid . ' phone=' . $sendPhone;
        $ignoreGid = true;
        $memberModel = loadModel('member');
        $status = $memberModel->setUsername($passid, $newUsername, $ignoreGid);
        if ($status !== false)
        {
            $returnMsg .= ' setNameStatus:1';
            $redis = RedisEx::getInstance();
            $expireTime = strtotime(date('Y-m-d', strtotime('+1 day'))) - 1 - time();
            $sendRecord = $redis->get($key);
            if (empty($sendRecord))
            {
                $retStatus = $this->sendNoticeSms($sendPhone, $sendMsg, $smsType, $postionId);
                if ($retStatus)
                {
                    $returnMsg .= ' sendStatus:1';
                    $redis->setex($key, $expireTime, 1);
                    return true;
                }
                else
                {
                    $returnMsg .= ' sendStatus:-1';
                    return false;
                }
            }
            else
            {
                $returnMsg .= ' sendStatus:2';
                return true;
            }
        }
        else
        {
            $returnMsg .= ' setNameStatus:-1';
            return false;
        }
    }

    /**
     * 发送通知类短信
     * -
     * @param int $phone 手机号码
     * @param string $msg 短信内容
     * @param int $smsType 短信类型
     * @param int $positionId 短信发送位置
     * @return bool
     */
    public function sendNoticeSms($phone, $msg, $smsType, $positionId)
    {
        $params = array(
            'phone'      => $phone, // 手机号码
            'msg'        => $msg, // 短信内容
            'smsType'    => $smsType, // 短信类别（固定值，1为验证码类，2为通知类）
            'pid'        => 14,
            'clientIp'   => get_client_ip(),
            'positionId' => $positionId,
            'mid'        => 'login',
        );
        if (RUNMODE == 'testing' || RUNMODE == 'development')
        {
            return true;
        }
        $info = json_decode(http_post(SMS_CENTER_DOMAIN . "/Api/Sms/Send", $params), true);
        // 添加预警埋点
        $code = (isset($info['status']) && $info['status'] == 1) ? $info['status'] : 0;
        loadAction('Warning')->record([
            'dir_type' => "w_sms",
            'type' => "sms",
            'code' => $code,
            'status' => $code == 1 ? true : false,
        ]);
        if ($info['status'] == '1')
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     * 功  能：
     *
     * @param string $mid mid
     * @return mixed
     */
    public static function getSmsType($mid)
    {
        return isset(self::$smsTypeConfig[$mid]) ? self::$smsTypeConfig[$mid] : self::$smsTypeConfig['default'];
    }

    /**
     * 功  能：
     *
     * @param string $domain forward域名
     * @param string $defaultMid 默认mid
     * @return string 对应的mid,匹配不到返回空字符串
     */
    public function getSmsCountMid($url, $defaultMid = 'login')
    {
        $configA = Config::get('smsCountProjects');
        if (!empty($configA) && is_array($configA)) {
            $domain = strtolower(parse_url($url, PHP_URL_HOST));
            if (isset($configA[$domain]) && "" != $configA[$domain]) {
                $defaultMid =  $configA[$domain];
            }
        }

        return $defaultMid;
    }
}
