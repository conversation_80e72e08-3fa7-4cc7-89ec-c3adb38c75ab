<?php
/**
 * Copyright (c) 2019,2345
 * 摘    要：
 * 作    者：liupeng
 * 修改日期：2019.04.10
 */
namespace WebLogger\Tests;

require_once __DIR__ . '/../../vendor/autoload.php';
define("ROOT_PATH", dirname(__DIR__) . "LoggerTest.php/");
define("APPPATH", dirname(dirname(__DIR__)) . '/app');

use PHPUnit\Framework\TestCase;
use WebLogger\Facade\LoggerFacade;

class LoggerTest extends TestCase
{
    /**
     * 单元测试用例
     * @return void
     */
    public function testDemo()
    {
        $resEmergency = LoggerFacade::emergency("这是 紧急2 日志 !", array('age' => 18, 'address' => '上海'));
        //$resAlert = LoggerFacade::alert("This an alert log !", array('age' => 18));
        //$resCritical = LoggerFacade::critical("This an critical log !", array('age' => 18));
        //$resError = LoggerFacade::error("This an error log !", array('age' => 18));
        //$resWarning = LoggerFacade::warning("This an warning log !", array('age' => 18));
        //$resNotice = LoggerFacade::notice("This an notice log !", array('age' => 18));
        //$resInfo = LoggerFacade::info("This an info log !");
        //$resDebug = LoggerFacade::debug("This an debug log !", array('age' => 18));

        $this->assertNotFalse($resEmergency);
        //$this->assertNotFalse($resAlert);
        //$this->assertNotFalse($resCritical);
        //$this->assertNotFalse($resError);
        //$this->assertNotFalse($resWarning);
        //$this->assertNotFalse($resNotice);
        //$this->assertTrue($resInfo);
        //$this->assertNotFalse($resDebug);
    }
}

$obj = new LoggerTest();
$obj->testDemo();
