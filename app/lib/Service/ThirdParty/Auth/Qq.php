<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/5/23
 * Time: 17:32
 */

namespace Service\ThirdParty\Auth;

use Service\Encryption\Aes\AesManager;
use Service\AccountStat\Consts\AccountStat;

class Qq extends Base
{
    /**
     * 功  能：登录
     *
     * @param string $mid mid
     * @param string $appid appid
     * @param string $qqCode $qqCode
     * @return array
     */
    public static function Login($mid, $appid, $qqCode)
    {
        $oauthAction = loadAction('oauth');
        $userBaseAction = loadAction('UserBase');
        /** @see OauthAction::qqRemote() */
        $result = $oauthAction->qqRemote($qqCode, $appid);
        //qq是否授权
        if ($result) {
            $openid = $result['openid'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'qq');
            $passid = !empty($return["passid"]) ? $return["passid"] : '';

            $ccConfig = loadAction('UnionLoginRedis')->getLoginConfig($mid, 'qq');
            $warningAction = loadAction('Warning');
            if ($ccConfig['need_bind_phone']) {//云控-强绑
                if ($passid) {
                    $userInfo = $memberModel->read($passid);
                    //qq已经绑定了，没有绑定手机号码
                    if (empty($userInfo['phone'])) {
                        return [
                            "code" => 6001,
                            "data" => [
                                'source' => '',
                                'bindPhone' => $ccConfig['need_shanyan'] ? "shanyan" : "phone",
                                'info' => (new AesManager())->aes128cbcEncrypt(
                                    json_encode(
                                        [
                                            'passid'    => $passid,
                                            'nickname'  => $result['nicknameUtf8'],
                                            'figureurl' => $result['figureurl'],
                                            'nicknameUtf8' => $result['nicknameUtf8'],
                                        ]
                                    )
                                ),
                            ],
                            'msg' => '登录失败，请重新再试',
                            'code_real' => 2100015,
                        ];
                    } else {
                        \Service\AccountStat\AccountStat::collect(
                            AccountStat::TP_LOGIN,
                            AccountStat::CTP_APP,
                            AccountStat::AC_TP_OAUTH_QQ,
                            true,
                            $mid
                        );
                        //直接登录
                        $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                        $userInfo["m_uid"] = unserialize($userInfo['m_uid']);
                        $uid = $userInfo['m_uid']['1'];
                        $username = $userInfo['username'];
                        $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;
                        $loginAction = loadAction('login');
                        $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                        $userBaseAction->updateThirdAvatarNicknameInfo($mid, $passid, $result, 'qq');
                        $warningAction->setRequestInfo(['passid' => $passid, 'login_time' => time()]);
                        return [
                            "code" => 200,
                            "data" => [
                                'source' => encryptCookieIByAes128cbc($cookie['I']),
                                'bindPhone' => '',
                                'login_type' => 1
                            ],
                            'msg' => '登录成功！',
                        ];
                    }
                } else {
                    // 提示绑定
                    $data = json_encode([
                        'id' => $openid,
                        'gender' => $result['gender'],
                        'nickname' => $result['nicknameUtf8'],
                        'figureurl' => $result['figureurl'],
                        'nicknameUtf8' => $result['nicknameUtf8'],
                    ]);
                    return [
                        "code" => 6002,
                        "data" => [
                            'source' => '',
                            'bindPhone' => $ccConfig['need_shanyan'] ? "shanyan" : "phone",
                            'info' => (new AesManager())->aes128cbcEncrypt($data),
                        ],
                        'msg' => '登录失败，请重新再试',
                        'code_real' => 2100015,
                    ];
                }
            } else {
                if (!$passid) {
                    loadAction('uploadToKafka')->addData(['event_type' => AccountStat::TP_REG]);
                    $nickname = !empty($result['nickname']) ? $result['nickname'] : '';
                    $nickname = \EncodingAction::transcoding($nickname, 'gbk');
                    $regResult = $memberModel->regOAuth(
                        'qq',
                        $openid,
                        $nickname,
                        get_client_ip(),
                        '',
                        $result['gender']
                    );
                    if ($regResult === false) {
                        \Service\AccountStat\AccountStat::collect(
                            AccountStat::TP_REG,
                            AccountStat::CTP_APP,
                            AccountStat::AC_TP_OAUTH_QQ,
                            false,
                            $mid
                        );
                        return [
                            "code" => 6010,
                            "data" => '',
                            'msg' => '登录失败，请重新再试',
                            'code_real' => 2100016,
                        ];
                    }
                    $passid = $regResult['passid'];
                    $uid = $regResult['uid'];
                    $username = $regResult['username'];
                    $userMod = $regResult['gid'] % 100 == 0 ? $regResult['gid'] : 0;

                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_REG,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_QQ,
                        true,
                        $mid
                    );
                    $loginType = 2;
                    $warningAction->setRequestInfo(['passid' => $passid, 'reg_time' => time(), 'login_time' => time()]);
                } else {
                    $userInfo = $memberModel->read($passid);
                    $userInfo["m_uid"] = unserialize($userInfo['m_uid']);
                    $uid = $userInfo['m_uid']['1'];
                    $username = $userInfo['username'];
                    $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;

                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_LOGIN,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_QQ,
                        true,
                        $mid
                    );
                    $loginType = 1;
                    $warningAction->setRequestInfo(['passid' => $passid, 'login_time' => time()]);
                }
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $loginAction = loadAction('login');
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                $userBaseAction->updateThirdAvatarNicknameInfo($mid, $passid, $result, 'qq');
                return [
                    "code" => 200,
                    "data" => [
                        'source' => encryptCookieIByAes128cbc($cookie['I']),
                        'bindPhone' => '',
                        'login_type' => $loginType
                    ],
                    'msg' => '登录成功！',
                ];
            }
        } else {
            return [
                "code" => 201,
                "data" => '',
                'msg' => '登录失败，请重新再试',
                'code_real' => 2100017,
            ];
        }
    }
}
