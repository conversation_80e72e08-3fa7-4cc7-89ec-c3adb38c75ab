<?php
use WebLogger\Facade\LoggerFacade;

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：UnionLoginRedisAction.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：07/09/2018 13:28
 */

class UnionLoginRedisAction extends Action
{
    const UNION_LOGIN_SDK_ALLOWED_MID = 'union:login:sdk:allowed:mid';
    const UNION_LOGIN_SDK_ENABLE_PACKAGE_IDS = 'union:login:sdk:enable:package:ids';
    const UNION_LOGIN_SDK_PACKAGE_VERSION_LIST = 'union:login:sdk:package:version:list';
    const N_UNION_LOGIN_SDK_CONFIG = 'n:union:login:sdk:config';
    const N_UNION_LOGIN_SDK_CONFIG_ALLOWED_MID = 'n:union:login:sdk:config:allowed:mid';
    //key版本号
    const UNION_LOGIN_SDK_PACKAGE_VERSION_LIST_VERSION_CODE = 'union:login:sdk:package:version:list:version:code';
    const N_UNION_LOGIN_SDK_CONFIG_VERSION_CODE = 'n:union:login:sdk:config:version:code';

    //游客模式
    const NORMAL_MODE = 1; //注册模式
    const TOURISTS_MODE = 2; //游客模式
    const N_TOURISTS_LOGIN_SDK_CONFIG_VERSION_CODE = 'n:tourists:login:sdk:config:version:code';
    const N_TOURISTS_LOGIN_SDK_CONFIG = 'n:tourists:login:sdk:config';
    const N_TOURISTS_LOGIN_SDK_CONFIG_ALLOWED_MID = 'n:tourists:login:sdk:config:allowed:mid';

    //渠道规则配置
    const TOURISTS_CHANNEL_UNLIMIT = 1;
    const TOURISTS_CHANNEL_EQUAL = 2;
    const TOURISTS_CHANNEL_NOTEQUAL = 2;

    //版本规则配置
    const TOURISTS_VERSION_UNLIMIT = 1;
    const TOURISTS_VERSION_EQUAL = 2;
    const TOURISTS_VERSION_NOTEQUAL = 3;
    const TOURISTS_VERSIONL_LARGE = 4;
    const TOURISTS_VERSIONL_LARGE_EQUAL = 5;
    const TOURISTS_VERSIONL_SMALL = 6;
    const TOURISTS_VERSIONL_SMALL_EQUAL = 7;

    private $redis;
    private $configModal;

    /**
     * UnionLoginRedisAction constructor.
     */
    public function __construct()
    {
        $this->redis = RedisEx::getInstance();
        $this->configModal = loadModel('UnionLoginConfig');
    }

    /**
     * User: panj
     *
     * @param string $mid mid
     *
     * @return bool
     */
    public function isAllowedMid($mid)
    {
        $allowMIds = $this->getAllowedMid();
        return isset($allowMIds[$mid]) && $allowMIds[$mid] > 0;
    }


    /**
     * User: panj
     * redis key 请求包版本号
     * @return mixed
     */
    public function getPackageListRedisKeyVersion()
    {
        $key = self::UNION_LOGIN_SDK_PACKAGE_VERSION_LIST_VERSION_CODE;
        return $this->redis->get($key);
    }

    /**
     * User: panj
     *
     * @param string $packageName package name
     * @param int $version version
     *
     * @return string
     */
    public function getPackageListRedisKey($packageName, $version)
    {
        $versionKey = $this->getPackageListRedisKeyVersion();
        $key = self::UNION_LOGIN_SDK_PACKAGE_VERSION_LIST . ':' . $packageName . ':' . $version . ':' . $versionKey;
        return $key;
    }

    /**
     * User: panj
     * @return array
     */
    public function getAllowedMid()
    {
        $redisKey = self::UNION_LOGIN_SDK_ALLOWED_MID;
        $mIdsApcu = apcu_fetch($redisKey);
        if (!empty($mIdsApcu)) {
            return $mIdsApcu;
        }
        $mIds = $this->redis->hGetAll($redisKey);
        if (!$mIds)
        {
            $mIds = $this->configModal->getAllowedMid();
            if (!$mIds)
            {
                $mIds = [];
            }
            else
            {
                $this->redis->hMSet($redisKey, $mIds);
            }
        }
        if (!empty($mIds)) {
            $isStore = apcu_store($redisKey, $mIds, Config::get("cloudApcuExpire"));
            if (!$isStore) {
                LoggerFacade::error("云控apcu:设置失败-mid", $isStore);
            }
        }

        return $mIds;
    }

    /**
     * User: panj
     * del allowed mid cache
     * @return mixed
     */
    public function delAllowedMidCache()
    {
        $redisKey = self::UNION_LOGIN_SDK_ALLOWED_MID;
        return $this->redis->del($redisKey);
    }

    /**
     * User: panj
     *
     * @param string $packageName package name
     * @param int $versionCode version code
     *
     * @return bool|mixed
     */
    public function getPackageList($packageName, $versionCode)
    {
        $packageApcuKey = self::UNION_LOGIN_SDK_PACKAGE_VERSION_LIST . ':' . $packageName . ':' . $versionCode;
        $packageData = apcu_fetch($packageApcuKey);
        if (!empty($packageData)) {
            return unserialize($packageData);
        }
        $key = self::getPackageListRedisKey($packageName, $versionCode);
        $package = $this->redis->get($key);
        if (!$package)
        {
            $res = $this->configModal->getByPackageVersion($packageName, $versionCode);
            if (!$res)
            {
                return false;
            }
            $package = serialize($res);
            $this->redis->set($key, $package, 7 * 86400);
        }
        if (!empty($package)) {
            $isStore = apcu_store($packageApcuKey, $package, Config::get("cloudApcuExpire"));
            if (!$isStore) {
                LoggerFacade::error("云控apcu:设置失败-getPackageList", $isStore);
            }
        }

        return unserialize($package);
    }

    /**
     * User: panj
     *
     * @return mixed
     */
    public function delPackageCache()
    {
        $versionKey = self::UNION_LOGIN_SDK_PACKAGE_VERSION_LIST_VERSION_CODE;
        return $this->redis->incr($versionKey) && $this->redis->expire($versionKey, 7 * 86400);
    }

    /**
     * User: panj
     * @return mixed
     */
    public function getEnablePackageByIds()
    {
        $redisKey = self::UNION_LOGIN_SDK_ENABLE_PACKAGE_IDS;
        $cacheData = apcu_fetch($redisKey);
        if (!empty($cacheData)) {
            return unserialize($cacheData);
        }
        $cacheData = $this->redis->get($redisKey);
        if (!$cacheData)
        {
            $data = $this->configModal->getEnablePackageByIds();
            $cacheData = serialize($data);
            $this->redis->set($redisKey, $cacheData);
        }

        if (!empty($cacheData)) {
            $isStore = apcu_store($redisKey, $cacheData, Config::get("cloudApcuExpire"));
            if (!$isStore) {
                LoggerFacade::error("云控apcu:设置失败-getEnablePackageByIds", $isStore);
            }
        }

        return unserialize($cacheData);
    }

    /**
     * User: panj
     * @return mixed
     */
    public function updateEnablePackageCache()
    {
        return $this->redis->del(self::UNION_LOGIN_SDK_ENABLE_PACKAGE_IDS);
    }

    /**
     * 功    能：获取配置缓存
     * 修改日期：2019年5月29日
     *
     * @param string $mid 唯一标识
     * @param int $mode 登录模式
     * @author: wangchenglong
     * @return bool|array
     */
    public function getConfigCache($mid, $mode = '')
    {
        $mode = $mode ?: self::NORMAL_MODE;
        $flag = $this->checkKey($mid, $mode);
        if (empty($flag)) {
            return array();
        }
        $midConfigCacheKey = "mid:config:cache:key:{$mid}:{$mode}";
        $config = apcu_fetch($midConfigCacheKey);
        if (empty($config)) {
            $configKey = self::getConfigRedisKey($mid, $mode);
            $config = $this->redis->get($configKey);
            if (empty($config)) {
                $loginConfigModel = loadModel('loginConfig');
                $res = $loginConfigModel->getConfigInfo($mid, $mode);
                if (!$res) {
                    $config = serialize(array());
                    $this->redis->set($configKey, $config, 1);
                } else {
                    $config = serialize($res);
                    $this->redis->set($configKey, $config, 7 * 86400);
                }
            }
            if (!empty(unserialize($config))) {
                $isStore = apcu_store($midConfigCacheKey, $config, Config::get("cloudApcuExpire"));
                if (!$isStore) {
                    LoggerFacade::error("云控apcu:设置失败-getConfigCache", $isStore);
                }
            }
        }

        return unserialize($config);
    }

    /**
     * 功    能：删除配置缓存
     * 修改日期：2019年5月29日
     *
     * @param int $mode 登录模式
     * @param string $mid 唯一标识
     * @author: wangchenglong
     * @return bool
     */
    public function delConfigCache($mode, $mid = '')
    {
        if ($mid) {
            $versionKey = (($mode == self::NORMAL_MODE) ? self::N_UNION_LOGIN_SDK_CONFIG_VERSION_CODE : self::N_TOURISTS_LOGIN_SDK_CONFIG_VERSION_CODE) . ':' . $mid;
            $number = $this->redis->get($versionKey);
            if (!$number) {
                $number = 1;
            } else {
                $number = $number + 1;
            }
            $key = (($mode == self::NORMAL_MODE) ? self::N_UNION_LOGIN_SDK_CONFIG : self::N_TOURISTS_LOGIN_SDK_CONFIG) . ':' . $mid . ':' . $number;
            $loginConfigModel = loadModel('loginConfig');
            $res = $loginConfigModel->getConfigInfo($mid, $mode);
            if ($res) {
                $config = serialize($res);
                $this->redis->set($key, $config, 7 * 86400);
            }
        } else {
            $versionKey = (($mode == self::NORMAL_MODE) ? self::N_UNION_LOGIN_SDK_CONFIG_VERSION_CODE : self::N_TOURISTS_LOGIN_SDK_CONFIG_VERSION_CODE) . ':';
            $number = $this->redis->get($versionKey);
            if (!$number) {
                $number = 1;
            } else {
                $number = $number + 1;
            }
            $key = (($mode == self::NORMAL_MODE) ? self::N_UNION_LOGIN_SDK_CONFIG_ALLOWED_MID : self::N_TOURISTS_LOGIN_SDK_CONFIG_ALLOWED_MID) . ':' . $number;
            $loginConfigModel = loadModel('loginConfig');
            $res = $loginConfigModel->getAllConfigMid($mode);
            if ($res) {
                $midArr = serialize($res);
                $this->redis->set($key, $midArr, 7 * 86400);
            }
        }
        return $this->redis->incr($versionKey) && $this->redis->expire($versionKey, 7 * 86400);
    }

    /**
     * 功    能：获取配置缓存key
     * 修改日期：2019年5月29日
     *
     * @param string $mid 唯一标识
     * @param int $mode 登录模式
     * @author: wangchenglong
     * @return string
     */
    public function getConfigRedisKey($mid, $mode)
    {
        $versionKey = ($mode == self::NORMAL_MODE) ? $this->getConfigRedisKeyVersion($mid) : $this->getTouristsConfigRedisKeyVersion($mid);
        $key = ($mode == self::NORMAL_MODE) ? self::N_UNION_LOGIN_SDK_CONFIG : self::N_TOURISTS_LOGIN_SDK_CONFIG;
        $key .= ':' . $mid . ':' . $versionKey;
        return $key;
    }


    /**
     * 功    能：获取配置缓存key版本
     * 修改日期：2019年5月29日
     *
     * @param string $mid 唯一标识
     * @author: wangchenglong
     * @return string
     */
    public function getConfigRedisKeyVersion($mid = '')
    {
        $key = self::N_UNION_LOGIN_SDK_CONFIG_VERSION_CODE . ':' . $mid;
        return $this->redis->get($key);
    }

    /**
     * 功    能：根据mid和登录类型获取登录配置
     * 修改日期：2019年5月29日
     *
     * @param string $mid 唯一标识
     * @param string $loginType 登录方式
     * @author: wangchenglong
     * @return bool|array
     */
    public function getLoginConfig($mid, $loginType)
    {
        $loginConfig = array(
            'need_bind_phone' => false,
            'need_shanyan' => false,
        );

        $config = $this->getConfigCache($mid);
        if (!$config || empty($config['detail_data'])) {
            return $loginConfig;
        }

        foreach ($config['detail_data'] as $key => $value) {
            if (strtolower($value['short_name']) == strtolower($loginType)) {
                switch ($value['compel_bind_phone']) {
                    case 1:
                        $loginConfig['need_bind_phone'] = true;
                        break;
                    case 2:
                        $loginConfig['need_bind_phone'] = true;
                        $loginConfig['need_shanyan'] = true;
                        break;
                    default:
                        $loginConfig['need_bind_phone'] = false;
                        $loginConfig['need_shanyan'] = false;
                }
            }
        }
        return $loginConfig;
    }

    /**
     * 功    能：根据mid获取显示验证码需要的短信数
     * 修改日期：2019年5月29日
     *
     * @param string $mid 唯一标识
     * @author: wangchenglong
     * @return int
     */
    public function getShowCaptchaNum($mid)
    {
        $ShowCaptchaNum = 5;
        $config = $this->getConfigCache($mid);
        if ($config && isset($config['show_captcha_num'])) {
            $ShowCaptchaNum = (int)$config['show_captcha_num'];
        }
        return $ShowCaptchaNum;
    }

    /**
     * 功    能：查询key是否有效
     * 修改日期：2019年5月29日
     *
     * @param string $mid 唯一标识
     * @param int $mode 登录模式
     * @author: wangchenglong
     * @return bool
     */
    public function checkKey($mid, $mode)
    {
        $midConfigLoginKey = "mid:config:key:{$mode}";
        $midArr = apcu_fetch($midConfigLoginKey);
        if (empty($midArr)) {
            $midArr = $this->getLoginConfigMidCache($mode);
            if (!empty($midArr)) {
                $isStore = apcu_store($midConfigLoginKey, $midArr, Config::get("cloudApcuExpire"));
                if (!$isStore) {
                    LoggerFacade::error("云控apcu:设置失败-mid", $isStore);
                }
            }
        }
        if ($midArr && array_key_exists($mid, $midArr)) {
            return true;
        }
        return false;
    }

    /**
     * 功    能：获取mid缓存
     * 修改日期：2019年5月29日
     *
     * @param int $mode 登录模式
     * @author: wangchenglong
     * @return array
     */
    public function getLoginConfigMidCache($mode = '')
    {
        $mode = $mode ?: self::NORMAL_MODE;
        $key = ($mode == self::NORMAL_MODE) ? $this->getLoginConfigMidKey() : $this->getTouristsConfigMidKey();
        $midArr = $this->redis->get($key);
        if (empty($midArr)) {
            $loginConfigModel = loadModel('loginConfig');
            $res = $loginConfigModel->getAllConfigMid($mode);
            if (!$res) {
                $midArr = serialize($res);
                $this->redis->set($key, $midArr, 1);
            } else {
                $midArr = serialize($res);
                $this->redis->set($key, $midArr, 7 * 86400);
            }
        }
        return unserialize($midArr);
    }

    /**
     * 功    能：获取mid key
     * 修改日期：2019年5月29日
     *
     * @author: wangchenglong
     * @return string
     */
    public function getLoginConfigMidKey()
    {
        $versionKey = $this->getConfigRedisKeyVersion();
        $key = self::N_UNION_LOGIN_SDK_CONFIG_ALLOWED_MID . ':' . $versionKey;
        return $key;
    }

    /**
     * 功    能：获取游客模式配置缓存key版本
     * 修改日期：2020年8月26日
     *
     * @param string $mid 唯一标识
     * @author: lvqf
     * @return string
     */
    public function getTouristsConfigRedisKeyVersion($mid = '')
    {
        $key = self::N_TOURISTS_LOGIN_SDK_CONFIG_VERSION_CODE . ':' . $mid;
        return $this->redis->get($key);
    }

    /**
     * 功    能：获取游客模式mid key
     * 修改日期：2020年8月27日
     *
     * @author: lvqf
     * @return string
     */
    public function getTouristsConfigMidKey()
    {
        $versionKey = $this->getTouristsConfigRedisKeyVersion();
        $key = self::N_TOURISTS_LOGIN_SDK_CONFIG_ALLOWED_MID . ':' . $versionKey;
        return $key;
    }
}
