<?php

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称：randCaptcha.php
 * 摘    要：验证码
 * 作    者：张小虎
 * 修改日期：2012.09.27
 */
session_start();

class code
{

    private $CodeNum = 4;               //默认显示四位验证码
    private $CheckCode = '';            //产生的验证码
    private $CheckImage = '';           //验证码的图片
    private $ImageWidth;                //验证码的图片宽度
    private $ImageHeight;               //验证码的图片宽度
    private $RandString = '0123456789'; //默认字符串
    private $FontSize = 20;             //生成字体的大小
    private $FunMin = '2';              //从第几个函数开始
    private $FunMax = '2';              //产生随机数的函数的总数目
    private $FontFamily;                //默认字体
    private $LineNum = 3;                //干扰线的数目
    private $PointNum = 100;             //干扰点的数目
    private $BackGround = '#F1F1F1';    //生成背景颜色

    public function __construct($width = '', $height = '')
    {
        $this->ImageWidth = !empty($width) ? intval($width) : 70;
        $this->ImageHeight = !empty($height) ? intval($height) : 34;
    }

    /**
     * @method GetRandNum 获取1234或1+2
     * @return type string $randnum
     */
    private function GetRandNum()
    {
        $funNum = rand($this->FunMin, $this->FunMax);
        switch ($funNum)
        {
            case 1:
                $randnum = $this->createRandNum1();
                break;
            case 2:
                $randnum = $this->createRandNum2();
                break;
            case 3:
                $randnum = $this->createRandNum3();
                break;
        }
        $_SESSION['checkIMGCode_new'] = $randnum['result'];
        return $this->CheckCode = $randnum;
    }

    /**
     * 生成图片
     */
    private function CreateImage()
    {
        $this->CheckImage = imagecreate($this->ImageWidth, $this->ImageHeight);
        $backcolor = imagecolorallocate($this->CheckImage, 0xFF, 0xFF, 0xFF);
        $font = imagecolorallocate($this->CheckImage, hexdec(substr($this->BackGround, 1, 2)), hexdec(substr($this->BackGround, 3, 2)), hexdec(substr($this->BackGround, 5, 2)));
        imagerectangle($this->CheckImage, 0, 0, $this->ImageWidth - 1, $this->ImageHeight - 1, $font);
    }

    /**
     * 生成随机字体
     */
    private function CreateFont()
    {
        $RandFontNum = rand(1, 8);
        $this->FontFamily = "fonts/" . $RandFontNum . ".TTF";
    }

    /**
     * 画点
     */
    private function CreatePoint()
    {
        for ($i = 0; $i <= $this->PointNum; $i++)
        {
            $pointcolor = imagecolorallocate($this->CheckImage, rand(0, 255), rand(0, 255), rand(0, 255));
            imagesetpixel($this->CheckImage, rand(0, $this->ImageWidth), rand(0, $this->ImageHeight), $pointcolor);
        }
    }

    /**
     * 画线
     */
    private function CreateLine()
    {
        for ($i = 0; $i < $this->LineNum; $i++)
        {
            $lineColor = imagecolorallocate($this->CheckImage, mt_rand(0, 256), mt_rand(0, 256), mt_rand(0, 256));
            $h1 = mt_rand(0, $this->ImageHeight);
            $h2 = mt_rand(0, $this->ImageHeight);
            imageline($this->CheckImage, 0, $h1, $this->ImageWidth, $h2, $lineColor);
            $h1++;
            $h2++;
            imageline($this->CheckImage, 0, $h1, $this->ImageWidth, $h2, $lineColor);
        }
    }

    /**
     * 将随机数写入图片
     */
    private function WriteCodeToImage()
    {
        $randnum = $this->GetRandNum();
        for ($i = 0; $i < $this->CodeNum; $i++)
        {
            if (is_numeric($randnum['num'][$i]))
            {
                $this->CreateFont();
            }
            else
            {
                $this->FontFamily = "fonts/7.TTF";
            }
            $fontcolor = imagecolorallocate($this->CheckImage, rand(0, 255), 0, rand(0, 255));
            $x = $this->ImageWidth / $this->CodeNum;
            $y = $this->ImageHeight / 1.4;
            imagettftext($this->CheckImage, $this->FontSize, rand(-10, 10), $x * $i, $y, $fontcolor, $this->FontFamily, $randnum['num'][$i]);
        }
    }

    /**
     * 生成验证图片
     */
    public function ShowImage()
    {
        Header("Content-type: image/PNG");
        if (substr($_SERVER['HTTP_REFERER'], 0, 25) == 'http://ie.5500w.com/jifen')
        {
            header('P3P: CP="CURa ADMa DEVa PSAo PSDo OUR BUS UNI PUR INT DEM STA PRE COM NAV OTC NOI DSP COR"');
        }
        $this->CreateImage();
        $this->CreateLine();
        $this->CreatePoint();
        $this->WriteCodeToImage();
        imagepng($this->CheckImage);
        imagedestroy($this->CheckImage);
    }

    /**
     * @method createRandNum1   产生1234类型的验证码
     * @return type $returnCode['num'] 写入图片的数据
     * @return type $returnCode['result'] 写入session的数据
     */
    private function createRandNum1()
    {
        $returnCode = array();
        $randCode = '';
        for ($i = 0; $i < $this->CodeNum; $i++)
        {
            $cutstrnum = mt_rand(0, strlen($this->RandString) - 1);
            $randCode.=trim(substr($this->RandString, $cutstrnum, 1));
        }
        $returnCode['num'] = $randCode;
        $returnCode['result'] = $randCode;
        return $returnCode;
    }

    /**
     * @method createRandNum2   产生1+2格式的验证码
     * @return type $returnCode['num'] 写入图片的数据
     * @return type $returnCode['result'] 写入session的数据
     */
    private function createRandNum2()
    {
        $returnCode = array();
        $operType = (rand(0, 1)) ? '+' : '-';
        $randNum1 = rand(0, 9);
        $randNum2 = rand(0, 9);
        if ($operType == '-')
        {
            if ($randNum1 > $randNum2)
            {
                $returnCode['num'] = $randNum1 . '-' . $randNum2 . '=';
                $returnCode['result'] = $randNum1 - $randNum2;
            }
            else
            {
                $returnCode['num'] = $randNum2 . '-' . $randNum1 . '=';
                $returnCode['result'] = $randNum2 - $randNum1;
            }
        }
        else
        {
            $returnCode['num'] = $randNum2 . '+' . $randNum1 . '=';
            $returnCode['result'] = $randNum2 + $randNum1;
        }
        return $returnCode;
    }

    /**
     * @method  createRandNum3  产生中文字符串
     * @return type $returnCode['num'] 写入图片的数据
     * @return type $returnCode['result'] 写入session的数据
     */
    public function createRandNum3()
    {
        $returnCode = array();
        $string = '';
        for ($i = 0; $i < $this->CodeNum; $i++)
        {
            $string = $string . "," . chr(rand(0xB0, 0xCC)) . chr(rand(0xA1, 0xBB));
        }
        $returnCode['num'] = explode(',', substr($string, 1));
        $returnCode['result'] = implode('', $returnCode['num']);
        return $returnCode;
    }

}

$img = new code();
$img->ShowImage();
?>