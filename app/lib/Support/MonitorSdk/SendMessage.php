<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：SendMessage.php
 * 摘    要：用于接受项目自定义错误和向客户端发送错误
 * 作    者：赵荣 + lynn
 * 修改日期：2016.2.22 + 20160712
 */

namespace Support\MonitorSdk;

class SendMessage
{

    //错误类型
    const TYPE_CUSTOM = 100;
    const TYPE_NETWORK = 200;
    const TYPE_APACHE = 300;
    const TYPE_PHP = 400;
    const TYPE_MYSQL = 500;
    const TYPE_REDIS = 600;
    const TYPE_SPHINX = 700;
    const TYPE_MONGODB = 800;
    const TYPE_SERVER = 900;
    //报警等级
    const LEVEL_DEBUG = 1;
    const LEVEL_INFO = 2;
    const LEVEL_NOTICE = 3;
    const LEVEL_WARNING = 4;
    const LEVEL_ERROR = 5;
    const LEVEL_CRITICAL = 6;
    const LEVEL_ALERT = 7;
    const LEVEL_EMERGENCY = 8;

    //项目域名
    private $domain;
    //检测脚本路径
    private $filepath;

    /**
     * 构造函数
     *
     * @param string $domain 项目域名
     * @param string $filepath 检测脚本路径
     * @param string $withHeartbeat 是否启用心跳检测，如果不是常规检测任务请不要启用心跳检测
     * @return void
     */
    public function __construct($domain, $filepath = "", $withHeartbeat = false)
    {
        $this->domain = $domain;
        $this->filepath = $filepath;
        if ($withHeartbeat)
        {
            //$this->sendHeartbeat();
        }
    }

    /**
     * 发送错误
     *
     * @param string $title 报错标题
     * @param string $content 报错内容
     * @param int $count 计数
     * @param int $errorType 错误类型
     * @param int $errorLevel 错误级别
     * @return bool
     */
    public function sendError($title, $content, $count = 1, $errorType = self::TYPE_CUSTOM, $errorLevel = self::LEVEL_NOTICE)
    {
        $sendData = array(
            'domain' => (string) $this->domain,
            'dir_type' => 'monitorSdk',
            'filepath' => (string) $this->filepath,
            'title' => (string) $title,
            'content' => (string) $content,
            'count' => intval($count),
            'errorType' => intval($errorType),
            'level' => intval($errorLevel),
            'class' => __CLASS__,
            'method' => __METHOD__,
            'line' => __LINE__,
        );
        return $this->sendMessage($sendData);
    }

    /**
     * 发送心跳
     *
     * @return bool
     */
    public function sendHeartbeat()
    {
        $sendData = array(
            'domain' => $this->domain,
            'filepath' => $this->filepath,
            'errorType' => 0,
        );
        $data = json_encode($sendData);
        return $this->sendMessage($data);
    }

    /**
     * 设置检测脚本路径
     * 
     * @param string $filepath 检测脚本路径
     * @return void
     */
    public function setFilePath($filepath)
    {
        $this->filepath = $filepath;
    }

    /**
     * 发送消息
     * 
     * @param array $data 消息内容
     * @return bool
     */
    private function sendMessage(array $data)
    {
        loadAction('Warning')->record($data);
        return true;
    }

}
