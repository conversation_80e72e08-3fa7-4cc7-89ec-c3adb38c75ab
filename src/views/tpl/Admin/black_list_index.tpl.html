<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台-手机黑名单</title>
    <link rel="stylesheet" href="/style/css/global.css">
    <script src="/style/js/jquery-1.8.3.min.js"></script>
    <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
    <!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a> > <span>手机黑名单</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="/Admin/BlackList/Index">手机黑名单</a>
        <a href="/Admin/BlackList/WriteList">手机白名单</a>
        <a href="/Admin/BlackList/IpWriteList">IP白名单</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->

    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
        <form action="" method="post" id="queryFormDiv">
            <div class="dataWrap clearfix">
                <em for="blackTypeSelect">黑名单分类：</em>
                <select class="selectMod w90" name="blackType" id="blackTypeSelect">
                    <option value="0">全部</option>
                    {{foreach from=$pageArray.balckTypeList key=balckTypeKey item=balckTypeInfo}}
                    <option value="{{$balckTypeKey}}" {{if $pageArray.blackType eq $balckTypeKey}}selected="selected" {{/if}} >{{$balckTypeInfo}}</option>
                    {{/foreach}}}
                </select>
            </div>
            <span class="input-search ml10"><i>搜索Icon</i><input value="{{$pageArray.phone}}" type="text" autocomplete="off" name="phone" placeholder="手机号码"/></span>
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 querySubmitForm">查询</a>
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 exportBlackPhone">批量导出</a>
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    <div class="funList mt20 clearfix">
        <a class="styleBtn_s styleBtnBlue left addblackClass" href="javascript:">添加黑名单</a>
        <a style="margin-left: 5px;" class="styleBtn_s styleBtnBlue left" target="_blank" href="/Admin/SmsProject/SmsSyncBlackPhoneKey">更新手机黑名单</a>
        <form action="/Admin/BlackList/BlackPhoneUpload" method="post" enctype="multipart/form-data">
            <input type="file" style="margin-left: 5px;" name="Filedata"/>
            <input type="hidden" name="blackType" class="blackTypeClass"/>
            <input type="submit" value="批量导入"/>
        </form>


    </div>
    <!--主体内容 开始-->
    <div class="contentWrap">

        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>黑名单号码</th>
                        <th>黑名单分类</th>
                        <th>经手人</th>
                        <th>操作时间</th>
                        <th>备注</th>
                        <th>-</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->

        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th>黑名单号码</th>
                <th>黑名单分类</th>
                <th>经手人</th>
                <th>操作时间</th>
                <th>备注</th>
                <th>-</th>
            </tr>
            {{foreach from=$pageArray.blackPhoneList item=blackInfo}}
            <tr>
                <td>{{$blackInfo.phone}}</td>
                <td>{{$pageArray.balckTypeList[$blackInfo.black_type]}}</td>
                <td>{{$blackInfo.addName}}</td>
                <td>{{$blackInfo.add_time}}</td>
                <td>{{$blackInfo.phone_note}}</td>
                <td><a href="javascript:" bpl_id="{{$blackInfo.bpl_id}}" class="delblackClass">解禁</a></td>
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->

        {{$pageArray.showPage}}

    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->

<!--透明遮罩-->
<div class="boxMask" style="display: none;">透明遮罩层</div>
<!--添加权限 弹窗 开始-->
<div class="boxMod w520 addBlackDiv">
    <div class="boxTop"><h2 class="boxTit">添加手机黑名单</h2><a class="BoxClose" href="javascript:">关闭按钮</a></div>
    <div class="tabFrom mtb20">
        <form id="blackListFromId">
            <table class="tabFromMod" cellpadding="0" cellspacing="0">
                <tr>
                    <td class="tabLeftTit">黑名单分类：</td>
                    <td>
                        <select class="tabSelect mt10" name="blackList[blackType]">
                            <option value="0">-</option>
                            {{foreach from=$pageArray.balckTypeList key=balckTypeKey item=balckTypeInfo}}
                            <option value="{{$balckTypeKey}}">{{$balckTypeInfo}}</option>
                            {{/foreach}}}
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="tabLeftTit">手机号码：</td>
                    <td><input class="tabInput" type="text" autocomplete="off" name="blackList[phone]"/></td>
                </tr>
                <tr class="surePhone">
                    <td class="tabLeftTit">号码备注：</td>
                    <td><input class="tabInput phoneTwo" type="text" autocomplete="off" name="blackList[phoneNote]"/></td>
                </tr>
                <tr>
                    <td colspan="2" align="center">
                        <input type="hidden" name="blackList[bpl_id]"/>
                        <input type="hidden" name="blackList[status]" value="1"/>
                        <a class="styleBtnBlue tabsave submitBlackList" href="javascript:">保存</a></td>
                </tr>
            </table>
        </form>
    </div><!--tabFrom end-->
</div><!--boxMod end-->
<!--添加权限 弹窗 结束-->


<script src="/style/js/tableFixed.js"></script>
<script src="/style/js/boxLocation.js"></script>
<script>
    $('.addblackClass').click(function () {
        $('.boxMask').show();
        $('.addBlackDiv').show();
        //$('.surePhone').show();

        $('select[name="blackList[blackType]"]').val(0);
        $('input[name="blackList[phone]"]').val('');
        $('input[name="blackList[bpl_id]"]').val('');

    });

    $('.submitBlackList').click(function () {
        var phone = $('input[name="blackList[phone]"]').val();
        var phoneNote = $('input[name="blackList[phoneNote]"]').val();
        var blackType = $('select[name="blackList[blackType]"]').val();

        if (blackType === 0) {
            alert('类型不能为空');
            return false;
        }
        if (phone === '') {
            alert('手机号码不能为空');
            return false;
        }

        $.post('/Admin/BlackList/SetPhoneBlackInfo', $('#blackListFromId').serialize(), function (data) {
            if (data.status === 'P00001' || data.status === 'P00011') {
                alert(data.msg);
                window.location = '/Admin/BlackList/Index';
            }
            else {
                alert(data.msg)
            }
        });
    });

    $('.editblackClass').click(function () {
        $('.boxMask').show();
        $('.addBlackDiv').show();
        var bpl_id = $(this).attr('bpl_id');
        $.post('/Admin/BlackList/GetBlackInfo', 'bpl_id=' + bpl_id, function (data) {
            $('select[name="blackList[blackType]"]').val(data.data.black_type);
            $('input[name="blackList[phone]"]').val(data.data.phone);
            $('input[name="blackList[bpl_id]"]').val(bpl_id);
            $('.phoneTwo').val(data.data.phone);
            //$('.surePhone').hide();
        })

    })

    $('.delblackClass').click(function () {
        if (confirm('确定解禁吗?')) {
            var bpl_id = $(this).attr('bpl_id');
            $.post('/Admin/BlackList/DelBlackPhone', 'bpl_id=' + bpl_id, function (data) {

                if (data.status == 'P00001') {
                    window.location = '/Admin/BlackList/Index';
                }
                else {
                    alert(data.msg);
                }
            })
        }

    });
    $('.BoxClose').click(function () {
        $('.boxMask').hide();
        $('.boxMod').hide();
    });

    $('.querySubmitForm').click(function () {
        $('#queryFormDiv').submit();
    });

    $('.exportBlackPhone').click(function () {
        var link = '/Admin/BlackList/Index?act=export&' + $('#queryFormDiv').serialize();
        window.open(link);
    });

    $('.selectBlackType').change(function () {
        $('.blackTypeClass').val($(this).val());
    });
</script>
</body>
</html>