<?php

includeBase('AdminController');

/**
 * Copyright (c) 2014,上海二三四五网络科技有限公司
 * 文件名称：LoginIpLogController.php
 * 摘    要：最近登录IP日志控制器
 * 作    者：张小虎
 * 修改日期：2014.12.15
 */
class LoginIpLogController extends AdminController
{

    protected $authName = '最近登录IP日志';

    /**
     * 默认方法
     */
    public function actionIndex()
    {
        $domain = $_GET['domain'];
        $allServerDomains = Config::get("domainServerIPs");
        if (isset($allServerDomains[$domain]))
        {
            $loginIpLogKey = "loginIpLog:$domain";
        }
        else
        {
            $loginIpLogKey = "loginIpLog:login.2345.com";
        }
        $redis = RedisEx::getInstance();
        $allowDomains = array();
        foreach ($allServerDomains as $domain => $serverIp)
        {
            if ($redis->ttl("loginIpLog:$domain") > 0)
            {
                $allowDomains[] = $domain;
            }
        }
        $ipLogs = array();
        $ipRanges = $redis->lRange($loginIpLogKey, 0, 1499);
        foreach ($ipRanges as $ipLog)
        {
            $ipLog = explode("#", $ipLog);
            $ipLogs[$ipLog[0]][] = $ipLog[1];
        }
        $pageArray['ipLogs'] = $ipLogs;
        $pageArray['allowDomains'] = $allowDomains;
        loadView('admin/login_ip_log.tpl.html', $pageArray);
    }

}
