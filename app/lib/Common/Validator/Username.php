<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：Username.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：09-20, 2016
 */

namespace Common\Validator;

use Common\Msg\ResponseMsg;

class Username extends Base
{

    public static function validator($value, array $args)
    {
        $res = array(ResponseMsg::SUCCESS, "正确的用户名");
        $regex = \Config::get('regex');
        if (preg_match($regex['username'], $value))
        {
            $res = ResponseMsg::getCodeMsgFormat(ResponseMsg::PARAMS_FORMAT, "不能包含特殊符号");
        }
        return $res;
    }

}