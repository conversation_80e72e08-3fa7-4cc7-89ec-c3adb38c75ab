<?php
/**
 * Copyright (c)  上海二三四五网络科技有限公司
 * 文件名称：ChuangLan253ChannelAction.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：04-22, 2017
 */

namespace Smschannel;

use FuncAction;

class ChuangLan253ChannelAction extends SmsChannelAction
{
    protected static $commitCode = array(
        0   => '提交成功',
        101 => '无此用户',
        102 => '密码错',
        103 => '提交过快,超过流速限制',
        104 => '系统忙,第三方平台原因',
        105 => '敏感短信',
        106 => '消息长度错',
        107 => '包含错误的手机号码',
        108 => '手机号码个数错',//（群发>50000或<=0）
        109 => '无发送额度',//（该用户可用短信数已使用完）
        110 => '不在发送时间内',
        113 => '扩展码格式错',//（非数字或者长度不对）
        114 => '可用参数组个数错误',//（小于最小设定值或者大于1000）;变量参数组大于5个
        116 => '签名错误',//（用户必须带签名的前提下）
        117 => 'IP地址认证错',//,请求调用的IP地址不是系统登记的IP地址
        118 => '用户没有相应的发送权限',//（账号被禁止发送）
        119 => '用户已过期',
        120 => '违反防盗用策略',//(日发送限制)
        123 => '发送类型错误',
        124 => '白模板匹配错误',
        125 => '匹配驳回模板，提交失败',
        127 => '定时发送时间格式错误',
        128 => '内容编码失败',
        129 => 'JSON格式错误',
        130 => '请求参数错误',//（缺少必填参数）
    );

    protected static $reportCode = array();

    /**
     * ChuangLan253ChannelAction constructor.
     *
     * @param string $user   username
     * @param string $pwd    password
     * @param string $apiUrl api url
     */
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }

    /**
     * 发送短信, 部分短信平台群发可能不一致, 但是这个通道支持
     *
     * @param string $phone 手机号数组
     * @param string $msg   短信内容
     * @param string $sign  sign
     *
     * @return int 1 发送成功 0 发送失败
     */
    public function send($phone, $msg, $sign)
    {
        $res = $this->multiPhoneSend(array($phone), $msg, $sign);

        $this->msgId = $this->getMultiReturnMsgId($phone);
        $this->OperatorMsg = $this->getMultiReturnMsg($phone);

        return $res;
    }

    /**
     * 相同内容群发
     *
     * @param array  $phones 手机号
     * @param string $msg    短信内容
     * @param string $sign   sign
     *
     * @return int 1 发送成功 0 发送失败
     */
    public function multiPhoneSend($phones, $msg, $sign)
    {
        $data = array(
            "account"  => $this->user,
            "password" => $this->pwd,
            'msg'      => urlencode(\EncodingAction::transcoding($sign . $msg, 'utf-8')),
            "phone"    => implode(",", $phones),
            "sendtime" => "",//定时发送，选填,格式: ************
            "report"   => "true",
            "uid"      => self::getMillisecond(),
        );
        $url = $this->apiUrl . "/msg/send/json";
        $response = FuncAction::postTimeout($url, json_encode($data), 5, false, true);
        $response = json_decode($response, true);
        $response = \EncodingAction::iteratorArray($response, "GBK");

        if ($response && isset($response['code']) && $response['code'] == 0)
        {
            foreach ($phones as $key => $phone)
            {
                $this->msgIdList[$phone] = $response['msgId'];
                $this->SetMultiMsgArray($phone, "0", '提交成功');
            }

            return self::RET_SUCC;
        }
        else
        {
            $errorMsg = isset(self::$commitCode[$response['code']]) ? self::$commitCode[$response['code']] : mb_convert_encoding(
                $response['errorMsg'],
                'gbk',
                'utf-8'
            );
            foreach ($phones as $key => $phone)
            {
                $this->SetMultiMsgArray($phone, $response['code'], $errorMsg);
            }

            return self::RET_ERR;
        }
    }

    /**
     * 群发不同内容短信, 不建议使用
     *
     * @param array  $multiContent multiContent
     * @param string $sign         sign
     *
     * @return int
     */
    public function batchSend($multiContent, $sign)
    {
        $multiXmt = array();
        foreach ($multiContent as $key => $value)
        {
            list($phone, $msg) = $value;
            $multiXmt[] = $phone . "," . mb_convert_encoding($msg, 'utf-8', 'gbk');
        }
        $data = array(
            "account"  => $this->user,
            "password" => $this->pwd,
            'msg'      => mb_convert_encoding($sign . '{$var}', 'utf-8', 'gbk'),
            "params"   => implode(";", $multiXmt),
            "sendtime" => "",//定时发送，选填,格式: ************
            "report"   => "true",
            "uid"      => self::getMillisecond(),
        );
        $url = $this->apiUrl . "/msg/variable/json";
        $response = FuncAction::postTimeout($url, json_encode($data), 10, false, true);
        $response = json_decode($response, true);
        $response = \EncodingAction::iteratorArray($response, "GBK");

        if ($response && isset($response['code']) && $response['code'] == "0")
        {
            foreach ($multiContent as $key => $value)
            {
                list($phone) = $value;
                $this->msgIdList[$phone] = $response['msgId'];
                $this->SetMultiMsgArray($phone, 0, self::$commitCode[0]);
            }

            return self::RET_SUCC;
        }
        else
        {
            foreach ($multiContent as $key => $value)
            {
                list($phone) = $value;
                $this->SetMultiMsgArray($phone, $response['code'], self::$commitCode[$response['code']]);
            }

            return self::RET_ERR;
        }
    }

    /**
     * 变量短信
     *
     * @param array  $multiContent multiContent
     * @param string $template     template
     * @param string $sign         sign
     *
     * @return int
     */
    public function variableMsgSend($multiContent, $template, $sign)
    {
        $multiXmt = array();
        foreach ($multiContent as $key => $value)
        {
            list($phone, $variables) = $value;
            $multiXmt[] = $phone . "," . mb_convert_encoding(implode(',', $variables), 'utf-8', 'gbk');
        }
        $data = array(
            "account"  => $this->user,
            "password" => $this->pwd,
            'msg'      => mb_convert_encoding($sign . $template, 'utf-8', 'gbk'),
            "params"   => implode(";", $multiXmt),
            "sendtime" => "",//定时发送，选填,格式: ************
            "report"   => "true",
            "uid"      => self::getMillisecond(),
        );
        $url = $this->apiUrl . "/msg/variable/json";
        $response = FuncAction::postTimeout($url, json_encode($data), 10, false, true);
        $response = json_decode($response, true);
        $response = \EncodingAction::iteratorArray($response, "GBK");

        if ($response && isset($response['code']) && $response['code'] == "0")
        {
            foreach ($multiContent as $key => $value)
            {
                list($phone) = $value;
                $this->msgIdList[$phone] = $response['msgId'];
                $this->SetMultiMsgArray($phone, 0, self::$commitCode[0]);
            }

            return self::RET_SUCC;
        }
        else
        {
            foreach ($multiContent as $key => $value)
            {
                list($phone) = $value;
                $this->SetMultiMsgArray($phone, $response['code'], self::$commitCode[$response['code']]);
            }

            return self::RET_ERR;
        }
    }

    /**
     * 获取 callback status 需要的配置文件, 和设定回调方法
     *
     * @param int $channelId channel
     *
     * @return array
     */
    public static function callbackStatusConfig($channelId)
    {
        $operatorConfigModel = \Admin\OperatorConfigModel::getInstance();
        $queryData = array(
            'id'  => $channelId,
            'row' => 1,
        );
        $channelConfig = $operatorConfigModel->getOperatorList($queryData);

        $params = array(
            'account'  => $channelConfig['username'],
            'password' => $channelConfig['password'],
            "count"    => "50",
        );

        return array(
            'url'           => $channelConfig['api_url'] . '/msg/pull/report',
            'data'          => json_encode($params),
            'header'        => ['Content-Type: application/json;'],
            'action'        => 'post',
            'callbackClass' => '\\Smschannel\\ChuangLan253ChannelAction',
            'callbackFunc'  => 'callbackStatus',
            'callback'      => '',
        );
    }

    /**
     * 获取 callback status, 是回调方法
     *
     * @param array $data data
     *
     * @return boolean
     */
    public static function callbackStatus($data)
    {
        $res = false;
        $data = json_decode($data, true);
        if ($data && $data['ret'] == 0 && isset($data['result']))
        {
            $redisAction = \RedisAction::connect();
            foreach ($data['result'] as $value)
            {
                $dataInfo = array(
                    'Mobile'       => $value['mobile'],
                    'Msg_Id'       => $value['msgId'],
                    'Status'       => $value['status'],
                    'originStatus' => $value['status'],
                    'statusDesc'   => $value['statusDesc'],
                    'time'         => $value['reportTime'],
                );

                $res = $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($dataInfo));
            }
        }

        return $res;
    }

    /**
     * 微秒，用作 msgId
     *
     * @return float
     */
    private static function getMillisecond()
    {
        list($s1, $s2) = explode(' ', microtime());

        return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
    }

    /**
     * 设置响应
     *
     * @param string $phone commit phone
     * @param string $code  commit response code
     * @param string $desc  commit response cde desc
     *
     * @return void
     */
    private function setMultiMsgArray($phone, $code, $desc)
    {
        $this->operatorMsgList[$phone] = array(
            'codeStatus' => $code,
            'codeDesc'   => !empty(self::$commitCode[$code]) ? self::$commitCode[$code] : $desc,
            'result'     => $code === "0" ? self::RET_SUCC : self::RET_ERR,
        );
    }

}
