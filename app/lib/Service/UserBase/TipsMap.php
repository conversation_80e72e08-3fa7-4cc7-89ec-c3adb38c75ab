<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/5/8
 * Time: 9:08
 */

namespace Service\UserBase;


class TipsMap
{
    const ERRORNICKNAMENOTREPLACE = ['code' => "3000000", 'msg' => '昵称不可重复'];
    const ERRORDELNIACKNAMEHASHTABLE = ['code' => "3000001", 'msg' => '删除昵称hash表数据失败'];
    const ERRORCREATEONLYNICKNAMEHASHTABLE = ['code' => "3000002", 'msg' => '创建唯一昵称失败'];
    const ERRORSERNICKNAMEINFOFAILE = ['code' => "3000003", 'msg' => '创建更新昵称失败'];
    //    const SUCCESSERNICKNAMEINFO = ['code' => 3000004 , 'msg' => '创建更新昵称成功'];
    const ERRORDELREDISAVATARINFOFAILE = ['code' => "3000005", 'msg' => '删除用户头像昵称缓存失败'];
    const ERRORUPDATEAVATARISEMPTY = ['code' => "3000006", 'msg' => '更新头像参数不能为空'];
    const ERRORUPDATEAVATARFAILE = ['code' => "3000007", 'msg' => '更新头像失败'];
    const ERRORUSERUPLOADAVATARFIALE = ['code' => "3000008", 'msg' => '用户上传头像失败'];
    const ERRORNICKNAMUSEREDEFAULT = ['code' => "3000009", 'msg' => '昵称格式不合法', 'realMsg' => '不可使用默认昵称'];
    const ERRORNETNOTUSE1 = ['code' => "3000010", 'msg' => '网络开小差，请稍后重试~', 'realMsg' => '未获取到配置信息'];
    const ERRORNETNOTUSE2 = ['code' => "3000011", 'msg' => '网络开小差，请稍后重试~', 'realMsg' => '配置信息不全'];
    const ERRORNETNOTUSE3 = ['code' => "3000012", 'msg' => '网络开小差，请稍后重试~', 'realMsg' => '长度配置规则不正确'];
    public static $errorNicknameLenFailed = ['code' => "3000013", 'msg' => ''];
    const ERRORNICKNAMENOTEMPTY = ['code' => "3000014", 'msg' => '昵称中间不可有空格'];
    const ERRORASSIGNRULESFAILE = ['code' => "3000015", 'msg' => '昵称不可使用_和-以外的特殊字符'];
    const ERRORNICKNAMEAUDITFAILE = ['code' => "3000016", 'msg' => '昵称更新失败', 'realMsg' => '昵称审核失败'];
    const ERRORNICKNAMEMODIFYFAILED = ['code' => "3000017", 'msg' => '网络开小差，请稍后重试~', 'realMsg' => 'DB或程序异常'];
    const ERRORPASSIDSLENFAILED = ['code' => "3000018", 'msg' => '批量最大长度50个', 'realMsg' => ''];
    const ERRORMIDEMPTYFAILED = ['code' => "3000019", 'msg' => 'mid不能为空', 'realMsg' => ''];
    const ERRORPARAMSEMPTYFAILED = ['code' => "3000020", 'msg' => '缺少参数', 'realMsg' => ''];
    const ERRORNOTUSERPASSID = ['code' => "3000021", 'msg' => '没有该用户，请先认证登录', 'realMsg' => ''];
    const ERROR_XCX_NO_USER = ['code' => "3000022", 'msg' => '小程序未绑定，请授权进行绑定', 'realMsg' => ''];
    const ERROR_XCX_NO_BINDUNIONID = ['code' => "3000023", 'msg' => '未绑定账号，请重新授权', 'realMsg' => '缺少unionid'];
    const ERROR_XCX_NO_BINDPHONE = ['code' => "3000024", 'msg' => '请绑定手机号码', 'realMsg' => '需要绑定手机号码'];
    const ERROR_XCX_UBINDWEIXIN = ['code' => "3000025", 'msg' => '用户解绑了微信号，请重新授权绑定', 'realMsg' => ''];
    const ERROR_XCV_CHANGE_WEIXIN_BIND = ['code' => "3000026", 'msg' => '用户换绑了第三方账号，请重新授权绑定', 'realMsg' => ''];
    const ERROR_XCX_DEL_OPENID_BIND = ['code' => "3000027", 'msg' => '解绑小程序失败', 'realMsg' => '删除openid关系失败'];
    const ERROR_XCX_SET_REDIS_SESSION_KEY = ['code' => "3000028", 'msg' => '保存用户凭证失败', 'realMsg' => 'redis保存失败'];
    const ERROR_XCX_AUTH_LOGIN_FAILED = ['code' => "3000029", 'msg' => '小程序授权登录失败', 'realMsg' => ''];
    const ERROR_GET_USER_INFO_FAILED = ['code' => "3000030", 'msg' => '该用户已注销，请重新授权', 'realMsg' => '注销用户'];
    const ERROR_PARSE_PUSH_DATA_FAILED = ['code' => "3000031", 'msg' => '小程序信息解密失败', 'realMsg' => ''];
    const ERROR_NO_OPENID = ['code' => "3000032", 'msg' => '用户数据缺失', 'realMsg' => '没有openid'];
    const ERROR_GET_SESSION_KEY_FAILED = ['code' => "3000033", 'msg' => '获取小程序身份凭证失败', 'realMsg' => ''];
    const ERROR_XCX_GET_UNIONID_FAILED = ['code' => "3000034", 'msg' => '小程序授权失败', 'realMsg' => '获取unionid失败'];
    const ERROR_XCX_NO_PHONE = ['code' => "3000035", 'msg' => '请输入绑定的手机号码', 'realMsg' => ''];
    const ERROR_PHONE_RULES_FAILED = ['code' => "3000036", 'msg' => '手机号格式不正确', 'realMsg' => ''];
    const ERROR_XCX_BIND_FAILED = ['code' => "3000037", 'msg' => '小程序绑定失败，请重新授权绑定', 'realMsg' => ''];
    const ERROR_XCX_PHONEISBIND_FAILED = ['code' => "3000038", 'msg' => '该手机号已被占用', 'realMsg' => '该手机号已经绑定过微信了'];
    const ERROR_XCV_MAKE_OPENID_MAP = ['code' => "3000039", 'msg' => '微信小程序绑定失败', 'realMsg' => '建立opneid失败'];
    const ERROR_CHECK_AUTH_FAILED = ['code' => "3000040", 'msg' => '用户登录认证失败', 'realMsg' => ''];
    const ERROR_CHECK_SESSION_FAILED = ['code' => "3000041", 'msg' => '用户登录认证失败', 'realMsg' => '身份续期失败'];
    const ERROR_THIRD_ALREADY_BIND_FAILED = ['code' => "3000042", 'msg' => '第三方已绑定过手机号码', 'realMsg' => ''];
    const ERROR_THIRD_REG_FAILED = ['code' => "3000043", 'msg' => '小程序注册绑定失败', 'realMsg' => ''];
    const SUCCESS_LOGIN = ['code' => "200", 'msg' => '登录成功', 'realMsg' => ''];
    const ERROR_XCX_SET_DB_SESSION_KEY = ['code' => "3000044", 'msg' => '保存用户凭证失败', 'realMsg' => '数据库保存失败'];
    const ERROR_XCX_API_FAILED = ['code' => "3000045", 'msg' => '接口异常,请稍后再试', 'realMsg' => ''];
    const ERROR_XCX_RELOAD_ACCESS_TOKEN_FAILED = ['code' => "3000046", 'msg' => 'reload access_token to db失败', 'realMsg' => ''];
}