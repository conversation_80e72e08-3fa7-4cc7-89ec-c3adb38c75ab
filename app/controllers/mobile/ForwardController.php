<?php

/**
 * 跳转控制器
 */
class ForwardController extends Controller
{

    public function actionIndex()
    {
        $forward = getForwardUrl();
        $passid = intval($_POST["passid"]);
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie();
        if (!$authInfo || $authInfo['i'] != $passid)
        {
            $memberModel = loadModel('member');
            $token = $memberModel->getToken($passid);
            if (md5($token . TOKENKEY) == $_POST["access"])
            {
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $cTime = time() + 3600 * 24 * 30;
                $result = $memberModel->read($passid);
                $bind = unserialize($result["m_uid"]);
                $uid = $bind[1];
                $username = $result['username'];
                $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
                if (strpos($username, "#qq#") === false && strpos($username, "#weibo#") === false)
                {
                    $needModifyName = 0;
                }
                else
                {
                    if (strpos($username, "#qq#") !== false)
                    {
                        $oauthType = 'qq';
                    }
                    else
                    {
                        $oauthType = 'weibo';
                    }
                    $usernameOld = $username;
                    $username = $memberModel->repairOldOAuthUser($passid, $oauthType);
                    if ($username)
                    {
                        $needModifyName = 0;
                        $memberModel->patchUserLog($passid, array('username_old' => $usernameOld, 'username' => $username, 'op_time' => time(), 'op_ip' => get_client_ip(), 'op_domain' => 'login.2345.com'));
                        noticeToChange($passid, 'changeUsername', array(
                            'passid' => $passid,
                            'uid' => $uid,
                            'type' => 'username',
                            'value' => $username
                        ));
                    }
                    else
                    {
                        $needModifyName = 1;
                        $username = explode("#$oauthType#", $usernameOld);
                        $username = $username[0];
                    }
                    $userMod = 100;
                }
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                $cookie['need_modify_name'] = $needModifyName;
                $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);
                redirect($forward);
            }
            else
            {
                redirect(PASSPORT_HOST . '/login?forward=' . urlencode($forward));
            }
        }
        else
        {
            redirect($forward);
        }
    }

}
