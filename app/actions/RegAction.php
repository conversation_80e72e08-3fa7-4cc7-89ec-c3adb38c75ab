<?php

use Common\Msg\ResponseMsg;
use Common\Validator\Validator;
use Octopus\Logger\Handler\StreamHandler;
use Octopus\Logger;
use \Service\AccountStat\Consts\AccountStat;

// 注册action
class RegAction extends Action
{

    /**
     * 注册 包装了reg方法, 方便统计
     *
     * @param  string $username 用户名
     * @param  string $password 密码
     * @param  string $email email
     * @param  integer $pwd_strength 密码强度
     * @param  string $refer 会跳地址
     * @param  string $servClient_domain 服务端域名
     * @param  string $userClient_ip 客户端ip
     * @param  mixed $source 来源
     * @param  int $clientType 终端类型
     * @param  bool $pwdHasMd5 密码是否md5一次
     *
     * @return array 失败的话返回一个只包含状态码的数组，成功返回带状态码和用户信息的数组
     */
    public function reg($username, $password, $email, $pwd_strength, $refer, $servClient_domain, $userClient_ip, $source = null, $clientType = AccountStat::CTP_WEB, $pwdHasMd5 = false)
    {
        $res = $this->regOrigin($username, $password, $email, $pwd_strength, $refer, $servClient_domain, $userClient_ip, $pwdHasMd5);
        //收集注册登录信息
        if ($source !== null)
        {
            $st = ($res[0] == 200) ? true : false;
            \Service\AccountStat\AccountStat::collect(AccountStat::TP_REG, $clientType, $username, $st, $source);
        }
        return $res;
    }

    /**
     * 注册
     * @param  string $username 用户名
     * @param  string $password 密码
     * @param  string $email email
     * @param  integer $pwd_strength 密码强度
     * @param  string $refer 会跳地址
     * @param  string $servClient_domain 服务端域名
     * @param  string $userClient_ip 客户端ip
     * @param  bool $pwdHasMd5 密码是否md5一次
     * @return array 失败的话返回一个只包含状态码的数组，成功返回带状态码和用户信息的数组
     */
    public function regOrigin($username, $password, $email, $pwd_strength, $refer, $servClient_domain, $userClient_ip, $pwdHasMd5 = false)
    {
        $isUseRedis = Config::get("isUseRedis");
        $regex = Config::get('regex');
        $nowTime = time();

        // 这里防止频繁的检测
        // 如果异常，会返回错误的状态码（400.2），并将ip加入到禁止列表中
        // ip禁止列表这里没有实际的作用，在登陆的时候，会判断这个ip列表，并将用户添加到可疑用户表里，参见 LoginAction::login
        if ($isUseRedis)
        {
            $redis = RedisEx::getInstance();
            $this->defineRegConfig($redis);

            // 公司服务端ip数组
            $ip_arr = getAllServerIps($redis);

            if (!in_array($userClient_ip, $ip_arr) && $servClient_domain != 'm.shouji.2345.com' && $servClient_domain != 'm.jifen.2345.com')
            {
                if (!filter_var($userClient_ip, FILTER_VALIDATE_IP))
                {
                    $userClient_ip = '0.0.0.0';
                }
                // 这个keyname保存的是ip对应的最新的检测时间
                $keyname = "checkTimeLog:" . str_replace(".", "_", $userClient_ip);
                $redis->lPush($keyname, $nowTime); // 将这次的检查时间加入到队列头
                $redis->expireAt($keyname, $nowTime + CIPFLT); // 设置队列过期时间
                $redis->lTrim($keyname, 0, CIPFLN - 1); // 只保留最新的 CIPFLN - 1 条

                /* $forbidenTime = $redis->zScore('checkForbidenIps', str_replace(".", "_", $userClient_ip));
                  if ($forbidenTime && $forbidenTime > ($nowTime - CIPFCT))
                  {
                  //   * error:批量刷check
                  return array('400.2');
                  } */

                // 取最新的规则次数检查时间日志
                $checkTime_range = $redis->lRange($keyname, 0, CIPFLN - 1);

                // 计算日志长度，如果大于规则次数才需要做判断
                $checkLen = count($checkTime_range);

                // 上面已经对队列进行保留 CIPFLN - 1 操作
                // 其实就是当队列满时，就需要对其进行下面的检查
                if ($checkLen >= CIPFLN)
                {
                    // 计算最近规则次数检查的时间间隔
                    $timeRange = $checkTime_range[0] - $checkTime_range[CIPFLN - 1];

                    // 如果最近规则次数检查的时间间隔小于规则时间，则将IP加入禁止列表
                    if ($timeRange < CIPFLT)
                    {
                        // 清理持续时间之前的ip
                        // 当ip加入禁止集合是有一个失效时间的，就是 CIPFCT 这个常量控制的
                        $redis->zRemRangeByScore('checkForbidenIps', 0, $nowTime - CIPFCT);

                        // 添加禁止IP
                        $redis->zAdd('checkForbidenIps', $nowTime, str_replace(".", "_", $userClient_ip));

                        // 下面这个 checkForbidenPersistentIps 段，是老早以前遗留下来的，导入到redis的，没有入口
                        $ppIp = str_replace(".", "_", substr($userClient_ip, 0, strrpos($userClient_ip, '.')));
                        if ($redis->exists('checkForbidenPersistentIps:' . $ppIp))
                        {
                            $pIps = $redis->sMembers('checkForbidenPersistentIps:' . $ppIp);
                            foreach ($pIps as $pIp)
                            {
                                $redis->zAdd('checkForbidenIps', $nowTime, $ppIp . '_' . $pIp);
                            }
                            $redis->sAdd('checkForbidenPersistentIps:' . $ppIp, substr($userClient_ip, strrpos($userClient_ip, '.') + 1));
                        }

                        //   * error:批量刷check
                        return array('400.2');
                    }
                }
            }
        }

        $username = trim($username);
        $password = trim($password);
        $email = strtolower(trim($email));

        // 常规字段检查 start =======================================================================================================
        //   * error:2345帐号最少2个字符;
        if (mb_strlen($username, "GBK") < 2)
        {
            return array('300.0');
        }

        //   * error:2345帐号请不要超过24个字符
        if (strlen($username) > 24)
        {
            return array('300.1');
        }

        //   * error:2345帐号请输入汉字，字母，数字，或邮箱地址
        if (preg_match($regex['username'], $username) || preg_match($regex['phone'], $username))
        {
            return array('300.2');
        }

        //   * error:密码最少6个字符
        if (!$pwdHasMd5 && strlen($password) < 6) {
            return array('300.3');
        }

        //   * error:密码最多16个字符
        if (!$pwdHasMd5 && strlen($password) > 16) {
            return array('300.4');
        }

        // 常规字段检查 end =======================================================================================================

        /** @var MemberModel $memberModel */
        $memberModel = loadModel('member');

        //   * error:此帐号已被注册，请修改2345帐号
        if (preg_match($regex['phone'], $username))
        {
            if ($memberModel->checkPhone($username))
            {
                return array('300.6');
            }
        }
        else if (filter_var($username, FILTER_VALIDATE_EMAIL))
        {
            if ($memberModel->checkEmail($username))
            {
                return array('300.6');
            }
        }
        else
        {
//            $badwords = new Badwords(10);
//            if ($badwords->filter($username))
//            {
//                return array('300.6');
//            }
            $validator = new Validator();
            $validator->addValidator($username, Validator::BAD_WORDS, array("昵称"));
            list($res, $msg) = $validator->validate();
            if ($res != ResponseMsg::SUCCESS) {
                // 存在禁词
                return array('300.6');
            }
            if ($memberModel->checkUser($username))
            {
                return array('300.6');
            }
        }
        //email不做必须字段，如有则做验证
        if ($email)
        {
            //   * error:请输入正确的邮箱
            if (!filter_var($email, FILTER_VALIDATE_EMAIL))
            {
                return array('300.5');
            }

            //   * error:此邮箱已被注册，请换一个
            if ($memberModel->checkEmail($email))
            {
                return array('300.7');
            }
        }

        $refer = urldecode($refer);
        $hostArr = parse_url(urldecode($refer));

        if (!$this->filterClientIp($userClient_ip, $servClient_domain))
        {
            return array('400.4');
        }



            $result = $memberModel->reg2345($username, $password, $email, $pwd_strength, $userClient_ip, 0, $pwdHasMd5 ? 1 : 0);
            $this->log($username, $servClient_domain, $userClient_ip, $result ? "success" : "failure", $result['passid']);
            if ($result)
            {
                $passid = $result['passid'];
                $uid = $result['uid'];
                $username = $result['username'];
                $userMod = 0;
                $loginAction = loadAction('login');
                $loginTime = explode(" ", microtime());
                $loginTime[0] = $loginTime[0] * 100000000;
                $loginTime = $loginTime[1] . "." . $loginTime[0];
                $sid = $loginAction->getSessionId($passid, $uid, $username, $loginTime);
                $phone = $memberModel->getPhoneByPassid($passid);
                $params = array(
                    'passid' => $passid,
                    'uid' => $uid,
                    'passusername' => $username,
                    'mod' => $userMod,
                    'key' => md5($uid . MD5KEY),
                    'refer' => urlencode($refer),
                    'locUrl' => base64_encode('http://bbs.2345.cn/api/passport.php?action=login'),
                    'userInfo' => array(
                        "uname" => $username,
                        "upass" => md5($username),
                        "umail" => $email
                    ),
                    'token' => $result['token'],
                    'loginTime' => $loginTime,
                    'sid' => $sid,
                    'phone' => $phone,
                    'cookie' => $loginAction->getLoginCookie($passid, $uid, $username, $userMod, $loginTime, $sid)
                );
            }
            else
            {
                if ($isUseRedis)
                {
                    // 域名下的注册数
                    $keyname_domain = "RFN:" . str_replace(".", "_", $servClient_domain);
                    $redis->incr($keyname_domain);
                    $redis->incr($keyname_domain . ":username");
                }
                return array('300.6');
            }

        if ($isUseRedis)
        {
            // 域名下的注册数
            $keyname_domain = "RSN:" . str_replace(".", "_", $servClient_domain);
            $redis->incr($keyname_domain);
            $redis->incr($keyname_domain . ":username");
            // 总计的注册数
            $redis->incr("regSuccNum");
        }
        return array('200.0', $params);
    }

    /**
     * 使用手机号注册2345帐号 包装了regPhone方法, 方便统计
     *
     * @param string $phone 手机号
     * @param string $password 密码
     * @param string $pwdStrength 密码强度
     * @param string $userClientIp 用户IP
     * @param string $servClientDomain 调用项目域名
     * @param int $gid gid
     * @param mixed $source 来源
     * @param int $clientType 终端类型
     * @param bool $isChinaMobile 是否是中国移动统一认证注册
     *
     * @return mixed 注册成功信息
     */
    public function regPhone($phone, $password, $pwdStrength, $userClientIp, $servClientDomain, $gid = 200, $source = null, $clientType = AccountStat::CTP_WEB, $isChinaMobile = false)
    {
        $res = $this->regPhoneOrigin($phone, $password, $pwdStrength, $userClientIp, $servClientDomain, $gid, $source);
        //收集注册登录信息
        if ($source !== null)
        {
            $st = ($res[0] == 200) ? true : false;
            if ($isChinaMobile)
            {
                $phone = AccountStat::AC_TP_OAUTH_CHINA_MOBILE;
            }
            \Service\AccountStat\AccountStat::collect(AccountStat::TP_REG, $clientType, $phone, $st, $source);
        }
        return $res;
    }

    /**
     * 使用手机号注册2345帐号
     * @param string $phone 手机号
     * @param string $password 密码
     * @param string $pwdStrength 密码强度
     * @param string $userClientIp 用户IP
     * @param string $servClientDomain 调用项目域名
     * @param int $gid GID
     * @param string $source Source|mid
     * @return remix 注册成功信息
     */
    public function regPhoneOrigin($phone, $password, $pwdStrength, $userClientIp, $servClientDomain, $gid = 200, $source = '')
    {
        $isUseRedis = Config::get("isUseRedis");
        $regex = Config::get('regex');
        $phone = trim($phone);
        $password = trim($password);
        // 常规字段检查 start =======================================================================================================
        if (!preg_match($regex['phone'], $phone))
        {
            return array('300.0');
        }
        //   * error:密码最少6个字符
        if (strlen($password) < 6)
        {
            return array('300.1');
        }
        //   * error:密码最多16个字符
        if (strlen($password) > 16)
        {
            return array('300.2');
        }
        // 常规字段检查 end =======================================================================================================
        $memberModel = loadModel('member');
        //   * error:此帐号已被注册，请修改2345帐号
        if ($memberModel->checkPhone($phone))
        {
            return array('300.3');
        }
        if (!$this->filterClientIp($userClientIp, $servClientDomain))
        {
            return array('400.2');
        }
        //章鱼兴修的量太小了，取消队列注册
        if (false && $source == QKL_ZYXQ_DOMAIN)
        {
            $result = $memberModel->regPhoneToQueue($phone, $password, $pwdStrength, $userClientIp, $gid);
        }
        else
        {
            $result = $memberModel->regPhone($phone, $password, $pwdStrength, $userClientIp, $gid);
        }

        $this->log($phone, $servClientDomain, $userClientIp, $result ? "success" : "failure", $result['passid']);
        if ($result)
        {
            $passid = $result['passid'];
            $uid = $result['uid'];
            $username = $result['username'];
            $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
            $loginAction = loadAction('login');
            $loginTime = explode(" ", microtime());
            $loginTime[0] = $loginTime[0] * 100000000;
            $loginTime = $loginTime[1] . "." . $loginTime[0];
            $sid = $loginAction->getSessionId($passid, $uid, $username, $loginTime);

            if ($source != QKL_ZYXQ_DOMAIN)
            {
                $phone = $memberModel->getPhoneByPassid($passid, true);
            }

            $params = array(
                'passid' => $passid,
                'uid' => $uid,
                'username' => $username,
                'loginTime' => $loginTime,
                'sid' => $sid,
                'gid' => $result['gid'],
                'token' => $result['token'],
                'phone' => $phone,
                'cookie' => $loginAction->getLoginCookie($passid, $uid, $username, $userMod, $loginTime, $sid)
            );
            if ($isUseRedis)
            {
                $redis = RedisEx::getInstance();
                // 域名下的注册数
                $keyname_domain = "RSN:" . str_replace(".", "_", $servClientDomain);
                $redis->incr($keyname_domain);
                $redis->incr($keyname_domain . ":phone");
                // 总计的注册数
                $redis->incr("regSuccNum");
            }
            return array('200.0', $params);
        }
        else
        {
            if ($isUseRedis)
            {
                $redis = RedisEx::getInstance();
                // 域名下的注册数
                $keyname_domain = "RFN:" . str_replace(".", "_", $servClientDomain);
                $redis->incr($keyname_domain);
                $redis->incr($keyname_domain . ":phone");
            }
            return array('500.0');
        }
    }

    /**
     * email注册  包装了regEmail, 方便统计
     *
     * @param string $email
     * @param string $password
     * @param int $pwdStrength
     * @param string $userClientIp
     * @param string $servClientDomain
     * @param  mixed $source 来源
     * @param  int $clientType 终端类型
     *
     * @return mixed
     */
    public function regEmail($email, $password, $pwdStrength, $userClientIp, $servClientDomain, $source = null, $clientType = AccountStat::CTP_WEB)
    {
        $res = $this->regEmailOrigin($email, $password, $pwdStrength, $userClientIp, $servClientDomain);
        //收集注册登录信息
        if ($source !== null)
        {
            $st = ($res[0] == 200) ? true : false;
            \Service\AccountStat\AccountStat::collect(AccountStat::TP_REG, $clientType, $email, $st, $source);
        }
        return $res;
    }

    /**
     * email注册
     * @param string $email
     * @param string $password
     * @param int $pwdStrength
     * @param string  $userClientIp
     * @param string $servClientDomain
     * @return mixed
     */
    public function regEmailOrigin($email, $password, $pwdStrength, $userClientIp, $servClientDomain)
    {
        $isUseRedis = Config::get("isUseRedis");
        $email = trim($email);
        $password = trim($password);
        // 常规字段检查 start =======================================================================================================
        if (!filter_var($email, FILTER_VALIDATE_EMAIL))
        {
            return array('300.0');
        }
        // 邮箱长度不能超过24个字符
        if (strlen($email) > 24)
        {
            return array('300.4');
        }
        //   * error:密码最少6个字符
        if (strlen($password) < 6)
        {
            return array('300.1');
        }
        //   * error:密码最多16个字符
        if (strlen($password) > 16)
        {
            return array('300.2');
        }
        // 常规字段检查 end =======================================================================================================
        $memberModel = loadModel('member');
        //   * error:此帐号已被注册，请修改2345帐号
        if ($memberModel->checkEmail($email, 0))
        {
            return array('300.3');
        }
        if (!$this->filterClientIp($userClientIp, $servClientDomain))
        {
            return array('400.2');
        }
        $result = $memberModel->regEmail($email, $password, $pwdStrength, $userClientIp);
        $this->log($email, $servClientDomain, $userClientIp, $result ? "success" : "failure", $result['passid']);
        if ($result)
        {
            $passid = $result['passid'];
            $uid = $result['uid'];
            $username = $result['username'];
            $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
            $loginAction = loadAction('login');
            $loginTime = explode(" ", microtime());
            $loginTime[0] = $loginTime[0] * 100000000;
            $loginTime = $loginTime[1] . "." . $loginTime[0];
            $sid = $loginAction->getSessionId($passid, $uid, $username, $loginTime);

            $phone = $memberModel->getPhoneByPassid($passid);
            $params = array(
                'passid' => $passid,
                'uid' => $uid,
                'username' => $username,
                'loginTime' => $loginTime,
                'sid' => $sid,
                'token' => $result['token'],
                'phone' => $phone,
                'cookie' => $loginAction->getLoginCookie($passid, $uid, $username, $userMod, $loginTime, $sid)
            );
            if ($isUseRedis)
            {
                $redis = RedisEx::getInstance();
                // 域名下的注册数
                $keyname_domain = "RSN:" . str_replace(".", "_", $servClientDomain);
                $redis->incr($keyname_domain);
                $redis->incr($keyname_domain . ":email");
                // 总计的注册数
                $redis->incr("regSuccNum");
            }
            return array('200.0', $params);
        }
        else
        {
            if ($isUseRedis)
            {
                $redis = RedisEx::getInstance();
                // 域名下的注册数
                $keyname_domain = "RFN:" . str_replace(".", "_", $servClientDomain);
                $redis->incr($keyname_domain);
                $redis->incr($keyname_domain . ":email");
            }
            return array('500.0');
        }
    }

    /**
     * 完善用户信息（PS：这个是提供给外部接口用的，调用在/app/controllers/api/PatchUserController.php）
     * @param  string $passid members表的记录id
     * @param  string $username 用户名
     * @param  string $password 密码
     * @param  integer $pwd_strength 密码强度
     * @param  string $email 邮箱
     * @return mixed 失败返回只包含状态码的数组，成功返回状态码和用户名的数组
     */
    public function patchUser($passid, $username, $password, $pwd_strength, $email = '')
    {
        $regex = Config::get('regex');
        if (mb_strlen($username, "GBK") < 2)
        {
            return array('300.0');
        }

        if (strlen($username) > 24)
        {
            return array('300.1');
        }

        if (preg_match($regex['username'], $username) || preg_match($regex['phone'], $username) || filter_var($username, FILTER_VALIDATE_EMAIL))
        {
            return array('300.2');
        }

        if (strlen($password) < 6)
        {
            return array('300.3');
        }

        if (strlen($password) > 16)
        {
            return array('300.4');
        }
//        $badwords = new Badwords(10);
//        if ($badwords->filter($username))
//        {
//            return array('300.6');
//        }
        $memberModel = loadModel('member');
        if ($memberModel->checkUser($username))
        {
            return array('300.6');
        }
        //email不做必须字段，如有则做验证
        if ($email)
        {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL))
            {
                return array('300.5');
            }

            if ($memberModel->checkEmail($email))
            {
                return array('300.7');
            }
        }
        $loginAction = loadAction('login');
        // 修改成功的话，返回修改后的用户名，否则返回之前的用户名
        if ($memberModel->setUser($passid, $username, $password, $pwd_strength, $email))
        {
            $uid = $memberModel->getUid($passid);
            if ($uid)
            {
                noticeToChange($passid, 'changeUsername', array(
                    'passid' => $passid,
                    'uid' => $uid,
                    'type' => 'username',
                    'value' => $username
                ));
            }
            $cookie = $loginAction->getLoginCookie($passid, $uid, $username, 0);
            return array('200.0', $username, 'cookie' => $cookie);
        }
        else
        {
            $result = $memberModel->read($passid);
            $uids = $result['m_uid'];
            $uids = unserialize($uids);
            $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
            $cookie = $loginAction->getLoginCookie($passid, $uids[1], $result['username'], $userMod);
            return array('200.1', $result['username'], 'cookie' => $cookie);
        }
    }

    /**
     * 补全用户名
     * @param  string $passid members表的记录id
     * @param  string $username 用户名
     */
    public function patchUsername($passid, $username)
    {
        $regex = Config::get('regex');
        if (mb_strlen($username, "GBK") < 2)
        {
            return array('300.0');
        }
        if (strlen($username) > 24)
        {
            return array('300.1');
        }
        if (preg_match($regex['username'], $username) || preg_match($regex['phone'], $username) || filter_var($username, FILTER_VALIDATE_EMAIL))
        {
            return array('300.2');
        }
//        $badwords = new Badwords(10);
//        if ($badwords->filter($username))
//        {
//            return array('300.3');
//        }
        $memberModel = loadModel('member');
        if ($memberModel->checkUser($username))
        {
            return array('300.3');
        }
        $loginAction = loadAction('login');
        // 修改成功的话，返回修改后的用户名，否则返回之前的用户名
        if ($memberModel->setUsername($passid, $username))
        {
            $uid = $memberModel->getUid($passid);
            if ($uid)
            {
                noticeToChange($passid, 'changeUsername', array(
                    'passid' => $passid,
                    'uid' => $uid,
                    'type' => 'username',
                    'value' => $username
                ));
            }
            $cookie = $loginAction->getLoginCookie($passid, $uid, $username, 0);
            return array('200.0', $username, 'cookie' => $cookie);
        }
        else
        {
            $result = $memberModel->read($passid);
            $uids = $result['m_uid'];
            $uids = unserialize($uids);
            $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
            $cookie = $loginAction->getLoginCookie($passid, $uids[1], $result['username'], $userMod);
            return array('200.1', $result['username'], 'cookie' => $cookie);
        }
    }

    public function patchPassword($passid, $password, $pwd_strength)
    {
        if (strlen($password) < 6)
        {
            return array('300.3');
        }
        if (strlen($password) > 16)
        {
            return array('300.4');
        }
        $memberModel = loadModel('member');
        $loginAction = loadAction('login');
        if ($memberModel->setFirstPassword($passid, $password, $pwd_strength))
        {
            $uid = $memberModel->getUid($passid);
            $result = $memberModel->read($passid);
            $username = $result['username'];
            $cookie = $loginAction->getLoginCookie($passid, $uid, $username, 0);
            if ($uid)
            {
                $this->NoticePassword($passid);
            }
            return array('200.0', $username, 'cookie' => $cookie);
        }
        else
        {
            $result = $memberModel->read($passid);
            $uids = $result['m_uid'];
            $uids = unserialize($uids);
            $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
            $cookie = $loginAction->getLoginCookie($passid, $uids[1], $result['username'], $userMod);
            return array('200.1', $result['username'], 'cookie' => $cookie);
        }
    }

    /**
     *
     * @param $passid
     * @param $nickname
     * @param $addTime
     * @param $ip
     * @return array
     * <AUTHOR>
     */
    public function patchNickname($passid, $nickname, $addTime, $ip)
    {
        $regex = Config::get('regex');
        if (mb_strlen($nickname, "GBK") < 2)
        {
            return array('300.0');
        }
        if (strlen($nickname) > 16)
        {
            return array('300.1');
        }
        if (preg_match($regex['username'], $nickname) || preg_match($regex['phone'], $nickname) || filter_var($nickname, FILTER_VALIDATE_EMAIL))
        {
            return array('300.2');
        }

        $validator = new \Common\Validator\Validator();
        $validator->addValidator($nickname, \Common\Validator\Validator::BAD_WORDS, array("昵称"));
        list($badWordsStatus, $msg) = $validator->validate();
        if ($badWordsStatus != \Common\Msg\ResponseMsg::SUCCESS) {
            return array('300.3');
        }

        $memberModel = loadModel('member');
        if ($memberModel->checkNickname($nickname)) {
            return array('300.3');
        }
        // 修改成功的话，返回修改后的昵称
        if ($memberModel->setNickname($passid, $nickname, $addTime, $ip))
        {
            $uid = $memberModel->getUid($passid);
            if ($uid)
            {
                noticeToChange($passid, 'changeNickname', array(
                    'passid' => $passid,
                    'uid' => $uid,
                    'type' => 'nickname',
                    'value' => $nickname,
                    'addtime' => $addTime,
                    'ip' => $ip,
                ));
            }
            return array('200.0', $nickname);
        }
        else
        {
            return array('400.0');
        }
    }

    /**
     * 通知其他项目用户密码已经修改
     * @param $passid
     * <AUTHOR>
     */
    public function NoticePassword($passid)
    {
        $memberModel = loadModel("member");
        $uid = $memberModel->getUid($passid);
        if ($uid)
        {
            noticeToChange($passid, 'changePassword', array(
                'passid' => $passid,
                'uid' => $uid,
                'type' => 'password',
                'value' => "",
            ));
        }
    }

    /**
     * 检测用户IP是否需要显示验证码
     * @param type $userClientIp
     * @return boolean
     */
    public function regCheckShowCode($userClientIp)
    {
        $isShowCode = true;
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis)
        {
            $nowTime = time();
            $redis = RedisEx::getInstance();
            $ipArr = getAllServerIps($redis);
            if (!in_array($userClientIp, $ipArr))
            {
                $ipKey = "regTimeLog:" . str_replace(".", "_", $userClientIp);
                $ipTimeRange = $redis->lRange($ipKey, 0, 49);
                if ($ipTimeRange)
                {
                    $redis->zRemRangeByScore('regForbidenIps', 0, $nowTime);
                    if (isset($ipTimeRange[23]) && ($nowTime - $ipTimeRange[23]) < 86400)
                    {
                        $isShowCode = true;
                    }
                    if (isset($ipTimeRange[8]) && ($nowTime - $ipTimeRange[8]) < 3600)
                    {
                        $isShowCode = true;
                    }
                    if (isset($ipTimeRange[3]) && ($nowTime - $ipTimeRange[3]) < 60)
                    {
                        $isShowCode = true;
                    }
                }
            }
            if ($isShowCode == true)
            {
                $redis->setex("showcode:" . str_replace(".", "_", $userClientIp), 3600, 1);
            }
            else
            {
                $showCode = $redis->get("showcode:" . str_replace(".", "_", $userClientIp));
                if ($showCode)
                {
                    $isShowCode = true;
                }
            }
        }
        return $isShowCode;
    }

    /**
     * IP过滤
     * @param type $userClientIp
     * @param type $servClientDomain
     * @return type
     */
    private function filterClientIp($userClientIp, $servClientDomain)
    {
//        if (strpos($userClientIp, "27.155") === 0 || strpos($userClientIp, "122.194") === 0 || strpos($userClientIp, "153.36") === 0 || strpos($userClientIp, "112.84") === 0 || strpos($userClientIp, "183.207") === 0)
//        {
//            return false;
//        }
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis)
        {
            $nowTime = time();
            $redis = RedisEx::getInstance();
            // 公司服务端ip数组
            $ipArr = getAllServerIps($redis);
            if (!in_array($userClientIp, $ipArr) && $servClientDomain != 'm.shouji.2345.com' && $servClientDomain != 'm.jifen.2345.com')
            {
                $forbidenTime = $redis->zScore('regForbidenIps', str_replace(".", "_", $userClientIp));
                if ($forbidenTime && $forbidenTime > $nowTime)
                {
                    return false;
                }
                $ipKey = "regTimeLog:" . str_replace(".", "_", $userClientIp);
                /* 各项目规则 */
                switch ($servClientDomain)
                {
                    case "game.2345.com":
                        $minLimit = 4;
                        $hourLimit = 29;
                        $dayLimit = 99;
                        break;
                    default:
                        $minLimit = 1;
                        $hourLimit = 9;
                        $dayLimit = 49;
                        break;
                }
                $ipTimeRange = $redis->lRange($ipKey, 0, $dayLimit);
                if ($ipTimeRange)
                {
                    $redis->zRemRangeByScore('regForbidenIps', 0, $nowTime);
                    //   * error:同一IP，一天内最多可注册50个帐号
                    if (isset($ipTimeRange[$dayLimit]))
                    {
                        if (($nowTime - $ipTimeRange[$dayLimit]) < 86400)
                        {
                            $redis->zAdd('regForbidenIps', $ipTimeRange[$dayLimit] + 86400, str_replace(".", "_", $userClientIp));
                            return false;
                        }
                    }
                    //   * error:同一IP，一小时内最多可注册15个帐号
                    if (isset($ipTimeRange[$hourLimit]))
                    {
                        if (($nowTime - $ipTimeRange[$hourLimit]) < 3600)
                        {
                            $redis->zAdd('regForbidenIps', $ipTimeRange[$hourLimit] + 3600, str_replace(".", "_", $userClientIp));
                            return false;
                        }
                    }
                    //   * error:同一IP，一分钟内最多可注册2个帐号
                    if (isset($ipTimeRange[$minLimit]))
                    {
                        if (($nowTime - $ipTimeRange[$minLimit]) < 60)
                        {
                            return false;
                        }
                    }
                }
                $redis->lPush($ipKey, $nowTime);
                $redis->lTrim($ipKey, 0, $dayLimit);
                $redis->expireAt($ipKey, $nowTime + 86400);
            }
        }
        return true;
    }

    /**
     * 从缓存中获取并定义一些检查的常量
     * @param  Redis $redis redis对象
     */
    private function defineRegConfig(&$redis)
    {
        // Check禁止IP规则次数
        define('CIPFLN', $redis->hGet('LimitSetting', 'CIPFLN'));
        // Check禁止IP规则时间
        define('CIPFLT', $redis->hGet('LimitSetting', 'CIPFLT'));
        // Check禁止IP持续时间
        define('CIPFCT', $redis->hGet('LimitSetting', 'CIPFCT'));
        // 注册锁定IP规则次数
        define('RIPLLN', $redis->hGet('LimitSetting', 'RIPLLN'));
        // 注册锁定IP规则时间
        define('RIPLLT', $redis->hGet('LimitSetting', 'RIPLLT'));
        // 注册锁定IP持续时间
        define('RIPLCT', $redis->hGet('LimitSetting', 'RIPLCT'));
        // 注册锁定IP段规则次数
        define('RIPDLLN', $redis->hGet('LimitSetting', 'RIPDLLN'));
        // 注册锁定IP段规则时间
        define('RIPDLLT', $redis->hGet('LimitSetting', 'RIPDLLT'));
        // 注册锁定IP段持续时间
        define('RIPDLCT', $redis->hGet('LimitSetting', 'RIPDLCT'));
        // 注册禁止IP段规则次数
        define('RIPDFLN', $redis->hGet('LimitSetting', 'RIPDFLN'));
        // 注册禁止IP段规则时间
        define('RIPDFLT', $redis->hGet('LimitSetting', 'RIPDFLT'));
        // 注册禁止IP段持续时间
        define('RIPDFCT', $redis->hGet('LimitSetting', 'RIPDFCT'));
    }

    /**
     * 添加注册日志
     *
     * @param string $username UserName
     * @param string $serverDomain ServerDomain
     * @param string $userIp UserIP
     * @param string $result Result
     * @param int $passid PassId
     * @return null
     */
    private function log($username, $serverDomain, $userIp, $result, $passid = 0)
    {
        $usernameUtf8 = mb_convert_encoding($username, "UTF-8", "GBK");
        $content = "\"{$_SERVER['REQUEST_URI']}\" \"$usernameUtf8\" \"$serverDomain\" \"$userIp\" \"$result\" \"$passid\"";
        xLog('reg', 'reg', $content);

        $refer = $_SERVER['HTTP_REFERER'];
        $password = isset($_POST['password']) ? $_POST['password'] : '';
        $all = json_encode($_POST);
        $content = ("\"REQUEST_URI:{$_SERVER['REQUEST_URI']}\" \"username:$usernameUtf8\" \"password:$password\" \"domain:{$serverDomain}\" \"ip:{$userIp}\" \"refer:{$refer}\"  \"res:$result\" \"all:{$all}\" \"passid:$passid\"");
        xLog('webapi_reg', 'webapi_success', $content);
    }

    /**
     * 发送激活邮件
     * @param  array $userinfo 用户信息
     */
    private function sendUnlockEmail($userinfo)
    {
        $smtpConfig = Config::get('smtp');
        $smtpemailto = $userinfo['email']; //发送给谁
        $mailsubject = "2345通行证激活邮件"; //邮件主题
        $mailbody = "<pre>亲爱的2345网址大全用户，您好！<br>";
        $mailbody .= "<br>" . "感谢您注册2345网址大全：<br>";
        $mailbody .= "<br>请您点击下面的链接，完成邮箱验证：<br><br><a href='" . $userinfo['url'] . "' target='_blank'>" . $userinfo['url'] . "</a><br><br>";
        $mailbody .= "如果以上链接无法点击，请将它拷贝到浏览器（例如IE）的地址栏中：<br><br>" . $userinfo['url'] . "</p><br><br>";
        $mailbody .= "如果你错误的收到了本电子邮件，请无需理会该邮件。<br><br>感谢您一直以来对2345网址大全关注与支持！</pre>";
        $mailtype = "HTML"; //邮件格式（HTML/TXT）,TXT为文本邮件
        $smtp = loadVendor('smtp');
        $smtp->init($smtpConfig['server'], $smtpConfig['port'], $smtpConfig['username'], $smtpConfig['password']);
        $smtp->sendmail($smtpemailto, $smtpConfig['email'], $mailsubject, $mailbody, $mailtype);
    }

}
