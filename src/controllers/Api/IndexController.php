<?php

namespace Api;

use Controller;
use Smschannel\DahanTricomChannelAction;

class IndexController extends Controller
{
    /**
     * SmsController constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取短链接-大汉三通
     * @return array|null
     */
    public function actionGetShortUrl()
    {
        $smsType = (int)$this->Filter->post('smsType');//sms短信类型
        $url = (string)$this->Filter->post('url');
        $days = (int)$this->Filter->post('days');
        $wxImgUrl = (string)$this->Filter->post('wxImgUrl');

        $serverIp = get_client_ip();
        $checkRs = \GetRedisAction::getInstance()->isIpWhiteList($serverIp);
        if (!$checkRs) {
            $errorData = [
                'code' => -200,
                'msg' => 'ip forbidden：'.$serverIp,
            ];
            return \MsgAction::returnJsonData($errorData);
        }

        $smsConfigs = \GetRedisAction::getInstance()->getSmsTypeConfig($smsType);
        $smsConfig = \SmsAction::getInstance()->GetOneConfig($smsConfigs);
        if (empty($smsConfig)) {
            $errorData = [
                'code' => -200,
                'msg' => 'smsType not found',
            ];
            return \MsgAction::returnJsonData($errorData);
        }
        if ($smsConfig['mobile_action_name'] != '\\Smschannel\\DahanTricomChannelAction') {
            $errorData = [
                'code' => -200,
                'msg' => 'sms channel not support',
            ];
            return \MsgAction::returnJsonData($errorData);
        }
        $action = new DahanTricomChannelAction($smsConfig['mobile_username'], $smsConfig['mobile_password'], '');
        $rs = $action->getShortUrl($url, $days, $wxImgUrl);
        $data = [
            'code' => 200,
            'msg' => 'success',
            'data' => json_decode($rs, true),
        ];
        return \MsgAction::returnJsonData($data);
    }
}
