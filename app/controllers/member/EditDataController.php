<?php

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:EditDataController.php
 * 摘    要:
 * 作    者:<EMAIL>
 * 修改日期: 2015/11/20
 */
class EditDataController extends Controller
{
    private $pageArray;

    /**
     * 构造函数初始化数据
     */
    public function __construct()
    {
        $this->pageArray['page'] = 'userData';
        $memberModel = loadModel("member");
        $userinfo = $memberModel->read(PASSID);
        $this->pageArray['phonetype'] = ($userinfo['phone']) ? "edit" : "bind";
        $this->pageArray['emailtype'] = ($userinfo['email'] && $userinfo['email_status']) ? "edit" : "bind";
    }

    /**
     * 基本资料展示
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $memberModel = loadModel("member");
        $userinfo = $memberModel->read(PASSID);
        //======================= 修 改 ============
        $userinfo['tel1'] = $userinfo['tel1'] == "" ? '区号' : $userinfo['tel1'];
        $userinfo['tel2'] = $userinfo['tel2'] == "" ? '电话号码' : $userinfo['tel2'];
        $userinfo['tel3'] = $userinfo['tel3'] == "" ? '分机' : $userinfo['tel3'];
        $userinfo['bday'] = explode('-', $userinfo['bday']);

        //年月日初始化
        for ($i = 1; $i < 100; $i++)
        {
            $temp['year'][] = date("Y") - $i - 5;
            $ii = strlen($i) == 1 ? "0" . $i : $i;
            if ($i < 13)
            {
                $temp['month'][] = $ii;
            }
            if ($i < 32)
            {
                $temp['day'][] = $ii;
            }
        }
        $this->pageArray['info'] = $userinfo;
        $this->pageArray['cdate'] = $temp;
        $this->pageArray['tabIndex'] = 5;
        $this->pageArray['nav'] = 'userdata';
        $this->pageArray['title'] = "基本资料-2345用户中心";
        //loadView("member/user_data.tpl.html", $this->pageArray);
        if (IsMobile())
        {
            list($avatarUrl) = \Service\Avatar\Avatar::getTransferAvatar('login', PASSID, 'middle');
            $this->pageArray['userimg'] = $avatarUrl;
            loadView('member/m/edit_data_wap.tpl.html', $this->pageArray);
        }
        else
        {
            loadView('member/user_data.tpl.html', $this->pageArray);
        }
    }

    /**
     * 修改资料
     * <AUTHOR>
     */
    public function actionEdit()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST')
        {
            $encryptAction = loadAction('Encrypt');
            $_POST = json_decode($encryptAction::aes128cbcHexDecrypt($_POST['data']), true);
            $_POST['name'] = iconv("UTF-8", "GBK//IGNORE", $_POST['name']);
            $_POST["old_area_text"] = iconv("UTF-8", "GBK//IGNORE", $_POST['old_area_text']);
            $_POST["new_area_text"] = iconv("UTF-8", "GBK//IGNORE", $_POST['new_area_text']);
            if (!preg_match("/^\d{5,}$/", $_POST['qq']) && $_POST['qq'] != '')
            {
                echo '300.0';
                return;
            }
            if (!preg_match("/^[\x80-\xff\w_ ]*$/", $_POST['name']) && $_POST['name'] != '')
            {
                echo '300.1';
                return;
            }
            // 验证器， 禁词
            $validator = new \Common\Validator\Validator();
            $validator->addValidator($_POST['name'], \Common\Validator\Validator::BAD_WORDS, array("昵称"));
            list($badWordsStarus, $msg) = $validator->validate();
            if ($badWordsStarus != \Common\Msg\ResponseMsg::SUCCESS)
            {
                echo '300.4';
                return;
            }
            if (!preg_match("/^\d{4}$/", $_POST['year']) && $_POST['year'] != '')
            {
                echo '300.2';
                return;
            }
            if (!preg_match("/^\d{2}$/", $_POST['month']) && $_POST['month'] != '')
            {
                echo '300.2';
                return;
            }
            if (!preg_match("/^\d{2}$/", $_POST['day']) && $_POST['day'] != '')
            {
                echo '300.2';
                return;
            }
            if (!preg_match("/^\d{6}$/", $_POST['area3']) && $_POST['area3'] != '')
            {
                echo '300.3';
                return;
            }
            $formArray["qq"] = $_POST["qq"];
            $formArray['name'] = $_POST['name'];
            $formArray["gender"] = intval($_POST["gender"]);
            $formArray["bday"] = $_POST["year"] . "-" . $_POST["month"] . "-" . $_POST["day"];
            $formArray['area'] = $_POST["area3"];
            $formArray['ip'] = get_client_ip();
            $item = array('name', 'gender', 'bday', 'qq', 'area');
            $update = array();
            foreach ($formArray as $key => $val)
            {
                if (in_array($key, $item))
                {
                    $update[$key] = $val;
                }
            }
            if (!empty($update))
            {
                $memberModel = loadModel("member");
                $userInfo = $memberModel->read(PASSID);
                $userInfo['passid'] = PASSID;

                if ($memberModel->edit(PASSID, $update) !== false)
                {
                    //日志行为打点:用户信息修改或添加; type:USER_INFO_MODIFY; sub_type:NAME、QQ、GENDER、BDAY、AREA;
                    $objLogV2Action = loadAction('logV2');
                    if ($update['name'] != $userInfo['name']) {
                        $objLogV2Action->report('ALL', $userInfo['name'] ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD', 'NAME', $userInfo, $userInfo['name'], $update['name']);
                    }
                    if ($update['qq'] != $userInfo['qq']) {
                        $strLogType = 'USER_INFO_MODIFY';
                        if ($userInfo['qq'] == '') {
                            $strLogType = 'USER_INFO_ADD';
                        } elseif ($update['qq'] == '') {
                            $strLogType = 'USER_INFO_UNBIND';
                        }
                        $objLogV2Action->report('ALL', $strLogType, 'QQ', $userInfo, $userInfo['qq'], $update['qq']);
                    }
                    if ($update['gender'] != $userInfo['gender']) {
                        $objLogV2Action->report('ALL', $userInfo['gender'] ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD', 'GENDER', $userInfo, $userInfo['gender'], $update['gender']);
                    }
                    if ($update['bday'] != $userInfo['bday']) {
                        $objLogV2Action->report('ALL', $userInfo['bday'] ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD', 'BDAY', $userInfo, $userInfo['bday'], $update['bday']);
                    }
                    if ($_POST["old_area_text"] != $_POST["new_area_text"]) {
                        $strLogType = 'USER_INFO_MODIFY';
                        if ($_POST["old_area_text"] == '') {
                            $strLogType = 'USER_INFO_ADD';
                        } elseif ($_POST["new_area_text"] == '') {
                            $strLogType = 'USER_INFO_UNBIND';
                        }
                        $objLogV2Action->report('ALL', $strLogType, 'AREA', $userInfo, $_POST["old_area_text"], $_POST["new_area_text"]);
                    }

                    echo "200.0";
                }
                else
                {
                    echo "500.0";
                }
            }
        }
    }

}