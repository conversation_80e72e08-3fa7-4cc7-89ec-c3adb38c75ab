<?php

use Common\Utils\Url;
use Service\AccountStat\Consts\AccountStat;
use Service\Encryption\Aes\AesManager;
use Service\Encryption\Sign\SignManager;
use WebLogger\Facade\LoggerFacade;

includeBase('NController');

class NUserLoginController extends NController
{
    private $checkLogin;

    /**
     * 账号 + 密码登录 ， 主要账号需要加密传递
     * 参考/clientapi/login/clientLogin
     * @return void
     */
    public function actionIndex()
    {
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            return;
        }
        $captchaId = Common\Utils\Url::getStringParam('captchaId');
        $app_use_neCaptcha = APP_USE_NECAPTCHA != "false" && !empty($captchaId);
        $warningAction = loadAction('Warning');
        $warningAction->setRequestInfo([
            'dir_type' => 'w_default',
            'type' => 'login',
            'path' => true,
            'account_type' => 'username',
            'source' => $_REQUEST['mid'],
            'app_use_neCaptcha' => $app_use_neCaptcha
        ]);
        $uploadToKafkaAction = loadAction('uploadToKafka');
        $uploadToKafkaAction->setSwitch(true);//开启kafka上报
        $uploadToKafkaAction->addData([
            'event_type' => AccountStat::TP_LOGIN,
            'type' => AccountStat::AC_TP_USERNAME,
        ]);
        session_start();
        if (!isset($_SESSION['expire'])) {
            $_SESSION['expire'] = 0;
        }
        $errInfo = '';
        $this->checkLogin = loadAction('checkLoginForClient');
        if (isset($_POST['checkCode'])) {
            $_POST['check_code'] = $_POST['checkCode'];//兼容
        }
        $checkCode = Url::getStringParam('checkCode');
        if (!$this->checkLogin->checkFLoginInfo($_POST, $errInfo, false)) {
            $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
            $info = '禁止登录，错误码1001，请联系客服！';
            if (RUNMODE == 'development') {
                $info .= '[' . $errInfo . ']';
            }
            unset($errInfo);
            $this->checkLogin->clearFInfo();
            $this->checkLogin->clearCaptcha();
            $this->codeResponseNew(403, "", "登录失败，请重新再试", 1000003);
        }
        $userNameField = $this->checkLogin->getUserNameField();
        $necessaryField = self::getNeedField([
            'password',
            'mid',
            'flToken',
            $userNameField,
            SignManager::$paramSignName,
        ]);
        list(, $mid) = $this->getParamAndAutoDeny($necessaryField);
        $loginAction = loadAction('login');

        if ($app_use_neCaptcha) {
            $validateAction = loadAction('NEValidate');
            $errInfo = null;
            if (!$validateAction->appCheck163Verify($_POST, $errInfo)) {
                $context = [
                    'level'   => 'error',
                    'type'    => "error_ne_net",
                    'path'    => "/clientapi/NUserLogin/index",
                    'context' => $errInfo,
                ];
                LoggerFacade::error("易盾校验失败", $context);
                if ($errInfo["error"] == "100105") {
                    $this->codeResponse(100105, "", "校验失败");
                }
                $this->codeResponse(4105, "", "校验失败");
            }
        } else {
            if ('pcbs' == $mid) {
                $this->checkLogin->setShowCodeLimitCount(5);
            }
            if ($this->checkLogin->isNeedCheckCaptcha()) {
                if (
                    !empty($checkCode) &&
                    isset($_SESSION['captcha_code'])
                ) {
                    $codeValidate = 0 == strcmp($checkCode, $_SESSION['captcha_code']);
                    if (!$codeValidate) {
                        $this->checkLogin->clearFInfo();
                        $this->checkLogin->clearCaptcha();
                        $this->codeResponseNew(304, "", "验证码错误，请重新输入", 1000004);
                    }
                } else {
                    $this->checkLogin->clearFInfo();
                    $this->checkLogin->clearCaptcha();
                    $this->codeResponseNew(305, "", "需要验证码！", 1000005);
                }
                $this->checkLogin->clearCaptcha();
            }
        }

        $aesManager = new AesManager();
        $username = $aesManager->aes128cbcHexDecrypt(trim($_POST[$userNameField]));
        $username = mb_convert_encoding($username, "gbk", "utf8");
        $password = $aesManager->aes128cbcHexDecrypt(trim($_POST['password']));
        $domain = trim($_POST['domain']);
        $result = $loginAction->login(
            $username,
            $password,
            $domain,
            get_client_ip(),
            1,
            $_SERVER['HTTP_USER_AGENT'],
            $domain,
            \Service\AccountStat\Consts\AccountStat::CTP_APP
        );
        $state = $result[0]; //返回状态
        //  $state返回值，分别代表的意思如下
        //	 * -2:通行证帐号有重名，需要改名
        //	 * -1:2345帐号密码错误，同一用户连续三次-1的情况，需要加验证码
        //	 * 0:需要帐号认领
        //	 * 1:登录成功，$result中的值包含帐号信息
        //   * 2:通行证帐号冻结中需要激活。
        //新增需要添加验证码状态
        //   * 3:登录需要添加验证码。
        //   * 4:非法域名。
        //   * 5:非法ip。
        //   * 6:限制ip6小时。
        //$state = -1;
        if ($state == 6) {
            //限制ip6小时
            $this->codeResponseNew(403, "", "登录失败，请重新再试", 2000001);
        } elseif ($state == 5) {
            //非法IP
            $this->codeResponseNew(403, "", "登录失败，请重新再试", 2000002);
        } elseif ($state == 4) {
            //非法域名
            $this->codeResponseNew(403, "", "登录失败，请重新再试", 2000003);
        } elseif ($state == 3) {
            $this->checkLogin->clearFInfo();
            $this->checkLogin->clearCaptcha();
            $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
            $this->codeResponseNew(304, "", "验证码错误，请重新输入", 2000004);
        } elseif ($state == 2) {
            setcookie('active_passid', $result['passid'], 0, '/');
            setcookie('active_email', $result['email'], 0, '/');
            $this->codeResponseNew(403, "", "登录失败，请重新再试", 2000005);
        } elseif ($state === -1) {
            $this->checkLogin->clearFInfo();
            $this->checkLogin->clearCaptcha();
            $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
            $this->codeResponseNew(400, "", "用户名或密码错误，请重新输入", 2000006);
        } elseif ($state === 1) {
            unset($_SESSION['expire']);
            $cookie = $result['cookie'];
            $data = ['source' => encryptCookieIByAes128cbc($cookie['I']),];
            if ('pcbs' == $mid) {
                $data['source2'] = encryptCookieIByAes128cbc($result['phone']);
            }
            $data['login_type'] = 1;
            $warningAction->setRequestInfo(['passid' => $result['passid'], 'login_time' => time()]);
            $this->codeResponseNew(200, $data, "登录成功", 2000000);
        }
    }
}
