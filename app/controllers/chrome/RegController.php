<?php

class RegController extends Controller
{

    public function actionIndex()
    {
        if ($_SERVER["REQUEST_METHOD"] == "POST")
        {
            $regex = Config::get('regex');
            if (filter_var($_POST['username'], FILTER_VALIDATE_EMAIL))
            {
                $this->regEmail();
            }
            elseif (preg_match($regex['phone'], $_POST['username']))
            {
                $this->regPhone();
            }
            else
            {
                $this->regUserName();
            }
        }
        else
        {
            loadView('chrome/reg.tpl.html');
        }
    }

    /**
     * 用户名注册
     */
    private function regUserName()
    {
        session_start();
        if (!isset($_POST["validate"]) || $_POST["validate"] == '' || $_POST["validate"] !== '' . $_SESSION["checkIMGCode_new"])
        {
            unset($_SESSION["checkIMGCode_new"]);
            die("{'err':1,'msg':'default@" . iconv("GBK", "UTF-8//IGNORE", "验证码输入错误") . "'}");
        }
        unset($_SESSION["checkIMGCode_new"]);
        $client_ip = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '2345帐号最少2个字符',
                1 => '2345帐号请不要超过24个字符',
                2 => '2345帐号请输入汉字，字母，数字，或邮箱地址',
                3 => '密码最少6个字符',
                4 => '密码最多16个字符',
                5 => '请输入正确的邮箱',
                6 => '此帐号已被注册，请修改2345帐号',
                7 => '此邮箱已被注册，请换一个',
            ),
            400 => array(
                0 => 400, //非法域名调用
                1 => 401, //非法IP调用
                2 => 402, //批量刷CHECK
                3 => 403, //IP段被禁止
                4 => 404, //IP被禁止
                5 => 405, //未验证通过（缺少isValidate）
            )
        );
        $username = trim(mb_convert_encoding($_POST['username'], "GBK", "UTF-8"));
        if (!isset($_POST['pwd_strength']))
        {
            $pwd_strength = 2;
        }
        else
        {
            $pwd_strength = $_POST['pwd_strength'];
        }
        $regAction = loadAction('reg');
        $source = \Service\AccountStat\Consts\AccountStat::SRC_IE_CHROME;
        $result = $regAction->reg($username, trim($_POST['password']), '', $pwd_strength, '', 'chrome.login.2345.com', $client_ip, $source);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            die("{'err':1,'msg':'default@error:{$msgs[$states[0]][$states[1]]}'}");
        }
        else if ($states[0] == 300)
        {
            if ($states[1] < 3 || $states[1] == 6)
            {
                die("{'err':1,'msg':'username@" . iconv("GBK", "UTF-8//IGNORE", $msgs[$states[0]][$states[1]]) . "'}");
            }
            else if ($states[1] < 5)
            {
                die("{'err':1,'msg':'password@" . iconv("GBK", "UTF-8//IGNORE", $msgs[$states[0]][$states[1]]) . "'}");
            }
        }
        else if ($states[0] == 200)
        {
            die("{'err':0,'uid':'{$result[1]['uid']}','sec':'" . get_sec_browser($result[1]['uid']) . "','passid':'{$result[1]['passid']}','token':'{$result[1]['token']}'}");
        }
    }

    /**
     * 手机注册
     */
    private function regPhone()
    {
        if (!isset($_POST["validate"]) || $_POST["validate"] == '' || !$this->actionCheckPhoneCode($_POST['username'], $_POST['validate'], true))
        {
            die("{'err':1,'msg':'default@" . iconv("GBK", "UTF-8//IGNORE", "验证码输入错误") . "'}");
        }
        $client_ip = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $username = trim(mb_convert_encoding($_POST['username'], "GBK", "UTF-8"));
        if (!isset($_POST['pwd_strength']))
        {
            $pwd_strength = 2;
        }
        else
        {
            $pwd_strength = $_POST['pwd_strength'];
        }
        $regAction = loadAction('reg');

        $source = \Service\AccountStat\Consts\AccountStat::SRC_IE_CHROME;
        $result = $regAction->regPhone($username, $_POST['password'], $pwd_strength, $client_ip, 'chrome.login.2345.com', 200, $source);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            die("{'err':1,'msg':'default@error:{$msgs[$states[0]][$states[1]]}'}");
        }
        else if ($states[0] == 300)
        {
            if ($states[1] > 0 && $states[1] < 3)
            {
                die("{'err':1,'msg':'password@" . iconv("GBK", "UTF-8//IGNORE", $msgs[$states[0]][$states[1]]) . "'}");
            }
            else
            {
                die("{'err':1,'msg':'username@" . iconv("GBK", "UTF-8//IGNORE", $msgs[$states[0]][$states[1]]) . "'}");
            }
        }
        else if ($states[0] == 200)
        {
            die("{'err':0,'uid':'{$result[1]['uid']}','sec':'" . get_sec_browser($result[1]['uid']) . "','passid':'{$result[1]['passid']}','token':'{$result[1]['token']}'}");
        }
    }

    /**
     * email注册
     */
    private function regEmail()
    {
        session_start();
        if (!isset($_POST["validate"]) || $_POST["validate"] == '' || $_POST["validate"] !== '' . $_SESSION["checkIMGCode_new"])
        {
            unset($_SESSION["checkIMGCode_new"]);
            die("{'err':1,'msg':'default@" . iconv("GBK", "UTF-8//IGNORE", "验证码输入错误") . "'}");
        }
        unset($_SESSION["checkIMGCode_new"]);
        $client_ip = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的邮箱地址',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此邮箱号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $username = trim(mb_convert_encoding($_POST['username'], "GBK", "UTF-8"));
        if (!isset($_POST['pwd_strength']))
        {
            $pwd_strength = 2;
        }
        else
        {
            $pwd_strength = $_POST['pwd_strength'];
        }
        $regAction = loadAction('reg');
        $source = \Service\AccountStat\Consts\AccountStat::SRC_IE_CHROME;
        $result = $regAction->regEmail($username, $_POST['password'], $pwd_strength, $client_ip, 'chrome.login.2345.com', $source);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            die("{'err':1,'msg':'default@error:{$msgs[$states[0]][$states[1]]}'}");
        }
        else if ($states[0] == 300)
        {
            if ($states[1] > 0 && $states[1] < 3)
            {
                die("{'err':1,'msg':'password@" . iconv("GBK", "UTF-8//IGNORE", $msgs[$states[0]][$states[1]]) . "'}");
            }
            else
            {
                die("{'err':1,'msg':'username@" . iconv("GBK", "UTF-8//IGNORE", $msgs[$states[0]][$states[1]]) . "'}");
            }
        }
        else if ($states[0] == 200)
        {
            die("{'err':0,'uid':'{$result[1]['uid']}','sec':'" . get_sec_browser($result[1]['uid']) . "','passid':'{$result[1]['passid']}','token':'{$result[1]['token']}'}");
        }
    }

    /**
     * 发送手机验证码
     */
    public function actionSendPhoneCode($type = 'voice')
    {
        $phone = isset($_POST['phone']) ? $_POST['phone'] : null;
        $regex = Config::get('regex');
        if (preg_match($regex['phone'], $phone))
        {
            $memberModel = loadModel('member');
            $ret = $memberModel->checkPhone($_POST['phone'], 1);
            if ($ret != 0)
            {
                echo '400.0';
                return;
            }

            if ($type == "voice")
            {
                $code = rand(1000, 9999);
            }
            else
            {
                $code = rand(100000, 999999);
            }
            $result = sendCodeUsePhone($phone, $code, 8, 166);
            if ($result == 1)
            {
                $redis = RedisEx::getInstance();
                $redis->setex("regchecktime:" . $phone, 1800, 0);
                $redis->setex("regcode:" . $phone, 1800, md5($phone . $code));
                echo '200.0';
            }
            else if ($result == 2)
            {
                echo '400.0';
            }
            else
            {
                echo '500.0';
            }
        }
        else
        {
            echo '300.0';
        }
    }

    /**
     * 检查手机验证码
     */
    public function actionCheckPhoneCode($phone = null, $code = null, $return = null)
    {
        $respCode = '200.0';
        $code = isset($_POST['code']) ? $_POST['code'] : $code;
        $phone = isset($_POST['phone']) ? $_POST['phone'] : $phone;
        if (preg_match('/^1[0123456789]\d{9}$/', $phone))
        {
            $redis = RedisEx::getInstance();

            $checkTime = $redis->get("regchecktime:" . $phone);
            if ($checkTime > 2)
            {
                $redis->del("regchecktime:" . $phone);
                $redis->del("regcode:" . $phone);
                $respCode = '400.0';
            }

            $checkcode = $redis->get("regcode:" . $phone);
            if (md5($phone . $code) == $checkcode && $checkcode)
            {
                $respCode = '200.0';
            }
            else
            {
                $redis->setex("regchecktime:" . $phone, 1800, ++$checkTime);
                $respCode = '400.0';
            }
        }
        else
        {
            $respCode = '300.0';
        }

        if ($return)
        {
            if (isset($redis))
            {
                $redis->del("regchecktime:" . $phone);
                $redis->del("regcode:" . $phone);
            }
            return $respCode == '200.0' ? TRUE : FALSE;
        }
        else
        {
            echo $respCode;
        }
    }

}
