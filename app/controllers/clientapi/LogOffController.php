<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/10/29
 * Time: 9:11
 */
use Common\Utils\Url as Requester;

class LogOffController extends Controller
{
    /**
     * 注销入口
     * -
     * @return void
     */
    public function actionIndex()
    {
        $_COOKIE = [];
        setcookie("I", "", time() - 3600, '/');
        if (!empty($_POST))
        {
            $necessaryField = array(
                'mid',
                'source',
                'timestamp',
                \Service\Encryption\Sign\SignManager::$paramSignName
            );
            $source = Requester::getStringParam('source');
            $mid = trim(Requester::getStringParam('mid'));
            $this->getParamAndAutoDeny($necessaryField);  //字段不能为空+sign校验
            $uFilter = new \Common\Utils\UFilter();
            $authInfo = $uFilter->sourceFilter($source);
            $loginAction = loadAction('login');
            $loginAuth = $loginAction->checkAuth($authInfo);  //校验登录授权
            if (!empty($loginAuth) && is_array($loginAuth))
            {
                $_COOKIE['I'] = http_build_query($authInfo);
                $loginAction->setLoginCookie(['I' => $_COOKIE['I']], time() + 3600, DOMAIN);
                $prefix = "http://";
                if (isHttps())
                {
                    $prefix = "https://";
                }
                $domain = $prefix . $_SERVER['HTTP_HOST'];
                loadAction('LogOff');
                $md5Str = LogOffAction::getAllowMd5($loginAuth['i'], $loginAuth['s']);
                $url = $domain . '/member/LogOff/index/' . $md5Str;
                if (!empty(LogOffAction::logoutCase($mid))) {
                    $url = $domain . '/member/PLogOff/index/' . $mid . "/" . $md5Str;
                }
                $this->codeResponse('200', ['link' => $url]);
            }
            else
            {
                $this->codeResponse('500', [], '校验失败');
            }
        }
        else
        {
            loadView("LogOff.html");
        }
    }

}
