<script>
var loginProxy = {
    GetQueryString : function (name) 
    {
        var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)"); 
        var r = window.location.search.substr(1).match(reg); 
        if (r!=null) 
        {
            return unescape(r[2]);
        }
        else
        {
            return null; 
        } 
    },
    isIe : function ()
    {
        if (!!window.ActiveXObject || "ActiveXObject" in window)
        {
            return true;
        }
        else
        {
            return false;
        }   
    },
    getParentIframe : function ()
    {
        var iframeObj = '';
        if ( this.isIe() )
        {
            iframeObj = window.parent.document.parentWindow
        }
        else
        {
            iframeObj = window.parent.document.defaultView;
        }
        return iframeObj;
    },
    getSubmitData : function ()
    {
        return this.getParentIframe().submitData;
    },
    getLoginObj : function ()
    {
        return this.getParentIframe().login;
    }
};
try {
    var proxyHash = location.hash ? location.hash.substring(1) : '';
    var getDomain = loginProxy.GetQueryString('domain');
    if ( getDomain != null || getDomain != document.domain)
    {
        document.domain = getDomain ;
    }
} catch(e) {};
if ( proxyHash == 'callbackLocation')
{
    loginProxy.getLoginObj().response(window.name);
}
else
{
    window.name = loginProxy.getSubmitData();
    window.location = 'http://passport.2345.com/proxy.html#'+proxyHash;
}
</script>
