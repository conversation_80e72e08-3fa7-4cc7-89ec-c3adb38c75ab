/* 积分 */
.tipspan{ display:block; margin:15px 8px; border:1px solid #ffddac; font-size:13px; background-color:#fff9f1; text-align:left; padding:5px 12px; color:#333; line-height:22px;}
.main_cnt1 p{ text-align:left; color:#333; line-height:20px;}
.fontcnt { padding-left:20px;}
.fontcnt p.gray{ color:#666;}
.fontcnt p i{ font-style:normal; color:#f60;}
.step{ padding:16px 0 0 0;}
.module_2{ margin-top:10px;}
.module_2 h3{ background:url(../images/mod2_h.gif) repeat-x; height:31px; line-height:31px; color:#ff7200; font-size:14px;}
.module_2 h3 span{ display:block; height:31px; float:left;}
.module_2 h3 span.mod2_h_lf{ background:url(../images/mod2_h_l.gif) left no-repeat;padding-left:30px;}
.module_2 h3 span.mod2_h_rt{ float:right; padding-right:15px; background:url(../images/mod2_h_r.gif) right no-repeat; font-weight:100;}
.mod2_cnt{ clear:both; overflow:hidden; zoom:1; padding:10px 20px 18px 20px; border:1px solid #cbe2f6; border-top:none; margin:0 auto;}
.mod2_cnt dl{ width:360px; color:#828282; height:123px; display:block; float:left; text-align:left; margin-left:10px; _margin-left:8px;}
.mod2_cnt dl dd i{font-weight:bold; color:#f60; font-style:normal; margin-right:5px;}
.mod2_cnt dl dt{ width:190px;margin-right:10px; float:left;}
.mod2_cnt dl dt img{border:2px solid #d4e3ef; }
.mod2_cnt dl dd{ width:115px; float:left; position:relative }
.mod2_cnt dd b{ line-height:18px; font-weight:100; display:block; margin-bottom:5px;}
.choujiang{ width:80px; height:26px; line-height:26px; color:#fff; background:transparent url(../images/btn01.gif) no-repeat; border:none; font-size:12px; cursor:pointer; margin:0px 0 3px; }
.hlink{ font-weight:100; font-size:12px; color:#00599e; text-decoration:underline; margin-left:15px;}
.toplink{ font-weight:100; font-size:14px; color:#FF7200; text-decoration:underline; margin-left:-9px;}
.more{ color:#999; text-decoration:none; font-size:12px;}
.mod2_cnt ul{  overflow:hidden; padding-left:20px; _padding-left:16px;}
.mod2_cnt ul li{ display:block; float:left; width:172px; height:200px; margin:10px 10px 10px 0px; _margin:10px 8px 10px 0px;}
.mod2_cnt ul li img{ border:1px solid #efefef;}
.mod2_cnt ul li img:hover{ border:1px solid #fea426}
.mod2_cnt ul li b{ font-weight:100; color:#a9a9a9; display:block; height:25px; line-height:25px;}
.mod2_cnt ul li em{ font-style:normal; color:#666;}
.mod2_cnt ul li em font{ color:#f60; font-size:12px; margin-right:5px;}
.duihuan{ width:49px; height:18px; background:transparent url(../images/btn02.gif) no-repeat; border:none; cursor:pointer; margin-left:5px;}
.product{ height:100%; color:#333;}
.product h4{ font-weight:100; font-size:16px; display:block; height:50px; line-height:50px; font-family:"黑体";}
.product dt img{ border:1px solid #eee; margin:8px 15px; float:left; width:300px; height:300px; padding:3px;}
.product dd{ text-align:left; line-height:25px;}
.product dd b{ font-weight:100; padding-top:40px; height:35px; display:block;}
.dhsub,.dhsub02{ border:none; background:transparent url(../images/btn04.gif) no-repeat; width:134px; height:32px; cursor:pointer;}
.dhsub02{ background:transparent url(../images/btn05.gif) no-repeat; margin:8px 0}
.p_detail{ display:block; clear:both; text-align:left; padding:2em;}
.p_detail p{ padding-bottom:10px; text-indent:2em;line-height:25px;}

.p_link{ height:20px; line-height:20px; text-align:left;}
.p_link a{ color:#0088e7; text-decoration:underline; margin:0 15px 0 10px;}
.p_link a.p_link_cur{ font-weight:bold; color:#f60; text-decoration:none;}
.butbar{ margin:10px 0 20px 50px; text-align:left; }
.butbar input{ font-size:12px; width:60px; height:25px; line-height:20px;}
.jfdetail{ background:#d9f1ff; margin:0 auto;}
.jfdetail th{ font-weight:100; height:22px;font-size:12px;}
.jfdetail td{ background:#f9fdff; height:24px; text-align:center; color:#333; font-size:12px;}
.info_modify{ margin:15px; padding:15px 0; background:#f6fbff;}
.info_modify th{ text-align:right; font-weight:100; color:#333; font-size:12px;}
.info_modify td{ line-height:26px; height:28px; text-align:left;}
.modyfy_btn{ width:80px; height:28px; font-size:12px; margin-top:5px;}
.padlf{ margin-left:50px;}
.bordernone{ border:none;}
dl.tuiguang{ background:url(../images/jifen-icon.gif) 0 0 no-repeat; padding-left:75px; width:160px; line-height:18px;}
.tuiguang dt{ color:#2876b2; font-weight:bold; font-size:14px; line-height:24px;}
.tg2{ background-position:-50px 0}
.fixlayer{ padding-right:15px; }
.fixlayer h5{ border-bottom:1px dashed #ccc; height:40px; margin-bottom:10px;line-height:40px; text-align:left; font-size:14px; font-weight:bold; }
.fixlayer h6{ font-size:14px; text-align:left; font-weight:100; height:24px; line-height:24px; padding:10px 0 5px 0; clear:both; cursor:pointer;}
.fixlayer h6 em{ float:right; font-style:normal;}
.layer_h{ background:#fffaf4; padding:10px; overflow:hidden;}
.layer_h i{ text-align:right; display:block; clear:both; margin-top:10px;width:100%; height:20px;}
.layer_h table{ background:#fde6c7;}
.layer_h td,.layer_h th{ background:#fff; padding:5px; text-align:left; color:#454545; font-size:14px; line-height:22px;}
.layer_h p{ color:#454545; font-size:14px; padding:8px 0 0 0; line-height:22px;}
.centerM{ margin:0 auto; text-align:center;} 
.resli{ width:46%; float:left; text-align:left; margin:15px 0 10px 15px; _margin:15px 0 10px 8px;}
.resli img{ float:left; margin:10px 10px 0 0;}
.resli b{ font-size:14px; height:30px; line-height:30px; }
.resli p{ color:#535353; line-height:18px;}


/*==========5.3日添加---------*/
.award_bbs_l{width:390px; float:left}
.award_bbs_r{width:390px; float:right}
.award_bbs_m{ border:1px solid #CBE2F6; border-top:0; padding:10px 20px; text-align:left;}

.bbs_m li{ height:24px; line-height:24px;}
.bbs_m li a{color:#535353; text-decoration:none}
.bbs_m li a:hover{color:#f60; text-decoration:underline}
.bbs_m li em{font-size:12px; font-style:normal; font-family:"宋体";}
.bbs_m li span{margin-right:5px; color:#a9a9a9}
.bbs_m li span.time{width:65px; float:right}
.award_bbs_m table{color:#535353;}
.award_bbs_m td.red{color:red}
.clear{clear:both}

.my_link{margin-bottom:10px; padding:10px 5px; border:1px solid #ffe0ab; height:45px; line-height:22px; background-image:url(../images/jifen_login.gif); text-align:left; color:#666}
.my_link a{text-decoration:underline}
.my_link a:hover{color:red; text-decoration:underline}
.jifenmx{padding:5px; height:24px; line-height:24px; background-color:#6aacef; text-align:left; color:#fff;}
.jifenmx a{color:#fff}
.jifenmx em.shour{ font-style:normal; margin-left:10px;}
.jifenmx .dizhi{ width:100px; font-style:normal; margin-right:20px;float:right;}
.jifenmx em.shour a{  padding:2px 5px; margin:0 5px;border:1px solid #6aacef; text-decoration:underline}
.jifenmx em.shour a:hover{color:#0557aa;border:1px solid #519de9; background-color:#d6edfd; text-decoration:none}
.jifenmx em.shour a.active{color:#0557aa;font-weight:bold;border:1px solid #c8e7fc; background-color:#f1f9ff; text-decoration:none}

.jfdetail02{margin:0 auto; border:1px solid #a3ccf6; }
.jfdetail02 th{ font-weight:100; background:#d9f1ff; height:22px;font-size:12px;}
.jfdetail02 td{height:24px; line-height:24px; text-align:center; color:#333; font-size:12px;border:1px solid #fff; border-top:0; border-right:0}

.jfdetail02 tr td a {text-decoration:underline;}
.jfdetail_bg1{ background-color:#d5f6c9}
.jfdetail_bg2{ background-color:#ffc0b1}
.jfdetail_bg3{ background-color:#fef9d2}

.jifen_bbs{ margin:10px 20px; text-align:left;}
.jifen_bbs table{ line-height:28px;}
.jifen_bbs th{border-bottom:1px solid #EEE; height:28px; line-height:28px; text-align:left;padding-left:5px; color:#000;}
.jifen_bbs td{padding-left:5px; height:28px; line-height:28px;border-bottom:1px solid #EEE; color:#333;}

.path em{width:60px; height:20px;padding-top:3px; margin-right:10px; float:right; }
h6.path span{float:right; color:#f60}

.fabu_bbs{width:690px; margin:0 auto; color:#666; text-align:left;}
.page .input2{width:30px; height:18px; border:1px solid #CCC}
.fabu_bbs_input{padding:3px;border:1px solid #ccc}
.jifen_subt{padding:0 5px; height:24px;}

.jifen_bbs_m{margin:0 20px; text-align:left;}
.jifen_bbs_m span{border:0;padding:0; margin:0; background-color:transparent}
.bbs_main{border-bottom:1px dashed #dbdbdb; margin-bottom:10px;}
.bbs_main h5{padding:0; margin:0; height:40px; line-height:40px; font-size:16px; font-weight:bold; text-align:center; color:#000; border-bottom:1px dashed #AFAFB0}
.bbs_main span.zuozhe{height:30px;padding-top:5px; display:block;text-align:center; font-size:12px; color:#999; margin-bottom:10px;}
.bbs_main span.zuozhe img, .bbs_back dd img{width:50px; height:18px;}
.bbs_main span.body{text-align:left; font-size:14px; color:#222; margin-bottom:10px; display:block;}
.bbs_main span.lou{text-align:right; color:#999; padding-bottom:10px; display:block;}
.bbs_back{border-bottom:1px dashed #dbdbdb; margin-bottom:10px;padding-bottom:10px; font-size:14px; color:#222}
.bbs_back dt{line-height:24px;}
.bbs_back dd{padding-top:5px; line-height:24px; color:#999;}
.bbs_back dd i{float:left; margin-right:3px; font-style:normal}
.bbs_back dd em{float:right; font-style:normal}

.layer_h p b {background: url("../images/icon_2.gif") no-repeat scroll left 2px transparent; display: block; font-weight: 100; padding-left: 18px;}
.jf_faq{ font-size:14px; color:#666; height:40px; line-height:40px; text-align:left; margin-bottom:15px;}
.jf_faq a{padding:5px 10px; color:#666; text-decoration:underline; border:1px solid #fff;}
.jf_faq a:hover{border:1px solid #ffddac; text-decoration:none;}
.jf_faq a.active{ color:#f60;font-weight:bold; text-decoration:none}

.faq{margin:0 auto; text-align:left; }
.faq dl{padding-bottom:10px; margin-bottom:10px;font-size:14px; border-bottom:1px dashed #c7c7c7}
.faq dt{padding-left:20px;height:24px;line-height:24px;font-weight:bold;color:#000;	background: url(../images/f_ico.gif) no-repeat 0px 6px;}
.faq dd{padding-left:20px;font-size:14px; line-height:24px;color:#666;background: url(../images/q_ico.gif) no-repeat 0px 6px;}
.btn{background: none repeat scroll 0 0 transparent;  border: 0 none; cursor: pointer; margin: 0 5px;overflow: visible;padding: 0;vertical-align: middle;}
.bbs_subt{background: url(../images/button_bg.gif) no-repeat scroll 0 0 transparent;color: #FFFFFF;cursor: pointer;display: inline-block;font: 14px; width:80px;height: 27px;text-align: center; text-decoration: none;white-space: nowrap; color:#005ba2;}
.bbs_subt2{background: url(../images/button_bg2.gif) no-repeat scroll 0 0 transparent;color: #FFFFFF; cursor: pointer; display: inline-block; font: 14px; width:80px; height: 27px; text-align: center; text-decoration: none;    white-space: nowrap; color:#666;}
.bbs_subt3{background: url(../images/button_bg3.gif) no-repeat scroll 0 0 transparent; color: #FFFFFF;cursor: pointer;display: inline-block;font: 14px; width:80px;height: 27px;text-align: center;text-decoration: none;    white-space: nowrap; color:#e57c00;}

.my_jifen{ line-height:24px;text-align:left; color:#666;}
.my_jifen strong{color:#f60;}

.mylink_ok{margin:20px;padding:10px;background-color:#fffae8; border:1px solid #ffe0ab;color:#666; text-align:left;}
.mylink_ok table{ font-size:14px;}
.mylink_ok a{text-decoration:underline}

.xiangxi{padding:5px;width:160px; left:0px; top:-30px; position:absolute; background-color:#fafdff; border:1px solid #d4e3ef; z-index:999}

.jifenmj{ width:325px; float:left; text-align:left; margin:15px 0 10px 25px;display:inline}
.jifenmj img{float:left; margin:10px 10px 0 0;}
.jifenmj b{ font-size:14px; height:30px; line-height:30px; }
.jifenmj p{width:240px; float:left; color:#535353; line-height:18px;}

.jifenmj_help{ width:315px; float:left; text-align:left; margin:15px 0 10px 15px;display:inline}
.jifenmj_help img{float:left; margin:10px 10px 0 0;}
.jifenmj_help b{ font-size:14px; height:30px; line-height:30px; }
.jifenmj_help p{width:230px; color:#535353; line-height:18px;float:left;}
.ke-toolbar-table span{background-color: none;}
.main_cnt1 .ke-container span{ background-color:none; border:none; padding:0; margin:0;}
.main_cnt1 .body span{ display:block; margin:0; border:0; font-size:12px; background-color:none; text-align:left; padding:0; color:none; line-height:0;}
.module_2 .body h3{ background:none; height:0; line-height:0; color:none; font-size:12px;}
.module_2 .body h3 span{ display:block; height:0px; float:left;}

.my_link .but01{padding:1px 8px;}

.mygift{ width:97%; _height:448px; min-height:448px; margin:0 auto;}
.mygift_left{ width:120px; float:left;text-align:left; _height:470px; min-height:470px ; border-right:1px solid #e8f2fa;}
.mygift_left ul{background:#fafdff; overflow:hidden; height:80px; padding:10px 0 10px 10px; }
.mygift_left li{ height:30px; line-height:30px; color:#666;border-bottom:1px dashed #eee}
.mygift_left li a{ color:#333; font-size:13px;}
.mygift_left li a:hover,.mygift_left li a.act{ font-weight:bold; color:#0088e7;}
.mygift_right{ float:right; width:570px;}

.gifttag{ height:25px; line-height:25px; border-bottom:2px solid #649bd1; margin-bottom:8px; padding-left:12px;}
.gifttag ul li{ float:left;display:block; width:80px; height:25px; line-height:25px; margin:0 6px 0 0; }
.gifttag ul li a{ display:block; width:80px; height:25px; line-height:25px; float:left; text-align:center; font-weight:bold; color:#3C85BC;}
.gifttag ul li a.act,.gifttag ul li a:hover{ background:#649bd1; color:#fff; font-weight:bold;}

.gifttab{ background:#e4f3fe;}
.gifttab td,.gifttab th{ color:#666; height:25px; line-height:25px; background:#fff;}
.gifttab th{ background:#f3f9fe; border-top:1px solid #fdfefe;}
.gifttab td.disp{ color:#f60; line-height:18px; text-align:left; padding:10px 0 10px 5em; background:#fafbfc}
.path2{ font-size:12px; color:#999; text-align:left; margin:0 10px 10px; padding:0 0 2px 10px; border-bottom:2px solid #f2f7fa;}

.paihang{padding-bottom:10px;}
.ph_w{text-align:left; border:1px solid #e4f0fa; border-top:0}
.ph_w table{background:url(../images/phb.png) no-repeat 5px 25px}
.ph_r_m{margin-right:15px;}
.ph_w td, .ph_m2 td{padding-left:5px;height:23px; line-height:23px; border-bottom:1px dotted #e6e6e6; color:#666}
.ph_w th, .ph_m2 th{padding-left:5px;height:24px; line-height:24px; background:#fbfbfb; border-bottom:1px solid #f0f0f0; color:#666; font-weight:normal; text-align:left;}
.ph_w th span{color:red; font-weight:bold}
.ph_m2{margin:0 10px;text-align:left;}
.ph_m2 table{border:1px solid #e4f0fa; text-align:left;}
.ph_m2 td{ height:25px; line-height:25px;padding:5px;}
.ph_m2 td img{margin-left:5px;}
.ph_m2 .no1 td{ color:#F60}
.ph_m2 td span{ width:90px; display:block; float:right}
.ph_m2 .toptd{ height:26px; line-height:26px;background:#eef9ff; color:#3c85bc; font-weight:bold;}
.ph_m2 ul{padding-left:10px; height:24px; margin-bottom:10px;border-bottom:1px solid #90b8e2}
.ph_m2 li{width:120px; height:24px; line-height:24px; text-align:center; float:left; margin-right:10px; background:#6aacef;}
.ph_m2 li a{ width:120px; height:25px; display:block;color:#fff;}
.ph_m2 li a:hover{color:#fff; text-decoration:underline}
.ph_m2 li a.active{color:#666; display:block; height:23px; margin-bottom:-1px; background:#fff; border:1px solid #90b8e2; border-bottom-color:#fff; font-weight:bold; position:relative;}
.ph_m2 li a:hover.active{color:#666; text-decoration:underline}
.path2{ font-size:12px; color:#999; text-align:left; margin:0 10px 10px; padding:0 0 2px 10px; border-bottom:2px solid #f2f7fa;}
.ipt_001{ width:40px; height:20px; line-height:20px; padding-left:3px;}
.p_info2 th{ padding-right:10px;}
.p_info2 th,.p_info2 td{ line-height:28px;}
.p_info2 label{ color:#000;}
.p_info2 .che{ width:13px; height:13px; margin:-3px 2px 0 8px; vertical-align:middle;}
.p_info2 select{ width:80px; margin:0 6px 0 0; font-size:13px;}
.p_info2 i{ color:#000; font-size:12px; font-style:normal;}

.ph_m2 ul li.modify{width:170px; height:20px; margin:0; display:block; background:none; float:right}
.ph_m2 ul li.modify a{width:auto; display:inline;COLOR: #3c85bc}
.ph_m2 ul li.modify a:hover{color:#f60}

/* 2011-11-14 */
.path3{ background:#f7fcff; height:25px; line-height:25px; font-weight:100; font-size:12px; text-align:left; border:1px solid #d9eefe; padding-left:20px; color:#f60;}
.path3 i{ margin:0 10px; font-style:normal; color:#3c85bc;}
.module_3{ border:1px solid #d9eefe; margin-top:6px; padding-bottom:10px;}
.module_3 h2{ background:#e4f3fe; border-bottom:1px solid #d9eefe; border-top:1px solid #eff7fc; height:25px; line-height:25px; text-align:left;padding-left:15px; margin-bottom:10px;}
.module_3 h2 b{ font-weight:bold; }
.module_3 h2 span{ float:right; font-weight:100; margin-right:15px;}
.module_3 h2 span a{ margin:0 5px;}
.module_3 h2 span a.forange{ color:#f60; text-decoration:underline;}
.module_3 td{ height:35px; line-height:35px; text-align:left; color:#666; padding-top:5px;}
.module_3 th{ text-align:right; padding-right:10px; font-weight:100;}

.shdztab td,.shdztab th{ height:35px; line-height:35px; text-align:left; color:#666; padding-top:5px; vertical-align:top;}
.shdztab th{ text-align:right; padding-right:10px; font-weight:100; width:150px;}
.ipt_002{ height:22px; line-height:22px; width:150px; border:1px solid #97afc5; padding-left:4px;}
.chk01{ width:13px; height:13px; margin:-3px 5px 0 0; padding-top:5px; vertical-align:middle;}
.btn01{ width:80px; height:25px; line-height:25px; cursor:pointer; margin:0 16px 20px 0;}
.remark{ text-align:left; line-height:24px; color:#0088e7; padding:15px 10px;}
.module_3 .infolist td,.module_3 .infolist th{ line-height:30px; height:30px;}

.mod2_cnt dd .jiang_sm{ height:72px; line-height:18px; font-weight:100; display:block; margin-bottom:5px; position:relative;}
.mod2_cnt dd .jiang_sm ul{padding:0; margin:0}
.mod2_cnt dd .jiang_sm li{ width:115px; height:18px; line-height:18px; padding:0; margin:0; color:#828282}
.mod2_cnt dd .jiang_sm li span{ width:35px;float:right; text-align:right}
.mod2_cnt dd .jiang_sm li .icon{ background:url(/images/jifen_bottomico.gif) no-repeat 38px 2px;}
.mod2_cnt dd .jf_r li span{padding-right:20px;}
.mod2_cnt dd .jiang_sm2{ background:#fafdff; border:1px solid #d4e3ef;padding:3px 5px; left:-6px; top:-4px; position:absolute;}
.choujiang_hover{ width:80px; height:26px; line-height:26px; color:#fff; background:transparent url(../images/btn01_hover.gif) no-repeat; border:none; font-size:12px; cursor:pointer; margin:0px 0 3px; }

.mod2_cnt dl dd i{color:#f60; font-style:normal; font-weight:normal; margin:0 5px;}

.lucky_img{padding-right:10px; width:191px; height:123px; float:left;}
.lucky_img img{border:2px solid #d4e3ef; width:187px; height:119px;}
.lucky_no{padding:22px 5px 0;border:2px solid #d4e3ef; width:177px; height:97px; background:url(/images/jf_cj_lucky.gif) no-repeat 0 0}
.fenlei{padding:2px 0 8px;height:16px; line-height:16px; display:block; font-size:12px; font-weight:bold; color:#fff;}
.fenlei i{float:right; font-weight:normal; font-style:normal}
.fenlei i a{color:#fff;}
.xh_main{height:58px; line-height:13px; color:#484334}
.xh_main1 {height:58px; line-height:13px; color:#484334}


.xianxia_m{padding:0 10px;text-align:left; background: #fff url(/images/xianxia_bg.gif) repeat-x 0 bottom; border-left:1px solid #fff; border-right:1px solid #fff;}
.xianxia_m .crumb{ margin-bottom:10px; height:21px; line-height:21px; border-bottom:2px solid #f2f7fa; color:#999}
.xianxia_m .xx_title{padding-left:10px; height:25px; line-height:25px; border-bottom:2px solid #649bd1;}
.xianxia_m .xx_title span{ width:80px; height:25px; display:inline-block; text-align:center; color:#fff; font-weight:bold; background:#649bd1}
.xianxia_m .xx_about{padding:15px 10px;}
.xianxia_m .xx_about li{padding-left:10px;height:24px; line-height:24px; background:url(/images/xianxia_ico.gif) no-repeat 0 10px; color:#454545}
.xianxia_m .xx_about span{height:24px; line-height:24px; display:block;}

.xx_list{padding:15px 10px; text-align:left; color:#454545;}
.xx_list span{padding-left:10px; height:21px; line-height:21px; display:block;color:#f60}
.xx_list table{ border:1px solid #bedcf5}
.xx_list td{height:26px; line-height:26px; border-right:1px solid #e4f3fe; border-bottom:1px solid #e4f3fe; text-align:center;}
.xx_list th{height:26px; line-height:26px; border-top:1px solid #fff; border-right:1px solid #bedcf5; border-bottom:1px solid #bedcf5; background:#e4f3fe; font-size:12px; text-align:center; color:#666}
.xx_list th.end, .xx_list td.end{border-right:none}
.d2 dd.line{ border-top:1px dashed #dedede;}
.d2 dd a{ width:85px; display:inline-block;}

/* 20120312 */
.mod3_cnt{ clear:both; overflow:hidden; zoom:1; padding:10px 5px; border:1px solid #cbe2f6; border-top:none; margin:0 auto;}
.module_2 h3 span.tip_r{ background:url(../images/bulb.png) left no-repeat; padding-left:15px; font-size:12px;float:right;}
.scroll dt{ width:17px; height:24px; overflow:hidden; background:url(../images/scroll.png) no-repeat; cursor:pointer; float:left; margin:65px 4px 0;}
.scroll dt.previewNone{ background-position:0 -26px;}
.scroll dt.previewDefault{ background-position:-1 -26px;}
.scroll dt.next{ float:right; background-position:right 0;}
.scroll dt.nextNone{ background-position:right -26px;}
.scroll dt.nextDefault{background-position:top -26px;}
.scroll{height:100%; overflow:hidden;}
/*.scroll dd{ width:726px; _width:722px; height:185px; overflow:hidden; float:left;}*/
.scroll dd{ _width:722px; height:185px; overflow:hidden; float:left;}
.scroll dd a{ display:block; float:left; width:117px; height:173px; padding-top:6px; border:1px solid #fff; background:#fff; margin-right:2px;_margin-right:1px; cursor:pointer;}
.scroll dd a p{ clear:both; font-size:12px; color:#666; padding-top:5px; }
.scroll dd a strong{ font-weight:100; font-size:14px; background:url(../images/scroll.png) 0 -58px no-repeat; padding-left:15px;}
.scroll dd a:hover p{ color:#fd5151;}
.scroll dd a:hover{ background:#fff7ea; border:1px solid #fac270; -moz-border-radius:4px;-khtml-border-radius:4px; -webkit-border-radius:4px;border-radius:4px;}
.winner{ height:144px; overflow:hidden;}
.winner dt{ float:left; width:142px; float:left; margin-left:10px; _margin-left:5px;}
.winner dt img{ border:1px solid #efefef;}
.winner dd{ text-align:left; margin:0 3px 0 7px; float:right; width:210px; color:#666; font-size:12px;}
.winner dd h4{ display:block; font-weight:bold; font-size:14px; color:#3c85bc; border-bottom:1px solid #efefef; height:25px; line-height:25px; margin-bottom:5px;}

/*积分首页图片幻灯片样式*/
.container{width:710px;height:150px;overflow:hidden;position:relative;margin:0 auto;}
.slider{position:absolute;width:9999em;}
.slider li{ list-style:none;display:inline;float:left;}
.slider li img{ width:710px;height:150px;}
.num{ position:absolute; right:5px; bottom:5px;}
.num li{float: left;color: #FF7300;text-align: center;line-height: 16px;width: 16px;height: 16px;font-family: Arial;font-size: 12px;cursor: pointer;overflow: hidden;margin: 3px 1px;border: 1px solid #FF7300;background-color: #fff;}
.num li.on{color: #fff;line-height: 21px;width: 21px;height: 21px;font-size: 16px;margin: 0 1px;border: 0;background-color: #FF7300;font-weight: bold;}

/*yuebo0401*/
.my_link{position:relative}
.jfdh{width:118px; height:34px; position:absolute; right:10px; top:15px;}
.jfdh a{width:118px; height:34px; display:block; background:url(../images/jifen0401.png) no-repeat 0 0}