<?php
includeBase('AdminController');
class DefaultController extends AdminController
{

    protected $authName = '';

    public function __construct()
    {
        parent::__construct();
    }

    public function actionIndex()
    {
        loadView('admin/index.tpl.html');
    }

    /**
     * <EMAIL>
     * 2016-03-15
     */
    public function actionLeft()
    {
        $model = loadModel('admin');
        $lists = $model->getAuths($_COOKIE['admin_user']);
        $auth_list = array();
        foreach ($lists AS $val)
        {
            $auth_list[$val['id']] = $val['auth'];
        }

        ksort($auth_list);
        $auth = array_flip($this->auth);

        $show = array();
        foreach ($auth_list AS $key => $val)
        {
            if (array_key_exists($val, $auth))
            {
                // $show[$key]['url'] = $auth[$val];
                // $show[$key]['name'] = $val;
                $show[] = $val;
            }
        }

        loadView('admin/left.tpl.html', $show);
    }

}