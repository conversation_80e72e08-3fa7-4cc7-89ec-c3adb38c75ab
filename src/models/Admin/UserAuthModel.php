<?php

namespace Admin;

class UserAuthModel extends \Model
{

    /**
     * UserAuthModel constructor.
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }


    /**
     * 获取权限列表
     * */
    public function getAuthList($data = array())
    {
        $where = '';
        $paramArr = array();
        $sql = 'SELECT * from  auth_list ';
        if (!empty($data['authId']))
        {
            $where .= ' authId = :authId AND';
            $paramArr[':authId'] = $data['authId'];
        }
        if (!empty($data['authPath']))
        {
            $where .= ' authPath = :authPath AND';
            $paramArr[':authPath'] = $data['authPath'];
        }
        if (isset($data['type']))
        {
            $where .= ' type = :type AND';
            $paramArr[':type'] = $data['type'];
        }

        if (isset($data['prid']))
        {
            $where .= ' prid = :prid AND';
            $paramArr[':prid'] = $data['prid'];
        }

        if (!empty($where))
        {
            $where = trim(' WHERE ' . $where, 'AND');
        }
        $where .= ' ORDER BY sort ASC , authId DESC';
        if (!empty($data['row']))
        {
            $authInfo = $this->pdo->find($sql . $where, $paramArr);
        }
        else
        {
            $authInfo = $this->pdo->findAll($sql . $where, $paramArr);
        }
        return $authInfo;
    }

    /**
     * 添加权限信息
     * */
    public function addAuthListInfo($data)
    {
        $this->pdo->beginTransaction();
        if (!empty($data['childPid']))
        {
            $data['prid'] = $data['childPid'];
        }
        $inserData = array(
            'authName' => $data['authName'],
            'addDate'  => date('Y-m-d H:i:s'),
            'authPath' => $data['authPath'],
            'prid'     => $data['prid'],
            //'authDesc' => $data['authDesc'],
            'isShow'   => !empty($data['isShow']) ? $data['isShow'] : 0,
        );
        $isTrue = $this->pdo->insert('auth_list', $inserData);
        //$lastId = $this->pdo->lastInsertId();
        //if ($isTrue)
        //{
        //    if (empty($data['childPid']) && !empty($data['prid']))
        //    {
        //        $groupData = array(
        //            'groupName' => $data['authName'],
        //            'addDate'   => date('Y-m-d H:i:s'),
        //            'threeId'   => $lastId,
        //        );
        //        $isTrue = $this->pdo->insert('group_list', $groupData);
        //    }
        //}
        if ($isTrue)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * 更新权限信息
     * */
    public function upAuthListInfo($data)
    {
        $this->pdo->beginTransaction();
        $condition = [
            'where'  => ' authId = :authId ',
            'params' => [':authId' => $data['authId']],
        ];
        $update = array(
            'authName' => $data['authName'],
            'addDate'  => date('Y-m-d H:i:s'),
            'authPath' => $data['authPath'],
            'authDesc' => @$data['authDesc'],
            'prid'     => $data['prid'],
            'isShow'   => !empty($data['isShow']) ? $data['isShow'] : 0,
        );
        $isTrue = $this->pdo->update('auth_list', $update, $condition);
        //if ($isTrue)
        //{
        //    $groupUpdata = array('groupName' => $data['authName']);
        //    $groupCondition = [
        //        'where'  => ' threeId = :threeId ',
        //        'params' => [':threeId' => $data['authId']],
        //    ];
        //    $isTrue = $this->pdo->update('group_list', $groupUpdata, $groupCondition);
        //}
        if ($isTrue)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * 设置权限信息
     * */
    public function setAuthInfo($data)
    {
        $authInfo = '';
        if (!empty($data['authId']))
        {
            $authInfo = $this->getAuthList(array('authId' => $data['authId'], 'row' => true));
        }

        if (!empty($authInfo))
        {
            $isTrue = $this->upAuthListInfo($data);
        }
        else
        {
            $isTrue = $this->addAuthListInfo($data);
        }
        return $isTrue;
    }

    /**
     * 删除权限
     * */

    public function delAuthInfo($authId)
    {
        $this->pdo->beginTransaction();
        $condition = array('where' => 'authId=:authId ', 'params' => array('authId' => $authId));
        $isTrue = $this->pdo->delete('auth_list', $condition);
        //if ($isTrue !== false)
        //{
        //    $groupCondition = array('where' => 'threeId=:threeId ', 'params' => array('threeId' => $authId));
        //    $isTrue = $this->pdo->delete('group_list', $groupCondition);
        //}
        if ($isTrue !== false)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * 获取用户信息
     * */
    public function getUserInfo($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(id) as total ';
        }

        $sql = "SELECT {$col} from  user_list ";
        if (!empty($data['uid']))
        {
            $where .= ' uid = :uid AND';
            $paramArr[':uid'] = $data['uid'];
        }
        if (!empty($data['username']))
        {
            $where .= ' username = :username AND';
            $paramArr[':username'] = $data['username'];
        }
        if (isset($data['status']))
        {
            $where .= ' status = :status AND';
            $paramArr[':status'] = $data['status'];
        }

        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $where .= ' add_time >= :startTime AND add_time <= :endTime AND';
            $paramArr[':startTime'] = $data['startTime'];
            $paramArr[':endTime'] = $data['endTime'];
        }
        if (!empty($data['serachName']))
        {
            $where .= " ( username like :serachName or addName like :serachName  ) AND";
            $paramArr[':serachName'] = "%{$data['serachName']}%";
            // $paramArr[':serachName'] ='%' . $data['serachName'].'%';
        }

        if (!empty($where))
        {
            $where = trim(' WHERE ' . $where, 'AND');
        }
        $where .= ' ORDER BY id DESC';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $where .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            $userInfo = $this->pdo->find($sql . $where, $paramArr);

        }
        else
        {
            $userInfo = $this->pdo->findAll($sql . $where, $paramArr);
        }
        return $userInfo;


    }

    /**
     * 查询组列表
     * */
    public function getGroupList($data = array())
    {
        $where = '';
        $paramArr = array();
        $sql = 'SELECT * from  group_list ';
        if (!empty($data['groupId']))
        {
            $where .= ' groupId = :groupId AND';
            $paramArr[':groupId'] = $data['groupId'];
        }
        if (!empty($data['groupName']))
        {
            $where .= ' groupName = :groupName AND';
            $paramArr[':groupName'] = $data['groupName'];
        }
        if (isset($data['threeId']))
        {
            $where .= ' threeId = :threeId AND';
            $paramArr[':threeId'] = $data['threeId'];
        }
        if (!empty($where))
        {
            $where = trim(' WHERE ' . $where, 'AND');
        }
        $where .= ' ORDER BY groupId ASC';
        if (!empty($data['row']))
        {
            $authInfo = $this->pdo->find($sql . $where, $paramArr);
        }
        else
        {
            $authInfo = $this->pdo->findAll($sql . $where, $paramArr);
        }
        return $authInfo;
    }

    /**
     * 添加用户信息
     * */
    public function addUserInfo($data)
    {
        $inserData = array(
            'uid'      => $data['uid'],
            'username' => $data['username'],
            'add_time' => date('Y-m-d H:i:s'),
            'addUid'   => $data['addUid'],
            'addName'  => $data['addName'],
        );
        $isTrue = $this->pdo->insert('user_list', $inserData);
        return $isTrue !== false ? true : false;
    }

    /**
     * 更新用户信息
     * */
    public function upUserInfo($data)
    {
        $condition = array('where' => ' uid = :uid ', 'params' => array(':uid' => $data['uid']));
        $updata = array();
        if (isset($data['username']))
        {
            $updata['username'] = $data['username'];
        }
        if (isset($data['status']))
        {
            $updata['status'] = $data['status'];
        }
        $isTrue = $this->pdo->update('user_list', $updata, $condition);
        return $isTrue !== false ? true : false;
    }


    /**
     * 查看用户所属组
     * */
    public function getUserGroupInfo($data)
    {
        $where = '';
        $paramArr = array();
        $sql = 'SELECT * from  user_group_list ';

        if (!empty($data['uid']))
        {
            $where .= ' uid = :uid AND';
            $paramArr[':uid'] = $data['uid'];
        }
        if (!empty($where))
        {
            $where = trim(' WHERE ' . $where, 'AND');
        }
        $where .= ' ORDER BY ugl_id DESC';
        if (!empty($data['row']))
        {
            $userGroup = $this->pdo->find($sql . $where, $paramArr);
        }
        else
        {
            $userGroup = $this->pdo->findAll($sql . $where, $paramArr);
        }
        return $userGroup;
    }

    /**
     * 添加用户所属组
     * */
    public function addUserGroupInfo($data)
    {
        $inserData = array(
            'uid'      => $data['uid'],
            'group_id' => $data['group_id'],
            'add_time' => date('Y-m-d H:i:s'),
        );
        $isTrue = $this->pdo->insert('user_group_list', $inserData);
        return $isTrue !== false ? true : false;
    }

    /**
     * 添加用户所属项目
     * */
    public function addUserProjectInfo($data)
    {

        $inserData = array(
            'uid'      => $data['uid'],
            'pid'      => $data['pid'],
            'add_time' => date('Y-m-d H:i:s'),
        );
        $isTrue = $this->pdo->insert('user_project_list', $inserData);
        return $isTrue !== false ? true : false;
    }

    /**
     * 查看用户所属组
     * */
    public function getUserProjectInfo($data = array())
    {
        $where = '';
        $paramArr = array();
        $sql = 'SELECT * from  user_project_list ';

        if (!empty($data['uid']))
        {
            $where .= ' uid = :uid AND';
            $paramArr[':uid'] = $data['uid'];
        }
        if (!empty($where))
        {
            $where = trim(' WHERE ' . $where, 'AND');
        }
        $where .= ' ORDER BY upl_id DESC';
        if (!empty($data['row']))
        {
            $userGroup = $this->pdo->find($sql . $where, $paramArr);
        }
        else
        {
            $userGroup = $this->pdo->findAll($sql . $where, $paramArr);
        }
        return $userGroup;
    }

    /**
     * 设置用户权限
     * */
    public function setUserAuthInfo($data)
    {
        $this->pdo->beginTransaction();
        $userInfo = $this->getUserInfo(array('uid' => $data['uid'], 'row' => true));
        if (empty($userInfo))
        {
            $isTrue = $this->addUserInfo($data);
        }
        else
        {
            if ($userInfo['status'] == 0)
            {
                $isTrue = $this->upUserInfo(array('uid' => $data['uid'], 'status' => 1));
            }
            else
            {
                $isTrue = true;
            }
        }
        //寻找组信息
        if ($isTrue !== false)
        {
            $userGroupList = $this->getUserGroupInfo(array('uid' => $data['uid']));
            if (empty($userGroupList))
            {
                foreach ($data['groupId'] as $userGroupId)
                {
                    $isTrue = $this->addUserGroupInfo(array('uid' => $data['uid'], 'group_id' => $userGroupId));
                    if ($isTrue === false)
                    {
                        continue;
                    }
                }
            }
            else
            {
                $groupIdStr = '';
                if (empty($data['groupId']))
                {
                    $sql = 'delete from user_group_list where uid=:uid ';
                }
                else
                {
                    foreach ($data['groupId'] as $userGroupId)
                    {
                        $groupIdStr .= $userGroupId . ',';
                    }
                    $groupIdStr = trim($groupIdStr, ',');
                    $sql = 'delete from user_group_list where uid=:uid and group_id not in (' . $groupIdStr . ') ';
                }
                $isTrue = $this->pdo->query($sql, array(':uid' => $data['uid']));
                if ($isTrue !== false)
                {
                    //$dbUserGroupList = $this->getUserGroupInfo(array('uid' => $data['uid']));
                    $dbGroupIds = array();
                    foreach ($userGroupList as $dbUserGroupInfo)
                    {
                        $dbGroupIds[] = $dbUserGroupInfo['group_id'];
                    }
                    $nowGroupIds = array_diff($data['groupId'], $dbGroupIds);
                    foreach ($nowGroupIds as $setGroupId)
                    {
                        $isTrue = $this->addUserGroupInfo(array('uid' => $data['uid'], 'group_id' => $setGroupId));
                        if ($isTrue === false)
                        {
                            continue;
                        }
                    }
                }
            }
        }

        //添加项目
        if ($isTrue !== false)
        {
            $UserProject = $this->getUserProjectInfo(array('uid' => $data['uid']));
            if (empty($UserProject))
            {
                foreach ($data['projectId'] as $projectInfo)
                {
                    $isTrue = $this->addUserProjectInfo(array('uid' => $data['uid'], 'pid' => $projectInfo));
                    if ($isTrue === false)
                    {
                        continue;
                    }
                }
            }
            else
            {
                if (empty($data['projectId']))
                {
                    $sql = 'delete from user_project_list where uid=:uid';
                }
                else
                {
                    $putSmsType = implode(',', $data['projectId']);
                    $sql = 'delete from user_project_list where uid=:uid and pid not in (' . $putSmsType . ') ';
                }
                $isTrue = $this->pdo->query($sql, array(':uid' => $data['uid']));


                if ($isTrue !== false)
                {
                    $dbProject = array();
                    foreach ($UserProject as $UserProjectInfo)
                    {
                        $dbProject[] = $UserProjectInfo['pid'];
                    }
                    $nowProjectList = array_diff($data['projectId'], $dbProject);
                    foreach ($nowProjectList as $nowProjectInfo)
                    {
                        $isTrue = $this->addUserProjectInfo(array('uid' => $data['uid'], 'pid' => $nowProjectInfo));
                        if ($isTrue === false)
                        {
                            continue;
                        }
                    }
                }
            }
        }
        if ($isTrue !== false)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * 获取组权限
     * */
    public function getUserGroupAuth($data)
    {
        $where = '';
        $paramArr = array();
        $sql = "SELECT * FROM `group_auth` ";

        if (isset($data['authId']))
        {
            $where .= " authId = :authId ";
            $paramArr[':authId'] = $data['authId'];
        }

        if (isset($data['groupId']))
        {
            $where .= " groupId = :groupId ";
            $paramArr[':groupId'] = $data['groupId'];
        }

        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where;
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    /**
     * 插入组权限
     * */
    public function addUserGroupAuth($data)
    {
        $inserData = array(
            'authId'  => $data['authId'],
            'groupId' => $data['groupId'],
            'addDate' => date('Y-m-d H:i:s'),
        );
        $isTrue = $this->pdo->insert('group_auth', $inserData);
        return $isTrue !== false ? true : false;
    }


    /**
     * 设置组权限
     * */
    public function setUserGroupAuthInfo($data)
    {
        $this->pdo->beginTransaction();
        $userGroup = $this->getUserGroupAuth(array('groupId' => $data['groupId']));
        $isTrue = true;
        if (empty($userGroup))
        {
            foreach ($data['authId'] as $authInfo)
            {
                $isTrue = $this->addUserGroupAuth(array('authId' => $authInfo, 'groupId' => $data['groupId']));
                if ($isTrue === false)
                {
                    continue;
                }
            }
        }
        else
        {
            $newAuthArr = implode(',', $data['authId']);
            $dbUserGroup = array();
            foreach ($userGroup as $userGroupInfo)
            {
                $dbUserGroup[] = $userGroupInfo['authId'];
            }
            $sql = 'delete from group_auth where groupId=:groupId ';
            if (!empty($newAuthArr))
            {
                $sql .= ' and authId not in (' . $newAuthArr . ') ';
            }
            $isTrue = $this->pdo->query($sql, array(':groupId' => $data['groupId']));
            if ($isTrue !== false)
            {
                $nowAuthIds = array_diff($data['authId'], $dbUserGroup);
                foreach ($nowAuthIds as $nowAuthInfo)
                {
                    $isTrue = $this->addUserGroupAuth(array('authId' => $nowAuthInfo, 'groupId' => $data['groupId']));
                    if ($isTrue === false)
                    {
                        continue;
                    }
                }
            }
        }
        if ($isTrue !== false)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * 添加组
     * */
    public function addUserGroupName($data)
    {
        $groupData = array('groupName' => $data['groupName'], 'addDate' => date('Y-m-d H:i:s'));
        $isTrue = $this->pdo->insert('group_list', $groupData);
        if ($isTrue !== false)
        {
            $isTrue = $this->pdo->lastInsertId();
        }
        return $isTrue;
    }

    /**
     * 更新组
     * */
    public function upUserGroupName($data)
    {
        $groupUpdata = array('groupName' => $data['groupName']);
        $groupCondition = array('where' => ' groupId = :groupId ', 'params' => array(':groupId' => $data['groupId'],),);
        $isTrue = $this->pdo->update('group_list', $groupUpdata, $groupCondition);
        return $isTrue;
    }

    /**
     * @param array $data data
     * @return bool|string
     */
    public function setGroupName($data)
    {
        if (empty($data['groupId']))
        {
            $isTrue = $this->addUserGroupName($data);
        }
        else
        {
            $isTrue = $this->upUserGroupName($data);
        }
        return $isTrue;
    }

    /**
     * @param int $groupId groupId
     * @return bool
     */
    public function delGroup($groupId)
    {
        $this->pdo->beginTransaction();
        $condition = array('where' => 'groupId=:groupId ', 'params' => array('groupId' => $groupId));
        $isTrue = $this->pdo->delete('group_list', $condition);
        if ($isTrue !== false)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * 获取用户分配的项目
     * */
    public function getUserProjectList($uid, $data = array())
    {
        $sql = "SELECT upl.uid,pl.`project_name`,pl.`pid`,pl.`status`,pl.letter FROM `user_project_list` AS upl , `project_list` AS pl WHERE upl.pid = pl.pid AND upl.uid = :uid AND pl.status = 1";
        $paramArr = array(
            'uid' => $uid,
        );
        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $sql .= " AND pl.add_time >=:startTime AND pl.add_time <:endTime  ";
            $paramArr[':startTime'] = $data['startTime'];
            $paramArr[':endTime'] = $data['endTime'];
        }

        if (!empty($data['projectId']))
        {
            $sql .= " AND pl.pid=:pid ";
            $paramArr[':pid'] = $data['projectId'];
        }
        $sql .= ' ORDER BY pl.pid ASC ';
        if (!empty($data['row']))
        {
            $userGroupList = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $userGroupList = $this->pdo->findAll($sql, $paramArr);
        }

        return $userGroupList;
    }

    /**
     * @param int $uid uid
     * @return array|bool
     */
    public function getUserGroupList($uid)
    {
        $sql = "SELECT ugl.uid,gl.groupId,gl.groupName FROM `user_group_list` AS ugl , `group_list` AS gl WHERE ugl.group_id = gl.groupId AND ugl.uid = :uid";
        $paramArr = array(
            ':uid' => $uid,
        );
        $sql .= ' ORDER BY gl.groupId ASC ';
        if (!empty($data['row']))
        {
            $userGroupList = $this->pdo->find($sql, $paramArr);
        }
        else
        {
            $userGroupList = $this->pdo->findAll($sql, $paramArr);
        }

        return $userGroupList;
    }


    /**
     * @param int $uid uid
     * @return array|bool
     */
    public function getUserAccessPaths($uid)
    {
        $sql = "SELECT `authPath` FROM `group_auth` AS ga ,`auth_list` AS al WHERE ga.authId = al.authId  AND groupId  IN (SELECT group_id FROM `user_group_list` AS ugl WHERE ugl.`uid` = :uid)";
        $paramArr = array(
            ':uid' => $uid,
        );
        $userAuth = $this->pdo->findAll($sql, $paramArr);
        return $userAuth;
    }

    /**
     * 获取用户访问权限
     * */
    public function getUserAccessAuth($uid, $authPath)
    {
        $sql = "SELECT * FROM `group_auth` AS ga ,`auth_list` AS al WHERE ga.authId = al.authId AND
             al.authPath = :authPath AND groupId  IN (SELECT group_id FROM `user_group_list` AS ugl 
              WHERE ugl.`uid` = :uid)";
        $paramArr = array(
            ':uid'      => $uid,
            ':authPath' => $authPath,
        );
        $userAuth = $this->pdo->find($sql, $paramArr);
        return $userAuth;
    }

    /**
     * 导航栏显示
     * */
    public function getUserNav($uid, $prid, $isShow = 1)
    {
        $sql = "SELECT DISTINCT(al.`authId`) ,al.`authName`,al.`authPath`,al.`addDate`,al.`prid`,al.`authDesc`,al.`isShow`,al.sort FROM `group_auth` AS ga , `auth_list` AS al WHERE ga.groupId  IN  
        ( SELECT group_id FROM `user_group_list` WHERE uid = :uid ) AND ga.authId = al.authId AND al.prid = :prid and al.isShow=:isShow ORDER BY al.sort ASC";

        $paramArr = array(
            ':uid'    => $uid,
            ':prid'   => $prid,
            ':isShow' => 1,
        );
        $userAuth = $this->pdo->findAll($sql, $paramArr);
        return $userAuth;
    }

    /**
     * User: panj
     * 用户是否属于某个组
     *
     * @param int $uid uid
     * @param int $groupId 组名Id
     *
     * @return array|bool
     */
    public function isOwnGroup($uid, $groupId)
    {
        $sql = " select uid, group_id from user_group_list where uid = :uid having group_id = :groupId";

        return $this->pdo->find($sql, [':uid' => $uid, ':groupId' => $groupId]);
    }

}
