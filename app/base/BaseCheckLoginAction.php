<?php


abstract class BaseCheckLoginAction extends Action
{
    protected $showCodeLimitCount;
    protected $IP;

    /**
     * BaseCheckLoginAction constructor.
     */
    public function __construct()
    {
        $this->showCodeLimitCount = 1;
        $this->IP = get_client_ip();
    }

    /**
     * setShowCodeLimitCount
     * -
     * @param int $errorCount ErrorCount
     * @return void
     * <AUTHOR>
     */
    public function setShowCodeLimitCount($errorCount)
    {
        $this->showCodeLimitCount = $errorCount;
    }

    /**
     * initFLoginInfo
     * -
     * @return mixed
     * <AUTHOR>
     */
    abstract public function initFLoginInfo();

    /**
     * getFLoginSessionInfo
     * -
     * @return mixed
     * <AUTHOR>
     */
    abstract public function getFLoginSessionInfo();
}
