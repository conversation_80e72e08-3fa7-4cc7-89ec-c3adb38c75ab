<?php
/**
 * Copyright (c) 2014,上海二三四五网络科技股份有限公司
 * 摘    要：MAS通道发送接口
 * 作    者：fansichi <<EMAIL>>
 * 修改日期：2014.01.08
 */

namespace Smschannel;
use Action;
use FuncAction;

 class MasSmsChannelAction extends SmsChannelAction
{
	public function __construct($user, $pwd, $apiUrl)
	{
		parent::__construct($user, $pwd, $apiUrl);
	}
	
	/**
	 * 发送短信
	 * @param string $phone 手机号
	 * @param string $msg   短信内容
	 * @return RET_SUCC 发送成功    RET_ERR 发送失败
	 */
	public function send($phone, $msg)
	{
		//MAS通道需要添加退订信息
		$msg .= ' 回复TD退订';
		$result = FuncAction::postTimeout($this->apiUrl, 
			array('Phone' => $phone, 'Content' => urlencode($msg), 'Sessionid' => ""));
        $this->SetMsgId('');
        $this->SetMsgArray($result);
		if($result == '1')
		{
			return self::RET_SUCC;
		} else
		{
			return self::RET_ERR;
		}	
	}
    
    private function SetMsgId ($msgId)
    {
        $this->msgId = $msgId;
    }
    
    private function SetMsgArray($result)
    {
        $this->OperatorMsg = array('codeStatus' => $result, 'codeDesc' =>$result);
    }
}
?>