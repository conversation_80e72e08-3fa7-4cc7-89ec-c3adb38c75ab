<?php

includeBase('CronController');

/**
 * 用户帐号操作
 */
class LogToFileFromRedisListController extends Controller
{
    protected $stream;
    protected $url;
    private $errorMessage;
    protected $filePermission;
    protected $useLocking;

    /**
     * LogToFileFromRedisListController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->filePermission = null;
        $this->useLocking = null;
    }

    /**
     * @author：dongx
     * @return void
     */
    public function actionIndex()
    {
        set_time_limit(0);
        $redis = RedisEx::getInstance();
        $sum = 1;
        $redisLen = $redis->lLen(LOG_REDIS_KEY);
        $redisLen = is_numeric($redisLen) ? $redisLen : 0;
        $redisNum = $redisLen * 2;
        $sleepTime = floor(100 + 10000 / ($redisNum + 1));
        while ($sum < $redisNum)
        {
            try
            {
                $logR = $redis->rPop(LOG_REDIS_KEY);
                if ($logR === false)
                {
                    sleep(1);
                    continue;
                }
                $array = unserialize($logR);
                if (!is_array($array))
                {
                    $array = json_decode($logR, true);
                }
                $this->write($array['url'] . 'redis', $array['content']);
            }
            catch (\Exception $exception)
            {
                $this->close();
                $redis = RedisEx::getInstance();
            }
            finally
            {
                usleep($sleepTime);
                $sum ++;

                if (mt_rand(1, 20) == 5)
                {
                    $this->close();
                    $redis = RedisEx::getInstance();
                }

            }
        }
        $this->close();
    }

    /**
     * 写入数据流
     *
     * @param    string $url    $url
     * @param  string   $record $record
     *
     * @author：dongx
     * @return void
     */
    protected function write($url, $record)
    {
        if (!$url)
        {
            throw new \LogicException(
                'Missing stream url, the stream can not be opened. This may be caused by a premature call to close().'
            );
        }
        $this->errorMessage = null;
        set_error_handler(array($this, 'customErrorHandler'));

        deepMkdir(substr($url, 0, strrpos($url, '/')));

        $this->stream = fopen($url, 'a');
        restore_error_handler();
        if (!is_resource($this->stream))
        {
            $this->stream = null;
            throw new \UnexpectedValueException(
                sprintf('The stream or file "%s" could not be opened: ' . $this->errorMessage, $this->url)
            );
        }

        if ($this->useLocking)
        {
            flock($this->stream, LOCK_EX);
        }
        fwrite($this->stream, $record);
        if ($this->useLocking)
        {
            flock($this->stream, LOCK_UN);
        }
    }

    /**
     * 关闭数据流
     *
     * @author：dongx
     * @return void
     */
    public function close()
    {
        if (is_resource($this->stream))
        {
            fclose($this->stream);
        }
        $this->stream = null;
        RedisEx::delInstance();
    }


    /**
     * 自定义错误操作
     *
     * @param type $code $code
     * @param type $msg  $msg
     */
    private function customErrorHandler($code, $msg)
    {
        $this->errorMessage = preg_replace('{^fopen\(.*?\): }', '', $msg);
    }


}
