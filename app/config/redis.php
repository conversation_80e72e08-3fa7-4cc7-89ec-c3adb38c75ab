<?php

/**
 * redis配置文件
 
 */
$config['redis'] = array(
    'default' => array(
        'master' => array(
/*env0015*/ 'host' => $_ENV['REDIS_MASTER_HOST'], //************
/*env0016*/ 'port' => $_ENV['REDIS_MASTER_PORT']
        ),
        'slave' => array(
/*env0017*/ 'host' => $_ENV['REDIS_MASTER_HOST'],//*************
/*env0018*/ 'port' => $_ENV['REDIS_MASTER_PORT']
        ),
/*env0019*/'auth' => $_ENV['REDIS_AUTH']
    ),
    // TODO 语音业务已废弃,待清理
    'YyRedis' => array(
        'master' => array(
            'host' => '*************', //************
            'port' => '6379'
        ),
        'slave' => array(
            'host' => '*************',//*************
            'port' => '6379'
        ),
        'auth' => 'rc_2345_redis'
    ),
    'test' => array(
        'master' => array(
/*env0020*/ 'host' => $_ENV['REDIS_MASTER_HOST'], //**************
/*env0021*/ 'port' => $_ENV['REDIS_MASTER_PORT']
        ),

/*env0022*/'auth' => $_ENV['REDIS_AUTH']
    )
);