<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/7/19
 * Time: 14:32
 */
use Octopus\PdoEx;

class UnionLoginLogModel extends Model
{
    private $table = 'union_login_log';

    /**
     * 添加联合登录日志
     * -
     * @param array $data 日志记录
     * @return bool|int
     */
    public function addUnionLoginLog($data)
    {
        $dbConfig = Config::get("database");
        $pdo = PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        $columns = [
            'passid' => 'passid',
            'mid' => 'mid',
            'old_mid' => 'old_mid',
            'request_time' => 'request_time',
            'login_ip' => 'login_ip',
            'login_status' => 'login_status',
            'offset_size' => 'offset_size',
            'add_time' => 'add_time',
        ];
        return $pdo->batch($this->table, $columns, $data);
    }

    /**
     * 删除DB实例
     * @return void
     */
    public function delInstance()
    {
        $dbConfig = Config::get("database");
        PdoEx::delInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
    }

    /**
     * 联合登录小时查询偏移量
     * -
     * @param string $startTime 查询起始时间
     * @return array|bool
     */
    public function getUnionLoginLog($startTime)
    {
        $dbConfig = Config::get("database");
        $pdo = PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        $sql = "SELECT offset_size FROM {$this->table} WHERE request_time >= :request_time  ORDER BY id DESC LIMIT 1";
        $params = [
            ':request_time' => $startTime,
        ];
        return $pdo->find($sql, $params);
    }
}
