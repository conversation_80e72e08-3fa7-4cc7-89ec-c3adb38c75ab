<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=GBK">
        <title>可疑注册IP</title>
        <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
        <link rel="stylesheet" href="/css/v3/top.css">
        <link rel="stylesheet" href="/css/v3/zhanghao.css">
        <script src="/js/jquery.min.js"></script>
    </head>
    <body>
    {{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
        
<div class="mainWrap">
    
		<!--面包屑导航 开始-->
        <div class="location">位置：<a href="#" target="_blank">登录注册IP管理</a>  &gt; <span>可疑登录IP</span></div>
        <!--面包屑导航 结束-->
        
        <!--Tab菜单 开始-->
        <div class="TabMenu mt10">
        	<a href="/houtai_gl/suspect/ips" >可疑登录IP</a>
            <a href="/houtai_gl/suspect/regips" class="currTabMenu">可疑注册IP</a>
            <a href="/houtai_gl/login_ip_log">最近登录IP日志</a>
            <a href="/houtai_gl/limit_ip/reg_limit_ip_list/black">注册IP黑名单管理</a>
            <a href="/houtai_gl/limit_ip/reg_limit_ip_list/white">注册IP白名单管理</a>
        </div>
        <!--Tab菜单 结束-->
    
        <div class="contentWrap">
            <!--table数据列表 开始-->            
            <table cellspacing="0" cellpadding="0" class="tabMod forFixed mt10">
                <tbody>
                	<tr>
                        <th>可疑注册IP</th>
                        <th>操作</th>
                    </tr>
                    {{foreach from=$pageArray.data item=vitem}}
                    <tr>
                        <td>{{$vitem}}</td>
                        <td><a href="/houtai_gl/suspect/regips?action=delete&ip={{$vitem}}">删除</a></td>
                    </tr>
                    {{/foreach}}
            	</tbody>
            </table>
            <!--table数据列表 结束-->        
        </div><!--contentWrap end-->
    
	</div>
    <script>
        $('.navList a').each(function (){
            if (  $(this).attr('href') == '/houtai_gl/suspect/ips' )
            {
                $(this).addClass('select');
            }
        });
    </script>
    </body>
</html>