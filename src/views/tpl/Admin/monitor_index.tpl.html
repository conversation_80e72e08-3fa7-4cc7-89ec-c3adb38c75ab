<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台</title>
    <link rel="stylesheet" href="/style/vue/element/element-ui.css">
</head>
<body>
<div id="app">
    <el-container>
        <el-header style="padding: 10px;margin:0px 20px; background-color: rgb(225,230,244);">
            <el-row>
                <el-col :span="16">
                    <el-breadcrumb separator-class="el-icon-arrow-right" style="margin-top: 12px;margin-left: 20px">
                        <el-breadcrumb-item>首页</el-breadcrumb-item>
                        <el-breadcrumb-item>发送量监控</el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
                <el-col :span="8">
                    <el-cascader
                            v-model="selectItem"
                            @change="handleSelectChange"
                            expand-trigger="hover"
                            style="width:100%"
                            placeholder="可搜索名称或数字"
                            :options="selectOption"
                            filterable
                    ></el-cascader>
                </el-col>
            </el-row>
        </el-header>
        <el-main style="padding-top: 5px">
            <el-row>
                <el-col :span="24">
                    <div id="echartOuterContainer">
                        <div id="echartContainer" style="height:100%;"></div>
                    </div>
                </el-col>
            </el-row>
        </el-main>
    </el-container>
</div>
</body>
<style>

</style>
<script src="/style/vue/vue[|if $smarty.const.RUNMODE eq 'production'|].min[|/if|].js"></script>
<script src="/style/vue/components/vue-resource[|if $smarty.const.RUNMODE eq 'production'|].min[|/if|].js"></script>
<script src="/style/vue/element/element-ui.js"></script>
<script src="/style/js/echarts.common.min.js"></script>
<!--<script src="/style/vue/global.js"></script>-->
<script>
    //原本global.js里面的内容 开始
    Date.prototype.Format = function (fmt) {
        var o = {
            "M+": this.getMonth() + 1, //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (const k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    };
    //原本global.js里面的内容 结束

    //默认application/json;改为x-www-form-urlencoded
    Vue.http.options.emulateJSON = true;
    let vm;
    window.onresize = function () {
        if (typeof(vm) !== 'undefined') {
            document.getElementById('echartOuterContainer').style.height = (window.innerHeight - 130) + 'px';
            vm.chart.resize();
        }
    };
    vm = new Vue({
        el: '#app',
        data: function () {
            return {
                chart: null,
                dateSet: [],
                updateDate: true,
                selectItem: ['all', 'all'],
                selectOption: [
                    {value: 'all', label: '全部', children: [{value: 'all', label: '全部'}]},
                    {value: 'api', label: '接口', children: [{value: 'send', label: '普通发送'}, {value: 'batchSend', label: '多点群发'}, {value: 'varSend', label: '变量群发'}]},
                    {value: 'type', label: '类型', children: []},
                    {value: 'proj', label: '项目', children: []},
                ],
                chartOption: {
                    title: {text: '', subtext: '当天、昨天、七天同期对比', itemGap: 0, left: 'center', textStyle: {fontSize: 15}},
                    tooltip: {trigger: 'axis',},
                    legend: {data: [], right: "4%", top: "12%", itemGap: 4, orient: "vertical"},
                    grid: {left: '0%', right: '6%', bottom: '7%', containLabel: true},
                    dataZoom: [{type: 'inside', start: 70, end: 100}, {type: 'slider', start: 0, end: 100,}],
                    xAxis: {type: 'category', boundaryGap: false, axisPointer: {label: {show: true}}},
                    yAxis: {type: 'value',},
                    series: [],
                    color: ['#eb0300', '#2875d6', '#dbac05', '#00be04', '#b001bc'],
                },
            }
        },
        methods: {
            handleSelectChange: function (e) {
                this.fillType(e[0], e[1])
            },
            formatDate: function () {
                this.dateSet = [];
                let date = new Date();
                date.setDate(date.getDate());
                this.dateSet.push(date.Format("yyyy-MM-dd hh:mm:ss"));
                date.setDate(date.getDate() - 1);
                this.dateSet.push(date.Format("yyyy-MM-dd hh:mm:ss"));
                date.setDate(date.getDate() - 7);
                this.dateSet.push(date.Format("yyyy-MM-dd hh:mm:ss"));
            },
            fillType: function (type, find) {
                if (this.updateDate) {
                    this.formatDate();
                }
                this.post('data', {
                    date: this.dateSet, type, find
                }).then((d => {
                    this.chart = echarts.init(document.getElementById('echartContainer'));
                    this.chartOption.series = [];
                    this.chartOption.legend.data = [];
                    for (let date of this.dateSet) {
                        date = date.substr(0, 10);
                        this.chartOption.series.push({
                            markLine: {data: [{name: '平均线', type: 'average'}]},
                            name: date,
                            type: 'line',
                            data: d[date]
                        });
                        this.chartOption.legend.data.push(date);
                        this.chartOption.title.text = '短信平台发送量统计：' + type + '-' + find;
                    }
                    this.chart.setOption(this.chartOption);
                }), (err) => alert(err));
            },
            fillSelect: function () {
                this.post('typeList', {}).then(
                    d => {
                        this.selectOption[2].children = d
                    }, (err) => alert(err)
                );
                this.post('projectList', {}).then(
                    d => {
                        this.selectOption[3].children = d
                    }, (err) => alert(err)
                );
            },
            post: function ($func, $postArr) {
                return new Promise((resolve, reject) => {
                    this.$http.post('/Admin/Monitor/Api?func=' + $func, $postArr).then(function (res) {
                        if (res.data.err === 0) {
                            //确保UTF-8可以正确在GBK编码文档中显示
                            resolve(JSON.parse(res.data.data));
                        } else if (res.data.msg) {
                            reject(res.data.msg)
                        } else {
                            reject(res.data.err)
                        }
                    }, function (res) {
                        reject(res.status)
                    });
                });
            }
        },
        mounted: function () {
            this.fillSelect();
            this.fillType('all', 'all');
            document.getElementById('echartOuterContainer').style.height = (window.innerHeight - 130) + 'px';
        }
    });
</script>
</html>
