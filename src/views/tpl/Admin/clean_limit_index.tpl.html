<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务平台-取消短信日限</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
  <script src="/style/js/tableFixed.js"></script>
  <script src="/style/js/boxLocation.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a>  >  <span>取消短信日限</span></div>
    <!--面包屑导航 结束-->
    
    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="javascript:">取消短信日限</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    
    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
        <form action="" method="post" id="clearLimitFromId">
            <span class="input-sp w130"><input type="text" autocomplete="off" name="phone" value="{{$pageArray.phone}}"/></span>
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10" onclick="$('#clearLimitFromId').submit()">查询</a>
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>手机号码</th>
                        <th>短信类型</th>
                        <th>条数</th>
                        <th>操作</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th>手机号码</th>
                <th>短信类型</th>
                <th>条数</th>
                <th>操作</th>
            </tr>
            {{foreach from=$pageArray.redisArr item=redisInfo}}
            <tr>
                <td>{{$redisInfo.phone}}</td>
                <td>{{$redisInfo.smsTypeName}}</td>
                <td>{{$redisInfo.nums}}</td>
                <td>
                    <a href="javascript:void(0);" keyword="{{$redisInfo.queryKey}}" class="clearTodayNums">清除</a> | <a href="javascript:void(0);" phone="{{$redisInfo.phone}}" smsType="{{$redisInfo.smsType}}" class="lookProjectSendTotal">查看</a>
                </td>
                
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
        
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
    
    
    	<!--透明遮罩-->
    <div class="boxMask" style="display: none;">透明遮罩层</div>
 <!--添加权限 弹窗 开始-->
	<div class="boxMod w520 sendTotalClass">
    	<div class="boxTop"><h2 class="boxTit">查看项目发送量</h2><a class="BoxClose" href="javascript:">关闭按钮</a></div>
        <div id="showProjecList">
            <table class="tabFromMod" cellpadding="0" cellspacing="0">
            </table>
        </div><!--tabFrom end-->
    </div><!--boxMod end-->
    <!--添加权限 弹窗 结束-->
    
    
</div><!--mainWrap end-->
<script>
$('.clearTodayNums').click(function (){
    if( confirm('确认是否真的清空？') )
    {
        var keyword = $(this).attr('keyword');
        $.post('/Admin/ClearLimit/ClearTodayLimit','keyword='+keyword,function (data){
            alert(data.msg);
            if (data.status == 'P00001')
            {
                window.location.reload();
            }
        },'json');
    }
});

$('.lookProjectSendTotal').click(function (){
    var phone = $(this).attr('phone');
    var smsType = $(this).attr('smsType');
    $.post('/Admin/ClearLimit/GetProjectSendTotal','phone='+phone+'&smsType='+smsType,function (data){
        $('.boxMask').show();
        $('.sendTotalClass').show();
        console.debug(data);
        $('#showProjecList').html(data.html);
    },'json');
});
$('.BoxClose').click(function (){
    $('.boxMask').hide();
    $('.boxMod').hide();
});
</script>
</body>
</html>