<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：JvChenCallbackController.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：11/09/2018 10:13
 */

namespace SmsInterface;

use Controller;

class JvChenCallbackController extends Controller
{
    protected $logFile;

    /**
     * JvChenCallbackController constructor.
     */
    public function __construct()
    {
        $isAccess = \ChannelIpWhiteList::isAccessAuthChannel('jvchen');

        if (!$isAccess)
        {
            header('HTTP/1.1 401 Unauthorized ');
            exit;
        }
    }

    /**
     * User: panj
     *
     * @param string $info info
     * @return null
     */
    public function setLog($info)
    {
        return;
        $logPath = APPPATH . '/logs/' . date('Ymd');
        if (!is_dir($logPath))
        {
            mkdir($logPath, 0777, true);
        }
        $this->logFile = $logPath . '/jvchen.log';
        $info = date('Y-m-d H:i:s') . '  ' . $info . PHP_EOL;
        file_put_contents($this->logFile, $info, FILE_APPEND);
    }

    /**
     * User: panj
     * @return null
     */
    public function actionReceiveReport()
    {
        $respJson = $this->Filter->post('report', true);
        $respArr = json_decode($respJson, true);
        $isOK = false;
        if ($respArr)
        {
            $reports = $respArr['reports'];
            if (!empty($reports))
            {
                $redisAction = \RedisAction::connect();
                foreach ($reports as $report)
                {
                    $params = array(
                        "Msg_Id"        => trim($report['batchno']),
                        "Mobile"        => trim($report['phone']),
                        "OriginStatus"  => trim($report['status']),
                        "Status"        => trim($report['status']),
                        "Callback_Time" => trim($report['recvtime']),
                    );
                    $isOK = $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($params));
                }
            }
        }

        //成功推送以接口使用方返回{"result":0}来判定
        $isOK ? die(json_encode(['result' => 0])) : die('error');
    }
}
