<?php
/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 摘    要：MemCache数据缓存键值获取，常量名和值保持一致，便于发现重名
 * 作    者：杜海明
 * 修改日期：2016.02.14
 */

class Config_CacheKey
{	
     /**
     * 短信配置的缓存
     */
    const SMS_TYPE_CONFIG = 'sms_type_config_';
     /**
     * 短信类型的缓存
     */
    const SMS_PROJECT_TYPE = 'sms_project_type_';
   
    /**
     * 短信黑名单的缓存
     */
    const SMS_PHONE_BLACK = 'sms_phone_black_';
    
    /**
     * 短信手机白名单的缓存 white
     */
    const SMS_PHONE_WHITE = 'sms_phone_white_';
       
    /**
     * 项目ip白名单列表的缓存
     */
    const SMS_IP_WHITE_LIST = 'sms_ip_white_list_';
    

    /**
     * 手机号 短信类型 短信数量
     */
    const SMS_SEND_PHONE_TYPE_NUMS = 'sms_send_phone_type_nums_';
    
    const CHANNEL_CALLBACK_STATUS_NEW = 'channelCallbackStatusNew';

    const MONITOR_LIMIT_RATE = 'monitor_limit_rate';

    const DATA_ARCHIVE_STATUS = 'DataArchiveStatus';
    /**
     * 短信统计缓存
     */
    const SMS_ADDUP_CACHE = 'sms_addup_cache:';

    /**
     * BI系统短信日统计数据，有效期7天
     */
    const BI_DATA_STATISTIC_DAILY = 'bi_data_statistic_daily';

    /**
     * BI系统统计数据相关项目服务商账号
     */
    const BI_DATA_SMS_PROVIDER_ACCOUNT = 'bi_data_sms_provider_account';

    /**
     * BI系统统计数据record id
     */
    const BI_DATA_RECODE_ID_LAST = 'bi_data_recode_id_last';
}
