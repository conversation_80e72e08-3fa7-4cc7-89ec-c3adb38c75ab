{{include file="header.tpl.html"}}
<div class="wrap">
	<div class="main clearfix">
		<div class="box mt10">
			<div class="tit clearfix">
				<span class="titleft"></span>
				<span class="titright"></span>
				<h2><a href="edit_info.php">个人资料</a> > 修改绑定邮箱</h2>
			</div>
			<div class="boxbd checkemail clearfix">
				<p class="step" style="background:url(/images/stepbg0.png) no-repeat;">
					<span class="st_1" style="color:#fff;">1.验证身份</span>
                    <span class="st_2" style="color:#666;">2.输入新邮箱地址</span>
                    <span class="st_3">3.发送邮箱验证码</span>
                    <span class="st_4">4.完成</span>
				</p>
				<div class="checkemailLeft">
					<form action="" method="post"{{if !$pageArray.isBindPhone}} onsubmit="return checkForm();"{{/if}}>
						  <input type="hidden" name="cmd" value="choice"/>
						<b>选择验证码获取方式：</b>
						<ul>
							<li>
								<label><input id="from_shouji" name="from" type="radio" value="shouji"{{if $pageArray.isBindPhone}} checked="checked"{{/if}}>以手机方式，获取验证码</label>
								{{if !$pageArray.isBindPhone}}
								<p class="w_er" id="shouji_err" style="display:none;">手机未绑定，请选择绑定邮箱方式或客服方式来修改邮箱<br/><a href="http://jifen.yl234.com/user/account.php?act=bind_phone" target="_blank">点击此处绑定手机</a></p>
								{{/if}}
							</li>
							<li>
								<label><input id="from_email" name="from" type="radio" value="email"{{if !$pageArray.isBindPhone}} checked="checked"{{/if}}>以绑定邮箱方式，获取验证码</label>
							</li>
						</ul>
						<input type="submit" onmouseout="this.className='btn4'" onmouseover="this.className='btn4_hov'" class="btn4" value="提交"/>
					</form>
					{{if !$pageArray.isBindPhone}}
					<script type="text/javascript">
						document.getElementById("from_shouji").onclick = function(){
							if(this.checked){
								document.getElementById("shouji_err").style.display = "";
							}
						};
						document.getElementById("from_email").onclick = function(){
							if(this.checked){
								document.getElementById("shouji_err").style.display = "none";
							}
						};
						function checkForm(){
							if(document.getElementById("from_shouji").checked){
								alert("手机未绑定，请选择绑定邮箱方式或客服方式来修改邮箱");
								return false;
							}else{
								return true;
							}
						}
					</script>
					{{/if}}
				</div>
				<div class="checkemailRight"><b>客服帮助</b>
					<p>电话：************</p>
					<p>QQ：  100689991</p>
					<p>邮箱：<a href="mailto:<EMAIL>"><EMAIL></a></p>
				</div>
			</div>           	
		</div>
	</div> 
</div>
{{include file="footer.tpl.html"}}
