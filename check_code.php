<?php
/*********************************************************
author			Jay
date			2005-6-30
function		51.com
email			<EMAIL>
*********************************************************/
//---------------------------------------------------------------------------------
//生成图形识别码
$str = 'abcdefghijklmnpqrstuvwxyz123456789';

$this_code = '';
for ($i = 0; $i < 4; $i++)
{
	$this_code .= $str[rand(0, strlen($str) - 1)];
}
session_start();
$_SESSION['checkIMGCode'] = $this_code;

$im = @imagecreate (55, 25) or die ("Cannot Initialize new GD image stream");
$background_color = imagecolorallocate ($im, 0, 121, 181);
$text_color = imagecolorallocate ($im, 255, 255, 255);


$tmp_y = rand( -3, 3 );
imagestring ($im, 5, rand(5,12), 4+$tmp_y, $this_code[0], $text_color);
$tmp_y = rand( -3, 3 );
imagestring ($im, 5, rand(15,22), 4+$tmp_y, $this_code[1], $text_color);
$tmp_y = rand( -3, 3 );
imagestring ($im, 5, rand(25,32), 4+$tmp_y, $this_code[2], $text_color);
$tmp_y = rand( -3, 3 );
imagestring ($im, 5, rand(35,42), 4+$tmp_y, $this_code[3], $text_color);

for( $i = 0; $i < 60; $i++ )
{
	imagesetpixel ( $im, rand(0,55), rand(0,25), $text_color);
}
imagepng ($im);
imagedestroy ($im);
