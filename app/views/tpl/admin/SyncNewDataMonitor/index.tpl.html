<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="GBK">
    <title></title>
    <link rel="stylesheet" media="all" type="text/css" href="/css/redmin/bootstrap/bootstrap.min.css">
    <!--<script src="/js/jquery.min.js"></script>-->
    <script src="/js/vue/vue.min.js"></script>
    <script src="/js/vue/vue-resource.min.js"></script>
</head>
<body>
<div class="jumbotron" id="indexForm">
    <div class="container">

        {{include file = "admin/SyncNewDataMonitor/nav.tpl.html"}}


        <div class="panel panel-default">
            <div class="panel-body">
                <p>当前队列长度： {{$pageArray.listLen}}</p>
                <table class="table table-striped">
                    <thead>
                    <tr>
                        <th>passid</th>
                        <th>更新时间</th>
                        <th>重试次数</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tr v-for="(n,index) in dataList">
                        <td><a :href="'/houtai_gl/SyncNewDataMonitor/diff?passid='+n.passid" target="_blank">${n.passid}</a></td>
                        <td>${n.time}</td>
                        <td>${n.num}</td>
                        <td>
                            <button class="btn btn-primary" v-on:click="diffClick(n.passid)">差异</button>
                            <button class="btn btn-primary" v-on:click="updateSyncData(n.passid)">更新</button>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
<script>
    var dataList = {{$pageArray.dataList}};
    var app = new Vue({
        el: "#indexForm",
        delimiters: ['${', '}'],
        data: {
            'dataList' : dataList,
        },
        methods: {
            diffClick : function (passid) {
                var url = '/houtai_gl/SyncNewDataMonitor/diff?passid=' + passid;
                window.open(url)
            },
            updateSyncData : function (passid) {
                var url = '/houtai_gl/SyncNewDataMonitor/UpdateSyncData?passid=' + passid;
                window.open(url)
            }
        }
    });
</script>
</body>
</html>