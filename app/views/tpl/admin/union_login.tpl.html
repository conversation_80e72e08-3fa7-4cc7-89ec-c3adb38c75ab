<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="gbk">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>联合登录管理</title>

    <!-- Bootstrap -->
    <link href="/css/UnionLogin/bootstrap.min.css" rel="stylesheet">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="/js/houtai_gl/unionLogin/html5shiv.min.js"></script>
    <script src="/js/houtai_gl/unionLogin/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<div class="container">
    <div class="row">
        <h1 class="text-center">联合登录管理</h1>
    </div>

    <div class="row">
        <div class="col-md-12 text-right">
            <button type="button" class="btn btn-success" onclick="location.href='/houtai_gl/UnionLogin/add'"
                    aria-label="Left Align">
                增加 <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
            </button>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <table class="table table-hover">
                <tr>
                    <th>项目名称</th>
                    <th>项目标识</th>
                    <th>所属包名</th>
                    <th>联合登录关联</th>
                    <th>操作</th>
                </tr>
                {{foreach from=$pageArray.list item=vitem key=key}}
                <tr>

                    <td>{{$vitem.name|mb_convert_encoding:gbk}}</td>
                    <td>{{$vitem.mid}}</td>
                    <td>{{$vitem.package_name}}</td>
                    <td>{{$vitem.relationshipName}}</td>
                    <td>
                        <a type="button" class="btn btn-link" href="/houtai_gl/UnionLogin/add?id={{$vitem.id}}">修改</a>
                        <button type="button" class="btn btn-link" onclick="del({{$vitem.id}})">删除</button>
                    </td>
                </tr>
                {{/foreach}}

                <!--<tr>-->
                <!--<td colspan="5" class="text-center">-->
                <!--<nav aria-label="Page navigation">-->
                <!--<ul class="pagination">-->
                <!--<li>-->
                <!--<a href="#" aria-label="Previous">-->
                <!--<span aria-hidden="true">&laquo;</span>-->
                <!--</a>-->
                <!--</li>-->
                <!--<li><a href="#">1</a></li>-->
                <!--<li><a href="#">2</a></li>-->
                <!--<li><a href="#">3</a></li>-->
                <!--<li><a href="#">4</a></li>-->
                <!--<li><a href="#">5</a></li>-->
                <!--<li>-->
                <!--<a href="#" aria-label="Next">-->
                <!--<span aria-hidden="true">&raquo;</span>-->
                <!--</a>-->
                <!--</li>-->
                <!--</ul>-->
                <!--</nav>-->
                <!--</td>-->
                <!--</tr>-->
            </table>
        </div>
    </div>

</div>


<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="/js/houtai_gl/unionLogin/jquery.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="/js/houtai_gl/unionLogin/bootstrap.min.js"></script>
<script>

    function del(id) {
        if (confirm('确定要删除吗?')) {
            location.href = '/houtai_gl/UnionLogin/Delete?id=' + id
        }
    }
</script>
</body>
</html>