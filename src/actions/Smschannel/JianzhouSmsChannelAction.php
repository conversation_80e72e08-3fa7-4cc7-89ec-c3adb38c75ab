<?php

/**
 * Copyright (c) 2014,上海二三四五网络科技股份有限公司
 * 摘    要：建州通道发送接口（点对点的接口 http://www.jianzhou.sh.cn:8080/JianzhouSMSWSServer/services/BusinessService?wsdl
 *  和 批量发送 http://www.jianzhou.sh.cn/JianzhouSMSWSServer/services/BusinessService?wsdl）
 * 作    者：fansichi <<EMAIL>>
 * 修改日期：2014.01.08
 */

namespace Smschannel;

use Action;

class JianzhouSmsChannelAction extends SmsChannelAction
{
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }

    /**
     * 发送短信，适用
     *
     * @param string $phone 手机号
     * @param string $msg   短信内容
     * @param string $sign  签名
     *
     * @return RET_SUCC 发送成功    RET_ERR 发送失败
     */
    public function send($phone, $msg, $sign)
    {
        require_once(SRCPATH . '/includes/nusoap_lib/nusoap.php');
        $omsg = $msg; //用于打印
        $msg = iconv('GBK', 'UTF-8//IGNORE', $msg . $sign);
        $client = new \nusoap_client($this->apiUrl, true);
        $client->soap_defencoding = 'utf-8';
        $client->decode_utf8 = false;
        $client->xml_encoding = 'utf-8';
        $err = $client->getError();
        if ($err)
        {
            return self::RET_ERR;
        }
        else
        {
            $params = array(
                'account'    => $this->user,
                'password'   => $this->pwd,
                'destmobile' => $phone,
                'msgText'    => $msg,
            );

            $result = $client->call('sendBatchMessage', $params, $this->apiUrl);
            if ($client->fault)
            {
                return self::RET_ERR;
            }
            else
            {
                $err = $client->getError();
                if ($err)
                {
                    return self::RET_ERR;
                }
                else
                {
                    $response_text = $result['sendBatchMessageReturn'];
                    $this->SetMsgArray($response_text);
                    if ($response_text > 0)
                    {
                        $this->SetMsgId($response_text);

                        return self::RET_SUCC;
                    }
                    else
                    {
                        return self::RET_ERR;
                    }
                }
            }
        }
    }

    private function SetMsgId($msgId)
    {
        $this->msgId = $msgId;
    }

    private function SetMsgArray($rspcode)
    {
        $code = array(
            -1  => '余额不足',
            -2  => '帐号或密码错误',
            -3  => '连接服务商失败',
            -4  => '超时',
            -5  => '其他错误，一般为网络问题，IP受限等',
            -6  => '短信内容为空',
            -7  => '目标号码为空',
            -8  => '用户通道设置不对，需要设置三个通道',
            -9  => '捕获未知异常',
            -10 => '超过最大定时时间限制',
            -11 => '目标号码在黑名单里',
            -13 => '没有权限使用该网关',
            -14 => '找不到对应的Channel ID',
            -17 => '没有提交权限，客户端帐号无法使用接口提交',
            -20 => '超速提交(一般为每秒一次提交)',
            -21 => '扩展参数不正确',
            -22 => 'Ip 被封停',
        );
        if ($rspcode > 0)
        {
            $code[1] = '成功';
            $rspcode = 1;
        }
        $this->OperatorMsg = array('codeStatus' => $rspcode, 'codeDesc' => !empty($code[$rspcode]) ? $code[$rspcode] : '未知错误',);
    }
}
