<?php

includeBase('CronController');

/**
 * 发送短信
 */
class HappyNewYearSendMessageController extends Controller
{
    private $pdo = null;
    private $redis = null;
    private $userTable = 'happy_new_year_user';
    private $ticketTable = 'happy_new_year_ticket';


    /**
     * HappyNewYearSendMessageController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->pdo = PdoEx::getInstance(DB_MY_2345);
        $this->redis = RedisEx::getInstance();
    }

    /**
     * @author：dongx
     * @return void
     */
    public function actionIndex()
    {
        $ret = $this->pdo->findAll(
            "SELECT * FROM $this->ticketTable WHERE luckly_type = :luckly_type and isGet = :isGet and is_send=:is_send and send_time<='" . date(
                'Y-m-d H:i:s'
            ) . "' limit 10",
            array(
                ":luckly_type" => 5,
                ":isGet"       => 1,
                ":is_send"     => 0,
            ),
            false
        );
        if (is_array($ret) && count($ret) > 0)
        {
            foreach ($ret as $k => $v)
            {
                $params = array(
                    'phone'      => $v['send_phone'], // 手机号码
                    'msg'        => $v['send_msg'], // 短信内容
                    'smsType'    => 7, // 短信类别（固定值，1为验证码类，2为通知类）
                    'pid'        => 14,
                    'clientIp'   => $v['send_ip'],
                    'positionId' => 323,
                    'mid'        => 'login',
                );

                $info = json_decode(http_post(SMS_CENTER_DOMAIN . "/Api/Sms/Send", $params), true);
                $status = ($info['status'] == 1 ? 'success' : 'error');
                $content = 'id:' . $v['id'] . '  status:' . $status . ' 回执:' . iconv('utf-8', 'gbk', $info['msg']) . ' data:' . json_encode($info['data']);
                if ($info['status'] == '1')
                {
                    xLog('happyNewYear', 'happyNewYear', $content);
                    $sendMsg = $this->pdo->query(
                        "UPDATE $this->ticketTable SET is_send =:is_send WHERE id = :id",
                        array(
                            ":is_send" => 1,
                            ":id"      => $v['id'],
                        ),
                        false
                    );
                    xLog('happyNewYear', 'happyNewYear_exec', $sendMsg);
                }
                else
                {
                    xLog('happyNewYear', 'happyNewYear', $content, 'notice');
                }
            }
        }
    }

}
