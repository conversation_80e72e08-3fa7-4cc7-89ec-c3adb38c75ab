<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gbk" />
<title>管理中心-2345网址导航高级版</title>

<link href="css/gaoji.css" rel="stylesheet" type="text/css" />
</head>

<body>

<!-- wrap -->
<div class="wrap">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td width="200" class="nav_border">
	<div class="left_nav">
    {{include file="admin_gjb2345/menu.html"}}
    </div></td>
    <td><div class="right_cnt">

<style>
*,body{ font-size:12px; }
.mytable{ background-color:#9cc; padding:0 0 0 0; text-align:center; padding:1px 1px 1px 1px;}
.mytable td {background-color:#FFF; padding:1px 1px 1px 1px;}
</style>
    <h4>回收站</h4>
    <br />

	<div style="width:100%; margin-left:30px; border:1px solid #CCC;">
		<form>
			<table border="0" width="80%">
				<tr align="right">
					<td>
						<select name="type">
							<option value="">类别</option>
							<option value="0" {{if $smarty.get.type === "0"}}selected{{/if}}>通知</option>
							<option value="1" {{if $smarty.get.type === "1"}}selected{{/if}}>公告</option>
						</select>
						<input type="text" name="search" value="{{$smarty.get.search}}" />
						<input type="submit" value="搜索" />
					</td>
				</tr>
			</table>
		</form>
		<table class="mytable" border="0" cellspacing="1" cellpadding="0" width="80%">
		  <tr>
		  	<th></th>
		  	<th>用户名</th>
		  	<th>类别</th>
			<th>标题</th>
			<th>状态</th>
			<th>时间</th>
		  </tr>
{{foreach key=key item=item from=$pageArray.list}}
		  <tr>
		  	<td><input type="checkbox" name="del" value="{{$item.id}}" /></td>
		  	<td>{{$item.uname}}</td>
		  	<td>{{if $item.atype eq 1}}公告{{else}}通知{{/if}}</td>
			<td>{{$item.title}}</td>
			<td>{{if $item.atype eq 0}}{{if $item.new eq 1}}未读{{else}}已读{{/if}} / {{/if}}<a href="javascript:;" onclick="recover({{$item.id}})">恢复</a></td>
			<td>{{$item.ctime}}</td>
		  </tr>
{{/foreach}}
		</table>
		&nbsp;&nbsp;&nbsp;<input type="submit" value="彻底删除" onclick="batch()" />
	</div>
{{include file="admin_gjb2345/footer.html"}}
<script type="text/javascript">
function del(id)
{
	if (confirm('确定删除'))
	{
		location.href = '{{$smarty.server.PHP_SELF}}?search={{$smarty.get.search}}&del='+id;
	}
}

function batch()
{
	all = document.getElementsByName('del');
	delval = '';
	for (i = 0; i < all.length; i++)
	{
		if (all[i].checked)
		{
			delval += all[i].value + ',';
		}
	}
	if (delval.length > 0)
	{
		delval = delval.substring(0, delval.length - 1);
		del(delval);
	}
}

function recover(id)
{
	if (confirm('确定恢复'))
	{
		location.href = '{{$smarty.server.PHP_SELF}}?type={{$smarty.get.type}}&search={{$smarty.get.search}}&recover='+id;
	}
}
</script>