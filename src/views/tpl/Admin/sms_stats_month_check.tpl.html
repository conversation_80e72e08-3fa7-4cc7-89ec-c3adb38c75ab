<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信通道-月度短信对账</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信通道</a>  >  <span>月度短信对账</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="/Admin/SmsStatsMonth/Index">月度短信对账</a>
    </div>
    <!--Tab菜单 结束-->

    <!--日期表单 开始-->
    <div class="funList mt20 clearfix">
        <form action="/Admin/SmsStatsMonth/Index" method="post" id="queryForm">
            <div class="clearfix">
                <div class="dataWrap clearfix">
                    <em>账单月份：</em>
                    <div class="dataList w130">
                        <div class="getTime"><input type="text" value="{{$pageArray.show_month}}" autocomplete="off" name="show_month" onclick="WdatePicker({dateFmt: 'yyyy-MM', minDate: '{{$pageArray.min_month}}', maxDate: '{{$pageArray.max_month}}'})" disabled="disabled"/><i>日历icon</i></div>
                    </div>
                </div>
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 checkData" onclick="checkData(1);">检查数据</a>
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml20 saveData">保存结果</a>
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml20 exitCheck" style="background:#ffa14e;">退出校对</a>
            </div>
        </form>
    </div>
    <!--日期表单 结束-->

    <!--分割 开始-->
    <div class="mt20" style="height:30px;background-color:#d6e4f7;color:#4c6180;font-weight: bold;vertical-align: inherit;line-height:  30px;padding-left: 10px;">
        对账配置
    </div>
    <!--分割 结束-->

    <!--配置列表 开始-->
    <div class="contentWrap" style="overflow-x: scroll;">
        <table class="tabMod forFixed" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
            <tr>
                <th>月份</th>
                <th>服务商</th>
                <th>结算条数</th>
                <th>结算单价</th>
                <th>结算总价</th>
                <th>操作人员</th>
                <th>操作时间</th>
            </tr>
            {{foreach from=$pageArray.provider key=cate item=name}}
            <tr class="config_tr config_tr_cate{{$cate}}">
                <td>
                    {{$pageArray.month}}
                    <input type="hidden" class="config_month" value="{{$pageArray.month}}">
                </td>
                <td>
                    {{$cate}}-{{$name}}
                    <input type="hidden" class="config_cate" value="{{$cate}}">
                    <input type="hidden" class="config_name" value="{{$name}}">
                    <input type="hidden" class="config_id" value="{{$pageArray.config.$cate['id']}}">
                </td>
                <td>
                    <input type="text" class="config_settle_count config_settle_count_cate_{{$cate}}" value="{{$pageArray.config.$cate['settle_count']}}" style="width:100px; text-align:center" onchange="changeConfig({{$cate}}, 1);">
                </td>
                <td>
                    <input type="text" class="config_price config_price_cate_{{$cate}}" value="{{$pageArray.config.$cate['price']}}" style="width:100px; text-align:center" onchange="changeConfig({{$cate}}, 2);">
                </td>
                <td class="config_cost config_cost_cate_{{$cate}}">
                    {{$pageArray.config.$cate['cost']}}
                </td>
                <td>{{$pageArray.config.$cate['operator']}}</td>
                <td>{{$pageArray.config.$cate['mtime']}}</td>
            </tr>
            {{/foreach}}
        </table>
    </div>
    <!--配置列表 end-->

    {{foreach from=$pageArray.data key=cate item=list}}
    {{if $list}}
    <!--分割 开始-->
    <div class="mt20" style="height:30px;background-color:#d6e4f7;color:#4c6180;font-weight: bold;vertical-align: inherit;line-height:  30px;padding-left: 10px;">
        {{$cate}}-{{if isset($pageArray.provider[$cate])}}{{$pageArray.provider[$cate]}}{{else}}未知服务商{{/if}}&nbsp;&nbsp;数据
    </div>
    <!--分割 结束-->

    <!--数据列表 开始-->
    <div class="contentWrap" style="overflow-x: scroll;">
        <table class="tabMod forFixed" cellpadding="0" cellspacing="0" style="table-layout:fixed;">
            <tr>
                <th>月份</th>
                <th>OA项目</th>
                <th>服务商</th>
                <th>发送条数</th>
                <th>计费条数</th>
                <th>结算条数</th>
                <th>结算单价</th>
                <th>结算总价</th>
                <th>校对状态</th>
                <th>备注</th>
                <th>操作人员</th>
                <th>操作时间</th>
            </tr>
            {{foreach from=$list item=info}}
            <tr class="data_tr data_tr_cate_{{$cate}} data_tr_id_{{$info.id}}">
                <td>{{$info.month}}</td>
                <td>{{$info.oa_project_id}}-{{$info.oa_project_name}}</td>
                <td>{{if isset($pageArray.provider[$info.provider_cate])}}{{$pageArray.provider[$info.provider_cate]}}{{else}}未知服务商{{/if}}</td>
                <td>{{$info.send_count}}</td>
                <td>{{$info.charge_count}}</td>
                <td>
                    <input type="text" class="data_settle_count data_settle_count_id_{{$info.id}}" value="{{$info.settle_count}}" style="width:100px; text-align:center" onchange="changeData({{$cate}});">
                    <input type="hidden" class="data_id" value="{{$info.id}}">
                    <input type="hidden" class="data_cate" value="{{$cate}}">
                </td>
                <td class="data_price data_price_cate_{{$cate}}">
                    {{$info.price}}
                </td>
                <td class="data_cost data_cost_id_{{$info.id}}">
                    {{$info.cost}}
                </td>
                <td>{{if $info.status == 1}}<font color="green"><b>校对完成</b></font>{{else if $info.status == 0}}<font color="red"><b>未校对</b></font>{{else}}<font color="#8b0000"><b>未知</b></font>{{/if}}</td>
                <td>
                    <input type="text" class="data_remark data_remark_id_{{$info.id}}" value="{{$info.remark}}" style="width:100px; text-align:center">
                </td>
                <td>{{$info.operator}}</td>
                <td>{{$info.mtime}}</td>
            </tr>
            {{/foreach}}
            <tr class="total_tr total_tr_{{$cate}}">
                <td></td>
                <td>汇总</td>
                <td></td>
                <td>{{$pageArray.total[$cate]['send_count']}}</td>
                <td>{{$pageArray.total[$cate]['charge_count']}}</td>
                <td class="total_settle_count_cate_{{$cate}}">
                    {{$pageArray.total[$cate]['settle_count']}}
                </td>
                <td class="total_price_cate_{{$cate}}">
                    {{$pageArray.total[$cate]['price']}}
                </td>
                <td class="total_cost_cate_{{$cate}}">
                    {{$pageArray.total[$cate]['cost']}}
                </td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
        </table>
    </div>
    <!--数据列表 end-->
    {{/if}}
    {{/foreach}}

</div>
<script src="/style/js/jquery-1.8.3.min.js"></script>
<script src="/style/js/tableFixed.js"></script>
<script>
    function changeConfig(cate, type) {
        var price = parseInt($('.config_price_cate_' + cate).val() * 1000);
        if (isNaN(price)) {
            price = 0;
        }
        $('.config_price_cate_' + cate).val((price / 1000).toFixed(3));

        //处理对账配置数据变化
        var settle_count = parseInt($('.config_settle_count_cate_' + cate).val());
        if (isNaN(settle_count)) {
            settle_count = 0;
        }
        $('.config_settle_count_cate_' + cate).val(settle_count);

        var cost = (price * settle_count / 1000).toFixed(3);
        $('.config_cost_cate_' + cate).html(cost);

        //处理服务商数据变化
        if (type == 2) {
            changeData(cate);
        }
    }

    function changeData(cate) {
        var price = parseInt($('.config_price_cate_' + cate).val() * 1000);
        if (isNaN(price)) {
            price = 0;
        }
        $('.data_price_cate_' + cate).html((price / 1000).toFixed(3));

        var total_settle_count = 0;
        $.each($('.data_tr_cate_' + cate), function (index, value, array) {
            var settle_count = parseInt($(value).find('.data_settle_count').val());
            if (isNaN(settle_count)) {
                settle_count = 0;
            }
            $(value).find('.data_settle_count').val(settle_count);
            total_settle_count += settle_count;
            var cost = (price * settle_count / 1000).toFixed(3);
            $(value).find('.data_cost').html(cost)
        });

        $('.total_price_cate_' + cate).html((price / 1000).toFixed(3));
        $('.total_settle_count_cate_' + cate).html(total_settle_count);
        $('.total_cost_cate_' + cate).html((total_settle_count * price / 1000).toFixed(3));
    }

    function checkData(type) {
        var error = false;
        $.each($('.config_tr'), function (index, value, array) {
            var cate = parseInt($(value).find('.config_cate').val());
            var name = $(value).find('.config_name').val();

            var config_settle_count = $(value).find('.config_settle_count').val();
            if (isNaN(config_settle_count) || (config_settle_count + '') != (parseInt(config_settle_count) + '') || config_settle_count < 0) {
                error = true;
                alert('检查未通过：请正确填写 ' + name + ' 服务商的结算条数');
                return false;
            }
            config_settle_count = parseInt(config_settle_count);

            var price = $(value).find('.config_price').val();
            if (price == '' || isNaN(price) || (price * 1000 + '') != (parseInt(price * 1000) + '') || price < 0) {
                error = true;
                alert('检查未通过：请正确填写 ' + name + ' 服务商的结算单价');
                return false;
            }

            changeConfig(cate, 2);

            var total_settle_count = parseInt($('.total_settle_count_cate_' + cate).html());
            if (isNaN(total_settle_count)) {
                total_settle_count = 0;
            }
            if (total_settle_count != config_settle_count) {
                error = true;
                alert('检查未通过：' + name + ' 服务商的结算条数不符，请进行调整');
                return false;
            }
        });
        if(type == 1 && !error) {
            alert('检查通过');
        }

        return error;
    }

    $('.saveData').click(function () {
        if (checkData(2)) {
            return false;
        }

        //构造post数据
        var post_data = {'config': {}, 'data': {}};

        //构造配置数据
        $.each($('.config_tr'), function (index, value, array) {
            var id = $(value).find('.config_id').val();
            var month = $(value).find('.config_month').val();
            var cate = $(value).find('.config_cate').val();
            var settle_count = $(value).find('.config_settle_count').val();
            var price = $(value).find('.config_price').val();
            post_data['config'][cate] = {
                'id': id,
                'cate': cate,
                'month': month,
                'settle_count': settle_count,
                'price': price
            };
        });

        //构造对账数据
        $.each($('.data_tr'), function (index, value, array) {
            var id = $(value).find('.data_id').val();
            var cate = $(value).find('.data_cate').val();
            var settle_count = $(value).find('.data_settle_count').val();
            var remark = $(value).find('.data_remark').val();
            post_data['data'][id] = {
                'id': id,
                'cate': cate,
                'settle_count': settle_count,
                'remark': remark
            };
        });

        $.post('/Admin/SmsStatsMonth/DoCheck', post_data, function (data) {
            if (data.code == 200) {
                alert('校对成功');
                var link = '/Admin/SmsStatsMonth/Index?' + $('#queryForm').serialize();
                window.location.href = link;
            }
            else {
                alert(data.msg)
            }
        });
    });

    $('.exitCheck').click(function () {
        var link = '/Admin/SmsStatsMonth/Index?' + $('#queryForm').serialize();
        window.location.href = link;
    });
</script>
</body>
</html>
