<?php
use Octopus\Logger\Handler\StreamHandler;
use Octopus\Logger;
class CheckController extends Controller
{

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        //如果项目方已经使用了图片验证码方式,则不需要在记录forbidenIp
        $mid = isset($_GET['mid']) ? \Common\Utils\Url::getStringParam("mid") : false;
        $isUseRedis = Config::get("isUseRedis");
        if ($mid != 'XQ' && $mid != 'XCTEST' && $isUseRedis)
        {
            $client_ip = get_client_ip();
            $nowTime = time();
            $redis = RedisEx::getInstance();
            //Check禁止IP规则次数
            define('CIPFLN', $redis->hGet('LimitSetting', 'CIPFLN'));
            //Check禁止IP规则时间
            define('CIPFLT', $redis->hGet('LimitSetting', 'CIPFLT'));
            //Check禁止IP持续时间
            define('CIPFCT', $redis->hGet('LimitSetting', 'CIPFCT'));
            //公司服务端ip数组
            $ip_arr = getAllServerIps($redis);
            if (!in_array($client_ip, $ip_arr))
            {
                $keyname = "checkTimeLog:" . str_replace(".", "_", $client_ip);
                $redis->lPush($keyname, $nowTime);
                $redis->expireAt($keyname, $nowTime + CIPFLT);
                $redis->lTrim($keyname, 0, CIPFLN - 1);
                $forbidenTime = $redis->zScore('checkForbidenIps', str_replace(".", "_", $client_ip));
                if ($forbidenTime && $forbidenTime > ($nowTime - CIPFCT))
                {
                    header("HTTP/1.1 403");
                    exit;
                }
                //取最新的规则次数检查时间日志
                $checkTime_range = $redis->lRange($keyname, 0, CIPFLN - 1);
                //计算日志长度，如果大于规则次数才需要做判断
                $checkLen = count($checkTime_range);
                if ($checkLen >= CIPFLN)
                {
                    //计算最近规则次数检查的时间间隔
                    $timeRange = $checkTime_range[0] - $checkTime_range[CIPFLN - 1];
                    //如果最近规则次数检查的时间间隔小于规则时间，则将IP加入禁止列表
                    if ($timeRange < CIPFLT)
                    {
                        //清理1小时之前的ip
                        $redis->zRemRangeByScore('checkForbidenIps', 0, $nowTime - CIPFCT);
                        //添加禁止IP
                        $redis->zAdd('checkForbidenIps', $nowTime, str_replace(".", "_", $client_ip));
                        if ($redis->exists('checkForbidenPersistentIps:' . str_replace(".", "_", substr($client_ip, 0, strrpos($client_ip, '.')))))
                        {
                            $ppIp = str_replace(".", "_", substr($client_ip, 0, strrpos($client_ip, '.')));
                            $pIps = $redis->sMembers('checkForbidenPersistentIps:' . $ppIp);
                            foreach ($pIps as $pIp)
                            {
                                $redis->zAdd('checkForbidenIps', $nowTime, $ppIp . '_' . $pIp);
                            }
                            $redis->sAdd('checkForbidenPersistentIps:' . $ppIp, substr($client_ip, strrpos($client_ip, '.') + 1));
                        }
                        header("HTTP/1.1 403");
                        exit;
                    }
                }
            }
        }
    }


    /**
     * jsonp形式检查
     */
    public function actionJsonp()
    {
        $value = $_GET['value'];
        
        $FindAction = loadAction('Find');
        $accoutTypeData = $FindAction->checkAccoutType($value);
        $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
        $type =  !empty($accoutTypeData['type']) ? $accoutTypeData['type'] :'username';
        $ret =  !empty($accoutTypeData['type']) ? 1 : 2;
        echo "$callback($ret);";exit;
    }


    /**
     * jsonp形式检查
     * <AUTHOR>
     */
    public function actionJsonCode()
    {
        session_start();
        $code = trim($_GET['value']);
        $type = 'email' == strval($_GET['type']) ? 'email' : 'phone';
        $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);

        $memberModel = loadModel('member');
        $userinfo = $memberModel->read($_SESSION['findPassid']);
        if (empty($userinfo))
        {
            echo "$callback(0);";
            exit;
        }

        if (('email' == $type && $code != $userinfo['email']) || ('phone' == $type && $code != $userinfo['phone']))
        {
            echo "$callback(2);";
            exit;
        }

        exit("$callback(1);");
    }

    /**
     * webapi 手机快捷登录 检查手机号状态
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionPhoneStatus()
    {
        $phone = \Common\Utils\Url::getStringParam("phone");
        $mid = \Common\Utils\Url::getStringParam("mid");
        $memberModel = loadModel('member');

        //检查验证码
        $mid != 'XCTEST' && $this->checkCaptcha();

        $status = 0;
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(300, "", "请确认您的手机号是否填写正确!");
        }
        else
        {
            $midArr = Config::get("mid");
            if (!isset($midArr[$mid]))
            {
                $this->codeResponse(501, "", "项目标识不正确!");
            }
            $res = $memberModel->checkPhone($phone, true); //检查是否存在手机号, 包括用户名
            if ($res > 0)
            {
                $status = 1;
            }
        }
        $data = array(
            "status" => $status,
        );
        $this->codeResponse(200, $data);
    }

    /**
     * 校验验证码
     * @author：dongx
     * @return void
     */
    private function checkCaptcha()
    {
        session_start();
        $check_code = isset($_REQUEST['check_code']) ? $_REQUEST['check_code'] : '';
        if ($check_code === '')
        {
            $this->codeResponse('P00005', "", "验证码不能为空!");
        }
        $codeValidate = (0 == strcmp($check_code, $_SESSION['checkIMGCode_new'])) || (0 == strcmp($check_code, $_SESSION['captcha_code']));
        //针对天气做特殊处理
        if (!$codeValidate && RUNMODE != 'development' && (!isset($_REQUEST['wxGroup']) || $_REQUEST['wxGroup'] != 1))
        {
            $this->codeResponse('P00004', "", "验证码输入错误!");
        }
    }
    /**
     * 检查是否需要验证码
     * mid 传递项目标识符
     * phone 做验证手机号码
     * */
    public function actionIsEnableShowCaptcha()
    {
        session_start();
        $mid = \Common\Utils\Url::getStringParam('mid');   //项目标示
        $phone = \Common\Utils\Url::getStringParam('phone');   //手机号码

        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(200, "", "");
        }

        $lockPro = Config::get("lockpro");
        //如果传递上来的mid存在配置项中,且手机不存在SEESION中,那么则开启显示验证码
        if (!empty($lockPro) && array_key_exists($mid, $lockPro))
        {
            //配置需要显示验证码, 且SESSION里面没有手机号码  显示验证码
            if (empty($_SESSION['captcha_phone']))
            {
                $this->codeResponse(200, "", "");
                exit;
            }
            else
            {
                //SESSION中存在手机号码,但手机号码不一致,显示验证码
                if ($_SESSION['captcha_phone'] != $phone)
                {
                    $this->codeResponse(200, "", "");
                }
                else
                {
                    //SEESION里面的手机和传递的手机一样,那么不需要显示验证码
                    $this->codeResponse(201, "", "");
                }
            }
        }
        else
        {
            //在配置里面没有找到显示验证码,返回无需显示验证码
            $this->codeResponse(201, "", "");
        }
    }

}