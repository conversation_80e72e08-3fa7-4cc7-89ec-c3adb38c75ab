@charset "gb2312";
body,p,dl,dt,dd,table,td,th,input,img,form,div,span,ul,ol,li,h1,h2,h3,h4,h5,h6,select,input,sub,sup{margin:0;padding:0;}
body{
	font-family: Arial;
	font-size: 12px;
	color: #111;
}
*html{background-image:url(about:blank);background-attachment:fixed;}
img,iframe{border:none;}
ul,li,ol{list-style:none;}
img{vertical-align:middle;}
input{ outline:none;}
em,b,i,strong,cite,sub,sup{font-style: normal;}
a{
	color:#235291;
	text-decoration:none;
}
a:hover{
	text-decoration:none;
	color:#fd5151;
}
.clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.clearfix{zoom:1;}

.main {
	width: 480px;
	background-color: #f6f9fe;
}
.banner {
	background-image: url(../images/sign_banner.png);
	background-repeat: no-repeat;
	height: 80px;
}
.box{
	padding-bottom: 36px;
	padding-top: 36px;
	position: relative;
}
.box_agree{
    padding-left: 37px;
	padding-bottom: 36px;
	padding-top: 26px;
	position: relative;
}
.box_retry{
	background-image: url(../images/retry_banner.png);
	background-repeat: no-repeat;
	height: 370px;
	width: 480px;
	background-color: #FFFFFF;
}
.box_retry p {
	padding-top: 300px;
	padding-left: 140px;
	_padding-left: 120px;
}

.popup {
	background-color: #fff5d7;
	border-bottom-width: 1px;
	border-bottom-style: solid;
	border-bottom-color: #efe3bf;
	line-height: 22px;
	height: 22px;
	position: absolute;
	width: 480px;
	top: 0px;
	left: 0px;
	text-align: center;
	color: #8f7132;
}
.box ul {
	border: 1px solid #aec6e2;
	width: 198px;
	line-height: 21px;
	position: absolute;
	left: 150px;
	top: 65px;
	background-color: #FFFFFF;
    height:auto !important;
    _height:expression(this.scrollHeight > 105 ? "105px" : "auto");
    max-height: 105px;
    overflow-x: hidden;
    overflow-y: scroll;
}
.box ul .name {
	color: #666;
	height: 21px;
	padding-right: 4px;
	padding-left: 4px;
}
.box ul .name_h {
	background-color: #779ad2;
	color: #FFFFFF;
	height: 21px;
	padding-right: 4px;
	padding-left: 4px;
	position: relative;
	font-size: 12px;
}


.input0,.input1,.input2 {
	height: 28px;
	line-height: 28px;
	border: 1px solid #aec6e2;
	padding-left: 4px;
	padding-right: 4px;
	color: #666666;
	font-size: 12px;
}

.input0 {
	width: 164px;
}
.input1 {
	width: 190px;
}
.input2 {
	width: 57px;
}
.check1 {
	vertical-align: middle;
	_margin: -2px -1px 0 -4px;
}
.textarea1 {
	line-height: 18px;
	border: 1px solid #aec6e2;
	padding-left: 4px;
	padding-right: 4px;
	color: #666666;
	font-size: 12px;
	height: 250px;
	width: 350px;
}

.txt1{
	font-size: 14px;
	color: #515d6e;
}
.txt2{
	font-size: 12px;
	color: #515d6e;
}
.name_h img {
	display: block;
	position: absolute;
	right: 7px;
	top: 5px;
}

.code img {
margin: 0 8px;
}
.sign_btn {
	height: 33px;
	width: 74px;
	display: block;
	line-height: 33px;
	text-align: center;
	background-image: url(../images/sign_btnbg.png);
	background-repeat: no-repeat;
	font-size: 14px;
	color: #FFFFFF;
	font-weight: bold;
	margin-left: 50px;
}
.sign_btn:hover {
	background-image: url(../images/sign_btnbg_h.png);
	background-repeat: no-repeat;
	color: #FFFFFF;
}
.agree_btn {
	height: 33px;
	width: 74px;
	display: block;
	line-height: 33px;
	text-align: center;
	background-image: url(../images/sign_btnbg.png);
	background-repeat: no-repeat;
	font-size: 14px;
	color: #FFFFFF;
	font-weight: bold;
	margin-left: 174px;
}
.agree_btn:hover {
	background-image: url(../images/sign_btnbg_h.png);
	background-repeat: no-repeat;
	color: #FFFFFF;
}
.retry_btn {
	height: 33px;
	width: 74px;
	display: block;
	line-height: 33px;
	text-align: center;
	background-image: url(../images/sign_btnbg.png);
	background-repeat: no-repeat;
	font-size: 14px;
	color: #FFFFFF;
	font-weight: bold;
	float: left;
	margin-left: 18px;
}
.retry_btn:hover {
	background-image: url(../images/sign_btnbg_h.png);
	background-repeat: no-repeat;
	color: #FFFFFF;
}
.h_sign{
	height: 20px;
	_height: 18px;
}
.h_reg{
	height: 15px;
	_height: 13px;
	font-size: 5px;
}
.h_reg2{
	height: 12px;
	_height: 10px;
	font-size: 5px;
}