<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：MsgAction.php
 * 摘    要：返回格式类
 * 作    者：杜海明
 * 修改日期：2015.04.17
 * */
class MsgAction extends Action
{

    public static $callbaskStatus = array(
        - 1 => '群发短信,单次请求手机号限制请控制在5000个以内(建议500个)!',
        - 2 => '手机号不正确或着是黑名单',
        - 3 => '群发数据格式不正确',
    );

    /**
     * 函数名称：returnJsonData
     * 参    数：
     * 作    者：杜海明
     * 功    能：返回json 或者jsonp
     * 修改日期：2015-04-15
     *
     * @param array $data data
     * @return null
     */
    public static function returnJsonData($data, $forceReturnJson = false)
    {
        if (PHP_SAPI === 'cli') {
            return $data;
        } elseif ($forceReturnJson) {
            return array_values($data);
        } else {
            header('Content-type: application/json');
            $callBack = isset($_GET['callback']) ? $_GET['callback'] : '';
            if (!empty($callBack)) {
                echo $callBack . '(' . json_encode(EncodingAction::transcoding($data, 'utf-8')) . ')';
            } else {
                echo json_encode(EncodingAction::transcoding($data, 'utf-8'));
            }
            exit;
        }
    }

    /**
     * @param int    $code code
     * @param string $msg  msg
     * @param array  $data data
     *
     * @return bool
     */
    public static function failedResponse($code = 0, $msg = "", $data = array())
    {
        $response["status"] = $code;
        $response["msg"] = $msg;
        $response["data"] = $data;

        return self::returnJsonData($response);
    }

    /**
     * @param string $msg  msg
     * @param array  $data data data
     *
     * @return bool
     */
    public static function successResponse($msg = "", $data = array())
    {
        $response["status"] = 1;
        $response["msg"] = $msg;
        $response["data"] = $data;

        return self::returnJsonData($response);
    }

    /**
     * @param string $msg  msg
     * @param array  $data data
     *
     * @return bool
     */
    public static function partSuccessResponse($msg = "", $data = array())
    {
        $response["status"] = 2;
        $response["msg"] = $msg;
        $response["data"] = $data;

        return self::returnJsonData($response);
    }

}
