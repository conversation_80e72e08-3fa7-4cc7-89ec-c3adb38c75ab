<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/3
 * Time: 9:40
 */
includeBase('ExternalController');

class ExternalCheckController extends ExternalController
{

    /**
     * ExternalCheckController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            $this->codeResponse("403", "", "");
        }
    }

    /**
     * 校验
     * @return void
     */
    public function actionIndex()
    {
        //校验必填字段及签名
        $necessaryField = array(
            'exId',
            'passid',
            'token',
            'requestTime',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check('data', $necessaryField);
        if ($aesDecodeData === false)
        {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        list($exId, $passid ,$token, $time) = $this->getParamAndAutoDeny($necessaryField);
        $LoginToken = loadAction("LoginToken");
        $isTokenValid = $LoginToken->checkToken($exId, $passid, $time, $token);
        if ($isTokenValid)
        {
            $this->codeResponse("200", "", "");
        }
        else
        {
            $this->codeResponse("500", "", "");
        }
    }
}
