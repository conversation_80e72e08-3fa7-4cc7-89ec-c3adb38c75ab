;(function($)
{
	$.fn.extend({
		boxVertical:function(options){
			var settings = {
				boxTitle: "提示框",
				boxTxt:"错误提示文案",
				callbackFun: function(){},
                width : '300',
                height : '32',
                initSetting : function (obj)
                {
                  
                },
                isShow : true
			};
			var _this = $(this);
			var options = $.extend(settings, options);

			_this.bind("click",function()
			{
                var obj = $(this);
                if ( $('#confirmDivId') )
                {
                    $('#confirmDivId').remove();
                }
                settings.initSetting(settings);
                if ( settings.isShow == false)
                {
                    settings.isShow = true;
                    return false;
                }
                var Htmls = "<div id='confirmDivId' class='boxMod js_boxMod w316' style='display:block;border:solid 1px #ccc;width:"+settings.width+"px'><div class='boxTop' id='boxTopId'><h2 class='boxTit'>"+settings.boxTitle+"</h2><a href='javascript:' class='BoxClose js_close'>关闭按钮</a></div><div class='tabFrom mtb20'><p class='errorTxt'>"+settings.boxTxt+"</p><div class='errorBtn mt20'><a href='javascript:' class='styleBtnBlue tabsave js_sure'>确定</a><a href='javascript:' class='styleBtnBlue ml10 tabsave js_close'>取消</a></div></div></div>";
                
                $(Htmls).appendTo("body");
                
                $('#confirmDivId').css('left',0);
                //$('#confirmDivId').css('width',settings.width);
                $("#confirmDivId").css("margin-left",($("body").width() -$("#confirmDivId").width())/2);
                $('#boxTopId').css('height',settings.height);
				$(".js_boxMod").css("margin-top",-($(".js_boxMod").height() / 2));
                //关闭
    			$(".js_close").live("click",function()
    			{
    				$(".js_boxMod").hide();
                    $('#confirmDivId').remove();
    			});
    			
    			//确定
    			$(".js_sure").bind("click",function()
    			{
    				settings.callbackFun(obj);
                    $('#confirmDivId').remove();
    			});
            });	
			
		}//boxVertical
	});
})(jQuery);