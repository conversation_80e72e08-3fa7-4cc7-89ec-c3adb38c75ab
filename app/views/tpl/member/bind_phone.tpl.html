{{include file = "public/mem_header.tpl.html"}}
{{include file = "public/left_nav.tpl.html}}
    <div class="account_right">
        <div class="changeEmail-A">
            <h2 class="pf">绑定手机</h2>
            <div class="boxCon">
                <div class="ts-wrap">
                    <p class="tsTxt">温馨提示：请输入您要绑定的手机号码，可用该手机登录所有2345产品</p>

                    <p class="ts-infor" id="phonetip" style="display: none"><i></i>发送成功，请查收</p>
                </div>
                <!--ts-wrap end-->

                <ul class="conUl">
                    <li>
                        <span class="conTit">手机号码：</span>

                        <div class="coninput">
                            <input class="defaultInput" type="text" id="phone" name="phone" onblur="checkPhone();" autocomplete="off" value="请输入手机号码" title="请输入手机号码" maxlength="11"/>
                        </div>
                        <a class="messCode" href="javascript:" onclick="sendCode();return false;">发送验证码</a>
                        <span id="sendingBtn" class="messCode-dsib" style="display:none;">重新发送（60s）</span>
                    </li>
                    <li>
                        <span class="conTit">验证码：</span>
                        <div class="coninput">
                            <input type="number" id="code" name="code" value="" autocomplete="off"/>
                        </div>
                    </li>
                </ul>
                <!--conUl end-->
                <a class="btn-tj" id="bindphonesubmit" href="javascript:">提交</a>
            </div>
            <h2 class="pf mt46">绑定手机常见问题</h2>
            <dl class="questionList">
                <dt>绑定手机时，为什么会提示“手机号已注册”</dt>
                <dd>因为该手机号已经在用户中心、积分、游戏等2345产品上注册或者绑定过，请重新输入新手机号码。</dd>
            </dl>
            <dl class="questionList mt20">
                <dt>手机换号了，无法修改绑定手机怎么办？ </dt>
                <dd>请致电客服人工解决，客服电话：************</dd>
            </dl>
            <dl class="questionList mt20">
                <dt>手机无法收到验证码 </dt>
                <dd>请检测垃圾信箱，是否被手机安全软件误拦截，若检查后仍旧无法收到，请致电客服人工帮助，客服电话：************</dd>
            </dl>
        </div>

    </div><!--account_right end-->
</div><!--main end-->

<div class="box_mark" style="display: none">遮罩层</div>

<div class="box_mod h232" id="bindsuccess">
    <div class="box_mod_tit"><a class="btn_box_close" href="javascript:"></a></div>
    <div class="infor-con">
        <i class="gouIcon"></i>
        <div class="infor-txt">
            <h2>绑定手机成功！</h2>
            <p id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a></p>
        </div>
    </div><!--boxCon end-->
</div>
<!--主体 end-->
{{include file = "public/qrcode.tpl.html"}}

<!--[if IE 6]>
<script type="text/javascript" src="/js/DD_belatedPNG_0.0.8a-min.js"></script>
<script type="text/javascript">
    DD_belatedPNG.fix('.png24');
</script>
<![endif]-->
<script src="/js/jquery-1.9.1.min.js"></script>
<script src="/js/tab_201509011442.js"></script>
{{include file = "public/mem_footer.tpl.html"}}


<script>
    var bottomObjectTimeId = setInterval(function(){
        if($("#ClCache") != undefined){
            clearInterval(bottomObjectTimeId);
            $("#ClCache").parent().css("display","none");
        }
    },1000);
    var windowHeight = $(window).height();
    var documentHeight;
    function maskBoxHeight()
    {
        var documentHeight = $(document).height();
        if (documentHeight > windowHeight) {
            $(".box_mark").css("height", documentHeight).show();
        } else {
            $(".box_mark").css("height", windowHeight).show();
        };
    };
    $(".coninput input").bind(
            {
                focus:function(){
                    $(this).parent().removeClass("inputTxtError");
                    $(this).parent().addClass("inputTxtFocus");
                    if($(this).val() == $(this).attr("title"))
                    {
                        $(this).val("");
                    }
                    $(this).addClass("colorInput");
                },
                blur:function(){
                    $(this).parent().removeClass("inputTxtFocus");
                    if($.trim($(this).val()) == "")
                    {
                        $(this).val($(this).attr("title"));
                        $(this).removeClass("colorInput");
                    };
                }
            }
    );
    var messageCodeTime = $(".messCode-dsib"),messageTimeNum = 60,btnOk = $(".messCode"),windowHeight = $(window).height();
    //打开弹框
    $("#showbindphone").bind("click",function()
    {
        $("#bindphone").eq($(this).index()).show();
        maskBoxHeight();
    });
    $("#verify-close").bind("click", function(){
        $("#phone").val("请输入手机号码").removeClass("colorInput");
        $("#code").val("");
        $("#phonetip").hide();
        messageCodeTime.hide();
        btnOk.text("发送验证码").show();
    });
    //关闭弹框
    $(".btn_box_close").bind("click",function()
    {
        $(".box_mod,.box_mark").hide();
    });
    $("#bindphonesubmit").bind("click", function(){
        var codeErr = true;
        var phone = $("#phone").val();
        var code = $("#code").val();
        if (phoneErr == 1) {
            return false;
        }
        if (code == "") {
            $("#phonetip").html('<i></i>请输入验证码！').addClass("ts-error").show();
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/member/bindPhone/do",
            async: false,
            data: "phone=" + AES.encrypt(phone) + "&code=" + code,
            timeout:3000,
            success: function(data) {
                if (data == '200') {
                    codeErr = false;
                    $("#bindphone").hide();
                    $("#bindsuccess").show();
                    maskBoxHeight();
                    var times = 3;
                    setInterval(function() {
                        if (times > 0) {
                            $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
                        } else {
                            window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
                        }
                        times--;
                    }, 1000)
                } else if (data == '300') {
                    $("#phonetip").html('<i></i>验证码错误，请重新输入').addClass("ts-error").show();
                }
                else if (data == '500') {
                    $("#phonetip").html('<i></i>服务器忙，请稍候再试').addClass("ts-error").show();
                }
            }
        });
        return !codeErr;
    });
    var phoneErr = 1;
    function checkPhone() {
        var phone = $("#phone").val();
        if (!phone) {
            phoneErr = 1;
            return false;
        }
        if (!/^1[0123456789]\d{9}$/.test(phone)) {
            phoneErr = 1;
            $("#phonetip").html('<i></i>请输入正确的手机号！').addClass("ts-error").show();
            return false;
        }
        $.ajax({
            url: "/api/check/jsonp",
            data: 'type=phone&value=' + phone,
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(response) {
                err = response;
                if (err == 1)
                {
                    phoneErr = 1;
                    $("#phonetip").html('<i></i>请输入正确的手机号！').addClass("ts-error").show();
                    return false;
                }
                else if (err == 2)
                {
                    phoneErr = 1;
                    $("#phonetip").html('<i></i>此手机号已被绑定，请重新输入！').addClass("ts-error").show();
                    $("#phonetip").show();
                }
                else if (err == 3)
                {
                    phoneErr = 1;
                    $("#phonetip").html('<i></i>此手机号已被绑定，请重新输入！').addClass("ts-error").show();
                    return false;
                }
                phoneErr = 0;
            },
            error: function(jqXHR, textStatus, errorThrown) {
                phoneErr = 1;
            },
            timeout: 3000
        });
    }
    function sendCode() {
        var phone = $("#phone").val();
        if (phone == '' || phone == $("#phone").attr("title")) {
            $("#phonetip").html('<i></i>请先输入手机号！').addClass("ts-error").show();
            return false;
        }
        if (phoneErr == 1) {
            return false;
        }
        $.post("/member/bindPhone/sendCode", {'phone': AES.encrypt(phone)}, function(data) {
            if(data.indexOf("200") >= 0)
            {
                $(".messCode").text("重新发送").css("display","none");
                messageCodeTime.css("display","block");
                messageTimeNum = 60;
                messageTime();
            } else if (data == '300') {
                $("#phonetip").html('<i></i>请输入正确的手机号！').addClass("ts-error").show();
            } else if (data == '301') {
                $("#phonetip").html('<i></i>此手机号已被绑定，请重新输入！').addClass("ts-error").show();
            }  else if (data == '400') {
                $("#phonetip").html('<i></i>验证的太频繁了，请稍后再试！').addClass("ts-error").show();
            } else if (data == '402') {
                $("#phonetip").html('<i></i>此手机号已在其他项目绑定，请重新输入！').addClass("ts-error").show();
            } else if (data == '500') {
                $("#phonetip").html('<i></i>服务器忙，请稍后再试！').addClass("ts-error").show();
            }
        });
    }
    function messageTime(){
        messageCodeTime.text("重新发送（"+messageTimeNum+ "s" + "）");
        messageTimeNum --;
        if(messageTimeNum>=0){
            setTimeout(messageTime,1000);
        }else{
            $(".messCode").css("display","block");
            messageCodeTime.css("display","none");
        };
    };
</script>
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
