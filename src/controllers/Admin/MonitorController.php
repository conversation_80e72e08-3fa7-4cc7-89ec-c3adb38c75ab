<?php

namespace Admin;

use Controller;

class MonitorController extends Controller
{
    protected $redis;

    /**
     * MonitorController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->redis = \RedisAction::connect();
    }

    /**
     * @return void
     */
    public function actionIndex()
    {
        loadView('Admin/monitor_index.tpl.html', [], false, ['[|', '|]']);
    }

    /**
     * @return void
     */
    public function actionMail()
    {
        loadView('Admin/monitor_mail.tpl.html', [], false, ['[|', '|]']);
    }

    /**
     * @return void
     */
    public function actionApi()
    {
        $func = $_GET['func'] ?? '';
        if (method_exists($this, $func))
        {
            echo $this->$func();
        }
        else
        {
            echo json_encode(['err' => -999, 'msg' => "Method {$func} not exists"]);
        }
    }

    /**
     * @return string
     */
    private function operatorList()
    {
        $operatorConfigModel = OperatorConfigModel::getInstance();
        $operatorList = $operatorConfigModel->getOperatorList();
        $operators = [];
        foreach ($operatorList as $key => $value)
        {
            if ($value['status'] == 1)
            {
                $operators[] = [
                    'value' => $value['id'],
                    'label' => $value['id'] . '-' . \EncodingAction::transcoding($value["provider_name"], 'utf-8'),
                ];
            }
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($operators)]);
    }

    /**
     * @return string
     */
    private function projectList()
    {
        $projectModel = \Admin\ProjectModel::getInstance();
        $projectList = $projectModel->getProjectList('', []);
        $projects = [];
        foreach ($projectList as $projectInfo)
        {
            if ($projectInfo['status'] == 1)
            {
                $projects[] = [
                    'value' => $projectInfo['pid'],
                    'label' => $projectInfo['pid'] . '-' . \EncodingAction::transcoding($projectInfo["project_name"], 'utf-8'),
                ];
            }
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($projects)]);
    }

    /**
     * @return string
     */
    private function typeList()
    {
        $smsTypes = [];
        $smsTypeModel = \Admin\SmsTypeModel::getInstance();
        $smsTypeList = $smsTypeModel->getSmsTypeList();
        foreach ($smsTypeList as $smsTypeKey => $smsTypeInfo)
        {
            $smsTypes[] = [
                'value' => $smsTypeInfo['sms_type'],
                'label' => $smsTypeInfo['sms_type'] . '-' . \EncodingAction::transcoding($smsTypeInfo["sms_name"], 'utf-8'),
            ];
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($smsTypes)]);
    }

    /**
     * @return string
     */
    private function data()
    {
        $dateTimeArr = is_array($_POST['date']) ? $_POST['date'] : [];
        $returnData = [];
        $rawDataSet = [];
        switch ($_POST['type'])
        {
            case 'all':
                $prefix = "sms:monitor:all:";
                break;
            case 'api':
            case 'type':
            case 'proj':
                $prefix = "sms:monitor:{$_POST['type']}:{$_POST['find']}:";
                break;
            default:
                return json_encode(['err' => -1, 'msg' => 'type not exists']);
        }
        foreach ($dateTimeArr as $dateTime)
        {
            $stamp = strtotime($dateTime);
            $date = date('Y-m-d', $stamp);
            $dateY = date('Y-m-d', $stamp - 86400);

            $rawData = $rawDataSet[$date] ?? $this->redis->hGetAll($prefix . $date);
            $rawDataSet[$date] = $rawDataSet[$date] ?? $rawData;
            $rawDataY = $rawDataSet[$dateY] ?? $this->redis->hGetAll($prefix . $dateY);
            $rawDataSet[$dateY] = $rawDataSet[$dateY] ?? $rawDataY;

            $i = date('H', $stamp) * 6 + (int)(date('i', $stamp) / 10);
            for ($j = $i + 1; $j < 144; $j++)
            {
                $value = $rawDataY[$j] ?? 0;
                $hm = date('H:i', strtotime($date) + $j * 600);
                $returnData[$date][] = [$hm, $value];
            }
            for ($j = 0; $j <= $i; $j++)
            {
                $value = $rawData[$j] ?? 0;
                $hm = date('H:i', strtotime($date) + $j * 600);
                $returnData[$date][] = [$hm, $value];
            }
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($returnData)]);
    }

    /**
     * @return string
     */
    private function mail()
    {
        $toDay = $this->redis->lRange('sms:monitor:dump:' . date('Ymd', time()), 0, -1);
        $lastDay = $this->redis->lRange('sms:monitor:dump:' . date('Ymd', time() - 86400), 0, -1);
        $llastDay = $this->redis->lRange('sms:monitor:dump:' . date('Ymd', time() - 86400 * 2), 0, -1);
        $mailDump = array_merge($toDay, $lastDay, $llastDay);
        $result = [];
        $index = 0;
        foreach ($mailDump as $mailStr)
        {
            if ($index++ === 100)
            {
                break;
            }
            $mailArr = explode('<br/>===================<br/>', $mailStr);
            $result[] = \EncodingAction::transcoding($mailArr, 'UTF-8');
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($result)]);
    }

    /**
     * 导出邮件报警记录
     * @return void
     */
    private function export()
    {
        AutoMakeCsvAction::init();
        AutoMakeCsvAction::setField([
            '日期',
            '时间',
            '数量',
            '比例',
            'sql',
        ]);
        for ($i = 0; $i < 5; $i++) {
            $date = date('Ymd', time() - $i * 3600 * 24);
            $key = 'sms:monitor:dump:' . $date;
            $list = $this->redis->lRange($key, 0, -1);
            foreach ($list as $item) {
                $item = explode('<br/>===================<br/>', $item);
                array_unshift($item, $date);
                AutoMakeCsvAction::writeData($item);
            }
        }
        AutoMakeCsvAction::headerLocationCsv();
        exit;
    }
}
