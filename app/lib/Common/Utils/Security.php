<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：Security.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：01-04, 2017
 */

namespace Common\Utils;

class Security
{

    /**
     * 数组解密
     *
     * @param string $key           key
     * @param string $preDecodeData preDecodeData
     *
     * @return mixed
     */
    public static function decodeArray($key = "", $preDecodeData = "")
    {
        $code = self::decodeString($key, $preDecodeData);
        $res = json_decode($code, true);

        return $res;
    }

    /**
     * 数组加密
     *
     * @param string $key           key
     * @param array  $preEncodeData preEncodeData
     *
     * @return string
     */
    public static function encodeArray($key = "", $preEncodeData = array())
    {
        $str = json_encode($preEncodeData);

        return self::encodeString($key, $str);
    }

    /**
     * 字符串解密 encode string
     *
     * @param string $key    key
     * @param string $string string
     *
     * @return string
     */
    public static function decodeString($key = "", $string = "")
    {
        $string = base64_decode($string);
        $code = array();
        $keyLen = strlen($key);
        $strLen = strlen($string);
        for ($i = 0; $i < $strLen; $i ++)
        {
            $k = $i % $keyLen;
            $code[] = $string[$i] ^ $key[$k];
        }
        $res = implode('', $code);

        return $res;
    }

    /**
     * 字符串加密 encode string
     *
     * @param string $key    key
     * @param string $string string
     *
     * @return string
     */
    public static function encodeString($key = "", $string = "")
    {
        $keyLen = strlen($key);
        $strLen = strlen($string);
        $strTmp = "";
        for ($i = 0; $i < $strLen; $i ++)
        {
            $k = $i % $keyLen;
            $strTmp .= $string[$i] ^ $key[$k];
        }
        $res = base64_encode($strTmp);

        return $res;
    }

}
