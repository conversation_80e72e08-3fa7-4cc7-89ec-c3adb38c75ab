var dianying_list = {};
var dianshiju_list = {};
$.loadJs("http://my.2345.com/api/get_movie_api.php?d="+escape(new Date()));

function showMovie(){
	movie_show_dianying();
	movie_show_dianshiju();
}
//
function movie_show_dianying(){
	var dianying_cls = this.dianying;
	for(var i = 0; i < 9; i++){
	this.dianying_list[i+1] = dianying_cls[i];
	}
	
	for(var i = 0; i < 9; i++){
		movie_set_dianying(dianying_list[i+1],'dianying_list_'+(i+1),'');
	}
}

function movie_show_dianshiju(){
	
	var dianshiju_cls = this.dianshiju;
	
	for(var i = 0; i < 9; i++){
		this.dianshiju_list[i+1] = dianshiju_cls[i];
	}
	
	for(var i = 0; i < 9; i++){
		movie_set_dianshiju(dianshiju_list[i+1],'dianshiju_list_'+(i+1),'11选5');
	}
}
//
//电影
function movie_set_dianying(cls,clsid,clsName){

	var actor = (cls.score != null) ? cls.score : '6.0';
	actor = (cls.score == '0') ? '6.0' : actor;
	var b = cls.mtype.split(',');
	var mtype = ($.type(b[0]) != "undefined"?b[0]:"") + ($.type(b[1]) != "undefined"?"/"+b[1]:"") + ($.type(b[2]) != "undefined"? "/"+b[2]:"");
	var r = '';
	if (clsid.toString().substr(14,1) == '1')
	{
		$(clsid).attr("className",'firstimg');
	}else{
		$(clsid).attr("className",'');
	}
	var numOfClsid = clsid.substr(14,1);
	$(clsid).bind("mouseover",function(){ movie_set_dianying_detail(numOfClsid); });
	if(numOfClsid == 1){
		$(clsid).html( 
		'<em class="hotbest">0'+ numOfClsid +'</em>'+
		'<a href="'+ cls.url +'" target="_blank" onclick="cc(\'dianying\')"><img src="'+ cls.img +'" onerror="" width="51" height="68"></a>' +
		'<a href="'+ cls.url +'" target="_blank" onclick="cc(\'dianying\')">'+ (cls.title.length <= 9 ? cls.title : cls.title.substr(0,8)+"...") +'</a><p>'+ mtype +'</p>'+
		'<p>评分：<font>'+ actor +'</font></p>');		
	}else{				
		if(numOfClsid <= 3){
			$(clsid).html( 
					'<em class="hotbest">0'+ numOfClsid +'</em><a onclick="cc(\'dianying\')" href="'+ cls.url +'" target="_blank">'+ cls.title.substr(0,12) + (cls.title.length > 12 ? "..." : '') +'</a>');	
		}else{
			$(clsid).html( 
					'<em>0'+ numOfClsid +'</em><a onclick="cc(\'dianying\')" href="'+ cls.url +'" target="_blank">'+ cls.title.substr(0,12) + (cls.title.length > 12 ? "..." : '') +'</a>');	
		}
	}
			
}

////电视剧
function movie_set_dianshiju(cls,clsid,num){
	var actor = (cls.score != null) ? cls.score : "6.0";
	actor = (cls.score == 0) ? '6.0' : actor;
	var b = cls.mtype.split(',');
	var mtype = ($.type(b[0]) != "undefined"?b[0]:"") + ($.type(b[1]) != "undefined"?"/"+b[1]:"");
	var r = '';
	if (clsid.toString().substr(15,1) == '1')
	{
		$(clsid).attr("className",'firstimg');
	}else{
		$(clsid).attr("className",'');
	}
	var numOfClsid = clsid.substr(15,1);
	//lottery_set_gp_detail(numOfClsid);
	$(clsid).bind("mouseover",function(){movie_set_dianshiju_detail(numOfClsid)  });
	//console.log(cls);
	if(numOfClsid == 1){
		$(clsid).html( 
		'<em class="hotbest">0'+ numOfClsid +'</em>'+
		'<a href="'+ cls.url +'" target="_blank" onclick="cc(\'dianshiju\')"><img src="'+ cls.img +'" onerror="" width="51" height="68"></a>' +
		'<a href="'+ cls.url +'" target="_blank" onclick="cc(\'dianshiju\')">'+ (cls.title.length <= 9 ? cls.title : cls.title.substr(0,8)+"...") +'</a><p>'+ mtype +'</p>'+
		'<p>评分：<font>'+ actor +'</font></p>');		
	}else{				
		if(numOfClsid <= 3){
			$(clsid).html( 
					'<em class="hotbest">0'+ numOfClsid +'</em><a onclick="cc(\'dianshiju\')" href="'+ cls.url +'" target="_blank">'+ cls.title.substr(0,12) + (cls.title.length > 12 ? "..." : '') +'</a>');	
		}else{
			$(clsid).html( 
					'<em>0'+ numOfClsid +'</em><a onclick="cc(\'dianshiju\')" href="'+ cls.url +'" target="_blank">'+ cls.title.substr(0,12) + (cls.title.length > 12 ? "..." : '') +'</a>');	
		}
	}
}


//电影滑动
function movie_set_dianying_detail(num){

	eval('var d = this.dianying_list['+num+']');

	var actor = (d.score != null) ? d.score : "6.0";
	actor = (d.score == 0) ? '6.0' : actor;
	var b = d.mtype.split(',');
	var mtype = ($.type(b[0]) != "undefined"?b[0]:"") + ($.type(b[1]) != "undefined"?"/"+b[1]:"") + ($.type(b[2]) != "undefined"? "/"+b[2]:"");
	var r = '';

	$('dianying_list_'+num).attr("className",'firstimg');
	$('dianying_list_'+num).bind("mouseover",function(){});
	if($.type(d.img) == "undefined"){
		var img = "images/errorpic.gif";
	}else{
		var img = d.img;
	}
	if(num <= 3){
		$('dianying_list_'+num).html(			 
				'<em class="hotbest">0'+ num +'</em>'+
				'<a onclick="cc(\'dianying\')" href="'+ d.url +'" target="_blank" onmouseover="event.cancelBubble=true;return false;"><img src="'+ img +'" onerror="" width="51" height="68"></a>' +
				'<a onclick="cc(\'dianying\')" href="'+ d.url +'" target="_blank" onmouseover="event.cancelBubble=true;return false;">'+ (d.title.length <= 9 ? d.title : d.title.substr(0,8)+"...") +'</a><p>'+ mtype +'</p>'+
				'<p>评分：<font>'+ actor +'</font></p>');
	}else{
		$('dianying_list_'+num).html(			 
				'<em>0'+ num +'</em>'+
				'<a onclick="cc(\'dianying\')" href="'+ d.url +'" target="_blank" onmouseover="event.cancelBubble=true;return false;"><img src="'+ img +'" onerror="" width="51" height="68"></a>' +
				'<a onclick="cc(\'dianying\')" href="'+ d.url +'" target="_blank" onmouseover="event.cancelBubble=true;return false;">'+ (d.title.length <= 9 ? d.title : d.title.substr(0,8)+"...") +'</a><p>'+ mtype +'</p>'+
				'<p>评分：<font>'+ actor +'</font></p>');
	}
	for(var i = 1; i <= 9; i++){
		if(i != num){
			$('dianying_list_'+i).attr("className",'');
			if(i <= 3){
				$('dianying_list_'+i).html( 
					'<em class="hotbest">0'+ i +'</em><a onclick="cc(\'dianying\')" href="'+ this.dianying_list[i].url +'" target="_blank">'+ this.dianying_list[i].title.substr(0,12) + (this.dianshiju_list[i].title.length > 12 ? "..." : '') +'</a>');	
			}else{
				$('dianying_list_'+i).html( 
						'<em>0'+ i +'</em><a onclick="cc(\'dianying\')" href="'+ this.dianying_list[i].url +'" target="_blank">'+ this.dianying_list[i].title.substr(0,12) + (this.dianshiju_list[i].title.length > 12 ? "..." : '') +'</a>');	
			}
		}
	}
}


//电视剧滑动
function movie_set_dianshiju_detail(num){

	eval('var d = this.dianshiju_list['+num+']');


	var actor = (d.score != null) ? d.score : "6.0";
	actor = (d.score == 0) ? '6.0' : actor;
	var b = d.mtype.split(',');
	var mtype = ($.type(b[0]) != "undefined"?b[0]:"") + ($.type(b[1]) != "undefined"?"/"+b[1]:"");
	var r = '';

	$('dianshiju_list_'+num).attr("className",'firstimg');
	$('dianshiju_list_'+num).bind("mouseover",function(){});
	if($.type(d.img) == "undefined"){
		var img = "images/errorpic.gif";
	}else{
		var img = d.img;
	}
	if(num <= 3)
		$('dianshiju_list_'+num).html(			 
				'<em class="hotbest">0'+ num +'</em>'+
				'<a href="'+ d.url +'" target="_blank" onclick="cc(\'dianshiju\')" onmouseover="event.cancelBubble=true;return false;"><img src="'+ img +'" onerror="" width="51" height="68"></a>' +
				'<a href="'+ d.url +'" target="_blank" onclick="cc(\'dianshiju\')" onmouseover="event.cancelBubble=true;return false;">'+ (d.title.length <= 9 ? d.title : d.title.substr(0,8)+"...") +'</a><p>'+ mtype +'</p>'+
				'<p>评分：<font>'+ actor +'</font></p>');
	else
		$('dianshiju_list_'+num).html(			 
				'<em>0'+ num +'</em>'+
				'<a href="'+ d.url +'" target="_blank" onclick="cc(\'dianshiju\')" onmouseover="event.cancelBubble=true;return false;"><img src="'+ img +'" onerror="" width="51" height="68"></a>' +
				'<a href="'+ d.url +'" target="_blank" onclick="cc(\'dianshiju\')" onmouseover="event.cancelBubble=true;return false;">'+ (d.title.length <= 9 ? d.title : d.title.substr(0,8)+"...") +'</a><p>'+ mtype +'</p>'+
				'<p>评分：<font>'+ actor +'</font></p>');
	for(var i = 1; i <= 9; i++){
		if(i != num){
			$('dianshiju_list_'+i).attr("className",'');
			if(i <= 3){
				$('dianshiju_list_'+i).html( 
					'<em class="hotbest">0'+ i +'</em><a onclick="cc(\'dianshiju\')" href="'+ this.dianshiju_list[i].url +'" target="_blank">'+ this.dianshiju_list[i].title.substr(0,12) + (this.dianshiju_list[i].title.length > 12 ? "..." : '') +'</a>');	
			}else{
				$('dianshiju_list_'+i).html( 
						'<em>0'+ i +'</em><a onclick="cc(\'dianshiju\')" href="'+ this.dianshiju_list[i].url +'" target="_blank">'+ this.dianshiju_list[i].title.substr(0,12) + (this.dianshiju_list[i].title.length > 12 ? "..." : '') +'</a>');	
			}
		}
	}		
}


function movieChangeOver(num){
	if(num == 1){
		$("dianying_a").attr('className','curr');
		$('dianying').show();
		$("dianshiju_a").removeClass();
		$('dianshiju').hide();
	}else{
		$("dianshiju_a").attr('className','curr');
		$('dianshiju').show();
		$("dianying_a").removeClass();
		$('dianying').hide();
	}
}



