<?php

class SendNoticeAction extends Action
{
    public function sendSmsNotice($msg, $smsType, $positionId = 113)
    {
        $contacts = array("18911931747" => "谢志新"); // test
        $url = "http://smsp.2345.net/Api/Sms/Send";
        $postArr = array(
            'msg'        => $msg,
            'smsType'    => $smsType,
            'pid'        => 113,
            'clientIp'   => $_SERVER['REMOTE_ADDR'],
            'positionId' => $positionId,
        );

        foreach ($contacts as $tel => $name)
        {
            $postArr['phone'] = $tel;
            $result = \FuncAction::request($url, $postArr)[0];
        }
    }
}
