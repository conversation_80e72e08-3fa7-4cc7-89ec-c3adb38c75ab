{{include file = "admin/redmin/layout.tpl.html"}}
{{include file = "admin/redmin/generalmodals.tpl.html"}}
<span class="span12">
    <h5><i class="icon-info-sign"></i> Redis Info</h5>
    <table class="table table-striped">
        <tr>
            <td>
                Version:
            </td>
            <td>
                {{$pageArray.info.redis_version}}
            </td>
        </tr>
        <tr>
            <td>
                Mode:
            </td>
            <td>
                {{$pageArray.info.redis_mode}}
            </td>
        </tr>
        <tr>
            <td>
                Role:
            </td>
            <td>
                {{$pageArray.info.role}}
            </td>
        </tr>
        <tr>
            <td>
                OS:
            </td>
            <td>
                {{$pageArray.info.os}}
            </td>
        </tr>
        <tr>
            <td>
                Process ID:
            </td>
            <td>
                {{$pageArray.info.process_id}}
            </td>
        </tr>
        <tr>
            <td>
                Uptime:
            </td>
            <td>
                {{if $pageArray.uptimeDays > 0}}{{$pageArray.uptimeDays}} days&nbsp;{{/if}}{{'H:i:s'|date:$pageArray.info.uptime_in_seconds}}
            </td>
        </tr>
        <tr>
            <td>
                Clients:
            </td>
            <td>
                {{$pageArray.info.connected_clients}}
            </td>
        </tr>
        {{if $pageArray.info.role == 'master'}}
            <tr>
                <td>
                    Slaves:
                </td>
                <td>
                    {{$pageArray.info.connected_slaves}}
                </td>
            </tr>
        {{/if}}
        <tr>
            <td>
                Used Memory:
            </td>
            <td>
                {{$pageArray.info.used_memory_human}}
            </td>
        </tr>
        <tr>
            <td>
                Used Memory Peak:
            </td>
            <td>
                {{$pageArray.info.used_memory_peak_human}}
            </td>
        </tr>
        <tr>
            <td>
                Memory Fragmentation Ratio:
            </td>
            <td>
                {{$pageArray.info.mem_fragmentation_ratio}}
            </td>
        </tr>
        <tr>
            <td>
                Last Save Time:
            </td>
            <td>
                {{if $pageArray.info.last_save_time}}{{'Y-m-d H:i:s'|date:$pageArray.info.last_save_time}}{{else}}{{'Y-m-d H:i:s'|date:$pageArray.info.rdb_last_save_time}}{{/if}}
            </td>
        </tr>
        <tr>
            <td>
                Total Connections Received:
            </td>
            <td>
                {{$pageArray.info.total_connections_received}}
            </td>
        </tr>
        <tr>
            <td>
                Total Commands Processed:
            </td>
            <td>
                {{$pageArray.info.total_commands_processed}}
            </td>
        </tr>
        <tr>
            <td>
                Expired Keys:
            </td>
            <td>
                {{$pageArray.info.expired_keys}}
            </td>
        </tr>
        <tr>
            <td>
                Keyspace Hits:
            </td>
            <td>
                {{$pageArray.info.keyspace_hits}}
            </td>
        </tr>
        <tr>
            <td>
                Keyspace Misses:
            </td>
            <td>
                {{$pageArray.info.keyspace_misses}}
            </td>
        </tr>
        <tr>
            <td>
                System CPU Usage:
            </td>
            <td>
                {{$pageArray.info.used_cpu_sys}}
            </td>
        </tr>
        <tr>
            <td>
                User CPU Usage:
            </td>
            <td>
                {{$pageArray.info.used_cpu_user}}
            </td>
        </tr>
        <tr>
            <td>
                Database Size:
            </td>
            <td>
                {{$pageArray.dbSize}}
            </td>
        </tr>
        <tr>
            <td>
                Last save to disk:
            </td>
            <td>
                {{'Y-m-d H:i:s'|date:$pageArray.lastSave }}
            </td>
        </tr>
    </table>
</span>
{{include file = "admin/redmin/footer.tpl.html"}}
