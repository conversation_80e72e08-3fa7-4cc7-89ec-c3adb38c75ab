<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/3/29
 * Time: 11:29
 */
use \Service\AccountStat\Consts\AccountStat;
use \Common\Utils\Url;

class WxWebBrowserController extends Controller
{
    /**
     * WxWebBrowserController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->startSession();
    }

    /**
     * 微信授权
     * @return void
     */
    public function actionIndex()
    {
        if (isset($_GET['forward'])) {
            $forward = getForwardUrl();
            setcookie('oauth_forward', $forward, 0, '/');
        }
        $midParam = "";
        if (isset($_GET['mid'])) {
            $midParam = "&mid=" . \Common\Utils\Url::getStringParam("mid");
        }
        if (isset($_GET['autoLogin']))
        {
            $midParam .= "&autoLogin=" . intval($_GET['autoLogin']);
        }
        redirect(PASSPORT_HOST . '/oauth/weixin/?callback=/clientapi/WxWebBrowser/callback' . $midParam);
    }

    /**
     * 微信回调
     * @return void
     */
    public function actionCallback()
    {
        $appid = preg_replace('/[^\w_\.\$]/', '', Url::getStringParam("appid"));
        setcookie('oauth_state', null, time() - 3600, '/');
        setcookie('redirect_uri', null, time() - 3600, '/');
        setcookie('oauth_forward', null, time() - 3600, '/');
        if (empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state']) {
            //  返回失败
            redirect('/');
        }
        $wxCode = Url::getStringParam('code');
        //如果没有wxcode  就跳回原来的页面
        if (empty($wxCode)) {
            redirect('/');
        }
        $source = \Common\Utils\Url::getStringParam("mid", "mobile");
        $oauthAction = loadAction('oauth');
        $result = $oauthAction->weixinCallback($wxCode, $appid);
        if ($result) {
            //注意这里需要使用unionid, 同一个开发者账号旗下不同的app
            $openid = $result['unionid'];
            $gender = $result['gender'];
            $nickname = $result['nickname'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'weixin');
            if ($return["passid"] > 0) {
                //收集登录信息
                $st = true;
                \Service\AccountStat\AccountStat::collect(
                    AccountStat::TP_LOGIN,
                    AccountStat::CTP_APP,
                    AccountStat::AC_TP_OAUTH_WEIXIN,
                    $st,
                    $source
                );
                $passid = $return["passid"];
                $result = $memberModel->read($passid);
                if ($result['locked'] == 2) {
                    //用户封禁
                    $this->closeDlg();
                }
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());

                if (in_array($_REQUEST['mid'], Config::get("loginSpecialMid"))) {

                    //删除青鸟修改密码的缓存
                    RedisEx::getInstance()->del("redis:cache:member:update:password:passid:$passid");
                }

                $result["m_uid"] = unserialize($result['m_uid']);
                $uid = $result['m_uid']['1'];
                $username = $result['username'];
                $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
                $loginAction = loadAction('login');
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                $phone = $result['phone'];
                $this->outputLoginCallbackJs($username, $nickname, $phone, $cookie);
            } else {
                $memberModel = loadModel('member');
                $clientIp = get_client_ip();
                $result = $memberModel->regOAuth('weixin', $openid, $nickname, $clientIp, '', $gender);
                //防止变量被覆盖
                $passid = $result['passid'];
                if ($result) {
                    //收集注册信息
                    $st = true;
                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_REG,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_WEIXIN,
                        $st,
                        $source
                    );
                    $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                    $result = $memberModel->read($passid);
                    $result["m_uid"] = unserialize($result['m_uid']);
                    $uid = $result['m_uid']['1'];
                    $username = $result['username'];
                    $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
                    $loginAction = loadAction('login');
                    $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                    $this->outputLoginCallbackJs($username, $nickname, "", $cookie);
                } else {
                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_REG,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_WEIXIN,
                        false,
                        $source
                    );
                    $this->closeDlg(2);
                }
            }
        } else {
            $this->closeDlg();
        }
    }

    /**
     * js关闭窗口
     * @param int $loginType 登录状态
     * @return void
     */
    private function closeDlg($loginType = 1)
    {
        $source = \Common\Utils\Url::getStringParam("mid", "mobile");
        $st = 0;
        if ($loginType == 1) {
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        } else {
            $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
        }
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        $accountType = \Service\AccountStat\Consts\AccountStat::AC_TP_OAUTH_WEIXIN;
        \Service\AccountStat\AccountStat::collect($type, $clientType, $accountType, $st, $source);
        echo '<script type="text/javascript">chrome.sync.closeDlg();</script>';
        exit;
    }

    /**
     * 吐出浏览器的js调用
     * @param string $username 用户名
     * @param string $nickname 昵称
     * @param int    $phone    手机号码
     * @param string $cookie   cookie
     * @return void
     */
    private function outputLoginCallbackJs($username, $nickname, $phone, $cookie)
    {
        $script = '<script type="text/javascript">' .
            'chrome.sync.onLogin("wechat","' . $username . '","' . $nickname . '",0,"","' . $cookie['I'] . '");';

        if (!empty($phone)) {
            $script .= 'if (chrome.sync.hasOwnProperty("setLoginUserPhoneNumber")) {' .
                'chrome.sync.setLoginUserPhoneNumber("' . $phone . '");' .
                '}';
        }
        if (isset($_GET['autoLogin']) && intval($_GET['autoLogin']) === 0)
        {
            $script .= 'chrome.sync.setIsAutoLogin(false);';
        } else {
            $script .= 'chrome.sync.setIsAutoLogin(true);';
        }
        $script .= 'chrome.sync.closeDlg();' .
            '</script>';
        echo $script;
        closeWindow();
    }
}
