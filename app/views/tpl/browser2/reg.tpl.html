<!DOCTYPE HTML>
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=gb2312">
        <title>2345帐号注册</title>
        <link href="/browser2/css/sign_20130806.css" rel="stylesheet" type="text/css">
        <script type="text/javascript" src="/js/ajax.js"></script>
        <script type="text/javascript" src="/js/common.js"></script>
    </head>
    <body scroll="no" style="overflow:hidden;">
        <div class="main">
            <div class="banner"></div>
            <div class="box">
                <div class="popup" id="error_msg" style="display: none;"></div>
                <form action="" method="post">
                    <table class="sign" width="100%" border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <td class="txt1"><div align="right">2345帐号：</div></td>
                            <td width="208">
                                <input tabindex="1" name="username" type="text" class="input1" id="username" onfocus="if(this.value=='可用邮箱作为您的2345帐号' ){this.value='';}select();" onblur="checkUser(1);if(this.value==''){this.value='可用邮箱作为您的2345帐号';}" value="可用邮箱作为您的2345帐号">
                            </td>
                            <td><div align="left"></div></td>
                        </tr>
                        <tr class="h_reg">
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td class="txt1"><div align="right">密码：</div></td>
                            <td>
                                <input onblur="checkPass();" tabindex="2" class="input1" maxlength="16" type="password" name="password" id="password" onfocus="select();">
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr class="h_reg">
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td class="txt1"><div align="right">重复密码：</div></td>
                            <td>
                                <input onblur="checkRepass();" tabindex="3" class="input1" maxlength="16" type="password" name="repassword" id="repassword" onfocus="select();">
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr class="h_reg">
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td class="txt1"><div align="right">邮箱地址：</div></td>
                            <td>
                                <input tabindex="4" name="email" type="text" class="input1" id="email" onfocus="if(this.value=='找回密码用，请填写您的常用邮箱' ){this.value='';}select();" onblur="checkEmail();if(this.value==''){this.value='找回密码用，请填写您的常用邮箱';}" value="找回密码用，请填写您的常用邮箱">
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr class="h_reg">
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td class="txt1"><div align="right">验证码：</div></td>
                            <td>
                                <div align="left">
                                    <table class="code" width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td width="28%"><input onblur="checkValidate();" tabindex="5" class="input2" type="text" name="validate" id="validate" maxlength="4" onfocus="select();"></td>
                                            <td width="24%"><img id="checkImg" src="/randCaptcha.php" onclick="this.src='/randCaptcha.php?'+new Date();"></td>
                                            <td width="48%"><a tabindex="-1" href="#" onclick="$('checkImg').src='/randCaptcha.php?'+new Date();return false;">换一张</a></td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr class="h_sign">
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>
                                <label for="agree"><input tabindex="-1" class="check1" type="checkbox" name="agree" id="agree" checked="checked">我同意</label>
                                <a tabindex="-1" href="/browser2/licence.htm">《服务协议》</a><a tabindex="-1" href="/browser2/declare.htm">《隐私政策》</a>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr class="h_reg2">
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>
                                <div align="left">
                                    <table class="txt2" width="100%" border="0" cellspacing="0" cellpadding="0">
                                        <tr>
                                            <td>
                                                <label for="remember">
                                                    <input tabindex="-1" checked="checked" class="check1" type="checkbox" name="remember" id="remember">
                                                    记住密码
                                                </label>
                                            </td>
                                            <td>
                                                <label for="automatic">
                                                    <input tabindex="-1" checked="checked" class="check1" type="checkbox" name="automatic" id="automatic">
                                                    下次自动登录
                                                </label>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr class="h_sign">
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>
                                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                    <tr>
                                        <td width="68%"><a tabindex="-1" class="sign_btn" href="#" onclick="form_submit();return false;">注 册</a></td>
                                        <td width="32%"><a tabindex="-1" style="text-decoration:underline;" href="#" onclick="window.external.RCCoralShowLoginDlg();return false;">返回</a></td>
                                    </tr>
                                </table>
                            </td>
                            <td>&nbsp;</td>
                        </tr>
                    </table>
                </form>
            </div>
        </div>
        <script type="text/javascript">
            var xhr=null;
            function initXhr(){
                if(window.XMLHttpRequest){
                    try{
                        xhr = new XMLHttpRequest();
                    }catch(e){
                        xhr=false;
                    }
                }else if(window.ActiveXObject){
                    try{
                        xhr = new ActiveXObject("Msxml2.XMLHTTP");
                    }catch(e){
                        try{
                            xhr=new ActiveXObject("Microsoft.XMLHTTP");
                        }catch(e){
                            xhr=false;
                        }
                    }
                }
            }
            initXhr();
            function sendPost(_url,_para,_callBack,_method){
                _callBack = _callBack||function(){};
                _method = _method||"POST";
                if(xhr){
                    xhr.open(_method,_url,true);
                    xhr.onreadystatechange = _callBack;
                    xhr.setRequestHeader("Content-Type","application/x-www-form-urlencoded;");
                    xhr.send(_para);
                }
            }
            window.external.RCCoralSetDlgSize(480,477);
            function show_msg(msg){
                if($("error_msg").innerHTML == ''){
                    $("error_msg").style.display='';
                    $("error_msg").innerHTML = msg;
                }
            }
            function hide_msg(){
                if($("error_msg").innerHTML != ''){
                    $("error_msg").style.display='none';
                    $("error_msg").innerHTML = '';
                }
            }
            function login(username)
            {
                if (username)
                {
                    window.location.href="/browser2/sign?username="+username;
                }
                else
                {
                    window.location.href="/browser2/sign";
                }
            }
            function checkUser(n)
            {
                username = $("username").value.trim();
                if (username.length < 2)
                {
                    show_msg('用户名最少2个字符');
                    return false;
                }
                if (username.length > 24)
                {
                    show_msg('用户名请不要超过24个字符');
                    return false;
                }
                if(/[^\u4E00-\u9FA5\w_@\.]/.test(username))
                {
                    show_msg('用户名请输入汉字，字母，数字，或邮箱地址');
                    return false;
                }
                startrequest('/api/check','type=username&username='+username,1,function (response){
                    if (response == 1)
                    {
                        err = 1;
                    }
                    else if (response == 2)
                    {
                        err = 2;
                    }
                    else
                    {
                        err = 0;
                    }
                });
                if (err == 1)
                {
                    show_msg('此帐号已被注册，请<a tabindex="-1" href="javascript:void(0);" class="blue" onclick="window.external.RCCoralShowLoginDlg();return false;">登录</a>或重新输入');
                    return false;
                }
                else if (err == 2)
                {
                    show_msg('这个2345帐号不适合您，换一个吧');
                    return false;
                }
                if (username != username.toLowerCase())
                {
                    show_msg('登录区分大小写，请牢记您的2345帐号');
                }
                else
                {
                    hide_msg();
                }
                if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username) && n == 1)
                {
                    $("email").value = username;
                }
                return true;
            }
            function checkPass()
            {
                var pass = $("password").value;
                if (pass.length < 6)
                {
                    show_msg('密码最少6个字符');
                    return false;
                }
                if (pass.length > 16)
                {
                    show_msg('密码最多16个字符');
                    return false;
                }
                if (pass == $('username').value)
                {
                    show_msg('密码不能与2345帐号一致，请重新输入');
                    return false;
                }
                if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
                {
                    var score = 0;
                }
                else
                {
                    var score = passwordGrade(pass);
                }
                if (score <= 10)
                {
                    show_msg('密码强度太弱了，请输入更为复杂的密码');
                    return false;
                }
                hide_msg();
                return true;
            }
            function checkPassSame(pass)
            {
                var first = pass.substring(0,1);
                var exp = new RegExp('^'+first+'+$');
                if(exp.test(pass))
                {
                    return false;
                }

                if (first == 'a' || first == 'A')
                {
                    f = pass.charCodeAt(0);
                    for(i = 1; i < pass.length; i++)
                    {
                        tmp = pass.charCodeAt(i);
                        if (tmp - f != i)
                        {
                            return true;
                        }
                    }
                    return false;
                }
                return true;
            }
            function passwordGrade(pwd) {
                var score = 0;
                var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
                var repeatCount = 0;
                var prevChar = '';
                //check length
                var len = pwd.length;
                score += len > 18 ? 18 : len;
                //check type
                for (var i = 0, num = regexArr.length; i < num; i++) { if (eval('/' + regexArr[i] + '/').test(pwd)) score += 4; }
                //bonus point
                for (var i = 0, num = regexArr.length; i < num; i++) {
                    if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2) score += 2;
                    if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5) score += 2;
                }
                //deduction
                for (var i = 0, num = pwd.length; i < num; i++) {
                    if (pwd.charAt(i) == prevChar) repeatCount++;
                    else prevChar = pwd.charAt(i);
                }
                score -= repeatCount * 1;
                return score;
            }
            function checkRepass()
            {
                if($("repassword").value==""){
                    show_msg('重复密码不能为空');
                    return false;
                }
                if ($("repassword").value != $("password").value)
                {
                    show_msg('两次输入密码不一致');
                    return false;
                }
                hide_msg();
                return true;
            }
            function checkEmail()
            {
                email = $("email").value.trim();
                if (email == "" || email == '输入邮箱作为您的2345帐号')
                {
                    show_msg('请输入邮箱');
                    return false;
                }
                if (!/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(email))
                {
                    show_msg('您输入的邮箱格式不正确');
                    return false;
                }
                startrequest('/api/check','type=email&email='+email,1,function(response){
                    if (response == 1)
                        err = 1;
                    else
                        err = 0;
                });
                if (err)
                {
                    show_msg('此邮箱已被注册，请换一个');
                    return false;
                }
                hide_msg();
                return true;
            }
            function checkValidate()
            {
                val = $("validate").value.trim();
                if (val == '')
                {
                    show_msg('请输入4位数字或结果');
                    return false;
                }
                startrequest('/api/check','type=validate&val='+val,1,function(response){
                    if (response == 0)
                        err = 1;
                    else
                        err = 0;
                });
                if(err)
                {
                    show_msg('您输入的验证码错误，请重新输入4位数字或结果');
                    $('checkImg').src='/randCaptcha.php?'+new Date();
                    return false;
                }
                hide_msg();
                return true;
            }
            function checkAll(n)
            {
                if (!checkUser(2))
                {
                    $("username").focus();
                    return false;
                }
                if (!checkPass())
                {
                    $("password").focus();
                    return false;
                }
                if (!checkRepass())
                {
                    $("repassword").focus();
                    return false;
                }
                if (!checkEmail())
                {
                    $("email").focus();
                    return false;
                }
                if (!checkValidate())
                {
                    $("validate").focus();
                    return false;
                }
                if (!$("agree").checked)
                {
                    show_msg("请同意2345服务协议");
                    return false;
                }
                return true;
            }
            function form_submit(){
                if(checkAll(1)){
                    cc("reg_all");
					sendPost("/browser2/reg","username="+$("username").value+
						"&password="+$("password").value+
						"&email="+$("email").value+
						"&validate="+$("validate").value,
					function(){
						if(xhr.readyState==4&&xhr.status==200&&xhr.responseText!="NaN"){
							data = eval("("+xhr.responseText+")");
							switch(data["err"]){
								case 0:
									cc("reg_success");
									window.external.RCCoralOnLogin($("username").value,"",0,"",data["uid"],data["sec"],data['passid'],data['token']);
									if ($("automatic").checked) {
										window.external.RCCoralSetLoginUserInfo($("username").value,$("password").value);
										window.external.RCCoralSetIsAutoLogin(true);
									}else{
										if ($("remember").checked) {
											window.external.RCCoralSetLoginUserInfo($("username").value,$("password").value);
										}else{
											window.external.RCCoralSetLoginUserInfo($("username").value,"");
										}
										window.external.RCCoralSetIsAutoLogin(false);
									}
									break;
								case 1:
									cc("reg_fail");
									show_msg(data['msg']);
									$('checkImg').src='/randCaptcha.php?'+new Date();
									break;
								case 2:
									cc("reg_fail");
									window.external.RCCoralOpenUrl(data["url"],"");
									window.external.RCCoralCloseDlg();
									break;
							}
						}
					});
					return false;
				}
			}
			function loadJs(_url){
				var callback = arguments[1]||function(){};
				var _js = document.createElement("script");
				_js.setAttribute("type", "text/javascript");
				_js.setAttribute("src", _url);
				_js.setAttribute("defer", true);
				if(document.all){
					_js.onreadystatechange = function(){
						if(this.readyState == "loaded" || this.readyState == 4 || this.readyState == "complete"){
							setTimeout(callback,10);
						}
					};
				}else{
					_js.onload = function(){
						callback();
					};
				}
				document.getElementsByTagName("head")[0].appendChild(_js);
			}
			function cc(a) {
				var b = arguments,
				web = "ajax62",
				a2,
				i1 = document.cookie.indexOf("uUiD="),
				i2;
				if (b.length > 1) web = b[1];
				if (i1 != -1) {
					i2 = document.cookie.indexOf(";", i1);
					a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
				}
				if (!a2) {
					a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
					document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
				}
				if (a.length > 0) {
					var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
					loadJs(c);
				}
				return true;
			}
			$("automatic").onclick = function(){
				if(this.checked){
					$("remember").checked = true;
				}
			};
			$("remember").onclick = function(){
				if(!this.checked){
					$("automatic").checked = false;
				}
			};
        </script>
    </body>
</html>