<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：EncodingAction.php
 * 摘    要：数组转码
 * 作    者：杜海明
 * 修改日期：2015.04.17
 * */
class EncodingAction
{

    /**
     * 函数名称：transcoding
     * 参    数：
     * 作    者：杜海明
     * 功    能：数组转码
     * 修改日期：2015-04-15
     */
    public static function transcoding($data, $outputEncode = 'gbk')
    {
        $codingData = array();
        if (is_array($data))
        {
            $codingData = self::iteratorArray($data, $outputEncode);
        }
        else
        {
            if (!empty($data))
            {
                $codingData = self::convertEncoding($data, $outputEncode);
            } else {
                $codingData = "";
            }

        } //print_r($codingData);exit;
        return $codingData;
    }

    /**
     * 函数名称：iteratorArray
     * 参    数：
     * 作    者：杜海明
     * 功    能：数组迭代器
     * 修改日期：2015-04-15
     */
    public static function iteratorArray(&$data, $outputEncode)
    {
        array_walk($data, function (&$item, $key, $outputEncode)
        {
            if (!empty($item))
            {
                if (is_array($item))
                {
                    EncodingAction::iteratorArray($item, $outputEncode); 
                }
                else
                {
                    $item = EncodingAction::convertEncoding($item, $outputEncode); 
                }
            }
        }
        , $outputEncode);
        return $data;
    }
    
    /**
     * 函数名称：convertEncoding
     * 参    数：$data 内容 $outputEncode 转成的编码
     * 作    者：杜海明
     * 功    能：转码
     * 修改日期：2015-04-15
     */
    public static function convertEncoding($data, $outputEncode)
    {
    
        $encode_arr = array(
            'UTF-8',
            'ASCII',
            'GBK',
            'GB2312',
            'BIG5',
            'JIS',
            'eucjp-win',
            'sjis-win',
            'EUC-JP');
        $encoded = mb_detect_encoding($data, $encode_arr);
        return self::checkEncode($data, $encoded) ? mb_convert_encoding($data, $outputEncode,
            $encoded) : $data;
    
    }

    /**
     * 函数名称：checkEncode
     * 参    数：$string 内容 $encoding 校验的编码
     * 作    者：杜海明
     * 功    能：校验编码
     * 修改日期：2015-04-15
     */
    public static function checkEncode($string, $encoding)
    {
        if (mb_check_encoding($string, $encoding) === true)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
}
