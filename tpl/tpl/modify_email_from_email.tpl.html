{{include file="header.tpl.html"}}
<div class="wrap">
	<div class="main clearfix">
		<div class="box mt10">
			<div class="tit clearfix">
				<span class="titleft"></span>
				<span class="titright"></span>
				<h2><a href="edit_info.php">个人资料</a> > 修改绑定邮箱</h2></div>
			<div class="boxbd checkemail clearfix">
				<p class="step">
					<span class="st_1">1.验证身份</span>
                    <span class="st_2">2.输入新邮箱地址</span>
                    <span class="st_3">3.发送邮箱验证码</span>
                    <span class="st_4">4.完成</span>
				</p>
				<form action="" method="post" onsubmit="return checkAll();">
					<input type="hidden" name="cmd" value="modify"/>
					<input type="hidden" name="from" value="email"/>
					<table class="infotable" width="650" border="0" cellspacing="0" cellpadding="0">
						<tr>
							<th>原有绑定邮箱：</th>
							<td width="280">
								<div class="oldemail">
									<font>{{$pageArray.info.displayEmail}}</font>
									<input class="btn5" style="width:100px;" type="button" value="获取验证码" onclick="validate();this.disabled=true;"/>
								</div>
							</td>    
							<td><span id="send" style="display:none;"></span></td>
						</tr>
						<tr>
							<th>验证码：</th>
							<td><input type="text" style="width:260px" class="{{if $pageArray.show_msg == 1}}inbox4 inbox_er{{else}}inbox4{{/if}}" name="code" id="code" value="{{$pageArray.code}}"></td>
							<td><span class="w_er" id="check_msg">{{if $pageArray.show_msg == 1}}{{$pageArray.err_msg}}{{/if}}</span></td>
						</tr>
						<tr>
							<th>新绑定邮箱：</th>
							<td><input type="text" style="width:260px" class="{{if $pageArray.show_msg == 2}}inbox4 inbox_er{{else}}inbox4{{/if}}" name="email" id="email" value="{{$pageArray.email}}" onblur="checkEmail();"></td>
							<td><span class="w_er" id="email_msg">{{if $pageArray.show_msg == 2}}{{$pageArray.err_msg}}{{/if}}</span></td>
						</tr>
						<tr>
							<th>&nbsp;</th>
							<td style="height:50px;"><input type="submit" onmouseout="this.className='btn4'" onmouseover="this.className='btn4_hov'" class="btn4" value="提交">&nbsp;<a href="edit_info.php" style="font-size:14px;">返回</a></td>
							<td>&nbsp;</td>
						</tr>
					</table>
				</form>
			</div>           	
		</div>
	</div> 
</div>
<script type="text/javascript" src="/js/ajax_tt.js"></script>
<script type="text/javascript">
	function $(id){
		return document.getElementById(id);
	}
	function validate(){
		$('send').innerHTML = '验证邮件发送中...';
		$('send').style.display = '';
		var arg = 'cmd=send_check&from=email';
		var url = '/member/modify_email.php';
		startrequest(arg,url,function(){
                        var emailVal = '{{$pageArray.info.displayEmail}}';
                        var emailExp = emailVal.split('@');
                        if(emailExp[1] == 'gmail.com'){
                            var emailHost = 'http://www.' + emailExp[1];
                        }else{
                            var emailHost = 'http://mail.' + emailExp[1];
                        }
			$('send').innerHTML = '验证邮件已发送到您的邮箱，<a href="'+emailHost+'" target="_blank">去邮箱收件</a>';
		});
	}
	function checkEmail()
	{
		var val = $('email').value;
		if (val != '')
		{
			if (!/^[_\.0-9a-z-]+@([0-9a-z][0-9a-z-]+\.)+[a-z]{2,4}$/.test(val))
			{
				$('email_msg').className = "w_er";
				$('email_msg').innerHTML = '请输入正确的邮箱';
				$('email').className = "inbox4 inbox_er";
				return false;
			}
			$('email_msg').className = "";
			$('email_msg').innerHTML = '<img src="/images/check.jpg"/>';
			$('email').className = "inbox4";
		}else{
			$('email_msg').className = "w_er";
			$('email_msg').innerHTML = '邮箱不能为空';
			$('email').className = "inbox4 inbox_er";
			return false;
		}
		return true;
	}
	$("code").onblur = function(){
		startrequest('cmd=check_code&code='+this.value,'/member/modify_email.php',function(msg){
			if(msg==1){
				$("code").className = "inbox4";
				$("check_msg").className = "";
				$("check_msg").innerHTML = '<img src="/images/check.jpg"/>';
			}else{
				$("code").className = "inbox4 inbox_er";
				$("check_msg").className = "w_er";
				$("check_msg").innerHTML = "验证码错误，请重新输入";
			}
		});
	};
	function checkAll(){
		var vd = true;
		if($("code").value == ""){
			$("code").className = "inbox4 inbox_er";
			$("check_msg").className = "w_er";
			$("check_msg").innerHTML = "请输入验证码";
			vd = false;
		}
		if(!checkEmail()){
			vd = false;
		}
		return vd;
	}
</script>
{{include file="footer.tpl.html"}}