<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台-短信发送状态</title>
    <link rel="stylesheet" href="/style/css/global.css">
    <script src="/style/js/jquery-1.8.3.min.js"></script>
    <script src="/style/js/tableFixed.js"></script>
    <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
    <!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a> > <span>短信发送状态</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="javascript:">短信发送状态</a>
        <a href="/Admin/ChannelReturnStatus/Multi">群发短信状态</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->

    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
        <form action="" method="post" id="queryFromId">
            <div class="clearfix">

                <div class="dataWrap clearfix">
                    <em>日期:</em>
                    <div class="dataList w130">
                        <div class="getTime">
                            <input type="text" value="{{$pageArray.startTime}}" autocomplete="off" name="startTime" onclick="WdatePicker()"/><i>日历icon</i>
                        </div>
                    </div>
                </div><!--dataWrap end-->
                <span class="dataTxt">至</span>
                <div class="dataWrap clearfix">
                    <div class="dataList w130">
                        <div class="getTime">
                            <input type="text" value="{{$pageArray.endTime}}" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i>
                        </div>
                    </div>
                </div><!--dataWrap end-->
                <div class="dataWrap ml10 clearfix">
                    <em>手机号码:</em>
                    <input class="ml10 w130" value="{{$pageArray.phone}}" type="text" name="phone"/>
                </div>
            </div>

            <div class="clearfix mt10">
                <span style="margin-left:0px;" class="dataTxt">服务商:</span>
                <div class="selectWrap left w170">
                    <div class="selectInput">
                        <input type="text" readonly="" value="全部" autocomplete="off" class="getListVal" id="selectedOperator"><i>下拉箭头</i>
                    </div>
                    <dl scroll="yes" class="selectList" style="display: none;" id="selectOperatorId">
                        <dd><a href="javascript:" value="0">全部</a></dd>
                        {{foreach from=$pageArray.OperatorList item=OperatorInfo}}
                        <dd><a href="javascript:" value="{{$OperatorInfo.id}}">{{$OperatorInfo.id}}-{{$OperatorInfo.provider_name}}</a></dd>
                        {{/foreach}}
                    </dl>
                    <input type="hidden" value="0" name="operatorId"/>
                </div>

                <span class="dataTxt" style="margin-left:10px">项目:</span>
                <div class="selectWrap left w170">
                    <div class="selectInput">
                        <input type="text" readonly="" value="全部" autocomplete="off" class="getListVal" id="projectSelected"><i>下拉箭头</i>
                    </div>
                    <dl scroll="yes" class="selectList addScroll" style="display: none;" id="selectPid">
                        <dd><a href="javascript:" value="0">全部</a></dd>
                        {{foreach from=$pageArray.projectList key=projectKey item=projectInfo}}
                        <dd><a href="javascript:" value="{{$projectKey}}">{{$projectKey}}-{{$projectInfo}}</a></dd>
                        {{/foreach}}
                    </dl>
                    <input type="hidden" name="projectId" value="0"/>
                </div>

                <span class="dataTxt" style="margin-left:10px">回执状态:</span>
                <div class="selectWrap left w90">
                    <div class="selectInput">
                        <input type="text" readonly="" value="全部" autocomplete="off" class="getListVal" id="ReceiptSelected"><i>下拉箭头</i>
                    </div>
                    <dl scroll="yes" class="selectList" style="display: none;" id="statusReceipt">
                        <dd><a href="javascript:" value="0">全部</a></dd>
                        <dd><a href="javascript:" value="1">成功</a></dd>
                        <dd><a href="javascript:" value="2">失败</a></dd>
                        <dd><a href="javascript:" value="9">无回执</a></dd>
                    </dl>
                    <input type="hidden" name="receiptStatus" value="0"/>
                </div>
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 queryFromSubmit">查询</a>
            </div>
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->

    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>短信ID</th>
                        <th>手机号码</th>
                        <th>服务器IP</th>
                        <th>用户IP</th>
                        <th>服务商</th>
                        <th>项目提交时间</th>
                        <th>项目提交状态</th>
                        <th>发送状态</th>
                        <th>回执时间</th>
                        <th>回执状态</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->

        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th>短信ID</th>
                <th>手机号码</th>
                <th>服务器IP</th>
                <th>用户IP</th>
                <th>服务商</th>
                <th>项目提交时间</th>
                <th>项目提交状态</th>
                <th>发送状态</th>
                <th>回执时间</th>
                <th>回执状态</th>
            </tr>
            {{foreach from=$pageArray.receipList item=receipInfo}}
            <tr title="{{$receipInfo.message}}">
                <td>{{$receipInfo.id}}</td>
                <td>{{$receipInfo.phone}}</td>
                <td>{{$receipInfo.server_ip}}</td>
                <td>{{$receipInfo.client_ip}}</td>
                <td>{{ $pageArray.allOperatorList[$receipInfo.channel]}}</td>
                <td>{{$receipInfo.send_time}}</td>
                <td>{{ if $receipInfo.send_status >= 0}}提交成功{{if $receipInfo.send_status==2}}({{$receipInfo.send_response}}){{/if}} {{else}}失败({{$receipInfo.send_response}}){{/if}}</td>
                <td>{{$receipInfo.codeDesc}}</td>
                <td>{{if !empty($receipInfo.scs_id)}}{{ $receipInfo.add_time }}{{/if}}</td>
                <td>{{if !empty($receipInfo.scs_id)}}{{if $receipInfo.status ==1}}成功{{else}}失败{{/if}}{{/if}}</td>

            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
        {{$pageArray.showPage}}
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->
<script src="/style/js/global.js"></script>
<script>
    $('#selectOperatorId a').click(function () {
        var operatorId = $(this).attr('value');
        $('input[name="operatorId"]').val(operatorId);
    });
    $('#selectOperatorId a').each(function (a, b) {
        var operatorId = $(this).attr('value');
        if (operatorId == '{{$pageArray.operatorId}}') {
            $('input[name="operatorId"]').val(operatorId);
            $('#selectedOperator').val($(this).html());
        }
    });

    $('#selectPid a').click(function () {
        var pId = $(this).attr('value');
        $('input[name="projectId"]').val(pId);
    });

    $('#selectPid a').each(function (a, b) {
        var pid = $(this).attr('value');
        if (pid == '{{$pageArray.projectId}}') {
            $('input[name="projectId"]').val(pid);
            $('#projectSelected').val($(this).html());
        }
    });

    $('#statusReceipt a').click(function () {
        var status = $(this).attr('value');
        $('input[name="receiptStatus"]').val(status);
    });

    $('#statusReceipt a').each(function (a, b) {
        var status = $(this).attr('value');
        if (status == '{{$pageArray.receiptStatus}}') {
            $('input[name="receiptStatus"]').val(status);
            $('#ReceiptSelected').val($(this).html());
        }
    });

    $('.queryFromSubmit').click(function () {
        $('#queryFromId').submit();
    });
</script>
</body>
</html>