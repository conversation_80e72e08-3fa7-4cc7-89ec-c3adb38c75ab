/**
 * Created by LP0167 on 2015/9/10.
 */
document.write('<script type="text/javascript" src="//passport.2345.com/js/client/base.js"></script>');
document.write('<script type="text/javascript" src="//passport.2345.com/js/client/jquery.md5.js"></script>');
document.write('<script type="text/javascript" src="//passport.2345.com/js/client/jsencrypt.min.js"></script>');
document.write('<script type="text/javascript" src="//passport.2345.com/js/client/crypto-js.js"></script>');
document.write('<script type="text/javascript" src="//passport.2345.com/js/client/pubKey.min.js?1"></script>');
if (console === undefined) {
    var console = {
        log: function ($str) {
        },
        debug: function ($str) {
        }
    }
}
var reg = {
    proxyUrl: '',
    errorLog: '',
    isShowCode: false,
    commitParam: new Object(),
    appendParam: new Object(),
    locationUrl: '',
    listenSubmit: true,
    changtip: false,
    uservalid: false,
    imgvalid: false,
    isQqShow: true,
    isWeixinShow: false,
    isThirdAjax: false,
    isAjax : false,
    oldDomain : document.domain,
    init: function () {
        $.ajax({
            url: '//passport.2345.com/webapi/RegCheck',
            type: "GET",
            dataType: 'jsonp',
            jsonp: 'jsoncallback',
            data: '',
            async: false,
            success: function (as) {
            }
        });
        $(".inputTxt input").live({
            focus: function () {
                $(this).parent().addClass("inputTxtFocus");
                $(this).siblings(".sDes").css("display", "none");
                $(this).parent().find('.placeholder').hide();
            },
            blur: function () {
                $(this).parent().prop('class', "inputTxt");
                if ($(this).val() == "") {
                    $(this).siblings(".sDes").css("display", "block");
                    $(this).parent().find('.placeholder').show();
                }
            }
        });
        $("input[name='username']").live({
            blur: function () {
                reg.checkUserName();
            }
        });
        $("input[name='password']").live({
            blur: function () {
                reg.safe('blur');
            }
        });
        $("input[name='repassword']").live({
            blur: function () {
                reg.checkRepass();
            }
        });
        $("input[name='agree']").live({
            change: function () {
                reg.checkAgree();
            }
        });
        $("input[name='validate']").live({
            focus: function () {
                $("#msg_validate").hide();
            }
        });
        $("#usercenter_pic").live({
            click: function () {
                $("#usercenter_pic").prop("src", "//passport.2345.com/captcha.php?I" + Math.random());
            }
        });
        $("#usercenter_a").live({
            click: function () {
                $("#usercenter_pic").prop("src", "//passport.2345.com/captcha.php?I" + Math.random());
            }
        });
        if (reg.listenSubmit) {
            $(".btn-submit").live({
                click: function () {
                    reg.submitForm();
                }
            });
        }
        $('.placeholder').click(function () {
            $(this).hide();
            $(this).prev('input').focus();
        });

        $('.thirdPartyReg').click(function () {

            var thirdQqCallLocation = reg.getThirdLocationUrl("qq");
            var type = $(this).attr('type');
            switch (type)
            {
                case 'qq':
                    var link = '//login.2345.com/qq?forward=' + thirdQqCallLocation;
                    break;
                default:
                    link = '';
                    break;
            }
            if (link === '')
            {
                return false;
            }
            if (reg.isThirdAjax)
            {
                reg.thirdExeName(link);
            } else
            {
                window.location = link;
            }
        });
        $("input").keypress(function (event){
            var key = event.which;
            if( key == 13){
                reg.submitForm();
            }
        });
    },
    setDomain : function (doamainName)
    {
        this.getDomainName = doamainName;
    },
    closeListenSubmit: function ()
    {
        this.listenSubmit = false;
    },
    //错误提示
    showErrorTips: function (tag, errMsg) {
        if (this.changtip)
        {
            $("#msg_all").find('span').prop('class', 'sError').html(errMsg);
            $("#msg_all").show();
            return;
        }
        var dom = $(tag);
        dom.prev('.inputTxt').addClass('inputTxtError');
        dom.find('span').prop('class', 'sError').html(errMsg);
        dom.show();
    },
    changeOriginTips: function ()
    {
        this.changtip = true;
    },
    AddAllTips: function ()
    {
        var str = '';
        if (this.changtip)
        {
            str += '<li class="alltips"><div class="formCon"><div class="tips" id="msg_all"><span class="sDes"></span></div> </div> </li> ';
        }
        return str;
    },
    //隐藏错误提示
    hideErrorTips: function (tag) {
        if (this.changtip)
        {
            $("#msg_all").find('span').prop('class', 'sDes').html('');
            ;
            $("#msg_all").hide();
            return;
        }
        var dom = $(tag);
        dom.prev('.inputTxt').removeClass('inputTxtError');
        dom.find('span').prop('class', 'sDes').html('');
        dom.hide();
    },
    //普通提示
    showTips: function (tag, message) {
        var dom = $(tag);
        dom.find('span').prop('class', 'sDes').html(message);
        dom.show();
    },
    safe: function (eName) {
        var isvalid = true;
        var msg_pwd = '';
        var pass = $("#usercenter_password").val();
        if (pass.length > 16) {
            msg_pwd = '\u6700\u591a16\u4e2a\u5b57\u7b26';
            isvalid = false;
        }
        try {
            var passscore = zxcvbn(pass);
            var score = passscore.score;
        } catch (err)
        {
            var longScore = reg.getPwdScore(pass);
            if (longScore <= 10)
            {
                score = 0;
            } else if (longScore >= 11 && longScore <= 20)
            {
                score = 1;
            } else if (longScore >= 21 && longScore <= 30)
            {
                score = 2;
            } else if (longScore >= 31 && longScore <= 40)
            {
                score = 3;
            } else if (longScore >= 41)
            {
                score = 4;
            }
        }
        if (score <= 0) {
            pass == '' ? msg_pwd = '\u5bc6\u7801\u4e0d\u80fd\u4e3a\u7a7a' : msg_pwd = '\u5bc6\u7801\u5f3a\u5ea6\u4e0d\u80fd\u4e3a\u5f31';
            isvalid = false;
        }
        if (eName === 'blur') {
            if (isvalid === true) {
                reg.hideErrorTips('#msg_pwd');

                if ($("#usercenter_password").val() != $("#usercenter_repassword").val() && $("#usercenter_repassword").val() != '') {
                    reg.showErrorTips("#msg_repassword", '\u4e24\u6b21\u8f93\u5165\u5bc6\u7801\u4e0d\u4e00\u81f4');
                }

            } else {
                reg.showErrorTips('#msg_pwd', msg_pwd);
            }
        }
        return isvalid;
    },
    checkPassSame: function (pass)
    {
        var first = pass.substring(0, 1);
        var exp = new RegExp('^' + first + '+$');
        if (exp.test(pass))
        {
            return false;
        }
        if (first == 'a' || first == 'A')
        {
            f = pass.charCodeAt(0);
            for (i = 1; i < pass.length; i++)
            {
                tmp = pass.charCodeAt(i);
                if (tmp - f != i)
                {
                    return true;
                }
            }
            return false;
        }
        return true;
    },
    passwordGrade: function (pwd)
    {
        var score = 0;
        var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
        var repeatCount = 0;
        var prevChar = '';
        //check length
        var len = pwd.length;
        score += len > 18 ? 18 : len;
        //check type
        for (var i = 0, num = regexArr.length; i < num; i++) {
            if (eval('/' + regexArr[i] + '/').test(pwd))
                score += 4;
        }
        //bonus point
        for (var i = 0, num = regexArr.length; i < num; i++) {
            if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
                score += 2;
            if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
                score += 2;
        }
        //deduction
        for (var i = 0, num = pwd.length; i < num; i++) {
            if (pwd.charAt(i) == prevChar)
                repeatCount++;
            else
                prevChar = pwd.charAt(i);
        }
        score -= repeatCount * 1;
        return score;
    },
    getPwdScore: function (pass)
    {
        var score = 0;
        if (pass.length < 6)
        {
            score = 0;
        } else if (pass == '123456' || pass == '654321' || pass == '111222' || reg.checkPassSame(pass) == false)
        {
            score = 0;
        } else
        {
            score = reg.passwordGrade(pass);
        }
        return score;

    },
    checkRepass: function () {
        if ($("#usercenter_password").val() == '') {
            return;
        }

        if ($("#usercenter_repassword").val() != $("#usercenter_password").val()) {
            reg.showErrorTips("#msg_repassword", '\u4e24\u6b21\u8f93\u5165\u5bc6\u7801\u4e0d\u4e00\u81f4');
            return false;
        } else {
            reg.hideErrorTips("#msg_repassword");
            return true;
        }

    },
    checkAgree: function () {
        if (!$("#agree").is(':checked')) {
            reg.showErrorTips("#msg_agree", '\u8bf7\u540c\u610f2345\u670d\u52a1\u534f\u8bae\u3001\u9690\u79c1\u58f0\u660e');
            return false;
        } else {
            reg.hideErrorTips("#msg_agree");
            return true;
        }
    },
    isHtml5: function () {
        if (window.applicationCache) {
            return true;
        } else {
            return false;
        }
    },
    submitForm: function () {
        if (!reg.checkUserName() && !(reg.uservalid === true)) {
            return false;
        }
        if (!reg.safe('blur')) {
            return false;
        }
        if (!reg.checkRepass()) {
            return false;
        }
        if (!reg.checkAgree()) {
            return false;
        }
        $('input[name="currtime"]').val(parseInt(new Date().getTime() / 1000));
        var getHost = window.location.host;
        var getCurrentUrl = window.location;
        var deDataObj = {
            "domain": getHost,
            "currentUrl": getCurrentUrl,
        };
        var encrypt = new JSEncrypt();
        var pubKey = regPubIndex[Math.floor(Math.random() * regPubIndex.length)];
        var aesKey = "";
        for(var i = 0; i < 16; i++){
            aesKey += Math.floor(Math.random() * 16).toString(16);
        }
        aesKey = aesKey.toUpperCase();
        encrypt.setPublicKey(regPubKey[pubKey]);
        $.each($('#myForm').serializeArray(), function(i, obj) {
            if (obj.name === 'password' || obj.name === 'repassword') {
                deDataObj[obj.name] = MD5(obj.value);
            } else {
                deDataObj[obj.name] = obj.value;
            }
        });
        var key = encrypt.encrypt(aesKey);
        var enData = CryptoJS.AES.encrypt(
            CryptoJS.enc.Utf8.parse(JSON.stringify(deDataObj)),
            CryptoJS.enc.Utf8.parse(aesKey), {
                iv: CryptoJS.enc.Utf8.parse("0123456789012345"),
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            }).ciphertext.toString(CryptoJS.enc.Base64);
        var sign = MD5('keyVersion=' + pubKey + '&encryptKey=' + key + '&encryptData=' + enData);
        window.submitData = 'keyVersion=' + pubKey + '&encryptKey=' + encodeURIComponent(key) + '&encryptData=' + encodeURIComponent(enData) + "&sign=" + sign;
        $.ajax({
            url: '//passport.2345.com/webapi/RegCheck/Token',
            type: "GET",
            dataType: 'jsonp',
            jsonp: 'jsoncallback',
            data: '',
            async: false,
            timeout : 1000,
            success: function (as) {
                console.debug(as);
            },
            error : function (XMLHttpRequest, textStatus, errorThrown)
            {

            }
        });
    },
    checkRegToken : function (data)
    {
        window.submitData += '&identityId=' + data.token;
        if (reg.isHtml5())
        {
            var win = document.getElementById("usercenter_regProxy").contentWindow;
            var protocal = document.location.protocol;
            win.postMessage(submitData, protocal + "//passport.2345.com");
        } else
        {
            reg.createProxy();
        }
    },
    checkUserName: function () {
        reg.uservalid = true;
        var msg_username = '';
        var username = $('input[name="username"]').val();
        if (username == '') {
            msg_username = '\u7528\u6237\u540d\u4e0d\u80fd\u4e3a\u7a7a';
            reg.uservalid = false
        }
        if (username != '' && username.length < 2) {
            msg_username = '\u6700\u5c112\u4e2a\u5b57\u7b26';
            reg.uservalid = false
        }
        if (username.replace(/[^\x00-\xff]/g, "**").length > 24) {
            msg_username = '\u8bf7\u4e0d\u8981\u8d85\u8fc724\u4e2a\u5b57\u7b26';
            reg.uservalid = false
        }
        if (/^1[0123456789]\d{9}$/.test(username)) {
            msg_username = '\u4e0d\u5141\u8bb8\u624b\u673a\u53f7\u6ce8\u518c';
            reg.uservalid = false;
        }
        if (!reg.uservalid) {
            reg.showErrorTips('#msg_username', msg_username);
            return false;
        }
        var type = 'username';
        var typeName = '\u7528\u6237\u540d';

        if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username)) {
            type = 'email';
            typeName = '\u90ae\u7bb1';
        }
        if (type == 'email')
        {
            $('#reg_type').val('email');
        } else
        {
            $('#reg_type').val('username');
        }
        if (type == 'email') {
            jQuery.ajax({
                url: "//passport.2345.com/api/check/jsonp",
                data: 'type=' + type + '&value=' + username + '&status=0',
                async: false,
                dataType: 'jsonp',
                success: function (response) {
                    err = response;
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR.status);
                },
                timeout: 3000
            });
        } else {
            jQuery.ajax({
                url: "//passport.2345.com/api/check/jsonp",
                data: 'type=' + type + '&value=' + username,
                async: false,
                dataType: 'jsonp',
                jsonp: 'callback',
                success: function (response) {
                    err = response;
                    if (err == 1) {
                        reg.uservalid = false;
                        msg_username = '\u8fd9\u4e2a' + typeName + '\u5df2\u7ecf\u88ab\u4f7f\u7528\uff0c\u6362\u4e00\u4e2a\u5427';

                    } else if (err == 2) {
                        reg.uservalid = false;
                        msg_username = '\u8fd9\u4e2a2345\u5e10\u53f7\u4e0d\u9002\u5408\u60a8\uff0c\u6362\u4e00\u4e2a\u5427';

                    }
                    if (typeof err === "undefined") {
                        reg.showErrorTips('#msg_username', '\u60a8\u7684\u6ce8\u518c\u53ef\u80fd\u8fc7\u4e8e\u9891\u7e41\uff0c\u82e5\u6709\u7591\u95ee\u8bf7\u8054\u7cfb\u5ba2\u670d\uff01');
                        return false;
                    }
                    if (type == 'username') {
                        if (err == 1) {
                            reg.uservalid = false;
                            msg_username = '\u8fd9\u4e2a' + typeName + '\u5df2\u7ecf\u88ab\u4f7f\u7528\uff0c\u6362\u4e00\u4e2a\u5427';

                        } else if (err == 2) {
                            reg.uservalid = false;
                            msg_username = '\u8fd9\u4e2a2345\u5e10\u53f7\u4e0d\u9002\u5408\u60a8\uff0c\u6362\u4e00\u4e2a\u5427';

                        }
                    } else {
                        if (err != 0) {
                            reg.uservalid = false;
                            msg_username = '\u8fd9\u4e2a' + typeName + '\u5df2\u7ecf\u88ab\u4f7f\u7528\uff0c\u6362\u4e00\u4e2a\u5427';
                        }

                    }
                    if (reg.uservalid === true) {
                        if (username !== username.toLowerCase()) {
                            msg_username = '\u767b\u5f55\u533a\u5206\u5927\u5c0f\u5199\uff0c\u8bf7\u7262\u8bb0\u60a8\u7684\u5e10\u53f7';
                        } else {
                            msg_username = '';
                        }
                        reg.showTips('#msg_username', msg_username);
                        return true;
                    } else {
                        reg.showErrorTips('#msg_username', msg_username);
                        return false;
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR.status);
                },
                timeout: 10000
            });
        }
    },
    checkRegStatus: function (data) {
        var code = 0;
        if (data.display) {
            reg.isShowCode = data.display;
            reg.showCodeImg();
            code = 1;
        }
        try
        {
            if (typeof(eval(AdSetSize)) == "function")
            {
                AdSetSize(code);
            }
        }
        catch(e)
        {}
    },
    showCodeImg: function () {
        d = new Date();
        $('.codeImg').show();
        $('.ulForm').addClass('showcode');
        //修复注册时，不出现错误提示的bug
        if ($.trim($('#msg_validate').html()) !== '') {
            var html = '<span class="sTit">\u9a8c\u8bc1\u7801\uff1a</span> <div class="formCon loginCode"><div class="inputTxt"><input id="validate" name="validate" type="text" value="" autocomplete="off"><span class="placeholder" style="display: block;">\u8f93\u5165\u9a8c\u8bc1\u7801</span> <i class="iRight"></i></div><div class="codePic"><span class="sPic"><img src="//passport.2345.com/captcha.php" alt="\u770b\u4e0d\u6e05\uff0c\u6362\u4e00\u5f20" id="usercenter_pic" width="88" height="40"></span><a href="#" id="usercenter_a" class="right">\u6362\u4e00\u6362</a></div><div class="tips" id="msg_validate"><span class="sDes">' + $('#msg_validate').html() + '</span> </div>';

        } else {
            var html = '<span class="sTit">\u9a8c\u8bc1\u7801\uff1a</span> <div class="formCon loginCode"><div class="inputTxt"><input id="validate" name="validate" type="text" value="" autocomplete="off"><span class="placeholder" style="display: block;">\u8f93\u5165\u9a8c\u8bc1\u7801</span> <i class="iRight"></i></div><div class="codePic"><span class="sPic"><img src="//passport.2345.com/captcha.php" alt="\u770b\u4e0d\u6e05\uff0c\u6362\u4e00\u5f20" id="usercenter_pic" width="88" height="40"></span><a href="#" id="usercenter_a" class="right">\u6362\u4e00\u6362</a></div><div class="tips" id="msg_validate"><span class="sDes"></span> </div>';

        }
        $('.codeImg').html(html);

        $("#validate").keypress(function (event){
            var key = event.which;
            if( key == 13){
                reg.submitForm();
            }
        });
    },
    setProxy: function (proxyUrl) {
        this.proxyUrl = proxyUrl;
    },
    setQqShow: function (flag) {
        this.isQqShow = flag;
    },
    setWeixinShow: function (flag) {
        this.isWeixinShow = flag;
    },
    setLocation: function (url)
    {
        this.locationUrl = url;
    },
    setThirdLocation: function (obj)
    {
        this.thirdLocationUrl = obj;
    },
    //设置提交的参数
    setCommitParam: function (data)
    {
        this.commitParam = data;
    },
    //追加提交参数
    appendCommitParam: function (data)
    {
        this.appendParam = data;
        var appendstr = '';
        for (var key in this.appendParam) {
            appendstr += '<input type="hidden" value="' + this.appendParam[key] + '" name="' + key + '">';
        }
        $("#myForm").append(appendstr);
    },
    getOtherParamHtml: function () {
        var str = '<input type="hidden" value="' + this.commitParam['serverCall'] + '" name="serverCall">';
        return str;
    },
    showReg: function (tag) {
        if (this.proxyUrl == '') {
            reg.showErrorTips('body', '\u4ee3\u7406\u9875\u9762\u4e0d\u80fd\u4e3a\u7a7a');
            return false;
        }
        $(tag).append(this.getRegHtml());
        this.init();
        if (this.isHtml5()) {
            this.createProxy();
            window.addEventListener("message", function (e) {
                reg.response(e.data);
            }, false);
        }
    },
    response: function (data)
    {
        var msgs = {
            "300": {
                'username': {
                    '0': '2345\u5e10\u53f7\u6700\u5c112\u4e2a\u5b57\u7b26 @msg_username',
                    '1': '2345\u5e10\u53f7\u8bf7\u4e0d\u8981\u8d85\u8fc724\u4e2a\u5b57\u7b26 @msg_username',
                    '2': '2345\u5e10\u53f7\u8bf7\u8f93\u5165\u6c49\u5b57\uff0c\u5b57\u6bcd\uff0c\u6570\u5b57\uff0c\u6216\u90ae\u7bb1\u5730\u5740 @msg_username',
                    '3': '\u5bc6\u7801\u6700\u5c116\u4e2a\u5b57\u7b26 @msg_pwd',
                    '4': '\u5bc6\u7801\u6700\u591a16\u4e2a\u5b57\u7b26 @msg_pwd',
                    '5': '\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u90ae\u7bb1 @msg_username',
                    '6': '\u6b64\u5e10\u53f7\u5df2\u88ab\u6ce8\u518c\uff0c\u8bf7\u4fee\u65392345\u5e10\u53f7 @msg_username',
                    '7': '\u6b64\u90ae\u7bb1\u5df2\u88ab\u6ce8\u518c\uff0c\u8bf7\u6362\u4e00\u4e2a @msg_username'
                },
                'phone': {
                    '0': '\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u624b\u673a\u53f7\u7801 @msg_username',
                    '1': '\u5bc6\u7801\u6700\u5c116\u4e2a\u5b57\u7b26 @msg_pwd',
                    '2': '\u5bc6\u7801\u6700\u591a16\u4e2a\u5b57\u7b26 @msg_pwd',
                    '3': '\u6b64\u624b\u673a\u53f7\u5df2\u88ab\u6ce8\u518c @msg_username'
                },
                'email': {
                    '0': '\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u90ae\u7bb1\u5730\u5740 @msg_username',
                    '1': '\u5bc6\u7801\u6700\u5c116\u4e2a\u5b57\u7b26 @msg_pwd',
                    '2': '\u5bc6\u7801\u6700\u591a16\u4e2a\u5b57\u7b26 @msg_pwd',
                    '3': '\u6b64\u90ae\u7bb1\u53f7\u5df2\u88ab\u6ce8\u518c @msg_username'
                }
            },
            "400": {
                '0': '\u975e\u6cd5\u57df\u540d\u8c03\u7528',
                '1': '\u975e\u6cd5IP\u8c03\u7528',
                '2': '\u6279\u91cf\u5237CHECK',
                '3': 'IP\u6bb5\u88ab\u7981\u6b62',
                '4': 'IP\u88ab\u7981\u6b62',
                '5': '\u672a\u9a8c\u8bc1\u901a\u8fc7\uff08\u7f3a\u5c11isValidate\uff09',
                '6': '\u7f51\u7edc\u5f02\u5e38',
            }
        };
        data = eval("(" + data + ")");
        try
        {
            data.msgCode;
        }
        catch(error)
        {
            data;
        }
        if (typeof data.msgCode !== "undefined" && data.msgCode !== '')
        {
            var regType = $('#reg_type').val();
            var codeArr = data.msgCode.split('.');
            if (codeArr[0] == '400')
            {
                reg.showErrorTips("#msg_agree", msgs['400'][codeArr[1]] + '\uff0c\u82e5\u6709\u7591\u95ee\u8bf7\u8054\u7cfb\u5ba2\u670d\uff01');
            } else if (codeArr[0] == '300')
            {
                msgArr = msgs['300'][regType][codeArr[1]].split('@');
                reg.showErrorTips("#" + msgArr[1], msgArr[0]);
            }
            return false;
        }
        if (typeof data.msg !== "undefined" && data.msg !== '')
        {
            msgArr = data.msg.split('@');
            if (typeof msgArr[1] === "undefined")
            {
                console.log(msgArr[0]);
            } else
            {
                reg.showErrorTips("#" + msgArr[1], msgArr[0]);
                if(msgArr[1] == 'msg_validate')
                {
                    reg.showCodeImg();
                }
            }
            return false;
        }
        if (typeof data.forwardPage === "undefined" || data.forwardPage === '')
        {
            data.forwardPage = '/reg.php';
        }
        if (typeof data.loadPage !== "undefined" && data.loadPage !== '')
        {
            setTimeout(function () {
                reg.locationPage(data);
            }, 1000);
            $.getScript(data.loadPage, function () {
                reg.locationPage(data);
            });
        } else
        {
            reg.locationPage(data)
        }
    },
    getThirdLocationUrl: function (type) {
        var url = '';
        switch (typeof (this.thirdLocationUrl)) {
            case 'string':
                url = this.thirdLocationUrl;
                break;
            case 'object' :
                if (type === "qq") {
                    url = (typeof this.thirdLocationUrl['qq']) !== 'undefined' ? this.thirdLocationUrl['qq'] : this.locationUrl;
                }
                else if (type === "weixin") {
                    url = (typeof this.thirdLocationUrl['weixin']) !== 'undefined' ? this.thirdLocationUrl['weixin'] : this.locationUrl;
                }
                break;
            default:
                url = this.locationUrl;
        }

        return url;
    },
    //微信登录
    setWeixinQrImg: function (wxQrImgId, cssUrl, style) {
        !function (a, b) {
            function d(a) {
                var e, c = b.createElement("iframe"),
                    d = "https://open.weixin.qq.com/connect/qrconnect?appid=" + a.appid + "&scope=" + a.scope + "&redirect_uri=" + a.redirect_uri + "&state=" + a.state + "&login_type=jssdk";
                d += a.style ? "&style=" + a.style : "";
                d += a.href ? "&href=" + a.href : "";
                c.src = d;
                c.frameBorder = "0";
                c.allowTransparency = "true";
                c.scrolling = "no";
                e = b.getElementById(a.id);
                var width = e.getAttribute("data-width"), height = e.getAttribute("data-height");
                c.width = width ? width : "150px", c.height = height ? height : "140px";
                e.innerHTML = "", e.appendChild(c)
            }

            a.WxLogin = d
        }(window, document);

        // 参考https://passport.2345.com/css/wx-qr-login.css
        if(!cssUrl) {
            cssUrl = "";
        }

        $.ajax({
            url: "//passport.2345.com/weixin/jsLoginQr/",
            data: 'forward=' + this.getThirdLocationUrl("weixin"),
            async: false,
            dataType: 'jsonp',
            success: function (response) {
                if (response) {
                    var obj = new WxLogin({
                        id: wxQrImgId,
                        appid: response.data.appid,
                        scope: 'snsapi_login',
                        redirect_uri: response.data.redirectUri,
                        state: response.data.state,
                        style: style,
                        href: cssUrl
                    });
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.log(jqXHR.status);
            },
            timeout: 3000
        });
    },
    setThirdExeFunc: function (funcName)
    {
        this.thirdExeName = funcName;
    },
    exeFunc : function ( funcName )
    {
        this.exeFuncName = funcName;
    },
    locationPage: function (data)
    {
        if (reg.isAjax)
        {
            reg.exeFuncName();
        }
        else
        {
            if (typeof (data.location) != "undefined")
            {
                window.location = data.location;
            } else if (this.locationUrl != '')
            {
                window.location = this.locationUrl;
            } else
            {
                window.location.reload();
            }
        }

    },
    getRegHtml: function () {
        return '<form id="myForm">' + this.getOtherParamHtml() + '<ul class="ulForm clearfix"> ' + this.AddAllTips() +
            '<li> <span class="sTit">2345\u5e10\u53f7\uff1a</span> <div class="formCon"> <div class="inputTxt"> <input type="text" name="username" value=""> <span class="placeholder" style="display: block;">2345\u5e10\u53f7</span></div> <div class="tips" id="msg_username"><span class="sDes">2-24\u4e2a\u5b57\u7b26\u7684\u5b57\u6bcd\uff0c\u6570\u5b57\uff0c\u4e0b\u5212\u7ebf</span></div> </div> </li> ' +
            '<input type="hidden"  name="currtime"/><input name="reg_type" type="hidden" id = "reg_type" value="username" />' +
            '<li> <span class="sTit">\u5e10\u53f7\u5bc6\u7801\uff1a</span> <div class="formCon"> <div class="inputTxt"> <input type="password" name="password" id="usercenter_password" maxlength="16" autocomplete="off" value=""> <span class="placeholder" style="display: block;">\u5e10\u53f7\u5bc6\u7801</span></div> <div class="tips" id="msg_pwd"><span class="sDes">\u5bc6\u7801\u4e3a6-16\u4e2a\u9664\u7a7a\u683c\u5916\u7684\u4efb\u610f\u5b57\u7b26</span></div> </div> </li>' +
            '<li id="verifypwd"> <span class="sTit">\u786e\u8ba4\u5bc6\u7801\uff1a</span><div class="formCon"> <div class="inputTxt"> <input type="password" name="repassword" id="usercenter_repassword" maxlength="16" autocomplete="off" value=""><span class="placeholder" style="display: block;">\u91cd\u590d\u5bc6\u7801</span></div> <div class="tips" id="msg_repassword"><span class="sDes"></span></div> </div> </li> ' +
            '<li class="codeImg" style="display:none"></li>' +
            ' <li class="checkTxt"> <span class="sTit">&nbsp;</span><div class="tips" id="msg_agree" style="display: none"><span class="sDes"></span></div> <div class="formCon"><span class="sCheck"><input type="checkbox" class="checkbox" checked id="agree" name="agree"><label for="agree">\u6211\u540c\u610f<a href="//passport.2345.com/licence.html" target="_blank">\u300a\u670d\u52a1\u534f\u8bae\u300b</a>\u3001<a href="//passport.2345.com/declare.html" target="_blank">\u300a\u9690\u79c1\u58f0\u660e\u300b</a></label></span> </div> </li> ' +
            '<li> <span class="sTit">&nbsp;</span> <div class="formCon"> <input type="button" class="btnStyle btn-submit" value="\u6ce8\u518c2345\u5e10\u53f7">' +
            '<div class="otherStyle"> ' +
            (reg.isQqShow ? '<a href="javascript:void(0);" class="blueBtn thirdPartyReg" type="qq"><i class="g-icon-qq"></i>QQ登录</a>' : '') +
            (reg.isWeixinShow? '<a href="javascript:void(0);" class="thirdPartyLogin login-weixin" type="weixinJs"><i class="g-icon-weixin"></i><em class="qqTxthide">微信登录</em></a>' : '' ) +
            '</div>' +
            '</div> </li> </ul> </form>';
    },
    createProxy: function () {
        var code = new Base64();
        if (this.isHtml5()) {
            $('body').append('<iframe id="usercenter_regProxy" style="display:none" src="//passport.2345.com/proxyreg.html#' + code.encode(this.proxyUrl) + '"></iframe>');
        } else {
            var proxyHash = code.encode(this.proxyUrl);
            var param = '';
            if ( this.oldDomain != document.domain )
            {
                this.getDomainName = document.domain;
            }
            if ( typeof this.getDomainName != 'undefined')
            {
                proxyHash = code.encode(this.proxyUrl + '?domain=' + this.getDomainName);
                param = '?domain='+this.getDomainName;
            }
            $('body').append('<iframe id="usercenter_regProxy" style="display:none" src="' + this.proxyUrl + param + '#' + proxyHash + '"></iframe>');
        }
    }

};