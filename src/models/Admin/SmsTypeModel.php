<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：TypeModel.php
 * 摘    要：sms短信类型
 * 作    者：zhangchao
 * 修改日期：2016.01.18
 * */

namespace Admin;

class SmsTypeModel extends \Model
{

    public static $smsTypeTable = 'my_sms_config';

    /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }


    /**
     * @param string $typeId 类型ID
     * @param string $orderBy 排序键值
     * @param string $orderByType asc or desc
     * @return array|bool
     */
    public function getTypeList($typeId = null, $orderBy = 'display_order', $orderByType = 'desc')
    {
        $col = 'id,sms_type,sms_name,mobile_provider_id,telecom_provider_id,unicom_provider_id,createdate';
        $where = 'WHERE 1';
        if ($typeId !== null)
        {
            $where = "WHERE id = {$typeId}";
        }
        $orderBy = ' ORDER BY ' . $orderBy . ' ' . $orderByType;
        $sql = "SELECT {$col} FROM `my_sms_config` $where" . $orderBy;
        return $this->pdo->findAll($sql);
    }

    /**
     * @param array $data data
     *
     * @return bool
     */
    public function addSmsType($data)
    {
        $insertFields = array(
            'sms_type',
            'sms_name',
            'mobile_provider_id',
            'telecom_provider_id',
            'unicom_provider_id',
            'is_restrict',
            'restrict_nums',
            'percent',
            'createdate',
            'monitor_config',
        );
        $insertData = array();
        $data['createdate'] = date('Y-m-d H:i:s');
        foreach ($insertFields as $insertField)
        {
            if (isset($data[$insertField]))
            {
                $insertData[$insertField] = $data[$insertField];
            }
        }
        $isTrue = $this->pdo->insert(self::$smsTypeTable, $insertData);
        if ($isTrue && $data['sms_type'] == 0)
        {
            $isTrue = $this->pdo->query("UPDATE " . self::$smsTypeTable . " SET `sms_type` = `id` WHERE `sms_type`=:sms_type", array(':sms_type' => 0));
        }

        return $isTrue !== false ? true : false;
    }


    /**
     * @param array $data data
     *
     * @return bool
     */
    public function editSmsType($data)
    {
        $condition = array('where' => ' id = :id ', 'params' => array(':id' => $data['id']));

        $updateFields = array(
            'sms_name',
            'mobile_provider_id',
            'telecom_provider_id',
            'unicom_provider_id',
            'is_restrict',
            'restrict_nums',
            'percent',
            'createdate',
            'monitor_config',
        );
        $update = array();
        $data['createdate'] = date('Y-m-d H:i:s');
        foreach ($updateFields as $updateField)
        {
            if (isset($data[$updateField]))
            {
                $update[$updateField] = $data[$updateField];
            }
        }

        $isTrue = $this->pdo->update(self::$smsTypeTable, $update, $condition);

        return $isTrue === false ? false : true;
    }

    /**
     * @param int $id id
     *
     * @return bool
     */
    public function delSmsType($id)
    {
        $condition = array('where' => ' id = :id ', 'params' => array(':id' => $id));

        $isTrue = $this->pdo->delete(self::$smsTypeTable, $condition);

        return $isTrue === false ? false : true;
    }

    /**
     * User: panj
     *
     * @param array  $data 参数
     * @param string $orderBy 排序键值
     * @param string $orderByType 排序方式
     *
     * @return array|bool
     */
    public function getSmsTypeList($data = array(), $orderBy = 'display_order', $orderByType = 'DESC')
    {
        $where = '';
        $paramArr = array();
        $sql = "SELECT * FROM `my_sms_config` ";

        if (isset($data['child']))
        {
            if ($data['child'] == 'only')
            {
                $where .= " AND `id` != `sms_type`";
            }
            elseif ($data['child'] != 'show')
            {
                $where .= " AND `id` = `sms_type`";
            }
        }
        else
        {
            $where .= " AND `id` = `sms_type`";
        }

        if (isset($data['type']))
        {
            $where .= " AND sms_type = :type ";
            $paramArr[':type'] = $data['type'];
        }
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, ' AND');
        }

        $orderByType = is_null($orderByType) ? 'DESC' : $orderByType;
        $orderBy = ' ORDER BY ' . $orderBy. ' ' .$orderByType;
        $sql .= $where;
        $sql .= $orderBy;
        if (isset($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    /**
     * User: panj
     *
     * @param array $data data
     *
     * @return bool
     */
    public function changeDisplayOrder($data)
    {
        if (empty($data) || !is_array($data))
        {
            return false;
        }
        foreach ($data as $id => $item)
        {
            $data[$id] = intval($item);
        }
        $when = '';
        foreach ($data as $id => $order)
        {
            $when .= ' WHEN ' . $id . ' THEN ' . $order . PHP_EOL;
        }
        $sql = " UPDATE my_sms_config
               SET display_order = CASE id " .
            $when .
            "END 
               WHERE id IN (" . implode(', ', array_keys($data)) . ")";
        $res = $this->pdo->query($sql);

        return $res != false;
    }
}
