<?php

includeBase('AdminController');

class AdminAuthController extends AdminController
{

    protected $authName = '权限管理';


    /**
     * 显示权限列表
     * <AUTHOR>
     * @date    2016-03-15
     */
    public function actionIndex()
    {
        $adminModel = loadModel('admin');
        $nickname = \Common\Utils\Url::getStringParam('nickname', "");

        $lists = $adminModel->getAuths($nickname);
        $auth_list = array();
        foreach ($lists AS $val)
        {
            $auth_list[$val['username']]['username'] = $val['username'];
            $auth_list[$val['username']]['op_user'] = $val['op_user'];
            $auth_list[$val['username']]['op_time'] = $val['op_time'];
            $auth_list[$val['username']]['auth'][] = $val['auth'];
        }

        $pageArray = array(
            'nickname'   => $nickname,
            'auth_list'  => $auth_list,
        );
        loadView("admin/admin_auth.tpl.html", $pageArray);
    }


    /**
     * <AUTHOR>
     * @date    2016-03-15
     */
    public function actionAdd()
    {
        if (!empty($_POST))
        {
            $this->saveAdd();
        }

        $pageArray = array(
            'username' => NULL,
            'action'   => '/houtai_gl/admin_auth/add',
            'auth_list'   => array(),
        );
        loadView('admin/admin_auth_info.tpl.html', $pageArray);
    }


    /**
     * <AUTHOR>
     * @date    2016-03-15
     */
    public function actionEdit()
    {
        if (!empty($_POST))
        {
            $this->saveEdit();
        }

        $username = trim($_GET['username']);

        $model = loadModel('admin');
        $lists = $model->getAuths($username);

        $auth_list = array();
        foreach ($lists AS $val)
        {
            $auth_list[$val['id']] = $val['auth'];
        }

        $pageArray = array(
            'username'  => $username,
            'auth_list' => $auth_list,
            'action'    => '/houtai_gl/admin_auth/edit',
        );
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']))
        {
            loadView('admin/admin_auth_info.tpl.html', $pageArray);
        }
        else
        {
            echo loadView('admin/admin_auth_info.tpl.html', $pageArray, true);
        }
    }


    /**
     * <AUTHOR>
     * @date    2016-03-15
     */
    private function saveAdd()
    {
        $model = loadModel('admin');
        $username = trim($_POST['username']);
        $auth = array_map('trim', $_POST['auth']);

        foreach ($auth AS $val)
        {
            $model->addAuth($val, $username, $_COOKIE['admin_user']);
        }
        redirect('/houtai_gl/admin_auth');
    }


    /**
     * <AUTHOR>
     * @date    2016-03-15
     */
    private function saveEdit()
    {
        $username = trim($_POST['username']);
        $auth = array_map('trim', $_POST['auth']);

        $model = loadModel('admin');
        $lists = $model->getAuths($username);

        $auth_list = array();
        foreach ($lists AS $val)
        {
            $auth_list[$val['id']] = $val['auth'];
        }

        $diff = array_diff($auth_list, $auth);
        if ($diff) {
            foreach ($diff AS $key => $val) {
                $model->delAuth($key);
            }
        }
        foreach ($auth AS $val) {
            if (!in_array($val, $auth_list)) {
                $model->addAuth($val, $username, $_COOKIE['admin_user']);
            }
        }
        redirect('/houtai_gl/admin_auth');
    }


    /**
     * <AUTHOR>
     * @date    2016-03-15
     */
    public function actionDelete()
    {
        $username = trim($_GET['username']);

        $model = loadModel('admin');
        $lists = $model->getAuths($username);
        if (empty($lists))
        {
            return false;
        }

        $res = $model->delAuthByUser($username);
        if (!$res)
        {
            return false;
        }
        redirect('/houtai_gl/admin_auth');
    }


}
