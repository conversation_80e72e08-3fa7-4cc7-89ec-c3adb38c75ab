<?php


class RegQueueRedisAction extends Action
{
    public $regQueueRedis = null;
    private $redisErrorCode = -1;
    private $regQueueRedisKey = REG_QUEUE_REDIS_LIST_KEY;

    /**
     * getUserDataRedisByHash
     * -
     * @param string $hashIndex RedisHashIndex(hash1,hash2,hash3)
     * @return bool|mixed
     * <AUTHOR>
     */
    private function initRegQueueRedisByHash($hashIndex)
    {
        if (!$hashIndex)
        {
            return false;
        }
        if ($this->regQueueRedis)
        {
            return $this->regQueueRedis;
        }
        $redisConfig = Config::get('redis');
        $dlxRedis = $redisConfig['dlx_regQueue'];
        foreach ($dlxRedis as $key => $redisInfo)
        {
            try
            {
                if ($hashIndex == $key && !empty($redisInfo['master']))
                {
                    $this->regQueueRedis = new Redis();
                    $this->regQueueRedis->connect($redisInfo['master']['host'], $redisInfo['master']['port'], 2.5);

                    if ($this->regQueueRedis)
                    {
                        $this->regQueueRedis->auth($redisInfo['master']['auth']);
                    }
                    else
                    {
                        $this->regQueueRedis = $this->redisErrorCode;
                    }
                }
            }
            catch (Exception $ex)
            {
                $this->regQueueRedis = $this->redisErrorCode;
            }
            catch (Error $er)
            {
                $this->regQueueRedis = $this->redisErrorCode;
            }
        }
        return $this->regQueueRedis;
    }

    /**
     * regQueueLength
     * -
     * @return int
     * <AUTHOR>
     */
    public function regQueueLength()
    {
        try
        {
            $this->regQueueRedis = $this->initRegQueueRedisByHash('hash1');
            if ($this->regQueueRedis == $this->redisErrorCode)
            {
                return -1;
            }
            $length = $this->regQueueRedis->lLen($this->regQueueRedisKey);
            return $length;
        }
        catch (Exception $ex)
        {
            return -1;
        }
        catch (Error $er)
        {
            return -1;
        }
    }

    /**
     * pushToRegQueue
     * -
     * @param string $passId PassId
     * @param array $regInfo RegInfo
     * @return int
     * <AUTHOR>
     */
    public function pushToRegQueue($passId, $regInfo)
    {
        try
        {
            $regInfoKeyArray = array(
                'members',
                'members_info',
                'members_phone',
            );
            foreach ($regInfoKeyArray as $key)
            {
                if (!isset($regInfo[$key]) || !is_array($regInfo[$key]) || count($regInfo[$key]) <= 0)
                {
                    return -1;
                }
            }
            $this->regQueueRedis = $this->initRegQueueRedisByHash('hash1');
            if ($this->regQueueRedis == $this->redisErrorCode)
            {
                return -1;
            }
            $regInfo['PMKeyId'] = $passId; //先保留此字段值
            $pushResult = $this->regQueueRedis->lPush($this->regQueueRedisKey, serialize($regInfo));
            return $pushResult;
        }
        catch (Exception $ex)
        {
            return -1;
        }
        catch (Error $er)
        {
            return -1;
        }
    }

    /**
     * popRegQueue
     * -
     * @param string $passId PassId
     * @return int|mixed
     * <AUTHOR>
     */
    public function popRegQueue($passId)
    {
        try
        {
            $t = $passId;
            $this->regQueueRedis = $this->initRegQueueRedisByHash('hash1');
            if ($this->regQueueRedis == $this->redisErrorCode)
            {
                return -1;
            }
            $redisValue = $this->regQueueRedis->rPop($this->regQueueRedisKey);
            $regInfo = unserialize($redisValue);
            return $regInfo;
        }
        catch (Exception $ex)
        {
            return -1;
        }
        catch (Error $er)
        {
            return -1;
        }
    }

    /**
     * clearConnection
     * -
     * @return void
     * <AUTHOR>
     */
    public function closeRegQueueRedis()
    {
        if ($this->regQueueRedis)
        {
            $this->regQueueRedis->close();
        }
        $this->regQueueRedis = null;
    }
}
