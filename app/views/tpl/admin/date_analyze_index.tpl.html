<!DOCTYPE html>
<html>
<head>
    <meta charset="GBK">
    <title>每天注册比率</title>
    <link rel="stylesheet" href="/css/auth/styles/global.css">
    <link rel="stylesheet" href="/css/auth/styles/top.css">
    <link rel="stylesheet" href="/css/auth/styles/zhanghao.css">
    <script type="text/javascript" src="/js/jquery.min.js"></script>
</head>
<body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
<!--右侧主体 开始-->
<div class="mainWrap">

    <!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">每天注册比率</a></div>
    <div class="TabMenu mt10">
   	    <a href="/houtai_gl/DateAnalyze/GetDateAnalyzeList" >时间碎片管理</a>
        <a href="/houtai_gl/DateAnalyze" class="currTabMenu">每天注册比</a>
    </div>
    <!--面包屑导航 结束-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl20">
                <table cellspacing="0" cellpadding="0" class="tabMod topFied">
                    <tbody>
                    <tr>
                        <th>时间</th>
                        <th>注册请求总数</th>
                        <th>注册请求总数(有验证码)</th>
                        <th>注册成功数(有验证码)</th>
                        <th>注册总请求数(没有验证码)</th>
                        <th>注册成功数(没有验证码)</th>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div><!--tabFiedWrap end-->

        <table cellspacing="0" cellpadding="0" class="tabMod forFixed mt10">
            <tbody>
            <tr>
                <tr>
                        <th>时间</th>
                        <th>注册请求总数</th>
                        <th>注册请求总数(有验证码)</th>
                        <th>注册成功数(有验证码)</th>
                        <th>注册总请求数(没有验证码)</th>
                        <th>注册成功数(没有验证码)</th>
                    </tr>
            </tr>
            {{foreach from=$pageArray.regCountArr item=item key=key}}
                <tr>
                    <td>{{$key}}</td>
                    <td>{{$item.regcountAccess}}</td>
                    <td>{{$item.regcountHaveCapAccess}}</td>
                    <td>{{$item.regcountHaveCapSucceed}}</td>
                    <td>{{$item.regcountNoCapAccess}}</td>
                    <td>{{$item.regcountNoCapSucceed}}</td>
                    <!--td>{{$item.regcountAccess}}</td-->
                </tr>
            {{/foreach}}
            </tbody>
        </table>
        <!--table数据列表 结束-->
    </div><!--contentWrap end-->

</div><!--mainWrap end-->
<!--右侧主体 结束-->

<script>
$('.navList a').each(function (){
        if (  $(this).attr('href') == '/houtai_gl/DateAnalyze/GetDateAnalyzeList' )
        {
            $(this).addClass('select');
        }
    });
</script>


</body>
</html>
