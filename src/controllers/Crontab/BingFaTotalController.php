<?php

namespace Crontab;
use Controller;

class BingFaTotalController extends Controller
{
    /**
     * 每分钟获取最大并发数
     * */
    public function actionIndex()
    {
        $path = dirname(APPPATH) . '/src/includes/ESL/ESL.php';
        include_once $path;

        $hostList = array(
            '*************',
            '**********',
            );


        $BingFaModel = BingFaModel::getInstance();
        $TotalAction = TotalAction::getInstance();
        $projectList = $TotalAction->getProjectTotalList();

        $vcodeCallsNum = 0;
        foreach ($hostList as $eachIp)
        {
            $sock = new \ESLconnection($eachIp, '8021', '2345.com');
            $sockOut = new \ESLconnection($sock->socketDescriptor());
            if ($sockOut->api('status') === null)
            {
                continue;
            }
            $callStr = $sockOut->api('show calls')->getBody();
            $callArr = explode("\n", $callStr);
            $headArr = explode(',', array_shift($callArr));
            foreach ($callArr as $key => $call)
            {
                if ($call)
                {
                    $eachCallArr = explode(',', $call);
                    if (count($headArr) === count($eachCallArr))
                    {
                        $asArr = array_combine($headArr, $eachCallArr);
                    }
                    if (strpos($asArr['cid_name'], 'audio_ver_') !== false)
                    {
                        // 语言验证码总并发数
                        $vcodeCallsNum++;
                        foreach ($projectList as $projectKey => $projectInfos)
                        {
                            if (strpos($asArr['cid_name'], $projectKey))
                            {
                                // 积分并发
                                $projectList[$projectKey]++;
                            }
                        }
                    }
                }
            }
        }
        $bingFaData = array('num' => $vcodeCallsNum, 'projectNums' => $projectList);
        $isTrue = $BingFaModel->addvcodeSipInfo($bingFaData);
        var_dump($isTrue);
        // 并发数报警
        $this->checkVoiceBingfaMax($vcodeCallsNum);
    }


    private function checkVoiceBingfaMax($vcodeCallsNum)
    {
        if ($vcodeCallsNum > 15)
        {
            $time = time();
            $hour = intval(date('H', $time));
            if ($hour > 0 && $hour < 8)
            {
                return false;
            }
            $redis = \RedisAction::connect('YyRedis');
            $redis = $redis->select(5);
            $existsSend = $redis->get("VoiceCode:BinfaWarning");
            if ($existsSend === false)
            {
                $redis->set("VoiceCode:BinfaWarning", date("Y-m-d H:i:s", $time), 600);
                $date = " [" . date("Y-m-d H:i:s", time()) . "] ";
                $msg = $date . "语音验证码并发数量过高，并发数大于15，" . "当前并发数为：" . $vcodeCallsNum . " 。";
                \SendNoticeAction::sendSmsNotice($msg, 6);
            }

        }
    }

    /**
     * 统计每天最大并发数 及其联通率
     * */
    public function actionBingFaNumsTotal()
    {
        //$date = '20160330';
        $date = date("Ymd", strtotime("-1 day"));

        //查找前一天的最大并发数
        $BingFaModel = BingFaModel::getInstance();
        $maxBindFa = $BingFaModel->getVcodeSipInfo($date);
        $maxBindFa['binfa'] ? $max = $maxBindFa['binfa'] : $max = 0; //今天最大并发数
        $sum = intval($maxBindFa['sum']); // 每日并发数总和
        $vcodeMaxDate = $BingFaModel->getVcodeMaxDateInfo($date, $max);
        foreach ($vcodeMaxDate as $vcodeMaxDateInfo)
        {
            $topTime[] = ($vcodeMaxDateInfo['hour'] . ':' . $vcodeMaxDateInfo['min']);
        }
        $date1 = date('Y-m-d', strtotime($date));
        //连通率
        $total = $BingFaModel->getVoiceUseTotal($date1, 0);
        $receive = $BingFaModel->getVoiceUseTotal($date1, 0, 1);
        $receive_percent = round(($receive['total'] / $total['total']) * 100, 2);
        $topTime = implode(',', $topTime);
        $BingFaModel->delProjectVcodeInfo($date);
        $proData = array(
            'date' => $date,
            'max' => $max,
            'top_time' => $topTime,
            'receive_percent' => $receive_percent);
        $isTrue = $BingFaModel->addVoiceBingfaData($proData);
        var_dump($isTrue);
    }
}

?>