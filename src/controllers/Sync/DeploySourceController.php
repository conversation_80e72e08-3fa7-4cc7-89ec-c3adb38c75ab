<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/2/11
 * Time: 16:27
 */

namespace Sync;

use Controller;
use Octopus\deployEncryption;
use Octopus\deploySource;

class DeploySourceController extends Controller
{
    public $deploySource = null;
    protected $inputData = [];

    /**
     * 初始
     * DeploySourceController constructor.
     * @return void
     */
    public function __construct()
    {
        $logPath = APPPATH . '/logs/rsyncLog/';
        $this->deploySource = deploySource::getInstance($logPath);
        $dataJson = deployEncryption::decodeString($_REQUEST['data']);
        $this->inputData = json_decode($dataJson, true);
        if (!$this->deploySource->checkSign($this->inputData)) {
            header('HTTP/1.1 401 Unauthorized ');
            exit;
        }
    }

    /**
     * 通知修改同步状态
     * @return void
     */
    public function actionNotice()
    {
        echo $this->deploySource->rsyncFormat($this->inputData['version']);
    }

    /**
     * 获取同步状态
     * @return void
     */
    public function actionGetStatus()
    {
        echo $this->deploySource->rsyncStatusFormat();
    }

    /**
     * 指定版本获取同步日志
     * @return void
     */
    public function actionGetRsyncLog()
    {
        echo $this->deploySource->getRsyncLog($this->inputData['version']);
    }
}
