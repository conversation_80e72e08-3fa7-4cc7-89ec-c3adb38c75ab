<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/5/10
 * Time: 17:30
 */
use \Service\Capacity\GetRangeTable;

class LoginLogModel extends Model
{
    private $pdoUser;

    /**
     * 初始化
     * -
     * AdminModel constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取新passport_user pdo
     * @return \Octopus\PdoEx
     */
    protected function getPdoUser()
    {
        if (!is_object($this->pdoUser)) {
            $dbConfig = Config::get("database");
            $this->pdoUser = \Octopus\PdoEx::getInstance(
                DB_PASSPORT_USER,
                $dbConfig[DB_PASSPORT_USER]
            );  //线上测试环境
        }

        return $this->pdoUser;
    }

    /**
     * 新库写入登录日志
     * @param int    $passid    passid
     * @param string $loginTime 登录时间
     * @param string $ip        ip
     * @return bool
     */
    public function setNewLoginLogToDb($passid, $loginTime, $ip)
    {
        $update = array(
            'login_time' => $loginTime,
            'login_ip'   => $ip,
        );
        $condition = [
            'where'  => 'id = :passid',
            'params' => [':passid' => $passid],
        ];
        try {
            $membersTable = GetRangeTable::Members($passid);

            return $this->getPdoUser()->update($membersTable, $update, $condition);
        }
        catch (\Exception $e) {
            return false;
        }
    }

}
