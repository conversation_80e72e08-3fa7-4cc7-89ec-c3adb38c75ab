<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：YunFengCallbackController.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：11/09/2018 10:13
 */

namespace SmsInterface;

use Controller;

class YunFengCallbackController extends Controller
{
    protected $logFile;

    /**
     * YuFengCallbackController constructor.
     */
    public function __construct()
    {
        $isAccess = \ChannelIpWhiteList::isAccessAuthChannel('yunfeng');

        if (!$isAccess)
        {
            header('HTTP/1.1 401 Unauthorized ');
            exit;
        }
    }

    /**
     * User: panj
     * @return null
     */
    public function actionReceiveReport()
    {
        $xmlString = file_get_contents('php://input');
        if (empty($xmlString))
        {
            die();
        }
        //change xml format to json
        $xml = simplexml_load_string($xmlString);
        $json = json_encode($xml);
        $respArr = json_decode($json, true);
        $isOK = false;
        if (isset($respArr['body']))
        {
            $records = $respArr['body']['records'];
            if (!empty($records) && isset($records['record']))
            {
                $record = $records['record'];
                $redisAction = \RedisAction::connect();
                $params = array(
                    "Msg_Id"        => trim($record['batch_num']),
                    "Mobile"        => trim($record['dest_id']),
                    "OriginStatus"  => trim($record['send_status']),
                    "Status"        => in_array(trim($record['receive_real_status']), ['0', 'DELIVRD']) ? 'DELIVRD' : trim($record['receive_real_status']),
                    "Callback_Time" => trim($record['stat_time']),
                );
                $isOK = $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($params));
            }
        }

        $isOK ? die('ok') : die('error');
    }
}
