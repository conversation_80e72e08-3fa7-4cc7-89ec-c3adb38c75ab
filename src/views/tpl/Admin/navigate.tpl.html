<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务器平台侧边栏</title>
  <link rel="stylesheet" href="/style/css/global.css?t=23">
</head>
{{if $smarty.const.RUNMODE neq 'production'}}
<style>
    .leftNav{background-color: #ffccef;}
    .navList li span.tit{background-color: #FFA67D;}
    .navList li p.pMore{background-color: #FFE3DF;}
    .navList li p.pMore a:hover{background-color: #ffd2c7;}
</style>
{{/if}}
<body>
    <div class="leftNav">
    	<h2><a href="/Admin/User/Default" target="rightFrame" onclick="javascript:location.reload()">短信服务平台{{if $smarty.const.RUNMODE neq 'production'}}[dev]{{/if}}</a></h2>
        <ul class="navList clearfix">
            {{foreach from=$pageArray.navList item=navInfo}}
            <li {{if ($navInfo.authName != "语音通道")}}class="cur"{{/if}}>
                <span class="tit"><em>{{$navInfo.authName}}</em><i class="iMore"></i></span>
                <p class="pMore">
                    {{foreach from=$navInfo.childModel item=childModelInfo}}
                    <a href="{{$childModelInfo.authPath}}" target="rightFrame">{{$childModelInfo.authName}}</a>
                    {{/foreach}}
                    <i class="clear"></i>
                </p>
            </li>
            {{/foreach}}
            
        </ul><!--navList end-->
    </div><!--leftNav end-->

<script src="/style/js/jquery-1.8.3.min.js"></script>

<script>
$(".navList li .tit").click(function()
{
  if(!$(this).parent().hasClass("cur"))
  {
    $(".navList li").removeClass("cur");
    $(this).parent().addClass("cur");
  }else
  {
    $(this).parent().removeClass("cur");
  };
});
</script>

</body>
</html>