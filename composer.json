{"name": "Octopus/Octopus", "repositories": [{"type": "composer", "url": "http://packagist.2345.cn/repo/private/"}], "require": {"php": ">=7.1.0", "smarty/smarty": "~2.6", "Octopus/Functions": "1.0.0", "Octopus/Router": "1.0.1", "Octopus/PdoEx": "1.0.3", "Octopus/RedisEx": "1.0.0", "Octopus/Filter": "1.0.0", "Octopus/Logger": "1.1.0", "OctopusUtf8/deploySource": "~1.0.0", "jichupingtai/crypto": "2.0.0", "OctopusUtf8/Web/log4phpGBK": "^2.4", "alibabacloud/client": "^1.5", "alibabacloud/dybaseapi": "^1.8"}, "autoload": {"psr-4": {"": ["app/classes", "src/actions", "src/controllers", "src/models", "src/hooks", "src/Service"]}, "files": ["app/includes/functions.php"]}, "config": {"secure-http": false}}