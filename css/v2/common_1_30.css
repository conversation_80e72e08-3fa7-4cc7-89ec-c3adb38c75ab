@charset "gb2312";
/**
* @name : common
* @update : 2015.01.12
* <AUTHOR> liux
**/
/* reset */
body { font: 12px/1.5 Arial,\5b8b\4f53,Tahoma,sans-serif; background-color: #ffffff; color: #333333; }

html, body, p, dl, dt, dd, table, td, th, input, img, form, div, span, ul, ol, li, h1, h2, h3, h4, h5, h6, select, fieldset, fieldset, button, sub, sup, textarea { margin: 0; padding: 0; }

table { border-collapse: collapse; border-spacing: 0; }

h1, h2, h3, h4, h5, h6 { font-size: 100%; }

iframe, img { border: 0 none; }

img { vertical-align: top; }

em, i { font-style: normal; }

ul, li, ol { list-style: none outside none; }

a { color: #333333; text-decoration: none; }

a:hover { color: #ff3300; text-decoration: underline; }

:focus { outline: 0; }

.clear { clear: both; overflow: hidden; font-size: 0; height: 0; line-height: 0; }

input, textarea, select { background-color: #ffffff; }

.wrapper { min-width: 1000px; *zoom: 1; }

.main { margin: 0 auto; width: 1000px; }

.minHeight { min-height: 400px; height: auto !important; _height: 400px; }

.input::-ms-clear { display: none; }

.clearfix, .pheader-col { *zoom: 1; }
.clearfix:after, .pheader-col:after { content: "."; display: block; height: 0; clear: both; visibility: hidden; line-height: 0; font-size: 0; }

.icon-ok-big, .icon-error, .ipt_radio { background: url(../../images/v2/uc_icon.png) no-repeat; }

.m-selectA, .m-selectA .arrow-btm { background: url(../../images/v2/uc_sprite.png) no-repeat; }

.pheader { height: 69px; background-color: #ffffff; margin-bottom: 12px; }
.pheader-col { margin: 0 auto; width: 1000px; }
.pheader .logo { float: left; width: 192px; height: 69px; background: url(../../images/v2/logo.png) no-repeat; }
.pheader .logo_login, .pheader .logo_reg { float: left; display: inline; margin: 28px 0 0 10px; width: 46px; height: 18px; overflow: hidden; text-indent: -9999px; }
.pheader .logo_login { background: url(../../images/v2/logo_login.png) no-repeat; }
.pheader .logo_reg { background: url(../../images/v2/logo_reg.png) no-repeat; }
.pheader .userinfo { float: right; margin-top: 30px; }
.pheader .userinfo .item { float: left; }
.pheader .userinfo .cut { float: left; margin: 3px 10px; width: 1px; height: 12px; overflow: hidden; background-color: #e2e2e2; }

.g-nav { position: relative; margin-bottom: 8px; height: 39px; background-color: #338ad1; border-top: 1px solid #1e87c6; font-family: \5FAE\8F6F\96C5\9ED1,\9ED1\4F53,\5B8B\4F53; }
.g-nav-row { position: relative; margin: 0 auto; width: 1000px; }
.g-nav-list { position: absolute; top: -1px; left: 0; font-size: 16px; }
.g-nav-list li { position: relative; float: left; display: inline; padding-right: 1px; }
.g-nav-list li a { position: relative; padding: 0 25px; display: inline-block; height: 38px; line-height: 38px; text-align: center; border: 1px solid #338ad1; color: #ffffff !important; cursor: pointer; text-decoration: none; }
.g-nav-list li a:hover { background-color: #1e77c9; text-decoration: none; border: 1px solid #1a73b1; z-index: 2; }
.g-nav-list li.current { z-index: 4; }
.g-nav-list li.current a { background-color: #186fbf !important; border: 1px solid #0f66a2; z-index: 3; }
.g-nav-list li .split { position: absolute; top: 8px; right: 0px; _right: -1px; width: 1px; height: 24px; overflow: hidden; background-color: #3695e2; font-style: normal; }

.pfooter { padding: 18px 0; text-align: center; color: #999999; }
.pfooter a { color: #999999; }
.pfooter a:hover { color: #ff3300; }
.pfooter em { margin: 0 10px; color: #d4d4d4; }

/*icon*/
.icon-ok-big { display: inline-block; vertical-align: middle; width: 46px; height: 46px; background-position: 0 -103px; }

.icon-error { display: inline-block; vertical-align: middle; width: 13px; height: 13px; background-position: -5px -80px; }

/*btn*/
.btn-yelA, .btn-grayA { display: inline-block; vertical-align: middle; width: 108px; height: 30px; line-height: 30px; text-align: center; font-size: 14px; border: 0 none; cursor: pointer; }
.btn-yelA:hover, .btn-grayA:hover { text-decoration: none; }

.btn-yelA { border: 1px solid #ee7600; background-color: #ff7e00; color: #ffffff; }
.btn-yelA:hover { color: #ffffff; border-color: #f5861b; background-color: #ff9834; }

.btn-grayA { border: 1px solid #e4e4e4; background-color: #e4e4e4; color: #999999; cursor: default; }

.btn-blueA { display: inline-block; vertical-align: middle; width: 112px; height: 31px; line-height: 31px; background-color: #338ad1; border: 1px solid #186cb1; text-align: center; color: #ffffff; font-size: 14px; cursor: pointer; -webkit-border-radius: 3px; -moz-border-radius: 3px; border-radius: 3px; }
.btn-blueA:hover { text-decoration: none; color: #ffffff; background-color: #499fe6; border-color: #2a84ce; }

/*form ele*/
.ipt_txt { width: 246px; padding: 4px 12px; height: 22px; line-height: 22px; font-size: 12px; border: 1px solid #c6ced6; color: #333333; vertical-align: middle; font-family: \5b8b\4f53; -moz-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; -webkit-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; }
.ipt_txt.ipt_defa { color: #999999; }
.ipt_txt.ipt_txt_error { border-color: #ff6600; }
.ipt_txt.ipt_txt_cur { color: #333333; border-color: #499fe6; }

.ipt_radio { display: inline-block; vertical-align: middle; width: 16px; height: 16px; overflow: hidden; background-position: -5px -5px; }
.ipt_radio_cur { background-position: -5px -31px; }

.m-selectA { display: inline-block; vertical-align: middle; *display: inline; *zoom: 1; border: 1px solid #d9d9d9; width: 200px; background-position: 0 -165px; background-repeat: repeat-x; height: 30px; line-height: 30px; margin-right: 12px; color: #848484; position: relative; width: auto; min-width: 94px; z-index: 3; font-size: 12px; cursor: pointer; }
.m-selectA .text { min-width: 40px; padding: 0 6px; border-right: 1px solid #ededed; float: left; text-align: center; cursor: pointer; }
.m-selectA .holder { width: 40px; height: 30px; border-left: 1px solid #fff; overflow: hidden; position: relative; font-size: 0; cursor: pointer; float: left; }
.m-selectA .option { position: absolute; width: 100%; _width: expression(this.parentNode.offsetWidth-2); background-color: #fbfbfb; height: 155px; border: 1px solid #d9d9d9; margin: -1px 0 0 -1px; top: 31px; left: 0; overflow: auto; }
.m-selectA .option li { padding: 0 6px; height: 30px; line-height: 30px; overflow: hidden; border-bottom: 1px solid #eee; }
.m-selectA .option li a { color: #666; display: block; }
.m-selectA .option li a:hover { color: #ff4800; text-decoration: none; }
.m-selectA .arrow-btm { width: 13px; height: 7px; background-position: -186px -115px; position: absolute; top: 12px; left: 15px; }

/* box */
.m-boxA { padding: 12px 30px; background-color: #ffffff; *zoom: 1; }
.m-boxA-hd { position: relative; height: 38px; line-height: 38px; border-bottom: 1px solid #eeeeee; *zoom: 1; }
.m-boxA-hd .more { position: absolute; right: 0; color: #338ad1; }
.m-boxA-hd .more:hover { color: #ff3300; }
.m-boxA-tit { float: left; margin-top: -2px; font-size: 14px; border-bottom: 2px solid #338ad1; }
.m-boxA-bd { padding: 12px 0 0; }

.m-boxB { padding: 6px 22px 15px; border: 1px solid #e3e8ec; }
.m-boxB-hd { position: relative; height: 38px; line-height: 38px; border-bottom: 1px solid #eeeeee; *zoom: 1; }
.m-boxB-tit { float: left; margin-top: -2px; font-size: 14px; border-bottom: 2px solid #338ad1; }
.m-boxB-bd { padding: 12px 0 0; }

/*m-form*/
.m-form .form-item { position: relative; margin: 15px 0 0 0; padding: 0 0 0 126px; line-height: 32px; font-size: 14px; *zoom: 1; }
.m-form .form-item-radio .piece { margin-right: 8px; cursor: pointer; color: #666666; }
.m-form .form-item-radio .ipt_radio { margin-right: 5px; }
.m-form .form-item-top { padding-left: 0; }
.m-form .form-field { position: absolute; left: 0; width: 122px; text-align: right; padding-right: 4px; }
.m-form .form-tips { margin-left: 7px; color: #999999; }
.m-form .form-tips-error { color: #ff6600; }
.m-form .form-tips .icon-error { margin-right: 8px; }
.m-form .yzmCon { display: inline-block; margin-right: 10px; width: 148px; }
.m-form .form-pwd-tips { color: #ff3f00; }
.m-form .form-pwd-tips i { display: inline-block; vertical-align: middle; margin: 0 2px; width: 17px; height: 4px; overflow: hidden; border: 1px solid #ff3f00; }
.m-form .form-pwd-tips i.on { background-color: #ff3f00; }
.m-form .form-pwd-tips em { margin-left: 5px; vertical-align: middle; }
.m-form .form-pwd-tips-2 { color: #ffc600; }
.m-form .form-pwd-tips-2 i { border-color: #ffc600; }
.m-form .form-pwd-tips-2 i.on { background-color: #ffc600; }
.m-form .form-pwd-tips-3 { color: #4dc214; }
.m-form .form-pwd-tips-3 i { border-color: #4dc214; }
.m-form .form-pwd-tips-3 i.on { background-color: #4dc214; }
.m-form .form-item-cur .ipt_radio { background-position: -5px -31px; }
.m-form .form-item-cur .ipt_txt { color: #333333; border-color: #499fe6; }
.m-form .goPrev { margin-left: 10px; color: #2371c8; }
.m-form .goPrev:hover { color: #ff3300; }

.m-form-radio .form-item { padding-left: 146px; }
.m-form-radio .form-field { width: 142px; cursor: pointer; }
.m-form-radio .ipt_radio { margin-right: 10px; _margin-top: 10px; _margin-bottom: 10px; }

.m-txtA { overflow: hidden; *zoom: 1; }
.m-txtA li { padding: 6px 0; color: #666666; }
.m-txtA li a { color: #2371c8; }
.m-txtA li a:hover { color: #ff3300; }

.m-ralistA li { line-height: 33px; }
.m-ralistA .ipt_radio { margin-right: 12px; _margin-top: 9px; _margin-bottom: 9px; }
.m-ralistA label { cursor: pointer; }
.m-ralistA .current .ipt_radio { background-position: -5px -31px; }

.wrap_ad { background-color: #f94840; z-index: 0; }
.grid_ad { margin: 0 auto; width: 1000px; height: 60px; }
.grid_ad img { width: 1000px; height: 60px; }
.grid_ad a{display: block; width: 1000px;height: 60px;}
.grid_ad .iconfont {  background: url('../../images/v3/icon_close.jpg') no-repeat; display: block; width: 25px; height: 25px; float: right; margin-top: -60px; margin-right: 0; cursor: pointer; font-size: 14px; color: #FFF; opacity: .9;}
