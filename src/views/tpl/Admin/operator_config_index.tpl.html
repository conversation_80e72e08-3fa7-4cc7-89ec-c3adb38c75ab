<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台-服务商配置</title>
    <link rel="stylesheet" href="/style/css/global.css">
</head>

<body>
<div class="mainWrap">
    <!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a> > <span>服务商配置</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="/Admin/OperatorConfig/Index">发送账号配置</a>
        <a href="/Admin/Callback/Index">回执账号配置</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->

    <div class="funList mt20 clearfix">
        <a class="styleBtn_s styleBtnBlue left "style="margin-right: 8px" href="javascript:onclick=saveDisplayOrder();">更新排序</a>
        <a class="styleBtn_s styleBtnBlue left addOperatorConfig" href="javascript:">添加服务商配置</a>
        <div class="funList mt20 clearfix">
            <form action="" method="POST" id="submitFormId">
                <span class="input-sp"><input type="text" autocomplete="off" name="OperatorName" value="{{$pageArray.OperatorName}}"/></span>
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 querySubmitForm">查询</a>
            </form>
        </div>
    </div>

    <!--主体内容 开始-->
    <div class="contentWrap">

        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>ID</th>
                        <th>序号</th>
                        <th>服务商</th>
                        <th>服务商分类</th>
                        <th>内容类型</th>
                        <th>通道状态</th>
                        <th>支持移动</th>
                        <th>支持电信</th>
                        <th>支持联通</th>
                        <th>签名</th>
                        <th>签名类型</th>
                        <th>描述(资费等)</th>
                        <th>单价(元)</th>
                        <th>API地址</th>
                        <th>账户号</th>
                        <th>账户密码</th>
                        <th>action</th>
                        <th>时间段</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->

        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th style="width:30px"><a href="javascript:onclick=sort('id')">ID</a></th>
                <th style="width:60px"><a href="javascript:onclick=sort('display_order')">序号(大号靠前)</a></th>
                <th style="width:100px">服务商</th>
                <th>服务商分类</th>
                <th>内容类型</th>
                <th>通道状态</th>
                <th>支持移动</th>
                <th>支持电信</th>
                <th>支持联通</th>
                <th>签名</th>
                <th style="width:60px">签名类型</th>
                <th style="width:150px">描述(资费等)</th>
                <th style="width:50px;">单价(元)</th>
                <th style="width:155px">API地址</th>
                <th>账户号</th>
                <th>账户密码</th>
                <th>action</th>
                <th style="width:90px">时间段</th>
            </tr>
            {{foreach from=$pageArray.operatorList item=operatorInfo }}
            <tr {{if $operatorInfo.status== 0}}class="lightGrayBg" {{/if}}>
                <td>{{$operatorInfo.id}}</td>
                <td><input type="text" name="displayOrder[{{$operatorInfo.id}}]" value="{{$operatorInfo.display_order}}" style="width:30px;text-align:center"></td>
                <td><a href="javascript:void(0);" operatorId="{{$operatorInfo.id}}" class="operatorNameClass">{{$operatorInfo.provider_name}}</a></td>
                <td>
                    {{if isset($pageArray.provider_cate_list[$operatorInfo.provider_cate])}}{{$pageArray.provider_cate_list[$operatorInfo.provider_cate]}}{{else}}未知{{/if}}
                </td>
                <td>{{if $operatorInfo.appType ==1}}短信{{else if $operatorInfo.appType == 2}}语音{{/if}}</td>
                <td><a href="javascript:void(0);" operatorId="{{$operatorInfo.id}}" operatorStatus="{{$operatorInfo.status}}" class="operatorStatusClass">{{if $operatorInfo.status == 1}}开{{else}}关{{/if}}</a></td>
                <td>{{if $operatorInfo.support_mobile == 1}}支持{{else}}不支持{{/if}}</td>
                <td>{{if $operatorInfo.support_telecom == 1}}支持{{else}}不支持{{/if}}</td>
                <td>{{if $operatorInfo.support_unicom == 1}}支持{{else}}不支持{{/if}}</td>
                <td>{{$operatorInfo.sign}}</td>
                <td>{{if $operatorInfo.sign_type == 0}}自定义{{else}}强制{{/if}}</td>
                <td>{{$operatorInfo.description}}</td>
                <td>{{$operatorInfo.unit_price}}</td>
                <td>{{$operatorInfo.api_url}}</td>
                <td>{{$operatorInfo.username}}</td>
                <td>{{$operatorInfo.password}}</td>
                <td>{{$operatorInfo.action_name}}</td>
                <td>{{$operatorInfo.timeQuantum}}</td>
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->


<!--透明遮罩-->
<div class="boxMask" style="display: none;">透明遮罩层</div>

<!--通道信息 弹窗 开始-->
<div class="boxMod w520" style="display:none;">
    <div class="boxTop"><h2 class="boxTit">服务商配置</h2><a class="BoxClose" href="javascript:">关闭按钮</a></div>
    <div class="scrollWrap">
        <p class="box-infro">移动MAS仅可用于手机验证码短信，其他类型短信禁止使用MAS。</p>
        <div class="tabFrom mtb20">
            <form id="operatorFormId" action="" method="post">
                <table class="tabFromMod" cellpadding="0" cellspacing="0">
                    <tr>
                        <td class="tabLeftTit">服务商：</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="operatorForm[provider_name]"/></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">服务商分类：</td>
                        <td>
                            {{foreach from=$pageArray.provider_cate_list key=cate item=name}}
                            <input type="radio" value="{{$cate}}" name="operatorForm[provider_cate]"><label>{{$name}}</label>&nbsp;&nbsp;
                            {{/foreach}}
                        </td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">内容类型：</td>
                        <td><input type="radio" value="1" name="operatorForm[appType]"><label>短信</label>&nbsp;&nbsp;<input type="radio" value="2" name="operatorForm[appType]"><label>语音</label></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">支持移动：</td>
                        <td><input type="checkbox" value="1" name="operatorForm[support_mobile]"></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">支持电信：</td>
                        <td><input type="checkbox" value="1" name="operatorForm[support_telecom]"></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">支持联通：</td>
                        <td><input type="checkbox" value="1" name="operatorForm[support_unicom]"></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">短信签名内容：</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="operatorForm[sign]"/></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">短信签名类型：</td>
                        <td><input type="radio" name="operatorForm[sign_type]" id="bySys" value="0"/><label for="bySys">系统自定义</label>&nbsp;&nbsp;<input type="radio" name="operatorForm[sign_type]" id="byOpr" value="1"/><label for="byOpr">运营商强制</label></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">描述：</td>
                        <td><textarea rows="3" cols="33" name="operatorForm[description]"></textarea></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">短信单价(元)：</td>
                        <td><input class="tabInput" type="text"  autocomplete name="operatorForm[unit_price]"></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">API地址：</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="operatorForm[api_url]"/></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">账户号：</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="operatorForm[username]"/></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">账户密码：</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="operatorForm[password]"/></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">action：</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="operatorForm[action_name]"/></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit">可用时间段：<br/>-为可用，+为不可用</td>
                        <td><input class="tabInput" type="text" autocomplete="off" name="operatorForm[timeQuantum]"/></td>
                    </tr>

                    <tr>
                        <td class="tabLeftTit" rowspan="2">预警配置：</td>
                        <td><textarea rows="2" cols="33" autocomplete="off" name="operatorForm[monitor_config]"></textarea></td>
                    </tr>
                    <tr>
                        <td>callbackRate-成功率阈值；stepTime-样本时间跨度；offsetTime-回执等待时间</td>
                    </tr>
                    <tr>
                        <input type="hidden" name="operatorForm[id]"/>
                        <input type="hidden" name="act" value="add"/>
                        <td colspan="2" align="center"><a class="styleBtnBlue tabsave submitFromOperator" href="javascript:">确定</a></td>
                    </tr>
                </table>


            </form>
        </div><!--tabFrom end-->
    </div>
</div><!--boxMod end-->
<!--通道信息 弹窗 结束-->

</body>
</html>
<script src="/style/js/jquery-1.8.3.min.js"></script>
<script src="/style/js/tableFixed.js"></script>
<script src="/style/js/boxLocation.js"></script>
<script>
    function sort(orderBy) {
        if (orderBy != 'id' && orderBy != 'display_order') {
            return;
        }

        var orderByType = "{{$pageArray.orderByType}}";
        orderByType = orderByType == 'asc' ? 'desc' : 'asc';

        var url = '/Admin/OperatorConfig/Index?orderBy=' + orderBy + '&type=' + orderByType;
        window.location.href = url;
    }

    function saveDisplayOrder() {
        var data = $('input[name^="displayOrder"]').serialize();
        $.post('/Admin/OperatorConfig/ChangeDisplayOrder', {displayOrder: data}, function (data) {
            alert(data.msg);
            window.location.reload();
        });
    }

    $('.operatorNameClass').click(function () {
        $('.boxMask').show();
        $('.boxMod').show();
        $('input[name="act"]').val('edit');
        var operatorId = $(this).attr('operatorId');
        if (operatorId === '') {
            return false;
        }
        $.post('/Admin/OperatorConfig/GetOperatorConfigInfo', 'id=' + operatorId, function (data) {
            if (data.status !== 'P00001') {
                alert(data.msg)
            } else {
                $('input[name="operatorForm[provider_name]"]').val(data.data.provider_name);
                if (data.data.support_mobile === '1') {
                    $('input[name="operatorForm[support_mobile]"]').attr('checked', true);
                }
                if (data.data.support_telecom === '1') {
                    $('input[name="operatorForm[support_telecom]"]').attr('checked', true);
                }
                if (data.data.support_unicom === '1') {
                    $('input[name="operatorForm[support_unicom]"]').attr('checked', true);
                }
                
                if (data.data.provider_cate === '1') {
                    $('input[name="operatorForm[provider_cate]"]:eq(0)').attr("checked", 'checked');
                }
                if (data.data.provider_cate === '2') {
                    $('input[name="operatorForm[provider_cate]"]:eq(1)').attr("checked", 'checked');
                }
                if (data.data.provider_cate === '3') {
                    $('input[name="operatorForm[provider_cate]"]:eq(2)').attr("checked", 'checked');
                }
                if (data.data.provider_cate === '4') {
                    $('input[name="operatorForm[provider_cate]"]:eq(3)').attr("checked", 'checked');
                }
                if (data.data.provider_cate === '5') {
                    $('input[name="operatorForm[provider_cate]"]:eq(4)').attr("checked", 'checked');
                }
                if (data.data.provider_cate === '0') {
                    $('input[name="operatorForm[provider_cate]"]:eq(5)').attr("checked", 'checked');
                }
                
                if (data.data.appType === '1') {
                    $('input[name="operatorForm[appType]"]:eq(0)').attr("checked", 'checked');
                }
                if (data.data.appType === '2') {
                    $('input[name="operatorForm[appType]"]:eq(1)').attr("checked", 'checked');
                }
                if (data.data.sign_type === '0') {
                    $('input[name="operatorForm[sign_type]"]:eq(0)').attr("checked", 'checked');
                }
                if (data.data.sign_type === '1') {
                    $('input[name="operatorForm[sign_type]"]:eq(1)').attr("checked", 'checked');
                }
                $('input[name="operatorForm[sign]"]').val(data.data.sign);
                $('textarea[name="operatorForm[description]"]').val(data.data.description);
                $('input[name="operatorForm[unit_price]"]').val(data.data.unit_price);
                $('input[name="operatorForm[api_url]"]').val(data.data.api_url);
                $('input[name="operatorForm[username]"]').val(data.data.username);
                $('input[name="operatorForm[password]"]').val(data.data.password);
                $('input[name="operatorForm[action_name]"]').val(data.data.action_name);
                $('textarea[name="operatorForm[monitor_config]"]').val(data.data.monitor_config);
                $('input[name="operatorForm[id]"]').val(data.data.id);
                $('input[name="operatorForm[timeQuantum]"]').val(data.data.timeQuantum);
            }

        });

    });

    $('.BoxClose').click(function () {
        $('.boxMask').hide();
        $('.boxMod').hide();
    });

    function judgeFunc() {
        var provider_name = $('input[name="operatorForm[provider_name]"]').val();
        var action_name = $('input[name="operatorForm[action_name]"]').val();
        if (provider_name === '') {
            alert('服务商不能为空');
            return false;
        }
        if (action_name === '') {
            alert('执行方式不能为空');
            return false;
        }
        return true;
    }

    $('.submitFromOperator').click(function () {
        if (!judgeFunc()) {
            return false;
        }
        var act = $('input[name="act"]').val();
        $.post('/Admin/OperatorConfig/ChangeOperatorConfig?actioType=' + act, $('#operatorFormId').serialize(), function (data) {
            if (data.status === 'P00001') {
                window.location = '/Admin/OperatorConfig/Index';
            }
            else {
                alert(data.msg);
            }
        });


    });

    $('.operatorStatusClass').click(function () {
        var oldOperatorStatus = $(this).attr('operatorStatus');
        var operatorId = $(this).attr('operatorId');
        var newOperatorStatus = null;
        var msg = '';
        if (oldOperatorStatus === '1') {
            msg = '你确定要关闭此渠道吗？';
            newOperatorStatus = 0;
        }
        else {
            msg = '你确定要打开此渠道吗？';
            newOperatorStatus = 1;
        }
        if (confirm(msg)) {
            $.post('/Admin/OperatorConfig/ChangeOperatorStatus', 'id=' + operatorId + '&operatorStatus=' + newOperatorStatus, function (data) {
                if (data.status === 'P00001') {
                    window.location = '/Admin/OperatorConfig/Index';
                }
                else {
                    alert(data.msg)
                }
            });
        }
    });

    $('.addOperatorConfig').click(function () {
        $('.boxMask').show();
        $('.boxMod').show();
        $('input[name="act"]').val('add');
        $('#operatorFormId')[0].reset();
        $('input[name="operatorForm[support_mobile]"]').attr('checked', false);
        $('input[name="operatorForm[support_telecom]"]').attr('checked', false);
        $('input[name="operatorForm[support_unicom]"]').attr('checked', false);
        $('textarea[name="operatorForm[monitor_config]"]').val('{"callbackRate":0.90,"stepTime":600,"offsetTime":120}');
        $('input[name="operatorForm[timeQuantum]"]').val('00:00-24:00');
    });

    $('.querySubmitForm').click(function () {
        $('#submitFormId').submit();
    });
</script>