<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/21
 * Time: 10:24
 */

namespace Service\Func;

use Admin\BlackListModel;
use Crontab\DataStatisticModel;
use Service\Handle\RequestHandle;
use Service\Lib\Model\ConfigModel;
use Service\Lib\Provider;

class SmsFunc
{
    // 点集白名单
    public static $djptMonitorStatus = [
        'E:CHAN' => '携号转网',
        'FAILURE' => '携号转网',
        'HD:19' => '运营商网关黑名单',
        'MX:0012' => '运营商网关黑名单',
        'MX:0011' => '运营商网关黑名单',
        'ER:3' => '运营商网关黑名单',
        'BWLIST _006' => '运营商网关黑名单',
        'MB:1077' => '运营商网关黑名单',
        'BWLIST' => '运营商网关黑名单',
        'DB00144' => '运营商网关黑名单',
        'TUIDING' => '运营商网关黑名单',
        'HIGRISK' => '运营商网关黑名单',
        'F002' => '运营商网关黑名单',
        'W-BLACK' => '运营商网关黑名单',
        '69' => '运营商网关黑名单',
        'MOBILE BLACK' => '运营商网关黑名单',
    ];

    /**
     * 判断是否移动手机号
     *
     * @param string $phone 电话号码
     *
     * @return bool
     */
    public static function isMobilePhone($phone)
    {
        return \FuncAction::isMPhone($phone);
    }

    /**
     * 判断是否电信手机号
     *
     * @param string $phone 电话号码
     *
     * @return bool
     */
    public static function isTelecomPhone($phone)
    {
        return \FuncAction::isChinaTelecomPhone($phone);
    }

    /**
     * 判断是否联通手机号
     *
     * @param string $phone 电话号码
     *
     * @return bool
     */
    public static function isUnicomPhone($phone)
    {
        return \FuncAction::isChinaUnicomPhone($phone);
    }

    /**
     * 校验通道可使用时间段
     * @param string $timeStr timeQuantum
     * @return int
     */
    public static function checkTimeQuantum($timeStr)
    {
        preg_match('/^([0-2][\d]):([0-5][\d])([+-])([0-2][\d]):([0-5][\d])$/', $timeStr, $timeMatch);
        if (!isset($timeMatch[0])) {
            return true;
        }
        $startTime = $timeMatch[1] * 60 + $timeMatch[2];
        $endTime = $timeMatch[4] * 60 + $timeMatch[5];
        $nowTime = date('H') * 60 + date('i');
        if ($timeMatch[3] === '-' && ($nowTime < $startTime || $nowTime > $endTime)) {
            return false;
        }
        if ($timeMatch[3] === '+' && ($nowTime > $startTime && $nowTime < $endTime)) {
            return false;
        }
        return true;
    }

    /**
     * User: panj
     * 随机获取sms 配置
     *
     * @param array $smsConfigs smsconfigs
     *
     * @return mixed
     */
    public static function randomTypeConfig($smsConfigs)
    {
        $list = array();
        $sum = 0;
        foreach ($smsConfigs as $index => $smsConfig) {
            $list[$index] = $smsConfig['percent'];
            $sum += $smsConfig['percent'];
        }
        $randNum = rand(1, $sum);
        foreach ($list as $k => $v) {
            $randNum -= $v;
            if ($randNum <= 0) {
                return $smsConfigs[$k];
            }
        }
        return $smsConfigs[0];
    }

    /**
     * 手机号码
     *
     * @param string $phone 手机号码
     *
     * @return boolean
     */
    public static function checkPhone($phone)
    {
        $res = preg_match('#^1[3,4,5,6,7,8,9]{1}[\d]{9}$#', $phone) || preg_match('#^[0]{2}[\d]*$#', $phone);

        return $res;
    }


    /**
     * 检查手机号合法和黑名单
     * @param array $phones phone list
     *
     * @return array
     */
    public static function checkMultiPhone($phones)
    {
        $configModel = new ConfigModel();
        $legalPhoneList = $illegalPhoneList = array();
        foreach ($phones as $phone) {
            if (!trim($phone)) {
                continue;
            }
            if (SmsFunc::checkPhone($phone)) {
                if ($configModel->isInBlackList($phone)) {
                    $illegalPhoneList[] = $phone;
                } else {
                    $legalPhoneList[] = $phone;
                }
            } else {
                $illegalPhoneList[] = $phone;
            }
        }

        return array($legalPhoneList, $illegalPhoneList);
    }

    /**
     * 短信内容检测
     *
     * @param string $content 短信内容
     *
     * @return boolean
     */
    public static function checkContent($content)
    {
        if (!$content || strlen(trim($content)) < 2) {
            return false;
        }

        return true;
    }

    /**
     * 统计短信内容字符数
     *
     * @param string $msg msg
     *
     * @return int
     */
    public static function smsCount($msg)
    {
        $msgLen = mb_strlen($msg, 'GBK');
        if ($msgLen <= 70) {
            return 1;
        } else {
            return ceil($msgLen / 67);
        }
    }

    /**
     * 通道检测
     *
     * @param array $config config
     *
     * @return array|bool
     */
    public static function checkChannelStatus($config)
    {
        if ($config['mobile_status'] == 0) {
            return [RequestFunc::MOBILE_CHANNEL_NOT_AVAILABLE, RequestFunc::$errText[RequestFunc::MOBILE_CHANNEL_NOT_AVAILABLE]];
        }
        if ($config['telecom_status'] == 0) {
            return [RequestFunc::TELCOM_CHANNEL_NOT_AVAILABLE, RequestFunc::$errText[RequestFunc::TELCOM_CHANNEL_NOT_AVAILABLE]];
        }
        if ($config['unicom_status'] == 0) {
            return [RequestFunc::UNICOM_CHANNEL_NOT_AVAILABLE, RequestFunc::$errText[RequestFunc::UNICOM_CHANNEL_NOT_AVAILABLE]];
        }

        return array(1, 'statusOK');
    }

    public static function Monitor($status, $phone, $logId, $logInfo = [])
    {
        $text = $logInfo['text'] ?? '';
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f55db329-1fa4-4b89-ab10-33dbd2dd9334';

        $targetTimestamp = strtotime($logInfo['send_time']);
        $todayStart = strtotime('today');
        if ($targetTimestamp <= $todayStart) {
            return false;
        }
        $account = $logInfo['account'] ?? '未知';
        // 黑名单账号
        if (in_array($account, ['yikedj106GdKE', 'taibai106', 'xingqiu106', 'zhangyuxq106', 'ygjc106zbb6'])) {
            return false;
        }

        $redis = \RedisAction::connect();
        $cacheKey = sprintf("cache:redis:sms:phone:fail:date:%s:%s", date('Ymd'), $phone);
        $expireTime = strtotime(date('Y-m-d')) + 86400 - time();
        $redis->incr($cacheKey);
        $redis->expire($cacheKey, $expireTime);

        $failCount = (int)$redis->get($cacheKey);
        if ($failCount <= 1) {
            return false;
        }
        $providerNameMap = DataStatisticModel::getInstance()->getProviderNameMap();
        $smsProviderData = DataStatisticModel::getInstance()->getSmsProviderById($logInfo['channel']);
        $sign = $smsProviderData['sign'] ?? '未知';
        $providerName = isset($smsProviderData['action_name']) ? ($providerNameMap[$smsProviderData['action_name']] ?? '未知') : '未知';

        $msg = "";
        if (RUNMODE !== 'production') {
            $msg = "【测试环境】\n";
        }
        $msg .= "预警类型：1001-【单个手机号连续多次短信未到达预警】\n";
        $msg .= sprintf("短信ID：%s\n", $logId);
        $msg .= sprintf("手机号：%s\n", $phone);
        $msg .= sprintf("回执状态：%s（%s）\n", $status, self::$djptMonitorStatus[$status] ?? '未知');
        $msg .= sprintf("供应商：%s\n", $providerName);
        $msg .= sprintf("短信账号：%s\n", $account);
        $msg .= sprintf("短信签名：%s\n", $sign);
        $msg .= "短信内容：$text\n";
        $msg .= sprintf("触发条件：该手机号已在当日连续%d次短信未到达。\n", $failCount);
        $msg = mb_convert_encoding($msg, 'utf-8', 'GBK');
        $data = [
            'msgtype' => 'text',
            'text' => ['content' => $msg],
        ];
        echo $msg . "\n";

        $preg = "/(验证码)(\D+|)(\d{4,6})/";
        if (preg_match($preg, $text, $matches)) {
            if (!empty($matches[3])) {
                return self::postJson($url, $data);
            }
        }
        return false;
    }

    public static function postJson(string $url, $post = [], $timeout = 5)
    {
        $ci = curl_init();
        $raw = json_encode($post);
        curl_setopt($ci, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ci, CURLOPT_POSTFIELDS, $raw);
        curl_setopt($ci, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ci, CURLOPT_BINARYTRANSFER, true);
        curl_setopt($ci, CURLOPT_URL, $url);
        curl_setopt($ci, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ci, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ci, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($raw),
        ]);
        $response = curl_exec($ci);
        $httpCode = curl_getinfo($ci, CURLINFO_HTTP_CODE);
        if ($httpCode == 200 && !empty($response)) {
            $res = $response;
        } else {
            $res = false;
        }
        return $res;
    }

    public function MonitorSendRatioAbnormal($timeStr, $data = [])
    {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=f55db329-1fa4-4b89-ab10-33dbd2dd9334';
        $account = $data['account'] ?? '未知';
        // 黑名单账号
        if (in_array($account, ['yikedj106GdKE', 'taibai106', 'xingqiu106', 'zhangyuxq106', 'ygjc106zbb6'])) {
            return false;
        }
        $ratio = $data['succ_cnt'] / $data['cnt'];
        $confRatio = 0.95;
        if ($ratio >= $confRatio && $data['cnt'] > 10) {
            return false;
        }
        $providerNameMap = DataStatisticModel::getInstance()->getProviderNameMap();
        $smsProviderData = DataStatisticModel::getInstance()->getSmsProviderById($data['channel']);
        $sign = $smsProviderData['sign'] ?? '未知';
        $providerName = isset($smsProviderData['action_name']) ? ($providerNameMap[$smsProviderData['action_name']] ?? '未知') : '未知';

        $msg = "";
        if (RUNMODE !== 'production') {
            $msg = "【测试环境】\n";
        }
        $msg .= "预警类型：1003-【单个短信通道账户到达率异常预警】\n";
        $msg .= sprintf("供应商：%s\n", $providerName);
        $msg .= sprintf("短信账号：%s\n", $account);
        $msg .= sprintf("短信签名：%s\n", $sign);
        $msg .= sprintf("时间段：%s\n", $timeStr);
        $msg .= sprintf("触发条件：发送数%d，到达数%d，到达率为%.2f%%，低于%.2f%%，请及时关注。\n", $data['cnt'], $data['succ_cnt'], $ratio * 100, $confRatio * 100);
        $msg = mb_convert_encoding($msg, 'utf-8', 'GBK');
        $data = [
            'msgtype' => 'text',
            'text' => ['content' => $msg],
        ];
        echo $msg . "\n";

        return self::postJson($url, $data);
    }
}
