{{include file = "public/mem_header.tpl.html"}}
{{include file = "public/left_nav.tpl.html}}
<div class="account_right" style=" height:500px;">
    <div class="changeEmail-A">
        <h2 class="pf">{{if $pageArray.info.email}}验证邮箱{{else}}绑定邮箱{{/if}}</h2>
        <!--绑定邮箱弹窗1 开始-->
        <div id="bindemail">
        <div class="boxCon">
            <div class="ts-wrap">
                <p class="tsTxt">温馨提示：请输入您要绑定的邮箱地址，可用该邮箱登录所有2345产品</p>

                <p class="ts-infor" id="emailtip" style="display: none"><i></i>发送成功，请查收</p>
            </div>
            <!--ts-wrap end-->

            <ul class="conUl">
                <li>
                    <span class="conTit">邮箱地址：</span>

                    <div class="coninput w195">
                        <input class="defaultInput" type="text" id="email" name="email" onblur="checkEmail();" autocomplete="off" {{if $pageArray.info.email}}value="{{$pageArray.info.email}}"{{else}}value="请输入邮箱地址"{{/if}} title="请输入邮箱地址" maxlength="24"/>
                    </div>
                    <a class="messCode" href="javascript:" onclick="sendCode();return false;">发送验证码</a>
                    <span id="sendingBtn" class="messCode-dsib" style="display:none;">重新发送（60s）</span>
                </li>
                <li>
                    <span class="conTit">验证码：</span>
                    <div class="coninput">
                        <input type="number" id="code" name="code" value="" autocomplete="off"/>
                    </div>
                </li>
            </ul>
            <!--conUl end-->
            <a class="btn-tj" id="bindemailsubmit" href="javascript:">提交</a>

            <h2 class="pf mt46">绑定邮箱常见问题</h2>
            <dl class="questionList">
                <dt>我没收到邮件怎么办？</dt>
                <dd>尝试如下方式：请检查邮箱地址是否正确，检查邮件垃圾箱，在邮箱设置中将"2345.com"添加到白名单再点击，若仍未收到，请稍后重新点击发送。</dd>
            </dl>
        </div>
        <!--boxCon end-->
    </div><!--box_mod end-->
    </div>
</div><!--account_right end-->
</div><!--main end-->

<div class="box_mark" style="display: none">遮罩层</div>


<!--绑定邮箱弹窗1 结束-->
<div class="box_mod h232" id="bindsuccess">
    <div class="box_mod_tit"><a class="btn_box_close" href="javascript:"></a></div>
    <div class="infor-con">
        <i class="gouIcon"></i>
        <div class="infor-txt">
            <h2>{{if $pageArray.info.email}}验证邮箱{{else}}绑定邮箱{{/if}}成功！</h2>
            <p id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a></p>
        </div>
    </div><!--boxCon end-->
</div>
<!--主体 end-->
{{include file = "public/qrcode.tpl.html"}}

<!--[if IE 6]>
<script type="text/javascript" src="/js/DD_belatedPNG_0.0.8a-min.js"></script>
<script type="text/javascript">
    DD_belatedPNG.fix('.png24');
</script>
<![endif]-->

<script src="/js/jquery-1.9.1.min.js"></script>
<script src="/js/tab_201509011442.js"></script>
{{include file = "public/mem_footer.tpl.html"}}
<script>
    var bottomObjectTimeId = setInterval(function(){
        if($("#ClCache") != undefined){
            clearInterval(bottomObjectTimeId);
            $("#ClCache").parent().css("display","none");
        }
    },1000);
    var windowHeight = $(window).height();
    var documentHeight;
    function maskBoxHeight()
    {
        var documentHeight = $(document).height();
        if (documentHeight > windowHeight) {
            $(".box_mark").css("height", documentHeight).show();
        } else {
            $(".box_mark").css("height", windowHeight).show();
        };
    };
    $(".coninput input").bind(
            {
                focus:function(){
                    $(this).parent().removeClass("inputTxtError");
                    $(this).parent().addClass("inputTxtFocus");
                    if($(this).val() == $(this).attr("title"))
                    {
                        $(this).val("");
                    }
                    $(this).addClass("colorInput");
                },
                blur:function(){
                    $(this).parent().removeClass("inputTxtFocus");
                    if($.trim($(this).val()) == "")
                    {
                        $(this).val($(this).attr("title"));
                        $(this).removeClass("colorInput");
                    };
                }
            }
    );
    var messageCodeTime = $(".messCode-dsib"),messageTimeNum = 60,btnOk = $(".messCode"),windowHeight = $(window).height();
    //打开弹框
    $("#showbindemail").bind("click",function()
    {
        $("#bindemail").show();
        var windowHeight = $(window).height(), documentHeight = $(document).height();
        if (documentHeight > windowHeight) {
            $(".box_mark").css("height", documentHeight);
        } else {
            $(".box_mark").css("height", windowHeight);
        }

        $(".box_mark").show();
    });
     $("#verify-close").bind("click", function(){
         $("#email").removeClass("colorInput");
         $("#code").val("");
         $("#emailtip").hide();
         messageCodeTime.hide();
         btnOk.text("发送验证码").show();
     });
     //关闭弹框
     $(".btn_box_close").bind("click",function()
     {
         $(".box_mod,.box_mark").hide();
     });
    $("#bindemailsubmit").bind("click", function(){
        var codeErr = true;
        var email = $("#email").val();
        var code = $("#code").val();
        if (emailErr == 1) {
            return false;
        }
        if (code == "") {
            $("#emailtip").html('<i></i>请输入验证码！').addClass("ts-error").show();
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/member/bindEmail/bind",
            async: false,
            data: "email=" + AES.encrypt(email) + "&code=" + code,
            timeout:3000,
            success: function(data) {
                if (data == '200') {
                    codeErr = false;
                    $("#bindsuccess").show();
                    maskBoxHeight()
                    var times = 3;
                    setInterval(function() {
                        if (times > 0) {
                            $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
                        } else {
                            window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
                        }
                        times--;
                    }, 1000)
                } else if (data == '300') {
                    $("#emailtip").html('<i></i>验证码错误，请重新输入').addClass("ts-error").show();
                }else if (data == '400') {
                    $("#emailtip").html('<i></i>验证码错误，请重新输入').addClass("ts-error").show();
                }
                else if (data == '500') {
                    $("#emailtip").html('<i></i>服务器忙，请稍候再试').addClass("ts-error").show();
                }
            }
        });
        return !codeErr;
    });
    var emailErr = 1;
    function checkEmail() {
        var email = $("#email").val();
        if (!email||email == '请输入邮箱地址') {
            emailErr = 1;
            return false;
        }
        if (!/^[_\.0-9a-z-]+@([0-9a-z][0-9a-z-]+\.)+[a-z]{2,4}$/.test(email)) {
            emailErr = 1;
            $("#emailtip").addClass('ts-error').html('<i></i>请输入正确的邮箱地址！').show();
            return false;
        }
        $.ajax({
            url: "/api/check/jsonp",
            data: 'type=email&value=' + email,
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(response) {
                err = response;
                if (err == 1) {
                    emailErr = 1;
                    $("#emailtip").addClass('ts-error').html('<i></i>请输入正确的邮箱地址！').show();
                    return false;
                } else if (err == 2) {
                    emailErr = 1;
                    $("#emailtip").addClass('ts-error').html('<i></i>此邮箱已被绑定，请重新输入！').show();
                    return false;
                } else if (err == 3) {
                    emailErr = 1;
                    $("#emailtip").addClass('ts-error').html('<i></i>此邮箱已被绑定，请重新输入！').show();
                    return false;
                }
                emailErr = 0;
            },
            error: function(jqXHR, textStatus, errorThrown) {
                emailErr = 1;
            },
            timeout: 3000
        });
    }
    function sendCode() {
        var email = $("#email").val();
        if (email == '' || email == $("#email").attr("title")) {
            $("#emailtip").html('<i></i>请先输入邮箱地址！').addClass("ts-error").show();
            return false;
        }
        if (emailErr == 1) {
            return false;
        }
        $.post("/member/bindEmail/sendCode", {'email': AES.encrypt(email)}, function(data) {
            if(data.indexOf("200") >= 0)
            {
                var emailExp = email.split('@');
                if (emailExp[1] == 'gmail.com') {
                    var emailHost = 'http://www.' + emailExp[1];
                } else {
                    var emailHost = 'http://mail.' + emailExp[1];
                }
                $('#emailtip').removeClass('ts-error').html('<i></i>验证邮件已发送，<a class="resgo" href="' + emailHost + '" target="_blank" class="email_url">去邮箱收件</a>').show();
                $("#bindemail .messCode").text("重新发送").css("display", "none");
                $("#bindemail .messCode-dsib").css("display", "block");
                messageTimeNum = 60;
                messageTime();
            } else if (data == '300') {
                $("#emailtip").html('<i></i>请输入正确的邮箱地址！').addClass("ts-error").show();
            } else if (data == '301') {
                $("#emailtip").html('<i></i>此邮箱已被绑定，请重新输入！').addClass("ts-error").show();
            } else if (data == '302') {
                $("#emailtip").html('<i></i>此邮箱地址已被绑定，请重新输入！').addClass("ts-error").show();
            }  else if (data == '400') {
                $("#emailtip").html('<i></i>验证的太频繁了，请稍后再试！').addClass("ts-error").show();
            } else if (data == '402') {
                $("#emailtip").html('<i></i>已经绑定过邮箱！').addClass("ts-error").show();
            } else if (data == '500') {
                $("#emailtip").html('<i></i>服务器忙，请稍后再试！').addClass("ts-error").show();
            }
        });
    }
    function messageTime(){
        messageCodeTime.text("重新发送（"+messageTimeNum+ "s" + "）");
        messageTimeNum --;
        if(messageTimeNum>=0){
            setTimeout(messageTime,1000);
        }else{
            $(".messCode").css("display","block");
            messageCodeTime.css("display","none");
        }
    }
    checkEmail();
</script>
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
