<?php

use Octopus\PdoEx;

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：MonitorAction.php
 * 摘    要：
 * 修改日期：2016.09.26
 * */
class MonitorAction extends Action
{

    protected $pdo;
    protected $channelList = array();
    protected $projectList = array();

    public function __construct()
    {
        $dbConfig = \Config::get("database");
        $this->pdo = PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);

        $smsProject = \Admin\SmsProjectModel::getInstance();
        $projectList = $smsProject->getPojectList();
        foreach($projectList as $val) {
            $pid = $val["pid"];
            $this->projectList[$pid] = $val["project_name"];
        }

		$sql = "SELECT * FROM my_sms_provider";
		$list = $this->pdo->findList($sql);
        foreach($list as $val) {
            $id = $val["id"];
            $this->channelList[$id] = $val["provider_name"];
        }
    }

    public function hourErrorChannel($days, $hour = -1)
    {
        if ($hour == -1)
        {
            $hour = date("H", strtotime("-1 hour"));
        }
        $startTime = date("Y-m-d 00:00:00", strtotime("-" . $days . " days"));
        $endTime = date("Y-m-d 23:59:59");

        $sql = "SELECT
                    ch.channel AS channel,
                    DAY(sms.send_time) AS day,
                    COUNT(1) AS count
                FROM
                    my_sms_logs AS sms
                        JOIN
                    my_sms_logs_channel AS ch ON sms.id = ch.log_id
                        JOIN
                    sms_channel_status AS st ON sms.id = st.log_id
                WHERE
                    sms.send_time >= :startTime
                        AND sms.send_time <= :endTime
                        AND st.status = :status
                        AND hour(sms.send_time) = :lastHour
                GROUP BY ch.channel , DAY(sms.send_time);";
        $params = array(
            ":startTime" => $startTime,
            ":endTime" => $endTime,
            ":lastHour" => $hour,
            ":status" => 0,
        );
        $res = $this->pdo->findList($sql, $params);

        $lineData = array();
        foreach ($res as $key => $val)
        {
            $channel = $val['channel'];
            $day = $val['day'];
            $count = $val['count'];
            $lineData[$channel][$day] = $count;
        }

        echo "======== $days 天内, $hour 点, [通道回执失败数]监控: ========<br />\n";
        foreach ($lineData as $channel => $value)
        {
            //            $value = array( 59, 29, 28, 13, 25, 32, 110 );
            $this->checkValueForadmin($value, $this->channelList[$channel], "right", 100);
        }
    }

    /**
     * 大于和小于都要监控到
     *
     * @param $days
     * @param int $hour
     */
    public function hourTotalChannel($days, $hour = -1)
    {
        if ($hour == -1)
        {
            $hour = date("H", strtotime("-1 hour"));
        }
        $startTime = date("Y-m-d 00:00:00", strtotime("-" . $days . " days"));
        $endTime = date("Y-m-d 23:59:59");

        $sql = "SELECT
                    ch.channel AS channel,
                    DAY(sms.send_time) AS day,
                    COUNT(1) AS count
                FROM
                    my_sms_logs AS sms
                        JOIN
                    my_sms_logs_channel AS ch ON sms.id = ch.log_id
                WHERE
                    sms.send_time >= :startTime
                        AND sms.send_time <= :endTime
                        AND hour(sms.send_time) = :lastHour
                GROUP BY ch.channel , DAY(sms.send_time); ";
        $params = array(
            ":startTime" => $startTime,
            ":endTime" => $endTime,
            ":lastHour" => $hour,
        );
        $res = $this->pdo->findList($sql, $params);

        $lineData = array();
        foreach ($res as $key => $val)
        {
            $channel = $val['channel'];
            $day = $val['day'];
            $count = $val['count'];
            $lineData[$channel][$day] = $count;
        }

        echo "======== $days 天内, $hour 点, [通道总数]监控: ========<br />\n";
        foreach ($lineData as $channel => $value)
        {
            $this->checkValueForadmin($value,  $this->channelList[$channel], "all");
        }
    }

    public function dayTotalChannel($days)
    {
        $startTime = date("Y-m-d 00:00:00", strtotime("-" . $days . " days"));
        $endTime = date("Y-m-d 23:59:59");

        $sql = "SELECT
                    ch.channel AS channel,
                    DAY(sms.send_time) AS day,
                    COUNT(1) AS count
                FROM
                    my_sms_logs AS sms
                        JOIN
                    my_sms_logs_channel AS ch ON sms.id = ch.log_id
                WHERE
                    sms.send_time >= :startTime
                        AND sms.send_time <= :endTime
                GROUP BY ch.channel , DAY(sms.send_time); ";
        $params = array(
            ":startTime" => $startTime,
            ":endTime" => $endTime,
        );
        $res = $this->pdo->findList($sql, $params);

        $lineData = array();
        foreach ($res as $key => $val)
        {
            $channel = $val['channel'];
            $day = $val['day'];
            $count = $val['count'];
            $lineData[$channel][$day] = $count;
        }

        echo "======== $days 天内, 每天[通道总数]监控: ========<br />\n";
        foreach ($lineData as $channel => $value)
        {
            $this->checkValueForadmin($value,  $this->channelList[$channel], "all");
        }
    }

    public function dayTotalProject($days)
    {
        $startTime = date("Y-m-d 00:00:00", strtotime("-" . $days . " days"));
        $endTime = date("Y-m-d 23:59:59");

        $sql = "SELECT
                    sms.pid AS pid,
                    DAY(sms.send_time) AS day,
                    COUNT(1) AS count
                FROM
                    my_sms_logs AS sms
                WHERE
                    sms.send_time >= :startTime
                        AND sms.send_time <= :endTime
                GROUP BY sms.pid, DAY(sms.send_time);";

        $params = array(
            ":startTime" => $startTime,
            ":endTime" => $endTime,
        );
        $res = $this->pdo->findList($sql, $params);

        $lineData = array();
        foreach ($res as $key => $val)
        {
            $pid = $val['pid'];
            $day = $val['day'];
            $count = $val['count'];
            $lineData[$pid][$day] = $count;
        }

        echo "======== $days 天内, 每天[各项目总数]监控: ========<br />\n";
        foreach ($lineData as $pid => $value)
        {
            $this->checkValueForadmin($value, $this->projectList[$pid], "all");
        }
    }

    private function checkValueForAdmin($value, $channel = "", $compare = "all", $max = -1)
    {
        $newValue = array_pop($value);

        if (count($value) < 4)
        {
            return;
        }

        if ($max > 0 && $newValue > $max)
        {
            echo "最大值得比较: channel:$channel, status:danger...<br />\n";
        }

        list($res, $data) = $this->checkLinearValue($value, $newValue, $compare);
        if (!$res)
        {
            echo "预测值比较: channel:$channel, status:warning...<br />\n";
        }
        else
        {
            echo "预测值比较: channel:$channel, status:success...<br />\n";
        }
        if ($max > 0)
        {
            echo "max: ";
            echo "<br />\n";
            echo $max;
            echo "<br />\n";
        }

        echo "value: ";
        echo "<br />\n";
        echo implode(", ", $value);
        echo "<br />\n";

        echo "compare: ";
        echo "<br />\n";
        echo implode(", ", $data);
        echo "<br />\n";
        echo "<br />\n";
        return $res;
    }

    /**
     * @param $value
     * @param $newValue
     * @param float $rate
     * @param string $compare
     *
     * @return array
     */
    public function checkLinearValue($value, $newValue, $compare = "all", $rate = 0.1)
    {
        $linearValue = $this->linear($value);
        $res = true;

        if ($compare == "left")
        {
            // 小于 左值
            if ($newValue < $linearValue * (1 - $rate))
            {
                $res = false;
            }
        }
        elseif ($compare == "right")
        {
            // 大于 右值
            if ($newValue > $linearValue * (1 + $rate))
            {
                $res = false;
            }
        }
        else
        {
            // 大于 右值 或者 小于 左值
            if ($newValue > $linearValue * (1 + $rate) || $newValue < $linearValue * (1 - $rate))
            {
                $res = false;
            }
        }
        return array($res, array($newValue, $linearValue, $linearValue * 0.9, $linearValue * 1.1));
    }

    /**
     * 获取 线性回归 预测值
     *
     * @param $value
     *
     * @return int
     */
    public function linear($value)
    {
        $count = count($value);
        $xArray = array();
        for ($j = 0;$j < $count;)
        {
            $xArray[] = ++$j;
        }
        $xAvg = $this->avg($xArray);
        $yAvg = $this->avg(array_values($value));
        $i = 0;
        $sumTmp1 = 0 - $count * $xAvg * $yAvg;
        $sumTmp2 = 0 - $count * $xAvg * $xAvg;
        foreach ($value as $key => $val)
        {
            $i++;
            $sumTmp1 += $val * $i;
            $sumTmp2 += $i * $i;
        }
        $b = 0;
        if ($sumTmp1 && $sumTmp2)
        {
            $b = $sumTmp1 / $sumTmp2;
        }
        $a = $yAvg - $b * $xAvg;
        return $i * $b + $a;
    }

    private function avg($values)
    {
        return array_sum($values) / count($values);
    }

}
