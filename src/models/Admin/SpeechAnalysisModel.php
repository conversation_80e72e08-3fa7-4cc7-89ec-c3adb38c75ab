<?php

/**
 * 语音分析
 * */
namespace Admin;
class SpeechAnalysisModel extends \Model
{
    /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    public function getSpeechAnalysisData($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col} FROM `freecall_request_yy_total` ";


        if (!empty($data['from']))
        {
            $where .= " `type` = :from AND";
            $paramArr[':from'] = $data['from'];
        }

        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $where .= ' `date` >=:startTime AND `date` <=:endTime AND';
            $paramArr[':startTime'] = $data['startTime'];
            $paramArr[':endTime'] = $data['endTime'];
        }


        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by id desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }


    public function getArrivalRateData($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col} FROM `freecall_request_total` ";


        if (!empty($data['from']))
        {
            $where .= " `channel` = :from AND";
            $paramArr[':from'] = $data['from'];
        }

        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $where .= ' `date` >=:startTime AND `date` <=:endTime AND';
            $paramArr[':startTime'] = $data['startTime'];
            $paramArr[':endTime'] = $data['endTime'];
        }


        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by id desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }
}

?>