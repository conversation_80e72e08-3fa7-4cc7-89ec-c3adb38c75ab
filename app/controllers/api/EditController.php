<?php

class EditController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }
        $passid = $_POST['uid'];
        $type = $_POST['type'];
        if ($passid == '')
        {
            die('-1');
        }
        $memberModel = loadModel('member');
        if ($type == 'w')
        {
            if (filter_var($_POST['ip'], FILTER_VALIDATE_IP))
            {
                $ip = $_POST['ip'];
            }
            else
            {
                $ip = '0.0.0.0';
            }
            $item = array('name', 'gender', 'bday', 'qq', 'area');
            $update = array();
            foreach ($_POST as $key => $val)
            {
                if (in_array($key, $item))
                {
                    $update[$key] = $val;
                }
            }
            if (!empty($update))
            {
                if ($memberModel->edit($passid, $update))
                {
                    echo 1;
                }
            }
        }
        elseif ($type == 'r')
        {
            $info = $memberModel->read($passid);
            echo serialize($info);
        }
        elseif ($type == 'p')
        {
            if (filter_var($_POST['ip'], FILTER_VALIDATE_IP))
            {
                $ip = $_POST['ip'];
            }
            else
            {
                $ip = '0.0.0.0';
            }
            if ($memberModel->chgPassword($passid, $_POST['old'], $_POST['new'], $_POST['pwd_strength'], $ip))
            {
                //日志行为打点:用户信息修改; type:USER_INFO_MODIFY; sub_type:PASSWORD;
                $info = $memberModel->read($passid);
                $info['passid'] = $passid;
                $objLogV2Action = loadAction('logV2');
                $objLogV2Action->report('ALL', 'USER_INFO_MODIFY', 'PASSWORD', $info, '', '');
                echo 1;
            }
            else
            {
                echo - 1;
            }
        }
    }

}