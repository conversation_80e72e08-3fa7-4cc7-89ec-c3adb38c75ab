<?php

class SignController extends Controller
{

    public function actionIndex()
    {
        session_start();
        if (!isset($_SESSION['expire']))
        {
            $_SESSION['expire'] = 0;
        }
        $loginAction = loadAction('login');
        if ($_SERVER["REQUEST_METHOD"] == "POST")
        {
            $isValidate = 0;
            if ($_SESSION['expire'] > 3 || $loginAction->decideCode(get_client_ip()))
            {
                $return['display'] = '';
                $check_code = $_POST['check_code'];
                if ($check_code != "" && $_SESSION['checkIMGCode'] != "")
                {
                    if ($check_code != $_SESSION['checkIMGCode'])
                    {
                        $_SESSION['checkIMGCode'] = "";
                        $return['sts'] = 3;
                        die(json_encode($return));
                    }
                }
                else
                {
                    $return['sts'] = 4;
                    die(json_encode($return));
                }
                unset($_SESSION['checkIMGCode']);
                $isValidate = 1;
            }
            else
            {
                $return['display'] = 'none';
            }
            $username = htmlspecialchars(iconv("UTF-8", "gb2312", $_POST['username']), ENT_QUOTES, 'ISO-8859-1');
            $password = htmlspecialchars($_POST['password'], ENT_QUOTES, 'ISO-8859-1');

            $source = \Service\AccountStat\Consts\AccountStat::SRC_IE_CHROME;
            $result = $loginAction->login($username, $password, 'chrome.login.2345.com', get_client_ip(), $isValidate, $_SERVER['HTTP_USER_AGENT'], $source);
            $state = $result[0];
            if ($state == 6)
            {
                $return['sts'] = 1006;
                die(json_encode($return));
            }
            elseif ($state == 5)
            {
                $return['sts'] = 1005;
                die(json_encode($return));
            }
            elseif ($state == 4)
            {
                $return['sts'] = 1004;
                die(json_encode($return));
            }
            elseif ($state == 3)
            {
                $_SESSION['expire'] = 4;
                $return['sts'] = 3;
                $return['display'] = '';
                die(json_encode($return));
            }
            elseif ($state == 2)
            {
                $return['sts'] = -1;
                die(json_encode($return));
            }
            elseif ($state === -1)
            {
                $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
                $return['sts'] = 1;
                if ($_SESSION['expire'] > 3)
                {
                    $return['display'] = '';
                }
                else
                {
                    $return['display'] = 'none';
                }
                die(json_encode($return));
            }
            elseif ($state === 1)
            {
                unset($_SESSION['expire']);
                die("{'sts':0,'uid':'{$result["uid"]}','sec':'" . get_sec_browser($result["uid"]) . "','passid':'{$result["passid"]}','token':'{$result["token"]}'}");
            }
        }
        else
        {
            $pageArray = array();
            if ($_SESSION['expire'] > 3 || $loginAction->decideCode(get_client_ip()))
            {
                $pageArray["display"] = "";
            }
            else
            {
                $pageArray["display"] = "none";
            }
            loadView('chrome/sign.tpl.html', $pageArray);
        }
    }

}