<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/6/5
 * Time: 15:15
 */

class SmsProjectBindLogIdAction extends Action
{
    private $redis;
    private static $projectSmsKey = 'project:sms:zscore';

    /**
     * SmsProjectBindLogIdAction constructor.
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->redis = RedisAction::connect();
    }

    /**
     * 功    能：检查流水号是否已发送
     * 修改日期：2019年6月11日
     *
     * @param int $projectSmsId 流水号
     * @author: wangchenglong
     * @return null
     */
    public function checkProjectId($projectSmsId)
    {
        $count = $this->redis->zScore(self::$projectSmsKey, $projectSmsId);
        if (!empty($count)) {
            return true;
        }
        return false;
    }

    /**
     * 功    能：载入
     * 修改日期：2019年6月11日
     *
     * @param int $projectSmsId 流水号
     * @param array $phoneLogId  logid
     * @author: wangchenglong
     * @return null
     */
    public function loadSmsProjectBindLog($projectSmsId, $phoneLogId)
    {
        foreach ($phoneLogId as $value) {
            $this->redis->zAdd(self::$projectSmsKey, $value, $projectSmsId);
        }
    }

    /**
     * 功    能：清空3天前的数据
     * 修改日期：2019年6月11日
     *
     * @author: wangchenglong
     * @return null
     */
    public function clearSmsProjectBindLog()
    {
        $date = date('Ym', strtotime("-3 day"));
        $dateTime = date('Y-m-d 00:00:00', strtotime("-3 day"));
        $smsModel = \SmsModel::getInstance();
        $phoneLogIds = $smsModel->getSmsLogIdByDate($date, $dateTime);
        if ($phoneLogIds) {
            $this->redis->zRemRangeByScore(self::$projectSmsKey, 0, $phoneLogIds);
            return true;
        } else {
            return false;
        }
    }

}
