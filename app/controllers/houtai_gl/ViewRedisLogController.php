<?php

includeBase('AdminController');

/**
 * Class ViewRedisLogController
 */
class ViewRedisLogController extends AdminController
{

    protected $redis;

    /**
     * ViewRedisLogController constructor.
     */
    public function __construct()
    {
        if (!in_array($_COOKIE['admin_user'], ['潘军', '杜海明', '朱锋锦']))
        {
            die('access deny!');
        }
        parent::__construct();
        loadAction('LoadRedis');
        $this->redis = \LoadRedisAction::getInstance('dlx_log', 'hash1');
    }

    /**
     * @author：dongx
     * @return void
     */
    public function actionIndex()
    {
        $date = $_GET['date'];
        $redisKey = 'APICALLED:' . $date;
        if ($this->redis->exists($redisKey))
        {
            $hash = $this->redis->hGetAll($redisKey);
            var_export($hash);
        }
    }
}
