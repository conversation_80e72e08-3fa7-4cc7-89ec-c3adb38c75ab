<?php

/**
 * 外部发送短信封装的类
 * <AUTHOR>
 */

class ApiSmsSenderAction extends SmsAction
{

    protected $phone;
    protected $msg;
    protected $smsType;
    protected $pid;
    protected $clientIp;
    protected $positionId;
    protected $passid;
    protected $submitParam = array();
    protected $isInit = false;
    protected $phoneLogIds = array();

    /**
     * 传入数据初始化
     *
     * @param array $data initData
     *
     * @return void
     */
    protected function init($data)
    {
        foreach ($data as $key => $info)
        {
            $this->$key = $info;
        }
    }

    /**
     * @param array $data data
     *
     * @return void
     */
    public function setSubmitData($data)
    {
        $this->submitParam = $data;
    }

    /**
     * @return array
     */
    public function getSubmitData()
    {
        return $this->submitParam;
    }

    /**
     * 短信发送接口
     *
     * @param array $data data
     *
     * @return bool
     */
    public function apiSend($data)
    {
        $this->init($data);
        $this->isInit = true;
        if (RUNMODE == 'development') {
            $channelTest = [
                108 => [2 => 134],
                110 => [2 => 135],
                5 => [1 => 138, 2 => 137],
                6 => [1 => 138, 2 => 136],
            ];
        } else {
            $channelTest = [
                141 => [2 => 251],
                138 => [2 => 252],
                8 => [1 => 256, 2 => 253],
                6 => [1 => 255, 2 => 254],
            ];
        }
        if (
            time() < strtotime('2019-11-14 17:00:00') &&
            array_key_exists($this->smsType, $channelTest)
        ) {
            $this->forceReturnJson = true;
            $smsType = $this->smsType;
            $channelTest[$smsType][0] = $smsType;
            $phoneList = [];
            foreach ($this->phone as $phone) {
                $remainder = ((int)substr($phone, -4)) % 11;
                if (isset($channelTest[$smsType][$remainder])) {
                    $phoneList[$remainder][] = $phone;
                } else {
                    $phoneList[0][] = $phone;
                }
            }
            $success = 0;
            $phoneLogIds = [];
            foreach ($phoneList as $index => $phone) {
                $this->smsType = $channelTest[$smsType][$index];
                $this->phone = $phone;
                $this->multi = count($phone) > 1 ? true : false;
                $res = $this->send();
                if ($res[0] == 1) {
                    $success++;
                }
                if (isset($res[2]['phoneLogIds'])) {
                    $phoneLogIds += $res[2]['phoneLogIds'];
                }
            }
            $this->forceReturnJson = false;
            if ($success > 0) {
                if ($success == count($phoneList)) {
                    $code = [1, self::$errStr[1]];
                } else {
                    $code = [2, '部分成功'];
                }
            } else {
                $code = [0, self::$errStr[0]];
            }
            $phoneLogIds && $phoneLogIds = [
                'phoneLogIds' => $phoneLogIds
            ];
            return $this->formatReturn($code, $phoneLogIds, true);
        }
        $res = $this->send();
        return $res;
    }


    /**
     * 验证参数
     * @param array $requiredParamList paramList
     *
     * @return bool|array
     */
    public function checkParams($requiredParamList)
    {
        foreach ($requiredParamList as $requiredKey => $requiredVal)
        {
            if (empty($requiredVal))
            {
                switch ($requiredKey)
                {
                    case 'phone':
                        return $this->formatReturn(array(-7, self::$errStr[-7]));
                        break;
                    case 'smsType':
                        return $this->formatReturn(array(-14, self::$errStr[-14]));
                        break;
                    case 'msg':
                        //如果传递是语音,那么就不在判断msg是否为空
                        if ($requiredParamList['appType'] === 2)
                        {
                            break;
                        }

                        return $this->formatReturn(array(-18, self::$errStr[-18]));
                        break;
                    case 'pid':
                        return $this->formatReturn(array(-13, self::$errStr[-13]));
                        break;
                    case 'clientIp':
                        return $this->formatReturn(array(-19, self::$errStr[-19]));
                        break;
                }
            }
        }

        return true;
    }


}
