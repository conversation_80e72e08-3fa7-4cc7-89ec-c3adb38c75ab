<?php
/**
 * 头像/昵称生态配置列表Model
 * User: yinmt
 * Date: 2020/04/27
 */

class GroupConfigModel extends Model
{
    private $pdo;
    private $tableName = 'group_config';

    /**
     * 初始化
     * -
     * AdminModel constructor.i
     */
    public function __construct() {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_MAIN, $dbConfig[DB_PASSPORT_MAIN]);
    }

    /**
     * 获取生态配置列表
     * <AUTHOR>
     * @return array|bool
     */
    public function getList() {
        $sql = 'select * from ' . $this->tableName;
        $result = $this->pdo->findAll($sql);

        $list = [];
        foreach ($result as $item) {
            $group = $item['group_name'];
            $key = $item['key'];
            if (!isset($list[$group])) {
                $list[$group]['groupName'] = $group;
            }
            $list[$group][$key] = $item['value'];
        }

        return $list;
    }

    /**
     * 新增生态配置
     * <AUTHOR>
     * @param string $groupName 生态
     * @param array $configs 配置数据
     * @return bool
     */
    public function addGroupConfig($groupName, $configs) {
        foreach ($configs as $key => $value) {
            $data = [
                'group_name' => $groupName,
                'key' => $key,
                'value' => $value,
                'created_by' => $_COOKIE['admin_user']
            ];

            if (!$this->pdo->insert($this->tableName, $data)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 按生态删除数据
     * <AUTHOR>
     * @param string $groupName 生态
     * @return bool|int
     */
    public function delByGroupName($groupName) {
        $condition = [
            "where" => "group_name = :group_name",
            "params" => [
                ":group_name" => $groupName,
            ],
        ];

        return $this->pdo->delete($this->tableName, $condition);
    }

    /**
     * 判断生态存在
     * <AUTHOR>
     * @param string $groupName 生态
     * @return array|bool
     */
    public function groupExist($groupName) {
        $sql = 'select `id` from ' . $this->tableName . ' where group_name=:group_name';
        $params = [':group_name' => $groupName];

        $result = $this->pdo->findAll($sql, $params);
        return $result;
    }

    /**
     * 按生态获取数据
     * <AUTHOR>
     * @param string $groupName 生态
     * @return mixed
     */
    public function getConfigByGroup($groupName) {
        $sql = 'select * from ' . $this->tableName . ' where group_name=:group_name';
        $params = [':group_name' => $groupName];

        $result = $this->pdo->findAll($sql, $params);
        foreach ($result as $item) {
            $key = $item['key'];
            $list[$key] = $item['value'];
        }
        return $list;
    }
}
