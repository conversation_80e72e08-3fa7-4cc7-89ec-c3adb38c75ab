<!DOCTYPE HTML>
<html>
    <head>
        <meta charset="gb2312" />
        <title>用户中心-找回密码</title>
        <meta content="" name="Description" />
        <meta content="" name="Keywords" />
        <link rel="stylesheet" type="text/css"  href="/css/v2/common_1_30.css?v={{$smarty.const.STATIC_FILE_VERSION}}">
        <link rel="stylesheet" type="text/css"  href="/css/v2/uc_9_18.css">
        <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
    </head>
    <body>
        <div class="wrapper">
            <div class="pheader">
                <div class="pheader-col">
                    <a class="logo" href="/member/edit_info.php"></a>
                    <div class="userinfo">
                        <span class="item"><a href="http://www.2345.com">首页</a></span>
                    </div>
                </div>
            </div>
            <div class="main">
                {{if $pageArray.step =='1'}}
               <div class="m-boxA minHeight">
			<div class="m-boxA-hd">
				<h2 class="m-boxA-tit">找回密码</h2>
				<a class="more" href="/login">&lt; 返回登录页面</a>
			</div>
			<div class="m-boxA-bd">
                <form id="find-form" action="/find/step2" method="post">
				<div class="ucfnBox">
					<div class="m-form">
						<div class="form-item">
							<span class="form-field">您的帐号：</span>

                                <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
                                <!--ipt_defa ipt_txt_error-->
                                <input class="ipt_txt ipt_defa" type="text" id="username"   value="手机/已验证邮箱/用户名" />
                                <input  type="hidden" id="username1" name="username"  value=" " />
							     <span class="form-tips form-tips-error accountError" style="display: none;"><i class="icon-error"></i>此用户名不存在</span>


						</div>
						<div class="form-item form-item-yzm">
							<div class="form-field">验证码：</div>
							<div class="form-con clearfix">
								<input class="ipt_txt ipt-txt-yzm" id="yzmcode" type="text" value="请输入四位数字" name="check_code" >
                                <!--这里src不能是#，不然会请求当前url，会造成session数据错误-->
                                <img class="img-yzm clickImg" src="" alt="">
                                <a class="trig-link clickImg" href="#">换一换</a>
                                <span class="form-tips form-tips-error codeError" style="display: none;"><i class="icon-error"></i>此用户名不存在</span>
							</div>
                            <div class="form-tips"></div>
						</div>


						<div class="form-item">
							<a class="btn-blueA commitFindData" href="###">下一步</a>
						</div>
					</div>
                    </form>
				</div>
			</div>
		</div>
		<!--找回密码1 end-->
    <script type="text/javascript">
        if (!("onpageshow" in window)) {
            onload = function() {
                $('.img-yzm,clickImg').prop('src','/Captcha?' + Math.random() );
                $("#yzmcode").val('请输入验证码');
            }
        } else {
			onpageshow = function() {
                $('.img-yzm,clickImg').prop('src','/Captcha?' + Math.random() );
                $("#yzmcode").val('请输入验证码');
            }
		}

        var emailErr = 1, phoneErr = 1, usernameErr = 1;
        $(function() {
            $("#username").focus(function() {
                if ($(this).val() == '手机/已验证邮箱/用户名') {
                    $(this).val('');
                }
            }).blur(function() {
                if ($(this).val() == '') {
                    $(this).val('手机/已验证邮箱/用户名');
                    $(this).parent().removeClass('form-item-cur');
                }
                else
                {
                    $.ajax({
                        url: "/webapi/check/jsonp",
                        data: 'value=' + $(this).val() + '&with=0',
                        async: false,
                        dataType: 'jsonp',
                        jsonp: 'callback',
                        success: function(status) {
                            if(status == 2)
                            {
                                $("#username").addClass('ipt_txt_error');
                                $('.accountError').html('<i class="icon-error"></i>帐号不存在').show();
                            }

                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                                phoneErr = 1;
                        },
                        timeout: 3000
                    });
                }
            });

            $("#yzmcode").val('请输入验证码');
            $("#yzmcode").focus(function() {
                if ($(this).val() == '请输入验证码') {
                    $(this).val('');
                }
                $(this).addClass('ipt_defa');
            }).blur(function() {
                if ($(this).val() == '') {
                    $(this).val('请输入验证码');
                    $(this).parent().parent().removeClass('form-item-cur');
                }
            });
        });
        function getCookie(name){
            var arr,reg= new RegExp("(^| )"+name+"=([^;]*)(;|$)");
            if(arr=document.cookie.match(reg))
            {
                return unescape(arr[2]);
            }
            else
            {
                return null;
            }
        }
                $('.commitFindData').click(function (){
                    var username = $("#username").val();
                    var yzmcode = $("#yzmcode").val();
                    if (username == '' || username =='手机/已验证邮箱/用户名')
                    {
                        $("#username").addClass('ipt_txt_error');
                        $('.accountError').html('<i class="icon-error"></i>请输入要找回的帐号').show();
                        return false;
                    }
                    if (yzmcode == '' || yzmcode =='请输入验证码')
                    {
                        $("#yzmcode").addClass('ipt_txt_error');
                        $('.codeError').html('<i class="icon-error"></i>请输入正确的四位数字').show();
                        return false;
                    }
                    $("#username1").val(AES.encrypt(username));
                    $('#find-form').submit();
                });

                if ( '{{$pageArray.yzmError}}' != '' )
                {
                    $('.codeError').html('<i class="icon-error"></i>{{$pageArray.yzmError}}').show();
                    $("#username").val('{{$pageArray.userName}}');

                }
                $('.clickImg').click(function (){
                    var d = new Date();
                    $('.img-yzm,clickImg').prop('src','/Captcha?d=' + d.getTime() );
                })

                </script>
                {{elseif $pageArray.step == '2'}}
                {{if $pageArray.repeat}}
                <div class="m-boxA minHeight">
                    <div class="m-boxA-hd">
                        <h2 class="m-boxA-tit">找回密码</h2>
                        <a class="more" href="/member/edit_info.php">&lt; 返回登录页面</a>
                    </div>
                    <div class="m-boxA-bd">
                        <div class="ucfnBox">
                            <form id="find-form" action="/find/step2" method="GET" onsubmit="return checkAll();">
                                <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
                                <input type="hidden" name="from" value="username"/>
                                <div class="titTips">请输入用户名：</div>
                                <div class="m-form">
                                    <div class="form-item form-item-top">
                                        <input class="ipt_txt ipt_defa" type="text" id="username" name="username" value="请输入用户名" onblur="checkUsername();"/>
                                        <span class="form-tips form-tips-error" id="usernameMsg" style="display: none;"><i class="icon-error"></i></span>
                                    </div>
                                </div>
                                <div class="btnNext"><a class="btn-blueA" href="#" onclick="$('#find-form').submit();
                                        return false;">下一步</a></div>
                            </form>
                        </div>
                    </div>
                </div>
                <script type="text/javascript">
                                    var usernameErr = 1;
                                    $(function() {
                                        $("#username").val('请输入用户名');
                                        $("#username").focus(function() {
                                            if ($(this).val() == '请输入用户名') {
                                                $(this).val('');
                                            }
                                        }).blur(function() {
                                            if ($(this).val() == '') {
                                                $(this).val('请输入用户名');
                                            }
                                        });
                                    });
                                    function checkUsername() {
                                        var username = $("#username").val();
                                        if (!username) {
                                            usernameErr = 1;
                                            return false;
                                        }
                                        if (/[^\u4E00-\u9FA5\w_@\.\-]/.test(username)) {
                                            usernameErr = 1;
                                            $("#username").addClass('ipt_txt_error');
                                            $("#usernameMsg").html('<i class="icon-error"></i>请输入正确的用户名！');
                                            $("#usernameMsg").show();
                                            return false;
                                        }
                                        $.ajax({
                                            url: "/api/check/jsonp",
                                            data: 'type=username&value=' + username,
                                            async: false,
                                            dataType: 'jsonp',
                                            jsonp: 'callback',
                                            success: function(response) {
                                                err = response;
                                                if (err == 0)
                                                {
                                                    usernameErr = 1;
                                                    $("#username").addClass('ipt_txt_error');
                                                    $("#usernameMsg").html('<i class="icon-error"></i>此用户名不存在！');
                                                    $("#usernameMsg").show();
                                                    return false;
                                                }
                                                else if (err == 2)
                                                {
                                                    usernameErr = 1;
                                                    $("#username").addClass('ipt_txt_error');
                                                    $("#usernameMsg").html('<i class="icon-error"></i>请输入正确的用户名！');
                                                    $("#usernameMsg").show();
                                                    return false;
                                                }
                                                usernameErr = 0;
                                            },
                                            error: function(jqXHR, textStatus, errorThrown) {
                                                usernameErr = 1;
                                            },
                                            timeout: 3000
                                        });
                                    }
                                    function checkAll() {
                                        checkUsername();
                                        return !usernameErr;
                                    }
                </script>
                {{elseif $pageArray.userinfo.email||$pageArray.userinfo.phone}}
                <div class="m-boxA minHeight">
                    <div class="m-boxA-hd">
                        <h2 class="m-boxA-tit">找回密码</h2>
                        <a class="more" href="/member/edit_info.php">&lt; 返回登录页面</a>
                    </div>
                    <div class="m-boxA-bd">
                        <div class="ucfnBox">
                            <form id="find-form" action="
                            {{if $pageArray.from != 'username'}}
                                /find/step3
                            {{else}}
                                /find/step2_5
                            {{/if}}
                            " method="GET">
                                <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
                                <div class="titTips">选择找回方式：</div>
                                <ul class="typeSel">
                                    {{if $pageArray.userinfo.phone && $pageArray.userinfo.email}}
                                    <li class="current">
                                        <i class="i-type i-type-phone"></i>
                                        <div class="tit">以手机方式</div>
                                        <div class="data">{{$pageArray.userinfo.phone}}</div>
                                    </li>
                                    <li>
                                        <i class="i-type i-type-mail"></i>
                                        <div class="tit">以绑定邮箱方式</div>
                                        <div class="data">{{$pageArray.userinfo.email}}</div>
                                    </li>
									<input type="hidden" name="from" id="from" value="phone"/>
									{{if $pageArray.from != 'username'}}
										<input type="hidden" name="csrf" value="{{$pageArray.csrf}}" />
										<input type="hidden" name="code" id="code" value="{{$pageArray.md5phone}}" />
									{{/if}}
                                    {{elseif $pageArray.userinfo.phone}}
                                    <li class="current">
                                        <i class="i-type i-type-phone"></i>
                                        <div class="tit">以手机方式</div>
                                        <div class="data">{{$pageArray.userinfo.phone}}</div>
                                    </li>
									<input type="hidden" name="from" id="from" value="phone"/>
									{{if $pageArray.from != 'username'}}
										<input type="hidden" name="csrf" value="{{$pageArray.csrf}}" />
										<input type="hidden" name="code" id="code" value="{{$pageArray.md5phone}}" />
									{{/if}}
                                    {{else}}
                                    <li class="current">
                                        <i class="i-type i-type-mail"></i>
                                        <div class="tit">以绑定邮箱方式</div>
                                        <div class="data">{{$pageArray.userinfo.email}}</div>
                                    </li>
									<input type="hidden" name="from" id="from" value="email"/>
									{{if $pageArray.from != 'username'}}
										<input type="hidden" name="csrf" value="{{$pageArray.csrf}}" />
										<input type="hidden" name="code" id="code" value="{{$pageArray.md5email}}" />
									{{/if}}
                                    {{/if}}
                                </ul>
                                <div class="btnNext"><a class="btn-blueA" href="#" onclick="$('#find-form').submit();
                                        return false;">下一步</a></div>
                            </form>
                        </div>
                    </div>
                </div>
                <script type="text/javascript">
                                    $(function() {
                                        $(".typeSel > li").bind({
                                            mouseenter: function() {
                                                $(this).addClass("hover");
                                            },
                                            mouseleave: function() {
                                                $(this).removeClass("hover");
                                            },
                                            click: function() {
                                                $(".typeSel > li").removeClass("current");
                                                $(this).addClass('current');
                                                if ($(this).find('i').hasClass('i-type-mail')) {
                                                    $("#from").val('email');
                                                    $('#code').val('{{$pageArray.md5email}}');
                                                } else {
                                                    $("#from").val('phone');
                                                    $('#code').val('{{$pageArray.md5phone}}');
                                                }
                                            }
                                        });
                                    });
                </script>
                {{else}}
                <div class="m-boxA minHeight">
                    <div class="m-boxA-hd">
                        <h2 class="m-boxA-tit">找回密码</h2>
                        <a class="more" href="/member/edit_info.php">&lt; 返回登录页面</a>
                    </div>
                    <div class="m-boxA-bd">
                        <div class="ucfnBox">
                            <div class="titTips">您的帐号没有绑定过手机或邮箱！</div>
                            <ul class="m-txtA">
                                <li>请致电客服：<strong>************</strong></li>
                            </ul>
                        </div>
                    </div>
                </div>
                {{/if}}
                {{elseif $pageArray.step == '2_5'}}
                <div class="m-boxA minHeight">
                    <div class="m-boxA-hd">
                        <h2 class="m-boxA-tit">找回密码</h2>
                        <a class="more" href="/member/edit_info.php">&lt; 返回登录页面</a>
                    </div>
                    <div class="m-boxA-bd">
                        <div class="ucfnBox">
                            <div class="m-form">
                                <form id="find-form" action="/find/step3" method="GET">
                                    <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
                                    <input type="hidden" id="from" name="from" value="{{$pageArray.from}}"/>
                                    <input type="hidden" name="csrf" value="{{$pageArray.csrf}}" />
									<input type='hidden' id='realcode' name='code' value='' />
                                    {{if $pageArray.from == 'email'}}
                                    <div class="form-item">
                                        <span class="form-field">绑定邮箱：</span>
                                        <span class="yzmCon">{{$pageArray.userinfo.email}}</span>
                                    </div>
                                    <div class="form-item">
                                        <span class="form-field">确认邮箱：</span>
                                        <input class="ipt_txt ipt_defa" id="code" type="text" placeholder="请输入你的验证邮箱,以确保是本人操作" />
                                        <span class="form-tips form-tips-error" id="checkMsg" style="display: none;"><i class="icon-error"></i></span>
                                    </div>
                                    <div class="form-item">
                                        <a class="btn-blueA" href="#" onclick="return checkUsername();">下一步</a>
                                    </div>
                                    {{else}}
                                    <div class="form-item">
                                        <span class="form-field">手机号：</span>
                                        <span class="yzmCon">{{$pageArray.userinfo.phone}}</span>
                                    </div>
                                    <div class="form-item">
                                        <span class="form-field">确认手机号：</span>
                                        <input class="ipt_txt ipt_defa" id="code" type="text" placeholder="请输入你的手机号码,以确保是本人操作" />
                                        <span class="form-tips form-tips-error" id="checkMsg" style="display: none;"><i class="icon-error"></i></span>
                                    </div>
                                    <div class="form-item">
                                        <a class="btn-blueA" href="#" onclick="return checkUsername();">下一步</a>
                                    </div>
                                    {{/if}}
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <script type="text/javascript" src="/js/login.js"></script>
                <script type="text/javascript">
                    if( !('placeholder' in document.createElement('input')) ){

                        $('input[placeholder],textarea[placeholder]').each(function(){
                            var that = $(this),
                                    text= that.attr('placeholder');
                            if(that.val()===""){
                                that.val(text).addClass('placeholder');
                            }
                            that.focus(function(){
                                if(that.val()===text){
                                    that.val("").removeClass('placeholder');
                                }
                            })
                            .blur(function(){
                                if(that.val()===""){
                                    that.val(text).addClass('placeholder');
                                }
                            })
                            .closest('form').submit(function(){
                                if(that.val() === text){
                                    that.val('');
                                }
                            });
                        });
                    }

                    function checkUsername() {
                        var username = $.trim($("#code").val());
                        var from = $.trim($('#from').val());
                        var msg = 'email' == from ? '请您输入验证邮箱' : '请您输入验证手机';
                        if (!username) {
                            $("#code").addClass('ipt_txt_error');
                            $("#checkMsg").html('<i class="icon-error"></i>'+msg);
                            $("#checkMsg").show();
                            return false;
                        }
                        if (/[^\u4E00-\u9FA5\w_@\.\-]/.test(username)) {
                            $("#code").addClass('ipt_txt_error');
                            $("#checkMsg").html('<i class="icon-error"></i>'+msg);
                            $("#checkMsg").show();
                            return false;
                        }
                        $.ajax({
                            url: "/webapi/check/jsonCode",
                            data: 'value=' + username + '&type='+$('#from').val(),
                            async: false,
                            dataType: 'jsonp',
                            jsonp: 'callback',
                            success: function(status) {
                                if (status == 0)
                                {
                                    $("#code").addClass('ipt_txt_error');
                                    $('#checkMsg').html('<i class="icon-error"></i>帐号不存在').show();
                                    return false;
                                }
                                else if (status == 2)
                                {
                                    var msg = 'email' == from ? '邮箱不匹配' : '手机号不匹配';
                                    $("#code").addClass('ipt_txt_error');
                                    $('#checkMsg').html('<i class="icon-error"></i>'+msg).show();
                                    return false;
                                }
                                $("#realcode").val(MD5(username));
                                $('#find-form').submit();
                            },
                            error: function(jqXHR, textStatus, errorThrown) {
                            },
                            timeout: 3000
                        });
                    }
                </script>
                {{elseif $pageArray.step == '3'}}
                <div class="m-boxA minHeight">
                    <div class="m-boxA-hd">
                        <h2 class="m-boxA-tit">找回密码</h2>
                        <a class="more" href="/member/edit_info.php">&lt; 返回登录页面</a>
                    </div>
                    <div class="m-boxA-bd">
                        <div class="ucfnBox">
                            <div class="m-form">
                                <form id="find-form" action="/find/step4" method="GET" onsubmit="return checkCode();">
                                    <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
                                    <input type="hidden" id="from" name="from" value="{{$pageArray.from}}"/>
                                    {{if $pageArray.from == 'email'}}
                                    <div class="form-item">
                                        <span class="form-field">邮箱地址：</span>
                                        <span class="yzmCon">{{$pageArray.userinfo.email}}</span>
                                        <a id="sendBtn" class="btn-yelA" href="#" onclick="sendCode();
                                        return false;">发送验证码</a>
                                        <span id="sendingBtn" class="btn-grayA" style="display: none;">重新发送（60）</span>
                                        <span class="form-tips form-tips-error" id="sendMsg" style="display: none;"><i class="icon-error"></i></span>
                                    </div>
                                    <div class="form-item">
                                        <span class="form-field">验证码：</span>
                                        <input class="ipt_txt ipt_defa" id="code" name="code" type="text" value="输入您绑定邮箱所收到的验证码" />
                                        <span class="form-tips form-tips-error" id="checkMsg" style="display: none;"><i class="icon-error"></i></span>
                                    </div>
                                    <div class="form-item">
                                        <a class="btn-blueA" href="#" onclick="$('#find-form').submit();
                                        return false;">下一步</a>
                                    </div>
                                    {{else}}
                                    <div class="form-item">
                                        <span class="form-field">手机号：</span>
                                        <span class="yzmCon">{{$pageArray.userinfo.phone}}</span>
                                        <a id="sendBtn" class="btn-yelA" href="#" onclick="sendCode();
                                        return false;">发送验证码</a>
                                        <span id="sendingBtn" class="btn-grayA" style="display: none;">重新发送（60）</span>
                                        <span class="form-tips form-tips-error" id="sendMsg" style="display: none;"><i class="icon-error"></i></span>
                                    </div>
                                    <div class="form-item">
                                        <span class="form-field">验证码：</span>
                                        <input class="ipt_txt ipt_defa" id="code" name="code" type="text" value="输入您绑定手机所收到的验证码" />
                                        <span class="form-tips form-tips-error" id="checkMsg" style="display: none;"><i class="icon-error"></i></span>
                                    </div>
                                    <div class="form-item">
                                        <a class="btn-blueA" href="#" onclick="$('#find-form').submit();
                                        return false;">下一步</a>
                                    </div>
                                    {{/if}}
                                </form>
                            </div>
                        </div>
                        {{if $pageArray.from == 'email'}}
                        <div class="nogetTips">
                            <p><strong>我没收到邮件？</strong></p>
                            <p>1、请检查邮箱地址是否正确。</p>
                            <p>2、请检查你的邮件垃圾箱。</p>
                            <p>3、请在邮箱设置中将“2345.com”添加到白名单后再点击。</p>
                            <p>4、若仍未收到，请稍后重新点击发送。</p>
                        </div>
                        {{/if}}
                        <script type="text/javascript">
                                    $(function() {
                                        /**
                                         * {{if $pageArray.rstep}}
                                         */
                                                sendCode();
                                         /**
                                          * {{/if}}
                                          */

                                        /**
                                         * {{if $pageArray.from == 'email'}}
                                         */

                                        $("#code").focus(function() {
                                            if ($(this).val() == "输入您绑定邮箱所收到的验证码") {
                                                $(this).val('');
                                            }
                                        }).blur(function() {
                                            if ($(this).val() == '') {
                                                $(this).val("输入您绑定邮箱所收到的验证码");
                                            }
                                        });
                                        /**
                                         * {{ else}}
                                         */

                                        $("#code").focus(function() {
                                            if ($(this).val() == "输入您绑定手机所收到的验证码") {
                                                $(this).val('');
                                            }
                                        }).blur(function() {
                                            if ($(this).val() == '') {
                                                $(this).val("输入您绑定手机所收到的验证码");
                                            }
                                        });
                                        /**
                                         * {{/if}}
                                         */
                                    });
                                    function sendCode() {
                                        var from = $("#from").val();
                                        $.post("/find/sendCode?forward={{$pageArray.forward|urlencode}}", {'from': from}, function(data) {
                                            if (data == '400.0') {
                                                $("#sendMsg").addClass('form-tips-error').html('<i class="icon-error"></i>验证太频繁，请稍后再试！');
                                                $("#sendMsg").show();
                                            } else if (data == '500.0') {
                                                $("#sendMsg").addClass('form-tips-error').html('<i class="icon-error"></i>服务器忙，请稍后再试！');
                                                $("#sendMsg").show();
                                            } else if (from == 'email') {
                                                var emailExp = '{{$pageArray.userinfo.email}}'.split('@');
                                                if (emailExp[1] == 'gmail.com') {
                                                    var emailHost = 'http://www.' + emailExp[1];
                                                } else {
                                                    var emailHost = 'http://mail.' + emailExp[1];
                                                }
                                                $('#sendMsg').removeClass('form-tips-error').html('验证邮件已发送，<a href="' + emailHost + '" target="_blank" class="email_url">去邮箱收件</a>');
                                                $("#sendMsg").show();
                                            }
                                        });
                                        $("#sendBtn").text('重新发送');
                                        showSendMsg(60);
                                    }
                                    function showSendMsg(times) {
                                        $("#sendBtn").hide();
                                        $("#sendingBtn").text('重新发送（' + times + '）');
                                        $("#sendingBtn").show();
                                        if (times > 1) {
                                            times--;
                                            setTimeout(function() {
                                                showSendMsg(times);
                                            }, 1000);
                                        } else {
                                            $("#sendingBtn").hide();
                                            $("#sendBtn").show();
                                        }
                                    }
                                    function checkCode() {
                                        var codeErr = true;
                                        var code = $("#code").val();
                                        var from = $("#from").val();
                                        $.ajax({
                                            type: "POST",
                                            url: "/find/checkCode",
                                            async: false,
                                            data: "from=" + from + "&code=" + code,
                                            success: function(data) {
                                                if (data == '200.0') {
                                                    codeErr = false;
                                                } else if (data == '400.0') {
                                                    $("#code").addClass('ipt_txt_error');
                                                    $("#checkMsg").html('<i class="icon-error"></i>验证码错误！');
                                                    $("#checkMsg").show();
                                                }
                                            }
                                        });
                                        return !codeErr;
                                    }
                        </script>
                    </div>
                </div>
                {{elseif $pageArray.step == '4'}}
                <div class="m-boxA minHeight">
                    <div class="m-boxA-hd">
                        <h2 class="m-boxA-tit">找回密码</h2>
                        <a class="more" href="/member/edit_info.php">&lt; 返回登录页面</a>
                    </div>
                    <div class="m-boxA-bd">
                        <div class="ucfnBox">
                            <div class="m-form">
                                <form id="find-form" action="/find/step5" method="POST" onsubmit="return checkPwd(true);">
                                    <input type="hidden" name="from" value="{{$pageArray.from}}"/>
                                    <input type="hidden" name="code" value="{{$pageArray.code}}"/>
                                    <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
                                    <input type="hidden" name="pwd_strength" id="pwd_strength"/>
                                    {{if $pageArray.username && $pageArray.username != ''}}
                                    <div class="form-item">
                                        <span class="form-field">用户名：</span>
                                        <strong class="yzmCon">{{$pageArray.username}}</strong>
                                    </div>
                                    {{/if}}
                                    <div class="form-item">
                                        <span class="form-field">输入新密码：</span>
                                        <input class="ipt_txt ipt_defa" type="password" id="password" name="password" onblur="checkPwd();"/>
                                        <span id="pwdStr" style="display: none;"></span>
                                        <span class="form-tips" id="pwdMsg">6-16个字符（区分大小写）</span>
                                    </div>
                                    <div class="form-item">
                                        <span class="form-field">再次输入新密码：</span>
                                        <input class="ipt_txt ipt_defa" type="password" id="repassword" onblur="checkPwd();"/>
                                        <span class="form-tips" id="repwdMsg"></span>
                                    </div>
                                    <div class="form-item">
                                        <a class="btn-blueA" href="javascript:void(0)" onclick="$('#find-form').submit();
                                        return false;">下一步</a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <script type="text/javascript" src="/js/client/zxcvbn.js"></script>
                <script type="text/javascript">
                    var flag = false;
                                    function checkPwd(submit) {
                                        if ($("#password").val() == '')
                                        {
                                            $("#password").addClass('ipt_txt_error');
                                            $('#pwdMsg').addClass('form-tips-error').html('<i class="icon-error"></i>密码不能为空');
                                            $('#pwdMsg').show();
                                            $("#pwdStr").hide();
                                            flag = false;
                                        }
                                        if ($("#password").val().length < 6)
                                        {
                                            $("#password").addClass('ipt_txt_error');
                                            $('#pwdMsg').addClass('form-tips-error').html('<i class="icon-error"></i>最少6个字符');
                                            $('#pwdMsg').show();
                                            $("#pwdStr").hide();
                                            flag = false;
                                        }
                                        if ($("#password").val().length > 16)
                                        {
                                            $("#password").addClass('ipt_txt_error');
                                            $('#pwdMsg').addClass('form-tips-error').html('<i class="icon-error"></i>最多16个字符');
                                            $('#pwdMsg').show();
                                            $("#pwdStr").hide();
                                            flag = false;
                                        }
                                        if ($("#password").val() == '123456' || $("#password").val() == '654321' || $("#password").val() == '111222' || checkPassSame($("#password").val()) == false)
                                        {
                                            $("#password").addClass('ipt_txt_error');
                                            $('#pwdMsg').addClass('form-tips-error').html('<i class="icon-error"></i>您的密码过于简单，请重新输入');
                                            $('#pwdMsg').show();
                                            $("#pwdStr").hide();
                                            flag = false;
                                        }
                                        if ($.browser.msie && ($.browser.version == "6.0") && !$.support.style) {
                                                var passscore = {};
                                                var longScore = passwordGrade($("#password").val());
                                                if(longScore <= 10)
                                                {
                                                    passscore.score = 0;
                                                }
                                                else if(longScore >= 11 && longScore <= 20)
                                                {
                                                    passscore.score = 1;
                                                }
                                                else if(longScore >= 21 && longScore <= 30)
                                                {
                                                    passscore.score = 2;
                                                }
                                                else if(longScore >= 31 && longScore <= 40)
                                                {
                                                    passscore.score = 3;
                                                }
                                                else if(longScore >= 41)
                                                {
                                                    passscore.score = 4;
                                                }
                                        }
                                        else
                                        {
                                            try
                                            {
                                                var passscore = zxcvbn($("#password").val());
                                                if (typeof passscore == 'undefined')
                                                {
                                                    var passscore = {};
                                                    var longScore = passwordGrade($("#password").val());
                                                    if(longScore <= 10)
                                                    {
                                                        passscore.score = 0;
                                                    }
                                                    else if(longScore >= 11 && longScore <= 20)
                                                    {
                                                        passscore.score = 1;
                                                    }
                                                    else if(longScore >= 21 && longScore <= 30)
                                                    {
                                                        passscore.score = 2;
                                                    }
                                                    else if(longScore >= 31 && longScore <= 40)
                                                    {
                                                        passscore.score = 3;
                                                    }
                                                    else if(longScore >= 41)
                                                    {
                                                        passscore.score = 4;
                                                    }
                                                }
                                            }
                                            catch(err)
                                            {
                                                var passscore = {};
                                                var longScore = passwordGrade($("#password").val());
                                                if(longScore <= 10)
                                                {
                                                    passscore.score = 0;
                                                }
                                                else if(longScore >= 11 && longScore <= 20)
                                                {
                                                    passscore.score = 1;
                                                }
                                                else if(longScore >= 21 && longScore <= 30)
                                                {
                                                    passscore.score = 2;
                                                }
                                                else if(longScore >= 31 && longScore <= 40)
                                                {
                                                    passscore.score = 3;
                                                }
                                                else if(longScore >= 41)
                                                {
                                                    passscore.score = 4;
                                                }
                                            }
                                        }
                                        if (passscore.score <= 0)
                                        {
                                            $('#pwd_strength').val(0);
                                            $("#password").addClass('ipt_txt_error');
                                            $('#pwdMsg').addClass('form-tips-error').html('<i class="icon-error"></i>您的密码过于简单，请重新输入');
                                            $('#pwdMsg').show();
                                            $("#pwdStr").attr("class", "form-pwd-tips form-pwd-tips-1").html('<i class="on"></i><i></i><i></i><i></i><em>弱</em>').show();
                                            flag = false;
                                        }
                                        else if (passscore.score == 1)
                                        {
                                            $('#pwd_strength').val(1);
                                            $('#pwdMsg').hide();
                                            $("#pwdStr").attr("class", "form-pwd-tips form-pwd-tips-2").html('<i class="on"></i><i class="on"></i><i></i><i></i><em>中</em>').show();
                                            flag = true;
                                        }
                                        else if (passscore.score == 2 )
                                        {
                                            $('#pwd_strength').val(2);
                                            $('#pwdMsg').hide();
                                            $("#pwdStr").attr("class", "form-pwd-tips form-pwd-tips-3").html('<i class="on"></i><i class="on"></i><i class="on"></i><i></i><em>强</em>').show();
                                            flag = true;
                                        }
                                        else if (passscore.score >= 3)
                                        {
                                            $('#pwd_strength').val(3);
                                            $('#pwdMsg').hide();
                                            $("#pwdStr").attr("class", "form-pwd-tips form-pwd-tips-3").html('<i class="on"></i><i class="on"></i><i class="on"></i><i class="on"></i><em>极强</em>').show();
                                            flag = true;
                                        }
                                        if ($("#repassword").val() != $("#password").val())
                                        {
                                            $("#repassword").addClass('ipt_txt_error');
                                            $('#repwdMsg').addClass('form-tips-error').html('<i class="icon-error"></i>您两次输入的密码不一致');
                                            $('#repwdMsg').show();
                                            flag = false;
                                        }

                                        if (flag == true && submit == true)
                                        {
                                            var pwd = $("#password").val();
                                            var repwd = $("#repassword").val();
                                            $("#password").val(AES.encrypt(pwd));
                                            $("#repassword").val(AES.encrypt(repwd));
                                        }
                                        return flag;
                                    }
                                    function checkPassSame(pass)
                                    {
                                        var first = pass.substring(0, 1);
                                        var exp = new RegExp('^' + first + '+$');
                                        if (exp.test(pass))
                                        {
                                            return false;
                                        }

                                        if (first == 'a' || first == 'A')
                                        {
                                            f = pass.charCodeAt(0);
                                            for (i = 1; i < pass.length; i++)
                                            {
                                                tmp = pass.charCodeAt(i);
                                                if (tmp - f != i)
                                                {
                                                    return true;
                                                }
                                            }
                                            return false;
                                        }
                                        return true;
                                    }
                                    
                                function passwordGrade(pwd) {
                                        var score = 0;
                                        var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
                                        var repeatCount = 0;
                                        var prevChar = '';

                                        //check length
                                        var len = pwd.length;
                                        score += len > 18 ? 18 : len;

                                        //check type
                                        for (var i = 0, num = regexArr.length; i < num; i++) {
                                            if (eval('/' + regexArr[i] + '/').test(pwd))
                                                score += 4;
                                        }

                                        //bonus point
                                        for (var i = 0, num = regexArr.length; i < num; i++) {
                                            if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
                                                score += 2;
                                            if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
                                                score += 2;
                                        }

                                        //deduction
                                        for (var i = 0, num = pwd.length; i < num; i++) {
                                            if (pwd.charAt(i) == prevChar)
                                                repeatCount++;
                                            else
                                                prevChar = pwd.charAt(i);
                                        }
                                        score -= repeatCount * 1;

                                        return score;
                        }
                                    
                </script>
                {{elseif $pageArray.step == '5'}}
                <div class="m-boxA minHeight">
                    <div class="m-boxA-hd">
                        <h2 class="m-boxA-tit">找回密码</h2>
                        <a class="more" href="/member/edit_info.php">&lt; 返回登录页面</a>
                    </div>
                    <div class="m-boxA-bd">
                        <div class="retShow">
                            <table>
                                <tbody>
                                    <tr>
                                        <td>
                                            <i class="icon-show icon-ok-big"></i>
                                            <div class="retCon">
                                                <div class="name">密码修改成功！</div>
                                                <div class="tips" id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/edit_info.php{{/if}}">返回登录页面</a></div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <script type="text/javascript">
                    var times = 3;
                    $(function() {
                        setInterval(function() {
                            if (times > 0) {
                                $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/edit_info.php{{/if}}">返回登录页面</a>');
                            } else {
                                window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/edit_info.php{{/if}}";
                            }
                            times--;
                        }, 1000);
                    });
                </script>
                {{/if}}
            </div>
            <div class="pfooter">
                <a href="http://www.2345.com/about/about.htm" target="_blank">关于2345</a><em>|</em><a href="http://bbs.2345.cn/fk/index.htm?cName=%u7528%u6237%u4E2D%u5FC3" target="_blank">错误反馈</a><em>|</em><a href="http://www.2345.com/" target="_blank">标准版</a><em>|</em><a href="http://www.2345.com/j.htm" target="_blank">精简版</a><em>|</em><a href="http://www.2345.com/laonian.htm" target="_blank">老年版</a><em>|</em><a href="http://m.2345.com/pages/" target="_blank">手机版</a><em>|</em><a href="http://www.2345.com/about/gyhd.htm" target="_blank">公益</a><em>|</em>版权所有 &copy; 2345.com <a href=" http://www.miitbeian.gov.cn/" target="_blank">沪ICP备12023051号-1</a>
            </div>
        </div>
        <script type="text/javascript">
            $(function() {
                $(".ipt_txt").bind({
                    focus: function() {
                        $(this).addClass("ipt_txt_cur");
                        $(this).removeClass('ipt_txt_error');
                        $(this).parent('.form-item').find(".form-tips-error").hide();
                    },
                    blur: function() {
                        $(this).removeClass("ipt_txt_cur");
                    }
                });
            });

            function cc(a) {
                var b = arguments,
                    web = "ajax54",
                    a2,
                    i1 = document.cookie.indexOf("uUiD="),
                    i2;
                if (b.length > 1)
                    web = b[1];
                if (i1 != -1) {
                    i2 = document.cookie.indexOf(";", i1);
                    a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
                }
                if (!a2) {
                    a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                    document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
                }
                if (a.length > 0) {
                    var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                    $.getScript(c);
                }
                return true;
            }

        </script>


    <script type="text/javascript" src="/js/common.js"></script>
    <script type="text/javascript" src="/js/client/aes.js"></script>
    <script type="text/javascript" src="/js/client/encrypt.min.js"></script>
    </body>
</html>