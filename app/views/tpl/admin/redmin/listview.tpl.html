{{include file = "admin/redmin/layout.tpl.html"}}
{{include file = "admin/redmin/generalmodals.tpl.html"}}
<div class="span12">
    <h5><i class="icon-key"></i>{{$pageArray.key}}</h5>
    <table class="table table-striped settable">
        <tr>
            <th>Index</th>
            <th>Value</th>
        </tr>
        {{foreach from = $pageArray.values key = member item = value}}
            <tr>
                <td>
                    {{$member + 1}}
                </td>
                <td>
                    {{$value}}
                </td>
            </tr>
        {{/foreach}}
    </table>
        <ul class="pager">
            <li class="previous {{if $pageArray.page == 0}}disabled{{/if}}">
                <a href="/houtai_gl/redis/view/{{$pageArray.key|urlencode}}/{{$pageArray.page-1}}">&larr; Previous</a>
            </li>
            <li class="next {{if $pageArray.page == $pageArray.ceil}}disabled{{/if}}">
                <a href="/houtai_gl/redis/view/{{$pageArray.key|urlencode}}/{{$pageArray.page+1}}">Next &rarr;</a>
            </li>
        </ul>
</div>
{{include file = "admin/redmin/footer.tpl.html"}}
