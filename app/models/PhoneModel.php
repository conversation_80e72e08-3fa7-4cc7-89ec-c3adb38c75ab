<?php

use Octopus\PdoEx;
use Service\Capacity\GetHashTable;
use Service\Capacity\GetRangeTable;
use Common\Confidential\Sensitive;

/**
 * Copyright (c) 2014,上海二三四五网络科技股份有限公司
 * 摘    要：绑定接口
 * 作    者：<EMAIL>
 * 修改日期：2014.12.24
 */
class PhoneModel extends Model
{
    private $pdo;
    private $pdoNew;
    private $pdoLog;

    /**
     * PhoneModel constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_PASSPORT, $dbConfig[DB_PASSPORT]);
    }

    /**
     * 功  能：
     *
     * @return PdoEx
     */
    protected function getPdoLog()
    {
        if (!is_object($this->pdoLog)) {
            $dbConfig = Config::get("database");
            $this->pdoLog = PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        }
        return $this->pdoLog;
    }

    /**
     *
     * Get passport_user pdo
     * @return PdoEx
     */
    protected function getPdoNew()
    {
        if (!is_object($this->pdoNew)) {
            $dbConfig = Config::get("database");
            $this->pdoNew = PdoEx::getInstance(DB_PASSPORT_USER, $dbConfig[DB_PASSPORT_USER], true);
        }
        return $this->pdoNew;
    }

    /**
     * 添加手机绑定信息
     * @param int $passid $passid
     * @param int $phone $phone
     * @return bool
     * desc:该函数兼容加解密
     */
    public function add($passid, $phone)
    {
        if ($passid < 1) {
            return false;
        }
        //修改redis
        $this->resetPhoneInfoToRedis($passid, $phone, false);

        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        $table = GetRangeTable::Members($passid);
        $result = $pdoNew->update($table, ['phone' => Sensitive::Encode($phone)], [
            'where' => 'id = :id',
            'params' => [':id' => $passid]
        ]);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $table = GetHashTable::MembersPhone($phone);
        $insert = array(
            'passid' => $passid,
            'phone' => Sensitive::Encode($phone),
            'last_update' => date('Y-m-d H:i:s', time()),
        );
        $result = $pdoNew->insert($table, $insert, false);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $pdoNew->commit();
        return true;
    }

    /**
     * 功  能：修改绑定手机
     * 作    者：wangl
     * 日    期：2014.12.24
     *
     * @param int $passid 用户ID
     * @param int $phone 手机号
     * @return bool|int
     * desc:该函数兼容加解密
     */
    public function edit($passid, $phone)
    {
        if ($passid < 1) {
            return false;
        }
        //修改redis
        $this->resetPhoneInfoToRedis($passid, $phone);

        $pdoNew = $this->getPdoNew();
        $pdoNew->beginTransaction();
        $table = GetRangeTable::Members($passid);
        // 删除旧的手机
        $sql = "SELECT phone FROM {$table} WHERE id=:id";
        $row = $pdoNew->find($sql, [':id' => $passid]);

        // desc:字段加解密兼容
        if (isset($row['phone'])) {
            $row['phone'] = Sensitive::Decode($row['phone']);
        }

        if ($row && $row['phone']) {
            $tablePhone = GetHashTable::MembersPhone($row['phone']);
            $result = $pdoNew->delete($tablePhone, [
                'where' => 'phone = :phone or phone = :aesPhone',
                'params' => [':phone' => $row['phone'], ':aesPhone' => Sensitive::Encode($row['phone'])]
            ]);
            if (!$result) {
                $pdoNew->rollBack();
                return false;
            }
        }
        $result = $pdoNew->update($table, ['phone' => Sensitive::Encode($phone)], [
            'where' => 'id = :id',
            'params' => [':id' => $passid]
        ]);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $tablePhone = GetHashTable::MembersPhone($phone);
        $insert = array(
            'passid' => $passid,
            'phone' => Sensitive::Encode($phone),
            'last_update' => date('Y-m-d H:i:s', time()),
        );
        $result = $pdoNew->insert($tablePhone, $insert, false);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $pdoNew->commit();
        return true;
    }

    /**
     * 作    者：wangl
     * 功    能：获取绑定信息
     * 日    期：2014.12.24
     * @param int $passid 用户ID
     * @return array|bool
     */
    public function get($passid)
    {
        if ($passid < 1) {
            return false;
        }

        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $row = $pdoNew->find(
            "SELECT phone FROM {$table} WHERE id=:id",
            [':id' => $passid]
        );

        // desc:字段加解密兼容
        if (isset($row['phone'])) {
            $row['phone'] = Sensitive::Decode($row['phone']);
        }

        if ($row && $row['phone']) {
            return $this->getByPhone($row['phone']);
        }
        return false;
    }

    /**
     * 功  能：通过手机号获取phone表中的其他数据
     *
     * @param string $phone phone
     * @return array|bool
     */
    public function getByPhone($phone)
    {
        if (empty($phone)) {
            return false;
        }

        $pdoNew = $this->getPdoNew();
        $table = GetHashTable::MembersPhone($phone);
        $data = $pdoNew->find(
            "SELECT * FROM {$table} WHERE phone=:phone or phone=:aesPhone",
            [':phone' => $phone, ':aesPhone' => Sensitive::Encode($phone)]
        );

        if (isset($data['phone'])) {
            $data['phone'] = Sensitive::Decode($data['phone']);
        }
        return $data;
    }

    /**
     * 解除手机绑定
     * @param int $passid $passid
     * @return bool
     */
    public function del($passid)
    {
        if ($passid < 1) {
            return false;
        }

        $pdoNew = $this->getPdoNew();
        $table = GetRangeTable::Members($passid);
        $sql = "SELECT phone FROM {$table} WHERE id=:id";
        $row = $pdoNew->find($sql, [':id' => $passid]);
        if (!$row || !$row['phone']) {
            return false;
        }

        if (isset($row['phone'])) {
            $row['phone'] = Sensitive::Decode($row['phone']);
        }

        $changeResult = changeRegPhoneRedis($row['phone']);
        xLog('resetBindPhone', 'resetBindPhone', 'passId:' . $passid . '解绑手机号码. redis修改结果:' . $changeResult);

        $pdoNew->beginTransaction();
        $result = $pdoNew->update($table, ['phone' => ''], [
            'where' => 'id = :id',
            'params' => [':id' => $passid]
        ]);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $table = GetHashTable::MembersPhone($row['phone']);
        $result = $pdoNew->delete($table, [
            'where' => 'phone = :phone',
            'params' => [':phone' => $row['phone']]
        ]);
        if (!$result) {
            $pdoNew->rollBack();
            return false;
        }
        $pdoNew->commit();
        return true;
    }

    /**
     * 设置手机号
     * @param int $passid 用户ID
     * @param int $phone 手机号
     * @return bool
     */
    public function set($passid, $phone)
    {
        return $this->edit($passid, $phone);
    }

    /**
     * 添加手机修改日志
     * @param int $passid 用户ID
     * @param array $log $log
     * @return bool
     * desc:该函数兼容加解密
     */
    public function log($passid, $log)
    {
        $log['passid'] = $passid;

        if (isset($log['phone'])) {
            $log['phone'] = Sensitive::Encode($log['phone']);
        }
        if (isset($log['phone_old'])) {
            $log['phone_old'] = Sensitive::Encode($log['phone_old']);
        }
        return $this->getPdoLog()->insert('members_phone_log', $log, false);
    }

    /**
     * 修改手机号码时,同时修改redis中的记录
     * 要在 修改入库之前, 否则老手机号码就查不到了
     * -
     * @param string $passid PassId
     * @param string $newPhone NewPhone
     * @param bool $recordLog 是否记录日志
     * @return void
     * <AUTHOR>
     */
    public function resetPhoneInfoToRedis($passid, $newPhone, $recordLog = true)
    {
        $changeResult = '';
        $logStr = $passid . ' 修改绑定手机,old:';
        $mpInfo = $this->get($passid);
        if ($mpInfo && isset($mpInfo['phone']) && !empty($mpInfo['phone'])) {
            $logStr .= $mpInfo['phone'];
            $tmpResult = changeRegPhoneRedis($mpInfo['phone']);
            if ($tmpResult === false) {
                $changeResult .= 'redis中检查结果:' . existsRegPhoneRedis($mpInfo['phone']) . ';';
                for ($i = 1; $i <= 3; $i++) {
                    $tmpResult = changeRegPhoneRedis($mpInfo['phone']);
                    if ($tmpResult === false) {
                        $changeResult .= $mpInfo['phone'] . ' 修改时redis第' . $i . '次返回false';
                        usleep(500);
                    } else {
                        $changeResult = $tmpResult;
                        break;
                    }
                }
            } else {
                $changeResult = $tmpResult;
            }
        }

        if (empty($mpInfo['phone']) || $mpInfo['phone'] != $newPhone) {
            $tmpResult = changeRegPhoneRedis($newPhone);
            if ($tmpResult === false) {
                $changeResult .= 'redis中检查结果:' . existsRegPhoneRedis($newPhone) . ';';
                for ($i = 1; $i <= 3; $i++) {
                    $tmpResult = changeRegPhoneRedis($newPhone);
                    if ($tmpResult === false) {
                        $changeResult .= '; newPhone:' . $newPhone . ' 修改时redis第' . $i . '次返回false';
                        usleep(500);
                    } else {
                        $changeResult .= '_' . $tmpResult;
                        break;
                    }
                }
            } else {
                $changeResult .= '_' . $tmpResult;
            }
        }

        $logStr .= '; new:' . $newPhone;
        if ($recordLog) {
            xLog('resetBindPhone', 'resetBindPhone', $logStr . '. redis修改结果:' . $changeResult);
        }
    }

    /**
     * savePhoneInfoToRedis
     * -
     * @param string $passId PassId
     * @param string $phone Phone
     * @return bool|int
     * <AUTHOR>
     */
    public function savePhoneInfoToRedis($passId, $phone)
    {
        if (empty($passId) || empty($phone)) {
            return false;
        }
        $table = GetRangeTable::Members($passId);
        $row = $this->getPdoNew()->find(
            "SELECT id,username,gid,uid FROM {$table} WHERE id = :passId LIMIT 1",
            array(":passId" => $passId)
        );
        if ($row && is_array($row)) {
            $memberModel = loadModel('member');
            $uid = serialize([1 => (int)$row['uid']]);
            $memberModel->saveRegPhoneToRedis($phone, $row['id'], $uid, $row['gid'], $row['username']);
            return 1;
        }
        return false;
    }
}
