<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/6/13
 * Time: 14:27
 */

use Octopus\PdoEx;

class NoticeNewModel extends Model
{
    private $pdo;

    private $idRange;

    /**
     * NoticeNewModel constructor.
     * -
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_PASSPORT_NOTICE, $dbConfig[DB_PASSPORT_NOTICE]);
        $this->idRange = new \Service\DivisionalDb\IdRange();
    }

    /**
     * 插入异步通知记录
     * -
     * @param array $data 异步日志记录
     * @return mixed
     */
    public function insert($data)
    {
        //获取当前ID
        $currentId = $this->idRange->getDbSequence($this->pdo, 'notification_id');
        if ($currentId === false)
        {
            return false;
        }
        $tableName = $this->idRange->getSubTable($currentId, 'notification_schedule', 'passport_notice');
        $data['id'] = $currentId;
        return $this->pdo->insert($tableName, $data);
    }

    /**
     * 修改通知状态
     * -
     * @param int $noticeId  通知记录ID
     * @param int $retryTimes 重试次数
     * @param array $results  通知返回记录
     * @param array $error   通知错误信息
     * @param array $info  通知信息
     * @return bool|int
     */
    public function updateNoticeStatus($noticeId, $retryTimes, $results, $error, $info)
    {
        $condition = array(
            'where' => 'id = :id',
            'params' => array(
                ':id' => $noticeId,
            )
        );
        if (noticeCallbackSuccess($results) === true)
        {
            $update = array('notice_status' => 1);
        }
        else
        {
            $retryTimes = $retryTimes + 1;
            $noticeTime = time() + $retryTimes * 300;
            $update = [
                'notice_error' => "$error:$results",
                'notice_info' => serialize($info),
                'retry_times' => $retryTimes,   //重试次数
                'notice_time' => $noticeTime,  // 下次通知时间
            ];
        }
        $tableName = $this->idRange->getSubTable($noticeId, 'notification_schedule', 'passport_notice');
        return $this->pdo->update($tableName, $update, $condition);
    }

    /**
     * 根据通知记录ID查询
     * -
     * @param int $id 通知记录ID
     * @return array|bool
     */
    public function queryNoticeInfoById($id)
    {
        $tableName = $this->idRange->getSubTable($id, 'notification_schedule', 'passport_notice');
        $sql = "SELECT id,passid,api_url,params,retry_times,notice_status,notice_type
                FROM {$tableName} WHERE id=:id AND notice_time<:notice_time ORDER BY `id` DESC LIMIT 1";
        $queryData = [
            'id' => $id,
            ':notice_time' => time()
        ];
        $row = $this->pdo->find($sql, $queryData);
        if (empty($row))
        {
            return array();
        }
        else
        {
            return $row;
        }
    }

    /**
     * 根据通知ID查询错误记录
     * -
     * @param int $id 通知记录ID
     * @param string $notice_type 通知类型
     * @return array|bool
     */
    public function queryErrorById($id, $notice_type = '')
    {
        $tableName = $this->idRange->getSubTable($id, 'notification_schedule', 'passport_notice');
        $sql = "SELECT id,passid,api_url,params,retry_times,notice_status,notice_type,notice_time,notice_info
                FROM {$tableName} WHERE id=:id ";
        $query = array(
            'id' => $id
        );
        if (!empty($notice_type))
        {
            $sql .= " AND notice_type=:notice_type";
            $query[':notice_type'] = $notice_type;
        }
        $sql .= " ORDER BY `id` DESC LIMIT 1";
        $row = $this->pdo->find($sql, $query);
        if (empty($row))
        {
            return array();
        }
        else
        {
            return $row;
        }
    }

    /**
     * 清除自增ID
     * -
     * @param string $time 时间戳
     * @return bool|int
     */
    public function clearSeq($time = '')
    {
        if (empty($time))
        {
            $time = time() - 3600;
        }
        $condition = array(
            "where" => 'val < :val',
            "params" => array(
                ':val' => $time
            )
        );
        return $this->pdo->delete('notification_id', $condition);
    }


    /**
     * 获取错误日志列表
     * -
     * @param int $retry_times 重试次数
     * @param string $retryThan 重试次数比较符
     * @param int $notice_status 通知状态
     * @param string $runTime 执行时间
     * @param array $params 执行查询参数
     * @param bool $isCount 是否count运算
     * @return array|bool
     */
    public function getErrorNoticeList($retry_times, $retryThan = 'lt', $notice_status = 0, $runTime = '', $params = [], $isCount = false)
    {
        switch ($retryThan)
        {
            case 'lt':
                $retryThan = '<';
                break;
            case 'gt':
                $retryThan = '>';
                break;
            case 'ge':
                $retryThan = '>=';
                break;
            default:
                $retryThan = '<';
                break;
        }
        $sql = "SELECT MAX(id) AS maxId,MIN(id) AS minId FROM `notification_id`";
        $row = $this->pdo->find($sql);
        $table1 = $this->idRange->getSubTable($row['minId'], 'notification_schedule', 'passport_notice');
        $table2 = $this->idRange->getSubTable($row['maxId'], 'notification_schedule', 'passport_notice');
        if (empty($runTime))
        {
            //默认查询2个小时以内的数据
            $runTime = strtotime("-2 hour");
        }
        $fields = "*";
        if ($isCount)
        {
            $fields = " COUNT(id) AS total ";
        }
        if ($table1 == $table2)
        {
            $sql = "SELECT {$fields} FROM {$table1} WHERE notice_time >= :notice_time AND retry_times {$retryThan} :retry_times AND notice_status = :notice_status";
            $runData = [
                    ':notice_time' => $runTime,
                    ':retry_times' => $retry_times,
                    ':notice_status' => $notice_status
            ];
            if (!empty($params['typeName']))
            {
                $sql .= " AND notice_type=:notice_type ";
                $runData[':notice_type'] = $params['typeName'];
            }
            $sql .= " ORDER BY ID ASC LIMIT 1000";
        }
        else
        {
            $sql = "(SELECT {$fields} FROM {$table1} WHERE notice_time >= :notice_time AND retry_times {$retryThan} :retry_times AND notice_status = :notice_status ";
            $runData = [
                ':notice_time' => $runTime,
                ':retry_times' => $retry_times,
                ':notice_status' => $notice_status,
            ];
            if (!empty($params['typeName']))
            {
                $sql .= " AND notice_type=:notice_type ";
                $runData[':notice_type'] = $params['typeName'];
            }
            $sql .= " ORDER BY ID ASC LIMIT 1000 )";
            $sql .= " UNION ALL(SELECT {$fields} FROM {$table2} WHERE notice_time >= :notice_time2 AND retry_times {$retryThan} :retry_times2 AND notice_status = :notice_status2 ";
            $runData = array_merge($runData, [
                ':notice_time2' => $runTime,
                ':retry_times2' => $retry_times,
                ':notice_status2' => $notice_status
            ]);
            if (!empty($params['typeName']))
            {
                $sql .= " AND notice_type=:notice_type2 ";
                $runData[':notice_type2'] = $params['typeName'];
            }
            $sql .= " ORDER BY ID ASC LIMIT 1000 )";
        }
        if ($isCount)
        {
            if ($table1 == $table2)
            {
                $result = $this->pdo->find($sql, $runData);
            }
            else
            {
                $result = $this->pdo->findAll($sql, $runData);
                return ['total' => $result[0]['total'] + $result[1]['total']];
            }
        }
        else
        {
            $result = $this->pdo->findAll($sql, $runData);
        }
        if (empty($result))
        {
            return [];
        }
        else
        {
            return $result;
        }
    }
}
