<?php

class Captcha<PERSON>ontroller extends Controller
{

    /**
     * 生成图片验证码
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        session_start();
        $mid = isset($_GET['mid']) ? Common\Utils\Url::getStringParam('mid') : false;
        $captchaAction = loadAction("captcha");
        $code = $captchaAction->generate($mid);
        $_SESSION["captcha_code"] = implode("", $code);
        if (in_array(strtolower($mid), ['srf', 'loginReg'])) {
            $captchaAction->movementCaptchaShow($mid);
        } else {
            $captchaAction->show($mid);
        }
    }

    /**
     * 必传mid
     * @return void
     */
    public function actionShow()
    {
        session_start();
        $mid = Common\Utils\Url::getStringParam('mid');
        if (empty($mid)) {
            return false;
        }
        $captchaAction = loadAction("captcha");
        $code = $captchaAction->generate($mid);
        $_SESSION["captcha_code"] = implode("", $code);
        if (in_array($mid, ['loginReg'])) {
            $captchaAction->movementCaptchaShow($mid);
        } else {
            $captchaAction->show($mid);
        }
    }
}
