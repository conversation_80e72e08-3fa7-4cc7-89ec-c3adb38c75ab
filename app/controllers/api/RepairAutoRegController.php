<?php

use WebLogger\Facade\LoggerFacade;

/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：AutoRegController.php
 * 摘    要：立即贷对接接口
 * 作    者：lynn
 * 修改日期：05-04, 2018
 */
class RepairAutoRegController extends Controller
{
    private $api = '';
    private $json = '';
    private $clientIp = '';

    /**
     * 安全验证
     * <AUTHOR>
     * @DateTime 2018-05-04T17:22:03+0800
     */
    public function __construct()
    {
        parent::__construct();
        $this->api = trim($_SERVER["REQUEST_URI"]);
        $this->clientIp = \Service\Security\Server::checkServerRequestRight();
        $this->json = file_get_contents("php://input");
        $_POST = json_decode($this->json, true);
        //必填参数缺失校验
        if (!isset($_POST["domain"])) {
            $this->echoResult(100, "必填参数domain缺失");
        }
        if (!isset($_POST["mid"])) {
            $this->echoResult(101, "必填参数mid缺失");
        }
        //必填参数合法注册校验
        $serverDomain = trim($_POST['domain']);
        $allowSeverDomains = Config::get("domainServerIPs");
        if (empty($serverDomain) || !isset($allowSeverDomains[$serverDomain])) {
            $this->echoResult(102, "参数domain非法");
        }
        $mid = trim($_POST['mid']);
        $mids = Config::get('mid');
        if (empty($mid) || !isset($mids[$mid])) {
            $this->echoResult(103, "参数mid非法");
        }
        //合法IP校验
        if (!$this->clientIp) {
            $this->echoResult(104, "非法IP");
        }
        //如果type值不为空时，只能是1，2  1:游戏会员，2:商城会员
        if (!empty($_POST['type']) && !in_array($_POST['type'], [1, 2])) {
            $this->echoResult(105, "无可用类型");
        }
    }

    /**
     * 输出结果
     * <AUTHOR>
     * @DateTime 2018-05-04T17:24:09+0800
     *
     * @param    [type]                   $code 200是成功其他都是失败
     * @param    [type]                   $msg  [description]
     *
     * @return   [type]                         [description]
     */
    private function echoResult($code, $msg)
    {
        if ($code == 200) {
            $response = array(
                "code"         => strval($code),
                "errorMessage" => "",
            );
        } else {
            $response = array(
                "code"         => strval($code),
                "errorMessage" => mb_convert_encoding(strval($msg), "UTF-8", "GBK"),
            );
        }
        if ($code != 200) {
            $str = "错误{$code}：" . $msg . "\t数据：" . json_encode($_POST);
            xLog("RepairAutoReg", "RepairAutoReg", $str);
        }
        $this->echoResponse($response);
    }

    /**
     * 参数校验
     * <AUTHOR>
     * @DateTime 2018-05-04T17:42:58+0800
     *
     * @param    [type]                   $post [description]
     *
     * @return   [type]                         [description]
     */
    private function paramCheck($post)
    {
        $names = array(
            "mobilePhone"     => "intval",
            "productId"       => "intval",
            "price"           => "intval",
            "expiryDay"       => "intval",
            "payDate"         => "trim",
            "merchantOrderNo" => "trim",
        );
        foreach ($names as $name => $func) {
            if (!isset($post[$name])) {
                $this->echoResult(110, "缺参数：" . $name);
            }
            $value = $func($post[$name]);
            if ($value != $post[$name]) {
                $this->echoResult(111, "参数值错误：" . $name);
            }
        }
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $post["mobilePhone"])) {
            $this->echoResult(112, "手机号错误");
        }
        if (!preg_match("/^[1-9]\d{1,}$/", $post["merchantOrderNo"])) {
            $this->echoResult(113, "流水号错误");
        }
        if ($post["productId"] < 1) {
            $this->echoResult(114, "参数productId错误");
        }
        if ($post["price"] < 1) {
            $this->echoResult(115, "参数price错误");
        }
        if ($post["expiryDay"] < 1) {
            $this->echoResult(116, "参数expiryDay错误");
        }
        if (!preg_match("/^2\d{13}$/", $post["payDate"])) {
            $this->echoResult(117, "参数payDate错误");
        }
    }

    /**
     * 自动校验或注册
     * <AUTHOR>
     * @DateTime 2018-05-04T17:28:02+0800
     * @return   [type]                   [description]
     */
    public function actionIndex()
    {
        //参数校验
        $this->paramCheck($_POST);
        $mobilePhone = $_POST["mobilePhone"];
        $memberModel = loadModel('member');
        $passId = $memberModel->getPassidByPhone($mobilePhone); //先检查手机号，如果没有再检查用户名
        if (empty($passId)) {
            $passId = $memberModel->getPassidByUsername($mobilePhone);
        }
        if (!empty($passId)) {
            //如果有passid 就转发手助
            $result = $this->sendApi($passId, $_POST);
            if ($result['code'] == 200) {
                $this->echoResult(200, "success");
            } else {
                $this->echoResult(500, "手助接收失败" . $result['response']);
            }
        } else {
            //手机号不存在，则注册帐号添加passid记录通知助手返回结果
            $pwd = mt_rand() . time();
            $password = substr(md5($pwd . MD5KEY), 0, 16);

            $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
            $pwdScore = $Zxcvbn->passwordStrength($password);
            $pwdStrength = $pwdScore['score'];
            $regAction = loadAction('reg');

            $gid = 300;
            // 网页端 注册
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
            $source = \Common\Utils\Url::getStringParam("mid", "LJD", \Common\Utils\Url::POST);
            $result = $regAction->regPhone(
                $mobilePhone,
                $password,
                $pwdStrength,
                $this->clientIp,
                $_POST['domain'],
                $gid,
                $source,
                $clientType
            );
            $states = explode(".", $result[0]);
            if ($states[0] == 200 && !empty($result[1]["passid"])) {
                $result = $this->sendApi($result[1]["passid"], $_POST);
                if ($result['code'] == 200) {
                    $this->echoResult(200, "success");
                } else {
                    $this->echoResult(500, "手助接收失败" . $result['response']);
                }
            }
            $this->echoResult(700, "注册失败：" . $result[0]);
        }
    }


    /**
     * 向助手发送api信息
     * <AUTHOR>
     * @DateTime 2018-05-07T13:28:39+0800
     *
     * @param    [type]                   $info [description]
     *
     * @return  array
     */
    private function sendApi($passid, $info)
    {
        $memberModel = loadModel('member');
        $userInfo = $memberModel->getUsernameByPassid($passid);
        $info["username"] = $userInfo["username"];
        $info["uid"] = $memberModel->getUid($passid);
        $retA = array(
            "code"     => 0,
            "response" => "",
        );
        $accountMerchantName = !empty($info['accountMerchantName']) ? urlencode($info['accountMerchantName']) : "";
        $url = "http://zhushou.2345.com/index.php?c=jikedai&d=repairDeliverCoupon";
        $postFields = [
            "phone"               => $info["mobilePhone"],
            "passid"              => $passid,
            "uname"               => $info["username"],
            "orderId"             => $info["merchantOrderNo"],
            "price"               => $info["price"],
            "uid"                 => $info["uid"],
            "expiryDay"           => $info['expiryDay'],
            "accountMerchantName" => $accountMerchantName,
            "ctime"               => date('Y-m-d H:i:s', strtotime($info['payDate'])),
        ];
        $options = array(
            CURLOPT_URL            => $url,
            CURLOPT_HEADER         => 0,
            CURLOPT_RETURNTRANSFER => 1,
            CURLOPT_USERAGENT      => "login.2345.com",
            CURLOPT_CONNECTTIMEOUT => 3,
            CURLOPT_TIMEOUT        => 3,
            CURLOPT_POST           => 1,
            CURLOPT_POSTFIELDS     => $postFields,
        );
        if (preg_match("/^https:\/\/(.*)/", $url)) {
            $options[CURLOPT_SSL_VERIFYPEER] = 0;
            $options[CURLOPT_SSL_VERIFYHOST] = 0;
        }
        $ch = curl_init();
        curl_setopt_array($ch, $options);
        $current = mSecTime();
        $result = curl_exec($ch);
        $curlInfo = curl_getinfo($ch);
        $httpCode = $curlInfo["http_code"];
        $sizeDownload = $curlInfo["size_download"];
        $errorMsg = "";
        if ($httpCode != 200 || $sizeDownload < 1) {
            $errorCode = curl_errno($ch);
            $error = curl_error($ch);
            $errorMsg .= "errorCode:" . $errorCode . PHP_EOL;
            $errorMsg .= "error:" . $error . PHP_EOL;
            foreach ($curlInfo as $name => $value) {
                $errorMsg .= $name . ":" . $value . PHP_EOL;
            }
        }
        curl_close($ch);
        if ($httpCode == 200 && $sizeDownload > 0) {
            $json = json_decode($result, true);
            if (!empty($json)) {
                $retA["code"] = intval($json["code"]);
            }
        }
        if (!empty($errorMsg)) {
            $retA["response"] = $errorMsg . PHP_EOL . $result;
        } else {
            $retA["response"] = $result;
        }
        LoggerFacade::info("sendApi - 自动校验或注册,向助手发送api信息", [
            "type" => "http_request",
            "path" => $url,
            "exec_time" => mSecTime() - $current,
            "code" => $retA["code"] ?? null,
            "http_code" => $httpCode,
        ]);

        return $retA;
    }

}
