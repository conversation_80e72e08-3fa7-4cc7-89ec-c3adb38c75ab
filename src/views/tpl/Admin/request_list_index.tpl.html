<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>语音管理-请求列表</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
  <script src="/style/js/tableFixed.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">请求列表</a>  >  <span>请求列表</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="javascript:">请求列表</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    
     <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="" method="post" id="queryFromId">
            <div class="dataWrap ml10 clearfix">
            	<em>请求来源：</em>
                <select class="selectMod w90 selectProject" name="from">
                    <option value="0">全部</option>
                    {{foreach from=$pageArray.projectList item=projectInfo}}
                        <option {{if $pageArray.from == $projectInfo.channel}} selected="selected"{{/if}} value="{{$projectInfo.channel}}">{{$projectInfo.desc}}</option>
                    {{/foreach}}
                </select>
            </div>
            
            <div class="dataWrap ml10 clearfix">
            	<em>手机号码：</em>
                <input value="{{$pageArray.mobile}}" type="text" name="mobile"/>
            </div>
            
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 queryFromSubmit" >查询</a>
        
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    
    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>日期</th>
                        <th>手机号码</th>
                        <th>验证码</th>
                        <th>使用状态</th>
                        <th>来源</th>
                        <th>请求状态</th>
                        <th>请求时间</th>
                        
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th>日期</th>
                <th>手机号码</th>
                <th>验证码</th>
                <th>使用状态</th>
                <th>来源</th>
                <th>请求状态</th>
                <th>请求时间</th>
                
            </tr>
            {{foreach from=$pageArray.telLog item=telLogInfo}}
            <tr>
                <td>{{$telLogInfo.date}}</td>
                <td>{{$telLogInfo.mobile}}</td>
                <td>{{$telLogInfo.code}}</td>
                <td>{{$telLogInfo.status}}</td>
                <td>{{$telLogInfo.from}}</td>
                <td>
                    <span class="{{if $telLogInfo.request_status eq 1}}greenBg{{else}}yellowBg{{/if}}">
                	{{$telLogInfo.requestStatusDesc}}
                    </span>
                </td>
                <td>{{$telLogInfo.add_date}}</td>
               
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->     
        {{$pageArray.showPage}}
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->

<div class="boxMask" style="display: none;">透明遮罩层</div>
 <!--新增项目 弹窗 开始-->
	<div class="boxMod w740 addProjectDivClass">
    	<div class="boxTop"><h2 class="boxTit divNewProject">添加新的渠道来源</h2><a class="BoxClose" href="javascript:">关闭按钮</a></div>
        <div class="scrollWrap">
            <div class="tabFrom mtb20">
                <form id="addProjectFrom" action="" method="post">
                    <table class="tabFromMod" cellpadding="0" cellspacing="0">
                        <tr>
                            <td class="tabLeftTit">来源标识：</td>
                            <td><input class="tabInput" type="text" autocomplete="off" name="channel"/></td>
                        </tr>
                        <tr>
                            <td class="tabLeftTit">来源描述：</td>
                            <td>
                                <input class="tabInput" type="text" autocomplete="off" name="desc"/>
                            </td>
                        </tr>
                        <tr>
                            <td class="tabLeftTit">来源编号：</td>
                            <td><input class="tabInput" type="text" autocomplete="off" name="number"/></td>
                        </tr>
                        <tr>
                            <td class="tabLeftTit">来源状态：</td>
                            <td>
                                <input type="radio" name="status" value="1" /> 开
                                <input type="radio" name="status" value="0" /> 关
                            </td>
                        </tr>
                        <tr>
                            <td class="tabLeftTit">开启语言验证码：</td>
                            <td>
                                <input type="radio" name="voice" value="1" /> 开
                                <input type="radio" name="voice"  value="0" /> 关
                            </td>
                        </tr>
                        <tr>
                            <td class="tabLeftTit">开启短信验证码：</td>
                            <td>
                                <input type="radio" name="sms" value="1"/> 开
                                <input type="radio" name="sms" value="0"/> 关
                            </td>
                        </tr>
                        <tr>
                        	<td colspan="2" align="center">
                                <a class="styleBtnBlue tabsave submitProjectInfo" href="javascript:">保存</a>
                            </td>
                        </tr>
                    </table>
                    
                </form>
            </div>
        </div><!--tabFrom end-->
    </div><!--boxMod end-->
    <!--新增项目 弹窗 结束-->
<script src="/style/js/boxLocation.js"></script>
<script>
$('.queryFromSubmit').click(function (){
    $('#queryFromId').submit();
});
</script>
</body>
</html>
