<?php
define('USE_NECAPTCHA', $_ENV['WEB_USE_NECAPTCHA']); // 易盾开关
define('NE_ACTIVE_ID', $_ENV['YY_NE_ACTIVE_ID']); // 易盾使用项目id配置
define('NE_MAIN_ACTIVE_ID', 'main_smart'); // 易盾使用项目id配置
define('NE_LOG_SWITCH', true); // 易盾上线观察数据
define("YIDUN_CAPTCHA_SECRET_ID", "263c8378858ebeb5f94225c96b712910");   // 验证码密钥对id
define("YIDUN_CAPTCHA_SECRET_KEY", "7f6813b3a8d45683401329aa99307ec6"); // 验证码密钥对key

define('APP_USE_NECAPTCHA', $_ENV['APP_USE_NECAPTCHA']); //移动端 易盾开关 true or false
define('APP_NE_ACTIVE_ID', $_ENV['APP_NE_ACTIVE_ID']); //移动端 易盾使用项目id配置  app_smart、app_normal


$config['NECaptchaConfig'] = [
    // 易盾后台开启项目
    'smart' => ['captchaId' => 'f8582f417b2b44dc859a715e8f75a0e1', 'type' => 'bind'],
    'normal' => ['captchaId' => '7a13ee6c35704b1ca53d072e47f2598d', 'type' => 'popup'],
    'main_smart' => ['captchaId' => '10df8790c89947028008ebeccbf9a885', 'type' => 'bind'],
    'main_normal' => ['captchaId' => '416a7bf2b6d44d869fd28391429fe4ab', 'type' => 'popup'],
    'app_smart' => ['captchaId' => 'f8582f417b2b44dc859a715e8f75a0e1', 'type' => 'smart'],  //4c87aab6da8349f5b1f45475abb353e0
    'app_normal' => ['captchaId' => '72c646c89c44458d98860d4aa69dccab', 'type' => 'normal'], //
];
