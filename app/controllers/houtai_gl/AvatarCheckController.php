<?php
/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:AvatarCheckController.php
 * 摘    要:
 * 作    者:<EMAIL>
 * 修改日期: 2015/11/4
 */
use Service\Avatar\Avatar;

includeBase('AdminController');

class AvatarCheckController extends AdminController
{
    protected $authName = "用户头像审核";

    /**
     * 后台展示未审核头像
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $memberModel = loadModel("member");
        $startTime = \Common\Utils\Url::getStringParam("starttime");
        $endTime = \Common\Utils\Url::getStringParam("endtime");
        $days = \Common\Utils\Url::getIntParam("days", 4);
        if (empty($startTime) || empty($endTime))
        {
            /** @see MemberModel::latestUnCheckAvatar() */
            $avatars = $memberModel->latestUnCheckAvatar($days);
        }
        else
        {
            $pageArray['starttime'] = $startTime;
            $pageArray['endtime'] = $endTime;
            /** @see MemberModel::uncheckAvatar */
            $avatars = $memberModel->uncheckAvatar($startTime, $endTime);
        }
        foreach ($avatars as $avatar)
        {
            $userFolder = ceil($avatar['passid'] / 3000);
            $avatar['path'] = $userFolder . "/{$avatar['passid']}_middle.jpg?v=" . time();
            $pageArray['avatar'][$avatar['uptime']][] = $avatar;
        }
        $sliceNum = 100;
        foreach ($pageArray['avatar'] as $day => $avatarInDay)
        {
            if (count($avatarInDay) > $sliceNum)
            {
                $pageArray['avatar'][$day] = array_slice($avatarInDay, 0, $sliceNum);
            }
        }
        krsort($pageArray['avatar']);
        loadView("admin/avatar.tpl.html", $pageArray);
    }

    /**
     * 审核头像
     * <AUTHOR>
     */
    public function actionVet()
    {
        $memberModel = loadModel("member");
        $checklist = $_POST['checklist'];
        $ret = $memberModel->vetAvatar($checklist);
        if ($ret)
        {
            echo json_encode(array("code" => "200.0", "msg" => mb_convert_encoding("审核成功", "UTF-8", "GBK")));
            return;
        }
        else
        {
            echo json_encode(array("code" => "400.0", "msg" => mb_convert_encoding("审核失败", "UTF-8", "GBK")));
            return;
        }
    }

    /**
     * 后台展示已审核过的头像
     * <AUTHOR>
     */
    public function actionVetted()
    {
        $memberModel = loadModel("member");
        $pageArray['startDate'] = $startDate = \Common\Utils\Url::getStringParam("startDate", date("Y-m-d"));
        $pageArray['endDate'] = $endDate = \Common\Utils\Url::getStringParam("endDate", date("Y-m-d"));
        $avatarsVetted = $memberModel->vettedAvatar($startDate, $endDate);
        $avatars = array();
        foreach ($avatarsVetted as $avatar)
        {
            $userFolder = ceil($avatar['passid'] / 3000);
            $avatar['path'] = $userFolder . "/{$avatar['passid']}_middle.jpg?v=" . time();
            $avatars[$avatar['uptime']][] = $avatar;
        }
        krsort($avatars);
        $pageArray['avatar'] = $avatars;
        loadView("admin/vetted.tpl.html", $pageArray);
    }

    /**
     * 删除未通过审核的头像 - 切换到新版
     * <AUTHOR>
     * @return void
     */
    public function actionDelete()
    {
        $userBaseModel = loadModel('UserBase');
        $memberModel = loadModel('member');
        $objLogV2Action = loadAction('logV2');
        $deletelist = $_POST['deletelist'];

        foreach ($deletelist as $key => $passid) {

            $arrUserInfo = $memberModel->read($passid);
            $arrUserInfo['passid'] = $passid;

            $arrOld = $userBaseModel->getAvatarNicknameInfoByPassId("login", $passid);

            $avatarData = [
                'valid_avatar_url' => "",
                'valid_avatar_source' => "",
                'in_approve_avatar_url' => "",
                'in_approve_avatar_source' => "",
                'approve_status' => 2, // 审核不通过
                'approve_person' => 'admin-delete',
            ];
            $errorMsg = [];
            $ret = $userBaseModel->setAvatarInfo("login", $passid, $avatarData, $errorMsg, "admin");
            if (!$ret) {
                echo json_encode(array("code" => "400.0", "msg" => mb_convert_encoding("删除失败", "UTF-8", "GBK")));
                return;
            }

            if ($arrOld['valid_avatar_url']) {
                //日志行为打点:用户信息解绑; type:USER_INFO_UNBIND; sub_type:AVATAR;
                loadAction('ImageUpload');
                $strAvatarOld = $strNew = \ImageUploadAction::privateDownloadUrl($arrOld['valid_avatar_url'], 'login', 'big', 86400);
                $objLogV2Action->report('ALL', 'USER_INFO_UNBIND', 'AVATAR', $arrUserInfo, $strAvatarOld, '', $_COOKIE["admin_user"]);
            }
        }
        echo json_encode(array("code" => "200.0", "msg" => mb_convert_encoding("删除成功", "UTF-8", "GBK")));
    }
}
