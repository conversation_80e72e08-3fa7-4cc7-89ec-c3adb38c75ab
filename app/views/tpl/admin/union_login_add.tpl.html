<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="gbk">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>联合登录管理</title>

    <!-- Bootstrap -->
    <link href="/css/UnionLogin/bootstrap.min.css" rel="stylesheet">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="/js/houtai_gl/unionLogin/html5shiv.min.js"></script>
    <script src="/js/houtai_gl/unionLogin/respond.min.js"></script>
    <![endif]-->
    <!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
    <script src="/js/houtai_gl/unionLogin/jquery.min.js"></script>
    <!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
    <script src="/js/houtai_gl/unionLogin/bootstrap.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
</head>
<body>
<div class="container">
    <div class="row">
        <h1 class="text-center">联合登录管理</h1>
    </div>
    <form class="form-horizontal" id="submitForm" action="/houtai_gl/UnionLogin/Save">
        <div class="form-group">
            <label for="name" class="col-sm-2 control-label">项目名称</label>
            <div class="col-sm-10">
                <input type="text" name="name" value="{{$pageArray.obj.name|mb_convert_encoding:gbk}}" class="form-control" id="name" placeholder="项目名称">
            </div>
        </div>
        <div class="form-group">
            <label for="mid" class="col-sm-2 control-label">项目标识</label>
            <div class="col-sm-10">
                <input type="text" value="{{$pageArray.obj.mid}}" name="mid" class="form-control" id="mid" placeholder="项目标识">
            </div>
        </div>
        <div class="form-group">
            <label for="package_name" class="col-sm-2 control-label">所属包名</label>
            <div class="col-sm-10">
                <textarea name="package_name" id="package_name" class="form-control" rows="3">{{$pageArray.obj.package_name}}</textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="relationship" class="col-sm-2 control-label">联合登录关联</label>
            <div class="col-sm-10">
                <select class="form-control js-example-basic-multiple" id="relationship" name="relationship[]"
                        multiple="multiple">
                    {{foreach from=$pageArray.basic item=vitem key=key}}
                    {{if $vitem.id!=$pageArray.obj.id}}
                    <option value="{{$vitem.id}}" {{if in_array($vitem.id,explode(';',$pageArray.obj.relationship)) }} selected {{/if}}>{{$vitem.name|mb_convert_encoding:gbk}}</option>
                    {{/if}}
                    {{/foreach}}
                </select>
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <input type="hidden" name="id" value="{{$pageArray.obj.id}}">
                <button type="button" id="submitButton" class="btn btn-info">保存</button>
                <a type="button"  class="btn btn-default" href="/houtai_gl/UnionLogin">返回</a>
            </div>
        </div>
    </form>

</div>
<script>
    $(document).ready(function () {
        $('.js-example-basic-multiple').select2();
    });
    $("#submitButton").click(function () {
        $.ajax({
            type: "POST",
            url: $('#submitForm').attr('action'),
            data: $('#submitForm').serialize(),
            dataType: 'JSON',
            success: function (data) {
                if (data.code == '200') {
                    alert(data.msg)
                    location.href = '/houtai_gl/UnionLogin'
                } else {
                    alert(data.msg)
                }
            }
        });
    })

</script>
</body>
</html>