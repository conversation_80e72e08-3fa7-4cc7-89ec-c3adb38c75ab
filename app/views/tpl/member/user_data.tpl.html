{{include file = "public/mem_header.tpl.html"}}
{{include file = "public/left_nav.tpl.html}}
    <!--account_left end-->
    <div class="account_right">
        <h2 class="pf">基本资料</h2>

        <div class="m-boxB-bd">
            <div class="m-form">
                <div class="form-item">
                    <span class="form-field ft-bold">真实姓名：</span>
                    <input class="ipt_txt ipt_defa" name="name" type="text"
                           value="{{if $pageArray.info.name}}{{$pageArray.info.name}}{{/if}}"/>
                </div>
                <div class="form-item">
                    <span class="form-field ft-bold">QQ：</span>
                    <input class="ipt_txt ipt_defa" type="text" name="qq"
                           value="{{if $pageArray.info.qq}}{{$pageArray.info.qq}}{{/if}}"/>
                </div>
                <div class="form-item form-item-radio">
                    <span class="form-field ft-bold">性别：</span>
                    <label for="female" class="piece genderItem"><i
                            class="ipt_radio{{if $pageArray.info.gender==2}} ipt_radio_cur{{/if}}"></i>女士<input
                            style="display: none;" type="radio" name="gender" id="female" value="2" {{if
                            $pageArray.info.gender==2}} checked="checked" {{/if}}></label>
                    <label for="male" class="piece genderItem"><i
                            class="ipt_radio{{if $pageArray.info.gender==1}} ipt_radio_cur{{/if}}"></i>男士<input
                            style="display: none;" type="radio" name="gender" id="male" value="1" {{if
                            $pageArray.info.gender==1}} checked="checked" {{/if}}></label>
                    <label class="piece genderItem" for="nomale"><i
                            class="ipt_radio{{if $pageArray.info.gender==0}} ipt_radio_cur{{/if}}"></i>保密<input
                            style="display: none;" type="radio" name="gender" id="nomale" value="0"
                            {{if $pageArray.info.gender==0}} checked="checked" {{/if}} ></label>
                </div>
                <div class="form-item" style="z-index: 10">
                    <span class="form-field ft-bold">生日：</span>
                    <div class="m-selectA" id="year" >
                        <input type="hidden" name="year" value="{{$pageArray.info.bday.0}}"/>

                        <div class="text">年</div>
                        <div class="holder"><i class="arrow-btm"></i></div>
                        <div class="option" style="display:none">
                            <ul>
                                {{foreach key=key item=item from=$pageArray.cdate.year}}
                                <li><a href="#" value="{{$item}}">{{$item}}</a></li>
                                {{/foreach}}
                            </ul>
                        </div>
                    </div>
                    <div class="m-selectA" id="month">
                        <input type="hidden" name="month" value="{{$pageArray.info.bday.1}}"/>

                        <div class="text">月</div>
                        <div class="holder"><i class="arrow-btm"></i></div>
                        <div class="option" style="display:none">
                            <ul>
                                {{foreach key=key item=item from=$pageArray.cdate.month}}
                                <li><a href="#" value="{{$item}}">{{$item}}</a></li>
                                {{/foreach}}
                            </ul>
                        </div>
                    </div>
                    <div class="m-selectA" id="day">
                        <input type="hidden" name="day" value="{{$pageArray.info.bday.2}}"/>

                        <div class="text">日</div>
                        <div class="holder"><i class="arrow-btm"></i></div>
                        <div class="option" style="display:none">
                            <ul>

                            </ul>
                        </div>
                    </div>
                </div>
                <div class="form-item" style="z-index: 9">
                    <span class="form-field ft-bold">地区：</span>

                    <div class="m-selectA" id="mcl" >
                        <input type="hidden" name="area1"/>

                        <div class="text">省</div>
                        <div class="holder"><i class="arrow-btm"></i></div>
                        <div style="display:none" class="option">
                            <ul>
                            </ul>
                        </div>
                    </div>
                    <div class="m-selectA" id="mcm">
                        <input type="hidden" name="area2"/>

                        <div class="text">市</div>
                        <div class="holder"><i class="arrow-btm"></i></div>
                        <div style="display:none" class="option">
                            <ul>
                            </ul>
                        </div>
                    </div>
                    <div class="m-selectA" id="mcn">
                        <input type="hidden" name="area3"/>

                        <div class="text">区县</div>
                        <div class="holder"><i class="arrow-btm"></i></div>
                        <div style="display:none" class="option">
                            <ul>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="form-item">
                    <a href="javascript:void(0);" onclick="submitForm();" class="btn-blueA">保存</a>
                </div>
            </div>
        </div>
    </div>
    <!--account_right end-->
</div><!--main end-->

{{include file = "public/qrcode.tpl.html"}}

<!--[if IE 6]>
<script src="http://buy.2345.com/buy_core/buyimg//js/DD_belatedPNG_0.0.8a-min.js"></script>
<script type="text/javascript">
    DD_belatedPNG.fix('.png24');
</script>
<![endif]-->
<script>
    $(function () {
        //input得到焦点和失去焦点
        $(".ipt_txt").focus(function () {
            $(this).addClass("ipt_txt_focus");
        });

        $(".ipt_txt").blur(function () {
            $(this).removeClass("ipt_txt_focus");
        });
    });//JQ
    var mcn_v = '{{$pageArray.info.area}}';
    var mcl_v = mcn_v.substring(0, 2) + '0000';
    var mcm_v = mcn_v.substring(0, 4) + '00';
    $("label.genderItem").click(function () {
        $("i.ipt_radio").attr("class", "ipt_radio");
        $("input[name='gender']").removeProp("checked");
        $(this).find("i.ipt_radio").addClass("ipt_radio_cur");
        $(this).find("input[name='gender']").prop("checked", "checked");
    });
    $(".m-selectA").click(function () {
        $(this).find('.option').show();
    }).hover(function () {
        clearTimeout($(this).data('dh'));
    }, function () {
        var that = this;
        $(this).data('dh', setTimeout(function () {
            $(that).find(".option").hide();
        }, 200));
    });
    $('.m-selectA .option ul li a').live('click', function () {
        $(this).parents('.m-selectA').find('.text').html($(this).html());
        $(this).parents('.m-selectA').find('input:hidden').val($(this).attr('value'));
        $(this).parents('.m-selectA').find('.option').hide();
        $(this).parents('.m-selectA').trigger('change');
        return false;
    });
    $("#year,#month").change(function () {
        allDay();
    });
    $("#mcl").change(function () {
        my_ds.on_select_l($(this).find('input:hidden').val());
    });
    $("#mcm").change(function () {
        my_ds.on_select_m($(this).find('input:hidden').val());
    });
    function allDay() {
        var year = $("#year input:hidden").val();
        var month = $("#month input:hidden").val();
        var maxDay;
        if (year != "" && month != "") {
            if (month == '01' || month == '03' || month == '05' || month == '07' || month == '08' || month == '10' || month == '12') {
                maxDay = 31;
            }
            else if (month == '04' || month == '06' || month == '09' || month == '11') {
                maxDay = 30;
            }
            else {
                if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0) {
                    maxDay = 29;
                }
                else {
                    maxDay = 28;
                }
            }
            var lis = [];
            for (var i = 1; i <= maxDay; i++) {
                var ii = i < 10 ? '0' + i : i;
                lis.push('<li><a href="#" value="' + ii + '">' + ii + '</a></li>');
            }
            $('#day .option ul').html(lis.join(''));
        }
    }
    if ('{{$pageArray.info.bday.0}}' != '0000') {
        $("#year .text").html("{{$pageArray.info.bday.0}}");
        $("#year input:hidden").val("{{$pageArray.info.bday.0}}")
        if ('{{$pageArray.info.bday.1}}' != '00') {
            $("#month .text").html("{{$pageArray.info.bday.1}}");
            $("#month input:hidden").val("{{$pageArray.info.bday.1}}")
            allDay();
            if ('{{$pageArray.info.bday.2}}' != '00') {
                $("#day .text").html("{{$pageArray.info.bday.2}}");
                $("#day input:hidden").val("{{$pageArray.info.bday.2}}");
            }
        }
    }
    function submitForm() {
        $.ajax({
            url: "/api/check/jsonp",
            data: 'type=username&value=' + $("input[name='name']").val(),
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function(response) {
                if (response != 2)
                {
                    $.post("/member/editData/edit",
                        {
                            data: AES.encrypt(
                                {
                                    'qq': $("input[name='qq']").val(),
                                    'name': $("input[name='name']").val(),
                                    'gender': $("input[name='gender']:checked").val(),
                                    'year': $("input[name='year']").val(),
                                    'month': $("input[name='month']").val(),
                                    'day': $("input[name='day']").val(),
                                    'area3': $("input[name='area3']").val(),
                                    'old_area_text': old_area_text,
                                    'new_area_text': getAreaText(),
                                }
                            )
                        },
                        function (data) {
                            if (data == '200.0') {
                                alert("资料修改成功！");
                                window.location = '/member/editData';
                            } else if (data == '300.0') {
                                alert("您输入的qq格式不正确！");
                            } else if (data == '300.1') {
                                alert("您输入的姓名格式不正确！");
                            } else if (data == '300.2') {
                                alert("您输入的生日格式不正确！");
                            } else if (data == '300.3') {
                                alert("您输入的地区格式不正确！");
                            } else if (data == '300.4') {
                                alert("您输入的内容包含非法字符，请重新输入！");
                            } else if(data == '500.0') {
                                alert("服务器忙，资料修改失败！");
                            }
                        });
                }
                else
                {
                    alert("您输入的内容包含非法字符，请重新输入！");
                }
            },
            error: function(jqXHR, textStatus, errorThrown) {
                alert("服务器忙，资料修改失败！");
            },
            timeout: 3000
        });
    }
</script>
<script src="/js/my_city_2015.js" type="text/javascript"></script>
<script>
    var old_area_text = getAreaText();

    function getAreaText() {
        var area_text_1 = $("#mcl").find("div.text").text();
        var area_text_2 = $("#mcm").find("div.text").text();
        var area_text_3 = $("#mcn").find("div.text").text();
        var area_text = area_text_1 + area_text_2 + area_text_3;
        if (area_text == '省市区县') {
            area_text = '';
        }
        return area_text;
    }
</script>
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
{{include file = "public/mem_footer.tpl.html"}}