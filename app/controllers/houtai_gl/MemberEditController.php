<?php

includeBase('AdminController');

class MemberEditController extends AdminController
{

    protected $authName = '注册用户编辑';

    public function actionIndex()
    {
        $passid = intval($_GET['id']);
        $memberModel = loadModel('member');
        $arr = $memberModel->read($passid);
        list($avatarUrl) = \Service\Avatar\Avatar::getTransferAvatar('login', $passid, 'middle');
        $arr['avatar'] = $avatarUrl;
        $arr["notSetUsername"] = $arr["gid"] % 100 == 0;

        $newArr['info'] = $arr;
        $newArr['allowResetUsername'] = $_COOKIE["admin_user"] == "王小威";

        if ($_POST)
        {
            $arrUserInfo = [
                'passid' => $passid,
                'username' => $arr['username'],
                'phone' => $arr['phone'],
                'email' => $arr['email'],
            ];
            $objLogV2Action = loadAction('logV2');

            $logs = array();
            if ($_POST['email'] != $arr['email'] && $_POST['email'] != '')
            {
                $emailAction = loadAction('email');
                $code = $emailAction->edit($passid, $_POST['email']);
                if ($code == '200.0')
                {
                    $logs['email'] = '修改了邮箱 ' . $arr['email'] . ' 为 ' . $_POST['email'];

                    //日志行为打点:用户信息添加或修改; type:USER_INFO_ADD; sub_type:EMAIL;
                    $strLogType = empty($arrUserInfo['email']) ? 'USER_INFO_ADD' : 'USER_INFO_MODIFY';
                    $objLogV2Action->report('ALL', $strLogType, 'EMAIL', $arrUserInfo, $arrUserInfo['email'], $_POST['email'], $_COOKIE["admin_user"]);
                }
                elseif ($code == '300.0')
                {
                    showMessage("邮箱修改失败，邮箱格式错误！", 'back');
                }
                elseif ($code == '300.1')
                {
                    showMessage("邮箱修改失败，已被其他用户当做用户名！", 'back');
                }
                elseif ($code == '300.2')
                {
                    showMessage("邮箱修改失败，已被其他用户绑定！", 'back');
                }
                elseif ($code == '500.0')
                {
                    showMessage("邮箱修改失败，重复修改了！", 'back');
                }
            }
            if (isset($_POST['username']) && $_POST['username'])
            {
                // 权限验证
                if (!$newArr['allowResetUsername'])
                {
                    showMessage("无修改用户名权限！", 'back');
                }
                $username = trim($_POST['username']);
                if (mb_strlen($username, "GBK") < 2)
                {
                    showMessage("2345帐号最少2个字符！", 'back');
                }
                if (strlen($username) > 24)
                {
                    showMessage("2345帐号请不要超过24个字符！", 'back');
                }
                $regex = Config::get('regex');
                if (preg_match($regex['username'], $username) || preg_match($regex['phone'], $username))
                {
                    if ($newArr['allowResetUsername'] == "王小威")
                    {
                        ;
                    }
                    else
                    {
                        showMessage("2345帐号请输入汉字，字母，数字，或邮箱地址！", 'back');
                    }
                }
                $memberModel = loadModel('member');
                if (preg_match($regex['phone'], $username))
                {
                    if ($memberModel->checkPhone($username))
                    {
                        showMessage("此帐号已被注册，请修改2345帐号！", 'back');
                    }
                }
                elseif (filter_var($username, FILTER_VALIDATE_EMAIL))
                {
                    if ($memberModel->checkEmail($username))
                    {
                        showMessage("此帐号已被注册，请修改2345帐号！", 'back');
                    }
                }
                else
                {
                    if ($memberModel->checkUser($username))
                    {
                        showMessage("此帐号已被注册，请修改2345帐号！", 'back');
                    }
                }
                /** @see MemberModel::updateUsername() */
                $res = $memberModel->updateUsername($passid, $_POST['username']);
                if ($res)
                {
                    $logs['username'] = '修改用户名 ' . $arr['username'] . ' 为 ' . $username;

                    //日志行为打点:用户信息添加或修改; type:USER_INFO_ADD; sub_type:USERNAME;
                    $strLogType = empty($arrUserInfo['username']) ? 'USER_INFO_ADD' : 'USER_INFO_MODIFY';
                    $objLogV2Action->report('ALL', $strLogType, 'USERNAME', $arrUserInfo, $arrUserInfo['username'], $_POST['username'], $_COOKIE["admin_user"]);
                }
                else
                {
                    showMessage("修改失败, 请重试！", 'back');
                }
            }
            if ($_POST['password'])
            {
                $logs['password'] = '修改了密码';
                $memberModel->updatePassword($passid, $_POST['password'], 2, get_client_ip());

                //日志行为打点:用户信息添加或修改; type:USER_INFO_ADD; sub_type:PASSWORD;
                $objLogV2Action->report('ALL', 'USER_INFO_MODIFY', 'PASSWORD', $arrUserInfo, '', '', $_COOKIE["admin_user"]);

                /*
                todo 上面 model 层已经做了 通知
                $regAction = loadAction("reg");
                $regAction->NoticePassword($passid);
                */
            }
            if (isset($_POST['active']) && $_POST['active'] == 1)
            {
                $logs['active'] = '激活了用户';
                $memberModel->unlockUser($passid);
            }
            if (sizeof($logs))
            {
                $logData = array(
                    'passid' => $passid,
                    'log' => implode(',', $logs),
                    'op_user' => $_COOKIE['admin_user'],
                    'op_time' => date('Y-m-d H:i:s')
                );
                $logModel = loadModel('Log');
                $logModel->addAdminLog($logData);
            }
            if (isset($_POST['returnUrl']) && !empty($_POST['returnUrl']))
            {
                header('location:' . $_POST['returnUrl']);
                exit;
            }
        }
        loadView('admin/member_edit.tpl.html', $newArr);
    }


    /**
     * 冻结（清理用户登录信息）或解冻账号,
     *
     * @return void
     */
    public function actionBanAccount()
    {
        $passid = intval($_GET['id']);
        $memberModel = loadModel('member');
        $userInfo = $memberModel->read($passid);
        if (!empty($userInfo))
        {
            $locked = 2 == $userInfo['locked'] ? 0 : 2;
            $res = $memberModel->setMemberLock($passid, $locked);
            if ($locked == 2 && $res)
            {
                $sessionService = new \Service\Account\Session();
                $sessionService->removeUserSessionByPassid($passid);
            }
            $logs['account'] = 2 == $userInfo['locked'] ? '解除冻结' : '点击冻结';

            $insert = array(
                'passid'  => $passid,
                'log'     => implode(',', $logs),
                'op_user' => $_COOKIE['admin_user'],
                'op_time' => date('Y-m-d H:i:s'),
            );
            $logModel = loadModel('Log');
            $logModel->addAdminLog($insert);

            //日志行为打点:用户冻结或解冻; type:USER_FREEZE; sub_type:PASSID;
            if ($res) {
                $arrUserInfo = [
                    'passid' => $passid,
                    'username' => $userInfo['username'],
                    'phone' => $userInfo['phone'],
                    'email' => $userInfo['email'],
                ];
                $objLogV2Action = loadAction('logV2');
                $strLogType = 2 == $userInfo['locked'] ? 'USER_UNFREEZE' : 'USER_FREEZE';
                $objLogV2Action->report('ALL', $strLogType, 'PASSID', $arrUserInfo, '', '', $_COOKIE["admin_user"]);
            }
        }
        redirect("/houtai_gl/memberEdit?id=$passid");
    }


    /**
     * 解绑手机
     */
    public function actionUnbindPhone()
    {
        $passid = intval($_GET['id']);
        if ($passid)
        {
            $memberModel = loadModel('member');
            $arrUserInfo = $memberModel->read($passid);
            $arrUserInfo['passid'] = $passid;

            $phoneModel = loadModel('phone');
            $res = $phoneModel->del($passid);

            if ($res) {
                //日志行为打点:用户信息解绑; type:USER_INFO_UNBIND; sub_type:PHONE;
                $objLogV2Action = loadAction('logV2');
                $objLogV2Action->report('ALL', 'USER_INFO_UNBIND', 'PHONE', $arrUserInfo, $arrUserInfo['phone'], '', $_COOKIE["admin_user"]);
            }

            $logs['phone'] = '解绑了手机';
            $logData = array(
                'passid' => $passid,
                'log' => implode(',', $logs),
                'op_user' => $_COOKIE['admin_user'],
                'op_time' => date('Y-m-d H:i:s')
            );
            $logModel = loadModel('Log');
            $logModel->addAdminLog($logData);

            $memberModel = loadModel('member');
            $uid = $memberModel->getUid($passid);
            noticeToChange($passid, 'changePhone', array(
                'passid' => $passid,
                'uid' => $uid,
                'type' => 'phone',
                'value' => ''
            ));
        }
        redirect("/houtai_gl/memberEdit?id=$passid");
    }

    /**
     * 同步帐号信息
     */
    public function actionNotice()
    {
        $passid = intval($_GET['id']);
        $type = $_GET['type'];
        if ($passid)
        {
            $memberModel = loadModel('member');
            $result = $memberModel->read($passid);
            $result["m_uid"] = unserialize($result['m_uid']);
            $uid = $result['m_uid']['1'];
            $username = $result['username'];
            $logs = [];
            if ($type == 'username')
            {
                noticeToChange($passid, 'changeUsername', array(
                    'passid' => $passid,
                    'uid' => $uid,
                    'type' => 'username',
                    'value' => $username
                ));
                $logs['notice'] = '同步了用户名信息' . $username;
            }
            else if ($type == 'email')
            {
                //TODO
            }
            else if ($type == 'phone')
            {
                $phoneModel = loadModel('phone');
                $phoneInfo = $phoneModel->get($passid);
                $phone = '';
                if ($phoneInfo['phone'])
                {
                    $phone = $phoneInfo['phone'];
                }
                noticeToChange($passid, 'changePhone', array(
                    'passid' => $passid,
                    'uid' => $uid,
                    'type' => 'phone',
                    'value' => $phone
                ));
                $logs['notice'] = '同步了手机号信息' . $phone;

            }
            $logData = array(
                'passid' => $passid,
                'log' => implode(',', $logs),
                'op_user' => $_COOKIE['admin_user'],
                'op_time' => date('Y-m-d H:i:s')
            );
            $logModel = loadModel('Log');
            $logModel->addAdminLog($logData);
        }
        if ($_REQUEST['from'] == 'v3') {
            exit('{"code":200,"message":"成功"}');
        }
        redirect("/houtai_gl/memberEdit?id=$passid");
    }

    /**
     * 重置用户修改用户名权限
     */
    public function actionAllowSetUsername()
    {
        $passid = intval($_GET['id']);
        $memberModel = loadModel('member');
        /** @see \MemberModel::allowSetUsername() */
        $res = $memberModel->allowSetUsername($passid);
        if ($res)
        {
            $logs = array("重置了用户名修改权限");
            $logData = array(
                'passid' => $passid,
                'log' => implode(',', $logs),
                'op_user' => $_COOKIE['admin_user'],
                'op_time' => date('Y-m-d H:i:s')
            );
            $logModel = loadModel('Log');
            $logModel->addAdminLog($logData);
            redirect("/houtai_gl/memberEdit?id=$passid");
            return true;
        }
        showMessage("重置用户名设置权限失败, 请重试！", 'back');
    }

}
