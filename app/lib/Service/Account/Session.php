<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：Session.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：10-24, 2016
 */

namespace Service\Account;

class Session
{

    /**
     *
     * 获取某个用户的session 的redis key
     *
     * @param int $passid passid
     *
     * @return string
     */
    private function getUserSessionKey($passid)
    {
        return "LoginSession:" . $passid;
    }

    /**
     * 获取用户所有 session 的redis key
     *
     * @return string
     */
    private function getAllSessionKey()
    {
        return "LoginSessions";
    }

    /**
     * @param int    $passid  passid
     * @param string $sid     sessionId
     * @param int    $expired expired
     *
     * @return bool
     */
    public function addUserSession($passid, $sid, $expired = 2592000)
    {
        $redis = \RedisEx::getInstance();
        $userSessionKey = $this->getUserSessionKey($passid);
        if ($redis->zAdd($userSessionKey, time() + $expired, $sid) !== false) {
            $redis->zRemRangeByRank($userSessionKey, 0, - 25);
            $isTrue = $redis->expireAt($userSessionKey, time() + $expired);
            return $isTrue;
        }
        return false;
    }

    /**
     * 用户 session key 是否过期
     *
     * @param int    $passid passid
     * @param string $sid    sessionId
     *
     * @return mixed
     */
    public function isUserSessionExpired($passid, $sid)
    {
        $redis = \RedisEx::getInstance();
        $userSessionKey = $this->getUserSessionKey($passid);

        $expiredTime = $redis->zScore($userSessionKey, $sid);
        if (!$expiredTime) {
            $expiredTime = $redis->zScore($userSessionKey, $sid);
        }

        return $expiredTime > time() ? $expiredTime : false;
    }

    /**
     * 删除 用户 redis session
     *
     * @param int $passid passid
     *
     * @return mixed
     */
    public function removeUserSessionByPassid($passid)
    {
        $redis = \RedisEx::getInstance();
        $userSessionKey = $this->getUserSessionKey($passid);
        /*
        $allSessionKey = $this->getAllSessionKey();

        $sids = $redis->zRange($userSessionKey, 0, - 1);
        // 删除 所有用户key中的 session key;
        /*
        foreach ($sids as $tmpSid)
        {
            $redis->zRem($allSessionKey, $tmpSid);
        }
        */
        // 删除 该用户 的 session key
        $res = $redis->del($userSessionKey);

        return $res;
    }

}
