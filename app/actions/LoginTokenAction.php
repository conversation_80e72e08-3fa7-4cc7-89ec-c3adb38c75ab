<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/3
 * Time: 18:06
 */

class LoginTokenAction extends Action
{
    /**
     * 获取外部配置
     * -
     * @param string $exId 项目标识
     * @param string $field 需要获取的字段名称
     * @return string
     */
    public function getExternalConfig($exId, $field = '')
    {
        $resultArr = [];
        $externalArr = \Service\Config\ExternalConfig::get($exId);
        if (empty($field))
        {
            return $externalArr;
        }
        $field = explode(".", $field);
        foreach ($field as $info)
        {
            if (empty($info))
            {
                continue;
            }
            if (empty($resultArr))
            {
                $resultArr = $externalArr[$info] ?? [];
            }
            else
            {
                $resultArr = $resultArr[$info] ?? [];
            }
        }
        return $resultArr;
    }

    /**
     * 获取登录redis key值
     * -
     * @param string $exId 外部项目唯一标识
     * @param int $passid passid
     * @return string
     */
    public function getLoginKey($exId, $passid)
    {
        return \Service\Config\LoginKey::LOGINTOKENKEY . $exId . ':' . $passid;
    }

    /**
     * 获取多端登录配置信息
     * -
     * @param string $exId 外部项目唯一标识
     * @return string
     */
    public function getMultiClientConfig($exId)
    {
        return $this->getExternalConfig($exId, 'MultiClientConfig');
    }

    /**
     * 获取登录生成的token值
     * -
     * @param string $exId 外部项目的唯一标识
     * @param int $passid passid
     * @param string $dynamicKey 动态因子
     * @param string $loginPrivateKey 项目登录KEY
     * @return string
     */
    public function generateLoginToken($exId, $passid, $dynamicKey, $loginPrivateKey)
    {
        return md5($passid . $exId . $dynamicKey . SESSIONKEY . $loginPrivateKey);
    }

    /**
     * 设置登录TOKEN
     * -
     * @param string $exId 外部项目唯一标识
     * @param int $passid passid
     * @return mixed
     */
    public function setLoginToken($exId, $passid)
    {
        $multiClientConfig = $this->getMultiClientConfig($exId);
        //如果没有找到项目login key 则返回失败
        if (empty($multiClientConfig['loginPrivateKey']))
        {
            return false;
        }
        $dynamicKey = time();
        //生成token
        $token = $this->generateLoginToken($exId, $passid, $dynamicKey, $multiClientConfig['loginPrivateKey']);
        $redis = RedisEx::getInstance();
        //有效期  如果项目设置了有效期 则使用项目有效期,否则使用默认有效期20天
        if (!empty($multiClientConfig['expired']) && (int)$multiClientConfig['expired'] > 0)
        {
            $expiredTime = time() + $multiClientConfig['expired'];
        }
        else
        {
            $expiredTime = time() + \Service\Config\externalConfig::$expired;
        }
        //有效token队列
        if (!empty($multiClientConfig['validTokenNum']) && (int)$multiClientConfig['validTokenNum'] > 0)
        {
            $validTokenNum = $multiClientConfig['validTokenNum'];
        }
        else
        {
            $validTokenNum = \Service\Config\externalConfig::$validTokenNum;
        }
        $loginTokenKey = $this->getLoginKey($exId, $passid);
        //如果不设置 则默认单端登录，为true时则开启多端登录，  位置token有效队列取决项目设置大小，无设置使用默认
        if (isset($multiClientConfig['isMultiClient']) && $multiClientConfig['isMultiClient'] === true)
        {
            $isAdd = $redis->zAdd($loginTokenKey, $expiredTime, $token);
            if ($isAdd)
            {
                $redis->zRemRangeByRank($loginTokenKey, 0, -$validTokenNum);
                $redis->expireAt($loginTokenKey, $expiredTime);
            }
            else
            {
                return false;
            }
        }
        else
        {
            $redis->del($loginTokenKey);
            $isAdd = $redis->zAdd($loginTokenKey, $expiredTime, $token);
            if ($isAdd)
            {
                $redis->expireAt($loginTokenKey, $expiredTime);
            }
            else
            {
                return false;
            }
        }
        $response = [
            'token' => $token,
            'passid' => $passid,
            'requestTime' => $dynamicKey,
            'exId' => $exId,
        ];
        $response['sign'] = \Common\Validator\Params::getSign($exId, $response);
        return $this->aes128cbcEncrypt($exId, $response);
    }


    /**
     * 更换令牌
     * -
     * @param string $exId 外部项目唯一标识
     * @param int $passid passid
     * @param string $token 老令牌
     * @return string
     */
    public function changeSpecifyToken($exId, $passid, $token)
    {
        $newToken = $this->setLoginToken($exId, $passid);
        if (!empty($newToken))
        {
            $multiClientConfig = $this->getMultiClientConfig($exId);
            //如果允许多端登录，更新token时删除老的token
            if (isset($multiClientConfig['isMultiClient']) && $multiClientConfig['isMultiClient'] === true)
            {
                $redis = RedisEx::getInstance();
                $loginTokenKey = $this->getLoginKey($exId, $passid);
                $redis->zRem($loginTokenKey, $token);
            }
            return $newToken;
        }
        else
        {
            return "";
        }
    }


    /**
     * @param string $exId 外部项目唯一标识
     * @param array $pushData 推送数据
     * @return array
     */
    public function pushLoginInfo($exId, $pushData)
    {
        $callbackUrl = $this->getExternalConfig($exId, "push.callbackUrl");
        //如果没有设置推送地址
        if (empty($callbackUrl))
        {
            return '';
        }
        else
        {
            $isSync = $this->getExternalConfig($exId, "push.sync");
            $pushData['timestamp'] = time();
            if ($isSync)
            {
                unset($pushData['phone']);
            }
            $pushData['sign'] = \Common\Validator\Params::getSign($exId, $pushData);
            $response['pushParam'] = json_encode($pushData);
            $response['callbackUrl'] = $callbackUrl;
            //如果是同步推送, 就返回callbackurl信息
            if ($isSync)
            {
                return $this->returnResponseData($exId, $response, false);
            }
            else
            {
                //如果设置了callbackUrl则进行异步推送
                loadAction('LoadRedis');
                $logRedis = LoadRedisAction::getInstance('dlx_log', 'hash1');
                $logRedis->lPush(\Service\Config\LoginKey::PUSHLOGININFOKEY, json_encode($response));
                return '';
            }
        }
    }

    /**
     * 校验传递的TOKEN是否有效
     * -
     * @param string $exId 外部项目唯一标识
     * @param string $passid passid
     * @param string $dynamicKey 动态因子
     * @param string $inputToken 传递的TOKEN值
     * @return bool
     */
    public function checkToken($exId, $passid, $dynamicKey, $inputToken)
    {
        $multiClientConfig = $this->getMultiClientConfig($exId);
        //如果没有找到项目login key 则返回失败
        if (empty($multiClientConfig['loginPrivateKey']))
        {
            return false;
        }
        $token = $this->generateLoginToken($exId, $passid, $dynamicKey, $multiClientConfig['loginPrivateKey']);
        $loginTokenKey = $this->getLoginKey($exId, $passid);
        if ($token == $inputToken)
        {
            $redis = RedisEx::getInstance();
            $expiredTime = $redis->zScore($loginTokenKey, $inputToken);
            if ($expiredTime > time())
            {
                return true;
            }
            else
            {
                $redis->zRem($loginTokenKey, $inputToken);
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    /**
     * 数据进行aes加密
     * -
     * @param string $exId 外部项目唯一标识
     * @param array $data 加密数据
     * @param bool $isSign 是否加签
     * @return string
     */
    public function returnResponseData($exId, $data, $isSign = true)
    {
        if (empty($data) || !is_array($data))
        {
            return '';
        }
        if ($isSign)
        {
            if (empty($data['timestamp']))
            {
                $data['timestamp'] = time();
            }
            $data['sign'] = \Common\Validator\Params::getSign($exId, $data);
        }
        return $this->aes128cbcEncrypt($exId, $data);
    }

    /**
     * aes加密
     * -
     * @param string $exId 外部项目唯一标识
     * @param array $data 加密参数
     * @return string
     */
    public function aes128cbcEncrypt($exId, $data)
    {
        $aesConfig = $this->getExternalConfig($exId, "aesConfig");
        if (!isset($aesConfig['aesKey']) || !isset($aesConfig['aesIv']))
        {
            return '';
        }
        $aes = new \Service\Encryption\Aes\AesManager($aesConfig['aesKey'], $aesConfig['aesIv']);
        loadAction("Encoding");
        return $exId . ":" . $aes->aes128cbcEncrypt(json_encode(EncodingAction::transcoding($data)));
    }

    /**
     * aes解密
     * -
     * @param string $exId 外部项目唯一标识
     * @param string $aesData 加密的字符串
     * @param bool $isCharset 是否进行转码
     * @return array
     */
    public function aes128cbcHexDecrypt($exId, $aesData, $isCharset = true)
    {
        $aesConfig = $this->getExternalConfig($exId, "aesConfig");
        if (!isset($aesConfig['aesKey']) || !isset($aesConfig['aesIv']))
        {
            return [];
        }
        $aes = new \Service\Encryption\Aes\AesManager($aesConfig['aesKey'], $aesConfig['aesIv']);
        loadAction("Encoding");
        $aesDecodeData = $aes->aes128cbcHexDecrypt($aesData);
        if (empty($aesDecodeData))
        {
            return [];
        }
        if ($isCharset)
        {
            $aesDecodeData = \EncodingAction::transcoding(json_decode($aesDecodeData, true), 'gbk');
        }
        return $aesDecodeData;
    }
}
