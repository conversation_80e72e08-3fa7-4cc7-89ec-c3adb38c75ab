<?php
namespace Admin;
class ClearLimitModel extends \Model
{
    /**
     * 初始数据库
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }
    
    public function getTodayProjectSendTotal($phone,$type)
    {
        $sql = "SELECT logid.*,pl.`project_name` FROM 
            (SELECT pid,COUNT(id) AS total,`type` FROM `my_sms_logs` WHERE phone=:phone AND `type` = :type AND 
            FROM_UNIXTIME( UNIX_TIMESTAMP( send_time) ,'%Y-%m-%d')= :send_time GROUP BY pid) AS logid,
            `project_list` AS pl WHERE logid.pid = pl.`pid` ";
        $paramArr = array(
            ':phone' => $phone ,
            ':type' => $type,
            ':send_time' => date('Y-m-d')
        );
        $projectTotal = $this->pdo->findAll($sql , $paramArr);
        return $projectTotal;
    }
}
?>