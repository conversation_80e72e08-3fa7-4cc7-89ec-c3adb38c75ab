{{include file = "public/mem_header.tpl.html"}}
{{include file = "public/left_nav.tpl.html}}
<div class="account_right" style=" height:500px;">

    <!--修改邮箱 开始-->
    <div class="changeEmail-A">
        <h2 class="pf">修改邮箱</h2>

        <div class="styleMod"><span class="blueTxt">{{$pageArray.email}}</span><a href="javascript:" id="showeditemail">修改</a>
        </div>
        <div class="styleMod">绑定邮箱可以用来登录您的2345帐号与找回密码</div>
    </div>
    <!--changeEmail-A end-->

    <h2 class="pf mt46">绑定邮箱常见问题</h2>
    <dl class="questionList">
        <dt>我没收到邮件怎么办</dt>
        <dd>尝试如下方式：请检查邮箱地址是否正确，检查邮件垃圾箱，在邮箱设置中将"2345.com"添加到白名单再点击，若仍未收到，请稍后重新点击发送。</dd>
    </dl>
</div><!--account_right end-->
<!--主体右侧 结束-->
</div><!--main end-->
<!--主体 end-->
{{include file = "public/qrcode.tpl.html"}}

<div class="box_mark" {{if $pageArray.open}}style="display:block"{{else}}style="display: none"{{/if}}>遮罩层</div>
<div class="box_mod h404" id="verifyemail" {{if $pageArray.open}}style="display:block"{{else}}style="display: none"{{/if}}>
<div class="box_mod_tit"><span>修改邮箱</span><a class="btn_box_close" id="verify-close" href="javascript:"></a></div>
<div class="boxCon">
    <div class="ts-wrap">
        <p class="tsTxt">为了保证帐号安全，修改邮箱前请先进行安全验证</p>
        <p class="ts-infor" id="verifyemailtip" style="display: none"><i></i>发送成功，请查收</p>
    </div>
    <!--ts-wrap end-->

    <p class="yzTxt">验证方式</p>

    <ul class="conUl2">
        <li class="clearfix">
            <div class="selectWrap clearfix">
                <input id="from" name="from" value="{{if $pageArray.phone}}phone{{else}}email{{/if}}" style="display: none"/>
                <div class="selectDiv">
                    <div class="coninput2">
                        <input class="getVal" type="text" autocomplete="off" readonly value="{{if $pageArray.phone}}手机（{{$pageArray.phone}}）{{else}}邮箱（{{$pageArray.email}}）{{/if}}"/>
                    </div>
                    <span class="btn-select"></span>
                </div>
                <dl class="selectBox">
                    {{if $pageArray.phone}}
                    <dd id="phonetype"><a href="javascript:">手机（{{$pageArray.phone}}）</a></dd>
                    {{/if}}
                    <dd id="emailtype"><a href="javascript:">邮箱（{{$pageArray.email}}）</a></dd>
                </dl>
            </div>
        </li>
        <li class="clearfix">
            <div class="coninput w261"><input class="defaultInput" type="text" name="code" id="verifycode"
                                              autocomplete="off" value="请输入验证码" title="请输入验证码"/></div>
            <a class="messCode" href="javascript:" onclick="sendCode();return false;">发送验证码</a>
            <span class="messCode-dsib" style="display:none;">重新发送（60s）</span>
        </li>
    </ul>
    <!--conUl2 end-->
    <a class="btn-tj" id="verifyemailsubmit" href="javascript:">提交</a>

    <div class="mt17" id="phonetip" {{if $pageArray.phone}}{{else}}style="display: none" {{
    /if}}>
    <p class="ifTxt">如果您的手机无法收到验证码，请致电客服解决，客服电话：************</p>
</div>
<div class="mt17" id="emailtip" {{if $pageArray.phone}}style="display: none"{{else}}{{/if}}>
<p class="Noemail">我没收到邮件,怎么办?</p>
<p class="ifTxt">尝试如下方式：请检查邮箱地址是否正确，检查邮件垃圾箱，在邮箱设置中
    将“2345.com”添加到白名单后再点击，若仍未收到，请稍后重新点击发送。
</p>
</div>
</div>
<!--boxCon end-->
</div><!--box_mod end-->
<!--修改邮箱弹窗2 结束-->

<!--修改邮箱弹窗1 开始-->
<div class="box_mod w500" id="editemail">
    <div class="box_mod_tit"><span>修改邮箱</span><a class="btn_box_close" id="editemail-close" href="javascript:"></a></div>
    <div class="boxCon">
        <div class="ts-wrap">
            <p class="tsTxt">温馨提示：请输入您要修改的邮箱地址，可用该邮箱登录所有2345产品</p>

            <p class="ts-infor" id="editemailtip" style="display: none"><i></i>发送成功，请查收</p>
        </div>
        <!--ts-wrap end-->
        <ul class="conUl">
            <li>
                <span class="conTit">邮箱地址：</span>
                <div class="coninput w195">
                    <input class="defaultInput" type="text" id="email" name="email" onblur="checkEmail();return false;"
                           autocomplete="off" value="请输入邮箱地址" title="请输入邮箱地址" maxlength="24"/>
                </div>
                <a class="messCode" href="javascript:" onclick="sendEditCode();return false;">发送验证码</a>
                <span id="sendingBtn" class="messCode-dsib" style="display:none;">重新发送（60s）</span>
            </li>
            <li>
                <span class="conTit">验证码：</span>
                <div class="coninput">
                    <input type="text" id="code" name="code" value="" autocomplete="off"/>
                </div>
            </li>
        </ul>
        <!--conUl end-->
        <a class="btn-tj" id="editemailsubmit" href="javascript:">提交</a>

        <div class="mt17">
            <p class="Noemail">我没收到邮件,怎么办?</p>
            <p class="ifTxt">尝试如下方式：请检查邮箱地址是否正确，检查邮件垃圾箱，在邮箱设置中
                将“2345.com”添加到白名单后再点击，若仍未收到，请稍后重新点击发送。
            </p>
        </div>
    </div>
    <!--boxCon end-->
</div><!--box_mod end-->
<!--修改邮箱弹窗1 结束-->

<div class="box_mod h232" id="editsuccess">
    <div class="box_mod_tit"><a class="btn_box_close" href="javascript:"></a></div>
    <div class="infor-con">
        <i class="gouIcon"></i>

        <div class="infor-txt">
            <h2>修改邮箱成功！</h2>

            <p id="forwardTips">3秒后<a
                    href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a></p>
        </div>
    </div>
    <!--boxCon end-->
</div>
<!--[if IE 6]>
<script type="text/javascript" src="/js/DD_belatedPNG_0.0.8a-min.js"></script>
<script type="text/javascript">
    DD_belatedPNG.fix('.png24');
</script>
<![endif]-->
{{include file = "public/mem_footer.tpl.html"}}


<script>
    var bottomObjectTimeId = setInterval(function(){
        if($("#ClCache") != undefined){
            clearInterval(bottomObjectTimeId);
            $("#ClCache").parent().css("display","none");
        }
    },1000);
    var windowHeight = $(window).height();
    var documentHeight;
    {{if $pageArray.open}}
        maskBoxHeight();
    {{/if}}
    function maskBoxHeight()
    {
        var documentHeight = $(document).height();
        if (documentHeight > windowHeight) {
            $(".box_mark").css("height", documentHeight).show();
        } else {
            $(".box_mark").css("height", windowHeight).show();
        };
    };
    $("#phonetype").bind("click", function () {
        $("#from").val("phone");
        $("#emailtip").hide();
        $("#phonetip").show();
    });
    $("#emailtype").bind("click", function () {
        $("#from").val("email");
        $("#phonetip").hide();
        $("#emailtip").show();
    });
    var messageCodeTime = $(".messCode-dsib"), messageTimeNum = 60, btnOk = $(".messCode"), windowHeight = $(window).height();
    $("#showeditemail").bind("click", function () {
//        $(".box_mark").css("height", windowHeight).show();
        maskBoxHeight();
        $("#verifyemail").show();
    });
    $("#verify-close").bind("click", function(){
        $("#verifycode").val("");
        $("#verifyemailtip").hide();
        messageCodeTime.hide();
        btnOk.text("发送验证码").show();
    });
    $("#editemail-close").bind("click", function(){
        $("#verifycode").val("");
        $("#verifyemailtip").hide();
        $("#email").val("请输入邮箱地址").removeClass("colorInput");
        $("#code").val("");
        $("#editemailtip").hide();
        messageCodeTime.hide();
        btnOk.text("发送验证码").show();
    });
    //关闭弹框
    $(".btn_box_close").bind("click", function () {
        $(".box_mod,.box_mark").hide();
    });
    $(".selectDiv").bind("click", function () {
        $(this).next(".selectBox").toggle();
        return false;
    });
    $(".selectBox a").bind("click", function () {
        $(this).parents(".selectWrap").find(".getVal").val($(this).text());
    });

    $(document).bind("click", function () {
        $(".selectBox").hide();
    });
    $(".defaultInput").bind(
            {
                focus: function () {
                    $(this).parent().removeClass("inputTxtError");
                    $(this).parent().addClass("inputTxtFocus");
                    if ($(this).val() == $(this).attr("title")) {
                        $(this).val("");
                    }
                    $(this).addClass("colorInput");
                },
                blur: function () {
                    $(this).parent().removeClass("inputTxtFocus");
                    if ($.trim($(this).val()) == "") {
                        $(this).val($(this).attr("title"));
                        $(this).removeClass("colorInput");
                    }
                    ;

                }
            }
    );
    function sendCode() {
        var from = $("#from").val();
        $.post("/member/editEmail/sendCode", {'from': from}, function (data) {
            if (data.indexOf("200") >= 0) {
                if (from == 'email')
                {
                    var emailExp = '{{$pageArray.email}}'.split('@');
                    if (emailExp[1] == 'gmail.com') {
                        var emailHost = 'http://www.' + emailExp[1];
                    } else {
                        var emailHost = 'http://mail.' + emailExp[1];
                    }
                    $('#verifyemailtip').removeClass('ts-error').html('<i></i>验证邮件已发送，<a  class="resgo" href="' + emailHost + '" target="_blank" class="email_url">去邮箱收件</a>');
                    $("#verifyemailtip").show();
                }
                $("#verifyemail .messCode").text("重新发送").css("display", "none");
                $("#verifyemail .messCode-dsib").css("display", "block");
                messageTimeNum = 60;
                messageTime("#verifyemail");

            } else if (data == '400.0') {
                $("#verifyemailtip").addClass('ts-error').html('<i></i>验证太频繁，请稍后再试！');
                $("#verifyemailtip").show();
            } else if (data == '500.0') {
                $("#verifyemailtip").addClass('ts-error').html('<i></i>服务器忙，请稍后再试！');
                $("#verifyemailtip").show();
            }
        });
    }
    function sendEditCode() {
        var email = $("#email").val();
        var verifycode = $("#verifycode").val();
        var from = $("#from").val();
        $.post("/member/editEmail/sendCode", {"email": AES.encrypt(email), "verifycode": verifycode, "from": from}, function (data) {
            if (data.indexOf("200") >= 0) {
                if (from == 'email' || from == 'phone') {
                    var emailExp = '{{$pageArray.email}}'.split('@');
                    if (emailExp[1] == 'gmail.com') {
                        var emailHost = 'http://www.' + emailExp[1];
                    } else {
                        var emailHost = 'http://mail.' + emailExp[1];
                    }
                    $('#editemailtip').removeClass('ts-error').html('<i></i>验证邮件已发送，<a  class="resgo" href="' + emailHost + '" target="_blank" class="email_url">去邮箱收件</a>');
                    $("#editemailtip").show();
                    $("#editemail .messCode").text("重新发送").css("display", "none");
                    $("#editemail .messCode-dsib").css("display", "block");
                    messageTimeNum = 60;
                    messageTime("#editemail");
                }
            } else if (data == '300') {
                $("#editemailtip").html('<i></i>请输入正确的邮箱地址！').addClass("ts-error").show();
            } else if (data == '301') {
                $("#editemailtip").html('<i></i>此邮箱已被绑定，请重新输入！').addClass("ts-error").show();
            } else if (data == '400') {
                $("#editemailtip").html('<i></i>验证的太频繁了，请稍后再试！').addClass("ts-error").show();
            } else if (data == '402') {
                $("#editemailtip").html('<i></i>此邮箱已在其他项目绑定，请重新输入！').addClass("ts-error").show();
            } else if (data == '500') {
                $("#editemailtip").html('<i></i>服务器忙，请稍后再试！').addClass("ts-error").show();
            }

        });
    }
    function messageTime(box) {
        $(box + " .messCode-dsib").text("重新发送（" + messageTimeNum + "s" + "）");
        messageTimeNum--;
        if (messageTimeNum >= 0) {
            setTimeout('messageTime("' + box + '")', 1000);
        } else {
            $(".messCode").css("display", "block");
            messageCodeTime.css("display", "none");
        }
        ;
    }
    ;
    $("#verifyemailsubmit").bind("click", function () {
        var codeErr = true;
        var code = $("#verifycode").val();
        var from = $("#from").val();
        if (code == "" || code == "请输入验证码") {
            $("#verifyemailtip").html('<i></i>请输入验证码！').addClass("ts-error").show();
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/member/editEmail/step3",
            async: false,
            data: "&from=" + from + "&code=" + code,
            timeout: 3000,
            success: function (data) {
                if (data == '200') {
                    codeErr = false;
                    $("#verifyemailtip").html('').hide();
                    $("#verifyemail").hide();
                    $("#editemail").show();
                } else if (data == '400') {
                    $("#verifyemailtip").html('<i></i>验证码错误，请重新输入').addClass("ts-error").show();
                }
            }
        });
        return !codeErr;
    });
    $("#editemailsubmit").bind("click", function () {
        var codeErr = true;
        var email = $("#email").val();
        var code = $("#code").val();
        var verifycode = $("#verifycode").val();
        if (code == "") {
            $("#editemailtip").html('<i></i>请输入验证码！').addClass("ts-error").show();
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/member/editEmail/step4",
            async: false,
            data: "&email=" + AES.encrypt(email) + "&code2=" + code,
            timeout: 3000,
            success: function (data) {
                if (data == '200') {
                    codeErr = false;
                    $("#editemail").hide();
                    $("#editsuccess").show();
                    var times = 3;
                    setInterval(function () {
                        if (times > 0) {
                            $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
                        } else {
                            window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
                        }
                        times--;
                    }, 1000)
                } else if (data == '400') {
                    $("#editemailtip").html('<i></i>验证码错误，请重新输入').addClass("ts-error").show();
                }
            }
        });
    });
    var emailErr = 1;
    function checkEmail() {
        var email = $("#email").val();
        if (!email) {
            emailErr = 1;
            return false;
        }
        if (!/^[_\.0-9a-z-]+@([0-9a-z][0-9a-z-]+\.)+[a-z]{2,4}$/.test(email)) {
            emailErr = 1;
            $("#editemailtip").addClass('ts-error').html('<i></i>请输入正确的邮箱地址！').show();
            return false;
        }
        $.ajax({
            url: "/api/check/jsonp",
            data: 'type=email&value=' + email,
            async: false,
            dataType: 'jsonp',
            jsonp: 'callback',
            success: function (response) {
                err = response;
                if (err == 1) {
                    emailErr = 1;
                    $("#editemailtip").addClass('ts-error').html('<i></i>请输入正确的邮箱地址！').show();
                    return false;
                } else if (err == 2) {
                    emailErr = 1;
                    $("#editemailtip").addClass('ts-error').html('<i></i>此邮箱已被绑定，请重新输入！').show();
                    return false;
                } else if (err == 3) {
                    emailErr = 1;
                    $("#editemailtip").addClass('ts-error').html('<i></i>此邮箱已被绑定，请重新输入！').show();
                    return false;
                }
                emailErr = 0;
            },
            error: function (jqXHR, textStatus, errorThrown) {
                emailErr = 1;
            },
            timeout: 3000
        });
    }
</script>
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
