<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台</title>
    <link rel="stylesheet" href="/style/vue/element/element-ui.css">
</head>
<body>
<div id="app">
    <el-container>
        <el-header style="padding: 10px;margin:0px 20px; background-color: rgb(225,230,244);">
            <el-row>
                <el-col :span="16">
                    <el-breadcrumb separator-class="el-icon-arrow-right" style="margin-top: 12px;margin-left: 20px">
                        <el-breadcrumb-item>首页</el-breadcrumb-item>
                        <el-breadcrumb-item>权限配置</el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
        </el-header>
        <el-main style="padding-top: 5px;margin: 0 10px">
            <el-tabs @tab-click="tabClick">
                <el-tab-pane label="用户" name="user">
                    <auth-user ref="ul"></auth-user>
                </el-tab-pane>
                <el-tab-pane label="组" name="group">
                    <auth-group ref="ug"></auth-group>
                </el-tab-pane>
                <el-tab-pane label="权限" name="auth">
                    <auth-list ref="ua"></auth-list>
                </el-tab-pane>
            </el-tabs>
        </el-main>
    </el-container>
</div>
</body>
<style>
    .el-form-item {
        margin-bottom: 5px;
    }
</style>
<script src="/style/vue/vue[|if $smarty.const.RUNMODE eq 'production'|].min[|/if|].js"></script>
<script src="/style/vue/components/vue-resource[|if $smarty.const.RUNMODE eq 'production'|].min[|/if|].js"></script>
<script src="/style/vue/element/element-ui.js"></script>
<!--<script src="/style/vue/global.js"></script>-->
<script>
    //原本global.js里面的内容 开始
    Promise.prototype.finally = function (callback) {
        var Promise = this.constructor;
        return this.then(
            function (value) {
                Promise.resolve(callback()).then(
                    function () {
                        return value;
                    }
                );
            },
            function (reason) {
                Promise.resolve(callback()).then(
                    function () {
                        throw reason;
                    }
                );
            }
        );
    };
    //原本global.js里面的内容 结束
</script>
<script src="/Admin/UserAuth/User"></script>
<script src="/Admin/UserAuth/List"></script>
<script src="/Admin/UserAuth/Group"></script>
<script>
    //默认application/json;改为x-www-form-urlencoded
    Vue.http.options.emulateJSON = true;
    let vm;
    vm = new Vue({
        el: '#app',
        components: {
            'auth-user': authUser,
            'auth-list': authList,
            'auth-group': authGroup,
        },
        data: function () {
            return {
                table: [],
                loading: false,
                showSql: false,
                showRepeat: false,
            }
        },
        methods: {
            pull: function () {
                this.fetch('/Admin/Monitor/Api?func=mail', {}).then(
                    d => {
                        this.table = JSON.parse(d.data);
                    }, (err) => this.$message.error(err)
                );
            },
            fetch: function ($url, $postArr) {
                return new Promise((resolve, reject) => {
                    this.$http.post($url, $postArr).then(res => {
                            if (res.data.err === 0) {
                                resolve(res.data);
                            } else if (res.data.msg) {
                                reject(res.data.err + '-' + res.data.msg)
                            } else {
                                reject(res.data.err)
                            }
                        }, res => reject(res.status)
                    );
                });
            },
            tabClick(tab) {
                switch (tab.name) {
                    case 'user':
                        this.$refs.ul.$emit('pull');
                        break;
                    case 'group':
                        this.$refs.ug.$emit('pull');
                        break;
                    case 'auth':
                        this.$refs.ua.$emit('pull');
                        break;
                }
            },
        },
        mounted: function () {
        }
    });




</script>
</html>
