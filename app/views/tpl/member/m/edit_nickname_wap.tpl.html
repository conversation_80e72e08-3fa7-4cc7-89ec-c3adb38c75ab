{{include file = "member/m/head.tpl.html"}}
    <header class="g-user-hd">
        <a href="javascript:;" class="g-user-back">返回</a>
        <h1 class="g-user-logo">2345用户中心</h1>
    </header>
    {{if $pageArray.success}}
    <article class="g-user-bd">
        <div class="g-user-result bind-sec">
            <h1><i></i>修改昵称成功</h1>
            <p id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a></p>
        </div>
    </article>
    <script type="text/javascript">
        var times = 3;
        setInterval(function() {
            if (times > 0) {
                $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
            } else {
                window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
            }
            times--;
        }, 1000);
    </script>
    {{else}}
    <article class="g-user-bd">
        <header>
            <h1 class="g-user-title">帐号信息</h1>
        </header>
        <form class="g-user-form" action="/member/editNickname" method="post" id="nicknameForm">
            <div class="form-item">
                <input type="text" class="form-ipt" name = "nickname" placeholder="昵称">
                <p class="form-tip-error" style="display: none"><i></i>昵称错误</p>
            </div>
            <div class="btn-group">
               <a id="addnickbtn" href="javascript:;" class="btn-confirm">确认修改</a>
            </div>
        </form>
    </article>
    {{include file = "public/footer_m.tpl.html"}}
    <script type="text/javascript">
    document.body.addEventListener('touchstart', function () {});
        $('#addnickbtn').on('click', function(e){
        $('#nicknameForm').submit();
    });
    </script>
    {{/if}}
</body>
</html>