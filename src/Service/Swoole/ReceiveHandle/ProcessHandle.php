<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/20
 * Time: 20:15
 */
namespace Service\Swoole\ReceiveHandle;

use Service\Swoole\Tool\Encoding;
use Service\Swoole\Tool\Format;
use Service\Swoole\Tool\Log;

class ProcessHandle
{

    /**
     * 接受内容
     * -
     * @param object $service 服务
     * @param object $fd 文件描述
     * @param string $reactor_id $reactor_id
     * @param string $data 数据
     * @return void
     */
    public function run($service, $fd, $reactor_id, $data)
    {
        $data = \EncodingAction::transcoding($data);
        if (!empty($data['Handle']))
        {
            $namespace = 'Service\Handle\\' . $data['Handle'];
            if (class_exists($namespace))
            {
                if (method_exists($namespace, 'run'))
                {
                    if (!empty($data['sync']))
                    {
                        \go(function () use ($service, $fd, $reactor_id, $data, $namespace) {
                            try
                            {
                                $responseData = json_decode((new $namespace())->run($data), true);
                                $service->send($fd, Format::returnJson($responseData['status'], $responseData['msg'], $responseData['data']));
                            }
                            catch (\Exception $exception)
                            {
                                $service->send($fd, Format::returnJson(-6, '同步异常', [$exception->getMessage()]));
                            }
                        });
                    }
                    else
                    {
                        $service->send($fd, Format::returnJson(1, '成功'));
                        $redirect_stdout = '';
                        $process = new \swoole_process(function (\swoole_process $worker) use ($service, $data, $namespace) {
                            \swoole_set_process_name("php smsSwooleService process");
                            try
                            {
                                $responseData = (new $namespace())->run($data);
                                if ($responseData['status'] != 1)
                                {
                                    Log::setLogMsg(json_encode(Encoding::transcoding($responseData, 'utf-8')), true, 'sendFail');
                                }
                                $worker->exit(0);
                            }
                            catch (\Exception $e)
                            {
                                $worker->exit(0);
                            }
                        }, $redirect_stdout);
                        $pid = $process->start();
                    }
                }
                else
                {
                    $service->send($fd, Format::returnJson(-7, 'Handle未实现'));
                }
            }
            else
            {
                $service->send($fd, Format::returnJson(-6, 'Handle不存在'));
            }
        }
        else
        {
            $service->send($fd, Format::returnJson(-3, '无处理脚本'));
        }
    }
}
