<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：AccountMonitor.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：10-14, 2016
 */

namespace Service\Monitor;

class AccountMonitor
{
    const RATE = 0.4;
    const AVG_MAX = 50;

    private $accountStatService;


    private function getAccountStatService()
    {
        if (!$this->accountStatService)
        {
            $this->accountStatService = new \Service\AccountStat\AccountStat();
        }
        return $this->accountStatService;
    }

    public function checkWarning($type, $source = "", $status = 1, $startTime = "", $endTime = "", $rate = self::RATE, $compare = \Service\Monitor\Monitor::CMP_ALL)
    {
        $minuteValues = $this->getAccountStatService()->getAmountForMinute($type, $source, $status, $startTime, $endTime);
        $originValues = array_values($minuteValues);
        $value = array_pop($originValues);
        $monitorService = new \Service\Monitor\Monitor();
        $res = $monitorService->checkLinearValue($originValues, $value, $rate, $compare);
        $info = $res["info"];
        $slope = $info["slope"];
        $offset = $info["offset"];
        $forecastValue = $info["forecastValue"];
        $max = $info["max"];
        $min = $info["min"];

        array_push($originValues, $value);
        $formatData = array();
        foreach ($originValues as $k => $v)
        {
            $formatData[] = array($k, $v);
        }

        $avg = $monitorService->avg($originValues);
        $data = array(
            "flag" => $res["flag"],
            "isWarning" => $res["flag"] ? "OK" : "ERROR",
            "source" => $source ? $source : "all the others",
            "status" => $status == \Service\AccountStat\Consts\AccountStat::STATUS_SUCC ? "succ" : "fail",
            "origin" => $originValues,
            "avg" => round($avg, 2),
            "data" => $formatData,
            "slope" => round($slope, 2),
            "offset" => round($offset, 2),
            "forecastValue" => round($forecastValue, 2),
            "newValue" => $value,
            "max" => round($max, 2),
            "min" => round($min, 2),
            "yMin" => min($originValues) < 0 ? min($originValues) : 0,
            "yMax" => max($originValues) * 1.3,
            "xMin" => min(array_keys($originValues)),
            "xMax" => max(array_keys($originValues)),
            "startTime" => $startTime,
            "endTime" => $endTime,
        );
        return $data;
    }

    /**
     * cron  报警
     *
     * @param $type
     * @param $source
     * @param $status
     * @param $startTime
     * @param $endTime
     * @param $rate
     * @param int $avgMax
     * @param $compare
     *
     * @return string
     */
    public function msgWarning($type, $source, $status, $startTime, $endTime, $rate, $avgMax = self::AVG_MAX, $compare = \Service\Monitor\Monitor::CMP_ALL)
    {
        $msg = "";
        $data = $this->checkWarning($type, $source, $status, $startTime, $endTime, $rate, $compare);
        if ($data["avg"] > $avgMax && $data["flag"] == false)
        {
            $msg .= "告警消息:" . ($data["newValue"] > $data["forecastValue"] ? "值大于预测范围" : "值小于预测范围") . "<BR>";
            $msg .= "类型:" . \Service\AccountStat\Consts\AccountStat::getTypeName($type) . \Service\AccountStat\Consts\AccountStat::getStatusName($status) . "<BR><BR>";
            $msg .= "来源:" . ($source ? $source : "全部(除game.2345.com)") . "<BR>";
            $msg .= "取样时间:" . $startTime . " ~ " . $endTime . "<BR>";
            $msg .= "取样点:[" . implode(",", $data["origin"]) . "]<BR><BR>";
            $msg .= "预测值:" . $data["forecastValue"] . "<BR>";
            $msg .= "预测比例:" . $rate . "<BR>";
            $msg .= "预测值范围:[" . $data["min"] . "," . $data["max"] . "]<BR>";
            $msg .= "实际值:" . $data["newValue"] . "<BR>";
            $msg .= "函数: y=" . $data["slope"] . "x + " . $data["offset"] . " <BR> <BR> <BR>";
        }
        return $msg;
    }


}