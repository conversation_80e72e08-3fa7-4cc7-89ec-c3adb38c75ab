<?php

namespace Admin;
class SmsProjectModel extends \Model
{
    /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    public function getPojectList($data = array(), $limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(pid) as total ';
        }
        $sql = "SELECT {$col} FROM `project_list` ";

        if (isset($data['pid']))
        {
            $where .= " pid = :pid AND";
            $paramArr[':pid'] = $data['pid'];
        }
        if (isset($data['project_name']))
        {
            $where .= " project_name = :project_name AND";
            $paramArr[':project_name'] = $data['project_name'];
        }

        if (!empty($data['projectName']))
        {
            $where .= " project_name like :projectName AND";
            $paramArr[':projectName'] = "%{$data['projectName']}%";
        }
        
        if (isset($data['status']))
        {
            $where .= " status = :status AND";
            $paramArr[':status'] = $data['status'];
        }
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by pid desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    public function getPojectPositionList($data)
    {
        $where = '';
        $paramArr = array();
        $sql = "SELECT * FROM `position_list` ";

        if (isset($data['pid']))
        {
            $where .= " pid = :pid ";
            $paramArr[':pid'] = $data['pid'];
        }
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' ORDER BY pl_id ASC';
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    public function getPojectSmsTypeList($data)
    {
        $where = '';
        $paramArr = array();
        $sql = "SELECT * FROM `project_type_list` ";

        if (isset($data['pid']))
        {
            $where .= " pid = :pid ";
            $paramArr[':pid'] = $data['pid'];
        }
        if (isset($data['tid']))
        {
            $where .= " tid = :tid ";
            $paramArr[':tid'] = $data['tid'];
        }
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' order by tid asc';
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }

    public function addProjectInfo($data)
    {
        $inserData = array(
            'project_name' => $data['projectName'],
            'oa_product_id' => $data['oa_product_id'],
            'create_user_id' => $data['createUid'],
            'add_time' => date('Y-m-d H:i:s'),
            'letter' => $data['letter']
            );
        $isTrue = $this->pdo->insert('project_list', $inserData);
        return $isTrue !== false ? true : false;
    }

    public function addPositionInfo($data)
    {
        $inserData = array(
            'position_name' => $data['position_name'],
            'pid' => $data['pid'],
            'add_time' => date('Y-m-d H:i:s'),
            );
        $isTrue = $this->pdo->insert('position_list', $inserData);
        return $isTrue !== false ? true : false;
    }

    public function addProjectSmsType($data)
    {
        $inserData = array(
            'pid' => $data['pid'],
            'tid' => $data['tid'],
            'add_time' => date('Y-m-d H:i:s'),
            );
        $isTrue = $this->pdo->insert('project_type_list', $inserData);
        return $isTrue !== false ? true : false;
    }

    /**
     * 更新用户信息
     * */
    public function upPostionInfo($data)
    {
        $condition = array('where' => ' pl_id = :pl_id ', 'params' => array(':pl_id' =>
                    $data['pl_id']));
        $updata = array('add_time' => date('Y-m-d H:i:s'));
        if ($data['position_name'])
        {
            $updata['position_name'] = $data['position_name'];
        }

        $isTrue = $this->pdo->update('position_list', $updata, $condition);
        return $isTrue !== false ? true : false;
    }


    /**
     * 更新用户信息
     * */
    public function upProjectInfo($data)
    {
        $condition = array('where' => ' pid = :pid ', 'params' => array(':pid' => $data['pid']));
        $updata = array('add_time' => date('Y-m-d H:i:s'));
        if ($data['project_name'])
        {
            $updata['project_name'] = $data['project_name'];
        }
        if ( !empty($data['letter']) )
        {
            $updata['letter'] = $data['letter'];
        }
        if (!empty($data['status']))
        {
            $updata['status'] = $data['status'];
        }
        $updata['oa_product_id'] = $data['oa_product_id'];
        $isTrue = $this->pdo->update('project_list', $updata, $condition);
        return $isTrue !== false ? true : false;
    }


    public function setProjectInfo($data)
    {
        $act = '';
        $this->pdo->beginTransaction();
        $data['projectName'] = \EncodingAction::transcoding($data['projectName']);
        $queryProject = array('row' => true);
        if (empty($data['projectId']))
        {
            $queryProject['project_name'] = $data['projectName'];
        }
        else
        {
            $this->upProjectInfo(array('pid' => $data['projectId'], 'project_name' => $data['projectName'], 'letter' => $data['letter'], 'oa_product_id' => $data['oa_product_id']));
            $queryProject['pid'] = $data['projectId'];
        }

        $getProject = $this->getPojectList($queryProject);
        if (empty($getProject))
        {
            $isTrue = $this->addProjectInfo($data);
            $projectId = $this->pdo->lastInsertId();

            $act = 'add';
        }
        else
        {
            $projectId = $getProject['pid'];
            $isTrue = true;
            $act = 'edit';
        }

        if ($isTrue !== false)
        {
            $posEditArr = array();
            if (empty($data['positionName']))
            {
                $data['positionName'] = array();
                $sql = 'delete from position_list where pid=:pid';
                $isTrue = $this->pdo->query($sql, array(':pid' => $projectId));
                        
            }
            else
            {
                foreach ($data['positionName'] as $positionNameInfo)
                {
                    if (empty($positionNameInfo))
                    {
                        continue;
                    }
                    if ($act == 'edit')
                    {
                        $positionI = explode(',', $positionNameInfo);
                        if (isset($positionI['0']) && isset($positionI['1']))
                        {
                            $posEditArr[$positionI['0']] = $positionI['1'];
                        }
                        else
                        {
                            $positionArr = array('position_name' => \EncodingAction::transcoding($positionNameInfo),
                                    'pid' => $projectId);
                            $isTrue = $this->addPositionInfo($positionArr);
                            if ($isTrue === false)
                            {
                                continue;
                            }
                            $newId = $this->pdo->lastInsertId();
                            $posEditArr[$newId] = $positionArr['position_name'];
                        }

                    }
                    else
                    {
                        $positionArr = array('position_name' => \EncodingAction::transcoding($positionNameInfo),
                                'pid' => $projectId);
                        $isTrue = $this->addPositionInfo($positionArr);
                        if ($isTrue === false)
                        {
                            continue;
                        }
                    }

                }
                if ($act == 'edit' && !empty($posEditArr))
                {
                    $posEditKeys = array_keys($posEditArr);
                    $posEditKeysStr = implode(',', $posEditKeys);
                    if (!empty($posEditKeysStr))
                    {
                        $sql = 'delete from position_list where pid=:pid and pl_id not in (' . $posEditKeysStr .
                            ') ';
                        $isTrue = $this->pdo->query($sql, array(':pid' => $projectId));
                    }

                    if ($isTrue !== false)
                    {
                        foreach ($posEditArr as $posEditArrKey => $posEditArrInfo)
                        {
                            $isTrue = $this->upPostionInfo(array('position_name' => \EncodingAction::
                                    transcoding($posEditArrInfo), 'pl_id' => $posEditArrKey));
                            if ($isTrue === false)
                            {
                                continue;
                            }
                        }

                    }

                }
            }

        }

        if ($isTrue !== false)
        {
            if (!empty($data['smsTypeId']))
            {
                $newSmsTypeStr = implode(',', $data['smsTypeId']);
            }
            else
            {
                $newSmsTypeStr = '';
                $data['smsTypeId'] = array();
            }

            $dbSmsList = $this->getPojectSmsTypeList(array('pid' => $projectId));
            if (empty($dbSmsList))
            {
                foreach ($data['smsTypeId'] as $smsTypeId)
                {
                    $smsTypeArr = array('pid' => $projectId, 'tid' => $smsTypeId);
                    $isTrue = $this->addProjectSmsType($smsTypeArr);
                    if ($isTrue === false)
                    {
                        continue;
                    }
                }
            }
            else
            {

                $typeSql = 'delete from project_type_list where pid=:pid';
                if (!empty($newSmsTypeStr))
                {
                    $typeSql .= ' and tid not in (' . $newSmsTypeStr . ') ';
                }

                $isTrue = $this->pdo->query($typeSql, array(':pid' => $projectId));
                if ($isTrue !== false)
                {

                    $dbSmsArr = array();
                    foreach ($dbSmsList as $dbSmsInfo)
                    {
                        $dbSmsArr[] = $dbSmsInfo['tid'];
                    }
                    $nowAuthIds = array_diff($data['smsTypeId'], $dbSmsArr);

                    foreach ($nowAuthIds as $nowAuthIdInfo)
                    {
                        $smsTypeArr = array('pid' => $projectId, 'tid' => $nowAuthIdInfo);
                        $isTrue = $this->addProjectSmsType($smsTypeArr);
                        if ($isTrue === false)
                        {
                            continue;
                        }
                    }
                }
            }


        }


        if ($isTrue !== false)
        {
            //设置项目分配类型
            $SetRedisAction = SetRedisAction::getInstance();
            $isTrue = $SetRedisAction->setProjectType($projectId);
        }
        if ($isTrue !== false)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }

    }
    /*
    public function delProject ()
    {
    $sql = 'delete from project_list where pid in (27)';
    $isTrue = $this->pdo->query($sql, array());
    }*/

}
