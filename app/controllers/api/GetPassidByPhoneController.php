<?php

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:GetPassidByPhone.php
 * 摘    要:
 * 作    者:<EMAIL>
 * 修改日期: 2016/3/9
 */
class GetPassidByPhoneController extends Controller
{
    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }

        $phone = $_POST['phone'];
        $memberModel = loadModel('member');
        $info = $memberModel->getPassidByPhone($phone);
        echo serialize($info);
    }
}