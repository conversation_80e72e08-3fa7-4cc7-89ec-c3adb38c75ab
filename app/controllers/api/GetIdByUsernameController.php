<?php

class GetIdByUsernameController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }
        if ($_REQUEST && isset($_REQUEST['username']))
        {
            if (!isset($_REQUEST['a']))
            {
                $fuzzy = true;
            }
            else
            {
                $fuzzy = false;
            }
            $username = filter_var($_REQUEST['username'], FILTER_SANITIZE_STRIPPED);
            $memberModel = loadModel('member');
            $ids = $memberModel->GetIdsByUsername($username, $fuzzy);
            echo implode(',', $ids);
        }
    }

}