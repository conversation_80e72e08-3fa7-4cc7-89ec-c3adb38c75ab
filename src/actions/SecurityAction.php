<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：SecurityAction.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：27/09/2018 09:54
 */

use jichupingtai\crypto\Crypto;

class SecurityAction extends Action
{
    const INFO_TYPE_PHONE = 1;
    const INFO_TYPE_VERIFY_CODE = 2;

    /**
     * User: panj
     * 对手机号，短信验证码等其它敏感信息进行脱感处理
     *
     * @param string $info 源信息
     * @param int    $type 源信息类型
     *
     * @return string
     */
    public static function desensitize($info, $type = self::INFO_TYPE_PHONE)
    {
        $security = '';
        switch ($type)
        {
            case self::INFO_TYPE_PHONE:
                $security = substr($info, 0, 3) . '****' . substr($info, - 4);
                break;
            case self::INFO_TYPE_VERIFY_CODE:
                $codeMid = (int)(strlen($info) / 2) - 1;
                $security = substr($info, 0, $codeMid) . '**' . substr($info, - 2);
                break;
        }

        return $security;
    }

    /**
     * User: panj
     * 从短信内容中提取出4至6位数的验证码
     *
     * @param string $sms sms content
     *
     * @return bool
     */
    public static function getVerifyCodeFromText($sms)
    {
        if (strpos($sms, '验证码') !== false || strpos($sms, '校验码') !== false)
        {
            //短信内容文案没有统一的模板，此方法提取的为含'验证码'，4到6个连续的数字。有可能误提到其它的数字内容，看情况进行优化
            $pattern = '/\d{4,6}[\,\，\|\.\。]/i';
            if (!preg_match($pattern, $sms, $code))
            {
                $pattern = '/\d{4,6}/i';

                return preg_match($pattern, $sms, $code) ? $code[0] : false;
            }

            return (int)$code[0];
        }

        return false;
    }

    /**
     * User: panj
     * @return string
     */
    protected static function getCryptoKey()
    {
        $cryptoConfig = Config::get('crypto');
        return $cryptoConfig['key'];
    }

    /**
     * User: panj
     * 对字符串进行加密
     * @param string $source source text
     *
     * @return string
     */
    public static function encrypt($source)
    {
        return Crypto::encrypt($source, self::getCryptoKey());
    }

    /**
     * User: panj
     * 对字符串进行解密
     * @param string $encrypted encrypted text
     *
     * @return string
     */
    public static function decrypt($encrypted)
    {
        return Crypto::decrypt($encrypted, self::getCryptoKey());
    }
}
