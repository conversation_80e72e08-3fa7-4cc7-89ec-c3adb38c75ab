<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2021/1/13
 * Time: 15:33
 */
use Service\Report\LSessionReport;
use WebLogger\Facade\LoggerFacade;

includeBase('CronController');

class LSessionBakController extends CronController
{
    /**
     * 将7台机器上的日志收集到有序集合中进行排序。
     * @param $h_i string
     */
    public function actionIndex($h_i = "")
    {
        LSessionReport::EchoLog("脚本开始");
        if (!empty($h_i)) {
            $logPath = $h_i . ".log";
        } else {
            $m = strtotime(date("Y-m-d H:i", time())) - 60;
            $logPath = date('H_i', $m) . ".log";
        }
        $fileName = LSessionReport::$logRoot . "/" . date("Ymd") . "/LSessionReport/" . $logPath;
        LSessionReport::EchoLog("开始读取:" . $fileName);
        LSessionReport::ReadLogToRedis($fileName);
        $goFilePath = "/opt/case/passport.2345.com/Logs/" . date("Ymd") . "/" . $logPath;
        LSessionReport::EchoLog("开始读取:" . $goFilePath);
        LSessionReport::ReadLogToRedis($goFilePath);
        LSessionReport::EchoLog("脚本结束");
    }

    /**
     * 回写单例
     */
    public function actionWriteBack()
    {
        LSessionReport::EchoLog("脚本开始");
        $redoNums = [];
        $nowTime = time();
        while (time() - $nowTime <= 60) {
            try {
                $list = LSessionReport::GetLSessionList(0, time() - 60);
                if (empty($list) || !is_array($list)) {
                    LSessionReport::EchoLog("无数据脚本等待1s");
                    sleep(1);
                    continue;
                }
                foreach ($list as $info => $score) {
                    if ($redoNums[$info] > 3) {
                        //3次删除失败，就不再删除
                        LSessionReport::EchoLog("数据清除失败3次，" . $info);
                        continue;
                    }
                    $isClear = LSessionReport::ClearSessionListKey($info); // 使用的时候删掉原来集合里面的数据
                    if ($isClear === false) {
                        LoggerFacade::error("loginSession:清除集合元素失败", $info);
                        LSessionReport::EchoLog("数据清除失败，" . $info);
                        $redoNums[$info] += 1;
                        continue;
                    }
                    list($cmd, $key, $sid, $time) = explode(",", $info);
                    $isWrite = LSessionReport::WriteBack($cmd, $key, $sid, $time);
                    if ($isWrite === false) {
                        LSessionReport::EchoLog("登录信息回写失败，" . $info);
                        LoggerFacade::error("loginSession:登录信息回写失败", $info);
                    }
                }
            }
            catch (\Exception $exception) {
                LoggerFacade::error("loginSession:redis异常", $exception->getTraceAsString());
                LSessionReport::EchoLog("redis异常，" . $exception->getTraceAsString());
            }
        }
        LSessionReport::EchoLog("脚本结束");
    }
}
