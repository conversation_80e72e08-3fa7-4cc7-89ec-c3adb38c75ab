<?php

namespace Admin;
use Controller;
class ClearLimitController extends Controller
{
    /**
     * 短信发送缓存列表
     * */
    public function actionIndex()
    {
        $phone = $this->Filter->getPost('phone');

        $pageArray['phone'] = $phone;
        $SmsTypeModel = SmsTypeModel::getInstance();
        $smsTypeList = $SmsTypeModel->getSmsTypeList();
        $GetRedisAction = \GetRedisAction::getInstance();
        $currDate = str_replace('-', '_', date('Y-m-d'));
        $redisArr = array();
        foreach ($smsTypeList as $smsTypeInfo)
        {
            $keyWord = \KeyAction::getSendTypeNumsKey($phone, $currDate, $smsTypeInfo['sms_type']);
            $keyValue = $GetRedisAction->queryKeysInfo($keyWord);
            if ($keyValue === '0' || !empty($keyValue))
            {
                $redisArr[] = array(
                    'phone' => $phone,
                    'smsType' => $smsTypeInfo['sms_type'],
                    'nums' => $keyValue,
                    'queryKey' => $keyWord,
                    'smsTypeName' => $smsTypeInfo['sms_name']);
            }

        }
        $pageArray['redisArr'] = $redisArr;
        loadView('Admin/clean_limit_index.tpl.html',$pageArray);

    }
    
    /**
     * 取消日限
     * */
    public function actionClearTodayLimit ()
    {
        $keyword = $this->Filter->getPost('keyword');
        if (empty ($keyword))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '关键字不能为空'));
        }
        $GetRedisAction = \GetRedisAction::getInstance();
        $isTrue = $GetRedisAction->clearTodayLimit($keyword);
        if ( $isTrue )
        { 
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '清除成功'));
        }
        else
        {
            return \MsgAction::returnJsonData(array('status' => 'P00031', 'msg' => '清除失败'));
        }
    }
    
    /**
     * 查看类型下具体项目发送量
     * */
    public function actionGetProjectSendTotal()
    {
        $smsType =  $this->Filter->getPost('smsType');
        $phone =  $this->Filter->getPost('phone');
        $ClearLimitModel = ClearLimitModel::getInstance();
        $LimitList = $ClearLimitModel->getTodayProjectSendTotal($phone,$smsType);
        $str = '<table class="tabFromMod" cellpadding="0" cellspacing="0">';
        $str .= "<tr><td>项目名称</td><td>发送量</td></tr>";
        foreach($LimitList as $LimitListInfo)
        {
            $str .= '<tr><td>'.$LimitListInfo['project_name'].'</td><td>'.$LimitListInfo['total'].'</td></tr>';
        }
        $str .= '</table>';
        return \MsgAction::returnJsonData(array('status' => 'P00001', 'html' => $str));
    }
}
