<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/4/16
 * Time: 11:07
 */

namespace Service\Capacity\Config;

class EmailRepeatPassId
{
    private static $data = [];

    /**
     * 检查是否是重复绑定邮箱
     * @param int $passId passid
     * @return bool
     */
    public static function check($passId)
    {
        if (empty(self::$data)) {
            $filePath = APPPATH . '/lib/Service/Capacity/Config/ignore/email.log';
            $handler = fopen($filePath, "r");
            while (!feof($handler)) {
                self::$data[] = trim(fgets($handler));
            }
            fclose($handler);
        }
        if (in_array($passId, self::$data)) {
            return true;
        } else {
            return false;
        }
    }
}
