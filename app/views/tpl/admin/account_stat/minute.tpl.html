<div class="mainWrap">

    <div class="location">位置：<a href="#" target="_blank">登录用户统计</a> &gt; <span>每日登录数表</span></div>

    <div class="TabMenu mt10">
        <a class="{{if $pageArray.clientTypeField eq 'web'}}currTabMenu{{/if}}"
           href="/houtai_gl/account_stat/minute/{{$pageArray.typeField}}/web">网页端数据</a>
        <a class="{{if $pageArray.clientTypeField eq 'app'}}currTabMenu{{/if}}"
           href="/houtai_gl/account_stat/minute/{{$pageArray.typeField}}/app">移动端数据</a>
    </div>
    <div class="selectTab mt10 clearfix">
        <a href="/houtai_gl/account_stat/day/{{$pageArray.typeField}}/{{$pageArray.clientTypeField}}/" class="">每日{{if
            $pageArray.typeField eq 'login'}}登录{{else}}注册{{/if}}状态数表</a><i
            class="iLine"></i>
        <a href="/houtai_gl/account_stat/minute/{{$pageArray.typeField}}/{{$pageArray.clientTypeField}}/" class="cur">每3分钟{{if
            $pageArray.typeField eq 'login'}}登录{{else}}注册{{/if}}状态数表</a><i
            class="iLine"></i>
        <a href="/houtai_gl/account_stat/detail/{{$pageArray.typeField}}/{{$pageArray.clientTypeField}}/" class="">{{if
            $pageArray.typeField eq 'login'}}登录{{else}}注册{{/if}}帐号类型数数据表</a>
    </div>

    <div id="container" style="min-width: 400px; height: 400px; margin: 0 auto"></div>

    <div>
        {{$pageArray.title}}：<span style="color:blue;">蓝色</span>代表登录成功，<span style="color:red;">红色</span>代表登录失败
        <a target="_blank" href="?export=1">导出excel表</a>
    </div>

    <table cellspacing="0" cellpadding="0" {{if $pageArray.clientTypeField eq 'app'}} style="width:1300px" {{else}} style="width:1600px"{{/if}}  class="tabMod whiteTab forFixed">
        <tr style="text-align: center;">
            <th width="80" rowspan="2">日期</th>
            {{foreach item=domains key=groupKey from=$pageArray.domainGroups name=domainGroups}}
            <th
                    {{if $smarty.foreach.domainGroups.index % 2== 0}} style="background-color:#d6e4f7" {{
            /if}}
            colspan="{{$domains|@count}}">{{$groupKey}}</th>
            {{/foreach}}
        </tr>
        <tr style="text-align: center;">
            {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
            {{foreach item=domain_name key=domain from=$domains}}
            <th {{if $smarty.foreach.domainGroups.index % 2== 0}} style="background-color:#d6e4f7" {{
            /if}}>
            <span title="{{$domain}}">{{if $domain_name}}{{$domain_name}}{{else}}{{$domain}}{{/if}}</span>
            <input type="button" onclick="draw('{{$domain}}')" value="图表"/>
            </th>
            {{/foreach}}
            {{/foreach}}
        </tr>
        {{foreach item=item key=date from=$pageArray.data}}
        <tr style="text-align: center">
            <td rowspan="2">{{$date}}</td>
            {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
            {{foreach item=domain_name key=domain from=$domains}}
            <td {{if $smarty.foreach.domainGroups.index % 2== 0}} style="background-color:#d6e4f7" {{
            /if}}>
            <span style="color:blue;">{{$item.succ.$domain|default:0}}</span>
            </td>
            {{/foreach}}
            {{/foreach}}
        </tr>

        <tr style="text-align: center">
            {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
            {{foreach item=domain_name key=domain from=$domains}}
            <td {{if $smarty.foreach.domainGroups.index % 2== 0}} style="background-color:#d6e4f7" {{
            /if}}>
            <span style="color:red;">{{$item.fail.$domain|default:0}}</span>
            </td>
            {{/foreach}}
            {{/foreach}}
        </tr>
        {{/foreach}}
    </table>

</div>

<script type="text/javascript" src="/js/lib/echartsV3/echarts.min.js"></script>
<script type="text/javascript">

    url = "/houtai_gl/account_stat_api/minute/{{$pageArray.typeField}}/{{$pageArray.clientTypeField}}/";
    $('.navList a').each(function () {
        if ($(this).attr('href') == '/houtai_gl/account_stat/day/{{$pageArray.typeField}}/') {
            $(this).addClass('select');
        }
    });
</script>
<script type="text/javascript" src="/js/houtai_gl/account_stat/account_stat_chart.js?version=************"></script>
