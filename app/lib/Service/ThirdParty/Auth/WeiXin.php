<?php

namespace Service\ThirdParty\Auth;

use Service\Encryption\Aes\AesManager;
use Service\AccountStat\Consts\AccountStat;

class WeiXin extends Base
{
    /**
     * 功  能：登录
     *
     * @param string $mid mid
     * @param string $appid appid
     * @param string $wxCode wxCode
     * @return array
     */
    public static function Login($mid, $appid, $wxCode)
    {
        $oauthAction = loadAction('oauth');
        $userBaseAction = loadAction('UserBase');
        /** @see OauthAction::weixinCallback() */
        $result = $oauthAction->weixinCallback($wxCode, $appid);
        //微信是否授权
        if ($result) {
            //注意这里需要使用unionid, 同一个开发者账号旗下不同的app
            $openid = $result['unionid'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'weixin');
            $passid = !empty($return["passid"]) ? $return["passid"] : '';

            $ccConfig = loadAction('UnionLoginRedis')->getLoginConfig($mid, 'weixin');
            $warningAction = loadAction('Warning');
            if ($ccConfig['need_bind_phone']) {//云控-强绑
                if ($passid) {
                    $userInfo = $memberModel->read($passid);
                    //微信已经绑定了，没有绑定手机号码
                    if (empty($userInfo['phone'])) {
                        return [
                            "code" => 6001,
                            "data" => [
                                'source' => '',
                                'bindPhone' => $ccConfig['need_shanyan'] ? "shanyan" : "phone",
                                'info' => (new AesManager())->aes128cbcEncrypt(
                                    json_encode([
                                        'passid' => $passid,
                                        'nickname' => $result['nicknameUtf8'],
                                        'figureurl' => $result['figureurl'],
                                        'nicknameUtf8' => $result['nicknameUtf8'],
                                    ])
                                )
                            ],
                            'msg' => '登录失败，请重新再试',
                            'code_real' => 2100018,
                        ];
                    } else {
                        \Service\AccountStat\AccountStat::collect(
                            AccountStat::TP_LOGIN,
                            AccountStat::CTP_APP,
                            AccountStat::AC_TP_OAUTH_WEIXIN,
                            true,
                            $mid
                        );
                        //微信绑定 手机号码绑定 直接登录
                        $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                        $userInfo["m_uid"] = unserialize($userInfo['m_uid']);
                        $uid = $userInfo['m_uid']['1'];
                        $username = $userInfo['username'];
                        $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;
                        $loginAction = loadAction('login');
                        $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                        $userBaseAction->updateThirdAvatarNicknameInfo($mid, $passid, $result, 'weixin');
                        $warningAction->setRequestInfo(['passid' => $passid, 'login_time' => time()]);
                        return [
                            "code" => 200,
                            "data" => [
                                'source' => encryptCookieIByAes128cbc($cookie['I']),
                                'bindPhone' => '',
                                'login_type' => 1
                            ],
                            'msg' => '登录成功！',
                        ];
                    }
                } else {
                    // 提示绑定
                    $data = json_encode([
                        'id' => $openid,
                        'gender' => $result['gender'],
                        'nickname' => $result['nicknameUtf8'],
                        'figureurl' => $result['figureurl'],
                        'nicknameUtf8' => $result['nicknameUtf8'],
                    ]);
                    return [
                        "code" => 6002,
                        "data" => [
                            'source' => '',
                            'bindPhone' => $ccConfig['need_shanyan'] ? "shanyan" : "phone",
                            'info' => (new AesManager())->aes128cbcEncrypt($data),
                        ],
                        'msg' => '登录失败，请重新再试',
                        'code_real' => 2100018,
                    ];
                }
            } else {
                if (!$passid) {
                    loadAction('uploadToKafka')->addData(['event_type' => AccountStat::TP_REG]);
                    $nickname = !empty($result['nickname']) ? $result['nickname'] : '';
                    $nickname = \EncodingAction::transcoding($nickname, 'gbk');
                    $regResult = $memberModel->regOAuth(
                        'weixin',
                        $openid,
                        $nickname,
                        get_client_ip(),
                        '',
                        $result['gender']
                    );
                    if ($regResult === false) {
                        \Service\AccountStat\AccountStat::collect(
                            AccountStat::TP_REG,
                            AccountStat::CTP_APP,
                            AccountStat::AC_TP_OAUTH_WEIXIN,
                            false,
                            $mid
                        );
                        return [
                            "code" => 6010,
                            "data" => '',
                            'msg' => '登录失败，请重新再试',
                            'code_real' => 2100019,
                        ];
                    }
                    $passid = $regResult['passid'];
                    $uid = $regResult['uid'];
                    $username = $regResult['username'];
                    $userMod = $regResult['gid'] % 100 == 0 ? $regResult['gid'] : 0;

                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_REG,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_WEIXIN,
                        true,
                        $mid
                    );
                    $loginType = 2;
                    $warningAction->setRequestInfo(['passid' => $passid, 'reg_time' => time(), 'login_time' => time()]);
                } else {
                    $userInfo = $memberModel->read($passid);
                    $userInfo["m_uid"] = unserialize($userInfo['m_uid']);
                    $uid = $userInfo['m_uid']['1'];
                    $username = $userInfo['username'];
                    $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;

                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_LOGIN,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_WEIXIN,
                        true,
                        $mid
                    );
                    $loginType = 1;
                    $warningAction->setRequestInfo(['passid' => $passid, 'login_time' => time()]);
                }
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $loginAction = loadAction('login');
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                $userBaseAction->updateThirdAvatarNicknameInfo($mid, $passid, $result, 'weixin');
                return [
                    "code" => 200,
                    "data" => [
                        'source' => encryptCookieIByAes128cbc($cookie['I']),
                        'bindPhone' => '',
                        'login_type' => $loginType
                    ],
                    'msg' => '登录成功！',
                ];
            }
        } else {
            return [
                "code" => 201,
                "data" => '',
                'msg' => '登录失败，请重新再试',
                'code_real' => 2100020,
            ];
        }
    }
}
