<?php
use Service\ThirdParty\Auth\NECaptchaVerifier;
use Service\ThirdParty\Auth\SecretPair;

class NECheckLoginAction extends Action
{
    public $showCodeLimitCount;
    public $IP;

    /**
     * CheckLoginAction constructor.
     */
    public function __construct()
    {
        $this->IP = get_client_ip();
    }

    public function initLoginInfo() {
        $now = time();
        $fLoginInfo['loginInit'] = $now;
        $fLoginInfo['token'] = md5(rand(1, 999999) . $now);
        $fLoginInfo['userNameField'] = $this->getRandUserName();

        return $fLoginInfo;
    }

    /**
     * getRandUserName
     * -
     * @return string
     * <AUTHOR>
     */
    private function getRandUserName()
    {
        $result = '';
        $length = mt_rand(5, 9);
        $charStr = '123546798abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charLength = strlen($charStr);
        for ($i = 0; $i < $length; $i++)
        {
            $result .= $charStr[mt_rand(0, $charLength - 1)];
        }
        return $result;
    }

    /**
     * getFLoginSessionInfo
     * -
     * @return array
     * <AUTHOR>
     */
    public function getFLoginSessionInfo()
    {
        $fLoginSessionInfo = array();
        if (isset($_SESSION[FLOGIN_SESSION_KEY]))
        {
            $fLoginSessionInfo = unserialize($_SESSION[FLOGIN_SESSION_KEY]);
        }
        return $fLoginSessionInfo;
    }

    /**
     * 将fLoginInfo和post中的相关数据合并成字符串用于记录日志用
     * -
     * @return string
     * <AUTHOR>
     */
    public function fLoginInfoToStr()
    {
        $result = 'fLoginInfo:';
        $userNameField = '';
        $fLoginSessionInfo = $this->getFLoginSessionInfo();
        if (count($fLoginSessionInfo) > 0)
        {
            $result .= implode(',', $fLoginSessionInfo) . ',';
            $userNameField = isset($fLoginSessionInfo['userNameField']) ? $fLoginSessionInfo['userNameField'] : '';
            if (!empty($userNameField))
            {
                $result .= 'userNameField:' . $userNameField;
            }
            else
            {
                $result .= 'userNameField:n';
            }
        }
        $result .= '; postData:';
        $result .= 'check_code:' . (isset($_POST['check_code']) ? $_POST['check_code'] : 'n') . ',';
        $result .= 'flToken:' . (isset($_POST['flToken']) ? $_POST['flToken'] : 'n') . ',';
        if (!empty($userNameField))
        {
            $result .= 'userNameField:' . $userNameField . ';';
        }
        else
        {
            $result .= 'userNameField:n';
        }
        return $result;
    }

    /**
     * checkFLoginInfoV2
     * -
     * @param array $postData PostData
     * @param string $errInfoStr ErrInfoStr
     * @return bool
     * <AUTHOR>
     */
    public function checkFLoginInfo($postData, &$errInfoStr)
    {
        $errInfoStr = '';
        $checkResult = true;
        $fLoginSessionInfo = $this->getFLoginSessionInfo();
        $fieldArray = array(
            'token',
            'loginInit',
            'userNameField',
        );

        foreach ($fieldArray as $key => $fName)
        {
            if (!isset($fLoginSessionInfo[$fName]) || empty($fLoginSessionInfo[$fName]))
            {
                $errInfoStr = $fName . '没有-服务端';
                return false;
            }
        }

        if ($checkResult)
        {
            $userNameField = $fLoginSessionInfo['userNameField'];
            if (!isset($postData['flToken']) || empty($postData['flToken']) || $postData['flToken'] != md5($fLoginSessionInfo['token']))
            {
                if (!isset($postData['flToken']) || empty($postData['flToken']))
                {
                    $errInfoStr = '缺少token参数或为空';
                }
                elseif ($postData['flToken'] != md5($fLoginSessionInfo['token']))
                {
                    $errInfoStr = 'token不一致';
                }
                return false;
            }
            elseif (!isset($postData[$userNameField]) || empty($postData[$userNameField]))
            {
                $errInfoStr = '缺少用户名字段参数';
                return false;
            }
        }
        return $checkResult;
    }

    public function verifyConfig($key = '') {
        if (empty($key)) {
            return \Config::get('NECaptchaConfig')[NE_ACTIVE_ID];
        }
        return \Config::get('NECaptchaConfig')[NE_ACTIVE_ID][$key];
    }

    public function check163Verify($postData, &$errInfo) {
        if (!$postData['NECaptchaValidate']) {
            $errInfo['content'] = '网络正在开小差，请稍后再试';
            return false;
        }
        RedisEx::delInstance();
        $verifier = new NECaptchaVerifier($this->verifyConfig('captchaId'), new SecretPair(YIDUN_CAPTCHA_SECRET_ID, YIDUN_CAPTCHA_SECRET_KEY));
        $validate = $_POST['NECaptchaValidate']; // 获得验证码二次校验数据
        $result = $verifier->verify($validate, []);
        if (!$result['result']) {
            $errInfo = $result;
            $errInfo['content'] = '验证码校验不通过，请重新登录';
        }
        return $result['result'];
    }

    /**
     * clearFInfo
     * -
     * @return void
     * <AUTHOR>
     */
    public function clearFInfo()
    {
        unset($_SESSION[FLOGIN_SESSION_KEY]);
    }

    /**
     * getUserNameField
     * -
     * @return mixed
     * <AUTHOR>
     */
    public function getUserNameField()
    {
        $result = "";
        if (isset($_SESSION[FLOGIN_SESSION_KEY]) && !empty($_SESSION[FLOGIN_SESSION_KEY]))
        {
            $fLoginSessionInfo = unserialize($_SESSION[FLOGIN_SESSION_KEY]);
            return isset($fLoginSessionInfo['userNameField']) ? $fLoginSessionInfo['userNameField'] : "";
        }
        return $result;
    }

    /**
     * clearCaptcha
     * -
     * @return void
     * <AUTHOR>
     */
    public function clearCaptcha()
    {
        unset($_SESSION['checkIMGCode_new']);
        unset($_SESSION['captcha_code']);
    }

    /**
     * isNeedCheckCaptcha
     * -
     * @return bool
     * <AUTHOR>
     */
    public function isNeedCheckCaptcha()
    {
        $checkResult = false;
        $fLoginSessionInfo = $this->getFLoginSessionInfo();
        if ($fLoginSessionInfo['showCaptcha'] > 0)
        {
            $checkResult = true;
        }
        else
        {
            $loginAction = loadAction('login');
            $expire = intval($_SESSION['expire']);
            if ($expire >= $this->showCodeLimitCount || $loginAction->decideCode(get_client_ip()))
            {
                $checkResult = true;
            }
            else
            {
                loadAction('checkIP');
                if (!CheckIPAction::checkISChinaIP($this->IP))
                {
                    $checkResult = true;
                }
            }
        }

        return $checkResult;
    }
}
