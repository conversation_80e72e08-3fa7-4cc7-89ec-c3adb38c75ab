<?php

use Octopus\PdoEx;

class ApiFailDataModel extends Model
{

    private $pdo;
    private $table = 'api_fail_data';

    /**
     * 初始化
     * <AUTHOR>
     * @DateTime 2018-05-07T10:22:02+0800
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
    }

    /**
     * 插入数据
     * <AUTHOR>
     * @DateTime 2018-05-07T10:23:51+0800
     * @param    [type]                   $data [description]
     * @return   [type]                         [description]
     */
    public function insert($data)
    {
        if (!isset($data["createTime"]))
        {
            $data["createTime"] = date("Y-m-d H:i:s");
        }
        return $this->pdo->insert($this->table, $data);
    }

    /**
     * 更新数据
     * <AUTHOR>
     * @DateTime 2018-05-07T10:28:22+0800
     * @param    [type]                   $id   [description]
     * @param    [type]                   $data [description]
     * @return   [type]                         [description]
     */
    public function update($id, $data)
    {
        $condition = array(
            "where" => "id = :id",
            "params" => array(
                ":id" => $id,
            ),
        );
        return $this->pdo->update($this->table, $data, $condition);
    }

    /**
     * 接口失败数量信息列表
     * <AUTHOR>
     * @DateTime 2018-05-07T12:34:39+0800
     * @param    [type]                   $receiveTime [description]
     * @return   [type]                                [description]
     */
    public function getApiFailList($receiveTime)
    {
        $sql = "select api, code, msg, fromIp, count(distinct(id)) num from `" . $this->table . "` where createTime > :receiveTime group by api, code";
        $params = array(
            ":receiveTime" => $receiveTime,
        );
        return $this->pdo->findAll($sql, $params);
    }

    /**
     * 获取需要清理的数据量
     * <AUTHOR>
     * @DateTime 2018-05-07T12:31:49+0800
     * @param string $createTime [<description>]
     * @return   [type]                   [description]
     */
    public function clearCount($createTime)
    {
        $sql = "select count(id) as total from `" . $this->table . "` where createTime < :createTime limit 1";
        $params = array(
            ":createTime" => $createTime,
        );
        $info = $this->pdo->find($sql, $params);
        return !empty($info["total"]) ? intval($info["total"]) : 0;
    }

    /**
     * 清理数据
     * <AUTHOR>
     * @DateTime 2018-05-10T14:06:54+0800
     * @param    [type]                   $date [description]
     * @return   [type]                         [description]
     */
    public function clear($date)
    {
        $condition = array(
            "where" => "createTime < :createTime",
            "params" => array(
                ":createTime" => $date,
            ),
        );
        return $this->pdo->delete($this->table, $condition);
    }
}
