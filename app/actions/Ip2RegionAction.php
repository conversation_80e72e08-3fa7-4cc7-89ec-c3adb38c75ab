<?php

class Ip2RegionAction extends Action
{
    /**
     * User: panj
     * 获取IP go 服务配置
     * @return array
     */
    protected static function getConfig()
    {
        if ('development' == RUNMODE || 'test' == RUNMODE)
        {
            $config = [
                'host' => 'ip2region.2345.com',
                'port' => '9990',
            ];
        }
        else
        {
            $config = [
                'host' => '127.0.0.1',
                'port' => '9990',
            ];
        }

        return $config;
    }

    /**
     * User: panj
     *
     * @param string $ip ip
     *
     * @return bool|mixed
     */
    public static function ip2region($ip)
    {
        if (!filter_var($ip, FILTER_VALIDATE_IP))
        {
            // return false 当成ip服务不可用，会继续查IO;这里return string
            return "$ip is not a valid IP address";
        }
        $config = self::getConfig();
        // 创建 连接 发送消息 接收响应 关闭连接
        $socket = socket_create(AF_INET, SOCK_STREAM, 0);
        if ($socket == false)
        {
            return false;
        }
        //超时1秒
        socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, ["sec" => 1, "usec" => 0]);
        socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, ["sec" => 1, "usec" => 0]);
        $connected = socket_connect($socket, $config['host'], $config['port']);
        if ($connected == false)
        {
            return false;
        }
        $send = socket_send($socket, $ip, strlen($ip), 0);
        if ($send == false)
        {
            return false;
        }
        $response = socket_read($socket, 1024);
        socket_close($socket);

        if (strlen($response) > 3)
        {
            return json_decode($response, true);
        }

        return false;
    }
}
