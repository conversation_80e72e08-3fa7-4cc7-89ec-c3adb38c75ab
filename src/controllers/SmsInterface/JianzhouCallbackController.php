<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：JianzhouCallbackController.php
 * 摘    要：建州回调
 * 修改日期：2017.08.01
 * */

namespace SmsInterface;

use Controller;

class JianzhouCallbackController extends Controller
{
    /**
     * 查看该渠道商是否具有访问权限
     * */
    public function __construct()
    {
        $isAccess = \ChannelIpWhiteList::isAccessAuthChannel('jianzhou');
        if (!$isAccess)
        {
            header('HTTP/1.1 401 Unauthorized ');
            exit;
        }
    }

    /**
     * 发短信回调接口状态同步
     *
     * 注意其他文件 的这个方法名 拼写错误..
     *
     * @return void
     */
    public function actionCallbackRsyncStatus()
    {
//        $respStr  = file_get_contents('php://input');
        $respStr = $this->Filter->post('args');
//        $redisAction = \RedisAction::connect();
//        $redisAction->setex("test_callback", 3600, $respStr);

        if ($respStr)
        {
            $data = explode(";", $respStr);
            $result = false;
            $redisAction = \RedisAction::connect();
            foreach ($data as $key => $val)
            {
                $tmp = explode(",", $val);
                if (count($tmp) >= 4)
                {
                    $status = trim($tmp[2]) == 0 ? "DELIVRD" : trim($tmp[2]);
                    $params = array(
                        "Msg_Id"        => trim($tmp[0]),
                        "Mobile"        => trim($tmp[1]),
                        "OriginStatus"  => trim($tmp[2]),
                        "Status"        => trim($status),
                        "Callback_Time" => trim($tmp[3]),
                    );
//                    $redisAction->setex("test_callback_ok", 3600, var_export($respStr, true));
                    $result = $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($params));
                }
            }
            if ($result)
            {
                echo '1';
                exit;
            }
        }
        echo '0';
        exit;
    }

    /**
     * 用户回复内容回调, 未使用到的代码
     *
     * @return void
     */
    public function actionCallbackSmsUplinkData()
    {
        exit;

        $phone = $this->Filter->getPost('phone');
        $content = $this->Filter->getPost('content');
        $time = $this->Filter->getPost('time');
        $SmsChannelAction = SmsChannelAction::getInstance();
        $content = \EncodingAction::transcoding($content);
        $isUnsub = $SmsChannelAction->checkTdTag($content);

        $SmsLogModel = SmsLogModel::getInstance();
        $channelData = array(
            'msgId'      => microtime(true) * 10000,
            'phone'      => $phone,
            'is_unsub'   => $isUnsub,
            'providerId' => 2,
            'message'    => $content,
        );
        $isTrue = $SmsLogModel->addSmsReceiptInfo($channelData);
        if ($isTrue)
        {
            echo '1';
            exit;
        }
        else
        {
            echo '0';
            exit;
        }
    }


}

?>