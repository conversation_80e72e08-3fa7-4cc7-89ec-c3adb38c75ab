var MD5=function(string){function RotateLeft(lValue,iShiftBits){return(lValue<<iShiftBits)|(lValue>>>(32-iShiftBits))}function AddUnsigned(lX,lY){var lX4,lY4,lX8,lY8,lResult;lX8=(lX&2147483648);lY8=(lY&2147483648);lX4=(lX&1073741824);lY4=(lY&1073741824);lResult=(lX&1073741823)+(lY&1073741823);if(lX4&lY4){return(lResult^2147483648^lX8^lY8)}if(lX4|lY4){if(lResult&1073741824){return(lResult^3221225472^lX8^lY8)}else{return(lResult^1073741824^lX8^lY8)}}else{return(lResult^lX8^lY8)}}function F(x,y,z){return(x&y)|((~x)&z)}function G(x,y,z){return(x&z)|(y&(~z))}function H(x,y,z){return(x^y^z)}function I(x,y,z){return(y^(x|(~z)))}function FF(a,b,c,d,x,s,ac){a=AddUnsigned(a,AddUnsigned(AddUnsigned(F(b,c,d),x),ac));return AddUnsigned(RotateLeft(a,s),b)}function GG(a,b,c,d,x,s,ac){a=AddUnsigned(a,AddUnsigned(AddUnsigned(G(b,c,d),x),ac));return AddUnsigned(RotateLeft(a,s),b)}function HH(a,b,c,d,x,s,ac){a=AddUnsigned(a,AddUnsigned(AddUnsigned(H(b,c,d),x),ac));return AddUnsigned(RotateLeft(a,s),b)}function II(a,b,c,d,x,s,ac){a=AddUnsigned(a,AddUnsigned(AddUnsigned(I(b,c,d),x),ac));return AddUnsigned(RotateLeft(a,s),b)}function ConvertToWordArray(string){var lWordCount;var lMessageLength=string.length;var lNumberOfWords_temp1=lMessageLength+8;var lNumberOfWords_temp2=(lNumberOfWords_temp1-(lNumberOfWords_temp1%64))/64;var lNumberOfWords=(lNumberOfWords_temp2+1)*16;var lWordArray=Array(lNumberOfWords-1);var lBytePosition=0;var lByteCount=0;while(lByteCount<lMessageLength){lWordCount=(lByteCount-(lByteCount%4))/4;lBytePosition=(lByteCount%4)*8;lWordArray[lWordCount]=(lWordArray[lWordCount]|(string.charCodeAt(lByteCount)<<lBytePosition));lByteCount++}lWordCount=(lByteCount-(lByteCount%4))/4;lBytePosition=(lByteCount%4)*8;lWordArray[lWordCount]=lWordArray[lWordCount]|(128<<lBytePosition);lWordArray[lNumberOfWords-2]=lMessageLength<<3;lWordArray[lNumberOfWords-1]=lMessageLength>>>29;return lWordArray}function WordToHex(lValue){var WordToHexValue="",WordToHexValue_temp="",lByte,lCount;for(lCount=0;lCount<=3;lCount++){lByte=(lValue>>>(lCount*8))&255;WordToHexValue_temp="0"+lByte.toString(16);WordToHexValue=WordToHexValue+WordToHexValue_temp.substr(WordToHexValue_temp.length-2,2)}return WordToHexValue}function Utf8Encode(string){string=string.replace(/\r\n/g,"\n");var utftext="";for(var n=0;n<string.length;n++){var c=string.charCodeAt(n);if(c<128){utftext+=String.fromCharCode(c)}else{if((c>127)&&(c<2048)){utftext+=String.fromCharCode((c>>6)|192);utftext+=String.fromCharCode((c&63)|128)}else{utftext+=String.fromCharCode((c>>12)|224);utftext+=String.fromCharCode(((c>>6)&63)|128);utftext+=String.fromCharCode((c&63)|128)}}}return utftext}var x=Array();var k,AA,BB,CC,DD,a,b,c,d;var S11=7,S12=12,S13=17,S14=22;var S21=5,S22=9,S23=14,S24=20;var S31=4,S32=11,S33=16,S34=23;var S41=6,S42=10,S43=15,S44=21;string=Utf8Encode(string);x=ConvertToWordArray(string);a=1732584193;b=4023233417;c=2562383102;d=271733878;for(k=0;k<x.length;k+=16){AA=a;BB=b;CC=c;DD=d;a=FF(a,b,c,d,x[k+0],S11,3614090360);d=FF(d,a,b,c,x[k+1],S12,3905402710);c=FF(c,d,a,b,x[k+2],S13,606105819);b=FF(b,c,d,a,x[k+3],S14,3250441966);a=FF(a,b,c,d,x[k+4],S11,4118548399);d=FF(d,a,b,c,x[k+5],S12,1200080426);c=FF(c,d,a,b,x[k+6],S13,2821735955);b=FF(b,c,d,a,x[k+7],S14,4249261313);a=FF(a,b,c,d,x[k+8],S11,1770035416);d=FF(d,a,b,c,x[k+9],S12,2336552879);c=FF(c,d,a,b,x[k+10],S13,4294925233);b=FF(b,c,d,a,x[k+11],S14,2304563134);a=FF(a,b,c,d,x[k+12],S11,1804603682);d=FF(d,a,b,c,x[k+13],S12,4254626195);c=FF(c,d,a,b,x[k+14],S13,2792965006);b=FF(b,c,d,a,x[k+15],S14,1236535329);a=GG(a,b,c,d,x[k+1],S21,4129170786);d=GG(d,a,b,c,x[k+6],S22,3225465664);c=GG(c,d,a,b,x[k+11],S23,643717713);b=GG(b,c,d,a,x[k+0],S24,3921069994);a=GG(a,b,c,d,x[k+5],S21,3593408605);d=GG(d,a,b,c,x[k+10],S22,38016083);c=GG(c,d,a,b,x[k+15],S23,3634488961);b=GG(b,c,d,a,x[k+4],S24,3889429448);a=GG(a,b,c,d,x[k+9],S21,568446438);d=GG(d,a,b,c,x[k+14],S22,3275163606);c=GG(c,d,a,b,x[k+3],S23,4107603335);b=GG(b,c,d,a,x[k+8],S24,1163531501);a=GG(a,b,c,d,x[k+13],S21,2850285829);d=GG(d,a,b,c,x[k+2],S22,4243563512);c=GG(c,d,a,b,x[k+7],S23,1735328473);b=GG(b,c,d,a,x[k+12],S24,2368359562);a=HH(a,b,c,d,x[k+5],S31,4294588738);d=HH(d,a,b,c,x[k+8],S32,2272392833);c=HH(c,d,a,b,x[k+11],S33,1839030562);b=HH(b,c,d,a,x[k+14],S34,4259657740);a=HH(a,b,c,d,x[k+1],S31,2763975236);d=HH(d,a,b,c,x[k+4],S32,1272893353);c=HH(c,d,a,b,x[k+7],S33,4139469664);b=HH(b,c,d,a,x[k+10],S34,3200236656);a=HH(a,b,c,d,x[k+13],S31,681279174);d=HH(d,a,b,c,x[k+0],S32,3936430074);c=HH(c,d,a,b,x[k+3],S33,3572445317);b=HH(b,c,d,a,x[k+6],S34,76029189);a=HH(a,b,c,d,x[k+9],S31,3654602809);d=HH(d,a,b,c,x[k+12],S32,3873151461);c=HH(c,d,a,b,x[k+15],S33,530742520);b=HH(b,c,d,a,x[k+2],S34,3299628645);a=II(a,b,c,d,x[k+0],S41,4096336452);d=II(d,a,b,c,x[k+7],S42,1126891415);c=II(c,d,a,b,x[k+14],S43,2878612391);b=II(b,c,d,a,x[k+5],S44,4237533241);a=II(a,b,c,d,x[k+12],S41,1700485571);d=II(d,a,b,c,x[k+3],S42,2399980690);c=II(c,d,a,b,x[k+10],S43,4293915773);b=II(b,c,d,a,x[k+1],S44,2240044497);
    a=II(a,b,c,d,x[k+8],S41,1873313359);d=II(d,a,b,c,x[k+15],S42,4264355552);c=II(c,d,a,b,x[k+6],S43,2734768916);b=II(b,c,d,a,x[k+13],S44,1309151649);a=II(a,b,c,d,x[k+4],S41,4149444226);d=II(d,a,b,c,x[k+11],S42,3174756917);c=II(c,d,a,b,x[k+2],S43,718787259);b=II(b,c,d,a,x[k+9],S44,3951481745);a=AddUnsigned(a,AA);b=AddUnsigned(b,BB);c=AddUnsigned(c,CC);d=AddUnsigned(d,DD)}var temp=WordToHex(a)+WordToHex(b)+WordToHex(c)+WordToHex(d);return temp.toLowerCase()};function Base64(){_keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";this.encode=function(input){var output="";var chr1,chr2,chr3,enc1,enc2,enc3,enc4;var i=0;input=_utf8_encode(input);while(i<input.length){chr1=input.charCodeAt(i++);chr2=input.charCodeAt(i++);chr3=input.charCodeAt(i++);enc1=chr1>>2;enc2=((chr1&3)<<4)|(chr2>>4);enc3=((chr2&15)<<2)|(chr3>>6);enc4=chr3&63;if(isNaN(chr2)){enc3=enc4=64}else{if(isNaN(chr3)){enc4=64}}output=output+_keyStr.charAt(enc1)+_keyStr.charAt(enc2)+_keyStr.charAt(enc3)+_keyStr.charAt(enc4)}return output};this.decode=function(input){var output="";var chr1,chr2,chr3;var enc1,enc2,enc3,enc4;var i=0;input=input.replace(/[^A-Za-z0-9\+\/\=]/g,"");while(i<input.length){enc1=_keyStr.indexOf(input.charAt(i++));enc2=_keyStr.indexOf(input.charAt(i++));enc3=_keyStr.indexOf(input.charAt(i++));enc4=_keyStr.indexOf(input.charAt(i++));chr1=(enc1<<2)|(enc2>>4);chr2=((enc2&15)<<4)|(enc3>>2);chr3=((enc3&3)<<6)|enc4;output=output+String.fromCharCode(chr1);if(enc3!=64){output=output+String.fromCharCode(chr2)}if(enc4!=64){output=output+String.fromCharCode(chr3)}}output=_utf8_decode(output);return output};_utf8_encode=function(string){string=string.replace(/\r\n/g,"\n");var utftext="";for(var n=0;n<string.length;n++){var c=string.charCodeAt(n);if(c<128){utftext+=String.fromCharCode(c)}else{if((c>127)&&(c<2048)){utftext+=String.fromCharCode((c>>6)|192);utftext+=String.fromCharCode((c&63)|128)}else{utftext+=String.fromCharCode((c>>12)|224);utftext+=String.fromCharCode(((c>>6)&63)|128);utftext+=String.fromCharCode((c&63)|128)}}}return utftext};_utf8_decode=function(utftext){var string="";var i=0;var c=c1=c2=0;while(i<utftext.length){c=utftext.charCodeAt(i);if(c<128){string+=String.fromCharCode(c);i++}else{if((c>191)&&(c<224)){c2=utftext.charCodeAt(i+1);string+=String.fromCharCode(((c&31)<<6)|(c2&63));i+=2}else{c2=utftext.charCodeAt(i+1);c3=utftext.charCodeAt(i+2);string+=String.fromCharCode(((c&15)<<12)|((c2&63)<<6)|(c3&63));i+=3}}}return string}}if(console===undefined){var console={log:function($str){},debug:function($str){}}}function responseszgyym(unf,isShowCaptcha){login.szgyym(unf,isShowCaptcha)}function isWeiXinBrowser(){var ua=window.navigator.userAgent.toLowerCase();return ua.match(/MicroMessenger/i)=="micromessenger"}function isH5GameAppBrowser(){var ua=window.navigator.userAgent.toLowerCase();return ua.match(/2345h5game_android_app/i)=="2345h5game_android_app"}function isMobileBrowser(){var ua=window.navigator.userAgent.toLowerCase();var bIsAndroid=ua.match(/android/i)=="android";var u=window.navigator.userAgent;var isiOS=!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);return bIsAndroid||isiOS}var login={proxyUrl:"",errorLog:"",isGame:false,isMicro:false,isQqShow:true,isWeixinShow:false,commitParam:new Object(),locationUrl:"",wxAppid:"wx9858d355cbe24ab4",isAjax:false,isThirdAjax:false,jqueryObj:$,oldDomain:document.domain,unf:"username",showCodeFirst:false,logining:false,loginDomain:document.location.protocol+"//passport.2345.com",kv:20180306,flToken:"",showCaptcha:false,projectMid:"yx",clientVer:1,UCKey:"F8DQMNWTIBWEJ3UD",pKey:"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2vJbHcM0dr0HdctK4wrDlzd4u2mW7QDd4ckZvmBnA60nqpZQ5l4EwIJnj7Ijjy3es6+i6N+0M7MVQh7SeM1lAjHZK0psvptpVrCgjiZTAfDErdGx8EL3YZzVEg6hioKQxCnKYyy8W6FV17yLTSZgKcDa6TR+JG0a+Cv8zikpiKiZfMd7yOC2X699zWP70w6WrQdyzp0HeHeIXDZDcKFY3x+r+w9jpb5DiDHy14Wkr8VAMan+nZb588NuKoAtghDGBwjdBYetFf28KVSj01bHxvJ7L/vR8ji8QlCS8VMMA+n/gay0QF85yUqk38DyPaigu9z3OGLC7io73oyD1t6D5wIDAQAB",init:function(ctrlSwitchA){this.loadFiles(ctrlSwitchA);login.jqueryObj(".g-login .changeCode,.codeImg").click(function(){login.showCodeImg()});login.jqueryObj(".g-login .g-inputTxt input").focus(function(){login.jqueryObj(this).parent().addClass("g-inputTxt-focus");login.jqueryObj(this).siblings(".sDes").css("display","none")});login.jqueryObj(".g-login .g-inputTxt input").blur(function(){login.jqueryObj(this).parent().removeClass("g-inputTxt-focus g-inputTxt-error");if(login.jqueryObj(this).val()==""){login.jqueryObj(this).siblings(".sDes").css("display","block")}});if(!login.isPlaceholder()){login.jqueryObj(function(){setTimeout(function(){login.jqueryObj(".g-login .g-inputTxt .judgmentValue").each(function(){if($(this).val()!=""){$(this).parent().find(".sDes").hide()}})},200)})}login.jqueryObj(".g-login .g-inputTxt input").keypress(function(event){var key=event.which;if(key==13){if(login.isMicro&&login.jqueryObj("#wd_code").is(":hidden")&&login.showCaptcha==true){if(login.checkUserAccount()&&login.checkUserPass()){login.checkLoginStatusBack({"display":true})
}}else{login.submitForm()}}});var exePopupName=this.exeFuncPopupName;login.jqueryObj(".g-login .loginClose").click(function(){if(typeof exePopupName!="undefined"){exePopupName()}login.jqueryObj("#jsLoginDiv").hide()});if(typeof this.locationUrl=="undefined"){this.locationUrl="http://"+document.domain}var callbackUrl="&callback=";switch(typeof(this.setCookieUrl)){case"string":callbackUrl+=this.setCookieUrl;break;case"object":for(var surl in this.setCookieUrl){callbackUrl+=this.setCookieUrl[surl]+","}break;default:callbackUrl=""}login.jqueryObj(".g-login .thirdPartyLogin").click(function(){var thirdQqCallLocation=login.getThirdLocationUrl("qq");var thirdWeixinCallLocation=login.getThirdLocationUrl("weixin");var type=login.jqueryObj(this).attr("type");var link="";switch(type){case"qq":link="//login.2345.com/qq?forward="+thirdQqCallLocation+callbackUrl;break;case"weixin":if(isWeiXinBrowser()){link="//passport.2345.com/weixin/mp/"+login.wxAppid+"?forward="+thirdWeixinCallLocation+callbackUrl}else{if(isH5GameAppBrowser()){login.launchWeixinClient(login.wxAppid,thirdWeixinCallLocation+callbackUrl);return}else{link="//passport.2345.com/weixin?forward="+thirdWeixinCallLocation+callbackUrl}}break;default:link="";break}if(link==""){return false}if(login.isThirdAjax){login.thirdExeName(link)}else{login.jqueryObj(this).attr("href",link);window.location=link}});login.loginControl()},setClientVer:function(ver){this.clientVer=ver},generateSign:function(param){var array;if(param instanceof Array){array=param}else{array=new Array();for(var tmpKey in param){array.push(tmpKey+"="+param[tmpKey])}}array.sort();return MD5(array.join("&")+"&UCKey="+this.UCKey)},appendUrl:function(path,param,isGet){if(!param){param={}}param["mid"]=login.projectMid;param["clientVer"]=login.clientVer;param["timestamp"]=parseInt(new Date().getTime()/1000);var array=new Array();for(var tKey in param){array.push(tKey+"="+param[tKey])}var tUrl=this.loginDomain+path;if(isGet){tUrl+="?"+array.join("&")+"&sign="+this.generateSign(array)}return tUrl},loginControl:function(loginErrorData){var param={};param["v"]=1;var url=this.appendUrl("/clientapi/check/faction",param,true);$.ajax({url:url,dataType:"jsonp",success:function(response){var decodeData=eval("("+response+")");if(decodeData.code==200){var JlfData=decodeData.data;login.unf=JlfData.unf;login.flToken=JlfData.flToken;login.showCaptcha=JlfData.showCaptcha;login.kv=JlfData.kv;login.jqueryObj('input[name="flToken"]').attr("value",login.flToken);if(JlfData.kv>login.kv){login.getNewKV()}else{responseszgyym(JlfData.unf,login.showCaptcha);if(loginErrorData){if(!login.showCaptcha&&loginErrorData.display){login.showCaptcha=true}login.refreshCodeImg(loginErrorData);if(typeof login.exeLoginFailureName!="undefined"){login.exeLoginFailureName(loginErrorData)}}if(!login.showCaptcha){var requireCaptcha=login.jqueryObj("input[name=requiredCaptcha]").val();if(requireCaptcha){login.showCaptcha=true;login.showCodeImg()}}setTimeout(login.removeSubmitDisabled,500)}}else{login.showErrorTips("#g-error-tips",decodeData.msg);if(loginErrorData){if(!login.showCaptcha&&loginErrorData.display){login.showCaptcha=true}login.refreshCodeImg(loginErrorData);if(typeof login.exeLoginFailureName!="undefined"){login.exeLoginFailureName(loginErrorData)}}console.log("deny")}}})},getThirdLocationUrl:function(type){var url="";switch(typeof(this.thirdLocationUrl)){case"string":url=this.thirdLocationUrl;break;case"object":if(type==="qq"){url=(typeof this.thirdLocationUrl["qq"])!=="undefined"?this.thirdLocationUrl["qq"]:this.locationUrl}else{if(type==="weixin"){url=(typeof this.thirdLocationUrl["weixin"])!=="undefined"?this.thirdLocationUrl["weixin"]:this.locationUrl}}break;default:url=this.locationUrl}return url},launchWeixinClient:function(wxAppid,forward){if(typeof(WXCallback)=="object"){$.ajax({url:"//passport.2345.com/weixin/h5App/"+wxAppid,data:"forward="+forward,async:false,dataType:"jsonp",success:function(response){WXCallback.launchWX(response)},error:function(jqXHR,textStatus,errorThrown){console.log(jqXHR.status)},timeout:3000})}},setWeixinQrImg:function(wxQrImgId,cssUrl,style){!function(a,b){function d(a){var e,c=b.createElement("iframe"),d="https://open.weixin.qq.com/connect/qrconnect?appid="+a.appid+"&scope="+a.scope+"&redirect_uri="+a.redirect_uri+"&state="+a.state+"&login_type=jssdk";d+=a.style?"&style="+a.style:"";d+=a.href?"&href="+a.href:"";c.src=d;c.frameBorder="0";c.allowTransparency="true";c.scrolling="no";e=b.getElementById(a.id);var width=e.getAttribute("data-width"),height=e.getAttribute("data-height");c.width=width?width:"150px",c.height=height?height:"140px";e.innerHTML="",e.appendChild(c)}a.WxLogin=d}(window,document);if(!cssUrl){cssUrl=""}$.ajax({url:login.loginDomain+"/weixin/jsLoginQr/",data:"forward="+this.getThirdLocationUrl("weixin"),async:false,dataType:"jsonp",success:function(response){if(response){var obj=new WxLogin({id:wxQrImgId,appid:response.data.appid,scope:"snsapi_login",redirect_uri:response.data.redirectUri,state:response.data.state,style:style,href:cssUrl})
}},error:function(jqXHR,textStatus,errorThrown){console.log(jqXHR.status)},timeout:3000})},loadFiles:function(ctrlSwitchA){if(typeof(ctrlSwitchA)!="object"||ctrlSwitchA==null){var ctrlSwitchA={"isPop":false}}if(typeof(ctrlSwitchA["isPop"])=="undefined"?false:!ctrlSwitchA["isPop"]){login.jqueryObj(".g-login .loginClose").hide()}},checkLoginStatus:function(data){if(data.display){login.showCaptcha=data.display;if(!login.isMicro){login.showCodeImg()}}},setRequiredCaptcha:function(data){$("input[name=requiredCaptcha]").val(data);login.showCaptcha=true;login.showCaptchaInput()},checkLoginStatusBack:function(data){if(data.display&&login.jqueryObj("#wd_code").is(":hidden")){login.showCaptcha=data.display;login.showCodeImg();login.hideErrorTips("#g-error-tips");login.jqueryObj("#wd_name").hide();login.jqueryObj("#wd_code").show()}else{login.hideCodeImg();login.hideErrorTips("#g-error-tips");login.jqueryObj("#wd_name").show();login.jqueryObj("#wd_code").hide()}},setWxAppid:function(wxAppid){this.wxAppid=wxAppid},setLocation:function(url){this.locationUrl=url},setGameProject:function(){this.isGame=true},setMicro:function(flag){this.isMicro=flag},setQqShow:function(flag){this.isQqShow=flag},setWeixinShow:function(flag){this.isWeixinShow=flag},setThirdLocation:function(obj){this.thirdLocationUrl=obj},setThirdExeFunc:function(funcName){this.thirdExeName=funcName},setError:function(error){this.errorLog=error},setProject:function(mid){this.projectMid=mid},getLastError:function(){return this.errorLog},exeFunc:function(funcName){this.exeFuncName=funcName},exeLoging:function(logingFuncName){this.exeLogingName=logingFuncName},exeLoginFailure:function(loginFailureName){this.exeLoginFailureName=loginFailureName},exePopup:function(popupName){this.exeFuncPopupName=popupName},setCommitParam:function(data){this.commitParam=data},getOtherParamHtml:function(){var str="";for(var key in this.commitParam){str+='<input type="hidden" value="'+this.commitParam[key]+'" name="'+key+'">'}return str},setLoginConfig:function(){},setDomain:function(doamainName){this.getDomainName=doamainName},setProxy:function(proxyUrl){this.proxyUrl=proxyUrl},setCookieUrl:function(url){this.setCookieUrl=url},isHtml5:function(){if(window.applicationCache){return true}else{return false}},getLoginHtml:function(){return'<div class="g-login" id="jsLoginDiv">'+'<div class="g-login-th">'+'<span class="sMark g-left">2345\u5e10\u53f7\u767b\u5f55</span>'+'<a href="javascript:void(0);" class="g-login-closeBtn g-right loginClose"></a>'+"</div>"+'<div class="g-login-tb clearfix">'+' <form id="myFormLogin">'+this.getOtherParamHtml()+'<input type="hidden" name="cmd" value="login">'+'<input type="hidden"  name="currtime"/>'+'<input type="hidden"  name="flToken"/>'+'<div class="g-login-form">'+'<div class="g-inputTxt">'+'<i class="g-icon-name"></i>'+'<span class="sDes">\u624B\u673A/\u5DF2\u9A8C\u8BC1\u90AE\u7BB1/\u7528\u6237\u540D</span>'+'<input class="judgmentValue" type="text" name="username" value="">'+"</div>"+'<div class="g-inputTxt">'+'<i class="g-icon-password"></i>'+'<span class="sDes">\u5bc6\u7801</span>'+'<input type="hidden" value="" name="password">'+'<input type="hidden" value="" name="requiredCaptcha">'+'<input class="judgmentValue" type="password" value="" id="expassword">'+"</div>"+'<div class="g-login-code" style="display:none">'+'<div class="g-inputTxt">'+'<span class="sDes">\u9a8c\u8bc1\u7801</span>'+'<input type="text" value="" name="check_code">'+"</div>"+'<div class="codePic"><span class="sPic g-left">'+'<img class="codeImg">'+'</span><a href="#"  class="g-right changeCode">\u6362\u4e00\u6362</a></div>'+"</div>"+'<div class="g-error" id="g-error-tips" style="display:none"></div>'+'<div class="g-txt"><span class="g-sCheck g-left"><input type="checkbox" checked class="g-checkbox" name="autoLogin">\u4e0b\u6b21\u81ea\u52a8\u767b\u5f55</span><a href="//passport.2345.com/find?type=password" target="_blank" class="g-right forgetPassClass">\u627E\u56DE\u5BC6\u7801</a></div>'+'<input  type="button" class="g-btn btn-submit" value="\u767b&nbsp;&nbsp;&nbsp;&nbsp;\u5f55">'+(login.isMicro?"":'<div class="g-txt g-txt-registration"><a href="//passport.2345.com/reg.php" target="_blank" class="g-txt-login-btn"><em>\u7acb\u523b</em>\u6ce8\u518c</a></div>')+'<div class="g-other-login">'+(!login.isQqShow&&!login.isWeixinShow?"":'<span class="sTit">\u4f60\u4e5f\u53ef\u4ee5\u7528\u4ee5\u4e0b\u65b9\u5f0f\u767b\u5f55</span>')+'<div class="otherStyle">'+(login.isQqShow?'<a href="javascript:void(0);" class="blueBtn thirdPartyLogin" type="qq"><i class="g-icon-qq"></i><em class="qqTxthide">QQ登录</em></a>':"")+(login.isWeixinShow?'<a href="javascript:void(0);" class="blueBtn thirdPartyLogin login-weixin" type="weixinJs"><i class="g-icon-weixin"></i><em class="qqTxthide">微信登录</em></a>':"")+"</div>"+(isWeiXinBrowser()||isH5GameAppBrowser()?'<div class="otherStyle">'+'<a href="javascript:void(0);" class="blueBtn thirdPartyLogin" type="weixin"><i class="g-icon-weixin"></i><em class="qqTxthide">微信登录</em></a>'+"</div>":"")+"</div>"+"</div>"+"</form>"+"</div>"+"</div>"
},showCodeImg:function(){d=new Date();login.jqueryObj(".g-login .codeImg").prop("src","//passport.2345.com/captcha?d="+d.getTime()+"&mid="+login.projectMid);login.jqueryObj(".g-login .g-login-code").show();if(this.isGame==true){this.showCodeImgForGameInit()}return},showCodeImgForGameInit:function(){login.jqueryObj(".g-login .g-login-form").addClass("showcode");if(this.isMicro==true){login.jqueryObj(".g-login .g-login-code").prevAll(".g-inputTxt").hide();login.jqueryObj(".g-login .g-txt").hide()}},showCaptchaInput:function(){login.jqueryObj(".g-login .g-login-code").show();login.jqueryObj(".g-login .g-login-form").addClass("showcode")},hideCodeImg:function(){d=new Date();login.jqueryObj(".g-login .g-login-code").hide();if(this.isGame==true){login.jqueryObj(".g-login .g-login-form").removeClass("showcode");if(this.isMicro==true){login.jqueryObj(".g-login .g-login-code").prevAll(".g-inputTxt").show();login.jqueryObj(".g-login .g-txt").show()}}return},showErrorTips:function(tag,msg){var gErrorTips=login.jqueryObj(tag);gErrorTips.html(msg);gErrorTips.show()},hideErrorTips:function(tag){var gErrorTips=login.jqueryObj(tag);gErrorTips.html("");gErrorTips.hide()},checkUserAccount:function(){var status=false;var unfS='.g-login .g-inputTxt input[name="'+login.unf+'"]';var userNameObj=login.jqueryObj(unfS);var username=userNameObj.val();if(username==""){login.showErrorTips("#g-error-tips","\u7528\u6237\u540d\u4e0d\u80fd\u4e3a\u7a7a");userNameObj.parent().addClass("g-inputTxt-error")}else{if(username.length<2){login.showErrorTips("#g-error-tips","\u6700\u5c112\u4e2a\u5b57\u7b26!");userNameObj.parent().addClass("g-inputTxt-error")}else{if(username.replace(/[^\x00-\xff]/g,"**").length>24){login.showErrorTips("#g-error-tips","\u8bf7\u4e0d\u8981\u8d85\u8fc724\u4e2a\u5b57\u7b26!");userNameObj.parent().addClass("g-inputTxt-error")}else{status=true}}}return status},checkUserPass:function(){var status=false;var password=login.jqueryObj("#expassword").val();if(password==""){login.showErrorTips("#g-error-tips","\u8bf7\u5148\u8f93\u5165\u5bc6\u7801!");login.jqueryObj("#expassword").parent().addClass("g-inputTxt-error")}else{password=MD5(password);var mdpwd=password.split("").reverse().join("");login.jqueryObj('.g-login input[name="password"]').val(mdpwd);status=true}return status},showLogin:function(tag,ctrlSwitchA){if(this.proxyUrl==""){login.showErrorTips("body","\u4ee3\u7406\u9875\u9762\u4e0d\u80fd\u4e3a\u7a7a");return false}login.jqueryObj(tag).append(this.getLoginHtml());this.init(ctrlSwitchA);if(this.isHtml5()){this.createProxy();window.addEventListener("message",function(e){login.response(e.data)},false)}login.jqueryObj(".g-login .btn-submit").click(function(){if(login.isMicro&&login.jqueryObj("#wd_code").is(":hidden")&&login.showCaptcha==true){if(login.checkUserAccount()&&login.checkUserPass()){login.checkLoginStatusBack({"display":true})}}else{login.submitForm()}})},generateLoginParam:function(){var param={};param["password"]=login.jqueryObj('.g-login input[name="password"]').val();param["cmd"]=login.jqueryObj('.g-login input[name="cmd"]').val();param["mid"]=login.projectMid;return"&sign="+login.generateSign(param)},submitForm:function(){if(login.jqueryObj.browser.msie&&(login.jqueryObj.browser.version=="6.0")&&!login.jqueryObj.support.style){login.jqueryObj(".g-login a.btn-submit").attr("href","###")}if(!login.checkUserAccount()){return false}if(!login.checkUserPass()){return false}if(login.showCaptcha==true){var checkCode=login.jqueryObj('.g-login input[name="check_code"]').val();if(checkCode==""){login.showErrorTips("#g-error-tips","\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801");return false}}login.jqueryObj('.g-login input[name="currtime"]').val(parseInt(new Date().getTime()/1000));var getHost=window.location.host;var getCurrentUrl=window.location;var tval=login.jqueryObj('input[name="flToken"]').val();login.jqueryObj('input[name="flToken"]').attr("value",MD5(tval));var submitData=login.jqueryObj("#myFormLogin").serialize()+"&domain="+getHost+"&currentUrl="+getCurrentUrl+"&mid="+login.projectMid;submitData+=login.generateLoginParam();window.submitData=submitData;if(login.logining){return}if(typeof this.exeLogingName!="undefined"){this.exeLogingName()}login.logining=true;login.jqueryObj(".btn-submit").attr("disabled","true");if(login.isHtml5()){var win=document.getElementById("loginProxy").contentWindow;var protocal=document.location.protocol;win.postMessage(submitData,protocal+"//passport.2345.com")}else{login.createProxy()}},createProxy:function(){var code=new Base64();if(this.isHtml5()){login.jqueryObj("body").append('<iframe id="loginProxy" style="display:none" src="//passport.2345.com/clientProxy.html#'+code.encode(this.proxyUrl)+'"></iframe>')}else{var proxyHash=code.encode(this.proxyUrl);var param="";if(this.oldDomain!=document.domain){this.getDomainName=document.domain}if(typeof this.getDomainName!="undefined"){proxyHash=code.fencode(this.proxyUrl+"?domain="+this.getDomainName);param="?domain="+this.getDomainName}login.jqueryObj("body").append('<iframe id="loginProxy" style="display:none" src="'+this.proxyUrl+param+"#"+proxyHash+'"></iframe>')
}},szgyym:function(unf,isShowCaptcha){var userNameObj=login.jqueryObj(".judgmentValue");login.jqueryObj(userNameObj[0]).attr("name",unf);if(isShowCaptcha){login.showCodeImg()}},removeSubmitDisabled:function(){login.logining=false;login.jqueryObj(".btn-submit").removeAttr("disabled")},response:function(retdata){data=eval("("+retdata+")");try{data.status}catch(error){login.removeSubmitDisabled();retdata;return false}if(data.status!="P00001"){login.loginControl(data)}else{if(typeof(data.loadPage)=="undefined"){data.loadPage=new Array()}if(document.domain.slice(-8)!="2345.com"&&data.setCookie!=undefined){var cookieI=eval("("+data.setCookie+")");var param="?"+cookieI.I;switch(typeof(this.setCookieUrl)){case"string":data.loadPage[data.loadPage.length]=this.setCookieUrl+param;break;case"object":for(var surl in this.setCookieUrl){data.loadPage[data.loadPage.length]=this.setCookieUrl[surl]+param}break;default:data.loadPage[data.loadPage.length]="/user/SetLoginCallBack?"+param}}if(data.loadPage.length==0){login.endRunFunc(data)}else{var len=data.loadPage.length;var j=0;for(var i in data.loadPage){login.jqueryObj.getScript(data.loadPage[i],function(){++j;if(j==len){login.endRunFunc(data)}})}}}},refreshCodeImg:function(data){if(typeof(data.location)!="undefined"){window.location=data.location;return false}if(data.display==false){login.showCaptcha=false}else{if(data.display==true||login.showCaptcha){login.showCaptcha=true;if(!login.isMicro){login.showCodeImg()}}}if(login.isMicro){if(data.status=="P00004"){login.showCodeImg()}else{login.checkLoginStatusBack({"display":false})}}login.jqueryObj('.g-login input[name="check_code"]').val("");if(!login.isMicro||data.status!="P00004"){login.jqueryObj('.g-login input[name="password"]').val("");login.jqueryObj("#expassword").val("")}login.showErrorTips("#g-error-tips",data.msg);login.jqueryObj("#expassword").focus()},endRunFunc:function(data){setTimeout(login.removeSubmitDisabled,500);if(typeof this.exeFuncName!="undefined"){this.exeFuncName(data)}if(!this.isAjax){login.locationPage(data)}},locationPage:function(data){if(typeof(data.location)!="undefined"){window.location=data.location}else{if(this.locationUrl!=""){window.location=this.locationUrl}else{window.location.reload()}}},chgWeiboText:function(text){},isPlaceholder:function(){var input=login.jqueryObj(".g-login input").get(0);return"placeholder" in input}};