let authUser = Vue.extend({
    template: `
<div>
<el-form label-position="right" label-width="10px">
    <el-row :gutter="5">
        <el-col :span="24">
            <el-form-item>
                <el-button type="primary" @click="create()" size="small" plain>新增用户</el-button>
                <el-button type="warning" @click="syncOA()" size="small" plain>同步离职</el-button>
            </el-form-item>
        </el-col>
    </el-row>
</el-form>
<el-table :data="table" v-loading="loading" element-loading-text="加载中…" fit highlight-current-row style="width: 100%">
    <el-table-column label="ID" width="80">
        <template slot-scope="scope">
            <span>{{ scope.row.uid }}</span>
        </template>
    </el-table-column>
    <el-table-column label="姓名" width="110">
        <template slot-scope="scope">
            <span @click="detail(scope.row.uid)">{{ scope.row.username }}</span>
        </template>
    </el-table-column>
    <el-table-column property="projectNameStr" label="拥有项目"></el-table-column>
    <el-table-column property="groupNameStr" label="所属组"></el-table-column>
</el-table>
<el-dialog title="用户权限设置" :visible.sync="dialogFormVisible" top="7vh">
    <el-form :model="form" label-width="80px">
        <el-form-item label="id">
            <span>{{ form.uid }}</span><span v-if="form.uid===''">姓名不存在</span>
        </el-form-item>
        <el-form-item label="姓名">
            <el-input v-model="form.username" auto-complete="off" @change="getUid()"></el-input>
        </el-form-item>
        <el-form-item label="所属项目">
            <el-select v-model="form.project" multiple style="width: 100%">
                <el-option
                    v-for="item in options.project"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="拥有权限">
            <el-select v-model="form.group" multiple style="width: 100%">
                <el-option
                    v-for="item in options.group"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
        <el-button size="small" type="danger" @click="del()" v-if="deleteTag===form.uid&&form.uid!==''" plain>删除</el-button>
        <el-button size="small" type="primary" @click="update()">确定</el-button>
    </div>
</el-dialog>
</div>`,
    data: function () {
        return {
            table: [],
            loading: false,
            dialogFormVisible: false,
            formLabelWidth: 80,
            form: {uid: '', username: '', project: [], group: []},
            deleteTag: undefined,
            options: {project: [], group: []}
        }
    },
    methods: {
        pull: function () {
            this.loading = true;
            this.fetch('/Admin/UserAuth/Api?func=userList', {}).then(
                d => {
                    this.table = JSON.parse(d.data);
                }, (err) => {
                    this.$message.error(err);
                }
            ).finally(() => this.loading = false);
        },
        init: function () {
            this.fetch('/Admin/UserAuth/Api?func=projectFullList', {}).then(
                d => {
                    this.options.project = JSON.parse(d.data);
                }, (err) => this.$message.error(err)
            );
            this.fetch('/Admin/UserAuth/Api?func=groupFullList', {}).then(
                d => {
                    this.options.group = JSON.parse(d.data);
                }, (err) => this.$message.error(err)
            );
        },
        getUid: function () {
            this.fetch('/Admin/UserAuth/Api?func=checkUid', {username: this.form.username}).then(
                d => {
                    this.form.uid = d.data;
                }, (err) => {
                    this.$message.error(err);
                    this.form.uid = "";
                }
            );
        },
        detail: function (uid) {
            this.form = {uid: '', username: '', project: [], group: []};
            this.fetch('/Admin/UserAuth/Api?func=userAuthDetail', {uid}).then(
                d => {
                    this.form = JSON.parse(d.data);
                    this.deleteTag = this.form.uid;
                    this.dialogFormVisible = true;
                }, (err) => this.$message.error(err)
            );
        },
        del: function () {
            this.$confirm('删除用户： ' + this.form.username + ' ？', '确认删除', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.fetch('/Admin/UserAuth/Api?func=deleteUser', {uid: this.form.uid}).then(
                    d => {
                        this.dialogFormVisible = false;
                        this.pull();
                    }, (err) => this.$message.error(err)
                );
            })
        },
        create: function (uid) {
            this.form = {uid: '', username: '', project: [], group: []};
            this.deleteTag = undefined;
            this.dialogFormVisible = true;
        },
        update: function () {
            this.getUid();
            this.fetch('/Admin/UserAuth/Api?func=updateUser', {
                uid: this.form.uid,
                username: this.form.username,
                group: this.form.group,
                project: this.form.project
            }).then(
                d => {
                    this.dialogFormVisible = false;
                    this.pull();
                }, (err) => this.$message.error(err)
            );
        },
        syncOA: function () {
            this.fetch('/Admin/UserAuth/Api?func=syncOA', {}).then(
                d => {
                    this.$message.success(d.msg);
                }, (err) => {
                    this.$message.error(err);
                }
            ).finally(() => this.pull());
        },
        fetch: function ($url, $postArr) {
            return new Promise((resolve, reject) => {
                this.$http.post($url, $postArr).then(res => {
                        if (res.data.err === 0) {
                            resolve(res.data);
                        } else if (res.data.msg) {
                            reject(res.data.err + ' - ' + res.data.msg)
                        } else {
                            reject(res.data.err)
                        }
                    }, res => reject(res.status)
                );
            });
        },
    },
    mounted: function () {
        this.$on('pull', function () {
            this.table = null;
            this.option = {project: [], group: []};
            this.init();
            this.pull();
        });
    }
});