<?php

namespace Admin;
use Controller;
class QueryMoneyController extends Controller
{
    public function actionGetChannelMoneyInfo()
    {
        //
        $pageArray = array();
        $GetMoneyInfoAction = GetMoneyInfoAction::getInstance();
        $channelList = GetMoneyInfoAction::getChannelList();
        $channelListMoney = array();
        foreach ($channelList as $channelName => $channelListInfo)
        {
            if (method_exists($GetMoneyInfoAction, $channelName))
            {
                $money = $GetMoneyInfoAction->$channelName();
                $channelListMoney[$channelName] = array('moeny' => $money, 'channelName' => $channelListInfo);
            }
        }
        $pageArray['channelListMoney'] = $channelListMoney;
        loadView('Admin/get_channel_moneyInfo.tpl.html', $pageArray);
    }
}

?>