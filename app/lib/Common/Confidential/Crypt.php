<?php

namespace Common\Confidential;

class Crypt
{
    /**
     * The encryption key.
     *
     * @var string
     */
    protected $key;

    /**
     *  algo
     */
    private $algo = 'AES-128-CBC';

    /**
     * The algorithm used for encryption.
     * @param string $key
     * $param  string  $iv
     * @var string
     */
    protected $iv;

    public function __construct($key, $iv, $cipher_algo = 'AES-128-CBC')
    {
        if (strlen($key) !== 16 || strlen($iv) !== 16) {
            throw new \Exception('Key or iv length must be 16 characters');
        }
        $this->key = $key;
        $this->iv = $iv;
        $this->algo = $cipher_algo;
    }

    /**
     * Create a new encrypter instance.
     *
     * @param string $key
     * @return string
     */
    public function encrypt($value)
    {
        if (is_null($value)) {
            return null;
        }
        $value = json_encode($value);
        $cipherText = openssl_encrypt(
            $value,
            $this->algo,
            $this->key,
            OPENSSL_RAW_DATA,
            $this->iv
        );
        return base64_encode($cipherText);
    }

    /**
     * Decrypt the given value.
     *
     * @param string $value
     * @return string
     * @throws \Illuminate\Contracts\Encryption\DecryptException
     */
    public function decrypt($value)
    {
        if (is_null($value)) {
            return null;
        }
        // 解码 base64 编码的加密数据
        $data = base64_decode($value);
        // 使用 AES-256-CBC 解密数据
        $decrypted = openssl_decrypt(
            $data,
            $this->algo,
            $this->key,
            OPENSSL_RAW_DATA,
            $this->iv
        );
        return json_decode($decrypted);
    }

    /**
     * 加密字符串
     */
    public static function encryptString($value)
    {
        $aes = new self(config('confidential.AES_KEY'), config('confidential.AES_IV'));
        $prefix = config('confidential.AES_PREFIX');
        if (is_null($value)) {
            return null;
        }
        // 加密数据
        $value = $aes->encrypt($value);
        return isset($value) ? $prefix . (string)$value : null;
    }
}