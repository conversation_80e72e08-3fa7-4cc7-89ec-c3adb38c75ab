<?php

includeBase('AdminController');

class LimitQueryController extends AdminController
{

    protected $authName = '查询限制信息';

    public function actionIndex()
    {
        //$page = \Common\Utils\Url::getPage();
        $PageAction = loadAction('Page');
        $pageList =  $PageAction->returnPageConfig(200);
        $limit = $pageList['limit'];
        $type = $_GET['type'];
        $search['type'] = $type;
        $offset = $pageList['page'];//($page - 1) * $limit;
        $redis = RedisEx::getInstance();
        switch ($type)
        {
            case 'getpwdforbidenuser':
                $key = 'getPwdForbidenUsers';
                $msg = "15天内找回密码超过10次的用户名禁止找回密码";
                break;
            case 'loginforbidenip':
                $key = 'loginForbidenIps';
                $msg = "最近" . $redis->hGet('LimitSetting', 'LIPFLN') . "次登录的时间间隔小于" . $this->humanReadable($redis->hGet('LimitSetting', 'LIPFLT')) . "的IP禁止登录，禁止" . $this->humanReadable($redis->hGet('LimitSetting', 'LIPFCT')) . "<br/>"
                        . "最近" . $redis->hGet('LimitSetting', 'LFIPFLN') . "次登录失败的时间间隔小于" . $this->humanReadable($redis->hGet('LimitSetting', 'LFIPFLT')) . "的IP禁止登录，禁止" . $this->humanReadable($redis->hGet('LimitSetting', 'LIPFCT')) . "<br/>"
                        . "最近" . $redis->hGet('LimitSetting', 'LNIPFLN') . "次登录空用户的时间间隔小于" . $this->humanReadable($redis->hGet('LimitSetting', 'LNIPFLT')) . "的IP禁止登录，禁止" . $this->humanReadable($redis->hGet('LimitSetting', 'LIPFCT')) . "";
                break;
            case 'reglockedipduan':
                $key = 'regLockedIpDuans';
                $msg = "" . $this->humanReadable($redis->hGet('LimitSetting', 'RIPDLLT')) . "内注册大于等于" . $redis->hGet('LimitSetting', 'RIPDLLN') . "个帐号的IP段，注册帐号需要激活，限制" . $this->humanReadable($redis->hGet('LimitSetting', 'RIPDLCT')) . "";
                break;
            case 'regforbidenipduan':
                $key = 'regForbidenIpDuans';
                $msg = "" . $this->humanReadable($redis->hGet('LimitSetting', 'RIPDFLT')) . "内注册大于等于" . $redis->hGet('LimitSetting', 'RIPDFLN') . "个帐号的IP段禁止注册，禁止" . $this->humanReadable($redis->hGet('LimitSetting', 'RIPDFCT')) . "";
                break;
            case 'regforbidenip':
                $key = 'regForbidenIps';
                $msg = "同一个ip一分钟内最多可注册2个帐号，一个小时最多15个，一天最多50个。 ";
                break;
            case 'checkforbidenip':
                $key = 'checkForbidenIps';
                $msg = "" . $this->humanReadable($redis->hGet('LimitSetting', 'CIPFLT')) . "内检查" . $redis->hGet('LimitSetting', 'CIPFLN') . "次以上的IP禁止检查，禁止" . $this->humanReadable($redis->hGet('LimitSetting', 'CIPFCT')) . "";
                break;
            case 'visitforbidenip':
                $key = 'visitForbidenIps';
                $msg = "访问频率高的IP，目前仅记录观察不禁止";
                break;
            default :
                $key = 'regForbidenIps';
                $msg = "同一个ip一分钟内最多可注册2个帐号，一个小时最多15个，一天最多50个。 ";
                break;
        }
        $count = $redis->zSize($key);
        $pageArray['type'] = $type;
        $pageArray['msg'] = $msg;
        if ($type == 'getpwdforbidenuser')
        {
            if ($_GET["act"] == "del")
            {
                $redis->del("getPwdTimeLog:" . md5($_GET["val"] . '#1'));
                $redis->zRem($key, mb_convert_encoding($_GET["val"], "UTF-8", "GBK"));
            }
            $data = $redis->zRevRange($key, $offset, $offset + $limit - 1, true);
            foreach ($data as $value => $time)
            {
                $pageArray['data'][] = array('value' => iconv("UTF-8", "GBK//IGNORE", $value), 'date' => date('Y-m-d H:i:s', $time), 'val' => urlencode(iconv("UTF-8", "GBK//IGNORE", $value)));
            }
        }
        else
        {
            if ($_SERVER["REQUEST_METHOD"] == "POST")
            {
                if ($type == 'loginforbidenip')
                {
                    $ip = $_POST['ip'];
                    $ipArr = explode(' ', $ip);
                    foreach ($ipArr as $item)
                    {
                        if ($item && filter_var($item, FILTER_VALIDATE_IP))
                        {
                            $redis->zAdd($key, time(), str_replace(".", "_", $item));
                        }
                    }

                }
                else if ($type == 'regforbidenip')
                {
                    $ip = $_POST['ip'];
                    $ipArr = explode(' ', $ip);
                    foreach ($ipArr as $item)
                    {
                        if ($item && filter_var($item, FILTER_VALIDATE_IP))
                        {
                            $redis->zAdd($key, time() + 86400, str_replace(".", "_", $item));
                        }
                    }
                }
            }
            if ($_GET["act"] == "del")
            {
                $redis->zRem($key, $_GET["val"]);
            }
            $ips = $redis->zRevRange($key, $offset, $offset + $limit - 1, true);
            $ipDuan = array();
            foreach ($ips as $ip => $time)
            {
                $ipTmp = explode("_", $ip);
                $ipDuan[$ipTmp[0] . "." . $ipTmp[1]][] = array('val' => $ip, 'value' => implode(".", $ipTmp), 'date' => date('Y-m-d H:i:s', $time));
            }
            $pageArray['data'] = array();
            foreach ($ipDuan as $duan)
            {
                foreach ($duan as $ip)
                {
                    $pageArray['data'][] = $ip;
                }
            }
        }
        $pageArray['listPage'] = $PageAction->showPage($count, '/houtai_gl/limit_query');//getPagination($page, $limit, $count, $search);
        loadView('admin/limit_query.tpl.html', $pageArray);
    }

    private function humanReadable($time)
    {
        $str = array();
        if ($time >= 86400)
        {
            $str[] = ($time / 86400) . '天';
            $time = $time % 86400;
        }
        if ($time >= 3600)
        {
            $str[] = ($time / 3600) . '小时';
            $time = $time % 3600;
        }
        if ($time >= 60)
        {
            $str[] = ($time / 60) . '分';
            $time = $time % 60;
        }
        if ($time)
        {
            $str[] = $time . '秒';
        }
        return implode('', $str);
    }

}
