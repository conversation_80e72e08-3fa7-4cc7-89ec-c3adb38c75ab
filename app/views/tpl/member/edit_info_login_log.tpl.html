<!DOCTYPE HTML>
<html>
<head>
    <meta charset="gb2312"/>
    <title>用户登录记录</title>
    <meta content="" name="Description"/>
    <meta content="" name="Keywords"/>
    <link rel="stylesheet" type="text/css"
          href="/css/member/global_20160222.css?v={{$smarty.const.STATIC_FILE_VERSION}}">
    <link rel="stylesheet" type="text/css" href="/css/member/userindex.css?v={{$smarty.const.STATIC_FILE_VERSION}}">

</head>

<body style="background: #fff">

<!-- 登录详情 start -->
<div class="loginList">
    <p>登录记录</p>
    <p style="font-size: 14px"> 由于运营商不定期调整网络，我们获取IP所在地可能不准确，请通过登录时间与产品判断是否为您本人操作。 以下为您最近的登录记录，若存在异常情况，请在核实后尽快修改密码。 </p>
    <dl class="abnormal_list">

        <dt><em class="w100">日期</em><em class="w100">时间</em><em class="w100">IP</em><em class="w120">浏览器</em><em
                class="w120">城市</em><em class="w120">登录产品</em></dt>
        {{ foreach from=$pageArray.loginlog item=item key=index }}
        <dd {{if $item.area == $pageArray.uncommonarea}}class="abnormal_infor" {{/if}}>
            <i class="w100">{{$item.day}}</i>
            <i class="w100">{{$item.second}}</i>
            <i class="w100">{{$item.ip}}</i>
            <i class="w120">{{$item.browser}}</i>
            <i class="w120">{{$item.area}}</i>
            <i class="w120">{{$item.product}}</i>
        </dd>
        {{/foreach}}
    </dl>

    <div class="bannerImg">
        <a href="http://wan.2345.com/?frm=yhzx-tg&referer=wzdh" target="_blank" onclick="cc('H180')"><img src="/images/v3/userindex_v3/banner1.png" alt=""></a>
        <a class="last" href="http://tools.2345.com/rili.htm?f=passport" target="_blank" onclick="cc('H182')"><img src="/images/v3/userindex_v3/banner3.png" alt=""></a>
    </div>

</div>
<script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>

<script>
    function cc(a) {
        var b = arguments,
            web = "ajax54",
            a2,
            i1 = document.cookie.indexOf("uUiD="),
            i2;
        if (b.length > 1)
            web = b[1];
        if (i1 != -1) {
            i2 = document.cookie.indexOf(";", i1);
            a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
        }
        if (!a2) {
            a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
            document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
        }
        if (a.length > 0) {
            var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
            $.getScript(c)
        }
        return true;
    }
</script>

<!-- 登录详情弹窗 end -->
</body>

</html>
