$(document).ready(function()
{
	var topFixed = $(".forFixed").offset().top;	//table头部菜单固定Obj
	forWidth();
	
	
	//table头部菜单th位置fixed
	$(window).scroll(function()
	{
		if($(window).scrollTop() > topFixed)
		{
			$(".topFied").show();			
		}else
		{
			$(".topFied").hide();
		};
	});
	
	$(window).resize(function()
	{
		forWidth();
	});
	
	function forWidth()
	{
		for(i = 0; i < $(".forFixed tr th").length; i++)
		{
			$(".topFied tr th").eq(i).width($(".forFixed tr th").eq(i).width());
		}
	};

	
});//JQ