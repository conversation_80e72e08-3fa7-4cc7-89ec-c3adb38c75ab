<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=GBK">
        <script type="text/javascript" src="/js/jquery.min.js"></script>
        <script type="text/javascript" src="/js/datepicker/WdatePicker.js"></script>
        <script type="text/javascript" src="/js/highcharts/highcharts.js"></script>
        <script type="text/javascript" src="/js/highcharts/modules/exporting.js"></script>
        <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
        <link rel="stylesheet" href="/css/v3/top.css">
        <link rel="stylesheet" href="/css/v3/zhanghao.css">
        <title>注册数详情表-注册用户统计</title>
        <script type="text/javascript">
            var chart;
                    function setSeries(data){
                    for (i = 0, len = chart.series.length; i < len; i++) {
                    chart.series[0].remove();
                    }
                    for (i in data){
                    chart.addSeries(data[i]);
                    }
                    }
            $(function() {
            chart = new Highcharts.Chart({
            chart: {
            renderTo: 'container',
                    type: 'line',
                    marginRight: 130,
                    marginBottom: 50
            },
                    title: {
            text: '{{$pageArray.title}}统计',
                    x: - 20 //center
            },
                    subtitle: {
            text: '',
                    x: - 20
            },
                    xAxis: {
            categories: [{{$pageArray.dates}}]
            },
                    yAxis: {
            title: {
            text: '注册次数'
            },
                    plotLines: [{
            value: 0,
                    width: 1,
                    color: '#808080'
            }]
            },
                    tooltip: {
            valueSuffix: '次'
            },
                    legend: {
            layout: 'vertical',
                    align: 'right',
                    verticalAlign: 'top',
                    x: - 10,
                    y: 100,
                    borderWidth: 0
            },
                    series: [{
            name: '{{$pageArray.domains.all}}用户名注册成功',
                    color: '#0000FF',
                    data: [{{$pageArray.charts.all.succ_num_username}}]
            }, {
            name: '{{$pageArray.domains.all}}手机注册成功',
                    color: '#0040FF',
                    data: [{{$pageArray.charts.all.succ_num_phone}}]
            }, {
            name: '{{$pageArray.domains.all}}邮箱注册成功',
                    color: '#0080FF',
                    data: [{{$pageArray.charts.all.succ_num_email}}]
            }, {
            name: '{{$pageArray.domains.all}}用户名注册失败',
                    color: '#FF0000',
                    data: [{{$pageArray.charts.all.fail_num_username}}]
            }, {
            name: '{{$pageArray.domains.all}}手机注册失败',
                    color: '#FF4000',
                    data: [{{$pageArray.charts.all.fail_num_phone}}]
            }, {
            name: '{{$pageArray.domains.all}}邮箱注册失败',
                    color: '#FF8000',
                    data: [{{$pageArray.charts.all.fail_num_email}}]
            }]
            });
            });</script>
    </head>
    <body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
<div class="mainWrap">
    
		<!--面包屑导航 开始-->
        <div class="location">位置：<a href="#" target="_blank">注册用户统计</a>  &gt; <span>注册数详细表</span></div>
        <!--面包屑导航 结束-->
        
        <!--Tab菜单 开始-->
        <div class="TabMenu mt10">
        	<a href="/houtai_gl/reg_sum">每日注册数表</a>
            <a href="/houtai_gl/reg_sum/hour">每3分钟注册数表</a>
            <a href="/houtai_gl/reg_sum/detail" class="currTabMenu">注册数详细表</a>
        </div>
        
        <!--Tab菜单 结束-->
        <div id="container" style="min-width: 400px; height: 400px; margin: 0 auto"></div>
        <div>{{$pageArray.title}}：<span style="color:blue;">蓝色</span>代表注册成功，<span style="color:red;">红色</span>代表注册失败</div>
        <table border="1" cellspacing="0" cellpadding="0" width="100%" class="tabMod whiteTab forFixed">
            <tr style="text-align: center;">
                {{foreach item=domains key=groupKey from=$pageArray.domainGroups name=domainGroups}}
                <th{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:#d6e4f7"{{/if}} colspan="{{$domains|@count}}">{{$groupKey}}</th>
                {{/foreach}}
            </tr>
            <tr style="text-align: center;">
                {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
                {{foreach item=domain_name key=domain from=$domains}}
                <th{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:#d6e4f7"{{/if}}><span title="{{$domain}}">{{if $domain_name}}{{$domain_name}}{{else}}{{$domain}}{{/if}}</span><input type="button" onclick="setSeries([
                    {name: '{{$pageArray.domains.$domain}}用户名注册成功', color: '#0000FF', data: [{{$pageArray.charts.$domain.succ_num_username}}]},
                    {name: '{{$pageArray.domains.$domain}}手机注册成功', color: '#0040FF', data: [{{$pageArray.charts.$domain.succ_num_phone}}]},
                    {name: '{{$pageArray.domains.$domain}}邮箱注册成功', color: '#0080FF', data: [{{$pageArray.charts.$domain.succ_num_email}}]},
                    {name: '{{$pageArray.domains.$domain}}用户名注册失败', color: '#FF0000', data: [{{$pageArray.charts.$domain.fail_num_username}}]},
                    {name: '{{$pageArray.domains.$domain}}手机注册失败', color: '#FF4000', data: [{{$pageArray.charts.$domain.fail_num_phone}}]},
                    {name: '{{$pageArray.domains.$domain}}邮箱注册失败', color: '#FF8000', data: [{{$pageArray.charts.$domain.fail_num_email}}]}
                    ]);" value="图表"/></th>
                {{/foreach}}
                {{/foreach}}
            </tr>
        </table>
            
</div>



<script>

$('.navList a').each(function (){
    if (  $(this).attr('href') == '/houtai_gl/reg_sum' )
    {
        $(this).addClass('select');
    }
});
</script>

        
    </body>
</html>