<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/5/7
 * Time: 11:13
 */
use Service\UserBase\Rules;
use Service\UserBase\UserAvatarCache;

includeBase('CronController');

class NicknameController extends CronController
{
    private static $redis = null;
    private static $expire = 60;

    /**
     * 进程结束后释放进程锁
     */
    public function __destruct()
    {
        // TODO: Implement __destruct() method.
        self::delProcessLock();
    }

    /**
     * 获取redis链接
     * @return \redis|null
     */
    protected static function getRedis()
    {
        if (!is_object(self::$redis)) {
            self::$redis = \RedisEx::getInstance();
        }

        return self::$redis;
    }

    /**
     * 进程锁
     * @param int $expire 有效期
     * @return bool
     */
    private static function getLock($expire = 120)
    {
        return self::getRedis()->set("userNickname:lock", time(), ['nx', 'ex' => $expire]);
    }

    /**
     * 删除进程锁
     * @return mixed 成功：1，失败：0
     */
    protected static function delProcessLock()
    {
        return self::getRedis()->del("userNickname:lock");
    }

    /**
     * 写入文件日志
     * @param string $msg     日志
     * @param string $level   等级
     * @param string $dirType 存放文件
     * @return void
     */
    private static function writeLog($msg, $level = 'info', $dirType = 'w_nickname_cron')
    {
        echo $msg . PHP_EOL;
        $logDir = APPPATH . "/logs/" . date('Ymd') . "/nicknameCronLog/";
        deepMkdir($logDir);
        $logErrorPath = $logDir . date('H') . '_run.log';
        $msg = date('Y-m-d H:i:s') . "\t" . $msg . PHP_EOL;
        file_put_contents($logErrorPath, $msg, FILE_APPEND);
        if (in_array($level, ['warning', 'fatal'])) {
            $warningData = [
                'dir_type'  => $dirType,
                'type'      => 'cron',
                'cronError' => $msg,
                'code'      => 500,
            ];
            loadAction('Warning')->record($warningData);
        }
    }

    /**
     * 昵称信息入库
     * @return void
     */
    public function actionIndex()
    {
        $userBaseModel = loadModel('UserBase');
        $startTime = time();
        $lockFlag = true;
        self::writeLog('昵称队列消费开始 ....................');
        try {
            $lastPassId = $userBaseModel->getLastPassId();
            if ($lastPassId <= 0) {
                self::writeLog('获取最大的passid失败 ...............', 'warning');

                return;
            }
            $lastPassId += 100000; //10w长度的有效范围
            while (time() - $startTime <= self::$expire) {
                if ($lockFlag && self::getLock() !== true) {
                    self::writeLog('cron has been running ..................');
                    sleep(1);
                    continue;
                }
                $lockFlag = false;
                $responseDataJson = UserAvatarCache::getNicknameUpdateList();
                if (!empty($responseDataJson)) {
                    $responseData = json_decode($responseDataJson, true);
                    $errorMsg = [];
                    if (empty($responseData['nickname'])) {
                        self::writeLog('nickname is empty:' . $responseDataJson . '................');
                        continue;
                    }
                    if ($responseData['passid'] <= 0) {
                        self::writeLog('passid小于0:' . "\t" . $responseDataJson . '................', 'warning');
                        continue;
                    }
                    if ($responseData['passid'] > $lastPassId) {
                        self::writeLog('passid大于有效访问:' . $lastPassId . "\t" . $responseDataJson . '................', 'warning');
                        continue;
                    }
                    $userNicknameInfo = $userBaseModel->getAvatarNicknameInfoByPassId($responseData['mid'], $responseData['passid'], '', true);
                    if (!empty($userNicknameInfo['nickname'])) {
                        //如果有昵称，则不在初始化昵称
                        self::writeLog($responseData['mid'] . "\t" . $responseData['passid'] . "\t" . $responseData['nickname'] . " 已存在");
                        continue;
                    }
                    $isUpdate = $userBaseModel->setNicknameInfo($responseData['mid'], $responseData['passid'], $responseData['nickname'], $errorMsg, [], 'default');
                    if ($isUpdate === false) {
                        //报错报道日志平台
                        self::writeLog('error:' . $responseDataJson . $errorMsg['code'] . $errorMsg['msg'], 'warning');
                    } else {
                        self::writeLog('success:' . $responseDataJson);
                    }
                } else {
                    self::writeLog('empty ................');
                    sleep(1);
                }
            }
        }
        catch (Exception $exception) {
            self::writeLog($exception->getMessage(), 'warning');
        }
        self::writeLog('script end ...............');
    }

    /**
     * 昵称消费队列长度监控
     * @return void
     */
    public function actionNicknameListMonitor()
    {
        self::writeLog('开始队列长度检测.........');
        $len = UserAvatarCache::getNicknameListLen();
        if ($len > 100) {
            $msg = '昵称入库队列有积压，当前长度为：' . $len;
            self::writeLog($msg, 'warning');
        }
        self::writeLog('队列长度检测完成.........');
    }

    /**
     * 修补昵称来源
     * @param string $mid 生态名称
     * @param int    $process   进程ID
     * @param int    $id        起始ID
     * @param int    $limit     读取数量
     * @return void
     */
    public function actionRepairNicknameSource(string $mid, int $process, int $id = 1, int $limit = 1000)
    {
        if ($process == 1) {
            $prefix = '';
        } else {
            $prefix = '_' . $process;
        }
        $groupName = \Service\UserBase\Rules::getGroupNameByMid($mid);
        if (empty($groupName)) {
            $groupName = 'Default';
        }
        $table = $groupName . '_user_nickname_avatar' . $prefix;
        $userBaseModel = loadModel('UserBase');
        if ($id == 1) {
            $id = 0;
        }
        while ($userList = $userBaseModel->getAllNicknameAvatar($table, $id, $limit)) {
            $updateList = [];
            foreach ($userList as $userInfo) {
                $id = $userInfo['id'];
                echo $id . PHP_EOL;
                //如果昵称是空的，昵称就没有来源
                if (empty($userInfo['nickname'])) {
                    continue;
                }
                //头像来源不为空，说明是有头像的， 如果为空说明只有昵称，没有头像
                if (!empty($userInfo['valid_avatar_source'])) {
                    //初始化数据
                    switch ($userInfo['valid_avatar_source']) {
                        case 'init':
                            // 更新nickname_source = weixin
                            $updateList['weixin'][] = $userInfo['id'];
                            break;
                        case 'weixin':
                            if (Rules::getCustomRules($mid)::notAllowUseDefaultNickname($userInfo['nickname'])) {
                                // 更新nickname_source = default
                                $updateList['default'][] = $userInfo['id'];
                            } else {
                                // 更新nickname_source = weixin
                                $updateList['weixin'][] = $userInfo['id'];
                            }
                            break;
                        case 'qq':
                            if (Rules::getCustomRules($mid)::notAllowUseDefaultNickname($userInfo['nickname'])) {
                                // 更新nickname_source = default
                                $updateList['default'][] = $userInfo['id'];
                            } else {
                                // 更新nickname_source = qq
                                $updateList['qq'][] = $userInfo['id'];
                            }
                            break;
                        case 'upload':
                            if (Rules::getCustomRules($mid)::notAllowUseDefaultNickname($userInfo['nickname'])) {
                                // 更新nickname_source = default
                                $updateList['default'][] = $userInfo['id'];
                            } else {
                                // 更新nickname_source =  weixin
                                $updateList['weixin'][] = $userInfo['id'];
                            }
                            break;
                        default:
                            //
                    }
                } else {
                    //头像没有来源有昵称，如果昵称是系统定义昵称则为默认昵称，如果非系统昵称，则为自定义昵称
                    if (Rules::getCustomRules($mid)::notAllowUseDefaultNickname($userInfo['nickname'])) {
                        // 更新nickname_source = default
                        $updateList['default'][] = $userInfo['id'];
                    } else {
                        //更新nickname_source = upload
                        $updateList['weixin'][] = $userInfo['id'];
                    }
                }
            }
            foreach ($updateList as $key => $idList) {
                $isSet = $userBaseModel->updateNicknameSource($table, $key, $idList);
                if (!$isSet) {
                    $msg = 'table:' . $table . '|nicknameSource:' . $key . "|idList=" . json_encode($idList);
                    $this->writeLog($msg, 'warning', 'repairNicknameSource');
                }
            }
        }
    }
}
