<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 摘    要：渠道商白名单
 * 作    者：杜海明
 * 修改日期：2016.02.14
 */

class ChannelIpWhiteList
{
    /**
     * 渠道商接口访问白名单
     * */
    public static function getInterfaceWhiteList()
    {
        return array(
            'zhengao'        => array(
                '*************',
                '*************',
                '**************',
                '**************',
                '*************',
                '***************',
                '***************',
                '***************',
                '***************',
                '***************',
                '***************',
                '***************',
                '***************',
                '*************',
                //环科路新ip
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**********',
                '**********',
                '**********',
                '**********',
                '**********',
            ),
            'jianzhou'       => array(
                '**********',
                '************** ',// jianzhou test
                '***************',
                '**************',
                '**************',
                '*************',
                //环科路新ip
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**********',
                '**********',
                '**********',
                '**********',
                '**********',
            ),
            'getSmsResponse' => array(
                '***************',
                '***************',
                '**************',
                '*************',
                '**************',
                '***************',
                '***************',
                '***************',
                '***************',
                '***************',
                '***************',
                '***************',
                '***************',
                //环科路新ip
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**********',
                '**********',
                '**********',
                '**********',
                '**********',
            ),
            'jvchen' => array(
                '**************',
                //环科路新ip
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**********',
                '**********',
                '**********',
                '**********',
                '**********',
            ),
            'yunfeng' => array(
                '121.41.32.182',
                //环科路新ip
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '120.55.126.176',
                '121.41.32.182',
                '**********',
                '**********',
                '**********',
                '**********',
                '**********',
            ),
            'angWang' => array(
                '47.104.84.72',
//                '192.168.229.1',
                //环科路新ip
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**********',
                '**********',
                '**********',
                '**********',
                '**********',
            ),
            'yunRong' => array(
                '121.196.222.119',
                '47.96.147.114',
//                '192.168.229.1',
                //环科路新ip
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '*************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**************',
                '**********',
                '**********',
                '**********',
                '**********',
                '**********',
            ),
        );
    }

    /**
     * 判断是否能够有访问权限
     * */
    public static function isAccessAuth()
    {
        $ipList = self::getInterfaceWhiteList();
        $ipArr = array();
        foreach ($ipList as $ipListInfo)
        {
            foreach ($ipListInfo as $ipInfo)
            {
                $ipArr[] = $ipInfo;
            }
        }
        $ip = get_client_ip();
        return in_array($ip, $ipArr);
    }

    /**
     * 渠道商是否具有访问权限
     * */
    public static function isAccessAuthChannel($channelName)
    {
        $ipList = self::getInterfaceWhiteList();
        $ip = get_client_ip();
        if (!empty($ipList[$channelName]) && in_array($ip, $ipList[$channelName]))
        {
            return true;
        }
        else
        {
            return false;
        }
    }
}
