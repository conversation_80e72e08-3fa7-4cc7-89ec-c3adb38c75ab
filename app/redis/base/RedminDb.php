<?php
includeRedis("RedminApp");

final class RedminDb
{
    protected static $_instances = array();

    /**
     *
     * @param null $driver Driver
     * @return mixed
     * <AUTHOR>
     */
    public static function factory($driver = null)
    {
        $driver = isset($driver) ? $driver : RedminApp::instance()->config['database']['driver'];

        if (!isset(self::$_instances[$driver]))
        {
            includeRedis(ucwords(strtolower($driver)) . 'Db');
            $class = ucwords(strtolower($driver)) . 'Db';
            self::$_instances[$driver] = new $class;
        }
        return self::$_instances[$driver];
    }

    /**
     * clearInstance
     * -
     * @param null $driver Driver
     * @return void
     * @static
     * <AUTHOR>
     */
    public static function clearInstance($driver = null)
    {
        $driver = isset($driver) ? $driver : RedminApp::instance()->config['database']['driver'];

        if (isset(self::$_instances[$driver]))
        {
            $tmpDriver = self::$_instances[$driver];
            if ($tmpDriver)
            {
                $tmpDriver->close();
            }
            unset(self::$_instances[$driver]);
        }
    }
}
