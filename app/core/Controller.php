<?php

use Common\Utils\Url as Requester;

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：Controller.php
 * 摘    要：Controller基类
 * 作    者：张小虎
 * 修改日期：2013.10.12
 */
class Controller
{
    private static $instance;

    /**
     * Controller constructor.
     */
    public function __construct()
    {
        self::$instance = $this;
//        \Service\Monitor\ApiCallHookMonitor::writeAccessNum();
        $mid = Requester::getStringParam('mid');
        $mid = $mid ?: (parse_url(Requester::getStringParam('forward'), PHP_URL_HOST) ?: 'ZT');
        $context = [
            'logUsage' => 'usageStatistics',
            'system' => '用户中心',
            'product' => $mid,
            'app' => $mid,
            'module' => Router::fetchClass(),
            'function' => Router::fetchMethod(),
            'version' => '',
            'count' => 1,
        ];
        WebLogger\Facade\LoggerFacade::info('用户中心api调用次数统计', $context);
    }

    /**
     * getInstance
     * -
     * @return Controller
     * @static
     * <AUTHOR>
     */
    public static function getInstance()
    {
        return self::$instance;
    }

    /**
     * echoResponse
     * -
     * @param array $data Data
     * @return void
     * <AUTHOR>
     */
    protected function echoResponse($data)
    {
        $codeReal = 0;
        if (isset($data['code_real'])) {
            $codeReal = $data['code_real'];
            unset($data['code_real']);
        }
        // 本地日志记录，不推荐使用，后续建议加入kafka
//        if (isset($data['code'])) {
//            loadAction('stat')->requestLog($data['code'], $data['msg'], $codeReal);
//        }
        RedisEx::delInstance();
        PdoEx::delInstance();
        // 上报大数据
        $kafkaAction = loadAction('uploadToKafka');
        if ($kafkaAction->getSwitch()) {
            $record = [];
            isset($data['code']) && $record['code'] = $data['code'];
            $record['code_real'] = $codeReal;
            $kafkaAction->addData($record);
            $kafkaAction->recordForKafka();
        }
        // 上报预警
        if (is_array($data) && isset($data['code'])) {
            loadAction('Warning')->setRequestInfo([
                'code' => $data['code'],
                'status' => $data['code'] == 200 ? true : false,
            ])->flush();
        }

        header('Content-Type: application/json');
        $callback = \Common\Utils\Url::getStringParam("callback", "");
        $callback = preg_replace('/[^\w_\.\$]/', '', $callback);
        $data = json_encode($data);
        if (!empty($callback)) {
            echo $callback . "($data)";
        } else {
            echo $data;
        }
        exit;
    }

    /**
     * echoResponse
     * -
     * @param array $data Data
     * @return void
     * <AUTHOR>
     */
    protected function echoResponseNew($data)
    {
        $codeReal = 0;
        if (isset($data['code_real'])) {
            $codeReal = $data['code_real'];
            unset($data['code_real']);
        }
        // 本地日志记录，不推荐使用，后续建议加入kafka
//        if (isset($data['code'])) {
//            loadAction('stat')->requestLog($data['code'], $data['msg'], $codeReal);
//        }
        // 上报大数据
        $kafkaAction = loadAction('uploadToKafka');
        if ($kafkaAction->getSwitch()) {
            $record = [];
            isset($data['code']) && $record['code'] = $data['code'];
            $record['code_real'] = $codeReal;
            $kafkaAction->addData($record);
            $kafkaAction->recordForKafka();
        }
        // 上报预警
        loadAction('Warning')->setRequestInfo([
            'code' => $data['code'],
            'status' => $data['code'] == 200 ? true : false,
        ])->flush();

        header('Content-Type: application/json');
        $callback = \Common\Utils\Url::getStringParam("callback", "");
        $callback = preg_replace('/[^\w_\.\$]/', '', $callback);
        $data = json_encode($data);
        if (!empty($callback)) {
            echo $callback . "('$data')";
        } else {
            echo $data;
        }
        exit;
    }

    /**
     * json格式输出,支持jsonp(默认callback)
     * -
     * @param int $code Code
     * @param string $data Data
     * @param string $msg Msg
     * @param int $codeReal 区别于code，用于问题定位
     * @return void
     * <AUTHOR>
     */
    protected function codeResponse($code, $data = "", $msg = "", $codeReal = 0)
    {
        if (!empty($msg)) {
            $msg = mb_convert_encoding($msg, "UTF-8", "GBK");
        }
        $response = array(
            "code" => $code,
            "data" => $data,
            'msg' => $msg,
            'code_real' => $codeReal,
        );
        $this->echoResponse($response);
    }

    /**
     * json格式输出,支持jsonp(默认callback)
     * -
     * @param int $code Code
     * @param string $data Data
     * @param string $msg Msg
     * @param int $codeReal 区别于code，用于问题定位
     * @return void
     * <AUTHOR>
     */
    protected function codeResponseNew($code, $data = "", $msg = "", $codeReal = 0)
    {
        if (!empty($msg)) {
            $msg = mb_convert_encoding($msg, "UTF-8", "GBK");
        }
        $response = array(
            "code" => $code,
            "data" => $data,
            'msg' => $msg,
            'code_real' => $codeReal,
        );
        $this->echoResponseNew($response);
    }

    /**
     * 过滤请求参数(非空+uFilter),如有一项为空或false,则4001
     * -
     * @param array $params Params
     * @return array|bool
     * <AUTHOR>
     */
    protected function getParamAndAutoDeny($params)
    {
        $checkResult = $this->checkNecessaryField($params);
        if ($checkResult === false) {
            $this->codeResponseNew(4001, '', '请求拒绝', 1000001);
        }
        $result = $this->uParamFilter($checkResult);
        if ($result === false) {
            $this->codeResponseNew(4002, '', '请求格式错误', 1000002);
        }
        return $result;
    }

    /**
     * 必填字段校验(只校验非空)
     * -
     *
     * @param array  $params Params
     * @param string $method $method
     *
     * @return array|bool
     * <AUTHOR>
     */
    protected function checkNecessaryField($params, $method = '')
    {
        $check = true;
        $checkResult = array();
        foreach ($params as $field) {
            $cValue = Requester::getStringParam($field, false, $method);
            if ($cValue === false) {
                $check = false;
                break;
            } else {
                $checkResult[$field] = $cValue;
            }
        }
        return $check ? $checkResult : false;
    }

    /**
     * uParamFilter
     * -
     * @param array $params Params
     * @return array|bool
     * <AUTHOR>
     */
    protected function uParamFilter($params)
    {
        static $uFilter;
        $returnArray = array();
        $filterResult = true;

        if (!$uFilter) {
            $uFilter = new \Common\Utils\UFilter();
        }
        foreach ($params as $name => $value) {
            $methodName = $name . 'Filter';
            if (method_exists($uFilter, $methodName)) {
                if ($name == \Service\Encryption\Sign\SignManager::$paramSignName) {
                    $fValue = $uFilter->$methodName($value, $params);
                } else {
                    $fValue = $uFilter->$methodName($value);
                }

                if ($fValue === null) {
                    $filterResult = false;
                    break;
                } else {
                    $returnArray[] = $fValue;
                }
            } else {
                $returnArray[] = $value;
            }
        }
        if ($filterResult) {
            return $returnArray;
        } else {
            return false;
        }
    }

    /**
     * startSession
     * -
     * @return void
     * <AUTHOR>
     */
    protected function startSession()
    {
        if (function_exists('session_status')) {
            if (session_status() != PHP_SESSION_ACTIVE) {
                session_start();
            }
        } else {
            if (session_id() === '') {
                session_start();
            }
        }
    }
}
