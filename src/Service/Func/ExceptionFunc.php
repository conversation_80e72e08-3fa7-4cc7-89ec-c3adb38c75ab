<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/9/4
 * Time: 12:08
 */
namespace Service\Func;

class ExceptionFunc
{

    /**
     * 捕获移除
     * @return void
     */
    public static function setHttpError()
    {
        register_shutdown_function(function () {
            $error = error_get_last();
            $levenArr = array(1, 4, 16, 64, 256, 4096, E_ALL);
            if (!empty($error) && in_array($error['type'], $levenArr))
            {
                $logPath = APPPATH . '/logs/' . date('Ymd');
                if (!is_dir($logPath))
                {
                    mkdir($logPath, 0777);
                }
                $errorLogPath = $logPath . '/error_' . date('H') . '.log';
                $msg = date('Y-m-d H:i:s') . "\r\n" . var_export($error, true) . "\r\n" . var_export($_GET, true) . "\r\n" . var_export($_POST, true) . "\r\n" . str_repeat('-', 40) . "\r\n";
                file_put_contents($errorLogPath, $msg, FILE_APPEND);
            }
        });
    }
}
