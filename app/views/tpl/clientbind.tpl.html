<!DOCTYPE HTML>
<!--[if lt IE 7]><html class="ie6"><![endif]-->
<!--[if IE 7]><html class="ie7"><![endif]-->
<!--[if IE 8]><html class="ie8"><![endif]-->
<!--[if gte IE 9]><html class="ie9"><![endif]-->
<html>
<head>
    <meta charset="gb2312">
    <title>游侠项目对战平台-2345游戏</title>
    <link rel="stylesheet" type="text/css" href="/css/youxiaduizhan-user.css">
    <style type="text/css">
        .overlay .g-inputTxt-error {border:1px solid red;}
        /* {{if $pageArray.gameid}} */
            .theme-user{background:url("{{$pageArray.imgurl}}") 50% 0 no-repeat;height:695px}
        /* {{/if}} */
    </style>
</head>
<body>
<div class="wrapper theme-user">
</div>
<!-- overlay -->
<div class="overlay">
    <div class="layer-box">
        <div class="layer-box-hd">
            <h2 class="txt">帐号设置</h2>
        </div>
        <div class="layer-box-bd">
            <div class="set-account tcenter">
                <p>游戏里的帐号重复啦，请设置新帐号登录游戏</p>
                <div class="widget-input mt18">
                    <input id="username" name="username" type="text">
				</div>
                <div style="display: none" class="form-tip txt-error mt10">错误提示</div>
                <div class="btn-grp mt18">
                    <a href="##" class="btn-blue" onClick="myFormSub(); return false">登录</a>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="/js/jquery-1.8.3.min.js"></script>
<script>
    $(function(){
        // 弹层
        // resize
        var resizePop = function(){
            $(".overlay").css({"height":$(window).height(),"width":$(window).width()});
        }
        resizePop();
        // 弹层垂直居中
        var modalPop = $('.layer-box');
        var verMiddle = function(){
            modalPop.each(function(){
                var modHeight = $(this).outerHeight();
                $(this).css({"margin-top":-(modHeight/2)});
            });
        }
        verMiddle();
        //resize
        $(window).resize(function(){
            resizePop();
            verMiddle();
        });
        // 关闭弹层
        var popCloseBtn = modalPop.find($('.layer-box-hd .close'));
        popCloseBtn.click(function(){
            $(".overlay").fadeOut(200);
        })
    });

    document.onkeydown = function(evt) {
        var evt = window.event?window.event:evt;
        if (evt.keyCode==13) {
            myFormSub();
        }
    }

    function myFormSub() {
        var username = $("#username").val();
        if (username.length < 2) {
            $(".form-tip").html("最少2个字符!").show();
            $('#username').parent().addClass("g-inputTxt-error");
            return false;
        }
        else if (username.replace(/[^\x00-\xff]/g, "**").length > 24) {
            $(".form-tip").html("请不要超过24个字符!").show();
            $('#username').parent().addClass("g-inputTxt-error");
            return false;
        }
        else {
            $('#username').parent().removeClass("g-inputTxt-error");
        }

        $.ajax({
            url: "/oauth/yxbind/client",
            data: {username: username},
            type: 'POST',
            async: false,
            dataType: "JSON",
            success: function (res) {
                if (res.err > 0) {
                    $('#username').parent().addClass("g-inputTxt-error");
                    $(".form-tip").html(res.msg).show();
                    return false;
                } else {
                    location.href = res.forward;
                }
            },
            timeout: 3000
        });
    }
</script>
</body>
</html>