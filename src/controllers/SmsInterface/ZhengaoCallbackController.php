<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：ZhengaoCallbackController.php
 * 摘    要：正奥回调
 * 修改日期：2016.03.02
 * */

namespace SmsInterface;

use Controller;

class ZhengaoCallbackController extends Controller
{

    /**
     * 查看该渠道商是否具有访问权限
     * */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 发短信回调接口状态同步
     */
    public function actionCallbackRynsStatus()
    {
        $data = file_get_contents('php://input');
        $dataJson = json_decode($data, true);
        if (is_array($dataJson))
        {
            $dataJson['TimeStamp'] = date('Y-m-d H:i:s');
            $data = json_encode($dataJson);
        }
        $redisAction = \RedisAction::connect();
        $isTrue = $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, $data);

        if ($isTrue)
        {
            echo 'success';
            exit;
        }
        else
        {
            echo 'fail';
            exit;
        }
    }

    /**
     * 语音状态码 同步
     * */
    public function actionCallbackVoiceStatus()
    {
        $postData = file_get_contents('php://input');
        if (empty($postData))
        {
            echo '<?xml version="1.0" encoding="UTF-8"?><response><retcode>1</retcode></response>';
            exit;
        }
        $RedisAction = \RedisAction::connect();
        $xml = (array)simplexml_load_string($postData);
        $data = array(
            'Mobile' => $xml['called'],
            'Msg_Id' => $xml['callid'],
            'Status' => $xml['state'] === '0' ? 'DELIVRD' : $xml['state'],   //0：正常通话；1：被叫未接听；2：被叫拒接；3：外呼失败
        );
        $isTrue = $RedisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($data));
        header("Content-type:text/xml");
        if ($isTrue)
        {
            echo '<?xml version="1.0" encoding="UTF-8"?><response><retcode>0</retcode></response>';
            exit;
        }
        else
        {
            echo '<?xml version="1.0" encoding="UTF-8"?><response><retcode>1</retcode></response>';
            exit;
        }
    }

    /**
     * 用户回复内容回调
     * */
    public function actionCallbackSmsUplinkData()
    {
        $data = file_get_contents('php://input');
        $data = json_decode($data);
        if (!empty($data) && is_object($data))
        {
            $filterArr = array(
                't',
                'td',
                '退',
                '退订',
            );
            $isUnsub = false;
            $returnmsg = \EncodingAction::transcoding($data->Content);
            foreach ($filterArr as $filterArrInfo)
            {

                $unsubStatus = stripos($returnmsg, $filterArrInfo);
                if ($unsubStatus !== false)
                {
                    $isUnsub = true;
                    continue;
                }
            }

            $SmsQueryLogModel = \Admin\SmsQueryLogModel::getInstance();
            $SmsLogModel = SmsLogModel::getInstance();
            $channelData = array(
                'msgId'      => microtime(true) * 10000,
                'phone'      => $data->Mobile,
                'is_unsub'   => $isUnsub,
                'providerId' => 7,
                'message'    => $returnmsg,
            );
            $isTrue = $SmsLogModel->addSmsReceiptInfo($channelData);
            if ($isTrue)
            {
                echo 'success';
                exit;
            }
            else
            {
                echo 'fail';
                exit;
            }
        }
    }
}
