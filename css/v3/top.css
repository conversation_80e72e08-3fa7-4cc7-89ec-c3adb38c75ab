/*公共顶部*/
.pub_top,.pub_oper .btn_p,.pub_oper .btn_n,.pub_nav li a:hover,.pub_nav li.current a{background:url(../images/global.png?t=20121220) no-repeat;}
.pub_top{position:relative;height:32px;background-position:0 0;background-repeat:repeat-x;*zoom:1; z-index:999;}
.pub_top .logo{ width:132px; height:28px; float:left;margin:3px 0 0 8px;display:inline;}
.pub_top .logo img{ width:132px; height:28px; display:block;}
.pub_nav{position:absolute;top:0;left:164px;}
.pub_nav ul{overflow:hidden;*zoom:1;}
.pub_nav li{float:left;height:32px;margin-left:-1px;display:inline;}
.pub_nav li a{display:inline-block;padding:0 8px;height:32px;overflow:hidden;text-align:center;line-height:32px;color:#ffffff;font-size:14px;font-family:"\5FAE\8F6F\96C5\9ED1","\9ED1\4F53";background:url(../images/top_cut.png) no-repeat right 12px;}
.pub_nav li.last a{background-image:none;}
.pub_nav li.current a,.pub_nav li a:hover{text-decoration:none;color:#ffffff;background-image:url(../images/global.png?t=20121220);background-position:0 -130px;}
.pub_nav li a span{display:inline-block;cursor:pointer;}
.pub_nav li.has_sub a:hover,.pub_nav li.has_sub.current a:hover,.pub_nav li.has_sub.current a{background-position:0 -65px;}
.pub_nav li.has_sub span{padding-right:12px;background:url(../images/arrow.png) no-repeat right 52%;}
.pub_sub_nav{padding:0 0 0 15px;background:#5f7a9f url(../images/sub_nav_bg_1220.png) repeat-x left bottom; z-index:999; position:relative;}
.pub_sub_nav .sub_nav_item{padding:2px 0;overflow:hidden;*zoom:1;}
.pub_sub_nav a{float:left;padding:0 10px;height:28px;line-height:28px;color:#ffffff !important;white-space:nowrap;}
.pub_sub_nav em{float:left;margin:5px 3px 0 3px;color:#b3c1d6;font-family:Arial;font-size:13px;}
.pub_sub_nav a:hover,.pub_sub_nav a.current{background-color:#8fa3bf;text-decoration:none;}
.pub_oper{float:right;margin-top:4px;}
.pub_oper .btn_p,.pub_oper .btn_n{float:left;width:33px;height:23px;overflow:hidden;}
.pub_oper .btn_p{width:34px;background-position:0 -233px;}
.pub_oper .btn_n{background-position:-34px -233px;}
.pub_oper .btn_p:hover{background-position:0 -271px;}
.pub_oper .btn_n:hover{background-position:-34px -271px;}
.pub_oper .btn_p_no,.pub_oper .btn_p_no:hover{background-position:0 -195px;cursor:default;}
.pub_oper .btn_n_no,.pub_oper .btn_n_no:hover{background-position:-34px -195px;cursor:default;}
.pub_oper .welcome{float:left;margin:2px 5px 0 10px;color:#e5f0ff; font-size:12px;}
.pub_oper .welcome a{margin-left:2px;color:#b1c2da;}

/*2016-03-03 pan不使用框架的css添加*/
html,body{ width:100%; height:100%; overflow:hidden;}
html{ _height:auto; _padding:100px 0 500px;}
.pub_top,.leftNav,.mainWrap{ position:absolute; left:0;}
.pub_top{ width:100%;}
.leftNav,.mainWrap{ top:32px; bottom:0px; _height:100%; overflow:auto;}
.pub_top{top:0;}
.mainWrap{ _position:relative; left:164px; right:0; _top:0; _left:0; _margin-left:164px;}
.leftNav{ top:32px;}