<!DOCTYPE HTML>
<html>
    <head>
        <meta charset="gb2312">
        <title>加速浏览器弹窗</title>
        <link href="/chrome/css/jiasu0313.css" rel="stylesheet" type="text/css">
        <style type="text/css">
            body > :not(.wrap){display:none;}
        </style>
    </head>
    <body>
        <div class="wrap">
            <div class="container">
                <a id="closeA" tabindex="-1" class="close" href="#"></a>
                <div class="content">
                    <div class="mod_box">
                        <div class="hd">注册2345帐号</div>
                        <div class="bd">
                            <form onsubmit="return form_submit();">
                                <ul class="form_login form_register">
                                    <li>
                                        <div class="con pos"><input id="username" class="inp suggest" type="text" placeholder="手机号/用户名/邮箱" autocomplete="off"/><p id="usernameMsg" class="msg"></p></div>
                                    </li>
                                    <li>
                                        <div class="con"><input id="password" class="inp" type="password" placeholder="密码"/><p id="passwordMsg" class="msg"></p></div>
                                    </li>
                                    <li>
                                        <div class="con"><input id="repassword" class="inp" type="password" placeholder="重复密码"/><p id="repasswordMsg" class="msg"></p></div>
                                    </li>
                                    <li class="yz_area" id="yzm_pic">
                                        <div class="con">
                                            <input id="validate" class="inp inp_s" type="text" maxlength="4" placeholder="输入计算结果"/>
                                            <img id="validateImg" tabindex="-1" class="pic" src="/randCaptcha.php" />
                                            <a id="validateA" tabindex="-1" href="#">看不清，换一张</a>
                                            <p id="validateMsg" class="msg"></p>
                                        </div>
                                    </li>
                                    <li class="yz_area" id="yzm_phone" style="display: none;">
                                        <div class="con">
                                            <input id="validatePhone" class="inp inp_s" type="text" maxlength="6" placeholder="输入验证码"/>
                                            <button id="sendBtn" class="btn-yzm">获取语音验证码</button>
                                            <div id="sendTitle" class="tips-voice">
                                                请接听系统电话，并填写验证码
                                                <a id="sendSms" class="spec" href="#">【获取短信验证码】</a>
                                            </div>
                                            <p id="validatePhoneMsg" class="msg"></p>
                                        </div>
                                    </li>
                                    <li>
                                        <label class="remember agree"><input id="agree" class="checkbox" type="checkbox" checked="checked"/>我同意<a tabindex="-1" href="/chrome/licence.htm">《服务协议》</a>、<a tabindex="-1" href="/chrome/declare.htm">《隐私政策》</a></label>
                                    </li>
                                    <li class="btn_area"><input id="submitButton" type="submit" class="btn_main" value="注 册"/></li>
                                    <li id="defaultMsg" class="msg"></li>
                                    <li class="desc">已有2345帐号，<a id="signA" tabindex="-1" href="#">立即登录&nbsp;&gt;&gt;</a></li>
                                </ul>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script type="text/javascript" src="/chrome/js/login.js"></script>
        <script type="text/javascript" src="/chrome/js/limitTel.js"></script>
        <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
        <script type="text/javascript" src="/js/jquery.autocomplete.min.js"></script>
        <script type="text/javascript">
                                var defaultBtnType = 'voice';
                                var defaultBtnTitle = '获取语音验证码';
                                var regType = 'username';
                                var readyToSubmit = true;
                                function cc(a) {
                                    var b = arguments,
                                            web = "ajax93",
                                            a2,
                                            i1 = document.cookie.indexOf("uUiD="),
                                            i2;
                                    if (b.length > 1)
                                        web = b[1];
                                    if (i1 != -1) {
                                        i2 = document.cookie.indexOf(";", i1);
                                        a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
                                    }
                                    if (!a2) {
                                        a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                                        document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
                                    }
                                    if (a.length > 0) {
                                        var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                                        $.getScript(c);
                                    }
                                    return true;
                                }
                                function showError(id, msg) {
                                    $("#" + id + "Msg").html(msg);
                                    $("#submitButton").removeAttr("disabled");
                                    $("#submitButton").val("注 册");
                                }
                                function hideError(id) {
                                    $("#" + id + "Msg").html('');
                                }
                                function checkUser(n)
                                {
                                    var username = $("#username").val();
                                    if (/[^\u4E00-\u9FA5\w_@\.]/.test(username))
                                    {
                                        showError('username', '请输入汉字，字母，数字');
                                        return false;
                                    }
                                    if (username.length < 2)
                                    {
                                        showError('username', '用户名最少2个字符');
                                        return false;
                                    }
                                    if (username.length > 24)
                                    {
                                        showError('username', '用户名请不要超过24个字符');
                                        return false;
                                    }
                                    if (/^1[0123456789]\d{9}$/.test(username)) {
                                        regType = 'phone';
                                        $("#yzm_pic").hide();
                                        $("#yzm_phone").show();
                                        if (username.substr(0, 5) in limitTel || username.substr(0, 6) in limitTel || username.substr(0, 7) in limitTel) {
                                            defaultBtnType = 'sms';
                                            defaultBtnTitle = '获取短信验证码';
                                            $("#sendTitle").hide();
                                            if (!$("#sendBtn").attr("disabled")) {
                                                $("#sendBtn").text('获取短信验证码');
                                            }
                                        } else {
                                            defaultBtnType = 'voice';
                                            defaultBtnTitle = '获取语音验证码';
                                            $("#sendTitle").show();
                                            if (!$("#sendBtn").attr("disabled")) {
                                                $("#sendBtn").text('获取语音验证码');
                                            }
                                        }
                                    } else if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username)) {
                                        regType = 'email';
                                        $("#yzm_phone").hide();
                                        $("#yzm_pic").show();
                                    } else {
                                        regType = 'username';
                                        $("#yzm_phone").hide();
                                        $("#yzm_pic").show();
                                    }
                                    if (regType == 'email') {
                                        jQuery.ajax({
                                            url: "/api/check/jsonp",
                                            data: 'type=' + regType + '&value=' + username + '&status=0',
                                            async: false,
                                            dataType: 'jsonp',
                                            jsonp: 'callback',
                                            success: function(response) {
                                                err = response;
                                            },
                                            error: function(jqXHR, textStatus, errorThrown) {
                                                console.log(jqXHR.status);
                                            },
                                            timeout: 3000
                                        });
                                    } else {
                                        jQuery.ajax({
                                            url: "/api/check/jsonp",
                                            data: 'type=' + regType + '&value=' + username,
                                            async: false,
                                            dataType: 'jsonp',
                                            jsonp: 'callback',
                                            success: function(response) {
                                                err = response;
                                            },
                                            error: function(jqXHR, textStatus, errorThrown) {
                                                console.log(jqXHR.status);
                                            },
                                            timeout: 3000
                                        });
                                    }
                                    if (typeof err === "undefined")
                                    {
                                        showError('username', '这个2345帐号不适合您，换一个吧');
                                        return false;
                                    }
                                    if (regType == 'username') {
                                        if (err == 1)
                                        {
                                            if (!$("#usernameMsg").data('login')) {
                                                $("#usernameMsg").data('login', true);
                                                showError('username', '此帐号已被注册，请<a tabindex="-1" href="#" class="blue" onclick="chrome.sync.showLoginDlg();return false;">登录</a>或重新输入');
                                            }
                                            return false;
                                        }
                                        else if (err == 2)
                                        {
                                            showError('username', '这个2345帐号不适合您，换一个吧');
                                            return false;
                                        }
                                    } else {
                                        if (err == 1)
                                        {
                                            showError('username', '这个2345帐号不适合您，换一个吧');
                                            return false;
                                        }
                                        else if (err == 2 || err == 3)
                                        {
                                            if (!$("#usernameMsg").data('login')) {
                                                $("#usernameMsg").data('login', true);
                                                showError('username', '此' + (regType == 'email' ? '邮箱' : '手机号') + '已被注册，请<a tabindex="-1" href="#" class="blue" onclick="chrome.sync.showLoginDlg();return false;">登录</a>或重新输入');
                                            }
                                            return false;
                                        }
                                    }
                                    if (username != username.toLowerCase())
                                    {
                                        showError('username', '登录区分大小写，请牢记您的2345帐号');
                                    }
                                    $("#usernameMsg").data('login', false);
                                    hideError("username");
                                    hideError("validate");
                                    hideError("validatePhone");
                                    return true;
                                }
                                function checkPass()
                                {
                                    var pass = $("#password").val();
                                    if (pass.length < 6)
                                    {
                                        showError('password', '密码最少6个字符');
                                        return false;
                                    }
                                    if (pass.length > 16)
                                    {
                                        showError('password', '密码最多16个字符');
                                        return false;
                                    }
                                    if (pass == $('#username').val())
                                    {
                                        showError('password', '密码不能与2345帐号一致，请重新输入');
                                        return false;
                                    }
                                    if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
                                    {
                                        var score = 0;
                                    }
                                    else
                                    {
                                        var score = passwordGrade(pass);
                                    }
                                    if (score <= 10)
                                    {
                                        showError('password', '密码强度太弱了');
                                        $('#pwd_strength').val(1);
                                        return false;
                                    }
                                    hideError("password");
                                    return true;
                                }
                                function checkPwdStrength() {
                                    var pass = $("#password").val();
                                    if (pass.length < 6)
                                    {
                                        return false;
                                    }
                                    if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
                                    {
                                        var score = 0;
                                    }
                                    else
                                    {
                                        var score = passwordGrade(pass);
                                    }
                                    if (score <= 10)
                                    {
                                        $('#pwd_strength').val(1);
                                    }
                                    else if (score >= 11 && score <= 20)
                                    {
                                        $('#pwd_strength').val(2);
                                    }
                                    else if (score >= 21 && score <= 30)
                                    {
                                        $('#pwd_strength').val(3);
                                    }
                                    else
                                    {
                                        $('#pwd_strength').val(4);
                                    }
                                }
                                function checkPassSame(pass)
                                {
                                    var first = pass.substring(0, 1);
                                    var exp = new RegExp('^' + first + '+$');
                                    if (exp.test(pass))
                                    {
                                        return false;
                                    }

                                    if (first == 'a' || first == 'A')
                                    {
                                        f = pass.charCodeAt(0);
                                        for (i = 1; i < pass.length; i++)
                                        {
                                            tmp = pass.charCodeAt(i);
                                            if (tmp - f != i)
                                            {
                                                return true;
                                            }
                                        }
                                        return false;
                                    }
                                    return true;
                                }
                                function passwordGrade(pwd) {
                                    var score = 0;
                                    var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
                                    var repeatCount = 0;
                                    var prevChar = '';
                                    //check length
                                    var len = pwd.length;
                                    score += len > 18 ? 18 : len;
                                    //check type
                                    for (var i = 0, num = regexArr.length; i < num; i++) {
                                        if (eval('/' + regexArr[i] + '/').test(pwd))
                                            score += 4;
                                    }
                                    //bonus point
                                    for (var i = 0, num = regexArr.length; i < num; i++) {
                                        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
                                            score += 2;
                                        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
                                            score += 2;
                                    }
                                    //deduction
                                    for (var i = 0, num = pwd.length; i < num; i++) {
                                        if (pwd.charAt(i) == prevChar)
                                            repeatCount++;
                                        else
                                            prevChar = pwd.charAt(i);
                                    }
                                    score -= repeatCount * 1;
                                    return score;
                                }
                                function checkRepass()
                                {
                                    if ($("#repassword").val() == "") {
                                        showError('repassword', '重复密码不能为空');
                                        return false;
                                    }
                                    if ($("#repassword").val() != $("#password").val())
                                    {
                                        showError('repassword', '两次输入密码不一致');
                                        return false;
                                    }
                                    hideError("repassword");
                                    return true;
                                }
                                function checkValidate()
                                {
                                    var val = $.trim($("#validate").val());
                                    if (val == '')
                                    {
                                        showError('validate', '请输入计算结果');
                                        return false;
                                    }
                                    $.ajax('/api/check', {
                                        type: "POST",
                                        data: 'type=validate&val=' + val,
                                        async: false,
                                        success: function(response) {
                                            if (response == 1) {
                                                err = 0;
                                            } else {
                                                err = 1;
                                            }
                                        }
                                    });
                                    if (err)
                                    {
                                        showError('validate', '您输入的验证码错误');
                                        $('#validate').val("");
                                        $('#validateImg').attr({src: '/randCaptcha.php?' + new Date()});
                                        return false;
                                    }
                                    hideError("validate");
                                    return true;
                                }
                                function checkAll()
                                {
                                    if (!checkUser(2))
                                    {
                                        $("#username").focus();
                                        return false;
                                    }
                                    if (!checkPass())
                                    {
                                        $("#password").focus();
                                        return false;
                                    }
                                    if (!checkRepass())
                                    {
                                        $("#repassword").focus();
                                        return false;
                                    }
                                    if (regType == 'phone') {
                                        if (!checkCode())
                                        {
                                            $("#validatePhone").focus();
                                            return false;
                                        }
                                    } else {
                                        if (!checkValidate())
                                        {
                                            $("#validate").focus();
                                            return false;
                                        }
                                    }
                                    if (!$("#agree").is(':checked'))
                                    {
                                        showError('default', '请同意2345服务协议');
                                        return false;
                                    }
                                    hideError("default");
                                    return true;
                                }
                                function form_submit() {
                                    if ($("#submitButton").attr("disabled")) {
                                        return false;
                                    }
                                    if (checkAll()) {
                                        $("#submitButton").attr("disabled", "disabled");
                                        $("#submitButton").val("注册中...");
                                        cc("reg_all");
                                        $.ajax({
                                            type: "POST",
                                            url: "/chrome/reg",
                                            data: "username=" + $("#username").val() + "&password=" + $("#password").val() + (regType == 'phone' ? ("&validate=" + $("#validatePhone").val()) : ("&validate=" + $("#validate").val())),
                                            success: function(data) {
                                                data = eval("(" + data + ")");
                                                switch (data["err"]) {
                                                    case 0:
                                                        cc("reg_success");
                                                        chrome.sync.onLogin($("#username").val(), "", 0, "", data["uid"], data["sec"], data['passid'], data['token']);
                                                        chrome.sync.setLoginUserInfo($("#username").val(), MD5($("#password").val()));
                                                        chrome.sync.setIsAutoLogin(true);
                                                        chrome.sync.closeDlg();
                                                        break;
                                                    case 1:
                                                        cc("reg_fail");
                                                        var msgs = data['msg'].split("@");
                                                        showError(msgs[0], msgs[1]);
                                                        $('#validateImg').attr({src: '/randCaptcha.php?' + new Date()});
                                                        break;
                                                    case 2:
                                                        cc("reg_fail");
                                                        chrome.sync.openUrl(data["url"], "");
                                                        chrome.sync.closeDlg();
                                                        break;
                                                }
                                            },
                                            error: function(XMLHttpRequest, textStatus, errorThrown) {
                                                if (XMLHttpRequest.readyState == 0) {
                                                    showError('default', '您的网络情况异常，请检查网络配置');
                                                } else {
                                                    showError('default', '无法连接到2345通行证，请稍后再试');
                                                }
                                            }
                                        });
                                    }
                                    return false;
                                }
                                function sendCode(codeType) {
                                    hideError("validatePhone");
                                    if (!checkUser(1)) {
                                        return false;
                                    }
                                    $.post("/chrome/reg/sendPhoneCode/" + codeType, {'phone': $("#username").val()}, function(data) {
                                        if (data == '300.0') {
                                            showError("validatePhone", '请输入正确的手机号！')
                                        } else if (data == '400.0') {
                                            showError("validatePhone", '发送频繁，请稍后再试！');
                                        } else if (data == '500.0') {
                                            showError("validatePhone", '手机号验证次数超出限制！');
                                        }
                                    });
                                    $("#sendBtn").text(defaultBtnTitle);
                                    showSendMsg(60);
                                }
                                function showSendMsg(times) {
                                    if (times > 1) {
                                        $("#sendBtn").attr("disabled", "disabled");
                                        $("#sendBtn").text('重新发送（' + times + '）');
                                        times--;
                                        setTimeout(function() {
                                            showSendMsg(times);
                                        }, 1000);
                                    } else {
                                        $("#sendBtn").text(defaultBtnTitle);
                                        $("#sendBtn").removeAttr('disabled');
                                    }
                                }
                                function checkCode() {
                                    var codeErr = true;
                                    var phone = $.trim($("#username").val());
                                    var code = $.trim($("#validatePhone").val());
                                    if (phone == '') {
                                        showError('username', '请输入汉字，字母，数字');
                                        return false;
                                    }
                                    if (code == '') {
                                        showError("validatePhone", '请输入验证码！');
                                        return false;
                                    }
                                    $.ajax({
                                        type: "POST",
                                        url: "/chrome/reg/checkPhoneCode",
                                        async: false,
                                        data: "phone=" + phone + "&code=" + code,
                                        success: function(data) {
                                            if (data == '200.0') {
                                                codeErr = false;
                                            } else if (data == '400.0') {
                                                showError("validatePhone", '验证码错误！');
                                            }
                                        }
                                    });
                                    return !codeErr;
                                }
                                $(document).ready(function() {
                                    document.ondragstart = function() {
                                        return false;
                                    };
                                    $(this).bind("contextmenu", function(event) {
                                        return $(event.target).is("input") && ($(event.target).attr("type") == "text" || $(event.target).attr("type") == "password");
                                    });
                                    chrome.sync.setDlgSize(684, 422);
                                    $("#username").focus();
                                    $("input.suggest").keydown(function(event) {
                                        if (event.keyCode == 13) {
                                            if (!readyToSubmit) {
                                                readyToSubmit = true;
                                                return false;
                                            }
                                        }
                                    });
                                    $("#closeA").click(function() {
                                        if (!$("#submitButton").attr("disabled")) {
                                            chrome.sync.closeDlg();
                                        }
                                        return false;
                                    });
                                    $("#username").blur(function() {
                                        checkUser(1);
                                    });
                                    $("#password").keydown(function() {
                                        checkPwdStrength();
                                    }).blur(function() {
                                        checkPass();
                                    });
                                    $("#repassword").blur(function() {
                                        checkRepass();
                                    });
                                    $("#validate").blur(function() {
                                        checkValidate();
                                    });
                                    $("#validateImg,#validateA").click(function() {
                                        $('#validateImg').attr({src: '/randCaptcha.php?' + new Date()});
                                        return false;
                                    });
                                    $("#sendBtn").click(function() {
                                        if (!$("#sendBtn").attr("disabled")) {
                                            sendCode(defaultBtnType);
                                        }
                                        return false;
                                    });
                                    $("#sendSms").click(function() {
                                        if (!$("#sendBtn").attr("disabled")) {
                                            sendCode('sms');
                                        }
                                        return false;
                                    });
                                    $("#signA").click(function() {
                                        chrome.sync.showLoginDlg();
                                        return false;
                                    });
                                    $("#username").result(function(data, value) {
                                        $(this).val(value.toString().replace(/<[^>]*?>/g, ''));
                                        checkUser(1);
                                    });
                                });
        </script>
    </body>
</html>
