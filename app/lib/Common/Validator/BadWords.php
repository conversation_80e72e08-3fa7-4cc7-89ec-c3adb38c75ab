<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：BadWords.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：09-20, 2016
 */

namespace Common\Validator;

use Common\Msg\ResponseMsg;

class BadWords extends Base
{

    public static function validator($value, array $args)
    {
        $res = array(ResponseMsg::SUCCESS, "合法内容");
        loadAction('ContentAudit');
        $nicknameAuditStatus = \ContentAuditAction::textAudit("login", $value);
        if (!in_array($nicknameAuditStatus, [\ContentAuditAction::CONT_AUDIT_PASS])) {
            $res = ResponseMsg::getCodeMsgFormat(ResponseMsg::PARAMS_FORMAT, $args[0], "不能包含敏感词汇");
        }

        return $res;
    }

}
