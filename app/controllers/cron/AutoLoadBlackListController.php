<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2016-8-16
 * Time: 11:27
 */
includeBase('CronController');
/**
 * 自动添加黑名单
 */
class AutoLoadBlackListController extends CronController
{
    /**
     * 自动载入黑名单
     */
    public function actionIndex ()
    {
        $redis = RedisEx::getInstance();
        //查询登录的Ip列表
        $ips = $redis->zRevRange("loginSuspectIps", 0, -1, true);

        $dbConfig = Config::get("database");
        $pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        foreach ($ips as $ip => $num)
        {
            //如果总数小于100个,则退出当前循环
            if ( $num < 100 )
            {
                continue;
            }
            //查询这个ip下存放的passid
            $passids = $redis->sMembers("LSP:$ip");
            $sizeOfCount = sizeof($passids);
            if ($passids && $sizeOfCount )
            {
                //切割数组
                $passList = array_chunk($passids,500 ,true );
                foreach ($passList as $passInfo)
                {
                    $result = loadModel('member')->getNewMemberByPassIdList($passInfo, ['reg_ip', 'id']);
                    foreach ($result as $row)
                    {
                        if (filter_var($row["reg_ip"], FILTER_VALIDATE_IP))
                        {
                            //如果redis的列表里面存在则不加入
                            if ( ! $redis->sIsMember('REG:BLACK:IP'  , $row["reg_ip"])  )
                            {
                                $redis->sAdd("REG:BLACK:IP", $row["reg_ip"]);
                            }
                            $pdo->duplicate("reg_suspect_ip", array("ip" => $row["reg_ip"], "status" => 1,'op_user'=>'','last_update' => date('Y-m-d H:i:s') ));
                            //$redis->sAdd("REG:BLACK:IP:DATE", $ip.'|'.date('Y-m-d H:i:s'));
                            $redis->sRem("LSP:{$ip}", $row['id']);
                        }
                    }
                }
            }
            $redis->zRem('loginSuspectIps', $ip);
        }
    }

    /**
     * 更新IP黑名单时间
     */
    public function actionUpdateBlackIpDate()
    {
        $redis = RedisEx::getInstance();
        $blackDate = $redis->sMembers('REG:BLACK:IP:DATE');
        $dbConfig = Config::get("database");
        $pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        foreach ($blackDate as $blackInfo)
        {
            list($ip , $date ) = explode('|',$blackInfo);
            $pdo->duplicate("reg_suspect_ip", array("ip" => $ip, "status" => 1,'op_user'=>'','last_update' => $date ));
            $redis->sRem('REG:BLACK:IP:DATE', $blackInfo);
        }
    }

    /**
     * 定时存储访问次数过多ip黑名单
     *  *\/3 * * * * cd /opt/case/login.2345.com; php cron.php cron/auto_load_black_list/store_visit_forbidden_ips
     */
    public function actionStoreVisitForbiddenIps()
    {
        $cacheKeys = "visitForbidenIps";
        $redis = RedisEx::getInstance();
        $dbConfig = Config::get("database");
        $pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);

        $data = $redis->zRevRange($cacheKeys, 0, -1, true);
        foreach ($data as $ip => $time)
        {
            $ip = str_replace("_", ".", $ip);
            if (filter_var($ip, FILTER_VALIDATE_IP))
            {
                if (!$redis->sIsMember('REG:BLACK:IP', $ip))
                {
                    $redis->sAdd("REG:BLACK:IP", $ip);
                    $pdo->duplicate("reg_suspect_ip", array("ip" => $ip, "status" => 1 ,'op_user'=>'','last_update' => date('Y-m-d H:i:s')  ));
                }
            }
        }
    }

    /**
     * 加载基础库
     */
    public function actionRunBaseBlankIp()
    {
        $redis = RedisEx::getInstance();
        $dbConfig = Config::get("database");
        $pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        $sql = "SELECT * FROM `reg_suspect_ip` ";
        $result = $pdo->findAll($sql, array() );
        foreach ($result as $info )
        {
            //如果redis的列表里面存在则不加入
            if ( ! $redis->sIsMember('REG:BLACK:IP'  , $info["ip"])  )
            {
                $redis->sAdd("REG:BLACK:IP", $info["ip"] );
            }
        }
    }
}