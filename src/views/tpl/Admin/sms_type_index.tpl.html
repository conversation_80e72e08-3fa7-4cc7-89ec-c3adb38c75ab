<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台-类型配置</title>
    <link rel="stylesheet" href="/style/css/global.css">
    <script src="/style/js/jquery-1.8.3.min.js"></script>
    <script src="/style/js/local.js"></script>
</head>
<body>
<div class="mainWrap">
    <!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a> > <span>类型配置</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="javascript:">类型配置</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->

    <div class="funList mt20 clearfix">
        <a class="styleBtn_s styleBtnBlue left "style="margin-right: 8px" href="javascript:onclick=saveDisplayOrder();">更新排序</a>
        <a href="javascript:" class="styleBtn_s styleBtnBlue left addTypeConfig">添加类型配置</a>
    </div>
    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>短信类型</th>
                        <th>序号</th>
                        <th>类型名称</th>
                        <th>分流占比</th>
                        <th>限制条数</th>
                        <th>移动短信通道</th>
                        <th>电信短信通道</th>
                        <th>联通短信通道</th>
                        <!--<th>配置时间</th>-->
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->

        <table class="tabMod forFixed mtb20" cellpadding="0" cellspacing="0">
            <tr>
                <th><a href="javascript:onclick=sort('id')">短信类型</a></th>
                <th style="width:60px"><a href="javascript:onclick=sort('display_order')">序号(大号靠前)</a></th>
                <th>类型名称</th>
                <th>分流占比</th>
                <th>限制条数</th>
                <th>移动短信通道</th>
                <th>电信短信通道</th>
                <th>联通短信通道</th>
                <!--<th>配置时间</th>-->
            </tr>
            {{foreach from=$pageArray.smsTypeList item=smsTypeInfo}}
            <tr class="parentType">
                <td>{{$smsTypeInfo.sms_type}}</td>
                <td><input type="text" name="displayOrder[{{$smsTypeInfo.sms_type}}]" value="{{$smsTypeInfo.display_order}}" style="width:30px;text-align:center"></td>
                <td><a href="javascript:void(0);" class="smsTypeNameClass" typeId="{{$smsTypeInfo.id}}">{{$smsTypeInfo.sms_name}}</a></td>
                <td>{{if isset($smsTypeInfo.child)}}{{$smsTypeInfo.percent}}{{/if}}</td>
                <td>{{if !empty($smsTypeInfo.restrict_nums)}} {{$smsTypeInfo.restrict_nums}} {{/if}}</td>
                <td>{{$smsTypeInfo.mobile_provider_id}}-{{$smsTypeInfo.mobileProvider}}</td>
                <td>{{$smsTypeInfo.telecom_provider_id}}-{{$smsTypeInfo.telecomProvider}}</td>
                <td>{{$smsTypeInfo.unicom_provider_id}}-{{$smsTypeInfo.unicomProvider}}</td>
                <!--<td>{{$smsTypeInfo.createdate}}</td>-->
            </tr>
            {{if isset($smsTypeInfo.child)}}
            {{foreach from=$smsTypeInfo.child item=smsTypeChildInfo}}
            <tr class="lightYellowGreenBg">
                <td></td>
                <td></td>
                <td>{{$smsTypeChildInfo.sms_name}}</td>
                <td>{{$smsTypeChildInfo.percent}}</td>
                <td>{{if !empty($smsTypeChildInfo.restrict_nums)}}{{$smsTypeChildInfo.restrict_nums}}{{/if}}</td>
                <td>{{$smsTypeChildInfo.mobile_provider_id}}-{{$smsTypeChildInfo.mobileProvider}}</td>
                <td>{{$smsTypeChildInfo.telecom_provider_id}}-{{$smsTypeChildInfo.telecomProvider}}</td>
                <td>{{$smsTypeChildInfo.unicom_provider_id}}-{{$smsTypeChildInfo.unicomProvider}}</td>
                <!--<td>{{$smsTypeChildInfo.createdate}}</td>-->
            </tr>
            {{/foreach}}
            {{/if}}
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
    </div><!--contentWrap end-->
    <!--主体内容 end-->
</div><!--mainWrap end-->


<!--透明遮罩-->
<div class="boxMask" style=" display:none;">透明遮罩层</div>

<!--通道信息 弹窗 开始-->
<div class="boxMod w740" style=" display:none;">
    <div class="boxTop"><h2 class="boxTit">类型配置</h2><a class="BoxClose" href="javascript:">关闭按钮</a></div>
    <div class="tabFrom mtb20" style="overflow-y: scroll; max-height: 400px">
        <form id="smsTypeForm">
            <input type="hidden" name="act" value="add"/>
        </form>

    </div><!--tabFrom end-->
    <div style="text-align: center;margin-bottom: 20px;">
        <a class="styleBtnBlue tabsave mr13" id="addChildType" href="javascript:">增加子类型</a>
        <a class="styleBtnBlue tabsave" id="addSubmitFromButton" href="javascript:">确定</a>
        <a class="styleBtnBlue tabsave ml13" id="closeButton" href="javascript:">关闭</a>
    </div>

    <div id="boxTemplate" style="display: none;">
        <table class="tabFromMod" id="smsTypeTable" cellpadding="0" cellspacing="0" style="padding: 5px;">
            <tr>
                <td class="tabLeftTit" id="smsTypeLabel">类型编号：</td>
                <td>
                    <span class="smsTypeSpan"></span>
                    <input class="sms_type" type="hidden"/>
                    <input class="sms_id" type="hidden"/>
                    <a class="delChildType noshow styleBtnBlue" style="float:right;margin-right: 20px;">删除子类型</a></td>
            </tr>
            <tr>
                <td class="tabLeftTit" id="smsNameLabel">类型名称：</td>
                <td><input class="tabInput sms_name" type="text" autocomplete="off"/></td>
            </tr>
            <tr>
                <td class="tabLeftTit">分流占比(0-100整数)：</td>
                <td><input class="tabInput percent" type="text" autocomplete="off"/></td>
            </tr>
            <tr>
                <td class="tabLeftTit">限制条数(0为无限制)：</td>
                <td><input class="tabInput restrict_nums" type="text" autocomplete="off"/></td>
            </tr>
            <tr>
                <td class="tabLeftTit">移动短信供应商：</td>
                <td>
                    <select class="tabSelect mobile_provider_id">
                        {{foreach from=$pageArray.OperatorList item=OperatorInfo }}
                        <option value="{{$OperatorInfo.id}}">{{$OperatorInfo.id}}-{{$OperatorInfo.provider_name}}</option>
                        {{/foreach}}
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tabLeftTit">电信短信供应商：</td>
                <td>
                    <select class="tabSelect telecom_provider_id">
                        {{foreach from=$pageArray.OperatorList item=OperatorInfo }}
                        <option value="{{$OperatorInfo.id}}">{{$OperatorInfo.id}}-{{$OperatorInfo.provider_name}}</option>
                        {{/foreach}}
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tabLeftTit">联通短信供应商：</td>
                <td>
                    <select class="tabSelect unicom_provider_id">
                        {{foreach from=$pageArray.OperatorList item=OperatorInfo }}
                        <option value="{{$OperatorInfo.id}}">{{$OperatorInfo.id}}-{{$OperatorInfo.provider_name}}</option>
                        {{/foreach}}
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tabLeftTit" rowspan="2">预警信息：</td>
                <td><textarea rows="2" cols="33" autocomplete="off" class="monitor_config"></textarea></td>
            </tr>
            <tr>
                <td>commitLimitRate-提交服务商成功率阈值；receiveLimitRate-项目提交成功率阈值；stepTime-样本时间跨度；offsetTime-回执等待时间</td>
            </tr>
        </table>
    </div>
</div><!--boxMod end-->
<!--通道信息 弹窗 结束-->


</body>
</html>

<script src="/style/js/tableFixed.js"></script>
<script src="/style/js/boxLocation.js"></script>
<script>
    var postArray = {};

    function sort(orderBy) {
        if (orderBy != 'id' && orderBy != 'display_order') {
            return;
        }

        var orderByType = "{{$pageArray.orderByType}}";
        orderByType = orderByType == 'asc' ? 'desc' : 'asc';

        var url = '/Admin/SmsType/Index?orderBy=' + orderBy + '&type=' + orderByType;
        window.location.href = url;
    }

    function saveDisplayOrder() {
        var data = $('input[name^="displayOrder"]').serialize();
        $.post('/Admin/SmsType/ChangeDisplayOrder', {displayOrder: data}, function (data) {
            alert(data.msg);
            window.location.reload();
        });
    }
    $('.smsTypeNameClass').click(function () {
        $('.boxMask').show();
        $('.boxMod').show();
        var type = $(this).attr('typeId');
        postArray = {'act': 'edit', 'typeId': type, 'data': {}};
        $.post('/Admin/SmsType/GetSmsTypeInfo', 'type=' + type, function (obj) {
            initConfig(obj.data);
        });
    });

    function initConfig(data) {
        $('#smsTypeForm').empty();
        data.forEach(function (value, index, array) {
            $('#smsTypeForm').append('<div typeid="' + value.id + '" class="typeDiv" style="padding: 10px 0;"></div>');
            var dataElement = $('div[typeid=' + value.id + ']');
            dataElement.append($('#boxTemplate').html());
            dataElement.find('.sms_type').val(value.sms_type);
            dataElement.find('.smsTypeSpan').html(value.id);
            dataElement.find('.sms_name').val(value.sms_name);
            dataElement.find('.percent').val(value.percent);
            dataElement.find('.mobile_provider_id').val(value.mobile_provider_id);
            dataElement.find('.telecom_provider_id').val(value.telecom_provider_id);
            dataElement.find('.unicom_provider_id').val(value.unicom_provider_id);
            dataElement.find('.sms_id').val(value.id);
            dataElement.find('.restrict_nums').val(value.restrict_nums);
            dataElement.find('.monitor_config').val(value.monitor_config);
            if (value.id != value.sms_type) {
                dataElement.find('#smsTypeLabel').html('子类型内部编号');
                dataElement.find('#smsNameLabel').html('子类型备注名');
                dataElement.find('.delChildType').css('display', 'inline-block');
                dataElement.find('.delChildType').click(function () {
                    dataElement.remove();
                });
            }
        });
        $('#addChildType').show();
        $(".boxMod").eq(0).css("margin-top", -($(".boxMod").eq(0).height() / 2));
    }

    $('#addSubmitFromButton').click(function () {
        var act = $('input[name="act"]').val();
        $.post('/Admin/SmsType/SetSmsTypeConfig?act=' + act, buildPostArray(), function (data) {
            if (data.status == 'P00001') {
                window.location = '/Admin/SmsType/Index';
            }
            else {
                alert(data.msg)
            }
        });
    });

    $('#addChildType').click(function () {
        var randomId = Math.floor(Math.random() * 10000);
        $('#smsTypeForm').append('<div typeid="newType_' + randomId + '" class="typeDiv" style="padding: 10px 0;"></div>');
        var dataElement = $('div [typeid=newType_' + randomId + ']');
        dataElement.append($('#boxTemplate').html());
        dataElement.find('.sms_id').val('0');
        dataElement.find('.sms_type').val('0');
        dataElement.find('.smsTypeSpan').html('新加子类型');
        dataElement.find('.percent').val(10);
        dataElement.find('.restrict_nums').val('0');
        dataElement.find('.monitor_config').val('{"stepTime":1200,"offsetTime":120,"commitLimitRate":0.90,"receiveLimitRate":0.90}');
        dataElement.find('#smsTypeLabel').html('子类型内部编号');
        dataElement.find('#smsNameLabel').html('子类型备注名');
        dataElement.find('.delChildType').css('display', 'inline-block');
        $(".boxMod").eq(0).css("margin-top", -($(".boxMod").eq(0).height() / 2));
        dataElement.find('.delChildType').click(function () {
            dataElement.remove();
        });
    });

    function buildPostArray() {
        $.each($('.typeDiv'), function (index, value, array) {
            console.log(value);
            var dataElement = $(value);
            postArray['data'][dataElement.attr('typeId')] = {
                'sms_type': dataElement.find('.sms_type').val(),
                'sms_name': dataElement.find('.sms_name').val(),
                'percent': dataElement.find('.percent').val(),
                'restrict_nums': dataElement.find('.restrict_nums').val(),
                'id': dataElement.find('.sms_id').val(),
                'mobile_provider_id': dataElement.find('.mobile_provider_id').val(),
                'telecom_provider_id': dataElement.find('.telecom_provider_id').val(),
                'unicom_provider_id': dataElement.find('.unicom_provider_id').val(),
                'monitor_config': dataElement.find('.monitor_config').val()
            };
        });
        return postArray;
    }

    $('.BoxClose, #closeButton').click(function () {
        $('.boxMask').hide();
        $('.boxMod').hide();
        $('#smsTypeForm').empty();
    });

    $('.addTypeConfig').click(function () {
        $('#addChildType').hide();
        $('#smsTypeForm').empty();
        $('#smsTypeForm').append('<div typeid="newParentType" class="typeDiv" style="padding: 10px 0;"></div>');
        var dataElement = $('div [typeid=newParentType]');
        dataElement.append($('#boxTemplate').html());
        postArray = {'act': 'add', 'data': {}};
        dataElement.find('.smsTypeSpan').html('新加主类型（创建后不可删除）');
        $(".boxMod").eq(0).css("margin-top", -($(".boxMod").eq(0).height() / 2));
        $('.boxMask').show();
        $('.boxMod').show();
    });
</script>