<?php

/**
 * Copyright (c) 2015,上海二三四五网络科技有限公司
 * 文件名称：RegController.php
 * 摘    要：客户端注册接口
 * 作    者：张小虎
 * 修改日期：2015.11.19
 */
class RegController extends Controller
{

    /**
     * 手机号注册
     */
    public function actionPhone()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        $mid = $_POST["mid"];
        $phone = $_POST['phone'];
        $verifyCode = $_POST["verify_code"];
        $password = trim($_POST['password']);
        $midArr = Config::get("mid");
        if (!array_key_exists($mid, $midArr))
        {
            die(json_encode(array(
                'code' => 501,
                'data' => '',
                'msg' => mb_convert_encoding('项目标识错误！', 'UTF-8', 'GBK')
            )));
        }
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            die(json_encode(array(
                'code' => 300,
                'data' => '',
                'msg' => mb_convert_encoding('请输入正确的手机号码！', 'UTF-8', 'GBK')
            )));
        }
        $phoneAction = loadAction("phone");
        if (!$phoneAction->checkRegCode($mid, $phone, md5($phone . $verifyCode)))
        {
            die(json_encode(array(
                'code' => 304,
                'data' => '',
                'msg' => mb_convert_encoding('手机验证码错误！', 'UTF-8', 'GBK')
            )));
        }
        $userClientIp = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        $regAction = loadAction('reg');

        // 客户端 注册
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        $source = \Common\Utils\Url::getStringParam("mid", "mobile", \Common\Utils\Url::POST);
        $result = $regAction->regPhone($phone, $password, $pwdStrength, $userClientIp, 'login.2345.com', 200, $source, $clientType);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            die(json_encode(array(
                'code' => 403,
                'data' => '',
                'msg' => 'error:' . $msgs[$states[0]][$states[1]]
            )));
        }
        else if ($states[0] == 300)
        {
            die(json_encode(array(
                'code' => $states[0] + $states[1],
                'data' => '',
                'msg' => mb_convert_encoding($msgs[$states[0]][$states[1]], 'UTF-8', 'GBK')
            )));
        }
        else if ($states[0] == 200)
        {
            $cookie = $result[1]['cookie'];
            die(json_encode(array(
                'code' => 200,
                'data' => array(
                    'I' => $cookie['I']
                ),
                'msg' => mb_convert_encoding('注册成功！', 'UTF-8', 'GBK')
            )));
        }
        else
        {
            die(json_encode(array(
                'code' => 500,
                'data' => '',
                'msg' => mb_convert_encoding('禁止注册！', 'UTF-8', 'GBK')
            )));
        }
    }

    /**
     * 邮箱注册
     */
    public function actionEmail()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        session_start();
        $email = $_POST['email'];
        $password = trim($_POST['password']);
        if (empty($_POST['captcha_code']) || empty($_SESSION['captcha_code']) || strtoupper($_POST['captcha_code']) != $_SESSION['captcha_code'])
        {
            unset($_SESSION["captcha_code"]);
            die(json_encode(array(
                'code' => 304,
                'data' => '',
                'msg' => mb_convert_encoding('图片验证码错误！', 'UTF-8', 'GBK')
            )));
        }
        unset($_SESSION["captcha_code"]);
        $userClientIp = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的邮箱，不要超过24个字符',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此邮箱已被注册',
            )
        );
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        $regAction = loadAction('reg');

        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        $source = \Common\Utils\Url::getStringParam("mid", "mobile", \Common\Utils\Url::POST);
        $result = $regAction->regEmail($email, $password, $pwdStrength, $userClientIp, 'login.2345.com', $source, $clientType);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            die(json_encode(array(
                'code' => 403,
                'data' => '',
                'msg' => 'error:' . $msgs[$states[0]][$states[1]]
            )));
        }
        else if ($states[0] == 300)
        {
            die(json_encode(array(
                'code' => $states[0] + $states[1],
                'data' => '',
                'msg' => mb_convert_encoding($msgs[$states[0]][$states[1]], 'UTF-8', 'GBK')
            )));
        }
        else if ($states[0] == 200)
        {
            $cookie = $result[1]['cookie'];
            die(json_encode(array(
                'code' => 200,
                'data' => array(
                    'I' => $cookie['I']
                ),
                'msg' => mb_convert_encoding('注册成功！', 'UTF-8', 'GBK')
            )));
        }
        else
        {
            die(json_encode(array(
                'code' => 500,
                'data' => '',
                'msg' => mb_convert_encoding('禁止注册！', 'UTF-8', 'GBK')
            )));
        }
    }


    /**
     * 用户名注册
     */
    public function actionUsername ()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST")
        {
            return;
        }
        session_start();
        $username = mb_convert_encoding($_POST['username'], 'gbk', 'utf-8');
        $password = trim($_POST['password']);

        //载入时间片
        $DateAnalyzeAction = loadAction('DateAnalyze');
        $isNormal = $DateAnalyzeAction->isNormalAccess('reg');
        $DateAnalyzeAction->regCount('regcountAccess');

        //载入令牌
        $SecurityAction = loadAction('Security');
        $uuid = \Common\Utils\Cookie::getUuid();
        $identityId = $_POST['identityId'];
        $isLegal = $SecurityAction->verifyRegToken($uuid , $identityId);

        //如果不合法
        if (!$isLegal)
        {
            $isShowCode = $DateAnalyzeAction->getShowCode('reg');
            if ( ! $isShowCode )
            {
                $DateAnalyzeAction->setShowCode('reg');
                echo json_encode(array(
                    'code' => 406,
                    'data' => '',
                    'msg' => mb_convert_encoding('验证码输入错误', "utf8", "gbk")
                ));exit;
            }

        }
        else
        {
            $DateAnalyzeAction->setRegToken ($identityId);
        }

        //如果时间规则计算有问题
        if (! $isNormal)
        {
            $isShowCode = $DateAnalyzeAction->getShowCode('reg');
            if ( ! $isShowCode  )
            {
                $DateAnalyzeAction->setShowCode('reg');
                if ( empty($_POST['captcha_code']) )
                {
                    echo json_encode(array(
                        'code' => 406,
                        'data' => '',
                        'msg' => mb_convert_encoding('验证码输入错误', "utf8", "gbk")
                    ));exit;
                }
            }
        }

        $isShowCode = $DateAnalyzeAction->getShowCode('reg');
        if ($isShowCode && (empty($_POST['captcha_code']) || empty($_SESSION['captcha_code']) || strtoupper($_POST['captcha_code']) != $_SESSION['captcha_code'] )  )
        {
            unset($_SESSION["captcha_code"]);
            echo json_encode(array(
                'code' => 406,
                'data' => '',
                'msg' => mb_convert_encoding('图片验证码错误！', 'UTF-8', 'GBK')
            ));exit;
        }
        unset($_SESSION['captcha_code']);
        $client_ip = get_client_ip();
        // 错误码
        $msgs = array(
            300 => array(
                0 => '2345帐号最少2个字符',
                1 => '2345帐号请不要超过24个字符',
                2 => '2345帐号请输入汉字，字母，数字，或邮箱地址',
                3 => '密码最少6个字符',
                4 => '密码最多16个字符',
                5 => '请输入正确的邮箱',
                6 => '此帐号已被注册，请修改2345帐号',
                7 => '此邮箱已被注册，请换一个'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );

        // 密码强度
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $regAction = loadAction('reg');

        // 客户端 注册
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
        $source = \Common\Utils\Url::getStringParam("mid", "mobile", \Common\Utils\Url::POST);
        $result = $regAction->reg($username, $password, '', $pwdScore['score'], '', 'login.2345.com', $client_ip, $source, $clientType);
        $states = explode('.', $result[0]);
        if ($states[0] == 400)
        {
            echo json_encode(array(
                'code' => $states[0] + $states[1],
                'data' => '',
                'msg' => mb_convert_encoding( $msgs[$states[0]][$states[1]] , 'UTF-8', 'GBK')
            ));exit;
        }
        elseif ($states[0] == 300)
        {
            echo json_encode(array(
                'code' => $states[0] + $states[1],
                'data' => '',
                'msg' => mb_convert_encoding( $msgs[$states[0]][$states[1]] , 'UTF-8', 'GBK')
            ));exit;
        }
        elseif ($states[0] == 200)
        {
            $cookie = $result[1]['cookie'];
            echo json_encode(array(
                'code' => 200,
                'data' => array(
                    'I' => $cookie['I']
                ),
                'msg' => mb_convert_encoding('注册成功！', 'UTF-8', 'GBK')
            ));exit;
        }
        else
        {
            echo json_encode(array(
                'code' => 500,
                'data' => '',
                'msg' => mb_convert_encoding('禁止注册！', 'UTF-8', 'GBK')
            ));exit;
        }
    }

}
