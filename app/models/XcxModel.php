<?php
use Octopus\PdoEx;
use Service\Capacity\GetHashTable;

/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/6/2
 * Time: 18:18
 */
class XcxModel extends Model
{
    private $pdo = null;

    /**
     * UserBaseModel constructor.
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_PASSPORT_USER, $dbConfig[DB_PASSPORT_USER]);
    }

    /**
     * 获取绑定关系
     * @param string $openid openid
     * @return array|bool
     */
    public function getBindOpenid($openid)
    {
        $table = GetHashTable::MembersBindOpenid($openid);
        $sql = "SELECT * FROM {$table} WHERE openid=:openid  ORDER BY id DESC";
        $params = [
            ':openid' => $openid,
        ];

        return $this->pdo->find($sql, $params);
    }

    /**
     * 添加openid和unionid的关系
     * @param int    $passid     passid
     * @param string $openid     openid
     * @param string $unionid    unionid
     * @param string $sessionKey sessionkey
     * @param string $appId      appId
     * @return bool|int
     */
    public function addOpenIdUnionIdInfo($passid, $openid, $unionid, $sessionKey, $appId = '')
    {
        $table = GetHashTable::MembersBindOpenid($openid);
        $insertData = array(
            'passid'     => $passid,
            'openid'     => $openid,
            'unionid'    => $unionid,
            'sessionKey' => $sessionKey,
            'appid'      => $appId,
            'add_time'   => date('Y-m-d H:i:s'),
        );

        return $this->pdo->insert($table, $insertData);
    }

    /**
     * 更新sessionkey
     * @param int    $passid     passid
     * @param string $openid     openid
     * @param string $unionid    unionid
     * @param string $sessionKey sessionkey
     * @return bool|int
     */
    public function updateOpenIdUnionIdInfo($passid, $openid, $unionid, $sessionKey)
    {
        $table = GetHashTable::MembersBindOpenid($openid);
        $condition = array(
            'where'  => 'passid = :passid and openid=:openid and unionid=:unionid',
            "params" => [
                ':passid'  => $passid,
                ':openid'  => $openid,
                ':unionid' => $unionid,
            ],
        );
        $update = [
            'sessionKey' => $sessionKey,
        ];

        return $this->pdo->update($table, $update, $condition);
    }

    /**
     * 删除openid和unionid关系记录
     * @param int    $passid  passid
     * @param string $openid  openid
     * @param string $unionid unionid
     * @return bool|int
     */
    public function delOpenIdUnionIdInfo($passid, $openid, $unionid)
    {
        $table = GetHashTable::MembersBindOpenid($openid);
        $condition = array(
            "where"  => 'passid = :passid and openid=:openid and unionid=:unionid',
            "params" => array(
                ':passid'  => $passid,
                ':openid'  => $openid,
                ':unionid' => $unionid,
            ),
        );

        return $this->pdo->delete($table, $condition);
    }

    /**
     * 设置access_token
     * @param array $data 更新内容
     * @return bool
     */
    public function setWxXcxAccessToken($data)
    {
        $data['update_date'] = date('Y-m-d H:i:s');
        $isSet = $this->pdo->duplicate('xcx_accesstoken', $data);
        if ($isSet !== false) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取access_token
     * @param string $appid appid
     * @return array|bool
     */
    public function getWxXcxAccessToken($appid)
    {
        $sql = "SELECT * FROM xcx_accesstoken WHERE appid = :appid";
        $params = [
            ':appid' => $appid,
        ];

        return $this->pdo->find($sql, $params);
    }

}
