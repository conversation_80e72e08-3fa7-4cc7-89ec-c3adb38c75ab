/* 记事本 */
#jsb-cnt{ height:490px; clear:both; }
.jsb-t1{overflow:hidden; margin:10px 20px 0; padding-bottom:5px; clear:both; text-align:right;}
.jsb-t{ clear:both; border-bottom:1px solid #d6d8e5; overflow:hidden; margin:10px auto 0; height:20px;}
.jsb-t a{margin:0 10px; padding:0 7px 0 2px; display:block; float:left; height:20px; line-height:20px; overflow:hidden; color:#555; padding-top:2px;border:1px solid #c2dcfc;border-bottom:none; vertical-align:middle;cursor:pointer;}
.jsb-t img{ border:none; vertical-align:text-bottom}
.jsb-t a:hover,.jsb-t .jsb_a{background-color:#c8e7ff; border-bottom:none; color:#454545; text-decoration:none; border-color:#5ea1f3;}
.jsb-t .jsb_a{ color:#454545;  background-color:#c8e7ff;cursor:pointer;}
.jsb-t b a{ color:#f60; text-decoration:underline;}
.j-item,.j-item-new{ margin:5px 0 5px 11px;_margin:5px 0 5px 8px;display:block; float:left; width:168px; border-top:1px solid #d8e9ff; background:url(../images/j-bd.gif) repeat-y;}
.j-item h4{ background:url(../images/item-t.gif) right no-repeat; color:#fff; font-family:Arial, Helvetica, sans-serif;font-size:11px; text-align:right; margin:8px; padding-right:17px;}
.j-t-left{ width:30px; display:block; float:left; height:16px; }
.j-t-01{background:url(../images/a11.gif) left no-repeat;}
.j-t-02{background:url(../images/a21.gif) left no-repeat;}
.j-t-03{background:url(../images/a31.gif) left no-repeat;}
.j-t-04{background:url(../images/a41.gif) left no-repeat;}
.j-t-05{background:url(../images/a51.gif) left no-repeat;}
.j-body,.j-body-new{ padding:0 10px; height:150px;}
.j-item p{height:144px; background:url(../images/line.gif);color:#555; text-align:left; text-indent:2em; line-height:24px; margin:0; padding:0;overflow:hidden;word-break:break-all;word-wrap:break-word}
.j-item p a{color:#555; }
.j-item p a:hover{color:#f60; }
.j-item h5,.j-item-new h5{ background:url(../images/tag-btm.gif) bottom no-repeat; height:32px; margin:0; padding:0; width:168px;}
.jsblist{ overflow:hidden; margin-top:10px;}
.jsblist1{ overflow:hidden;}
.j-body-new{ height:184px;text-align:center; line-height:30px;}
.j-body-new img{ border:none;}
.j-body-new a{color:#333; text-align:left; position:relative; top:70px;margin:0; padding:0}
.j-body-new a:hover{ color:#f60;}
.page2{ display:block; border-top:1px solid #d6d8e5;clear:both; color:#9f9f9f; margin:5px 5px 0; text-align:right; height:25px; line-height:25px; }
.page2 a{ color:#434343; border:1px solid #dadfe4; padding:1px 2px;margin:0 2px; background:url(../images/bg1.gif) repeat-x;cursor:pointer;}
.page2 select{ *top:3px;_top:3px; margin-right:15px; position:relative;}
.sc{ width:44px; height:19px; line-height:19px; border:none; background:transparent url(../images/btnbg.gif) no-repeat; color:#626262; cursor:pointer;}
.njs{ background:transparent url(../images/btnbg02.gif) no-repeat; border:none; width:80px; height:19px; line-height:19px; color:#626262;cursor:pointer;}
.j-tool{ margin-top:5px;}
.j-tool a{ color:#a4b5c7; font-size:12px; margin:0 3px; padding:0 2px;}
.j-tool a:hover{ color:#fff; background:#81889a; text-decoration:none;}

#jsb-cnt b{ font-weight:100; color:#356592; height:20px; line-height:20px; margin-left:20px;font-size:14px; clear:both; text-align:left; display:block;}
#jsb-cnt strong{  color:#2b80cf; height:20px; line-height:20px; margin-left:20px;font-size:12px; clear:both; text-align:left; display:block;}
#jsb-cnt ul{ margin:0 12px 7px 20px;}
#jsb-cnt ul li{ line-height:30px; height:30px; border-bottom:1px solid #eff0f5; text-align:left}
#jsb-cnt ul li a{ color:#626262; line-height:25px;}
#jsb-cnt ul li a:hover{ color:#f60;}
#jsb-cnt ul li em{ font-style:normal; color:#aaa; font-family:"Times New Roman", Times, serif; margin-left:10px;}
.list-tool{ margin-left:30px;}
#jsb-cnt ul li .list-tool a{ background:#f1f8fe; color:#aaa; padding:1px 3px; margin:0 3px;}
#jsb-cnt ul li .list-tool a:hover{ background:#e0edfc; color:#356592; text-decoration:none;}
.j-tip{ padding-left:30px; margin:40px 0 0 30px;}
#jsb{ position:relative;}
#new{ color:#f00; font-size:12px; position:absolute; font-weight:100; right:35px; top:-5px;}
.lb{ height:22px; line-height:22px;  text-align:left;}
.lb b{ display:block; float:left; font-weight:100;}
.lb a{ display:inline; float:left; margin:0 3px; padding:0 2px 0 20px; border:1px solid #b2c6d5; color:#555; cursor:pointer;}
.fl01{ background:url(../images/a11.gif) left no-repeat;}
.fl02{ background:url(../images/a21.gif) left no-repeat;}
.fl03{ background:url(../images/a31.gif) left no-repeat;}
.fl04{ background:url(../images/a41.gif) left no-repeat;}
.fl05{ background:url(../images/a51.gif) left no-repeat;}
.lb_a{ background-color:#9ed4fd; color:#0068b7;}
.lb a:hover{ background-color:#9ed4fd; color:#0068b7;}
.newj{ background:#f4faff; border:1px solid #d8e9ff; text-align:center; margin: 10px; height:510px; line-height:40px; }
.newj img{ border:none; margin-top:150px;}
.chk{background:url(../images/lb.gif) center left no-repeat; padding-left:15px;  margin-right:20px;cursor:pointer; text-decoration:underline; zoom:1;}
.zyll{background:url(../images/zy.gif) center left no-repeat; padding-left:15px;  margin-right:20px;cursor:pointer; text-decoration:underline; zoom:1;}
.dc{background:url(../images/mbi_032.gif) center left no-repeat; padding-left:15px;  margin-right:35px; cursor:pointer; text-decoration:underline; zoom:1;}
.dc2{background:url(../images/mbi_032.gif) center left no-repeat; padding-left:15px;  margin-right:15px; cursor:pointer; text-decoration:underline; zoom:1;}
.fah{ background:url(../images/iconfh.gif) center left no-repeat; padding-left:15px; margin-right:40px; text-decoration:underline; zoom:1;}
.fah2{ background:url(../images/iconfh.gif) center left no-repeat; padding-left:15px; margin-right:90px; text-decoration:underline; zoom:1;}
.key-ipt{ border:1px solid #bcbcbc; border-right:none; color:#bfbfbf; font-size:12px; padding-left:3px;width:110px; height:16px; line-height:16px; position:relative; _top:3px;}
.key-btn{ background:transparent url(../images/key-btn.gif) no-repeat; border:none; color:#1b1b1b; width:38px; height:18px; line-height:18px;position:relative;  top:0px\9;top:1px;*top:0;_top:3px; cursor:pointer;}
#mjsb{ border:none; background:none;color:#f60; float:right; padding:0; margin:0;}
#mjsb:hover{ text-decoration:underline;}