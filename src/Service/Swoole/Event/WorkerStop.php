<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/17
 * Time: 16:20
 */

namespace Service\Swoole\Event;

use Service\Func\ServiceFunc;
use Service\Swoole\Tool\Log;

class WorkerStop
{
    /**
     * 关闭work
     * -
     * @param object $service 服务
     * @param string $worker_id work id
     * @return void
     */
    public static function OnWorkerStop($service, $worker_id)
    {
        $msg = $service->host . ':' . $service->port . 'worker_pid:' . $service->worker_pid . 'work_id:' . $worker_id;
        ServiceFunc::sendWx($msg);
//        Log::setLogMsg('OnWorkerStop:' . $worker_id);
    }
}
