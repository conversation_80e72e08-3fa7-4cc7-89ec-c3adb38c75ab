<?php

use Octopus\PdoEx;

class UnionLoginConfigModel extends Model
{
    const PACKAGE_STATUS_DISABLE = 0;
    const PACKAGE_STATUS_ENABLE = 1;

    const PACKAGE_LIST_STATUS_DISABLE = 0;
    const PACKAGE_LIST_STATUS_ENABLE = 1;

    private $pdo;
    private $table = 'union_login_package';
    private $configTable = 'union_login_config';

    /**
     * UnionLoginConfigModel constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_PASSPORT_MAIN, $dbConfig[DB_PASSPORT_MAIN]);
    }

    /**
     * User: panj
     *
     * @param int $id id
     *
     * @return array|bool
     */
    public function getById($id)
    {
        $sql = "select * from {$this->table} where id=:id limit 1";

        return $this->pdo->find($sql, [':id' => $id]);
    }

    /**
     * User: panj
     *
     * @param string $mids mids
     * @param int $offset offset
     * @param int $pageSize pageSize
     *
     * @return array|bool
     */
    public function getList($mids, $offset = 0, $pageSize = 1000)
    {
        $where = 'where 1';
        $params = [];
        if (is_array($mids) && !empty($mids))
        {
            $midStr = '';
            foreach ($mids as $mid)
            {
                $midStr .= ":" . $mid . ",";
                $params[":$mid"] = $mid;
            }
            $midStr = rtrim($midStr, ',');
            $where .= ' and mid in (' . $midStr . ')';
        }

        $sql = "select * from {$this->table} " . $where;

        return $this->pdo->findList($sql, $params, $pageSize, $offset);
    }

    /**
     * User: panj
     * 获取联合登录列表可用的mid
     * @return array|bool
     */
    public function getAllowedMid()
    {
        $sql = "select p.mid, count(p.mid) as cnt from {$this->table} as p join {$this->configTable} c on `p`.`id` = `c`.`pid` where c.status=:status group by mid";
        $res = $this->pdo->findAll($sql, [':status' => self::PACKAGE_LIST_STATUS_ENABLE]);
        if (!empty($res))
        {
            $mIds = [];
            foreach ($res as $key => $value)
            {
                $mIds[$value['mid']] = $value['cnt'];
            }

            return $mIds;
        }
        return false;
    }

    /**
     * User: panj
     * @param array $args params
     * @param int $offset offset
     * @param int $pageSize pageSize
     * @return array|bool
     */
    public function getConfigList($args, $offset = 0, $pageSize = 1000)
    {
        $where = ' where 1';
        $params = [];
        if (!empty($args))
        {
            foreach ($args as $k => $v)
            {
                if (is_string($v) && !empty(trim($v)))
                {
                    $where .= " and $k =:$k";
                    $params[":$k"] = $v;
                }
            }
            if (isset($args['adminAllowMid']) && !empty($args['adminAllowMid']))
            {
                $mids = $args['adminAllowMid'];
                $midStr = '';
                foreach ($mids as $mid)
                {
                    $midStr .= ":" . $mid . ",";
                    $params[":$mid"] = $mid;
                }
                $midStr = rtrim($midStr, ',');
                $where .= ' and mid in (' . $midStr . ')';
            }
        }
        $sql = "select CONCAT_WS(:separator, p.pname, p.package_name, p.sign) as pname,p.mid, c.* from {$this->configTable} c join {$this->table} p on `p`.`id` =`c`.`pid`";
        $params[':separator'] = '-';
        $sql .= $where;
        return $this->pdo->findList($sql, $params, $pageSize, $offset);
    }

    /**
     * User: panj
     *
     * @param int $id id
     *
     * @return array|bool
     */
    public function getConfigById($id = 0)
    {
        if (empty($id))
        {
            return false;
        }
        $sql = "select p.mid, p.pname, p.package_name, p.sign, c.* from {$this->configTable} c join {$this->table} p on `p`.`id` =`c`.`pid` and c.id=:id";

        return $this->pdo->find($sql, [':id' => $id]);
    }

    /**
     * insert
     *
     * @param array $data $data
     *
     * @author：dongx
     * @return bool|int
     */
    public function insert($data)
    {
        $data['status'] = self::PACKAGE_STATUS_ENABLE;
        $data['create_at'] = date('Y-m-d H:i:s');
        if (isset($data['id']))
        {
            unset($data['id']);
        }

        return $this->pdo->insert($this->table, $data);
    }

    /**
     * User: panj
     *
     * @param array $data data
     *
     * @return bool|array
     */
    public function packageExist($data)
    {
        $where = ' where (pname=:pname or package_name=:package_name)';
        $params = [
            ':pname'        => $data['pname'],
            ':package_name' => $data['package_name'],
        ];
        if (isset($data['id']) && $data['id'])
        {
            $where .= ' and id !=:id';
            $params[':id'] = $data['id'];
        }
        $sql = "select id, pname,  package_name from {$this->table}" . $where;
        $res = $this->pdo->findAll($sql, $params);

        if (empty($res))
        {
            return false;
        }

        foreach ($res as $k => $v)
        {
            if ($v['pname'] == $data['pname'])
            {
                return [true, $data['pname']];
            }

            if ($v['package_name'] == $data['package_name'])
            {
                return [true, $data['package_name']];
            }
        }
        return false;
    }

    /**
     * User: panj
     *
     * @param int $pid pid
     * @param int $versionCode version code
     * @param int $id id
     *
     * @return bool
     */
    public function configExit($pid, $versionCode, $id = null)
    {
        $where = ' where pid=:pid and lowest_version=:lowest_version ';
        $params = [
            ':pid' => $pid,
            ':lowest_version' => $versionCode,
        ];
        if (!is_null($id))
        {
            $where .= ' and id !=:id';
            $params[':id'] = $id;
        }
        $sql = "select * from {$this->configTable} " . $where;
        $res = $this->pdo->find($sql, $params);

        return $res ? true : false;
    }

    /**
     * update
     *
     * @param int   $id   $id
     * @param array $data $data
     *
     * @author：dongx
     * @return bool|int
     */
    public function update($id, $data)
    {
        $condition = array(
            "where"  => "id = :id",
            "params" => array(
                ":id" => $id,
            ),
        );

        return $this->pdo->update($this->table, $data, $condition);
    }

    /**
     * User: panj
     * @param array $mids mid
     * @return array|bool
     */
    public function getAllEnablePackage($mids)
    {
        $where = ' where 1';
        $params = [];
        if (is_array($mids) && !empty($mids))
        {
            $midStr = '';
            foreach ($mids as $mid)
            {
                $midStr .= ":" . $mid . ",";
                $params[":$mid"] = $mid;
            }
            $midStr = rtrim($midStr, ',');
            $where .= ' and mid in (' . $midStr . ')';
        }
        $where .= ' and status=:status';
        $params[':status'] = self::PACKAGE_STATUS_ENABLE;
        $sql = "select id, mid, pname, package_name, sign from {$this->table} " .$where;
        $res = $this->pdo->findAll($sql, $params);

        return !empty($res) ? $res : false;
    }

    /**
     * User: panj
     *
     * @param array $data data
     *
     * @return bool|int
     */
    public function insertConfig($data)
    {
        $data['create_at'] = date('Y-m-d H:i:s');
        return $this->pdo->insert($this->configTable, $data, true);
    }

    /**
     * User: panj
     *
     * @param int $id id
     * @param array $data data
     *
     * @return bool|int
     */
    public function updateConfig($id, $data)
    {
        $condition = array(
            "where"  => "id = :id",
            "params" => array(
                ":id" => $id,
            ),
        );

        return $this->pdo->update($this->configTable, $data, $condition);
    }

    /**
     * User: panj
     *
     * @return array|bool
     */
    public function getEnablePackageByIds()
    {
        $sql = " select id, pname, package_name, sign, status from {$this->table} where status=:status";
        $res = $this->pdo->findAll($sql, [':status' => self::PACKAGE_STATUS_ENABLE]);

        return !empty($res) ? $res : false;
    }

    /**
     * User: panj
     *
     * @param string $package package name
     * @param int $version version code
     *
     * @return array|bool
     */
    public function getByPackageVersion($package, $version)
    {
        $where = ' where p.package_name=:package ';
        $params = [
            ':package' => $package,
        ];
        $orderBy = ' order by c.lowest_version ';
        $sql = "select p.mid, p.pname, p.package_name, p.sign,p.status as pStatus,c.id, c.lowest_version, c.status, c.pull_list, c.verify_list from {$this->configTable} c join {$this->table} p on `p`.`id` =`c`.`pid` ";
        $sql .= $where . $orderBy;
        $res = $this->pdo->findAll($sql, $params);

        if (empty($res))
        {
            return false;
        }
        else
        {
            $count = count($res);
            if ($count == 1 && $version >= $res[0]['lowest_version'])
            {
                return $res[0];
            }
            else
            {
                for ($i = 1; $i < $count; $i ++)
                {
                    //处理边界情况，左闭右开区间
                    if ($version >= $res[$i - 1]['lowest_version'] && $version < $res[$i]['lowest_version'])
                    {
                        return $res[$i - 1];
                    }
                    elseif ($version >= $res[$count - 1]['lowest_version'])
                    {
                        return $res[$count - 1];
                    }
                }
            }

            return false;
        }
    }

    /**
     * 删除
     * @author：dongx
     * @return @return bool|int
     */
    public function delete()
    {
        $condition = array(
            "where"  => 'id = :id',
            "params" => array(
                ':id' => isset($_GET['id']) ? $_GET['id'] : '',
            ),
        );

        return $this->pdo->delete($this->table, $condition);
    }

    /**
     * User: panj
     *
     * @param array $pids pids
     *
     * @return array|bool
     */
    public function getConfigByPids($pids)
    {
        $where = " WHERE 1 ";
        $params = [];
        if (is_array($pids) && !empty($pids))
        {
            $pidStr = '';
            foreach ($pids as $pid)
            {
                $pidStr .= ":" . $pid . ",";
                $params[":$pid"] = $pid;
            }
            $pidStr = rtrim($pidStr, ',');
            $where .= ' and pid in (' . $pidStr . ')';
            $sql = " SELECT id, pid, verify_list from {$this->configTable} " . $where;
            return $this->pdo->findAll($sql, $params);
        }
        else
        {
            return false;
        }
    }
}
