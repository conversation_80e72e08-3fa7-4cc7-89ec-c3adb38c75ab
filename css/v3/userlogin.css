@charset "utf-8";
/* CSS Document */

.pfooter{ margin-top:0px;}
.login_wrap{ height:480px; background:#e8f7fc; border-top:1px solid #e7e7e7;}
.login_center{ width:1000px; height:100%; margin:0 auto;}
.login_box{ float:right;}
.trig_gopro{ width:487px; height:383px; float:left; overflow:hidden; margin-left:87px; margin-top:49px;}

.g-login .g-login-th{ border-bottom:none;}
.g-login { width: 337px; border:none; margin-right:6px;}
.g-login .g-inputTxt{ height:34px;}
.g-icon-name, .g-icon-password{ top:9px;}
.g-login .g-inputTxt .sDes{ top:8px;}
.g-login .g-inputTxt input{ padding:8px 0;}
.g-login-code .codePic{ height:36px;}
.g-login-code .codePic .sPic{ height:36px;}
.g-login-code .codePic .sPic img{ display:block;}
.g-login-code .codePic{ line-height:36px;}
.g-login .g-txt{ padding:13px 0 6px 0;}
.g-login .g-error,.g-other-login,.g-other-login .otherStyle{ padding-top:7px;}
.g-login .g-txt-registration{ padding:7px 0 8px 0;}
.g-login .g-login-th{ padding:0 19px;}
.g-login .g-login-th{ height:56px; line-height:56px;}
.g-login .g-inputTxt{ margin-top:18px;}
.g-login .g-login-tb{ padding-top:0px; margin-top:-18px;}
.footer { border-top: 1px solid #eee; height:55px; overflow: hidden; background-color: #fff; min-width: 1000px; line-height: 56px; text-align: center; color: #999; font-size:14px; font-family:Arial,微软雅黑,宋体;}
.footerCon { width: 1000px; margin: 0 auto;}
.footer em { color: #dcd4dc; padding: 0 10px;}
.footer a { color: #999;}
.footer a:hover { color: #f30;}
.g-login .g-inputTxt{}
.g-login-code{ margin-top:18px;}
.g-login-code .g-inputTxt{ margin-top:0px;}
.logoPic{ float:left;}
.sTxt { color: #666; float: left; font-size: 18px; height: 64px; line-height: 64px; overflow: hidden; padding: 7px 0 0 10px;}
.g-login .g-login-th { background-color: #fff; }