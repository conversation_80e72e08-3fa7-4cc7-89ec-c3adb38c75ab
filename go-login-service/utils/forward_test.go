package utils

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"strings"
	"testing"
)

func TestGetForwardUrl(t *testing.T) {
	// Set up test environment
	os.Setenv("HTTP_HOST", "login.2345.com")
	defer os.Unsetenv("HTTP_HOST")

	tests := []struct {
		name     string
		method   string
		getParam string
		postData string
		expected string
	}{
		{
			name:     "Valid 2345.com URL from GET",
			method:   "GET",
			getParam: "http://www.2345.com/test",
			expected: "http://www.2345.com/test",
		},
		{
			name:     "Valid 2345.cn URL from GET",
			method:   "GET",
			getParam: "http://passport.2345.cn/login",
			expected: "http://passport.2345.cn/login",
		},
		{
			name:     "Valid hao774.com URL",
			method:   "GET",
			getParam: "https://www.hao774.com/",
			expected: "https://www.hao774.com/",
		},
		{
			name:     "Valid 7255.com URL with path",
			method:   "GET",
			getParam: "http://www.7255.com/search?q=test",
			expected: "http://www.7255.com/search?q=test",
		},
		{
			name:     "Invalid domain - should return default",
			method:   "GET",
			getParam: "http://www.baidu.com",
			expected: "http://www.2345.com",
		},
		{
			name:     "Invalid URL format",
			method:   "GET",
			getParam: "not-a-url",
			expected: "",
		},
		{
			name:     "Empty forward parameter",
			method:   "GET",
			getParam: "",
			expected: "",
		},
		{
			name:     "URL with spaces - should be encoded",
			method:   "GET",
			getParam: "http://www.2345.com/test page",
			expected: "http://www.2345.com/test page",
		},
		{
			name:     "URL with quotes - should be removed",
			method:   "GET",
			getParam: "http://www.2345.com/test\"'page",
			expected: "http://www.2345.com/testpage",
		},
		{
			name:     "POST parameter takes precedence",
			method:   "POST",
			getParam: "http://www.baidu.com",
			postData: "forward=http://www.2345.com/post",
			expected: "http://www.2345.com/post",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request
			var req *http.Request
			if tt.method == "POST" {
				body := strings.NewReader(tt.postData)
				req = httptest.NewRequest("POST", "/?forward="+url.QueryEscape(tt.getParam), body)
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			} else {
				req = httptest.NewRequest("GET", "/?forward="+url.QueryEscape(tt.getParam), nil)
			}

			result := GetForwardUrl(req)
			if result != tt.expected {
				t.Errorf("GetForwardUrl() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetForwardFromRequest(t *testing.T) {
	tests := []struct {
		name     string
		method   string
		getParam string
		postData string
		expected string
	}{
		{
			name:     "GET parameter",
			method:   "GET",
			getParam: "http://www.example.com",
			expected: "http://www.example.com",
		},
		{
			name:     "POST parameter",
			method:   "POST",
			postData: "forward=http://www.example.com",
			expected: "http://www.example.com",
		},
		{
			name:     "POST takes precedence over GET",
			method:   "POST",
			getParam: "http://get.example.com",
			postData: "forward=http://post.example.com",
			expected: "http://post.example.com",
		},
		{
			name:     "No parameters",
			method:   "GET",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req *http.Request
			if tt.method == "POST" {
				body := strings.NewReader(tt.postData)
				req = httptest.NewRequest("POST", "/?forward="+url.QueryEscape(tt.getParam), body)
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			} else {
				req = httptest.NewRequest("GET", "/?forward="+url.QueryEscape(tt.getParam), nil)
			}

			result := getForwardFromRequest(req)
			if result != tt.expected {
				t.Errorf("getForwardFromRequest() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestHasWhitespace(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"no-spaces", false},
		{"has spaces", true},
		{"has\ttab", true},
		{"has\nnewline", true},
		{"", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := hasWhitespace(tt.input)
			if result != tt.expected {
				t.Errorf("hasWhitespace(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestValidateURL(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"http://www.example.com", "http://www.example.com"},
		{"https://www.example.com", "https://www.example.com"},
		{"ftp://www.example.com", ""},
		{"not-a-url", ""},
		{"", ""},
		{"http://", ""},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := validateURL(tt.input)
			if result != tt.expected {
				t.Errorf("validateURL(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestIsAllowedDomain(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
	}{
		{"http://www.2345.com", true},
		{"https://passport.2345.cn", true},
		{"http://www.hao774.com/test", true},
		{"https://api.7255.com?param=value", true},
		{"http://www.9991.com", true},
		{"http://www.2345jr.com", true},
		{"https://www.km.com", true},
		{"http://subdomain.2345.cc", true},
		{"https://test.2345.com.cn", true},
		{"http://www.baidu.com", false},
		{"https://www.google.com", false},
		{"http://malicious.com", false},
		{"", false},
		{"not-a-url", false},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := isAllowedDomain(tt.input)
			if result != tt.expected {
				t.Errorf("isAllowedDomain(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestGetDomainConstant(t *testing.T) {
	tests := []struct {
		name     string
		httpHost string
		expected string
	}{
		{
			name:     "login.2345.com",
			httpHost: "login.2345.com",
			expected: "2345.com",
		},
		{
			name:     "passport.2345.cn",
			httpHost: "passport.2345.cn",
			expected: "2345.cn",
		},
		{
			name:     "my.9991.com",
			httpHost: "my.9991.com",
			expected: "9991.com",
		},
		{
			name:     "www.2345.com",
			httpHost: "www.2345.com",
			expected: "www.2345.com",
		},
		{
			name:     "empty host",
			httpHost: "",
			expected: "2345.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variable
			if tt.httpHost != "" {
				os.Setenv("HTTP_HOST", tt.httpHost)
			} else {
				os.Unsetenv("HTTP_HOST")
			}
			defer os.Unsetenv("HTTP_HOST")

			result := getDomainConstant()
			if result != tt.expected {
				t.Errorf("getDomainConstant() = %v, want %v", result, tt.expected)
			}
		})
	}
}
