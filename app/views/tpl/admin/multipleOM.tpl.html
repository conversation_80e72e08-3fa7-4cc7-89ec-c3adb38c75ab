<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=GBK">
    <title>跨机器的OM</title>
    <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
    <link href="/css/v3/zhanghao.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/auth/styles/top.css">
    <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
</head>
<body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
<div class="mainWrap">

    <!--面包屑导航 开始-->
    <div class="location">位置：<a href="#" target="_blank">帐号体系</a>  &gt; <span>跨机器的OM</span></div>
    <!--面包屑导航 结束-->

    <form name="omForm" method="post" id="omForm" action="operate">
        <div class="contentWrap">
            <!--table数据列表 开始-->
            <table cellspacing="0" cellpadding="0" class="tabFromMod mt10">
                <tbody>
                <tr>
                    <td class="tabLeftTit">用户中心服务器IP</td>
                    <td width="270">
                        <textarea cols="100" name="ipList">{{$pageArray.ucServiceIP}}</textarea>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td class="tabLeftTit">文件列表</td>
                    <td>
                        <textarea cols="100" name="fileList"></textarea>
                    </td>
                    <td class="txtLeftPl15"></td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <input class="styleBtnBlue left ml10" type="button" value="确定清理" id="btnClear" />
                        <input class="styleBtnBlue left ml10" type="button" value="Reset All" id="btnReset" />
                    </td>
                </tr>
                </tbody>
            </table>
        </div><!--contentWrap end-->
    </form>
</div>


<script type="text/javascript">
    $(function () {
        $('#btnClear').click(function () {
            $('#omForm').attr('action', 'multipleOM/operate?clear=1');
            $('#omForm').submit();
        });
        $('#btnReset').click(function () {
            $('#omForm').attr('action', 'multipleOM/operate?reset=1');
            $('#omForm').submit();
        });
    })
</script>
</body>
</html>
