<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/12/10
 * Time: 14:28
 */
includeBase('ExternalController');

class ExternalUserInfoController extends ExternalController
{
    /**
     * ExternalCheckController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            $this->codeResponse("403", "", "");
        }
    }

    /**
     * 获取用户信息
     * -
     * @return mixed
     */
    public function actionIndex()
    {
        //校验必填字段及签名
        $necessaryField = array(
            'exId',
            'passid',
            'token',
            'requestTime',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );
        $paramCheck = new \Common\Validator\Params();
        $aesDecodeData = $paramCheck->check('data', $necessaryField);
        if ($aesDecodeData === false)
        {
            $errorInfo = $paramCheck->getError();
            $this->codeResponse($errorInfo['code'], "", $errorInfo['msg']);
        }
        list($exId, $passid ,$token, $time) = $this->getParamAndAutoDeny($necessaryField);
        $LoginToken = loadAction("LoginToken");
        $isTokenValid = $LoginToken->checkToken($exId, $passid, $time, $token);
        if ($isTokenValid)
        {
            $memberModel = loadModel('member');
            $info = $memberModel->read($passid);
            if (empty($info))
            {
                $this->codeResponse("404", "", "");
            }
            $this->codeResponse("200", \Common\Utils\Encoding::transcoding($info, 'gbk', 'utf-8'), "");
        }
        else
        {
            $this->codeResponse("500", "", "");
        }
    }
}
