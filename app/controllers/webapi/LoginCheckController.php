<?php
/**
 * 客户端检查是否显示验证码借口
 * */
class LoginCheckController extends Controller
{
    /**
     * actionIndex
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex ()
    {
//        session_start();
//        $loginAction = loadAction('login');
//        $isShowCode = false;
//        $showCodeLimitCount = Config::get("showCodeLimitCount");
//        if ($_SESSION['expire'] > $showCodeLimitCount || $loginAction->decideCode(get_client_ip()))
//        {
//            $isShowCode = true;
//        }
        echo "login.checkLoginStatus ( ".  json_encode(array('display' => true )) .")" ;
        exit;
    }
}