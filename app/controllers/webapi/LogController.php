<?php
use Octopus\Logger;
class LogController extends Controller
{

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();

    }

    /**
     * 日志记录测试
     * @author：dongx
     * @return void
     */
    public function actionTest()
    {
        $userClient_ip = get_client_ip();
        $content="ip:".$userClient_ip;
        xLog('test','test',$content);
    }
}