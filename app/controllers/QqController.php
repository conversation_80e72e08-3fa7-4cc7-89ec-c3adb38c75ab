<?php

use \Service\AccountStat\Consts\AccountStat;

class QqController extends Controller
{

    /**
     * qq登录链接, 设置 cookie
     *
     * @return void
     */
    public function actionIndex()
    {
        if (isset($_GET['forward']))
        {
            $forward = getForwardUrl();
            setcookie('qqforward', $forward, time() + 3600 * 24, '/');
            setcookie('oAuthFocusLogin', isset($_GET['focusLogin']) ? 1 : 0, time() + 3600 * 24, '/');
        }

        if (isset($_GET['callback']))
        {
            setcookie('oauthThirdCallback', $_GET['callback'], time() + 3600 * 24, '/');
        }
        elseif (isset($forward))
        {
            preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $forward, $matches);
            if ($matches[0] == '9991.com')
            {
                setcookie('oauthThirdCallback', "http://login.9991.com/webapi/sso/login", time() + 3600 * 24, '/');
            }
            else
            {
                setcookie('oauthThirdCallback', "", time() - 3600, '/');
            }
        }
        redirect(LOGIN_HOST . '/oauth/qq?callback=/qq/callback');
    }

    public function actionCallback()
    {
        if (!empty($_GET["rewrite"]) && !empty(dev::parasDomain($_GET["rewrite"]))) {
            $locationParam = $_GET;
            unset($locationParam["rewrite"]);
            $uri = parse_url($_SERVER['REQUEST_URI']);
            if (isset($_GET['isBind'])) {
                //青鸟绑定回掉
                $locationUrl = $_GET["rewrite"] . "?" . http_build_query($locationParam);
            } else {
                $locationUrl = $_GET["rewrite"] . "/" . $uri['path'] . "?" . http_build_query($locationParam);
            }
            header("Location: {$locationUrl}");
            exit;
        }
        $chrome = \Common\Utils\Http::isChrome();
        if ($chrome && isset($_COOKIE['qqforward'])) {
            $successHost = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
            switch ($successHost) {
                case 'www.hao774.com':
                    if ($_SERVER['HTTP_HOST'] != 'passport.hao774.com') {
                        redirect("http://passport.hao774.com{$_SERVER['REQUEST_URI']}&skip_oauth=1&qq_forward={$_COOKIE['qqforward']}&redirect_uri={$_COOKIE['redirect_uri']}");
                        exit;
                    }
                    break;
                // case 'passport.7255.com':
                //     if ($_SERVER['HTTP_HOST'] != 'passport.7255.com') {
                //         header("Location: http://passport.7255.com{$_SERVER['REQUEST_URI']}" );
                //     }
                //     break;
                // default:
                //     break;
            }
        }

        if (isset($_COOKIE['qqforward']) && strpos($_COOKIE['qqforward'], "http://www.2345.com") === 0 && $_COOKIE['oAuthFocusLogin'])
        {
            setcookie('oauth_state', null, time() - 3600, '/');
            setcookie('redirect_uri', null, time() - 3600, '/');
            redirect($_COOKIE['qqforward']);
        }
        $allOauthConfig = Config::get('oauth');
        $oauthConfig = $allOauthConfig['qq'];
        $isUseRedis = Config::get("isUseRedis");

        if ($isUseRedis)
        {
            $redis = RedisEx::getInstance();
        }
        if ((empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state']) && empty($_GET['skip_oauth']))
        {
            setcookie('oauth_state', null, time() - 3600, '/');
            setcookie('redirect_uri', null, time() - 3600, '/');

            $redirectUrl = "/";
            $host = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
            preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $host, $matches);
            if ($matches[0] == "2345.com" || $matches[0] == "2345.cn")
            {
                $redirectUrl = $_COOKIE['qqforward'];
            }

            redirect($redirectUrl);
        }
        $oauthAction = loadAction('oauth');
        $redirectUri = empty($_COOKIE["redirect_uri"]) ? $_GET['redirect_uri'] : $_COOKIE["redirect_uri"];
        $result = $oauthAction->qqCallback($_REQUEST['code'], $redirectUri);
        if ($result)
        {
            $openid = $result['openid'];
            $accessToken = $result['access_token'];

            //todo 以下cookie 为 \QqController::actionIslogin 所用， 但会影响oauth/bind 功能，故删除，之后可以直接删除！ xiezx
//            setcookie('openid', $openid, 0, '/');
//            setcookie('oauth_type', 'qq', time() + 3600 * 24 * 30, '/', '2345.com');
//            setcookie('access_token', $accessToken, 0, '/');
            //领卷用
            setcookie('qqopenid', $openid, 0, '/');
            setcookie('qq_access_token', $accessToken, 0, '/');
            $gender = $result['gender'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'qq');
            if ($return["passid"] > 0)
            {
                //收集注册登录信息
                $st = true;
                $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
                $clientType = AccountStat::CTP_WEB;
                if (!$source)
                {
                    $source = "login.2345.com";
                }
                elseif ($source == "app.2345.com")
                {
                    $source = "andbs";
                    $clientType = AccountStat::CTP_APP;
                }
                \Service\AccountStat\AccountStat::collect(AccountStat::TP_LOGIN, $clientType, AccountStat::AC_TP_OAUTH_QQ, $st, $source);

                if ($isUseRedis)
                {
                    $redis->incr('LSN:qq_login_2345_com');
                }
                $passid = $return["passid"];
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $result = $memberModel->read($passid);
                if ($result['locked'] == 2) {
                    //此账号已被冻结，请联系客服
                    $redirectUrl = isset($_GET["rewrite"]) ? $_GET["rewrite"] : PASSPORT_HOST;
                    redirect($redirectUrl);
                }
                if ($result['gender'] == 0 && $gender != 0)
                {
                    $memberModel->edit($passid, array('gender' => $gender));
                }
                $result["m_uid"] = unserialize($result['m_uid']);
                $uid = $result['m_uid']['1'];
                $username = $result['username'];
                $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
                if (strpos($username, "#qq#") !== false)
                {
                    $usernameOld = $username;
                    $username = $memberModel->repairOldOAuthUser($passid, 'qq');
                    if ($username)
                    {
                        $needModifyName = 0;
                        $memberModel->patchUserLog($passid, array('username_old' => $usernameOld, 'username' => $username, 'op_time' => time(), 'op_ip' => get_client_ip(), 'op_domain' => 'login.2345.com'));
                        noticeToChange($passid, 'changeUsername', array(
                            'passid' => $passid,
                            'uid' => $uid,
                            'type' => 'username',
                            'value' => $username
                        ));
                    }
                    else
                    {
                        $needModifyName = 1;
                        $username = explode("#qq#", $usernameOld);
                        $username = $username[0];
                    }
                    $userMod = 100;
                }
                else
                {
                    $needModifyName = 0;
                }
                $cTime = time() + 3600 * 24 * 30;
                $loginAction = loadAction('login');
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                $cookie['need_modify_name'] = $needModifyName;
                $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);

                setcookie('oauth_state', null, time() - 3600, '/');
                setcookie('redirect_uri', null, time() - 3600, '/');

                // 设置其他域名的登录cookie： 2345.cn等
                $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
                if (!\Common\Utils\Http::isWeiXinBrowser() && !\Common\Utils\Http::isH5AppWebView())
                {
                    setcookie('qqforward', '', 0, '/');
                }
                $pageArray = $loginAction->getRedirectParams($thirdCallbackCookies, 'qq');

                return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $pageArray);
            }
            else
            {
                setcookie('oauth_type', 'qq', time() + 3600 * 24 * 30, '/', DOMAIN);
                setcookie('openid', $openid, 0, '/');
                setcookie('access_token', $accessToken, 0, '/');

                $redirect = LOGIN_HOST . '/oauth/bind';
            }
        }
        else
        {
            setcookie('oauth_state', null, time() - 3600, '/');
            setcookie('redirect_uri', null, time() - 3600, '/');

            //收集注册登录信息
            $st = false;
            $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
            $clientType = AccountStat::CTP_WEB;
            if (!$source)
            {
                $source = "login.2345.com";
            }
            elseif ($source == "app.2345.com")
            {
                $source = "andbs";
                $clientType = AccountStat::CTP_APP;
            }
            \Service\AccountStat\AccountStat::collect(AccountStat::TP_LOGIN, $clientType, AccountStat::AC_TP_OAUTH_QQ, $st, $source);
            if ($isUseRedis)
            {
                $redis->incr('LFN:qq_login_2345_com');
            }
            if (isset($_COOKIE['qqforward']))
            {
                setcookie('qqforward', '', 0, '/');
                $redirect = $_COOKIE['qqforward'];
            }
            else
            {
                $redirect = LOGIN_HOST;
            }
        }
        redirect($redirect);
    }

    /**
     * 判断qq是否登录
     */
    public function actionIslogin()
    {
        $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
        $oauthType = $_COOKIE['oauth_type'];
        $openid = $_COOKIE['openid'];
        $accessToken = $_COOKIE['access_token'];
        $ret = 1;
        if (!$openid || !$accessToken || $oauthType != 'qq')
        {
            $ret = 0;
        }
        $oauthAction = loadAction('oauth');
        $oauthUser = $oauthAction->oauthUserInfo($oauthType, $accessToken, $openid);
        if (!$oauthUser)
        {
            $ret = 0;
        }
        echo "$callback($ret);";
    }

}