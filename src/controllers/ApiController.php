<?php

class ApiController extends Controller
{
    /**
     * @return void
     */
    public function actionOalogin()
    {
        $uid = $this->Filter->getPost('uid');
        $username = $this->Filter->getPost('username');
        $mk = $this->Filter->getPost('mk');
        $url = "https://oa.2345.cn/api/login.php";
        $postArr = array(
            "uid"      => $uid,
            "username" => $username,
            "mk"       => $mk,
            "sysid"    => 180,
            "ip"       => $_SERVER['REMOTE_ADDR'],
        );
        $result = FuncAction::request($url, $postArr, ['referer' => 'http://www.oa.ruichuang.net/yigebucunzaidemulu/api.php'])[0];
        if (empty($result))
        {
            echo '账号没有OA权限';
            exit;
        }
        $result = unserialize($result);
        if (!is_array($result['auth']))
        {
            echo '账号没有OA权限';
            exit;
        }
        foreach ($result['auth'] as $resultInfo)
        {
            if ((int)$resultInfo['sysid'] === 180)
            {
                $userAuthModel = \Admin\UserAuthModel::getInstance();
                if (!$userAuthModel->getUserInfo(array('uid' => $uid, 'status' => 1))) {
                    echo "<h3>【短信平台权限申请步骤】</h3>
                        <ol>
                        <li>在OA权限中申请“短信平台”</li>
                        <li>请登录jira系统创建任务，项目选择中台支持（ZTZC），经办人：胡学文，注明申请人员，需要的项目以及权限，并在抄送栏填写部门负责人即可。</li>
                        </ol>";
                    exit;
                }
                \Admin\UserAction::setAdmin($uid);
                $_SESSION['userInfo'] = $postArr;
                $_SESSION['SMSPISLOGIN'] = true;
                $_SESSION['whitePath'] = array_map(function ($d) {
                    return strtolower($d['authPath']);
                }, $userAuthModel->getUserAccessPaths($uid));
                header('Location: /Admin/User/Index');
            }
        }
    }
}
