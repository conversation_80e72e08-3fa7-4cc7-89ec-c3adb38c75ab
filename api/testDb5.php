<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/6/20
 * Time: 17:53
 */


define("BASE_PATH", realpath(dirname(__FILE__) . "/../") . "/");
require_once BASE_PATH . "/include/init.php";
include_once BASE_PATH . "/config.inc.php";
$config = include(BASE_PATH . "app/config/config.php");

if ($_GET['user'] == 'dhm' && $_GET['token'] == '2iVrUHm1AqmqONolnjpBWnt')
{
    $dbConnect = '';
    $testMachine = "master";
    $host = $_ENV['DB_PASSPORT_NOTICE_MASTER_HOST'];
    $port = $_ENV['DB_PASSPORT_NOTICE_MASTER_PORT'];
    $dbname = 'passport_notice';
    $username = $_ENV['DB_PASSPORT_NOTICE_USERNAME'];
    $password = $_ENV['DB_PASSPORT_NOTICE_PASSWORD'];
    $res = testDb($host, $port, $dbname, $username, $password, $testMachine, $dbConnect);
    echo $res . "<br />";
    if (is_object($dbConnect))
    {
        $sth = $dbConnect->prepare('select * from testDb order by id DESC limit 1');
        $sth->execute();
        $row = $sth->fetch(PDO::FETCH_ASSOC);
        if (empty($row))
        {
            echo "查询无数据<br/>";
        }
        else
        {
            var_export($row);
            echo "<br/>";
        }
        if ($_GET['action'] == 'add')
        {
            $addSql = "INSERT INTO testDb (`name`,`status`,`add_time`) VALUES (':name' ,':status',':add_time' )";
            $stmt = $dbConnect->prepare($addSql);
            $stmt->execute(array(':name' => 'testDb', ':status' => 1, ':add_time' => date('Y-m-d H:i:s')));
            echo '插入ID：'. $dbConnect->lastinsertid();
        }
        if ($_GET['action'] == 'setRedis')
        {
            $redis = new Redis();
            $result = $redis->connect($_ENV['REDIS_DEFAULT_HOST'], $_ENV['REDIS_DEFAULT_PORT'], 3);
            $redis->auth('rc_redis');
            $redis->set('LoadPhone:stop:runing', 1);
        }
        $dbConnect = null;
    }
    else
    {
        show404Alarm($dbConnect);
    }
}
elseif ($_GET['user'] == 'testdb57')
{
    testDB57($config);
    testDB57slave();
}
elseif ($_GET['user'] == 'test172redis')
{
/*env0019*/ $res = testRedisNew($_ENV['REDIS_SLAVE2_HOST'], $_ENV['REDIS_SLAVE2_PORT'], $_ENV['REDIS_SLAVE2_AUTH']);
    if ($res)
    {
        show404Alarm($res);
    }
} elseif ($_GET['user'] == 'bigdataqueue') {
/*env0020*/ $host = $_ENV['REDIS_DEFAULT_HOST'];
    //$host = "redis.passport.2345.com";
/*env0021*/ $port = $_ENV['REDIS_DEFAULT_PORT'];
/*env0022*/ $pass = $_ENV['REDIS_DEFAULT_AUTH'];
    $key = 'BIG_DATA_REDIS_KEY';

    $redis48 = new Redis();
    $result48 = $redis48->connect($host, $port, 3);
    if (!$result48) {
        show404Alarm("连接失败");
        exit;
    }
    $redis48->auth($pass);
    $len = $redis48->lLen($key);
    $str = "消息队列 {$key} 长度:{$len} ";
    if ($len > 30000) {
        show404Alarm($str . "超过指定值 30000");
    } else {
        echo $str;
    }
}
elseif ($_GET['user'] == 'ip2region')
{
    $ret = testIp2region();
    $web = isset($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : 'cli';
    echo 'web ' . $web . "<br>";
    if (!$ret[0])
    {
        echo 'ip2region 服务不可用!';
        show404Alarm($ret[1]);
    }
    else
    {
        echo 'ip2region 服务返回:'. var_export($ret[1], true);
    }
} elseif ($_GET['user'] == 'redis198') {
    $redis198 = new Redis();
    $result198 = $redis198->connect('*************', 6379, 3);
    $redis198->auth('rc_redis');
    $result198 = $redis198->info();
    var_export($redis198->hLen('dlx:regPhoneInfo:shard'));
    echo "<br />";
    echo 'redis-198  db0:' . $result198['db0'];
    echo "<br />";
    echo "=============================";
    echo "<br />";

    $redis155 = new Redis();
/*env0023*/ $result155 = $redis155->connect($_ENV['DLX_MEMBERS_HASH2_REDIS_MASTER_HOST'], $_ENV['DLX_MEMBERS_HASH2_REDIS_MASTER_PORT'], 3);
/*env0024*/ $redis155->auth($_ENV['DLX_MEMBERS_HASH2_REDIS_MASTER_AUTH']);
    $result155 = $redis155->info();
    var_export($redis155->hLen('dlx:regPhoneInfo:shard'));
    echo "<br />";
    echo 'redis-155  db0:' . $result155['db0'];
    echo "<br />";
    echo "=============================";
    echo "<br />";
} elseif ($_GET['user'] == 'redis165') {
    $redis165 = new Redis();
    $result165 = $redis165->connect('*************', 6379, 3);
    $redis165->auth('rc_redis');
    $result165 = $redis165->info();
    echo 'redis-165  db0:' . $result165['db0'];
    echo "<br />";
    echo "=============================";
    echo "<br />";

    $redis130 = new Redis();
/*env0025*/ $result130 = $redis130->connect($_ENV['DLX_LOGINQUEUE_REDIS_MASTER_HOST'], $_ENV['DLX_LOGINQUEUE_REDIS_MASTER_PORT'], 3);
/*env0026*/ $redis130->auth($_ENV['DLX_LOGINQUEUE_REDIS_MASTER_AUTH']);
    $result130 = $redis130->info();
    echo 'redis-130  db0:' . $result130['db0'];
    echo "<br />";
    echo "=============================";
    echo "<br />";
} elseif ($_GET['user'] == "testDb_scj") {
    $dbConnect = '';
    $testMachine = "master";
/*env0027*/ $host = $_ENV['DB_MY_2345_MASTER_HOST'];
/*env0028*/ $port = $_ENV['DB_MY_2345_MASTER_PORT'];
    $dbname = 'my_2345';
/*env0029*/ $username = $_ENV['DB_MY_2345_USERNAME'];
/*env0030*/ $password = $_ENV['DB_MY_2345_PASSWORD'];
    $res = testDb($host, $port, $dbname, $username, $password, $testMachine, $dbConnect);
    echo $res . "<br />";
    if (is_object($dbConnect)) {
        $sth = $dbConnect->prepare("SELECT * FROM `fav_myset` order by id desc limit 1");
        $sth->execute();
        $row = $sth->fetch(PDO::FETCH_ASSOC);
        if (empty($row)) {
            echo "查询无数据<br/>";
        } else {
            var_export($row);
            echo "<br/>";
        }
    } else {
        echo "链接失败";
        show404Alarm($dbConnect);
    }
} elseif ($_GET['user'] == "mysql89") {
    $dbConnect = '';
    $testMachine = "master";
/*env0031*/ $host = $_ENV['DB_PASSPORT_USER_MASTER_HOST'];
/*env0032*/ $port = $_ENV['DB_PASSPORT_USER_MASTER_PORT'];
    $dbname = 'passport_user';
/*env0033*/ $username = $_ENV['DB_PASSPORT_USER_USERNAME'];
/*env0034*/ $password = $_ENV['DB_PASSPORT_USER_PASSWORD'];
    //    $host = '*************';
    //    $port = '3306';
    //    $dbname = 'passport_user';
    //    $username = 'root';
    //    $password = 'rcroot';

    $res = testDb($host, $port, $dbname, $username, $password, $testMachine, $dbConnect);
    echo $res . "<br />";
    if (is_object($dbConnect)) {
        $sth = $dbConnect->prepare('select * from union_login_config order by id DESC limit 1');
        $sth->execute();
        $row = $sth->fetch(PDO::FETCH_ASSOC);
        if (empty($row)) {
            echo "查询无数据<br/>";
        } else {
            var_export($row);
            echo "<br/>";
        }
        if ($_GET['action'] == 'add') {
            $addSql = "INSERT INTO union_login_config(`pull_list`,`verify_list`) VALUES ('{}' ,'{}')";
            $stmt = $dbConnect->prepare($addSql);
            $stmt->execute();
            echo '插入ID：' . $dbConnect->lastinsertid();
        }
        $dbConnect = null;
    } else {
        show404Alarm($dbConnect);
    }
} elseif ($_GET['user'] == 'checktime') {
    $serverIp = !empty($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : '';
    $str = $serverIp . ',' . time() . ',' . date('Y-m-d H:i:s');
    echo $str;
} elseif ($_GET['user'] == 'testuserdb') {
    // I master
    $testMachine = "master";
    $dbname = DB_PASSPORT_USER;
    $host = $config['database'][$dbname][$testMachine]['host'];
    $port = $config['database'][$dbname][$testMachine]['port'];
    $username = $config['database'][$dbname]['username'];
    $password = $config['database'][$dbname]['password'];
    $dbConnect = null;
    $res = testDb($host, $port, $dbname, $username, $password, $testMachine, $dbConnect);
    if (!is_object($dbConnect)) {
        $msg[] = $res;
    }

    // I slave
/*env0035*/ $host = $_ENV['DB_PASSPORT_AVATAR_SLAVE_HOST'];
/*env0036*/ $port = $_ENV['DB_PASSPORT_AVATAR_SLAVE_PORT'];
/*env0037*/ $username = $_ENV['DB_PASSPORT_AVATAR_USERNAME'];
/*env0038*/ $password = $_ENV['DB_PASSPORT_AVATAR_PASSWORD'];
// test
//    $host = '*************';
//    $port = 3307;
//    $username = 'root';
//    $password = 'rcroot';
    $dbConnect = null;
    $testMachine = 'slave';
    $res = testDb($host, $port, $dbname, $username, $password, $testMachine, $dbConnect);
    if (!is_object($dbConnect)) {
        $msg[] = $res;
    }
    $username = "rc_status";
    $password = "rc_status";
    // 测试主从
    $error = testSyncTime($host, $port, $username, $password);
    if ($error !== false) {
        $msg[] = $host . '<br>'. $error;
    }

    if (!empty($msg)) {
        show404Alarm(implode("<br /><br />", $msg));
    }
} elseif ($_GET['user'] == 'testDbAvatar') {
    // I master
    $testMachine = "master";
    $dbname = DB_PASSPORT_AVATAR;
    $host = $config['database'][$dbname][$testMachine]['host'];
    $port = $config['database'][$dbname][$testMachine]['port'];
    $username = $config['database'][$dbname]['username'];
    $password = $config['database'][$dbname]['password'];
    $dbConnect = null;
    $res = testDb($host, $port, $dbname, $username, $password, $testMachine, $dbConnect);
    if (!is_object($dbConnect)) {
        $msg[] = $res;
    }

        // I slave
/*env0039*/ $host = $_ENV['DB_PASSPORT_AVATAR_SLAVE_HOST'];
/*env0040*/ $port = $_ENV['DB_PASSPORT_AVATAR_SLAVE_PORT'];
/*env0041*/ $username = $_ENV['DB_PASSPORT_AVATAR_USERNAME'];
/*env0042*/ $password = $_ENV['DB_PASSPORT_AVATAR_PASSWORD'];

    $testMachine = 'slave';
    $dbConnect = null;
    $res = testDb($host, $port, $dbname, $username, $password, $testMachine, $dbConnect);
    if (!is_object($dbConnect)) {
        $msg[] = $res;
    }
    $username = "rc_status";
    $password = "rc_status";
    // 测试主从
    $error = testSyncTime($host, $port, $username, $password);
    if ($error !== false) {
        $msg[] = $host . '<br>' . $error;
    }

    if (!empty($msg)) {
        show404Alarm(implode("<br /><br />", $msg));
    }
} elseif ($_GET['user'] == 'apcu_cache' && $_GET['token'] == "911D88E46D244BAC5C1C75665CCAFFE9") {
    $key = "IP:WHITE:LIST";
    $ipList = apcu_fetch($key);
    echo "apcu内的值：";
    if (is_array($ipList)) {
        foreach ($ipList as $ip) {
            echo $ip . "<br />";
        }
    } else {
        echo "empty<br />";
    }
    $cacheInfo = apcu_cache_info();
    var_export($cacheInfo['cache_list']);
} elseif ($_GET['user'] == "kafka_len") {
    $redis = \RedisEx::getInstance();
    $key = 'BIG_DATA_REDIS_KEY';
    $len = $redis->lLen($key);
    $str = "消息队列 {$key} 长度:{$len} ";
    if ($len > 30000) {
        show404Alarm($str . "超过指定值 30000");
    } else {
        echo $str;
    }
}

/**
 * 测试数据库连接情况
 *
 * @param string $host $host
 * @param int $port $port
 * @param string $dbname $dbname
 * @param string $username $username
 * @param string $password $password
 * @param string $testMachine $testMachine
 * @param string $dbConnect $dbConnect
 *
 * @author：dongx
 * @return string
 */
function testDb($host, $port, $dbname, $username, $password, $testMachine, &$dbConnect)
{
    if ($fp = @fsockopen($host, $port, $errno, $errstr, 5))
    {
        @fclose($fp);
        $dsn = 'mysql:host=' . $host . ';port=' . $port . ';dbname=' . $dbname;
        try
        {
            $dbConnect = new PDO($dsn, $username, $password);
            return $testMachine . "主服务器数据库(IP:" . $host . ")，访问成功";
        }
        catch (PDOException $e)
        {
            return 'Connection failed: ' . $e->getMessage() . "<br />" . $testMachine . "服务器数据库(IP:" . $host . ")，链接不成功testDb5！";
        }
    }
    else
    {
        @fclose($fp);
        return $testMachine . "主服务器数据库(IP:" . $host . ")，访问不成功testDb5！";
    }
}

/**
 * User: panj
 * test db mysql 5.7 passport_main
 *
 * @param array $config config
 * @return null
 */
function testDB57($config)
{
    $databaseConfig = $config['database']['passport_main'];
    $host = $databaseConfig['master']['host'];
    $port = $databaseConfig['master']['port'];
    $username = $databaseConfig['username'];
    $password = $databaseConfig['password'];
    $dbname = DB_PASSPORT_MAIN;
    $testMachine = 'master';
    $dsn = 'mysql:host=' . $host . ';port=' . $port . ';dbname=' . $dbname;
    try
    {
        $dbConnect = new PDO($dsn, $username, $password);
        echo $testMachine . "主服务器数据库(IP:" . $host . ")，访问成功";
    }
    catch (PDOException $e)
    {
        $msg = 'Connection failed: ' . $e->getMessage() . "<br />" . $testMachine . "服务器数据库(IP:" . $host . ")，链接不成功testDb57！";
        show404Alarm($msg);
    }


    if (is_object($dbConnect))
    {
        $sth = $dbConnect->prepare('select * from union_login_config order by id DESC limit 1');
        $sth->execute();
        $row = $sth->fetch(PDO::FETCH_ASSOC);
        if (empty($row))
        {
            echo "查询无数据<br/>";
        }
        else
        {
            var_export($row);
            echo "<br/>";
        }
    }
    else
    {
        var_export($dbConnect);
    }
}


/**
 * User: panj
 * 183 slave testDb
 * @return null
 */
function testDB57slave()
{
    $dbConnect = '';
    $testMachine = "slave";
/*env0043*/ $host = $_ENV['DB_MY_2345_SLAVE_HOST'];
/*env0044*/ $port = $_ENV['DB_MY_2345_SLAVE_PORT'];
    $username = 'rc_status';
    $password = 'rc_status';
    $error = testSyncTime($host, $port, $username, $password);
    if ($error !== false)
    {
        $msg = $host . '<br>'. $error;
        show404Alarm($msg);
    }
    else
    {
        echo $host . '主从正常';
    }
}

/**
 * User: panj
 * 判断主服务器同步到从服务器的同步时间
 *
 * @param string  $host host
 * @param int $port port
 * @param string $username username
 * @param string $password password
 *
 * @return bool|string
 */
function testSyncTime($host, $port, $username, $password)
{
    $dsn = 'mysql:host=' . $host . ';port=' . $port;
    try
    {
        $dbh = new PDO($dsn, $username, $password);
        $sth = $dbh->prepare('show slave status;');
        $sth->execute();
        $row = $sth->fetch(PDO::FETCH_ASSOC);
        if (empty($row))
        {
            return "测试主从同步失败:show slave status 失败";
        }
        if ($row['Seconds_Behind_Master'] > 15)
        {
            return "从库同步数据时间大于15秒,为：" . $row['Seconds_Behind_Master'] . '__';
        }
        elseif ($row["Slave_IO_Running"] != "Yes")
        {
            return "Slave_IO_Running值不为Yes, 为：" . $row["Slave_IO_Running"] . "__";
        }
        elseif ($row["Slave_SQL_Running"] != "Yes")
        {
            return "Slave_SQL_Running值不为Yes, 为：" . $row["Slave_SQL_Running"] . "__";
        }
    }
    catch (PDOException $e)
    {
        return '测试主从同步连接失败: ' . $e->getMessage();
    }
    return false;
}

/**
 * User: panj
 *
 * @param string $ip IPV4 address
 *
 * @return array
 */
function testIp2region($ip = '**************')
{
    if (!filter_var($ip, FILTER_VALIDATE_IP))
    {
        exit("$ip is a invalid IP address");
    }
    $config = [
        'host' => '127.0.0.1',
        'port' => '9990',
    ];
    // 创建 连接 发送消息 接收响应 关闭连接
    $socket = socket_create(AF_INET, SOCK_STREAM, 0);
    if ($socket == false)
    {
        return [false, 'socket_create 失败'];
    }
    //超时1秒
    socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, ["sec" => 1, "usec" => 0]);
    socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, ["sec" => 1, "usec" => 0]);
    $connected = socket_connect($socket, $config['host'], $config['port']);
    if ($connected == false)
    {
        return [false, 'socket_connect 失败'];
    }
    $send = socket_send($socket, $ip, strlen($ip), 0);
    if ($send == false)
    {
        return [false, 'socket_send 失败'];
    }
    $response = socket_read($socket, 1024);
    socket_close($socket);
    loadAction('Encoding');
    $response = EncodingAction::transcoding($response);
    return [true, $response];
}

/**
 * User: panj
 * test db 报警
 *
 * @param string $msg msg
 * @return null
 *
 */
function show404Alarm($msg)
{
    header("HTTP/1.1 404 Not Found");
    echo $msg;
}

/**
 * testRedisNew
 * -
 * @param string $host Host
 * @param int $port Port
 * @param string $password PWD
 * @return bool|string
 * <AUTHOR>
 */
function testRedisNew($host, $port, $password)
{
    $redis = new Redis();
    $result = $redis->connect($host, $port, 3);
    if ($result != 1)
    {
        return "redis服务器(IP:" . $host . ")，链接不成功testDb4！";
    }
    else
    {
        $redis->auth($password);
        $memory = $redis->info("MEMORY");
        // 10737418240 10G
        if (!isset($memory["used_memory"]) || $memory["used_memory"] > 10737418240)
        {
            $redis->close();
            return "redis服务器(IP:" . $host . ")，使用内存超过10G！";
        }
        $testKey = 'LSN:login_2345_com:phone';
        $getResult = $redis->get($testKey);
        if (!$getResult)
        {
            $redis->close();
            return "redis服务器(IP:" . $host . ")，get值失败！";
        }
        $redis->close();
        return false;
    }
}
