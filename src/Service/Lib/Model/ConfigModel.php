<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/9/10
 * Time: 13:26
 */
namespace Service\Lib\Model;

use Admin\BlackListModel;
use Service\Func\FileConfigFunc;
use Service\Lib\Provider;

class ConfigModel
{

    /**
     * 手机黑名单列表
     * redis 和 混合模式 都使用redis数据
     * mysql 读取数据库写文件
     * -
     * @param int $phone 手机号码
     * @return bool|string
     */
    public function isInBlackList($phone)
    {
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                $getRedisAction = \GetRedisAction::getInstance();
                $response = $getRedisAction->isInBlackList($phone);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                $fileName = 'phoneBlackListConfig';
                if (FileConfigFunc::isReloadFileConfig($fileName))
                {
                    $queryData = [
                        'status' => 1
                    ];
                    $black = BlackListModel::getInstance()->getUserBlackList($queryData);
                    $arrConfig = [];
                    foreach ($black as $blackInfo)
                    {
                        $arrConfig[] = $blackInfo['phone'];
                    }
                    $msg = 'return ' . var_export($arrConfig, true);
                    FileConfigFunc::writeFileConfig($fileName, $msg, 'w');
                    $response = in_array($phone, $arrConfig) ? true : false;
                }
                else
                {
                    $blackListConfig = FileConfigFunc::getFileConfig($fileName);
                    if (is_array($blackListConfig))
                    {
                        $response = in_array($phone, $blackListConfig) ? true : false;
                    }
                    else
                    {
                        $queryData = [
                            'phone' => $phone,
                            'status' => 1
                        ];
                        $black = BlackListModel::getInstance()->getUserBlackList($queryData);
                        unlink(FileConfigFunc::getFileNamePath($fileName));
                        $response = !empty($black) ? true : false;
                    }
                }
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $getRedisAction = \GetRedisAction::getInstance();
                $response = $getRedisAction->isInBlackList($phone);
                break;
            default:
                $getRedisAction = \GetRedisAction::getInstance();
                $response = $getRedisAction->isInBlackList($phone);
        }
        return $response;
    }

    /**
     * 根据项目获取项目短信配置
     * -
     * @param int $pid pid
     * @return array|string
     */
    public function getSmsConfigProjectTypeList($pid)
    {
        $getRedisAction = \GetRedisAction::getInstance();
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                $response = $getRedisAction->getSmsConfigProjectTypeList($pid);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                $fileName = 'smsProjectConfig/pid_' . $pid;
                if (FileConfigFunc::isReloadFileConfig($fileName))
                {
                    $list = \SmsModel::getInstance()->getSmsConfigProjectTypeList($pid);
                    $smsTypes = array();
                    if (!empty($list))
                    {
                        foreach ($list as $ele)
                        {
                            $smsTypes[] = $ele['tid'];
                        }
                    }
                    $response = $smsTypes;
                    FileConfigFunc::writeFileConfig($fileName, 'return ' . var_export($smsTypes, true), 'w');
                }
                else
                {
                    $smsTypes = FileConfigFunc::getFileConfig($fileName);
                    //如果是数组 则返回， 不是数组重新查库
                    if (is_array($smsTypes))
                    {
                        $response = $smsTypes;
                    }
                    else
                    {
                        $list = \SmsModel::getInstance()->getSmsConfigProjectTypeList($pid);
                        $smsTypes = array();
                        if (!empty($list))
                        {
                            foreach ($list as $ele)
                            {
                                $smsTypes[] = $ele['tid'];
                            }
                        }
                        $response = $smsTypes;
                        unlink(FileConfigFunc::getFileNamePath($fileName));
                    }
                }
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $response = $getRedisAction->getSmsConfigProjectTypeList($pid);
                break;
            default:
                $response = $getRedisAction->getSmsConfigProjectTypeList($pid);
        }
        return $response;
    }

    /**
     * 根据短信类型获取相关的配置信息
     * -
     * @param int $smsType 短信类型
     * @return array|bool|mixed|string
     */
    public function getSmsTypeConfig($smsType)
    {
        $getRedisAction = \GetRedisAction::getInstance();
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                $response = $getRedisAction->getSmsTypeConfig($smsType);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                $fileName = 'smsTypeConfig/type_' . $smsType;
                if (FileConfigFunc::isReloadFileConfig($fileName))
                {
                    $smsTypeData = \SmsModel::getInstance()->getSmsConfig($smsType);
                    $response = $smsTypeData;
                    FileConfigFunc::writeFileConfig($fileName, 'return ' . var_export($smsTypeData, true), 'w');
                }
                else
                {
                    $smsTypeData = FileConfigFunc::getFileConfig($fileName);
                    if (is_array($smsTypeData))
                    {
                        $response = $smsTypeData;
                    }
                    else
                    {
                        $smsTypeData = \SmsModel::getInstance()->getSmsConfig($smsType);
                        $response = $smsTypeData;
                        unlink(FileConfigFunc::getFileNamePath($fileName));
                    }
                }
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $response = $getRedisAction->getSmsTypeConfig($smsType);
                break;
            default:
                $response = $getRedisAction->getSmsTypeConfig($smsType);
        }
        return $response;
    }

    /**
     * 手机白名单列表
     * -
     * @param int $phone 手机号码
     * @return bool|string
     */
    public function isPhoneWhiteList($phone)
    {
        $getRedisAction = \GetRedisAction::getInstance();
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                $response = $getRedisAction->isPhoneWhiteList($phone);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                $fileName = 'phoneWhiteListConfig';
                if (FileConfigFunc::isReloadFileConfig($fileName))
                {
                    $queryData = [
                        'status' => 2
                    ];
                    $black = BlackListModel::getInstance()->getUserBlackList($queryData);
                    $arrConfig = [];
                    foreach ($black as $blackInfo)
                    {
                        $arrConfig[] = $blackInfo['phone'];
                    }
                    $msg = 'return ' . var_export($arrConfig, true);
                    FileConfigFunc::writeFileConfig($fileName, $msg, 'w');
                    $response = in_array($phone, $arrConfig) ? true : false;
                }
                else
                {
                    $blackListConfig = FileConfigFunc::getFileConfig($fileName);
                    if (is_array($blackListConfig))
                    {
                        $response = in_array($phone, $blackListConfig) ? true : false;
                    }
                    else
                    {
                        $queryData = [
                            'phone' => $phone,
                            'status' => 2
                        ];
                        $black = BlackListModel::getInstance()->getUserBlackList($queryData);
                        unlink(FileConfigFunc::getFileNamePath($fileName));
                        $response = !empty($black) ? true : false;
                    }
                }
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $response = $getRedisAction->isPhoneWhiteList($phone);
                break;
            default:
                $response = $getRedisAction->isPhoneWhiteList($phone);
        }
        return $response;
    }

    /**
     * 根据手机号码和类型时间获取发送条数
     * -
     * @param int $phone 手机号码
     * @param int $type 类型
     * @param string $date 时间
     * @return int|string
     */
    public function getSmsNumByPhoneType($phone, $type, $date)
    {
        $getRedisAction = \GetRedisAction::getInstance();
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                $response = $getRedisAction->getSmsNumByPhoneType($phone, $type, $date);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                $response = \SmsModel::getInstance()->getSmsNumByPhoneType($phone, $type, $date);
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $response = $getRedisAction->getSmsNumByPhoneType($phone, $type, $date);
                break;
            default:
                $response = $getRedisAction->getSmsNumByPhoneType($phone, $type, $date);
        }
        return $response;
    }

    /**
     * 访问权限控制
     * @return bool
     */
    public static function accessCheck()
    {
        $GetRedisAction = \GetRedisAction::getInstance();
        if (PHP_SAPI !== 'cli')
        {
            $serverIp = get_client_ip();
        }
        elseif (!isset($serverIp))
        {
            $serverIp = "";
        }
        //获取ip白名单
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                $ips = $GetRedisAction->isIpWhiteList($serverIp);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                $fileName = 'ipWhiteList';
                if (FileConfigFunc::isReloadFileConfig($fileName))
                {
                    $ipList = [];
                    $whiteList = BlackListModel::getInstance()->getIpWriteList();
                    foreach ($whiteList as $whiteInfo)
                    {
                        $ipList[] = $whiteInfo['ip'];
                    }
                    FileConfigFunc::writeFileConfig($fileName, 'return ' . var_export($ipList, true), 'w');
                    $ips = in_array($serverIp, $ipList) ? true : false;
                }
                else
                {
                    $ipList = FileConfigFunc::getFileConfig($fileName);
                    if (is_array($ipList))
                    {
                        $ips = in_array($serverIp, $ipList) ? true : false;
                    }
                    else
                    {
                        unlink(FileConfigFunc::getFileNamePath($fileName));
                        $queryData = [
                            'ip' => $serverIp,
                        ];
                        $writeIp = BlackListModel::getInstance()->getIpWriteList($queryData);
                        $ips = !empty($writeIp) ? true : false;
                    }
                }
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $ips = $GetRedisAction->isIpWhiteList($serverIp);
                break;
            default:
                $ips = $GetRedisAction->isIpWhiteList($serverIp);
        }
        if ($ips == true)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     * 接口统计访问量
     * -
     * @param int $num 提交数
     * @param string $apiType a请求的pi
     * @param int $smsType 短信类型
     * @param int $projectId 项目ID
     * @return void
     */
    public function incrMonitor($num, $apiType, $smsType, $projectId)
    {
        $GetRedisAction = \GetRedisAction::getInstance();
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                $GetRedisAction->incrMonitor($num, $apiType, $smsType, $projectId);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                //单独数据库模式 暂不统计访问量
//                $day = date("Y-m-d");
//                $hour = date('H') * 6 + floor(date('i') / 10);
//                $this->incrKeySum("sms:monitor:all:{$day}", $hour, $num);
//                $this->incrKeySum("sms:monitor:api:{$apiType}:{$day}", $hour, $num);
//                $this->incrKeySum("sms:monitor:type:{$smsType}:{$day}", $hour, $num);
//                $this->incrKeySum("sms:monitor:proj:{$projectId}:{$day}", $hour, $num);
//                $this->incrKeySum("sms:monitor:typeSum:{$day}", $smsType, $num);
//                $this->incrKeySum("sms:monitor:projSum:{$day}", $projectId, $num);
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $GetRedisAction->incrMonitor($num, $apiType, $smsType, $projectId);
                break;
            default:
                $GetRedisAction->incrMonitor($num, $apiType, $smsType, $projectId);
        }
    }

    /**
     * 设置logid和msgid之间的关系  减轻数据库的压力
     * -
     * @param int $phone 手机号码
     * @param int $time 时间
     * @param int $msgId msgid
     * @param int $logId logid
     * @return void
     */
    public function setexMsgLogAndLogId($phone, $time, $msgId, $logId)
    {
        $msgIdAndPhoneKey = "CHANNEL_DATA:" . $msgId . ':' . $phone;
        $GetRedisAction = \GetRedisAction::getInstance();
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                ////将msgId:phone => logId的对应关系写入redis的set，减轻数据库查询的压力
                $GetRedisAction->redis->setex($msgIdAndPhoneKey, $time, $logId);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                //数据库模式 无需写入对应关系
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $GetRedisAction->redis->setex($msgIdAndPhoneKey, $time, $logId);
                break;
            default:
                $GetRedisAction->redis->setex($msgIdAndPhoneKey, $time, $logId);
        }
    }

    /**
     * 设置手机号码短信类型下发了多少条
     * -
     * @param int $phone 手机号码
     * @param int $smsType 短信类型
     * @param string $date 时间
     * @return void
     */
    public function setSmsNumByPhoneType($phone, $smsType, $date)
    {
        $GetRedisAction = \GetRedisAction::getInstance();
        switch (Provider::getStorageMold())
        {
            case Provider::STORAGE_TYPE_REDIS:
                $GetRedisAction->setSmsNumByPhoneType($phone, $smsType, $date);
                break;
            case Provider::STORAGE_TYPE_MYSQL:
                //数据库模式发送次数不用写
                break;
            case Provider::STORAGE_TYPE_REDIS_AND_MYSQL:
                $GetRedisAction->setSmsNumByPhoneType($phone, $smsType, $date);
                break;
            default:
                $GetRedisAction->setSmsNumByPhoneType($phone, $smsType, $date);
        }
    }
}
