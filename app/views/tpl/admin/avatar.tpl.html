<!doctype html>
<html>
<head>
    <meta charset="gb2312">
    <title>头像审核-未审核</title>
    <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
    <link href="/css/v3/zhanghao.css" rel="stylesheet">
    <link rel="stylesheet" href="/css/v3/top.css">
    <script type="text/javascript" src="/js/datepicker/WdatePicker.js"></script>
    <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
</head>

<body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
<div class="mainWrap">

    <!--面包屑导航 开始-->
        <div class="location">位置：<a target="_blank" href="#">帐号体系</a>  > <span>头像审核-未审核</span></div>
        <!--面包屑导航 结束-->
        
        <!--Tab菜单 开始-->
        <div class="TabMenu mt10">
        	<a class="currTabMenu" href="javascript:">未审核</a>
            <a href="/houtai_gl/avatarCheck/vetted">已审核</a>            
        </div>
        
        <form method="get" name="searchForm">
        <!--日期和下拉菜单 开始-->
        <div class="funList mt20 clearfix">
        	<div class="dataWrap clearfix">
                <em>日期：</em>
                <div class="dataList w130">
                    <div class="getTime"><input type="text" autocomplete="off" name="starttime" onClick="javascript:WdatePicker();" value="{{$pageArray.starttime}}"/><i>日历icon</i></div>
                </div>
            </div>
            
            <span class="dataTxt">至</span>
            
            <div class="dataWrap clearfix">
                <div class="dataList w130">
                    <div class="getTime"><input type="text" autocomplete="off"  name="endtime" onClick="javascript:WdatePicker();" value="{{$pageArray.endtime}}"><i>日历icon</i></div>
                </div>
            </div>
            <div class="left ml10 clearfix">
                <a href="javascript:" class="styleBtnBlue left ml10" onclick="document.forms['searchForm'].submit();return false;" >查询</a>
            </div>
        </div><!--funList end-->
        <!--日期和下拉菜单 结束-->
        </form>
  
    <!--top_cz end-->

    <div class="contentWrap">
        <div class="tx_listWarp mt20">     
        <div class="tx_list">
            {{foreach from = $pageArray.avatar item=avatar key=key}}
            <p class="time_title">{{$key}}</p>
            <ul class="user_list clearfix">
                {{foreach from = $avatar key=num item = avatarvalue}}
                <li><img class="user_pic" src="/member/avatar/{{$avatarvalue.path}}">
                    <em>{{$avatarvalue.passid}}</em>
                    <i class="close-pic"></i>
                    <input style="display: none" name="checkid" value="{{$avatarvalue.passid}}">
                </li>
                {{/foreach}}
            </ul>
            <div class="mt20 clearfix">
                <a class="styleBtnBlue left ml20 check" href="javascript:">审核通过</a>
                <a class="styleBtnBlue left ml20 delete" href="javascript:">批量删除</a>
            </div>
            {{/foreach}}
        </div>
        </div>
    </div>
    <!--tx_listWarp end-->
</div>
<!--tx_warp end-->
</body>
</html>

<script type="text/javascript" src="/js/boxfixed.js"></script>
<script>
    $(function () {
        $(".user_pic").bind("click", function () {
            $(this).toggleClass("curr_userpic");
            $(this).parent().find(".close-pic").toggle();
            if ($(this).parent().find("input").attr("name") == "checkid") {
                $(this).parent().find("input").attr("name", "deleteid");
            }
            else {
                $(this).parent().find("input").attr("name", "checkid");
            }
        });
        $(".check").bind("click", function () {
            var userlist = $(this).parent().prev(".user_list");
            var checkinput = userlist.find("input[name=checkid]");
            var checklist = new Array();
            if (checkinput.length > 0) {
                checkinput.each(
                        function () {
                            checklist.push($(this).val());
                        });
                $.ajax(
                        {
                            type: "post",
                            url: "/houtai_gl/avatarCheck/vet",
                            data: {'checklist': checklist},
                            success:function(data)
                            {
                                var retobj = $.parseJSON(data);
                                alert(retobj.msg);
                                window.location ="/houtai_gl/avatarCheck/"
                            }
                        }
                );
            }
            else {
                alert("没有通过审核的头像");
            }
        });
        
        $(".delete").boxVertical({
    		initSetting: function (settings){
    		    var nums = $(".delete").parent().prev(".user_list").find("input[name=deleteid]").length;
                settings.boxTitle= '您确定要删除这' + nums + '张头像吗？'
    		    settings.boxTxt = '您确定要删除这' + nums + '张头像吗？'
                if ( nums <= 0 )
                {
                    settings.isShow = false;
                     alert("没有需要删除的头像");
                }  
            },
            callbackFun:function(obj){
    		   var userlist = obj.parent().prev(".user_list");
                var deleteinput = userlist.find("input[name=deleteid]");
                var deletelist = new Array();
                if (deleteinput.length > 0)
                {
                    deleteinput.each(
                            function () {
                                deletelist.push($(this).val());
                            });
                    $.ajax(
                                {
                                    type: "post",
                                    url: "/houtai_gl/avatarCheck/delete",
                                    data: {'deletelist': deletelist},
                                    success: function (data) {
                                        var retobj = $.parseJSON(data);
                                        alert(retobj.msg);
                                        window.location = "/houtai_gl/avatarCheck/"
                                    }
                                }
                        );
                        
                }
                else {
                    alert("没有需要删除的头像");
                }
    		}
    	});
        
        
        $(".more_li").bind("click", function(){
            var hideli = $(this).parent().find("li:hidden");
            if(hideli.length > 0)
            {
                hideli.each(function(){
                    $(this).show();
                });
                $(this).hide();
            }
        });
    });//JQ
    
    $('.navList a').each(function (){
        if (  $(this).attr('href') == '/houtai_gl/avatar_check' )
        {
            $(this).addClass('select');
        }
    });
</script>
