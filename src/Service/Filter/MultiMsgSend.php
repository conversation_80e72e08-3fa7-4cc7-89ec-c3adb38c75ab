<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：MultiMsgSend.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：31/08/2018 09:59
 */

namespace Service\Filter;

use Service\Func\RequestFunc;
use Service\Func\SmsFunc;
use Service\Lib\Model\ConfigModel;
use Service\Lib\Provider;
use Service\Swoole\Client;
use Service\Swoole\Tool\Log;

class MultiMsgSend extends BaseSend
{
    protected $phoneMsgArr;
    protected $phoneList;
    protected $multiContent = [];
    protected $illegalPhoneList;
    protected $failResponse;
    protected $successResponse;

    /**
     * User: panj
     * 前置必要检查
     *
     * @param array $data data
     *
     * @return mixed
     */
    public function preCheck($data)
    {
        $requestFunc = new RequestFunc();
        //前置检测必要字段
        $isTrue = $requestFunc->checkRequireParams($data);

        if ($isTrue !== true)
        {
            list($status, $msg) = $isTrue;

            return $this->setReturnInfo($status, $msg);
        }
        else
        {
            if (count($this->phoneMsgArr) > RequestFunc::MULTI_SEND_MAX_LIMIT)
            {
                $status = RequestFunc::MULTI_SEND_PHONE_NUMBER_NOT_ALLOWED;
                $msg = RequestFunc::$multiErrStr[RequestFunc::MULTI_SEND_PHONE_NUMBER_NOT_ALLOWED];

                return $this->setReturnInfo($status, $msg);
            }

            foreach ($this->phoneMsgArr as $value)
            {
                list($packageId, $phone, $msg) = explode("|", $value);
                if (mb_strlen($msg, "UTF-8") > RequestFunc::MSG_CONTENT_LENGTH)
                {
                    $status = RequestFunc::SMS_CONTENT_LENGTH_TOO_LONG;
                    $msg = RequestFunc::$errText[RequestFunc::SMS_CONTENT_LENGTH_TOO_LONG];

                    return $this->setReturnInfo($status, $msg);
                }
            }
        }
    }

    /**
     * User: panj
     * 生成Logid,存储原始请求数据
     *
     * @return mixed
     */
    public function initLogId()
    {
        //sessionId = 定义一个唯一的sessionid； 用来表示该群发为同一个会话.
        $sessionId = uniqid();
        $insertData = array();

        $column = array(
            "phone",
            "encrypt_phone",
            "text",
            "sessionid",
            "type",
            "send_time",
            "send_status",
            "send_response",
            "passid",
            "pid",
            'business_id',
            'client_ip',
            'server_ip',
        );

        foreach ($this->phoneMsgArr as $value)
        {
            list($packageId, $phone, $msg) = explode("|", $value);
            $this->phoneList[] = $phone;
            if (isset($insertData[$phone]))
            {
                continue;
            }
            $insertData[$phone] = array(
                $phone,
                $phone,
                $msg,
                $sessionId,
                $this->smsType,
                date("Y-m-d H:i:s"),
                - 22,
                '等待发送',
                0,
                $this->pid,
                $this->positionId,
                $this->clientIp,
                get_client_ip(),
            );
        }

        $phoneLogIds = (new Provider())->setSmsLogs($insertData, $column);
        if ($phoneLogIds)
        {
            foreach ($this->phoneMsgArr as $value)
            {
                list($packageId, $phone, $msg) = explode("|", $value);
                $this->phoneList[] = $phone;
                $logId = $phoneLogIds[$phone];
                $this->multiContent[$packageId] = array($phone, $msg, $logId);
            }
        }
        else
        {
            $info = var_export($insertData, true) . PHP_EOL . ' setSmsLogs return :' . var_export($phoneLogIds, true);
            \WebLogger\Facade\LoggerFacade::info($info);
            $status = RequestFunc::RECEIVE_SMS_REQUEST_FAILED;
            $msg = RequestFunc::$errText[RequestFunc::RECEIVE_SMS_REQUEST_FAILED];

            return $this->setReturnInfo($status, $msg, []);
        }
    }

    /**
     * User: panj
     * 严格后置检查,黑白名单
     *
     *
     * @return mixed
     */
    public function strictCheck()
    {
        $configModel = new ConfigModel();
        list($legalPhoneList, $this->illegalPhoneList) = SmsFunc::checkMultiPhone($this->phoneList);
        if (empty($legalPhoneList))
        {
            $status = RequestFunc::MULTI_SEND_LIMIT;
            $msg = RequestFunc::$multiErrStr[RequestFunc::MULTI_SEND_LIMIT];

            return $this->setReturnInfo($status, $msg);
        }
        $duplicatePhoneLogId = array();
        foreach ($this->multiContent as $packageId => $value)
        {
            list($phone, $msg, $logId) = $value;
            if (in_array($phone, $this->illegalPhoneList))
            {
                unset($this->multiContent[$packageId]);
                $this->failResponse[$packageId] = array("logId" => "", "phone" => $phone, "msg" => "手机号黑名单或者不合法");
            }
            else
            {
                if (isset($duplicatePhoneLogId[$phone]))
                {
                    unset($this->multiContent[$packageId]);
                    $this->failResponse[$packageId] = array(
                        "logId" => $duplicatePhoneLogId[$phone],
                        "phone" => $phone,
                        "msg"   => "手机号重复",
                    );
                }
                else
                {
                    $duplicatePhoneLogId[$phone] = $logId;
                }
            }
        }

        // 查看smsType 是否在 pid 短信类型之中
        $smsTypes = $configModel->getSmsConfigProjectTypeList($this->pid);
        if (!is_array($smsTypes) || !in_array($this->smsType, $smsTypes))
        {
            $status = RequestFunc::INVALID_SMS_TYPE;
            $msg = RequestFunc::$errText[RequestFunc::INVALID_SMS_TYPE];

            return $this->setReturnInfo($status, $msg);
        }

        // ip白名单 检测
        if (!ConfigModel::accessCheck())
        {
            $status = RequestFunc::REMOTE_IP_CHECK_FAILED;
            $msg = RequestFunc::$errText[RequestFunc::REMOTE_IP_CHECK_FAILED];

            return $this->setReturnInfo($status, $msg);
        }

        //获取类型配置
        $smsConfigs = $configModel->getSmsTypeConfig($this->smsType);
        $smsConfig = SmsFunc::randomTypeConfig($smsConfigs);
        //通道关闭检测
        $check = SmsFunc::checkChannelStatus($smsConfig);
        if ($check[0] < 0)
        {
            list($status, $msg) = $check;

            return $this->setReturnInfo($status, $msg);
        }
        if (isset($smsConfig['mobile_timeQuantum']) && !SmsFunc::checkTimeQuantum($smsConfig['mobile_timeQuantum']))
        {
            $status = RequestFunc::TIME_NOT_ALLOWED_FOR_CHANNEL;
            $msg = RequestFunc::$errText[RequestFunc::TIME_NOT_ALLOWED_FOR_CHANNEL];

            return $this->setReturnInfo($status, $msg);
        }
        $class = $smsConfig['mobile_action_name'];
        // 通道类名
        if (empty($class) || !class_exists($class))
        {
            $status = RequestFunc::WRONG_CHANNEL_CONFIG_CLASS;
            $msg = RequestFunc::$errText[RequestFunc::WRONG_CHANNEL_CONFIG_CLASS];

            return $this->setReturnInfo($status, $msg);
        }
    }

    /**
     * User: panj
     * 封装数据，提交调取服务商接口
     *
     * @param array $data data
     *
     * @return mixed
     */
    public function request($data)
    {
        $configModel = new ConfigModel();
        $configModel->incrMonitor(count($this->phoneMsgArr), 'batchSend', $this->smsType, $this->pid);

        $data['multiContent'] = $this->multiContent;
        $data['illegalPhoneList'] = $this->illegalPhoneList;
        $data['failResponse'] = $this->failResponse;
        $data['successResponse'] = $this->successResponse;
        $requestSwooleData = array_merge($data, [
            'ReceiveHandle' => 'ProcessHandle',
            'Handle' => 'MultiSendHandle',
            'sync' => false,
            'StorageType' => Provider::getStorageMold()
        ]);

        $res = Client::sendTo($requestSwooleData);
        return $this->setReturnInfo($res['status'], $res['msg'], $res['data']);
    }

    /**
     * User: panj
     * 初始化数据
     *
     * @param array $data data
     * @return null
     */
    public function initData($data)
    {
        parent::initData($data);
        $this->phoneMsgArr = explode(",", $this->phoneMsg);
    }

    /**
     * User: panj
     * 程序入口
     *
     * @param array $data 发送数据
     *
     * @return array
     */
    public function run($data)
    {
        $this->initData($data);
        if ($this->breakOff)
        {
            return $this->getReturnInfo();
        }

        //生成LogId的前置检查
        $this->preCheck($data);
        if ($this->breakOff)
        {
            return $this->getReturnInfo();
        }

        //生成LogId,存原始请求数据
        $this->initLogId();
        if ($this->breakOff)
        {
            return $this->getReturnInfo();
        }

        //提交服务商之前的黑白名单等严格检查
        $this->strictCheck();
        if ($this->breakOff)
        {
            return $this->getReturnInfo();
        }
        $this->request($data);

        return $this->getReturnInfo();
    }
}
