<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：Mid.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：09-20, 2016
 */

namespace Common\Validator;

use Common\Msg\ResponseMsg;

class Mid extends Base
{

    public static function validator($value, array $args)
    {
        $res = array(ResponseMsg::SUCCESS, "正确的mid");
        $midArr = \Config::get("mid");
        if (!isset($midArr[$value]))
        {
            $res = ResponseMsg::getCodeMsgFormat(ResponseMsg::PARAMS_FORMAT, $args[0], "不正确的mid");
        }
        return $res;
    }

}