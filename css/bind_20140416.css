@charset "gb2312";
/* 
 * Created : 2014/03/07
 * Author  : <PERSON><PERSON>
 */
/* reset */
body,p,dl,dt,dd,table,td,th,input,img,form,div,span,ul,ol,li,h1,h2,h3,h4,h5,h6,select,input,sub,sup{margin:0;padding:0;}
body{ background:#fff url(../images/bind-wrapbg-re.jpg) repeat-x 0 99px;font:12px/1.5 Arial;}
button,input,select,textarea{ font-family:tahoma,arial,simsun}
img,iframe{border:none;}
ul,li,ol{list-style:none;}
img{vertical-align:middle;}
input{ outline:none;}
em,b,i,strong,cite,sub,sup{font-style: normal;}
a{color:#333; text-decoration:none;}
a:hover{text-decoration:underline;color:#f30;}
.clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.clearfix{*zoom:1;}

.inner{ width:990px; margin:0 auto;}
/* top */
.top{ height:99px;}
.top .inner{ position:relative;}
.top_link{ display:inline-block; position:absolute; height:99px; line-height:99px; top:0; right:0; color:#d9d9d9;}
.top_link a{ color:#666; margin:8px;}
.top_link a:hover{ color:#f30;}
.top .logo{ display:inline-block; position:absolute; left:0; top:0; width:214px; height:45px;}
.top .logo img{ margin-top:27px;}
/* container */
.container{min-width:990px;background: url(../images/bind-wrapbg.jpg) no-repeat 50% 0;}
.container .inner{overflow: hidden;position:relative;}
.bind-wrap{width:488px;margin:31px 0 0 231px;background: #fff;}
.bind-wrap .bind-head{padding:32px 15px 0 205px;height:130px;position:relative;*zoom:1;}
.bind-wrap .bind-head .avatar{position:absolute;left:49px;top:20px;display:inline-block;width:120px;height:120px;padding:11px;background: url(../images/bind-sprite-********.png) no-repeat 0 -217px;}
.bind-wrap .bind-head h3{height:40px;border-bottom:1px dashed #a3b1b8;font:normal 16px/40px Verdana;color:#909090;}
.bind-wrap .bind-head h3 strong.name{color:#6db92f;font-weight: normal;}
.bind-wrap .bind-head p{font-size:14px;line-height: 22px;color:#909090;font-family: 'Microsoft YaHei';padding:5px 0;}

.bind-form{padding:0 60px 0 54px;}
.bind-form .title{font:normal 16px/1.5 'Microsoft YaHei'; color:#909090;padding:10px 0;}
.bind-form .form-item:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.bind-form .form-item{margin-bottom: 14px;*zoom:1;}
.bind-form .form-item .form-m{height:39px;width:370px;border:1px solid #d9d9d9;*zoom:1;background: url(../images/bind-input-rebg.png) repeat-x;position:relative;overflow: hidden;}
.bind-form .form-item .form-m:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.bind-form .form-item .lab{float: left;width:54px;height:39px;border-right:1px solid #d9d9d9;margin-right:14px;}
.bind-form .form-item .lab i{display:block;clear:both;overflow: hidden;background: url(../images/bind-sprite-********.png) no-repeat;}
.bind-form .form-item .lab .account{width:20px;height:13px;margin:13px auto 0;background-position: 100% -242px;}
.bind-form .form-item .lab .password{width:16px;height:18px;margin:11px auto 0;background-position: 100% -283px;}
.bind-form .form-item .lab .captcha{width:15px;height:17px;margin:11px auto 0;background-position: 100% -316px;}
.bind-form .form-item .u-int{height:19px;width:287px;padding:10px 0px;border:0 none;background: none;outline:none;}
.bind-form .form-item .placeholder{position:absolute;left:69px;top:12px;cursor:text; color:#cacaca;}
.bind-form .captcha-item .form-m{width:174px;float: left;}
.bind-form .captcha-item .u-int{width:91px;}
.bind-form .captcha-item img,.bind-form .captcha-item a{float: left;margin-left: 10px;} 
.bind-form .captcha-item a{color:#1989d7;margin:12px 0 0 15px;}
.bind-form .error-tips{padding-left:20px;margin-bottom:20px;color:red;background: url(../images/bind-sprite-********.png) no-repeat -360px -215px;}

.btn-bind, .btn-create{width:374px;height:52px;display:inline-block;line-height: 52px;background: url(../images/bind-sprite-********.png) no-repeat 0 0;text-align: center;}
.btn-create{background-position: 0 -107px;color:#FFF;font:16px/52px 'Microsoft YaHei';}
.btn-bind:hover{background-position: 0 -53px;}
 .btn-create:hover{color:#FFF;background-position: 0 -160px;text-decoration: none;}

/* foot */
.foot{ height:30px; line-height:30px; background:#f0f0f0; text-align:center; color:#e3e3e3; margin-top:50px;}
.foot a, .foot font{ color:#999; margin:0 8px; font-size:12px;}
.foot a:hover{ color:#f30;}