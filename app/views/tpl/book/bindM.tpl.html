<!DOCTYPE html>
<html>
    <head>
        <meta charset="gbk" />
        <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
        <title>2345小说</title>
        <link rel="stylesheet" href="/css/book/book_mobile_v2.css">
        <style type="text/css">
            .error-tips{
                color:red;
            }
        </style>
    </head>
    <body>
        <!--wrapper begin-->
        <div class="wrapper mailbg">
            <header class="header">
                <a href="http://book.2345.com/m/" class="back">返回</a>
                <span class="logo_t"></span>
            </header>
            <!--container begin-->
            <div class="container">
                <!-- detail -->
                <section class="bindMail">
                    <div class="pers_info clearfix">
                        <span class="avatar"><img src="{{$pageArray.oauthUser.figureurl}}" width="80" height="80" alt=""></span>
                        <h3>{{$pageArray.oauthUser.nickname}}</h3>
                        <p>您已成功使用{{$pageArray.oauthName}}帐号登录2345小说大全!</p>
                    </div>
                    <form id="bind-form" action="" method="post">
                        <ul class="mailform">
                            <li class="l-field">
                                <label for="email">建议使用您的QQ邮箱<em>（必填）</em></label>
                                <input type="text" class="text" id="email" name="email" placeholder="格式：QQ号码@qq.com">
                                <p id="errorMsg" class="error-tips"{{if !$pageArray.alert}} style="display:none;"{{/if}}>{{$pageArray.alert}}</p>
                            </li>
                            <li><button type="submit" class="btn_a">确 定</button></li>
                        </ul>
                    </form>
                </section>
                <!-- detail end-->
            </div>
            <!--push place-->
            <div class="push"></div>
        </div>
        <!--wrapper end-->

        <script src="/js/jquery-1.8.3.min.js"></script>
        <script type="text/javascript">
            function checkForm() {
                var ret = true;
                if ($("#email").val() == "") {
                    $("#errorMsg").html("请输入邮箱");
                    $("#errorMsg").show();
                    ret = false;
                } else if (!/[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}/.test($("#email").val())) {
                    $("#errorMsg").html("请输入正确的邮箱");
                    $("#errorMsg").show();
                    ret = false;
                } else {
                    $.ajax('/api/check', {
                        type: "POST",
                        data: 'type=emailUsername&email=' + $("#email").val(),
                        async: false,
                        success: function(response) {
                            if (response == 1 || response == 2)
                            {
                                $("#errorMsg").html("此邮箱已被注册，请换一个");
                                $("#errorMsg").show();
                                ret = false;
                            }
                        }
                    });
                }
                if (ret) {
                    $("#errorMsg").hide();
                    return true;
                } else {
                    return false;
                }
            }
            $(function() {
                $("#bind-form").submit(function() {
                    return checkForm();
                });
            });
        </script>
    </body>
</html>