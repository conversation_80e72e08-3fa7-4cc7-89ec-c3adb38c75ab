<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/24
 * Time: 13:46
 */

namespace Admin;

use Controller;
use Service\Func\ServiceFunc;
use Service\Service;

class SwooleServiceController extends Controller
{
    /**
     * swoole配置首页
     * -
     * @return void
     */
    public function actionIndex()
    {
        $serviceList = [];
        $ipList = ServiceFunc::regServiceList();
        foreach ($ipList as $key => $info)
        {
            $serviceList[$key]['ipInfo'] = $info;
            list($ip, $port) = explode(':', $info);
            $serviceStatus = ServiceFunc::hasRegServiceIp($ip, $port);
            $serviceList[$key]['status'] = $serviceStatus;
            switch ($serviceStatus)
            {
                case -1:
                    $serviceList[$key]['statusName'] = ' 等待启动';
                    break;
                case 1:
                    $serviceList[$key]['statusName'] = ' 运行中';
                    break;
                case 0:
                    $serviceList[$key]['statusName'] = '';
                    break;
                default:
                    $serviceList[$key]['statusName'] = ' 未知状态';
            }
        }
        $pageArray['serviceList'] = $serviceList;
        $pageArray['pageList'] = 'list';
        loadView('Admin/swoole_service_index.html', $pageArray);
    }

    /**
     * 添加服务ip
     * -
     * @return string
     */
    public function actionAddServiceIp()
    {
        $ip = trim($this->Filter->getPost('ip'));
        $port = (int)trim($this->Filter->getPost('port'));
        if (empty($ip) || empty($port))
        {
            return \MsgAction::returnJsonData(array('status' => '-1', 'msg' => 'ip或者端口号不能为空'));
        }
        if (!filter_var($ip, FILTER_VALIDATE_IP))
        {
            return \MsgAction::returnJsonData(array('status' => '-2', 'msg' => 'ip格式错误'));
        }
        if (!empty(ServiceFunc::hasRegServiceIp($ip, $port)))
        {
            return \MsgAction::returnJsonData(array('status' => '-3', 'msg' => '添加的ip和端口已存在'));
        }
        else
        {
            $regStatus = ServiceFunc::regServiceIp($ip, $port, 0);
            if ($regStatus !== false)
            {
                return \MsgAction::returnJsonData(array('status' => '1', 'msg' => '添加成功'));
            }
            else
            {
                return \MsgAction::returnJsonData(array('status' => '-4', 'msg' => '添加失败'));
            }
        }
    }

    /**
     * 设置服务器状态
     * -
     * @return string
     */
    public function actionSetServiceStatus()
    {
        $ip = trim($this->Filter->getPost('ip'));
        list($ip, $port) = explode(':', $ip);
        $status = trim($this->Filter->getPost('status'));
        if (empty($ip) || empty($port))
        {
            return \MsgAction::returnJsonData(array('status' => '-1', 'msg' => 'ip或者端口号不能为空'));
        }
        if (!filter_var($ip, FILTER_VALIDATE_IP))
        {
            return \MsgAction::returnJsonData(array('status' => '-2', 'msg' => 'ip格式错误'));
        }
        $service = new Service();
        switch ($status)
        {
            case 'start':
                $regStatus = ServiceFunc::regServiceIp($ip, $port, -1);
                if ($regStatus !== false)
                {
                    return \MsgAction::returnJsonData(array('status' => '1', 'msg' => '开启成功'));
                }
                else
                {
                    return \MsgAction::returnJsonData(array('status' => '-5', 'msg' => '开启失败'));
                }
                break;
            case 'shutdown':
                $response = $service->shutdown($ip, $port);
                $response = json_decode($response, true);
                if ($response['status'] == 1)
                {
                    return \MsgAction::returnJsonData(array('status' => '1', 'msg' => '成功关闭服务'));
                }
                else
                {
                    return \MsgAction::returnJsonData(array('status' => '-4', 'msg' => '关闭服务失败'));
                }
                break;
            case 'reload':
                $response = $service->reload($ip, $port);
                $response = json_decode($response, true);
                if ($response['status'] == 1)
                {
                    return \MsgAction::returnJsonData(array('status' => '1', 'msg' => 'reload服务成功'));
                }
                else
                {
                    return \MsgAction::returnJsonData(array('status' => '-6', 'msg' => 'reload服务失败'));
                }
                break;
            case 'remove':
                $serviceStatus = ServiceFunc::hasRegServiceIp($ip, $port);
                //只有是未开启状态才能移除
                if ($serviceStatus === (float)0 || $serviceStatus === 0)
                {
                    $removeStatus = ServiceFunc::removeRegServiceIp($ip, $port);
                }
                else
                {
                    $removeStatus = false;
                }

                if ($removeStatus !== false)
                {
                    return \MsgAction::returnJsonData(array('status' => '1', 'msg' => '移除成功'));
                }
                else
                {
                    return \MsgAction::returnJsonData(array('status' => '-5', 'msg' => '只有未开启状态才能移除'));
                }
                break;
            case 'exception':
                $status = $service->status($ip, $port);
                if (!empty($status['status']) && $status['status'] == 1)
                {
                    return \MsgAction::returnJsonData(array('status' => '-7', 'msg' => '服务器正常状态不能移除'));
                }
                $regStatus = ServiceFunc::regServiceIp($ip, $port, 0);
                if ($regStatus !== false)
                {
                    return \MsgAction::returnJsonData(array('status' => '1', 'msg' => '移除成功'));
                }
                else
                {
                    return \MsgAction::returnJsonData(array('status' => '-8', 'msg' => '移除失败'));
                }
                break;
            default:
                break;
        }
    }

    /**
     * 设置服务器
     * -
     * @return void
     */
    public function actionServiceDetail()
    {
        $ip = trim($this->Filter->get('ip'));
        list($ip, $port) = explode(':', $ip);
        if (empty($ip) || empty($port))
        {
            return \MsgAction::returnJsonData(array('status' => '-1', 'msg' => 'ip或者端口号不能为空'));
        }
        if (!filter_var($ip, FILTER_VALIDATE_IP))
        {
            return \MsgAction::returnJsonData(array('status' => '-2', 'msg' => 'ip格式错误'));
        }

        $service = new Service();

        //连接总数
        $count = $service->getConnectionCount($ip, $port);
        $count = json_decode($count, true);
        $pageArray['count'] = $count['data']['count'];

        //进程列表
        $servicePs = $service->getSmsServicePs($ip, $port);
        $servicePs = json_decode($servicePs, true);
        $pageArray['servicePs'] = str_replace("\n", '<br />', $servicePs['data']['msg']);

        //僵尸进程
        $zombiePs = $service->getZombiePs($ip, $port);
        $pageArray['zombiePs'] = str_replace("\n", '<br />', $zombiePs['data']['msg']['output']);

        $pageArray['pageList'] = 'info';
        loadView('Admin/swoole_service_index.html', $pageArray);
    }

    /**
     * User: panj
     * 设置走swoole项目的pid
     * @return null
     */
    public function actionSetProject()
    {
        $pageArray = [];
        loadView('Admin/swoole_set_project.tpl.html', $pageArray);
    }

    /**
     * User: panj
     * 获取swoole 数据存储，项目信息
     * @return null
     */
    public function actionProjectList()
    {
        $data = [];
        $list = ServiceFunc::useSwooleProjectList();
        if (!empty($list))
        {
            $smsProjectModel = SmsProjectModel::getInstance();
            foreach ($list as $pid)
            {
                //dirty here
                $res = $smsProjectModel->getPojectList(['pid' => $pid, 'row' => 1]);
                $res && $data[] = $res;
            }
        }
        $ret['list'] = $data;
        $ret['storage'] = ServiceFunc::getSmsServiceStorageType();
        return \MsgAction::returnJsonData(array('status' => '0', 'msg' => '', 'data' => $ret));
    }

    /**
     * User: panj
     * 删除走swoole的项目pid
     * @return null
     */
    public function actionDeletePid()
    {
        $pid = (int)$this->Filter->post('pid');
        $res = ServiceFunc::delUseSwooleProject($pid);
        $msg = $res ? '成功' : '删除失败';

        return \MsgAction::returnJsonData(array('status' => '0', 'msg' => $msg));
    }

    /**
     * User: panj
     * 增加走swoole的项目pid
     * @return null
     */
    public function actionAddPid()
    {
        $pid = (int)$this->Filter->post('pid');
        $res = ServiceFunc::addUseSwooleProject($pid);
        $msg = $res ? '成功' : '增加失败';

        return \MsgAction::returnJsonData(array('status' => '0', 'msg' => $msg));
    }

    /**
     * 设置数据存储方式
     * User: panj
     * @return null
     */
    public function actionSetStorage()
    {
        $storageType = trim($this->Filter->post('storageType'));
        $res = ServiceFunc::setSmsServiceStorageType($storageType);
        $msg = $res ? '成功' : '增加失败';

        return \MsgAction::returnJsonData(array('status' => '0', 'msg' => $msg));
    }

    /**
     * User: panj
     * 更改合局swoole使用状态
     * @return null
     */
    public function actionChangeSwooleGlobalStatus()
    {
        $status = (int)($this->Filter->post('status'));

        $status = $status > 0 ? 1 : 0;
        $res = ServiceFunc::setSwooleGlobal($status);
        $msg = $res ? '成功' : '失败';

        return \MsgAction::returnJsonData(array('status' => '0', 'msg' => $msg));
    }

    /**
     * User: panj
     * 获取是否使用swoole的状态
     * @return null
     */
    public function actionGetSwooleGlobalStatus()
    {
        $useSwoole = ServiceFunc::useSwooleGlobal();
        $useSwoole = $useSwoole ? 1 : 0;
        return \MsgAction::returnJsonData(array('status' => '0', 'data' => ['useSwoole' => $useSwoole]));
    }
}
