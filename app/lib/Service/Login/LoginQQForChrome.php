<?php

namespace Service\Login;

use Config;
use RedisEx;
use Service\AccountStat\Consts\AccountStat as AccountStatConst;
use Service\AccountStat\AccountStat as AccountStat;
use Service\Avatar\Avatar;

class LoginQQForChrome extends LoginQQService
{
    /**
     * callback
     * -
     * @return void
     * <AUTHOR>
     */
    public function callback()
    {
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis)
        {
            $redis = RedisEx::getInstance();
        }
        setcookie('oauth_state', null, time() - 3600, '/');
        setcookie('redirect_uri', null, time() - 3600, '/');
        setcookie('oauth_forward', null, time() - 3600, '/');

        if (empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state'])
        {
            redirect('/');
        }
        $oauthAction = loadAction('Oauth');
        $result = $oauthAction->qqCallback($_REQUEST['code'], $_COOKIE["redirect_uri"]);
        if ($result)
        {
            $openid = $result['openid'];
            $nickname = $result['nickname'];
            $figureurl = $result['figureurl'];
            $gender = $result['gender'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'qq');
            $source = AccountStatConst::SRC_IE_CHROME;
            if ($return["passid"] > 0)
            {
                $passid = $return["passid"];
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $result = $memberModel->read($passid);
                if ($result['gender'] == 0 && $gender != 0)
                {
                    $memberModel->edit($passid, array('gender' => $gender));
                }
                $result["token"] = $memberModel->getToken($passid);
                $username = $result['username'];
                $nickname = $username;
                $bind = unserialize($result["m_uid"]);
                $uid = $bind['1'];
                if (strpos($username, "#qq#") !== false)
                {
                    $usernameOld = $username;
                    $username = $memberModel->repairOldOAuthUser($passid, 'qq');
                    if ($username)
                    {
                        $nickname = $username;
                        $memberModel->patchUserLog($passid, array('username_old' => $usernameOld, 'username' => $username, 'op_time' => time(), 'op_ip' => get_client_ip(), 'op_domain' => 'login.2345.com'));
                        noticeToChange($passid, 'changeUsername', array(
                            'passid' => $passid,
                            'uid' => $uid,
                            'type' => 'username',
                            'value' => $username
                        ));
                    }
                    else
                    {
                        $username = $usernameOld;
                        $nickname = explode("#qq#", $username);
                        $nickname = $nickname[0];
                    }
                }
            }
            else
            {
                $nickname = str_replace(array("#qq#", "#weibo#"), "", $nickname);
                $nickname = trim($nickname);
                if (!$nickname)
                {
                    $nickname = "qzuser_" . substr(md5(microtime()), 0, 6);
                }
                $clientIp = get_client_ip();
                $result = $memberModel->regOAuth('qq', $openid, $nickname, $clientIp, '', $gender);
                if (!$result)
                {
                    AccountStat::collect(
                        AccountStatConst::TP_REG,
                        AccountStatConst::CTP_WEB,
                        AccountStatConst::AC_TP_OAUTH_QQ,
                        0,
                        $source
                    );

                    closeWindow();
                }
                $passid = $result["passid"];
                $uid = $result["uid"];
                $username = $result['username'];
                $nickname = $username;
                if ($isUseRedis)
                {
                    $redis->incr("regOAuthSuccNum");
                    $redis->incr('RSN:qq_chrome_login_2345_com');
                }
            }

            if ($return["passid"] > 0)
            {
                $type = AccountStatConst::TP_LOGIN;
            }
            else
            {
                $type = AccountStatConst::TP_REG;
            }
            AccountStat::collect($type, AccountStatConst::CTP_WEB, AccountStatConst::AC_TP_OAUTH_QQ, 1, $source);

            $sec = get_sec_browser($uid);
            $token = $result["token"];
            if ($isUseRedis)
            {
                $redis->incr('LSN:qq_chrome_login_2345_com');
            }
            echo '<script type="text/javascript">chrome.sync.onLogin("qq","' . $username . '","' . $nickname . '",0,"","' . $uid . '","' . $sec . '","' . $passid . '","' . $token . '");chrome.sync.checkIsFirstLogin();chrome.sync.setIsAutoLogin(true);</script>';
            if ($_COOKIE['oauth_forward'])
            {
                echo '<script type="text/javascript">window.location = "' . str_replace("\"", "'", $_COOKIE['oauth_forward']) . '";</script>';
            }
            else
            {
                closeWindow();
            }
        }
        else
        {
            if ($isUseRedis)
            {
                $redis->incr('LFN:qq_chrome_login_2345_com');
            }

            $source = AccountStatConst::SRC_IE_CHROME;
            AccountStat::collect(AccountStatConst::TP_LOGIN, AccountStatConst::CTP_WEB, AccountStatConst::AC_TP_OAUTH_QQ, 0, $source);

            closeWindow();
        }
    }
}
