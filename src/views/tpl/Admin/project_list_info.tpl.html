<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务平台-项目列表</title>
  <link rel="stylesheet" href="/style/css/global.css">
   <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a>  >  <span>项目列表</span></div>
    <!--面包屑导航 结束-->
    
    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="javascript:">项目列表</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    
    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
	   <form action="" method="POST" id="submitFormId">
            <div class="dataWrap clearfix">
        	<em>日期：</em>
            <div class="dataList w130">
            	<div class="getTime"><input value="{{$pageArray.startTime}}" type="text" autocomplete="off" name="startTime" onclick="WdatePicker()" /><i>日历icon</i></div>
            </div>
        </div>
        <span class="dataTxt">至</span>
        <div class="dataWrap clearfix">
            <div class="dataList w130">
            	<div class="getTime"><input value="{{$pageArray.endTime}}" type="text" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
            </div>
        </div>
    	
        
        <div class="dataWrap ml10 clearfix">
       	    <em>项目：</em>
            <select class="selectMod ml10 w200" name="projectId">
                <option value="0">-请选择-</option>
                {{foreach from=$pageArray.projectList item=projectInfo}}
              	     <option value="{{$projectInfo.pid}}" {{if $projectInfo.pid eq $pageArray.projectId}} selected="selected"{{/if}}>{{$projectInfo.project_name}}</option>
                {{/foreach}}
            </select>
        </div>
           <div class="dataWrap clearfix ml20">
               <input type="checkbox" name="useCache" id="useCache" value="1" {{if $pageArray.useCache}}checked{{/if}}/>
               <!--本方法用于处理浏览器自带serialize方法处理radio和checkbox时的不选中无提交现象-->
               <input type="hidden" value="1" name="fromClick"/>
               <label for="useCache">使用缓存</label>
           </div>
            
        <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 querySubmitForm">查询</a> 
        
        <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 exportProjectData">导出</a> 
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    
    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th style="width:70px">日期</th>
                        <th style="width:25px">ID</th>
                        <th style="width:70px">名称</th>
                        {{foreach from=$pageArray.postionList key=postionKey item=postionName}}
                            <th>{{$postionName}}</th>
                        {{/foreach}}
                        <th style="width:50px">统计</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th style="width:70px">日期</th>
                <th style="width:25px">ID</th>
                <th style="width:70px">名称</th>
                {{foreach from=$pageArray.postionList key=postionKey item=postionName}}
                    <th>{{$postionName}}</th>
                {{/foreach}}
                <th style="width:50px">统计</th>
            </tr>
            
            {{foreach from=$pageArray.everyListTotal key=everyListKey item=everyListInfo}}
                <tr>
                    <td style="width:70px" {{if $everyListInfo.isCache == 1}}class="lightYellowBg"{{/if}}>{{$everyListKey}}</td>
                    <td style="width:40px">{{$everyListInfo.pid}}</td>
                    <td style="width:70px">{{$everyListInfo.project_name}}</td>
                    {{foreach from=$pageArray.postionList key=postionKey item=postionName}}
                        <td>{{if empty($everyListInfo.postionList[$postionKey])}}0  {{else}} {{$everyListInfo.postionList[$postionKey].total}} {{/if}}</td>
                    {{/foreach}}
                    <td style="width:50px">{{if empty($everyListInfo.useSmsTotal)}}0{{else}}{{$everyListInfo.useSmsTotal}}{{/if}}</td>
                </tr>
            {{/foreach}}
            {{if !empty($pageArray.postionList)}}
            <tr>
                <td></td>
                <td></td>
                <td></td>
                {{foreach from=$pageArray.postionList key=postionKey item=postionName}}
                    <td>{{$pageArray.typeTotalList[$postionKey]}}</td>
                {{/foreach}}
                <td style="width:50px">{{$pageArray.allTotal}}</td>
            </tr>
            {{/if}}
        </table>
        <!--table数据列表 结束-->
        
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->
<script src="/style/js/jquery-1.8.3.min.js"></script>
<script src="/style/js/tableFixed.js"></script>
<script src="/style/js/boxLocation.js"></script>
<script>
$('.querySubmitForm').click(function (){
   $('#submitFormId').submit();  
})

$('.exportProjectData').click(function (){
    var link = '/Admin/SmsProject/ProjectListInfo?act=export&' + $('#submitFormId').serialize();
    window.open(link);
})
</script>
</body>
</html>