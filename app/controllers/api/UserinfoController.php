<?php


class UserinfoController extends Controller
{

    /**
     * UserinfoController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight()) {
            die('401');
        }
    }

    /**
     * 服务器获取用户信息接口
     *
     * @return void
     */
    public function actionIndex()
    {
        $passid = intval($_POST['uid']);
        $memberModel = loadModel('member');
        $info = $memberModel->read($passid);
        echo serialize($info);
    }


    /**
     * 服务器详细接口
     * -
     * @return int
     */
    public function actionBatchDetail()
    {
        $tableField = [
            'm.id',
            'm.username',
            'm.pwd_strength',
            'm.uid',
            'm.gid',
            'm.reg_ip',
            'm.reg_time',
            'm.login_ip',
            'm.login_time',
            'm.locked',
            'mb.passid',
            'mb.openid',
            'mb.nickname',
            'mb.type',
            'mb.username',
            'mb.bindtime',
            'mb.unbindcode',
            'mp.passid',
            'mp.phone',
            'mp.last_update',
            'mi.uid',
            'mi.name',
            'mi.gender',
            'mi.bday',
            'mi.msn',
            'mi.qq',
            'mi.area',
            'mi.address',
            'mi.zip',
            'mi.city',
            'mi.phone',
            'mi.tel1',
            'mi.tel2',
            'mi.tel3',
            'mi.email',
            'mi.email_status',
            'mn.id',
            'mn.passid',
            'mn.nickname',
            'mn.addtime',
            'mn.ip',
        ];
        $passidStr = \Common\Utils\Url::getStringParam("passids", "", "post");
        $params = \Common\Utils\Url::getStringParam("params", "", "get");
        $params = preg_replace("/[^a-zA-Z0-9_,.]/", "", $params);
        $params = explode(",", $params);
        $params = array_filter($params);
        $passidStr = preg_replace("/[^0-9,]/", "", $passidStr);
        $passids = explode(",", $passidStr);
        $passids = array_filter($passids);
        if (!$passids) {
            $this->codeResponse(-2, "", "请检查passid");
            exit;
        }
        if (($passidCount = count($passids)) > 1000) {
            $this->codeResponse(-2, "", "限制passid数为1000");
            exit;
        }
        $tables = [];
        foreach ($params as $key => $val) {
            $val = trim($val);
            if (!in_array($val, $tableField)) {
                unset($params[$key]);
                continue;
            }
            $tables[] = substr($val, 0, stripos($val, '.'));
        }
        if (!$params) {
            $this->codeResponse(-1, "", "请指定有效参数");
            exit;
        }
        // 记录每日passid个数; 1个月1个key，过期时间30天；
        $redis = RedisEx::getInstance();
        $key = "INFO_PASSID_COUNT:" . date("Ym");
        $hashKey = date("Ymd");
        $redis->hIncrBy($key, $hashKey, $passidCount);
        $redis->expire($key, 2592000);//30 days

        $openidList = [];
        $phoneList = [];
        $tables = array_unique($tables);
        // 获取用户基本信息
        $memberModel = loadModel('member');
        $info = $memberModel->getNewMemberByPassIdList($passids);
        foreach ($info as $key => $item) {
            $openidTmp = json_decode($item['openid'], true);
            if ($openidTmp) {
                // 这里保持改造前的逻辑只取一条，但是应该是有问题的
                $openidList[] = current($openidTmp);
            }
            if ($item['phone']) {
                $phoneList[] = $item['phone'];
            }
            $itemTmp = [];
            foreach ($item as $keyTmp => $val) {
                $itemTmp['m.' . $keyTmp] = $val;
            }
            $info[$item['id']] = $itemTmp;
            unset($info[$key]);
        }
        // 获取用户扩展信息
        if (in_array('mi', $tables)) {
            $memberInfo = $memberModel->getNewMemberInfoByPassIdList($passids);
            foreach ($memberInfo as $item) {
                if (isset($info[$item['passid']])) {
                    $itemTmp = [];
                    foreach ($item as $keyTmp => $val) {
                        $itemTmp['mi.' . $keyTmp] = $val;
                    }
                    $info[$item['passid']] = array_merge($info[$item['passid']], $itemTmp);
                }
            }
            unset($memberInfo);
        }
        // 获取绑定信息
        if (in_array('mb', $tables)) {
            $bindInfo = $memberModel->getNewMemberBindByPassIdList($openidList);
            foreach ($bindInfo as $item) {
                if (isset($info[$item['passid']])) {
                    $itemTmp = [];
                    foreach ($item as $keyTmp => $val) {
                        $itemTmp['mb.' . $keyTmp] = $val;
                    }
                    $info[$item['passid']] = array_merge($info[$item['passid']], $itemTmp);
                }
            }
            unset($bindInfo);
        }
        // 获取手机信息
        if (in_array('mp', $tables)) {
            $phoneInfo = $memberModel->getNewMemberPhoneByPassIdList($phoneList);
            foreach ($phoneInfo as $item) {
                if (isset($info[$item['passid']])) {
                    $itemTmp = [];
                    foreach ($item as $keyTmp => $val) {
                        $itemTmp['mp.' . $keyTmp] = $val;
                    }
                    $info[$item['passid']] = array_merge($info[$item['passid']], $itemTmp);
                }
            }
            unset($phoneInfo);
        }
        // 获取昵称信息
        if (in_array('mn', $tables)) {
            $nicknameInfo = $memberModel->getNicknameByPassIdList($passids);
            foreach ($nicknameInfo as $item) {
                if (isset($info[$item['passid']])) {
                    $itemTmp = [];
                    foreach ($item as $keyTmp => $val) {
                        $itemTmp['mn.' . $keyTmp] = $val;
                    }
                    $info[$item['passid']] = array_merge($info[$item['passid']], $itemTmp);
                }
            }
            unset($nicknameInfo);
        }
        // 字段过滤
        $return = [];
        foreach ($info as $key => $item) {
            $tmp = [];
            foreach ($params as $param) {
                if (isset($item[$param])) {
                    $tmp[$param] = $item[$param];
                } else {
                    $tmp[$param] = "";
                }
            }
            if (in_array('mi.uid', $params) && isset($item['mi.passid'])) {
                $tmp['mi.uid'] = $item['mi.passid'];
            }
            if (in_array('m.username', $params) && isset($item['mi.passid'])) {
                $tmp["m.username"] = iconv('GBK', 'UTF-8', $tmp["m.username"]);
            }
            if (in_array('mb.nickname', $params) && isset($item['mi.passid'])) {
                $tmp["mb.nickname"] = iconv('GBK', 'UTF-8', $tmp["mb.nickname"]);
            }
            $return[$key] = $tmp;
        }
        $this->codeResponse(1, $return);
        exit;
    }


    /**
     * 服务器获取用户绑定信息， 手机号，邮箱，qq，微信
     *
     * @return int
     */
    public function actionBindInfo()
    {
        $passid = intval($_POST['passid']);
        $memberModel = loadModel('member');
        /** @see  MemberModel::bindInfo */
        $info = $memberModel->bindInfo($passid);
        echo serialize($info);

        return 0;
    }

    /**
     * 检测异常登录信息
     *
     * <AUTHOR>
     * @return void;
     */
    public function actionLogging()
    {
        $passid = intval($_POST['passid']);
        $redis = RedisEx::getInstance();
        $loginLogs = $redis->zRevRangeByScore("LSSP:" . $passid, time(), 0, array('withscores' => true));
        $commonArea = $return = array();
        $count = 0;
        foreach ($loginLogs as $log => $logtime) {
            $day = date("Y-m-d", $logtime);
            $second = date("H:i:s", $logtime);
            list($ip, $from, $browser, $system) = explode("|", $log);
            $cityId = getCityId($ip);
            $area = getAreaNameByIpLib($ip);
            $ipExp = explode(".", $ip);
            $hideIp = implode(".", array($ipExp[0], $ipExp[1], $ipExp[2], "*"));
            $return[$count]['day'] = $day;
            $return[$count]['second'] = $second;
            $return[$count]['ip'] = $hideIp;
            $return[$count]['browser'] = mb_convert_encoding($browser, "utf8", "gbk");
            $return[$count]['product'] = mb_convert_encoding($this->detectProduct($from), 'utf8', 'gbk');
            $return[$count]['area'] = mb_convert_encoding($area, 'utf8', 'gbk');
            $commonArea[$area] = is_null($commonArea[$area]) ? 0 : $commonArea[$area];
            $commonArea[$area]++;
            $count++;
        }

        if (count($commonArea) > 1) {
            $minvalue = min($commonArea);
            $minArea = array_search($minvalue, $commonArea);
            foreach ($return as $key => &$val) {
                if ($minArea == mb_convert_encoding($val['area'], 'gbk', 'utf8')) {
                    $val['uncommon'] = true;
                }
            }
        }

        die(json_encode($return));
    }

    /**
     * @param string $domain domain
     *
     * @return string
     * <AUTHOR>
     */
    private function detectProduct($domain)
    {
        switch ($domain) {
            case "buy.2345.com":
                $product = "购物";
                break;
            case "wan.2345.com":
                $product = "游戏";
                break;
            case "book.2345.com":
                $product = "小说";
                break;
            case "caipiao.2345.com":
                $product = "彩票";
                break;
            case "cps.2345.com":
                $product = "效果联盟平台";
                break;
            case "chrome.login.2345.com":
                $product = "加速浏览器";
                break;
            case "game.2345.com":
                $product = "游戏";
                break;
            case "ie.2345.com":
                $product = "浏览器";
                break;
            case "jifen.2345.com":
            case "jifen.yl234.com":
                $product = "技术员联盟";
                break;
            case "login.2345.com":
                $product = "用户中心";
                break;
            case "m.book.2345.com":
                $product = "小说";
                break;
            case "v.2345.com":
                $product = "2345影视";
                break;
            case "kan.2345.com":
                $product = "2345影视kan";
                break;
            case "dianying.2345.com":
                $product = "2345电影";
                break;
            case "tv.2345.com":
                $product = "2345电视剧";
                break;
            case "dongman.2345.com":
                $product = "2345动漫";
                break;
            case "m.jifen.2345.com":
                $product = "技术员联盟";
                break;
            case "m.shouji.2345.com":
                $product = "手机联盟";
                break;
            case "mobile.login.2345.com":
                $product = "用户中心";
                break;
            case "zhushou.2345.com":
                $product = "手机助手";
                break;
            default:
                $product = "用户中心";
                break;
        }

        return $product;
    }
}
