<?php

namespace Admin;

use Controller;

class SmsQueryLogController extends Controller
{
    /**
     * @return void
     */
    public function actionIndex()
    {
        loadView('Admin/sms_query_log_index.tpl.html', [], false, ['[|', '|]']);
    }

    /**
     * @return void
     */
    public function actionApi()
    {
        $func = $_GET['func'] ?? '';
        if (method_exists($this, $func))
        {
            echo $this->$func();
        }
        else
        {
            echo json_encode(['err' => -999, 'msg' => "Method '{$func}' not exists"]);
        }
    }

    /**
     * @return string
     */
    private function data()
    {
        $form = is_array($_POST['data']) ? $_POST['data'] : [];
        $page = is_array($_POST['page']) ? $_POST['page'] : ['current' => 1, 'size' => 100, 'show' => false];
        $returnData = [];

        if (!is_array($form['dateRange']) || count($form['dateRange']) === 0)
        {
            $form['dateRange'] = [date('Y-m-d 00:00:00'), date('Y-m-d 23:59:59')];
        }
        if (strtotime($form['dateRange'][0]) >= strtotime($form['dateRange'][1]))
        {
            return json_encode(['err' => -1, 'msg' => 'date invaild']);
        }
        if (date('Ym', strtotime($form['dateRange'][0])) !== date('Ym', strtotime($form['dateRange'][1])))
        {
            return json_encode(['err' => -1, 'msg' => \EncodingAction::convertEncoding('不允许跨月查询', 'utf-8')]);
        }
        $form['encrypt_phone'] = \SecurityAction::encrypt($form['phone']);
        $tagListModel = TagListModel::getInstance();
        $smsQueryLogModel = SmsQueryLogModel::getInstance();
        $logList = $smsQueryLogModel->getSmsLog($form, ['page' => $page['current'], 'limit' => $page['size']]);
        if ($page['show'] === true || $page['show'] === 'true')
        {
            $total = $smsQueryLogModel->getSmsLog(array_merge($form, ['count' => 1]))['total'];
        }
        else
        {
            $total = 0;
        }

        foreach ($logList as $log)
        {
            $logData = [];
            $logData['logId'] = $log['log_id'];

            //手机号，短信验证码脱敏处理
            if (!UserAction::isAdmin())
            {
                $verifyCode = \SecurityAction::getVerifyCodeFromText($log['text']);
                if ($verifyCode !== false)
                {
                    $securityText = \SecurityAction::desensitize($verifyCode, \SecurityAction::INFO_TYPE_VERIFY_CODE);
                    $log['text'] = str_replace($verifyCode, $securityText, $log['text']);
                }
            }

            $logData['phone'] = strpos($log['phone'], '*') === false ? \SecurityAction::desensitize($log['phone']) : $log['phone'];
            $logData['text'] = \EncodingAction::transcoding($log['text'], 'UTF-8');
            $logData['smsCount'] = $log['sms_count'];
            $logData['account'] = $log['account'] ?? "null account";
            $logData['operator'] = $log['channel'];
            $logData['type'] = $log['type'];
            $logData['project'] = $log['pid'];
            $logData['position'] = $log['business_id'];
            $logData['serverIp'] = $log['server_ip'];
            $logData['clientIp'] = $log['client_ip'];
            $logData['sendTime'] = $log['send_time'];
            $logData['sendStatus'] = $log['send_status'];
            $logData['sendResponse'] = \EncodingAction::transcoding($log['send_response'], 'UTF-8');
            $logData['submitTime'] = $log['code_time'] ?? '0000-00-00 00:00:00';
            $logData['submitStatus'] = $log['code_status'];
            $logData['submitResponse'] = \EncodingAction::transcoding($log['code_desc'], 'UTF-8', 'GBK');
            $logData['callbackTime'] = $log['callback_time'];
            $logData['callbackStatus'] = $log['callback_status'];
            $logData['callbackResponse'] = \EncodingAction::transcoding($log['callback_message'], 'UTF-8');
            if ($log['pid'] === '14')
            {
                $logTag = $tagListModel->getTagInfoByLogIds([$log['log_id']]);
                if (isset($logTag[$log['log_id']]["mid"]))
                {
                    $logData["mid"] = $logTag[$log['log_id']]["mid"];
                }
            }
            $returnData[] = $logData;
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($returnData), 'total' => (int)$total]);
    }

    /**
     * User: panj
     * 加密手机解密
     * @return string
     */
    private function decrypt()
    {
        $form = is_array($_POST['data']) ? $_POST['data'] : [];
        $logId = intval($_POST['log_id']) ?? 0;
        if ($logId <= 0)
        {
            return json_encode(['err' => - 1, 'msg' => 'date invaild']);
        }
        if (!is_array($form['dateRange']) || count($form['dateRange']) === 0)
        {
            $form['dateRange'] = [date('Y-m-d 00:00:00'), date('Y-m-d 23:59:59')];
        }
        if (strtotime($form['dateRange'][0]) >= strtotime($form['dateRange'][1]))
        {
            return json_encode(['err' => -1, 'msg' => 'date invaild']);
        }
        if (date('Ym', strtotime($form['dateRange'][0])) !== date('Ym', strtotime($form['dateRange'][1])))
        {
            return json_encode(['err' => -1, 'msg' => \EncodingAction::convertEncoding('不允许跨月查询', 'utf-8')]);
        }
        $data = [
            'logId' => $logId,
            'dateRange' => $form['dateRange'],
        ];
        $smsQueryLogModel = SmsQueryLogModel::getInstance();
        $smsInfo = $smsQueryLogModel->getByLogId($data);
        if (!empty($smsInfo))
        {
            if (strpos($smsInfo['phone'], '*') === false)
            {
                // phone 是明文
                $phone = $smsInfo['phone'];
            }
            else
            {
                // phone 是密文
                $phone = \SecurityAction::decrypt($smsInfo['encrypt_phone']);
                $phone = !empty($phone) ? $phone : $smsInfo['phone'];
            }
            return json_encode(['err' => 0, 'msg' => 'ok', 'phone' => $phone]);
        }
        return json_encode(['err' => -1, 'msg' => \EncodingAction::convertEncoding('记录未找到', 'utf-8')]);
    }

    /**
     * @return string
     */
    private function operatorList()
    {
        $operatorConfigModel = OperatorConfigModel::getInstance();
        $operatorList = $operatorConfigModel->getOperatorList();
        $operators = [];
        foreach ($operatorList as $key => $value)
        {
            if ($value['status'] == 1)
            {
                $operators[] = [
                    'value' => (int)$value['id'],
                    'label' => $value['id'] . '-' . \EncodingAction::transcoding($value["provider_name"], 'utf-8'),
                ];
            }
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($operators)]);
    }

    /**
     * @return string
     */
    private function projectList()
    {
        //$projectModel = \Admin\ProjectModel::getInstance();
        //$projectList = $projectModel->getProjectList('', []);
        $userAuthModel = UserAuthModel::getInstance();
        $uid = UserAction::getAdminUid();
        $projectList = $userAuthModel->getUserProjectList($uid);
        $projects = [];
        foreach ($projectList as $projectInfo)
        {
            if ($projectInfo['status'] == 1)
            {
                $projects[] = [
                    'value' => (int)$projectInfo['pid'],
                    'label' => $projectInfo['pid'] . '-' . \EncodingAction::transcoding($projectInfo["project_name"], 'utf-8'),
                ];
            }
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($projects)]);
    }

    /**
     * @return string
     */
    private function typeList()
    {
        $smsTypes = [];
        $smsTypeModel = \Admin\SmsTypeModel::getInstance();
        $smsTypeList = $smsTypeModel->getSmsTypeList();
        foreach ($smsTypeList as $smsTypeKey => $smsTypeInfo)
        {
            $smsTypes[] = [
                'value' => (int)$smsTypeInfo['sms_type'],
                'label' => $smsTypeInfo['sms_type'] . '-' . \EncodingAction::transcoding($smsTypeInfo["sms_name"], 'utf-8'),
            ];
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($smsTypes)]);
    }

    /**
     * @return string
     */
    private function positionList()
    {
        $position = [];
        $smsProjectModel = SmsProjectModel::getInstance();
        if (!empty($_POST['project']))
        {
            $projectInfo = $smsProjectModel->getPojectList(['pid' => $_POST['project'], 'row' => true]);
            if (empty($projectInfo))
            {
                return json_encode(['err' => -1, 'msg' => 'no such project']);
            }
            $positionList = $smsProjectModel->getPojectPositionList(['pid' => $_POST['project']]);
        }
        else
        {
            $positionList = $smsProjectModel->getPojectPositionList([]);
        }
        foreach ($positionList as $k => $v)
        {
            $position[] = [
                'value' => (int)$v['pl_id'],
                'label' => $v['pl_id'] . '-' . \EncodingAction::transcoding($v["position_name"], 'utf-8'),
            ];
        }
        return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($position)]);
    }

    /**
     * redis查询
     * @return string
     */
    public function queryRedis()
    {
        $form = is_array($_POST['data']) ? $_POST['data'] : [];
        $page = is_array($_POST['page']) ? $_POST['page'] : ['current' => 1, 'size' => 100, 'show' => false];
        $returnData = [];

        if (!is_array($form['dateRange']) || count($form['dateRange']) === 0)
        {
            $form['dateRange'] = [date('Y-m-d H:00:00'), date('Y-m-d H:59:59')];
        }
        if (strtotime($form['dateRange'][0]) >= strtotime($form['dateRange'][1]))
        {
            return json_encode(['err' => -1, 'msg' => 'date invaild']);
        }
        if (date('Ym', strtotime($form['dateRange'][0])) !== date('Ym', strtotime($form['dateRange'][1])))
        {
            return json_encode(['err' => -1, 'msg' => \EncodingAction::convertEncoding('不允许跨月查询', 'utf-8')]);
        }

        $redis = \RedisAction::connect();
        if ($page['show'] === true || $page['show'] === 'true')
        {
            $total = $redis->zCount(\Service\Lib\Action\KeyProvider::getSmsServiceLogIdSetKey(), 0, time());
        }
        else
        {
            $total = 0;
        }

        $search['phone'] = $form['phone'];
        $search['send_status'] = $form['submitStatus'];
        $search['pid'] = $form['project'];
        $search['type'] = $form['type'];
        $search['channel'] = $form['operator'];
        $search['business_id'] = $form['position'];
        $search['callback_status'] = $form['callbackStatus'];
        foreach ($search as $key => $info)
        {
            if (empty($info))
            {
                unset($search[$key]);
            }
        }

        $i = 0;
        $offset = 1000;
        $searchData = [];
        while ($i <= ceil($total / $offset))
        {
            $limit = $i * $offset;
            $listData = $redis->zRevRangeByScore(\Service\Lib\Action\KeyProvider::getSmsServiceLogIdSetKey(), strtotime($form['dateRange'][1]), strtotime($form['dateRange'][0]), ['limit' => [$limit, $offset]]);
            $i++;
            if (!empty($listData))
            {
                foreach ($listData as $listInfo)
                {
                    $searchTag = true;
                    $queryData = json_decode($listInfo, true);
                    if (!empty($search['phone']) && $queryData['phone'] != $search['phone'])
                    {
                        continue;
                    }
                    $data = $redis->hGetAll(\Service\Lib\Action\KeyProvider::getSmsServiceLogIdDataKey($queryData['logId']));
                    if (empty($data))
                    {
                        continue;
                    }
                    foreach ($search as $key => $info)
                    {
                        if (empty($data[$key]))
                        {
                            if (in_array($key, ['callback_status', 'submitStatus']) && $info == 9)
                            {
                                // 允许通过
                                ;
                            }
                            else
                            {
                                $searchTag = false;
                                continue;
                            }
                        }
                        else
                        {
                            if ($data[$key] != $info)
                            {
                                $searchTag = false;
                                continue;
                            }
                        }
                    }
                    if ($searchTag)
                    {
                        $searchData[$data['log_id']] = $data;
                        if (count($searchData) == $page['size'])
                        {
                            break 2;
                        }
                    }
                }
            }
        }
        if (!empty($searchData))
        {
            foreach ($searchData as $log)
            {
                $logData = [];
                $logData['logId'] = $log['log_id'];
                $logData['phone'] = $log['phone'];
                $logData['text'] = \EncodingAction::transcoding($log['text'], 'UTF-8');
                $logData['smsCount'] = isset($log['sms_count']) ? $log['sms_count'] : null;
                $logData['account'] = $log['account'] ?? "null account";
                $logData['operator'] = !empty($log['channel']) ? $log['channel'] : null;
                $logData['type'] = $log['type'];
                $logData['project'] = $log['pid'];
                $logData['position'] = $log['business_id'];
                $logData['serverIp'] = $log['server_ip'];
                $logData['clientIp'] = $log['client_ip'];
                $logData['sendTime'] = $log['send_time'];
                $logData['sendStatus'] = $log['send_status'];
                $logData['sendResponse'] = \EncodingAction::transcoding($log['send_response'], 'UTF-8');
                $logData['submitTime'] = $log['code_time'] ?? '0000-00-00 00:00:00';
                $logData['submitStatus'] = isset($log['code_status']) ? $log['code_status'] : null;
                $logData['submitResponse'] = !empty($log['code_desc']) ? \EncodingAction::transcoding($log['code_desc'], 'UTF-8') : null;
                $logData['callbackTime'] = !empty($log['callback_time']) ? $log['callback_time'] : null;
                $logData['callbackStatus'] = !empty($log['callback_status']) ? $log['callback_status'] : null;
                $logData['callbackResponse'] = !empty($log['callback_message']) ? \EncodingAction::transcoding($log['callback_message'], 'UTF-8') : '';
                $returnData[] = $logData;
            }
            return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode($returnData), 'total' => (int)$total]);
        }
        else
        {
            return json_encode(['err' => 0, 'msg' => 'ok', 'data' => json_encode([]), 'total' => $total]);
        }
    }

}
