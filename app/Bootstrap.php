<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：Bootstrap.php
 * 摘    要：MVC框架启动文件
 * 作    者：张小虎
 * 修改日期：2013.10.12
 */
include APPPATH . '/core/FlushData.php';
include APPPATH . '/core/Config.php';
include APPPATH . '/core/Common.php';
include APPPATH . '/core/Router.php';
include APPPATH . '/core/Controller.php';
include APPPATH . '/core/Action.php';
include APPPATH . '/core/Model.php';
//todo remove next line after replace pdo connector
include APPPATH . '/core/PdoEx.php';
include APPPATH . '/core/RedisEx.php';
include APPPATH . '/core/Filter.php';
include APPPATH . '/vendor/autoload.php';
use Octopus\Logger\Handler\StreamHandler;
use Octopus\Logger\Registry;
use Octopus\Logger;
use Dotenv\Dotenv;

// 加载.env
$envPath = '/opt/case/config/login.2345.com';
if (file_exists($envPath . '/.env')) {
    $dotEnv = new Dotenv($envPath, '.env');
    $dotEnv->load();
}
//print_r($_ENV);exit;

Config::load();
$autoloadIncludes = Config::get('autoloadIncludes');
if ($autoloadIncludes)
{
    foreach ($autoloadIncludes as $includeFile)
    {
        includeFile($includeFile);
    }
}
dev::getInstance(RUNMODE);

// if (dev::isDevelopment() && $_REQUEST['mid'] != 'JSQNLLQ') {
//     if (!empty(dev::getDevTag()) && $_REQUEST['mid'] != 'pcbs') {
//         define('PASSPORT_HOST', (isHttps() ? 'https://' : 'http://') . 'passport.' . dev::getDevTag() . '.2345.cn');
//         define('LOGIN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.' . dev::getDevTag() . '.2345.cn');
//         define('LOGIN_CN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.' . dev::getDevTag() . '.2345.cn');
//     } else {
//         define('PASSPORT_HOST', (isHttps() ? 'https://' : 'http://') . 'passport.2345.com');
//         define('LOGIN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.2345.com');
//         define('LOGIN_CN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.2345.cn');
//     }
// } else {
//     if ($_REQUEST['mid'] == 'JSQNLLQ') {
//         define('PASSPORT_HOST', (isHttps() ? 'https://' : 'http://') . 'passport.qnchrome.com');
//         define('LOGIN_HOST', (isHttps() ? 'https://' : 'http://') . 'passport.qnchrome.com');
//         define('LOGIN_CN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.2345.cn');
//     } else {
//         define('PASSPORT_HOST', (isHttps() ? 'https://' : 'http://') . 'passport.2345.com');
//         define('LOGIN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.2345.com');
//         define('LOGIN_CN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.2345.cn');
//     }
// }

$host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
if ($_REQUEST['mid'] == 'JSQNLLQ' || strpos($host, 'qnchrome.com') !== false) {
    define('PASSPORT_HOST', (isHttps() ? 'https://' : 'http://') . 'passport.qnchrome.com');
    define('LOGIN_HOST', (isHttps() ? 'https://' : 'http://') . 'passport.qnchrome.com');
    define('LOGIN_CN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.2345.cn');
} else {
    define('PASSPORT_HOST', (isHttps() ? 'https://' : 'http://') . 'passport.2345.com');
    define('LOGIN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.2345.com');
    define('LOGIN_CN_HOST', (isHttps() ? 'https://' : 'http://') . 'login.2345.cn');
}

// 2017-12-13 00:00:00 之后，替换为
// define('SHOW_AD', false);
define('SHOW_AD', time() < strtotime('2017-12-13 00:00:00'));

/**
 * 添加运行时日志
 */
$logPath = APPPATH . "/logs/" . date("Ymd") . "/runtime";
deepMkdir($logPath);
$logger = new Logger("runtime");
$logger->pushHandler(new StreamHandler($logPath . "/" . date('H') . ".log"));
Registry::addLogger($logger);
ini_set("display_errors", 0);

if (RUNMODE == "development")
{
//    ini_set("display_errors", 1);
    ini_set("default_charset", 'gbk');
    ini_set("serialize_precision", -1);
    error_reporting(E_ALL & ~E_NOTICE);

    $logPath = APPPATH . "/logs/" . date("Ymd") . "/debug";
    deepMkdir($logPath);
    $logger = new Logger("debug");
    $logger->pushHandler(new StreamHandler($logPath . "/" . date('H') . ".log"));
    Registry::addLogger($logger);
}

$preFilters = Config::get('preFilters');
if ($preFilters)
{
    foreach ($preFilters as $preFilter)
    {
        $filter = loadFilter($preFilter);
        $filter->run();
    }
}
FlushData::load();
Router::parseUrl();
$directory = Router::fetchDirectory();
$className = Router::fetchClass();
$methodName = Router::fetchMethod();
$params = Router::fetchParams();
$routedFilters = Config::get('routedFilters');
if ($routedFilters)
{
    foreach ($routedFilters as $routedFilter)
    {
        $filter = loadFilter($routedFilter);
        $filter->run("$directory/$className/$methodName");
    }
}

$classFile = APPPATH . "/controllers/{$directory}{$className}.php";
if (!file_exists($classFile))
{
    show404();
}
else
{
    include $classFile;
    if (!in_array($methodName, get_class_methods($className)) || in_array($methodName, get_class_methods('Controller')))
    {
        show404();
    }
    else
    {
        $reflectionMethod = new ReflectionMethod($className, $methodName);
        if (!$reflectionMethod->isPublic() || $reflectionMethod->isStatic())
        {
            show404();
        }
        else
        {
            $preControllerFilters = Config::get('preControllerFilters');
            if ($preControllerFilters)
            {
                foreach ($preControllerFilters as $preControllerFilter)
                {
                    $filter = loadFilter($preControllerFilter);
                    $filter->run("$directory/$className/$methodName");
                    if(strpos($directory, "member") !== false)
                    {
                        $filter->isLogin();
                    }
                }
            }
            $class = new $className();
            call_user_func_array(array(&$class, $methodName), $params);
        }
    }
}
