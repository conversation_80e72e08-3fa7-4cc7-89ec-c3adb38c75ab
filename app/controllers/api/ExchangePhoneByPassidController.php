<?php
use Service\Capacity\GetRangeTable;

/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/2/20
 * Time: 14:14
 */
class ExchangePhoneByPassidController extends Controller
{

    private static $lockTime = '25';  //单位秒

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight()) {
            $this->codeResponse("401", [], "非法访问");
        }
    }

    /**
     * 获取访问锁KEY
     * @param string $mid 项目ID
     * @return string
     */
    private static function getVisitLockKey($mid)
    {
        return 'exchange:hidePhone:mid:' . $mid;
    }

    /**
     * 隐藏手机中间4位
     * @param int $phone 手机号码
     * @return string
     */
    private static function hidePhone($phone)
    {
        return substr($phone, 0, 3) . '****' . substr($phone, - 4);
    }

    /**
     * 批量入口
     * @return void
     */
    public function actionIndex()
    {
        $mid = \Common\Utils\Url::getStringParam('mid');
        $passIds = \Common\Utils\Url::getStringParam('passIds');
        if (empty($mid) || empty($passIds)) {
            $this->codeResponse("406", [], "缺少参数");
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid])) {
            $this->codeResponse("501", [], "项目标识不正确");
        }
        $passIdList = explode(',', $passIds);
        if (count($passIdList) > 500) {
            $this->codeResponse("407", [], "长度超限,最大支持500个");
        }
        foreach ($passIdList as $key => $inputPassId) {
            $passIdList[$key] = trim($inputPassId);
            if (!is_numeric($passIdList[$key])) {
                unset($passIdList[$key]);
            }
            if (empty($passIdList[$key])) {
                unset($passIdList[$key]);
            }
        }
        try {
            $redis = RedisEx::getInstance();
            $visitLock = $redis->set(self::getVisitLockKey($mid), time(), ['nx', 'ex' => self::$lockTime]);
            if (!$visitLock) {
                $this->codeResponse("408", [], "请求频繁");
            }
            $memberModel = loadModel('member');
            $outData = [];
            $userList = $memberModel->getNewMemberByPassIdList($passIdList, ['id', 'phone']);
            foreach ($userList as $userInfo) {
                if (empty($userInfo['phone'])) {
                    $outData[$userInfo['id']] = '';
                } else {
                    $outData[$userInfo['id']] = self::hidePhone($userInfo['phone']);
                }
            }
            foreach ($passIdList as $passId) {
                if (!isset($outData[$passId])) {
                    $outData[$passId] = '';
                }
            }
            $this->codeResponse("200", $outData, "获取成功");
        }
        catch (Exception $exception) {
            $this->codeResponse("500", [], "系统错误");
        }
    }
}
