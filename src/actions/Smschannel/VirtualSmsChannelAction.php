<?php
/**
 * 文件名称：VirtualSmsChannelAction.php
 * @copyright Copyright (c) 2017，上海二三四五网络科技股份有限公司
 * <AUTHOR>
 * @group     互联网研发中心/平台开发组
 * @date      2017/12/08
 */

namespace Smschannel;

class VirtualSmsChannelAction extends SmsChannelAction
{
    /**
     * 发送短信，单条
     * @param array|string $phone 手机号
     * @param string       $msg   短信内容
     * @param string       $sign  签名
     * @return int
     */
    public function send($phone, string $msg, string $sign)
    {
        $redisAction = \RedisAction::connect();
        $multi = 0;
        if (is_array($phone))
        {
            $multi = 1;
        }
        if (strpos($msg, '成功') !== false)
        {
            $desc = '虚拟成功';
            $resu = self::RET_SUCC;
        }
        else
        {
            $desc = '虚拟失败';
            $resu = self::RET_ERR;
        }

        if (!$multi)
        {
            $msgid = time() . rand(100, 999);
            $this->msgId = $msgid;
            $this->OperatorMsg = array(
                'result'     => $resu,
                'codeStatus' => 2345,
                'codeDesc'   => $desc,
            );
            $dataInfo = array(
                'Mobile'   => $phone,//手机号码
                'Msg_Id'   => $msgid,//订单号码
                'Status'   => $resu === 1 ? 'DELIVRD' : 'FAILED',
                'SendTime' => date('Y-m-d H:i:s'),//发送时间
                'RecvTime' => date('Y-m-d H:i:s'),//到达时间
            );
            $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($dataInfo));
            return $resu;
        }
        else
        {
            /** @var array $phone */
            foreach ($phone as $index => $phoneLine)
            {
                $msgid = time() . rand(100, 999);
                $this->msgIdList[$phoneLine] = $msgid;
                $this->operatorMsgList[$phoneLine] = array(
                    'result'     => $resu,
                    'codeStatus' => 2345,
                    'codeDesc'   => $desc,
                );
                $dataInfo = array(
                    'Mobile'   => $phoneLine,//手机号码
                    'Msg_Id'   => $msgid,//订单号码
                    'Status'   => $resu === 1 ? 'DELIVRD' : 'FAILED',
                    'SendTime' => date('Y-m-d H:i:s'),//发送时间
                    'RecvTime' => date('Y-m-d H:i:s'),//到达时间
                );
                $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($dataInfo));
            }
            return $resu;
        }
    }

    /**
     * 相同内容多个手机号群发
     * @param array  $phone [phone1,phone2,phone3]
     * @param string $msg   message
     * @param string $sign  签名
     * @return int
     */
    public function multiPhoneSend($phone, $msg, $sign)
    {
        $this->send($phone, $msg, $sign);
    }

    /**
     * 不同手机号不同内容无变量群发
     * @param array $multiContent multiContent
     * @return int
     */
    public function batchSend($multiContent)
    {
        $redisAction = \RedisAction::connect();
        $oneSuccess = false;
        $oneFailed = false;
        foreach ($multiContent as $key => $value)
        {
            $msgid = time() . rand(100, 999);
            if (strpos($value[1], '成功') !== false)
            {
                $desc = '虚拟成功';
                $resu = self::RET_SUCC;
                $oneSuccess = true;
            }
            else
            {
                $desc = '虚拟失败';
                $resu = self::RET_ERR;
                $oneFailed = true;
            }
            $this->msgIdList[$value[0]] = $msgid;
            $this->operatorMsgList[$value[0]] = array(
                'codeStatus' => 2345,
                'codeDesc'   => $desc,
                'result'     => $resu,
            );
            $dataInfo = array(
                'Mobile'   => $value[0],//手机号码
                'Msg_Id'   => $msgid,//订单号码
                'Status'   => $resu === 1 ? 'DELIVRD' : 'FAILED',
                'SendTime' => date('Y-m-d H:i:s'),//发送时间
                'RecvTime' => date('Y-m-d H:i:s'),//到达时间
            );
            $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($dataInfo));
        }
        return $oneSuccess ? ($oneFailed ? self::RET_SUCC_PART : self::RET_SUCC) : self::RET_ERR;
    }

    public static function callbackStatusConfig($channelConfig)
    {
        $params = array(
            'username' => $channelConfig['username'],//用户名
            'password' => $channelConfig['password'],//密码
        );
        $api = $channelConfig['api_url'];
        $query = http_build_query($params);
        $finalUrl = "{$api}?{$query}";

        return array(
            'url'           => '',
            'data'          => '',
            'account'       => $channelConfig['username'],
            'header'        => [],
            'action'        => 'get',
            'callbackClass' => '\\Smschannel\\VirtualSmsChannelAction',
            'callbackFunc'  => 'callbackStatus',
            'callback'      => '',
        );
    }

    public static function callbackStatus($data)
    {
        return 0;
    }
}
