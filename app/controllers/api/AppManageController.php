<?php
use Service\appClient\client;
use WebLogger\Facade\LoggerFacade;

/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2021/2/4
 * Time: 13:07
 */
class AppManageController extends Controller
{
    private $appClient = null;

    public function __construct()
    {
        $this->appClient = client::getInstances([]);
    }

    /**
     * 接口创建应用
     */
    public function actionSave()
    {
        $data = file_get_contents("php://input");
        LoggerFacade::info("App申请", ["data" => $data]);
        $decodeData = json_decode($data, true);
        if (empty($decodeData["productCode"]) || empty($decodeData['requestId']) || empty($decodeData['version']) || empty($decodeData['timestamp'])) {
            $this->echoJsonResponse(500, "", "FD10001", "缺少项目必要参数");
        }
        $dataSort = json_encode($decodeData["data"], JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);
        $makeSign = md5($decodeData["productCode"] . $decodeData['requestId'] . $decodeData['version'] . $decodeData['timestamp'] . $dataSort . $this->getSign());
        if ($makeSign != $decodeData['sign']) {
            $this->echoJsonResponse(403, "", "FD10002", "签名不正确");
        }
        $mid = $decodeData["data"]["appCode"];
        $appName = $decodeData["data"]["appName"];
        if (empty($mid) || empty($appName)) {
            $this->echoJsonResponse(500, "", "FD10003", "应用名称或应用code不能为空");
        }
        $packageName = $decodeData["data"]["packageName"] ?? "";
        $packageSign = $decodeData["data"]["packageSign"] ?? "";
        if ($this->checkMidIsExists($mid)) {
            $this->echoJsonResponse(200, $this->outputData($mid));
        }
        if ($this->create($mid, $appName, $packageName, $packageSign)) {
            $redisAction = loadAction('UnionLoginRedis');
            $redisAction->updateEnablePackageCache();
            $redisAction->delPackageCache($packageName);
            $this->echoJsonResponse(200, $this->outputData($mid));
        } else {
            $this->echoJsonResponse(500, '', "FD10004", "创建失败,请联系管理员");
        }
    }

    /**
     * 创建应用
     * @param $mid
     * @param $appName
     * @param $packageName
     * @param $packageSign
     * @return bool
     */
    protected function create($mid, $appName, $packageName, $packageSign)
    {
        $response = $this->appClient->saveAppInfo(0, 0, $mid, $appName, $packageName, $packageSign);
        if (!$response->success()) {
            LoggerFacade::error("接口创建应用-失败", $response->getMsg());

            return false;
        }

        return true;
    }

    /**
     * 判断mid是否已存在
     * @param $mid
     * @return bool
     */
    protected function checkMidIsExists($mid)
    {
        $appList = $this->appClient->getAppList();
        if (!$appList->success()) {
            LoggerFacade::error("接口创建应用-获取应用列表失败", $appList->getMsg());
            $this->codeResponseNew(500, "", "获取应用列表失败");
        }
        foreach ($appList->getData() as $info) {
            if ($info["mid"] == $mid) {
                return true;
            }
        }

        return false;
    }

    /**
     * 输出mid和uckey
     * @param $mid
     * @return array
     */
    protected function outputData($mid)
    {
        $uc = $this->appClient->getAppUcKey($mid);
        if (!$uc->success()) {
            LoggerFacade::error("接口创建应用-获取uckey失败", $uc->getMsg());
            $this->codeResponseNew(500, "", "获取应用信息失败");
        }

        return [
            "mid"   => $mid,
            "ucKey" => $uc->getData()["ucKey"],
        ];
    }

    /**
     * @param        $code
     * @param array  $data
     * @param string $errorCode
     * @param string $errorMessage
     */
    protected function echoJsonResponse($code, $data = array(), $errorCode = "", $errorMessage = "")
    {
        $response = array(
            "success" => $code == 200 ? true : false,
            "code"    => $code,
            "data"    => $data,
        );
        if (!empty($errorCode)) {
            $response["errorCode"] = $errorCode;
        }
        if (!empty($errorMessage)) {
            $response["errorMessage"] = iconv("GBK", "UTF-8", $errorMessage);
        }
        echo json_encode($response);
        exit;
    }

    /**
     * 返回sign key
     * @return mixed|string
     */
    protected function getSign()
    {
        return !empty($_ENV["APP_SIGN_KEY"]) ? $_ENV["APP_SIGN_KEY"] : "ea5e5802";
    }
}
