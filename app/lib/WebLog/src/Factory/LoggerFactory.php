<?php
/**
 * Created on 2019-10-29 16:35
 * FileName LoggerFactory.php
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 */

namespace WebLogger\Factory;

use WebLogger\Services\AbstractLogger;
use WebLogger\Services\OctopusLoggerService;
use WebLogger\Services\SeasLogLoggerService;

class LoggerFactory
{
    private static $logger = null;

    /**
     * @return AbstractLogger
     */
    public static function getLogger()
    {
        if (extension_loaded('SeasLog')) {
            if (is_null(self::$logger)) {
                self::$logger = new SeasLogLoggerService();
            }
        } else {
            // OctopusLoggerService 内部需要按照小时实现单例模式
            self::$logger = OctopusLoggerService::getInstance();
        }
        return self::$logger;
    }
}
