<?php

class WarningAction extends Action
{
    private $requestInfo = [];// 记录请求的整体响应信息
    private $data = [];//记录请求过程中的其他埋点信息

    /**
     * @param array $data $data
     * @return $this
     */
    public function setRequestInfo($data)
    {
        if (isset($data['path']) && $data['path']) {
            $pathInfo = parse_url($_SERVER['REQUEST_URI']);
            $pathInfo && $data['path'] = strtolower($pathInfo['path']);
        }
        $this->requestInfo = array_merge($this->requestInfo, $data);
        return $this;
    }

    /**
     * @param array $data $data
     * @return $this
     */
    public function addData($data)
    {
        $this->data[] = $data;
        return $this;
    }

    /**
     * 全局推送使用
     *
     * @return void
     */
    public function flush()
    {
        $dirType = "";
        if (isset($this->requestInfo['dir_type'])) {
            $dirType = $this->requestInfo['dir_type'];
            unset($this->requestInfo['dir_type']);
        }
        $allStr = '';
        if (isset($this->requestInfo['type'])) {
            array_unshift($this->data, $this->requestInfo);
        }
        foreach ($this->data as $item) {
            $allStr .= $this->format($item);
        }
        $this->write($allStr, $dirType);
    }

    /**
     * 立即记录,服务维度的埋点使用
     *
     * @param array $data $data
     * @return void
     */
    public function record($data)
    {
        $dirType = "";
        if (isset($data['dir_type'])) {
            $dirType = $data['dir_type'];
            unset($data['dir_type']);
        }
        $this->write($this->format($data), $dirType);
    }

    /**
     * 数据格式化
     *
     * @param array $data $data
     * @return string
     */
    private function format($data)
    {
        $context = "";
        if (isset($data['context'])) {
            $context = $data['context'];
            unset($data['context']);
        }
        if (isset($data['level'])) {
            $level = $data['level'];
            unset($data['level']);
        }
        $time = explode(' ', microtime());
        $str = "project:login.2345.com|ip:{$_SERVER['SERVER_ADDR']}|time:"
            . date('Y-m-d H:i:s', $time[1]) . "." . substr($time[0], 2, 3) . "|level:" . ($level ?? 'info') . "|";
        foreach ($data as $key => $val) {
            if (is_bool($val)) {
                $val = $val == true ? "true" : "false";
            }
            $str .= "{$key}:{$val}|";
        }
        $str = substr($str, 0, -1);
        $context && $str .= "|context:" . serialize($context);
        $str .= PHP_EOL;
        return $str;
    }

    /**
     * 写入文件
     *
     * @param string $content $content
     * @param string $dirType $dirType
     * @return void
     */
    private function write($content, $dirType = "")
    {
        empty($dirType) && $dirType = "w_default";
        $path = APPPATH . "/logs/" . date("Ymd") . "/Warning/" . date("H");
        if (!is_dir($path)) {
            @mkdir($path, 0755, true);
        }
        file_put_contents($path . "/" . $dirType . ".log", $content, FILE_APPEND);
    }
}
