<?php
/**
 * 文件名称：GuoDu2SmsChannelAction.php
 * @copyright Copyright (c) 2017，上海二三四五网络科技股份有限公司
 * <AUTHOR>
 * @group     互联网研发中心/平台开发组
 * @date      2017/12/21
 */

namespace Smschannel;

use FuncAction;

class GuoDu2SmsChannelAction extends SmsChannelAction
{
    /**
     * @param array  $multiContent multiContent
     * @param string $sign         sign
     * @return int
     */
    public function batchSend($multiContent, $sign)
    {
        $phoneArr = array();
        $msgArr = array();
        foreach ($multiContent as $key => $value)
        {
            list($phone, $msg) = $value;
            $phoneArr[] = $phone;
            $msgArr[] = urlencode($sign . $msg);
        }
        $phoneStr = implode(",", $phoneArr);
        $msgStr = implode(",", $msgArr);
        $params = array(
            'OperID'    => $this->user,//用户名
            'OperPass'  => $this->pwd,//密码
            'DesMobile' => $phoneStr,
            "Content"   => $msgStr,
        );
        $resultStr = FuncAction::request($this->apiUrl, $params)[0];
        $response = explode(',', $resultStr);

        if (in_array($response[0], array('00', '01', '03')))
        {
            foreach ($multiContent as $key => $value)
            {
                $this->msgIdList[$value[0]] = $response[1];
                $this->operatorMsgList[$value[0]] = array(
                    'codeStatus' => 1,
                    'codeDesc'   => "成功",
                    'result'     => self::RET_SUCC,
                );
            }
            return self::RET_SUCC;
        }
        else
        {
            foreach ($multiContent as $key => $value)
            {
                $this->operatorMsgList[$value[0]] = array(
                    'codeStatus' => 0,
                    'codeDesc'   => "失败，代码{$response[0]}",
                    'result'     => self::RET_ERR,
                );
            }
            return self::RET_ERR;
        }
    }
}
