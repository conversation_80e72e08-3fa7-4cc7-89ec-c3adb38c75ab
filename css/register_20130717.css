@charset "gb2312";
/* CSS Document */

body{ background:#f8fcff;}
.wrap{ background:url(../images/bg.gif) top center no-repeat;}
.top,.main,.foot{ width:960px; margin:0 auto;}
.top{height:87px; color:#ccc;}
.top p{ height:25px; line-height:25px; margin-top:50px; display:block; float:right;}
.top .logo img{ margin:13px 0 0 0;}
.main{ border:1px solid #e3e3e3; border-top:2px solid #91c4f7; background:#fff; min-height:364px; _height:364px; }
.main_dl{background:#fff url(../images/lgmainbg.png) right top no-repeat; }
.foot{ color:#ccc; height:50px; line-height:50px; border-top:2px solid #f0f4f7; text-align:center;}
.foot span{ color:#666;}
.blue{ color:#1989d7;}
.gray{ color:#999; font-size:12px;}
.orange{ color:#ff7404;}

.m_left{ width:340px; float:left; padding:75px 0 70px 100px; background:url(../images/line.png) right 10px no-repeat;}
.m_left2{width:563px; float:right; text-align:left; padding:32px 0 35px 40px;background: url(../images/rightbg2.png) -30px 0 no-repeat; *padding-top:28px;_padding-top:34px;}
.m_left2 .reg_tit01{padding-left:12px;height:32px;overflow:hidden;}
.m_left2 .reg_tit01 img{vertical-align:-2px;}
.m_left2 .reg_tit01 a{color:#1989d7;font-size:13px;margin-left:8px;}
.m_left2 .reg_tit01 a:hover{color:#f60;}
.m_left2 .hasreg{display:inline-block;vertical-align:-6px;margin-left:25px;}
.m_left2 .hasreg a{color:#1989d7;}
.m_left2 .hasreg a:hover{color:#f60;}
.m_right{ width:420px; padding:46px 0 0 35px; float:left;*padding-top:50px;_padding-top:44px;}
.m_right2{width:350px; float:left;padding:40px 0px 0 0px; color:#666; position:relative;}
.m_right2 .reg_tit01{height:22px;overflow:hidden; margin-left:45px;}
.m_right2 .qq_sina_login{margin-left:63px;padding-top:28px;}
.m_right2 .tip{ background:url(../images/icons.gif) -170px -195px no-repeat; height:25px; line-height:25px; margin-top:30px; padding-left:25px;}
.m_right_what{ position:absolute; top:40px; right:30px;}
.rgstab01{ margin-top:42px;}
.rgstab01 th{ font-weight:100; font-size:14px; color:#333; text-align:right; padding:7px 12px 5px 0;line-height:30px; vertical-align:top; width:120px;}
.rgstab01 th .info{ margin:0 20px 0 30px; padding-top:15px; border-top:1px solid #dde3e4;}
.rgstab01 td{ text-align:left; vertical-align:top; line-height:30px; padding:6px 0; }
.rgstab01 td p{ height:22px; line-height:22px;}
.rgstab01 td p.red{color:red}
.btnsgp{ width:215px; height:94px; overflow:hidden;}
.qq_sina_login a.s_btn{ background: url(../images/login_130711.png) 0 -199px no-repeat; width:99px; height:35px; line-height:35px; color:#666; font-size:14px; margin:20px 6px 0 0; float:left;}
.qq_sina_login a.s_btn:hover{ background-position: -100px -199px; text-decoration:none; }
.ico_sinablog,.ico_taobao{ display:inline-block; width:19px; height:16px; overflow:hidden; float:left; margin:10px 5px 0 8px; background:url(../images/login_130711.png) no-repeat -214px -199px;}
.ico_taobao{ background-position:-244px -199px;}
/*.rgstab02{width:480px;}*/
.rgstab02 th{ width:33%;}
.rgstab02 .tit01{ padding-left:72px;padding-bottom:15px;height:32px;overflow:hidden;}
.rgstab02 .tit01 img{vertical-align:-4px;}
.rgstab02 .tit01 span{ font-size:13px;margin-left:8px;}
.rgstab02 .tit01 span a{ color:#1989d7;font-weight:bold;}
.rgstab02 .tit01 span a:hover{color:#f60;}
.rgstab01 img{vertical-align:middle;}
.btn_01{border:none; background:transparent url(../images/icons.gif) 0 -389px no-repeat; width:141px; height:43px; cursor:pointer; vertical-align:middle; margin-right:13px; }
.btn_01_hov{ border:none; background:transparent url(../images/icons.gif) 0 0 no-repeat; width:141px; height:43px; cursor:pointer; vertical-align:middle; margin-right:13px;}
.btn_02{ border:none; background:transparent url(../images/icons.gif) 0 -44px no-repeat; width:119px; height:34px; cursor:pointer; vertical-align:middle; margin:0 5px 0 20px;}
.btn_03{border:none; background:transparent url(../images/login_130711.png) 0 -50px no-repeat; width:134px; height:39px; cursor:pointer; vertical-align:middle; margin-top:10px}
.btn_03_hov{border:none; background:transparent url(../images/login_130711.png) -150px -50px no-repeat; width:134px; height:39px; cursor:pointer; vertical-align:middle; margin-top:10px}
.btn_04_2{border:none; float:left; background:transparent url(../images/icons.gif) 0 -187px no-repeat; width:119px; height:34px; cursor:pointer; margin-left:5px;}
.check01{ width:13px; height:13px; vertical-align:middle; margin:-2px 4px 0 0 }
.m_right2 .p2{ line-height:34px; height:34px;}
.m_right2 .p2 i{ display:inline-block; float:left;}
.rgstab01 .height1 td,.rgstab01 .height1 th{ height:25px; line-height:25px; padding-top:0; }
.rgstab01{ margin-top:0px; clear:both;}
.rgstab01 th{font-weight:100; font-size:14px; color:#666; text-align:right;height:40px; padding:8px 12px 0 0;}
.rgstab01 td{ height:32px; line-height:32px; padding:8px 0 0;}
.rgstab01 th font{color:#e92828; font-family:Verdana, Arial, Helvetica, sans-serif; vertical-align:middle; margin-right:4px;}
.rgstab01 .code td{ padding:0;}
.pwtip{display:block; color:#e92828; background:url(../images/icons.gif) -187px -213px no-repeat; padding-left:8px; height:32px; line-height:32px;}
.cnt_right2 i{ font-style:normal; text-align:right; display:block; float:left}


.flink td{ text-align:left; line-height:40px; padding:2px 5px;}
.ipt_01{ height:28px; line-height:28px; border:1px solid #cdcdcd; background:transparent url(../images/icons.gif) 0 -110px repeat-x; width:195px; padding-left:5px; font-size:14px;}
.ipt_code{ height:28px; line-height:28px; border:1px solid #cdcdcd; background:transparent url(../images/icons.gif) 0 -110px repeat-x; width:80px; padding-left:5px; font-size:14px;}
.tit01{ padding:0 0 20px 57px;}

.tuiguang{margin:0 -10px;padding-top:30px; text-align:left;}
.tuiguang li{height:21px; line-height:21px; padding-left:10px; background:url(./tuiguang_ico.png) no-repeat 0 5px; color:#0b54ac}
.qq_sina_login{margin:0px 0 10px;}
.qq_sina_login .tit02{ padding-bottom:25px;}
.qq_sina_login a.qq{ background-position: 0 -100px; width:205px; height:39px; line-height:1000px; display:block;background: url(../images/login_130711.png) 0 -100px no-repeat; overflow:hidden;}
.qq_sina_login a.qq:hover{ background-position: -225px -100px; }
.qq_sina_login a.sina{ background-position:0 -150px;}
.qq_sina_login a.sina:hover{ background-position: -225px -150px; }


.btn_01{ background: url(../images/login_130711.png) 0 0 no-repeat; width:134px; height:39px; }
.btn_01_hov{ background: url(../images/login_130711.png) -150px 0 no-repeat; width:134px; height:39px; }

.info span{ color:#333; font-size:12px; }
.info a{ color:#1989d7; font-weight:bold; font-size:14px; padding: 0 20px 0 10px; text-decoration:underline; }
.info a:hover{ color:#ff6600; }

.no_link{ width:200px; min-height:80px;_height:80px; margin:40px auto;padding:10px 0 0 100px; line-height:24px; background:url(./txz_img.png) no-repeat -155px 0}
.no_link em{font-size:14px; font-weight:bold; font-style:normal}
.no_link span a{color:#1989d7; text-decoration:underline}
.no_link span a:hover{color:#f60; text-decoration:underline}

.user_ok{width:380px; min-height:60px;_height:60px; margin:0px auto;padding:20px 0 0 100px; line-height:24px; font-size:16px; font-weight:bold; background:url(../images/txz_img.png) no-repeat -155px -122px}
.user_ok span{color:#1989d7}

.password_safety{padding:2px 5px 0 0;width:175px; height:19px; line-height:19px; font-size:12px; color:#666; display:inline-block; overflow:hidden}
.password_safety em{padding:2px 3px 4px; width:106px; height:13px; display:inline-block; background:url(../images/passwordbg.png) no-repeat 0 0}
.password_safety em i{text-align:center;height:13px;line-height:13px;_padding-top:1px;display:inline-block; font-style:normal; color:#fff; vertical-align:top }
.password_safety em i.w1{width:26px; background:url(../images/passwordbg.png) no-repeat 0 -19px;}
.password_safety em i.w2{width:54px; background:url(../images/passwordbg.png) no-repeat 0 -33px;}
.password_safety em i.w3{width:78px; background:url(../images/passwordbg.png) no-repeat 0 -47px;}
.password_safety em i.w4{width:106px; background:url(../images/passwordbg.png) no-repeat 0 -61px;}
#msg_username img{ float:left; margin:7px 3px 0 0;}

/* 20130603 */
.fred{ text-decoration:none; color:#f30;}
.getpw_box{padding-top:50px;}
.getpw_box td{ font-size:12px;}
.getpw_box td label{ font-size:14px; color:#666; display:inline-block;_margin-top:7px; cursor:pointer}
.getpw_box .radio_1{ width:13px; height:13px; overflow:hidden; vertical-align:middle; margin:-3px 3px 0 0;}
.tip_alt{ display:inline-block; width:15px; height:15px; position:relative; z-index:2; background:url(../images/icon_0603.png) 0 0 no-repeat; margin:-6px 0 0 -14px; +margin-top:5px; vertical-align:middle;}
.tip_alt span{ display:inline-block; position:absolute; top:-34px; left:-105px; width:212px; height:30px; line-height:25px; background:url(../images/icon_0603.png) 0 -20px no-repeat; font-size:12px; color:#999; text-align:center;}
.getpw_box .rgstab02 th{ width:340px}
.getpw_box .pwtip{  font-size:12px;}
.btn_submit{border:none; background:transparent url(../images/icon_0603.png) 0 -57px no-repeat;width:134px; height:39px;cursor:pointer; vertical-align:middle; margin-right:13px; }
.btn_submit_hov{ border:none; background:transparent url(../images/icon_0603.png) 0 -97px no-repeat; width:134px; height:39px; cursor:pointer; vertical-align:middle; margin-right:13px;}
.getpw_box .rgstab02 th ins.fred{ margin-right:3px;}
.service_icon{ display:inline-block; background:url(../images/icon_0603.png) -196px -58px no-repeat; padding-left:22px; text-decoration:none; margin-right:13px; color:#333;}