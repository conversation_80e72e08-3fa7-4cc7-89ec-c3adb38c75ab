<?php

class LoginCheckController extends Controller
{

    /**
     * login check
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }

        if (filter_var($_POST['ip'], FILTER_VALIDATE_IP))
        {
            $loginIp = $_POST['ip'];
            $loginAction = loadAction('login');
            if ($loginAction->decideCode($loginIp))
            {
                echo 2;  //IP 非法登录
            }
            else
            {
                echo 3;  //IP 正常登录
            }
        }
        else
        {
            echo 1;  //IP 登录IP为空
        }
    }

}