<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/4/11
 * Time: 10:55
 */

namespace Smschannel;

use FuncAction;
use Service\Swoole\Tool\Log;

class ZdjrChannelAction extends SmsChannelAction
{
    /**
     * 初始化
     * @param string $user   username
     * @param string $pwd    password
     * @param string $apiUrl api url
     */
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }

    /**
     * 单条发送
     * @param int    $phone 手机号码
     * @param string $msg   发送内容
     * @param string $sign  签名
     * @return int
     */
    public function send($phone, $msg, $sign)
    {
        $phoneStr = $phone;
        $multi = 0;
        if (is_array($phoneStr)) {
            $phoneStr = implode(",", $phoneStr);
            $multi = 1;
        }
        list($accessCode, $user) = explode(':', $this->user);
        $msg_u8 = iconv('GBK', 'UTF-8', $sign . $msg);
        $params = [
            'action'   => 'send',
            'account'  => $user,
            'password' => $this->pwd,
            'mobile'   => $phoneStr,
            'content'  => $msg_u8,
            'extno'    => $accessCode, //'7012',
        ];
        $response = FuncAction::postTimeout($this->apiUrl, $params);
        /*        $response = '<?xml version="1.0" encoding="utf-8" ?><returnsms><returnstatus>Success</returnstatus><message>OK</message><remainpoint>116</remainpoint><taskID>5059260704940634072</taskID><resplist><resp>5059260704940634072#@#***********#@#0#@#</resp></resplist><successCounts>1</successCounts></returnsms>';*/
        //        Log::setLogMsg($response, true, 'zdjr/send');
        $res = simplexml_load_string($response);

        if ($res->returnstatus == 'Success') {
            if ($multi == 1) {
                $phoneMsgList = [];
                foreach ($res->resplist->resp as $respInfo) {
                    $respArr = explode('#@#', $respInfo);
                    $phoneMsgList[$respArr[1]] = [
                        'msgId' => $respArr[0],
                        'msg'   => join('#@#', $respArr),
                    ];
                }
                foreach ($phone as $phoneLine) {
                    $responseMsg = !empty($phoneMsgList[$phoneLine]) ? $phoneMsgList[$phoneLine] : [];
                    $this->msgIdList[$phoneLine] = $responseMsg['msgId'];
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_SUCC,
                        'codeStatus' => 0,
                        'codeDesc'   => '成功',
                    );
                }
            } else {
                $this->setMsgId($res->taskID);
                $this->OperatorMsg = array(
                    'result'     => self::RET_SUCC,
                    'codeStatus' => 0,
                    'codeDesc'   => '成功',
                );
            }

            return self::RET_SUCC;
        } else {
            if ($multi == 1) {
                $phoneMsgList = [];
                foreach ($res->resplist->resp as $respInfo) {
                    $respArr = explode('#@#', $respInfo);
                    $phoneMsgList[$respArr[1]] = [
                        'msgId' => $respArr[0],
                        'msg'   => join('#@#', $respArr),
                    ];
                }
                foreach ($phone as $phoneLine) {
                    $this->msgIdList[$phoneLine] = $phoneMsgList[$phoneLine]['msgId'];
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_ERR,
                        'codeStatus' => - 1,
                        'codeDesc'   => mb_convert_encoding($phoneMsgList[$phoneLine]['msg'], 'GBK', 'UTF-8'),
                    );
                }
            } else {
                $errorMsg = '';
                foreach ($res->resplist->resp as $respInfo) {
                    $errorMsg .= $respInfo;
                }
                $this->setMsgId($res->taskID);
                $this->OperatorMsg = array(
                    'result'     => self::RET_ERR,
                    'codeStatus' => - 1,
                    'codeDesc'   => mb_convert_encoding($res->message . $errorMsg, 'GBK', 'UTF-8'),
                );
            }

            return self::RET_ERR;
        }
    }

    /**
     * set msgId
     * @param string $msgId msgId
     * @return null
     */
    private function setMsgId($msgId)
    {
        $this->msgId = $msgId;
    }

    /**
     * 单条发送
     * @param int    $phone 手机号码
     * @param string $msg   发送内容
     * @param string $sign  签名
     * @return int
     */
    public function multiPhoneSend($phone, $msg, $sign)
    {
        return $this->send($phone, $msg, $sign);
    }


    /**
     * 获取 callback status 需要的配置文件, 和设定回调方法
     * @param array $channelConfig channelConfig
     * @return array
     */
    public static function callbackStatusConfig($channelConfig)
    {
        $params = array(
            'action'   => 'report',
            'account'  => $channelConfig['username'],//用户名
            'password' => $channelConfig['password'],//密码
        );
        $api = $channelConfig['api_url'];
        $query = http_build_query($params);
        $finalUrl = "{$api}?{$query}";

        return array(
            'url'           => $finalUrl,
            'data'          => '',
            'account'       => $channelConfig['username'],
            'action'        => 'get',
            'callbackClass' => '\\Smschannel\\ZdjrChannelAction',
            'callbackFunc'  => 'callbackStatus',
            'callback'      => '',
        );
    }

    /**
     * 获取 callback status, 是回调方法
     * @param array $data data
     * @return boolean
     */
    public static function callbackStatus($data)
    {
        //        Log::setLogMsg($data, true, 'zdjr/callback');
        /*
         $data = '<?xml version="1.0" encoding="utf-8" ?><returnsms><statusbox><mobile>***********</mobile><taskid>5060115575231230076</taskid><status>10</status><errorcode>DELIVRD</errorcode><receivetime>2019/4/12 14:18:19</receivetime></statusbox></returnsms>';
         $data1 = '<?xml version="1.0" encoding="utf-8" ?><returnsms><statusbox><mobile>***********</mobile><taskid>5059260704940634072</taskid><status>10</status><errorcode>DELIVRD</errorcode><receivetime>2019/4/12 13:33:45</receivetime></statusbox><statusbox><mobile>18862735209</mobile><taskid>5059837673667309592</taskid><status>10</status><errorcode>DELIVRD</errorcode><receivetime>2019/4/12 14:02:32</receivetime></statusbox><statusbox><mobile>***********</mobile><taskid>5059837673667309591</taskid><status>10</status><errorcode>DELIVRD</errorcode><receivetime>2019/4/12 14:02:33</receivetime></statusbox></returnsms>';
        */
        $res = (array)simplexml_load_string($data);
        if (!empty($res['errorstatus'])) {
            \WebLogger\Facade\LoggerFacade::info('error ZdjrChannelAction::send response', (array)$data);
            return false;
        }
        $result = true;
        if (!empty($res['statusbox'])) {
            $redisAction = \RedisAction::connect();
            if (is_object($res['statusbox'])) {
                $result = self::pushResponseData((array)$res['statusbox'], $redisAction);
            } elseif (is_array($res['statusbox'])) {
                foreach ($res['statusbox'] as $box) {
                    $result &= self::pushResponseData((array)$box, $redisAction);
                }
            }
        }

        return $result;
    }

    /**
     * 载入回执信息
     * @param array  $data        载入redis数组
     * @param object $redisAction redis
     * @return mixed
     */
    private static function pushResponseData($data, $redisAction)
    {
        $dataInfo = array(
            'Mobile'    => $data['mobile'],//手机号码
            'Msg_Id'    => $data['taskid'],//订单号码
            'SendTime'  => date('Y-m-d H:i:s', strtotime($data['receivetime'])),  //发送时间
            'RecvTime'  => date('Y-m-d H:i:s', strtotime($data['receivetime'])),   //到达时间
            'TimeStamp' => date('Y-m-d H:i:s'),//到达时间格式化
        );
        if ($data['errorcode'] == 'DELIVRD') {
            $dataInfo['Status'] = 'DELIVRD';
        } else {
            $dataInfo['Status'] = $data['errorcode'];
        }

        return $redisAction->lPush(
            \Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW,
            json_encode($dataInfo)
        );
    }

}
