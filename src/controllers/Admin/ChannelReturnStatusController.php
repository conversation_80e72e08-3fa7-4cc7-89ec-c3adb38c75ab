<?php

namespace Admin;

use Controller;

class ChannelReturnStatusController extends Controller
{
    /**
     * 短信发送状态
     * @return void
     */
    public function actionIndex()
    {
        $pageArray = array();

        $pageArray['startTime'] = $this->Filter->getPost('startTime');
        $pageArray['endTime'] = $this->Filter->getPost('endTime');
        $pageArray['phone'] = $this->Filter->getPost('phone');
        $pageArray['status'] = $this->Filter->getPost('status');
        $pageArray['operatorId'] = $this->Filter->getPost('operatorId');
        $pageArray['projectId'] = $this->Filter->getPost('projectId');
        $pageArray['receiptStatus'] = $this->Filter->getPost('receiptStatus');
        if (empty($pageArray['startTime']) || empty($pageArray['endTime']))
        {
            $pageArray['startTime'] = date('Y-m-d');//date('Y-m-d' ,strtotime("-2 day"));
            $pageArray['endTime'] = date('Y-m-d');
        }
        $channelReturnStatusModel = ChannelReturnStatusModel::getInstance();
        $queryData = array(
            'phone'         => $pageArray['phone'],
            'startTime'     => $pageArray['startTime'],
            'endTime'       => $pageArray['endTime'],
            'channel'       => $pageArray['operatorId'],
            'pid'           => $pageArray['projectId'],
            'receiptStatus' => $pageArray['receiptStatus'],
        );
        $pageAction = \PageAction::getInstance();
        $receiptList = $channelReturnStatusModel->getChannelReturnInfo($queryData, $pageAction->returnPageConfig(50));
        $pageArray['receipList'] = $receiptList;
        $queryData['row'] = true;
        $queryData['count'] = true;
        $receiptListCount = $channelReturnStatusModel->getChannelReturnInfo($queryData);
        $pageArray['showPage'] = $pageAction->showPage($receiptListCount['total'], '/Admin/ChannelReturnStatus/Index');

        // 服务商
        $operatorConfigModel = OperatorConfigModel::getInstance();
        $operatorList = $operatorConfigModel->getOperatorList();

        $operatorArr = array();
        foreach ($operatorList as $operatorKey => $operatorInfo)
        {
            $operatorArr[$operatorInfo['id']] = $operatorInfo['provider_name'];
        }
        $pageArray['allOperatorList'] = $operatorArr;

        $operatorArr = array();
        foreach ($operatorList as $operatorKey => $operatorInfo)
        {
            $operatorArr[$operatorInfo['id']] = $operatorInfo;
            if ($operatorInfo['status'] == 1)
            {
                $pageArray['OperatorList'][] = $operatorInfo;
            }
        }

        // 项目
        $projectModel = \Admin\ProjectSecondModel::getInstance();
        $projectList = $projectModel->getProjectList();
        $projects = array();
        foreach ($projectList as $projectInfo)
        {
            if ($projectInfo['status'] == 1)
            {
                $projects[$projectInfo['pid']] = $projectInfo['project_name'];
            }
        }
        $pageArray['projectList'] = $projects;
        loadView('Admin/channel_return_status.tpl.html', $pageArray);
    }

    public function actionMulti()
    {
        $pageArray = array();

        $pageArray['startTime'] = $this->Filter->getPost('startTime');
        $pageArray['endTime'] = $this->Filter->getPost('endTime');
        $pageArray['status'] = $this->Filter->getPost('status');
        if (empty($pageArray['startTime']) || empty($pageArray['endTime']))
        {
            $pageArray['startTime'] = date('Y-m-d');
            $pageArray['endTime'] = date('Y-m-d');
        }
        $ChannelReturnStatusModel = ChannelReturnStatusModel::getInstance();
        $queryData = array(
            'startTime' => $pageArray['startTime'],
            'endTime'   => $pageArray['endTime'],
        );
        $PageAction = \PageAction::getInstance();
        $receipList = $ChannelReturnStatusModel->getMultiChannelReturnInfo($queryData, $PageAction->returnPageConfig(50));
        foreach($receipList as $key => $val)
        {
            $receipList[$key]["shortPhone"] = substr($val["phone"], 0, 50);
            $receipList[$key]["illegalShortPhone"] = substr($val["illegal_phone"], 0, 50);
        }

        $pageArray['receipList'] = $receipList;
        $queryData['row'] = true;
        $queryData['count'] = true;
        $receipListCount = $ChannelReturnStatusModel->getMultiChannelReturnInfo($queryData);
        $pageArray['showPage'] = $PageAction->showPage($receipListCount['total'], '/Admin/ChannelReturnStatus/Multi');

        $OperatorConfigModel = OperatorConfigModel::getInstance();
        $OperatorList = $OperatorConfigModel->getOperatorList();
        $operatorArr = array();
        foreach ($OperatorList as $OperatorKey => $OperatorInfo)
        {
            $operatorArr[$OperatorInfo['id']] = $OperatorInfo['provider_name'];
        }
        $pageArray['operatorList'] = $operatorArr;

        loadView('Admin/channel_return_multi_status.tpl.html', $pageArray);
    }
}
