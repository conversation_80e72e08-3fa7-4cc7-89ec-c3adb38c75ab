<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=GBK">
        <script type="text/javascript" src="/js/datepicker/WdatePicker.js"></script>
        <title>最近登录IP日志</title>
        <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
        <link rel="stylesheet" href="/css/v3/top.css">
        <link rel="stylesheet" href="/css/v3/zhanghao.css">
        <script src="/js/jquery.min.js"></script>
    </head>
    <body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}        
<div class="mainWrap">
    
		<!--面包屑导航 开始-->
        <div class="location">位置：<a href="#" target="_blank">登录注册IP管理</a>  &gt; <span>最近登录IP日志</span></div>
        <!--面包屑导航 结束-->
        
        <!--Tab菜单 开始-->
        <div class="TabMenu mt10">
        	<a href="/houtai_gl/suspect/ips" >可疑登录IP</a>
            <a href="/houtai_gl/suspect/regips">可疑注册IP</a>
            <a href="/houtai_gl/login_ip_log" class="currTabMenu">最近登录IP日志</a>
            <a href="/houtai_gl/limit_ip/reg_limit_ip_list/black">注册IP黑名单管理</a>
            <a href="/houtai_gl/limit_ip/reg_limit_ip_list/white">注册IP白名单管理</a>
        </div>
        <!--Tab菜单 结束-->
        
        <p class="Explanation mt10">各个域名下最近访问登录的用户IP日志</p>
        
        <div class="selectTab mt10">
            {{foreach from=$pageArray.allowDomains item=domain}}
                <a href="/houtai_gl/login_ip_log?domain={{$domain}}">{{$domain}}日志</a><i class="iLine"></i>
            {{/foreach}}
        </div>
    
        <div class="contentWrap">
            <!--table数据列表 开始-->            
            <table cellspacing="0" cellpadding="0" class="tabMod forFixed">
                <tbody>
                	<tr>
                        <th width="300">IP</th>
                        <th>登录时间</th>
                    </tr>
                    {{foreach from=$pageArray.ipLogs item=ipLogs key=ip}}
                        
                        {{if $ipLogs|@count > 1}}
                            {{foreach from=$ipLogs item=ipLog key=key}}
                                {{if $key == 0}}
                                    <tr>
                                        <td rowspan='{{$ipLogs|@count}}' valign="top"><a href="/houtai_gl/suspect/passids?ip={{$ip}}" target="_blank">{{$ip}}</a></td>
                                        <td>{{$ipLog}}</td>
                                    </tr>
                                {{else}}
                                    <tr>
                                        <td>{{$ipLog}}</td>
                                    </tr>
                                {{/if}}
                            {{/foreach}}
                        {{else}}
                            <tr>
                                <td>{{$ip}}</td>
                                <td>{{$ipLogs.0}}</td>
                            </tr>
                        {{/if}}
                        
                    {{/foreach}}
            	</tbody>
            </table>
            <!--table数据列表 结束-->        
        </div><!--contentWrap end-->
    
	</div>        
        <script>
        $('.navList a').each(function (){
            if (  $(this).attr('href') == '/houtai_gl/suspect/ips' )
            {
                $(this).addClass('select');
            }
        });
    </script>
    </body>
</html>