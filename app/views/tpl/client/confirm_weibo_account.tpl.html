<!DOCTYPE HTML>
<html>
<head>
    <meta charset="gb2312" />
    <title>2345网址导航用户中心-登录</title>
    <style type="text/css">
        body{ font:12px/1.5 \5FAE\8F6F\96C5\9ED1,\5b8b\4f53;background:#fff; color:#333;}
        html,body,p,dl,dt,dd,table,td,th,input,img,form,div,span,ul,ol,li,h1,h2,h3,h4,h5,h6,select,fieldset,fieldset,input,button,sub,sup,textarea{margin:0;padding:0; }
        table {border-collapse:collapse; border-spacing:0;}
        h1,h2,h3,h4,h5,h6 {font-size:100%; font-weight:normal;}
        iframe,img{ border:0 none;}
        img{ vertical-align:middle;}
        em,i{font-style: normal;}
        ul,li,ol{list-style:none outside none;}
        .clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
        .clearfix{*zoom:1;}
        a{ text-decoration:none;color:#666}
        a:hover{ text-decoration:underline;color:#f30}
        :focus{ outline:0;}
        .clear{ clear:both; overflow:hidden; font-size:0; height:0;line-height:0;}
        .mt10{ margin-top:10px;}
        .mt90{ margin-top:90px;}

        .main { margin: 0 auto; width: 640px;}
        .m-boxA { height:296px; padding: 12px 20px; background-color: #fff; *zoom: 1; }
        .m-boxA-hd { position: relative; height: 38px; line-height: 38px; border-bottom: 1px solid #eeeeee; *zoom: 1; }
        .m-boxA-hd .more { position: absolute; right: 0; color: #338ad1; }
        .m-boxA-hd .more:hover { color: #ff3300; }
        .m-boxA-tit { float: left; margin-top: -2px; font-size: 14px; border-bottom: 2px solid #338ad1; }
        .m-boxA-bd { padding: 12px 0 0; }

        .weiboTxt{ text-align:center; font-size:18px; color:#666; font-family:\5FAE\8F6F\96C5\9ED1, Microsoft YaHei; line-height:46px;}
        .weiboTxt a,.shelvesTxt a{ color:#166cbb;}
        .shelvesTxt{ font-size:18px; color:#666; font-family:\5FAE\8F6F\96C5\9ED1, Microsoft YaHei; margin-top:40px;}
        .shelvesTit{ height:46px;}
        .shelvesTit .m-boxA-tit{ font-size:18px; color:#666; font-family:\5FAE\8F6F\96C5\9ED1, Microsoft YaHei; margin-top:0px; border:0; padding-bottom:5px;}
        .shelvesForm .form-field{ color:#666;}

        /*m-form*/
        .m-form .form-item { position: relative; margin: 15px 0 0 0; padding: 0 0 0 112px; line-height: 32px; font-size: 14px; *zoom: 1; }
        .m-form .form-item-radio .piece { margin-right: 8px; cursor: pointer; color: #666666; }
        .m-form .form-item-radio .ipt_radio { margin-right: 5px; }
        .m-form .form-item-top { padding-left: 0; }
        .m-form .form-field { position: absolute; left: 0; width: 108px; text-align: right; padding-right: 4px; }
        .m-form .form-tips { margin-left: 7px; color: #999999; }
        .m-form .form-tips-error { color: #ff6600; }
        .m-form .form-tips .icon-error { margin-right: 8px; }
        .m-form .yzmCon { display: inline-block; margin-right: 10px; width: 148px; font-family:Arial; font-weight:bold;}
        .m-form .form-pwd-tips { color: #ff3f00; }
        .m-form .form-pwd-tips i { display: inline-block; vertical-align: middle; margin: 0 2px; width: 17px; height: 4px; overflow: hidden; border: 1px solid #ff3f00; }
        .m-form .form-pwd-tips i.on { background-color: #ff3f00; }
        .m-form .form-pwd-tips em { margin-left: 5px; vertical-align: middle; }
        .m-form .form-pwd-tips-2 { color: #ffc600; }
        .m-form .form-pwd-tips-2 i { border-color: #ffc600; }
        .m-form .form-pwd-tips-2 i.on { background-color: #ffc600; }
        .m-form .form-pwd-tips-3 { color: #4dc214; }
        .m-form .form-pwd-tips-3 i { border-color: #4dc214; }
        .m-form .form-pwd-tips-3 i.on { background-color: #4dc214; }
        .m-form .form-item-cur .ipt_radio { background-position: -5px -31px; }
        .m-form .form-item-cur .ipt_txt { color: #333333; border-color: #499fe6; }
        .m-form .goPrev { margin-left: 10px; color: #2371c8; }
        .m-form .goPrev:hover { color: #ff3300; }
        .btn-blueA { display: inline-block; vertical-align: middle; width: 112px; height: 31px; line-height: 31px; background-color: #338ad1; border: 1px solid #186cb1; text-align: center; color: #ffffff; font-size: 14px; cursor: pointer; -webkit-border-radius: 3px; -moz-border-radius: 3px; border-radius: 3px; }
        .btn-blueA:hover { text-decoration: none; color: #ffffff; background-color: #499fe6; border-color: #2a84ce; }

        /*form ele*/
        .ipt_txt { width: 246px; padding: 4px 12px; height: 22px; line-height: 22px; font-size: 12px; border: 1px solid #c6ced6; color: #333333; vertical-align: middle; font-family: \5b8b\4f53; -moz-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; -webkit-box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; box-shadow: 1px 1px 2px rgba(204, 204, 204, 0.5) inset; }
        .ipt_txt.ipt_defa { color: #999999; }
        .ipt_txt.ipt_txt_error { border-color: #ff6600; }
        .ipt_txt.ipt_txt_cur { color: #333333; border-color: #499fe6; }

        .ipt_radio { display: inline-block; vertical-align: middle; width: 16px; height: 16px; overflow: hidden; background-position: -5px -5px; }
        .ipt_radio_cur { background-position: -5px -31px; }

        .m-selectA { display: inline-block; vertical-align: middle; *display: inline; *zoom: 1; border: 1px solid #d9d9d9; width: 200px; background-position: 0 -165px; background-repeat: repeat-x; height: 30px; line-height: 30px; margin-right: 12px; color: #848484; position: relative; width: auto; min-width: 94px; z-index: 3; font-size: 12px; cursor: pointer; }
        .m-selectA .text { min-width: 40px; padding: 0 6px; border-right: 1px solid #ededed; float: left; text-align: center; cursor: pointer; }
        .m-selectA .holder { width: 40px; height: 30px; border-left: 1px solid #fff; overflow: hidden; position: relative; font-size: 0; cursor: pointer; float: left; }
        .m-selectA .option { position: absolute; width: 100%; _width: expression(this.parentNode.offsetWidth-2); background-color: #fbfbfb; height: 155px; border: 1px solid #d9d9d9; margin: -1px 0 0 -1px; top: 31px; left: 0; overflow: auto; }
        .m-selectA .option li { padding: 0 6px; height: 30px; line-height: 30px; overflow: hidden; border-bottom: 1px solid #eee; }
        .m-selectA .option li a { color: #666; display: block; }
        .m-selectA .option li a:hover { color: #ff4800; text-decoration: none; }
        .m-selectA .arrow-btm { width: 13px; height: 7px; background-position: -186px -115px; position: absolute; top: 12px; left: 15px; }
    </style>
</head>
<body style="background:#fff;">
<div class="main">

    <div class="m-boxA mt10">
        <div class="m-boxA-hd shelvesTit">
            <h2 class="m-boxA-tit">2345帐号不再支持微博登录，检测到你已经设置过：</h2>
        </div>
        <div class="m-boxA-bd">
            <div class="ucfnBox">
                <div class="m-form shelvesForm">
                    {{foreach key=key item=item from=$pageArray.binded}}
                    <div class="form-item">
                        <span class="form-field">{{$item.name}}：</span>
                        <strong class="yzmCon">{{$item.value}}</strong>
                    </div>
                    {{/foreach}}
                    <p class="shelvesTxt">请使用上述方式登录</p>
                </div>
            </div>
        </div>
    </div>

</div>

<div style="display: none">
    <script type="text/javascript" src="//web.50bangzh.com/js/userc2345"></script>
</div>

</body>
</html>
