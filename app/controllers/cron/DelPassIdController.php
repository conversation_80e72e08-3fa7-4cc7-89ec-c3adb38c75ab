<?php

includeBase('CronController');

class DelPassIdController extends CronController
{
    private $delTimeLength = 3600 * 3;

    /**
     * actionIndex
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $logger = $this->getLogger('delAutoIncrPassId');
        $logger->info('开始删除...');
        $memberModel = loadModel('member');
        $memberModel->delPassIdByTime($this->delTimeLength);
        $logger->info('删除结束');
        echo '删除结束';
    }
}
