<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/15
 * Time: 15:52
 */
namespace Crontab;

use Controller;
use Service\Func\ServiceFunc;
use Service\Service;
use Service\Swoole\Tool\Encoding;

class SmsServiceController extends Controller
{
    /**
     * 开启swoole
     * @param string $ip ip
     * @param string $port 端口
     * @param string $call 本地服务
     * @return void
     */
    public function actionStart($ip = '', $port = '', $call = 'local')
    {
        $status = '';
        if ($call == 'server')
        {
            list($ip, $port, $status) = ServiceFunc::getAllowLocalIp($ip, $port);
        }
        if (!empty($ip) && !empty($port))
        {
            (new Service())->start($ip, $port);
        }
        else
        {
            switch ($status)
            {
                case 1:
                    echo $status . PHP_EOL;
                    break;
                case null:
                    break;
                default:
                    echo 'fail' . PHP_EOL;
            }
        }
    }

    /**
     * 重启服务
     * -
     * @param string $ip  ip
     * @param string $port 端口号
     * @param string $call 本地服务
     * @return void
     */
    public function actionReload($ip = '', $port = '', $call = 'local')
    {
        if ($call == 'server')
        {
            list($ip, $port) = ServiceFunc::getStartIp($ip, $port);
        }
        if (!empty($ip) && !empty($port))
        {
            $service = new Service();
            $service->reload($ip, $port);
        }
        else
        {
            echo 'fail';
            echo PHP_EOL;
        }
    }

    /**
     * 关闭服务
     * -
     * @param string $ip  ip
     * @param  string $port 端口号
     * @param string $call 本地服务
     * @return void
     */
    public function actionShutdown($ip = '', $port = '', $call = 'local')
    {
        if ($call == 'server')
        {
            list($ip, $port) = ServiceFunc::getStartIp($ip, $port);
        }
        if (!empty($ip) && !empty($port))
        {
            $service = new Service();
            $service->shutdown($ip, $port);
        }
        else
        {
            echo 'fail';
        }
    }

    /**
     * swoole监控
     * -
     * @return void
     */
    public function actionMonitorSwoole()
    {
        //todo 进程自检
        $ipList = ServiceFunc::regServiceList();
        $service = new Service();
        foreach ($ipList as $ipInfo)
        {
            list($ip, $port) = explode(':', $ipInfo);
            $status = ServiceFunc::hasRegServiceIp($ip, $port);
            //如果当前是激活状态
            if ($status == 1)
            {
                $serviceStatus = $service->status($ip, $port);
                if ($serviceStatus['status'] == 1)
                {
                    $msg = $ip . ':' . $port;
                    //todo  连接数监控
                    $connection = $service->getConnectionCount($ip, $port);
                    var_export($msg . Encoding::transcoding('当前的连接数：', 'utf-8') . json_decode($connection, true)['data']['count']) . PHP_EOL;
                }
                else
                {
                    var_export($serviceStatus);
                    $msg = $ip . ':' . $port . '未能连接上';
                    echo Encoding::transcoding($msg, 'utf-8') . PHP_EOL;
                    ServiceFunc::sendWx($msg);
                }
            }
        }
    }
}
