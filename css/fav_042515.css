/* title */
.wrap_tit{height:34px; line-height:34px; background:#00a0e3; position:relative; z-index:5}
.wrap_tit h2{margin-left:10px; color:#fff; font-weight:100; font-size:18px; display:inline-block; width:230px; float:left;}
.tool_menu{ width:525px;float:left;}
.tool_menu a.t_btn{ display:inline-block; height:22px; line-height:22px;color:#fff; padding:1px 9px; float:left; margin:5px 5px 0; position:relative;}
.tool_menu a:hover.t_btn{ background:#00abf5; border:1px solid #66cdf9; padding:0 8px; text-decoration:none;}
.tool_menu a.t_btn ins.new{ display:inline-block; width:16px; height:16px; overflow:hidden; background:url(../images/dian.png) no-repeat; position:absolute; top:-1px; right:-6px;}
.tool_menu a:hover.t_btn ins.new{ top:-2px; right:-7px;}
.moredata{ display:inline-block; position:relative; z-index:5; float:left}
.moredata_btn{ width:26px; height:22px; display:inline-block; background:url(../images/fav_bgs.png) 0 0 no-repeat; float:left;margin:6px 5px 0;}
a:hover.moredata_btn{background-color:#00abf5; border:1px solid #66cdf9; margin:5px 4px 0 }
.moredata .d_list{ position:absolute; z-index:99; top:30px; left:4px; border:1px solid #bababa; background:#fff; padding:3px; width:129px; box-shadow:0px 2px 7px -1px #b9b9b9;}
.moredata .d_list li a{ display:block; height:26px; line-height:26px; padding-left:11px;}
.moredata .d_list li a:hover{ background:#00abf5; color:#fff; text-decoration:none}
.tool_search{width:210px; height:24px;position:absolute; right:10px; top:5px}
.tool_search input{ float:left;}
.sch_txt{ width:143px; height:24px; line-height:24px; padding-left:7px; background:#fff; color:#999; border:0 none; font-size:12px;}
.sch_btn{ width:48px; height:24px; background:#00a0e3; border:1px solid #66cdf9; color:#fff; margin-left:1px; cursor:pointer; font-size:14px;outline: none;}
/* main */
.tree li s,.tree i,.tree li ins.setup,.ico_star{ background:url(../images/fav_bgs.png) no-repeat; vertical-align:middle }
.main{ border:1px solid #e3e3e3;}
.left_nav{ width:229px; background:#f5f5f5; float:left; border-right:1px solid #e3e3e3; padding:5px 0; position:relative;}
.tree{height:470px;overflow:hidden; width:219px;}
.tree .inner{width:100%;display:table;}
.tree li{height:28px; line-height:28px; margin:2px 0;padding-left:16px; font-size:14px; color:#000;white-space:nowrap;word-break:keep-all;cursor:pointer; clear:both; overflow:hidden; vertical-align:middle}
.tree li.cur,.tree li:hover{ background:#e3e3e3;}
.tree i,.tree s,.tree a{vertical-align:middle;display:inline-block;*display:inline;*zoom:1;}
.tree li i{width:9px;height:9px;overflow:hidden;background-position:-89px 0; margin:2px 5px  0 0;}
.tree li .none{background:none;}
.tree li s{margin:2px 5px 0 0 ;width:16px;height:16px;overflow:hidden;background-position:-71px 0;}
.tree li.myfavtit s{ background-position:-35px 0; margin-top:0;}
.tree li.import s{ background-position:-53px 0; margin-top:0;}
.tree li a{white-space:white-space; display:inline-block; height:26px; line-height:26px; vertical-align:top; _vertical-align:middle}
.tree li a span{color:#999; font-size:12px; display:none}
.tree a font{display:inline-block;*display:inline;*zoom:1;white-space:nowrap;word-break:keep-all; font-size:14px;}
.tree li:hover a{text-decoration:none;color:#000;}
.tree li:hover a font{text-decoration:none;}
.tree li ins.setup{ display:inline-block; width:14px; height:14px; line-height:14px; overflow:hidden; text-decoration:none; background-position:0 -23px; margin:-2px 12px 0 12px; cursor:pointer; vertical-align:middle;visibility: hidden; }
.tree li.cur ins.setup{ display:inline-block;visibility: visible;}
/* left  scroll bar */
.scroll_bar{ width:10px; height:480px; background:#f9f9f9; position:absolute; right:0; top:0; overflow:hidden; display:none; z-index:90;}
.scroll_bar .bar{ display:inline-block; position:absolute; top:0; left:0; width:8px; height:100px; background:#d9d9d9; border:1px solid #bababa; cursor:pointer;overflow:hidden; z-index:91;}
.scroll_bar_cross{ height:10px; width:229px;background:#f5f5f5; position:absolute; left:0;bottom:0; overflow:hidden; display:none; z-index:90;}
.scroll_bar_cross .bar{ display:inline-block; position:absolute;bottom:0; left:0; height:10px;widht:200px; background:#e0e0e0;cursor:pointer;overflow:hidden; z-index:91}
/* right_con */
.right_con{ margin-left:229px;background:#fff; overflow:hidden; height:480px; min-width:756px;_width:expression((document.documentElement.clientWidth||document.body.clientWidth)<756?"756px":""); position:relative;}
.right_con_tit{ height:32px; line-height:32px; background:#f9f9f9; border-bottom:1px solid #e3e3e3; color:#999; font-size:14px;}
.right_con_tit .name{ display:block; float:left; width:280px; padding-left:20px; border-right:1px solid #e3e3e3;}
.right_con_tit .website{ display:block; float:left; padding-left:20px;}
/* right list */
.con_bd{ position:relative; display:block;z-index:2}
.con_list{ padding:10px 20px 10px 10px; height:420px; overflow:hidden;font-size:14px;position:relative; z-index:3;}
.con_list dl{ height:35px; line-height:35px;overflow:hidden;}
.con_list dl:hover,.con_list dl.hov{ background:#f2fafe;}
.con_list dd{ display:inline-block; float:left;height:35px;overflow:hidden;}
.con_list .li_name{ width:250px;}
.con_list .li_name img{ float:left; margin:10px 4px 0 10px;}
.con_list .li_mark{ width:20px;}
.con_list .li_mark .ico_star{ display:block; float:left; width:15px; height:15px; background-position:-18px -23px; margin-top:11px; cursor:pointer;}
.con_list .li_mark a:hover.ico_star,.con_list .li_mark .ico_star_cur{ background-position:-35px 0;}
.con_list .li_link{ width:330px; padding-left:20px;}
.con_list .li_link a{color:#999;}
.con_list .li_link a:hover{color:#f60;}
.con_list .li_set{ color:#dce4e8; width:100px;}
.con_list .li_set a{ color:#00a0e3; margin:0 10px}
.con_list .li_set a:hover{ color:#f60;}
/* right scroll bar */
.scroll_bar2{ width:10px; overflow:hidden; background:#f5f5f5; height:480px; position:absolute; right:0; top:0; z-index:90;}
.scroll_bar2 .bar{ display:inline-block; position:absolute; top:0; left:0; width:10px; height:100px; background:#e0e0e0;cursor:pointer;overflow:hidden; z-index:91;}
.scroll_bar2_cross{ height:10px; width:100%;background:#f5f5f5; position:absolute; left:0;bottom:0; overflow:hidden; display:none; z-index:90;}
.scroll_bar2_cross .bar{ display:inline-block; position:absolute;bottom:0; left:0; height:10px;widht:200px; background:#e0e0e0;cursor:pointer;overflow:hidden; z-index:91}
/* pop_box */
.pop_box .pop_tit,.pop_box .pop_tit span,.pop_box .pop_content .btn_area .btn,.pop_box .pop_content .sel_box,.pop_box .pop_content .icon span{background:url(../images/fav2013_4.png) no-repeat 0 0;}
.pop_box .pop_content .drop{background:url(../images/tree_bg_4.png) repeat-y 0 0;}
.pop_box{position:absolute;padding:3px;width:384px;background-color:#a0a0a0;z-index:9999;}
.pop_box .inner{background-color:#fff;width:384px;}
.pop_box .pop_tit{padding:0 10px 0 11px;height:35px;line-height:35px;font-size:14px;color:#4c6370;background-position:0 -230px;background-repeat:repeat-x;}
.pop_box .pop_tit span{float:right;margin-top:10px;width:18px;height:18px;overflow:hidden;display:inline-block;*display:inline;*zoom:1;background-position:-140px -60px;cursor:pointer;}
.pop_box .pop_content{padding:7px 21px;background-color:#fff;position:relative;}
.pop_box .pop_content .field{margin-bottom:15px;}
.pop_box .pop_content .hd{width:314px;margin-bottom:5px;}
.pop_box .pop_content .hd label{font-weight:bold;}
.pop_box .pop_content .inp{padding:0 7px;width:300px;height:22px;font-size:14px;line-height:22px;color:#333;border:1px solid #b9c5cd;}
.pop_box .pop_content .spark_indeed{color:#ff5100}
.pop_box .pop_content .hd a,.pop_box .pop_content .btn_area a{ text-decoration:underline;}
.pop_box .pop_content .hd .help{float:right;}
.pop_box .pop_content .hd .help img{ vertical-align:middle}
.pop_box .pop_content .textarea{padding:3px 7px;width:300px;font-size:14px;line-height:22px; color:#333;border:1px solid #b9c5cd;resize:none;}
.pop_box .pop_content .nomargin{margin-bottom:0;}
.pop_box .pop_content .btn_area{padding:10px 0 4px;}
.pop_box .pop_content .btn_area .btn{margin:0 15px 0 0;padding:0;border:0 none;width:82px;height:25px;text-align:center;line-height:25px;background-position:0 -90px;color:#fff;font-weight:bold;cursor:pointer; vertical-align:middle}
.pop_box .pop_content .drop{position:absolute;left:21px;top:53px;_left:0;width:314px;border:1px solid #b8c4cc;background-color:#fff;z-index:99;}
.pop_box .pop_content .drop li{line-height:25px;height:25px;overflow:hidden;}
.pop_box .pop_content .drop a{padding-left:7px;color:#333;zoom:1;display:block;}
.pop_box .pop_content .drop a:hover{color:#333; text-decoration:none;background-color:#bfe0ed;}
.pop_box .pop_content .drop .keycode{color:#333; text-decoration:none;background-color:#bfe0ed;}
.pop_box .pop_content .sel_box{padding:0 0 0 7px;width:185px;height:22px;font-size:14px;line-height:22px;color:#333;border:1px solid #b9c5cd;background-position:0 -270px;}
.pop_box .pop_content .sel_list{position:absolute;left:21px;top:115px;_left:0;*top:117px;width:192px;border:1px solid #b8c4cc;background-color:#fff;z-index:99;}
.pop_box .move .sel_list{top:53px;}
.pop_box .pop_content .tree{height:225px;}
.pop_box .pop_content .result{padding:11px 0 0 50px;width:250px;}
.pop_box .pop_content .icon{float:left;width:60px;}
.pop_box .pop_content .hint{float:left;width:190px;line-height:22px;}
.pop_box .pop_content .hint h4{font-size:12px;}
.pop_box .pop_content .icon span{width:44px;height:44px;background-position:-90px -60px;display:inline-block;*display:inline;*zoom:1;}
.pop_box .pop_content .hint .btn_area{padding:19px 0 11px;}