<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2021/6/21
 * Time: 13:19
 */

class LogoutAvatarController extends Controller
{
    private static $listApi = [
        'andsjzs' => 'http://zhushou.2345.com/index.php?c=webApi&d=memberState&project=user_center',
    ];

    /**
     * AccountStatController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight()) {
            $this->codeResponse(- 401, "", "限制IP，无访问权限！");
        }
    }

    /**
     * 手助注销获取头像的特殊接口
     */
    public function actionIndex($mid, $passid)
    {
        if (empty(self::$listApi[$mid])) {
            $this->codeResponseNew(404, array('url' => ""), '');
        }
        $prefix = "http://";
        if (isHttps()) {
            $prefix = "https://";
        }
        $avatarHost = $prefix . $_SERVER['HTTP_HOST'];
        $defaultUrl = $avatarHost . '/pic/avatar/default_v2.jpg';// 给一个默认地址
        $time = time();
        $sign = md5("i7VeourGHjxOwsXN" . $passid . $time);
        $url = self::$listApi[$mid] . "&passid={$passid}&timestamp={$time}&sign={$sign}";
        if (dev::isDevelopment()) {
            $url = dev::getDevDomain($url);
        }
        $response = http_get($url);
        $dataDecode = json_decode($response, true);
        if (!empty($dataDecode) && $dataDecode["code"] == 200) {
            list($avatarUrl, $isDefaultAvatar) = \Service\Avatar\Avatar::getTransferAvatar($mid, $passid, "big");
            if ($isDefaultAvatar) {
                $this->codeResponseNew(200, array('url' => $avatarUrl), '获取头像成功！');
            } else {
                if (!empty($dataDecode["data"])) {
                    $responseData = $dataDecode["data"];
                    list($path, $query) = explode("?", $avatarUrl);
                    $fileName = basename($path);
                    if (preg_match('^init_', $fileName)) {
                        $avatarUrl = $defaultUrl;
                    } else {
                        if (dev::isDevelopment()) {
                            $fileName = str_replace("DEV_", "", $fileName);
                        }
                        list($upMid, $upTime) = explode("_", $fileName);
                        $logoutTime = !empty($responseData["updated_at"]) ? strtotime($responseData["updated_at"]) : strtotime($dataDecode["data"]["created_at"]);
                        //                        $logoutTime = 1790203466;
                        if ($upTime < $logoutTime) {
                            $avatarUrl = $defaultUrl;
                        }
                    }
                }
                $this->codeResponseNew(200, array('url' => $avatarUrl), '获取头像成功！');
            }
        } else {
            $this->codeResponseNew(200, array('url' => $defaultUrl), '获取头像成功！');
        }
    }

}