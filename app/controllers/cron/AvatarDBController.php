<?php

includeBase('CronController');

class AvatarDBController extends CronController
{
    private $prefix = '';
    private $count = '';

    public function __construct()
    {
        parent::__construct();
        global $argv;
        $this->prefix = $argv[2];
        $this->count = $argv[3];
    }

    /**
     * 新建昵称/头像主表
     */
    public function actionNicknameAvatar()
    {
        $tableModel = loadModel('UserBase');
        $result = $tableModel->createTable('user_nickname_avatar', $this->prefix, $this->count);

        if (!$result) {
            $logData = [
                'level' => 'error',
                'dir_type' => 'avatar_db',
                'type' => 'error_create',
                'content' => 'user_nickname_avatar表创建失败',
            ];
            loadAction('Warning')->record($logData);
        }
    }

    /**
     * 新建passid昵称映射表
     */
    public function actionNicknameAvatarMap()
    {
        $tableModel = loadModel('UserBase');
        $result = $tableModel->createTable('user_nickname_map', $this->prefix, $this->count);

        if (!$result) {
            $logData = [
                'level' => 'error',
                'dir_type' => 'avatar_db',
                'type' => 'error_create',
                'content' => 'user_nickname_map表创建失败',
            ];
            loadAction('Warning')->record($logData);
        }
    }
}