"use strict";

var ua = navigator.userAgent.toLowerCase();
var isIE = ua.indexOf("msie") > -1;
var safariVersion;
if (isIE) {
    safariVersion = ua.match(/msie ([\d.]+)/)[1];
}

window.onload = function () {
    $("#mainBox").draggable({
        containment: 'parent', drag: throttle(function () {
            setCropper();
            setPreview();
        }, 16, { leading: true, trailing: false }) });
    document.onselectstart = new Function('event.returnValue=false;');
    document.ondragstart = new Function('event.returnValue=false;');

    var cropBoxElem = document.getElementById('cropBox');
// 裁剪框内相关元素
    var mainBoxElem = document.getElementById("mainBox"); // 裁剪框
    var mainBoxJsHack = document.getElementById('mainBoxJsHack');
    /* 拖拽的小点 */
    var rightDownEle = document.getElementById('right-down');
    var cropBoxClient = getPosition(cropBoxElem);

    var scroll = getScroll();
    var maxX = cropBoxClient.left + cropBoxElem.clientWidth;
    var maxY = cropBoxClient.top + cropBoxElem.clientHeight;

    window.onscroll = function () {
        scroll = getScroll();
    };

    var ifKeyDown = false; // 鼠标按下事件


    /* ie8一下存在bug */
    rightDownEle.onmousedown = function (e) {
        e = e || window.event;
        stopPropagation(e);
        ifKeyDown = 1;
    };

    document.onmouseup = function () {
        ifKeyDown = 0;
    };

    /* 加节流进行优化 */
    document.onmousemove = throttle(function (e) {
        e = e || window.event;
        if (ifKeyDown) {
            rightDown(e);
            setCropper();
            setPreview();
        }
    }, 16, { leading: true, trailing: false });

    function rightDown(e) {
        var clientX = e.clientX,
            clientY = e.clientY;

        var x = clientX + scroll.scrollLeft;
        var y = clientY + scroll.scrollTop;
        if (y > maxY || x > maxX) {
            return;
        }

        var heightBefore = mainBoxElem.offsetHeight; // 裁剪框变化前的高度
        var mainY = getPosition(mainBoxElem).top; // 裁剪框相对于屏幕上边的距离

        var addHeight = y - heightBefore - mainY;
        var offsetHeight = mainBoxElem.offsetHeight,
            offsetWidth = mainBoxElem.offsetWidth;

        var height = offsetHeight + addHeight;
        var width = offsetWidth + addHeight;
        $(mainBoxElem).css({
            width: width,
            height: height
        });
    }

    /* 剪切框中应该显示的位置 */
    function setCropper() {
        var offsetTop = mainBoxElem.offsetTop,
            offsetLeft = mainBoxElem.offsetLeft,
            offsetWidth = mainBoxElem.offsetWidth;

        $('#mainBox .crop_box_image img').css({
            marginLeft: picSize.marginLeft - offsetLeft + 'px',
            marginTop: picSize.marginTop - offsetTop + 'px'
        });
    }

    function setPreview() {
        /* 克隆节点 */
        var preview = $('#mainBox .crop_box_image').clone(true);
        $('.preview-item').html(preview);
        var _$$ = $('#mainBox .crop_box_image')[0],
            clientWidth = _$$.clientWidth,
            clientHeight = _$$.clientHeight;

        var x1 = 200 / clientWidth;
        var x2 = 120 / clientWidth;
        var x3 = 48 / clientWidth;
        $('.preview-item .crop_box_image').css({
            width: clientWidth + 'px',
            height: clientHeight + 'px'
        });
        $('.preview-item-1 .crop_box_image').css({
            zoom: x1,
            '-moz-transform': "scale(" + x1 + ")", // 兼容火狐
            '-moz-transform-origin':'top left'
        });

        $('.preview-item-2 .crop_box_image').css({
            zoom: x2,
            '-moz-transform': "scale(" + x2 + ")",
            '-moz-transform-origin':'top left'
        });

        $('.preview-item-3 .crop_box_image').css({
            zoom: x3,
            '-moz-transform': "scale(" + x3 + ")",
            '-moz-transform-origin':'top left'
        });
    }

    function getPosition(node) {
        var left = node.offsetLeft;
        var top = node.offsetTop;
        var parent = node.offsetParent;
        while (parent != null) {
            left += parent.offsetLeft;
            top += parent.offsetTop;
            parent = parent.offsetParent;
        }
        return {"left": left, "top": top};
    };

    function getScroll() {
        var top = document.documentElement.scrollTop || document.body.scrollTop;
        var left = document.documentElement.scrollLeft || document.body.scrollLeft;
        return {
            scrollTop: top,
            scrollLeft: left
        };
    }

    function stopPropagation(e) {
        if (e.stopPropagation) {
            e.stopPropagation();
        } else {
            e.cancelBubble = true;
        }
    }

    function autoSize(element) {
        var _element$ = element[0],
            clientHeight = _element$.clientHeight,
            clientWidth = _element$.clientWidth;
        console.log('高', clientHeight, '宽', clientWidth);
        $('.crop_box_image img').attr({width: '', height: ''});
        $('.crop_box_image img').css({width: '', height: '', marginTop: 0, marginLeft: 0});
        if (clientHeight > clientWidth) {
            var scale = 300 / clientHeight;
            var marginLeft = (300 - clientWidth * scale) / 2;
            $(element).css({
                height: '300px',
                width: clientWidth * scale + 'px',
                marginLeft: marginLeft + 'px',
                marginTop: 0
            });
            $('.crop_box_image img').attr({'height': 300, width: ''});
        } else {
            var scale = 300 / clientWidth;
            var marginTop = (300 - clientHeight * scale) / 2;
            $(element).css({
                width: '300px',
                marginTop: marginTop + 'px'
            });
            $('.crop_box_image img').attr({'height': '', width: 300});
        }
        return {
            marginLeft: marginLeft || 0,
            marginTop: marginTop || 0
        };
    }

    $('.a-uplod span').on('click', function () {
        console.log(1);
        $('.a-uplod input').click();
    });

    $('.a-uplod input').on('change', function (e) {
        if (safariVersion != 'undefined' && safariVersion <= 9) {
            alert('请升级到IE9以上版本');
        } else {
            var file = new FileReader();
            file.readAsDataURL(e.target.files[0]);
            file.onload = function (e) {
                $('.crop-images').attr('src', e.target.result);
                $('.crop-images').css({width: '', height: '', marginTop: 0, marginLeft: 0});
                $('#cropBox img').attr('src', e.target.result);
                $('.crop_box_image img').attr({width: '', height: ''});
                $('.crop_box_image img').css({width: '', height: '', marginTop: 0, marginLeft: 0});

                $('#cropBox img').one('load', function () {
                    setTimeout(function () {
                        picSize = autoSize($('.crop-images'));
                        setCropper();
                        setPreview();
                    }, 100);
                });
            };
        }
    });

    /* 初始化操作 */
    var picSize = autoSize($('.crop-images'));
    setCropper();
    setPreview();


    function throttle(cb, wait, options) {
        var previous = 0;
        var timer = void 0,
            that = void 0,
            args = void 0;
        if (!options) options = {};

        var later = function later() {
            previous = options.leading === false ? 0 : Date.now();
            timer = null;
            cb.apply(that, args);
            if (!timer) {
                that = args = null;
            }
        };

        var throttled = function throttled() {
            var now = Date.now();
            if (!previous && options.leading === false) {
                previous = now;
            }
            var remaining = wait - (Date.now() - previous);
            that = this;
            args = arguments;

            if (remaining <= 0 || remaining > wait) {
                if (timer) {
                    clearTimeout(timer);
                    timer = null;
                }
                previous = now;
                cb.apply(that, args);
                if (!timer) {
                    that = args = null;
                }
            } else if (!timer && options.trailing !== false) {
                timer = setTimeout(later, remaining);
            }
        };
        throttled.cancel = function () {
            clearTimeout(timer);
            previous = 0;
            timer = null;
        };
        return throttled;
    }
};