<?php
/**
 * Class ContentAuditAction
 * 用户昵称审核：
 * 1.昵称阿里云盾同步接口审核
 */

use Service\ContentAudit\AliyunAudit;

class ContentAuditAction extends Action
{
    // 审核上传返回信息
    // TODO 现在与imageuploadAction中的状态码耦合，需要数字对上，后期解耦
    const CONT_CHECK_FAIL = 6;  // 检查失败
    const CONT_AUDIT_PASS = 3;  // 审核通过
    const CONT_AUDIT_BLOCK = 2; // 审核不通过(包括接口调用失败)
    const CONT_AUDIT_WAIT = 1;  // 待审核
    const CONT_AUDIT_FAIL = 0;   // 审核接口请求失败

    // 服务商 - 需要与实现类同名
    const AUDIT_SERVER_ALI = 'aliyun';

    // 内容审核静态对象
    /** @var \Service\ContentAudit\ContentAuditInterface $auditObj */
    protected static $auditObj;

    /**
     * 文本审核
     *
     * @param string $mid     项目id
     * @param string $content 待审核内容
     * @param string $bizType 审核规则
     *
     * @return int
     * @throws Exception
     */
    public static function textAudit (string $mid, string $content)
    {
        $auditResult = self::CONT_CHECK_FAIL;
        $paramA = self::projectConfig($mid);
        $timeStart = mSecTime();
        list($result, $msg) = self::$auditObj->textAudit($content, $paramA);
        $timeUsed = mSecTime() - $timeStart;

        // 不同实现类的结果匹配
        if (self::$auditObj instanceof AliyunAudit) {
            $auditResult = self::aliTextSuggestionMap($result, $mid, $timeUsed);
        }

        return $auditResult;
    }

    /**
     * 单个图片同步审核
     *
     * @param string $mid    项目id
     * @param string $url    图片可访问地址
     * @param string $dataId 阿里api一次请求用于区分多个数据源的id
     *
     * @return array (状态, taskId, 返回信息)
     * @throws Exception
     */
    public static function imageSyncAudit (string $mid, string $url, string $dataId = null)
    {
        $auditResult = self::CONT_CHECK_FAIL;
        $paramA = self::projectConfig($mid);
        if (!empty($dataId)) {
            $paramA['dataId'] = $dataId;
        }
        $timeStart = mSecTime();
        list($result, $taskId, $infoS) = self::$auditObj->imageSyncAudit($url, $paramA);
        $timeUsed = mSecTime() - $timeStart;

        // 不同实现类的结果匹配
        if (self::$auditObj instanceof AliyunAudit) {
            $auditResult = self::aliImageSuggestionMap($result, $mid, $timeUsed, 'sync');
        }

        return [$auditResult, $taskId, $infoS];
    }

    /**
     * 图片异步审核
     *
     * @param string $mid    项目id
     * @param string $url    图片可访问地址
     * @param string $dataId 阿里api一次请求用于区分多个数据源的id
     *
     * @return array (状态, taskId, 返回信息)
     * @throws Exception
     */
    public static function imageAsyncAudit (string $mid, string $url, string $dataId = null)
    {
        $auditResult = self::CONT_CHECK_FAIL;
        $paramA = self::projectConfig($mid);
        if (!empty($dataId)) {
            $paramA['dataId'] = $dataId;
        }
        $timeStart = mSecTime();
        list($result, $taskId, $infoS) = self::$auditObj->imageAsyncAudit($url, $paramA);
        $timeUsed = mSecTime() - $timeStart;

        // 不同实现类的结果匹配
        if (self::$auditObj instanceof AliyunAudit) {
            $auditResult = self::aliImageSuggestionMap($result, $mid, $timeUsed, 'async');
        }

        return [$auditResult, $taskId, $infoS];
    }

    /**
     * 项目设置
     *
     * @param string $mid 项目id
     *
     * @return array 项目配置
     * @throws Exception
     */
    private static function projectConfig(string $mid)
    {
        // TODO 目前项目对oss和内容审核配置没有区分，后期有区分了在此设置
        $configName = 'default';
        // 现在获取默认的配置
        $auditConfig = Config::get('audit')[$configName];
        $className = 'Service\\ContentAudit\\' . ucfirst(self::AUDIT_SERVER_ALI) . 'Audit';
        self::$auditObj = new $className();
        self::$auditObj->init($auditConfig);

        // 组装参数
        $paramA = [
            'mid' => $mid,
            'callbackDomain' => $auditConfig['callbackDomain'],
            'callbackSeed' => $auditConfig['callbackSeed'],
        ];
        // 先读取项目自有配置，没有的话读取common，没有common的话就不设置
        $commonConfig = 'common';
        $paramList = [
            'productTextBizType' => 'textBizType',
            'productImageBizType' => 'imageBizType',
            'productImageScenes' => 'scenes',
        ];
        foreach ($paramList as $configMapName => $configName) {
            if (!empty($auditConfig[$configMapName])) {
                if (isset($auditConfig[$configMapName][$mid])) {
                    $paramA[$configName] = $auditConfig[$configMapName][$mid];
                } elseif (isset($auditConfig[$configMapName][$commonConfig])) {
                    $paramA[$configName] = $auditConfig[$configMapName][$commonConfig];
                }
            }
        }

        return $paramA;
    }

    /**
     * 阿里内容审核结果映射
     *
     * @param string $result   结果
     * @param string $mid      项目名
     * @param string $type     类型
     * @param int    $usedTime 使用时间，用于埋点日志
     * @param string $source   数据来源
     *
     * @return string 映射规则结果
     */
    public static function aliSuggestionMap(string $result, string $mid, string $type, int $usedTime, string $source)
    {
        $auditResult = self::CONT_CHECK_FAIL;
        if ('text' === $type) {
            switch ($result) {
                case self::$auditObj::SUGGESTION_PASS:
                    $auditResult = self::CONT_AUDIT_PASS;
                    break;
                case self::$auditObj::SUGGESTION_BLOCK:
                    $auditResult = self::CONT_AUDIT_BLOCK;
                    break;
                case self::$auditObj::SUGGESTION_REVIEW:
                    // 文本检查人工认为通过，线上策略应不存在人工，出现人工日志warning提示
                    $logLevel = 'warning';
                    $auditResult = self::CONT_AUDIT_PASS;
                    break;
                // 文本检查接口调用失败认为不通过，出现失败日志error提示
                case 'false':
                // 文本检查接口返回新字段，日志error提示
                default:
                    $logLevel = 'error';
                    $auditResult = self::CONT_AUDIT_FAIL;
            }
        }

        if ('image' === $type) {
            switch ($result) {
                case self::$auditObj::SUGGESTION_PASS:
                    $auditResult = self::CONT_AUDIT_PASS;
                    break;
                // 人工审核，审核不过均为待审核
                case self::$auditObj::SUGGESTION_BLOCK:
                case self::$auditObj::SUGGESTION_REVIEW:
                    $auditResult = self::CONT_AUDIT_WAIT;
                    break;
                // 检查接口调用失败认为不通过，出现失败日志error提示
                case 'false':
                // 检查接口返回新字段，日志error提示
                default:
                    $logLevel = 'error';
                    $auditResult = self::CONT_AUDIT_FAIL;
            }
        }

        // 审核日志埋点上传日志统一
        $logData = [
            'mid' => $mid,
            'class' => __CLASS__,
            'method' => __METHOD__,
            'line' => __LINE__,
            'level' => $logLevel ?? 'info',
            'used_time' => $usedTime,
            'source' => $source,
            'dir_type' => 'p_ContentAuditAction',
            'log_type' => 'log_point_' . $type . '_audit_' . ($result === false ? 'false' : $result),
        ];
        loadAction('Warning')->record($logData);

        return $auditResult;
    }

    /**
     * 阿里文本内容审核结果映射
     *
     * @param string $result   结果
     * @param string $mid      项目名
     * @param int    $usedTime 使用时间，用于埋点日志
     *
     * @return string 映射规则结果
     */
    private static function aliTextSuggestionMap(string $result, string $mid, int $usedTime)
    {
        return self::aliSuggestionMap($result, $mid, 'text', $usedTime, 'sync');
    }

    /**
     * 阿里图片内容审核结果映射
     *
     * @param string $result   结果
     * @param string $mid      项目名
     * @param int    $usedTime 使用时间，用于埋点日志
     * @param string $source   数据来源
     *
     * @return string 映射规则结果
     */
    private static function aliImageSuggestionMap(string $result, string $mid, int $usedTime, string $source)
    {
        return self::aliSuggestionMap($result, $mid, 'image', $usedTime, $source);
    }
}