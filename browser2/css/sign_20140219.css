@charset "gb2312";
body,p,dl,dt,dd,table,td,th,input,img,form,div,span,ul,ol,li,h1,h2,h3,h4,h5,h6,select,input,sub,sup{margin:0;padding:0;}
body{
  font-family: Arial;
  font-size: 12px;
  color: #111;
}
*html{background-image:url(about:blank);background-attachment:fixed;}
img,iframe{border:none;}
ul,li,ol{list-style:none;}
img{vertical-align:middle;}
input{ outline:none;}
em,b,i,strong,cite,sub,sup{font-style: normal;}
a{
  color:#235291;
  text-decoration:none;
  outline:none;
  blr:expression(this.onFocus=this.blur());
}
a:hover{
  text-decoration:none;
  color:#fd5151;
}
.clearfix:after{content:".";display:block;height:0;clear:both;visibility:hidden;}
.clearfix{zoom:1;}

.main {
  width: 480px;
  background-color: #f6f9fe;
}
.banner {
  background-image: url(../images/sign_banner.png);
  background-repeat: no-repeat;
  height: 80px;
}
.box{
  padding-bottom: 27px;
  padding-top: 36px;
  position: relative;
}
.box_agree{
  padding-left: 37px;
  padding-bottom: 36px;
  padding-top: 26px;
  position: relative;
}
.box_retry{
  background-image: url(../images/retry_banner.png);
  background-repeat: no-repeat;
  height: 370px;
  width: 480px;
  background-color: #FFFFFF;
}
.box_retry p {
  padding-top: 300px;
  padding-left: 140px;
  _padding-left: 120px;
}

.popup {
  background-color: #fff5d7;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #efe3bf;
  line-height: 22px;
  height: 22px;
  position: absolute;
  width: 480px;
  top: 0px;
  left: 0px;
  text-align: center;
  color: #8f7132;
}
.box ul {
  border: 1px solid #aec6e2;
  width: 198px;
  line-height: 21px;
  position: absolute;
  left: 150px;
  top: 65px;
  background-color: #FFFFFF;
  height:auto !important;
  _height:expression(this.scrollHeight > 105 ? "105px" : "auto");
  max-height: 105px;
  overflow-x: hidden;
  overflow-y: scroll;
}
.box ul .name {
  color: #666;
  height: 21px;
  padding-right: 4px;
  padding-left: 4px;
}
.box ul .name_h {
  background-color: #779ad2;
  color: #FFFFFF;
  height: 21px;
  padding-right: 4px;
  padding-left: 4px;
  position: relative;
  font-size: 12px;
}


.input0,.input1,.input2 {
  height: 28px;
  line-height: 28px;
  border: 1px solid #aec6e2;
  padding-left: 4px;
  padding-right: 4px;
  color: #666666;
  font-size: 12px;
}

.input0 {
  width: 164px;
}
.input1 {
  width: 190px;
}
.input2 {
  width: 57px;
}
.check1 {
  vertical-align: middle;
  _margin: -2px -1px 0 -4px;
}
.textarea1 {
  line-height: 18px;
  border: 1px solid #aec6e2;
  padding-left: 4px;
  padding-right: 4px;
  color: #666666;
  font-size: 12px;
  height: 250px;
  width: 350px;
}

.txt1{
  font-size: 14px;
  color: #515d6e;
}
.txt2{
  font-size: 12px;
  color: #515d6e;
}
.name_h img {
  display: block;
  position: absolute;
  right: 7px;
  top: 5px;
}

.code img {
  margin: 0 8px;
}
.sign_btn {
  height: 33px;
  width: 74px;
  display: block;
  line-height: 33px;
  text-align: center;
  background-color: none;
  background: none;
  background: url(../images/sign_btnbg.png) no-repeat;
  background-image: url(../images/sign_btnbg.png);
  background-repeat: no-repeat;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: bold;
  margin-left: 50px;
}
.sign_btn:hover {
  background-image: url(../images/sign_btnbg_h.png);
  background-repeat: no-repeat;
  color: #FFFFFF;
}
.agree_btn {
  height: 33px;
  width: 74px;
  display: block;
  line-height: 33px;
  text-align: center;
  background-image: url(../images/sign_btnbg.png);
  background-repeat: no-repeat;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: bold;
  margin-left: 174px;
}
.agree_btn:hover {
  background-image: url(../images/sign_btnbg_h.png);
  background-repeat: no-repeat;
  color: #FFFFFF;
}
.retry_btn {
  height: 33px;
  width: 74px;
  display: block;
  line-height: 33px;
  text-align: center;
  background-image: url(../images/sign_btnbg.png);
  background-repeat: no-repeat;
  font-size: 14px;
  color: #FFFFFF;
  font-weight: bold;
  float: left;
  margin-left: 18px;
}
.retry_btn:hover {
  background-image: url(../images/sign_btnbg_h.png);
  background-repeat: no-repeat;
  color: #FFFFFF;
}
.h_sign{
  height: 20px;
  _height: 18px;
}
.h_reg{
  height: 15px;
  _height: 13px;
  font-size: 5px;
}
.h_reg2{
  height: 12px;
  _height: 10px;
  font-size: 5px;
}

.syn_small{
  padding-top: 25px;
}
.syn_small a{
  background-color: transparent;
  background-image: url(../images/sprites_20130806.png);
  _background-image:url(../images/sprites_20130806.gif);
  background-repeat: no-repeat;
  padding-left: 20px;
  display:inline-block;
  height:16px;
  line-height:16px;
  *zoom:1;
  *position:relative;
  _margin-top:40px;
}
.syn_small .sina{
  background-position:0 1px;
  margin-left: 100px;
}
.syn_small .qq{
  background-position:0 -50px;
  margin-left:30px;
}
.syn_small .taobao{
  background-position:0 -350px;
  margin-left:30px;
  display:none;
}

.sign_success_hd{
  height:81px;
  background: url(../images/sign_success_hd.png) repeat-x;
}
.sign_success_hd .wp{
  width:990px;
  margin:0 auto;
  position:relative;
}
.sign_success_hd .logo{
  width:270px;
  height:81px;
  background-image:url(../images/sign_success_logo.png);
}
.sign_success_hd .home{
  position:absolute;
  top:22px;
  right:20px;
  width:75px;
  height:29px;
  line-height:28px;
  background: url(../images/sprites_20130806.png) 0 -300px no-repeat;
  _background-image:url(../images/sprites_20130806.gif);
  padding-left:12px;
  font-size:14px;
}
.sign_success_co{
  height:322px;
  background: #fff url(../images/sign_success_co.png) repeat-x;
}
.sign_success_co .wp{
  width:380px;
  margin:0 auto;
  padding-top:20px;
}
.sign_success_co h1{
  color:#449ccd;
  font-size:24px;
  font-weight:bold;
  font-family:"Microsoft Yahei";
  text-align:center;
  height:48px;
  line-height:48px;
  padding-left:54px;
  background-image:url(../images/sprites_20130806.png);
  _background-image:url(../images/sprites_20130806.gif);
  background-repeat:no-repeat;
  margin-bottom:30px;
}
.sign_success_co h1.qq_big{
  background-position:20px -200px;
}
.sign_success_co h1.sina_big{
  background-position:20px -100px;
}
.sign_success_co h1.taobao_big{
  background-position:26px -394px;
}
.sign_success_co h3{
  color:#666;
  font-size:14px;
  height:28px;
  line-height:28px;
  font-weight:normal;
  margin-bottom:20px;
  text-align:center;
}
.sign_success_co p{
  color:#666;
  font-size:12px;
  margin-bottom:40px;
}
.sign_success_co p .check1{
  margin-left:40px;
  *margin-left:32px;
}
.sign_success_co p .sign_btn{
  margin: 0 auto;
}
