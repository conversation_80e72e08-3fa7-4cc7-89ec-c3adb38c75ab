<!DOCTYPE HTML>
<html style="overflow:hidden">
    <head>
        <meta charset="gb2312">
        <title>2345王牌浏览器注册</title>
        <link href="/browser2/css/p_login_20140421.css" rel="stylesheet" type="text/css">
        <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
        <script type="text/javascript">
            window.external.RCCoralSetDlgSize(700, 380);
            function show_ftip(field, msg)
            {
                var className = arguments[2] || 'ftip box_red';
                $("#ftip_" + field).html(msg + '<ins></ins>');
                $("#ftip_" + field).attr("class", className);
                $("#ftip_" + field).show();
            }
            function login(username)
            {
                if (username)
                {
                    window.location.href = "/browser2/sign?ver=3.1&username=" + username;
                }
                else
                {
                    window.location.href = "/browser2/sign?ver=3.1";
                }
            }
            function checkUser(n)
            {
                var username = $.trim($("#username").val());
                if (username == '可用邮箱作为您的2345帐号') {
                    username = '';
                }
                if (username.length < 2)
                {
                    show_ftip('username', '用户名最少2个字符');
                    return false;
                }
                if (username.length > 24)
                {
                    show_ftip('username', '用户名请不要超过24个字符');
                    return false;
                }
                if (/[^\u4E00-\u9FA5\w_@\.]/.test(username))
                {
                    show_ftip('username', '请输入汉字，字母，数字，或邮箱地址');
                    return false;
                }
                $.ajax('/api/check', {
                    type: "POST",
                    data: 'type=username&username=' + username,
                    async: false,
                    success: function(response) {
                        if (response == 1)
                        {
                            err = 1;
                        }
                        else if (response == 2)
                        {
                            err = 2;
                        }
                        else
                        {
                            err = 0;
                        }
                    }
                });
                if (err == 1)
                {
                    show_ftip('username', '此帐号已被注册，请<a tabindex="-1" href="javascript:void(0);" class="blue" onclick="window.external.RCCoralShowLoginDlg();return false;">登录</a>或重新输入');
                    return false;
                }
                else if (err == 2)
                {
                    show_ftip('username', '这个2345帐号不适合您，换一个吧');
                    return false;
                }
                if (username != username.toLowerCase())
                {
                    show_ftip('username', '登录区分大小写，请牢记您的2345帐号', 'ftip box_green');
                }
                if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username) && n == 1)
                {
                    $("#email").val(username);
                }
                return true;
            }
            function checkPass()
            {
                var pass = $("#password").val();
                if (pass.length < 6)
                {
                    show_ftip('password', '密码最少6个字符');
                    return false;
                }
                if (pass.length > 16)
                {
                    show_ftip('password', '密码最多16个字符');
                    return false;
                }
                if (pass == $('#username').val())
                {
                    show_ftip('password', '密码不能与2345帐号一致，请重新输入');
                    return false;
                }
                if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
                {
                    var score = 0;
                }
                else
                {
                    var score = passwordGrade(pass);
                }
                if (score <= 10)
                {
                    show_ftip('password', '<p>密码强度</p><ul><li class="s1">弱</li></ul>', 'ftip box_red pwd_status');
                    $('#pwd_strength').val(1);
                    return false;
                }
                return true;
            }
            function checkPwdStrength()
            {
                var pass = $("#password").val();
                if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
                {
                    var score = 0;
                }
                else
                {
                    var score = passwordGrade(pass);
                }
                if (score <= 10)
                {
                    show_ftip('password', '<p>密码强度</p><ul><li class="s1">弱</li></ul>', 'ftip box_red pwd_status');
                    $('#pwd_strength').val(1);
                }
                else if (score >= 11 && score <= 20)
                {
                    show_ftip('password', '<p>密码强度</p><ul><li class="s2">中</li></ul>', 'ftip box_green pwd_status');
                    $('#pwd_strength').val(2);
                }
                else if (score >= 21 && score <= 30)
                {
                    show_ftip('password', '<p>密码强度</p><ul><li class="s3">强</li></ul>', 'ftip box_green pwd_status');
                    $('#pwd_strength').val(3);
                }
                else
                {
                    show_ftip('password', '<p>密码强度</p><ul><li class="s4">极强</li></ul>', 'ftip box_green pwd_status');
                    $('#pwd_strength').val(4);
                }
            }
            function checkPassSame(pass)
            {
                var first = pass.substring(0, 1);
                var exp = new RegExp('^' + first + '+$');
                if (exp.test(pass))
                {
                    return false;
                }

                if (first == 'a' || first == 'A')
                {
                    f = pass.charCodeAt(0);
                    for (i = 1; i < pass.length; i++)
                    {
                        tmp = pass.charCodeAt(i);
                        if (tmp - f != i)
                        {
                            return true;
                        }
                    }
                    return false;
                }
                return true;
            }
            function passwordGrade(pwd)
            {
                var score = 0;
                var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
                var repeatCount = 0;
                var prevChar = '';
                //check length
                var len = pwd.length;
                score += len > 18 ? 18 : len;
                //check type
                for (var i = 0, num = regexArr.length; i < num; i++) {
                    if (eval('/' + regexArr[i] + '/').test(pwd))
                        score += 4;
                }
                //bonus point
                for (var i = 0, num = regexArr.length; i < num; i++) {
                    if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
                        score += 2;
                    if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
                        score += 2;
                }
                //deduction
                for (var i = 0, num = pwd.length; i < num; i++) {
                    if (pwd.charAt(i) == prevChar)
                        repeatCount++;
                    else
                        prevChar = pwd.charAt(i);
                }
                score -= repeatCount * 1;
                return score;
            }
            function checkRepass()
            {
                if ($("#repassword").val() == "") {
                    show_ftip('repassword', '重复密码不能为空');
                    return false;
                }
                if ($("#repassword").val() != $("#password").val())
                {
                    show_ftip('repassword', '两次输入密码不一致');
                    return false;
                }
                return true;
            }
            function checkEmail()
            {
                email = $.trim($("#email").val());
                if (email == "" || email == '输入邮箱作为您的2345帐号')
                {
                    show_ftip('email', '请输入邮箱');
                    return false;
                }
                if (!/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(email))
                {
                    show_ftip('email', '您输入的邮箱格式不正确');
                    return false;
                }
                $.ajax('/api/check', {
                    type: "POST",
                    data: 'type=email&email=' + email,
                    async: false,
                    success: function(response) {
                        if (response == 1) {
                            err = 1;
                        } else {
                            err = 0;
                        }
                    }
                });
                if (err)
                {
                    show_ftip('email', '此邮箱已被注册，请换一个');
                    return false;
                }
                return true;
            }
            function checkValidate()
            {
                var val = $.trim($("#validate").val());
                if (val == '')
                {
                    show_ftip('validate', '请输入4位数字或结果');
                    return false;
                }
                $.ajax('/api/check', {
                    type: "POST",
                    data: 'type=validate&val=' + val,
                    async: false,
                    success: function(response) {
                        if (response == 1) {
                            err = 0;
                        } else {
                            err = 1;
                        }
                    }
                });
                if (err)
                {
                    show_ftip('validate', '您输入的验证码错误');
                    $('#checkImg').attr("src", '/randCaptcha.php?' + new Date());
                    return false;
                }
                return true;
            }
            function checkAll(n)
            {
                if (!checkUser(2))
                {
                    $("#username").focus();
                    return false;
                }
                if (!checkPass())
                {
                    $("#password").focus();
                    return false;
                }
                if (!checkRepass())
                {
                    $("#repassword").focus();
                    return false;
                }
                if (!checkEmail())
                {
                    $("#email").focus();
                    return false;
                }
                if (!checkValidate())
                {
                    $("#validate").focus();
                    return false;
                }
                if (!$("#agree").is(':checked'))
                {
                    show_ftip('agree', '请同意2345服务协议');
                    return false;
                }
                return true;
            }
            function form_submit()
            {
                if (checkAll(1)) {
                    cc("reg_all");
                    $.ajax({
                        type: "POST",
                        url: "/browser2/reg",
                        data: "username=" + $("#username").val() +
                                "&password=" + $("#password").val() +
                                "&email=" + $("#email").val() +
                                "&validate=" + $("#validate").val(),
                        success: function(data) {
                            data = eval("(" + data + ")");
                            switch (data["err"]) {
                                case 0:
                                    cc("reg_success");
                                    window.external.RCCoralOnLogin($("#username").val(), "", 0, "", data["uid"], data["sec"], data['passid'], data['token']);
                                    if ($("#remember").is(':checked')) {
                                        window.external.RCCoralSetLoginUserInfo($("#username").val(), $("#password").val());
                                        window.external.RCCoralSetIsAutoLogin(true);
                                    } else {
                                        window.external.RCCoralSetLoginUserInfo($("#username").val(), "");
                                        window.external.RCCoralSetIsAutoLogin(false);
                                    }
                                    break;
                                case 1:
                                    cc("reg_fail");
                                    show_ftip('agree', data['msg']);
                                    $('#checkImg').attr("src", '/randCaptcha.php?' + new Date());
                                    break;
                                case 2:
                                    cc("reg_fail");
                                    window.external.RCCoralOpenUrl(data["url"], "");
                                    window.external.RCCoralCloseDlg();
                                    break;
                            }
                        }
                    });
                    return false;
                }
            }
            function cc(a)
            {
                var b = arguments,
                        web = "ajax62",
                        a2,
                        i1 = document.cookie.indexOf("uUiD="),
                        i2;
                if (b.length > 1)
                    web = b[1];
                if (i1 != -1) {
                    i2 = document.cookie.indexOf(";", i1);
                    a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
                }
                if (!a2) {
                    a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                    document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
                }
                if (a.length > 0) {
                    var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                    $.getScript(c);
                }
                return true;
            }
        </script>
    </head>
    <body style="overflow:hidden">
        <div class="wrap">
            <div class="lg_box">
                <div class="lg_t">注册2345帐号<a tabindex="-1" href="#" onclick="window.external.RCCoralOpenUrl('http://gaoji.2345.com/help.php?c=4', '');
                return false;" class="tmore ml105">什么是2345帐号?</a></div>
                <div class="lg_bd">
                    <form action="" method="post" onsubmit="form_submit();
                return false;">
                        <input type="hidden" id="pwd_strength"/>
                        <dl>
                            <dt><ins>*</ins>2345帐号</dt>
                            <dd>
                                <span class="input_wp iwp_1">
                                    <input class="ipt_01" type="text" id="username" onfocus="if (this.value == '可用邮箱作为您的2345帐号') {
                    this.value = '';
                }
                select();
                show_ftip('username', '请输入2-20个字符、汉字、字母！');" onblur="$('#ftip_username').hide();
                checkUser(1);
                if (this.value == '') {
                    this.value = '可用邮箱作为您的2345帐号';
                }" value="可用邮箱作为您的2345帐号">
                                    <span class="ftip box_red" id="ftip_username" style="display:none;">请输入2-20个字符、汉字、字母！<ins></ins></span>
                                </span>
                            </dd>
                        </dl>
                        <dl>
                            <dt><ins>*</ins>密&nbsp;&nbsp;&nbsp;&nbsp;码</dt>
                            <dd>
                                <span class="input_wp iwp_2">
                                    <input class="ipt_01" type="password" maxlength="16" id="password" onblur="$('#ftip_password').hide();
                checkPass();" onfocus="select();" onkeyup="checkPwdStrength();"/>
                                    <span class="ftip box_green pwd_status" id="ftip_password" style="display:none;"></span>
                                </span>
                            </dd>
                        </dl>
                        <dl>
                            <dt><ins>*</ins>重复密码</dt>
                            <dd>
                                <span class="input_wp iwp_3">
                                    <input class="ipt_01" type="password" maxlength="16" id="repassword" onblur="$('#ftip_repassword').hide();
                checkRepass();" onfocus="select();"/>
                                    <span class="ftip box_red" id="ftip_repassword" style="display:none"></span>
                                </span>
                            </dd>
                        </dl>
                        <dl>
                            <dt><ins>*</ins>邮箱地址</dt>
                            <dd>
                                <span class="input_wp">
                                    <input class="ipt_01" type="text" id="email" onblur="$('#ftip_email').hide();
                checkEmail()" onfocus="select();"/>
                                    <span class="ftip box_red" id="ftip_email" style="display:none"></span>
                                </span>
                            </dd>
                        </dl>
                        <dl>
                            <dt><ins>*</ins>验 证 码</dt>
                            <dd>
                                <span class="input_wp2">
                                    <input id="validate" maxlength="4" onblur="$('#ftip_validate').hide();
                checkValidate();" onfocus="select();" class="ipt_02" type="text"/>
                                    <span class="ftip box_red" id="ftip_validate" style="display:none"></span>
                                </span>
                                <img id="checkImg" src="/randCaptcha.php" onclick="this.src = '/randCaptcha.php?' + new Date();"/>
                                <a tabindex="-1" href="#" onclick="$('#checkImg').attr('src', '/randCaptcha.php?' + new Date());
                return false;">看不清，换一张</a>
                            </dd>
                        </dl>
                        <dl class="h40">
                            <dt></dt>
                            <dd style="position:relative;">
                                <span class="ftip box_red" id="ftip_agree" style="display:none"></span>
                                <label>
                                    <input class="check_01" type="checkbox" id="agree" checked="checked" onclick="$('#ftip_agree').hide();">我同意
                                    <a tabindex="-1" href="/browser2/licence3.htm">《服务协议》</a>
                                    <a tabindex="-1" href="/browser2/declare3.htm">《隐私政策》</a>
                                </label>
                            </dd>
                        </dl>
                        <dl class="h32"><dt></dt><dd><input class="btn_01" onMouseOver="this.className = 'btn_01_hov'" onMouseOut="this.className = 'btn_01'" type="submit" value="完成注册">
                                <label><input class="check_02" type="checkbox" id="remember" checked="checked">记住密码</label>&nbsp;&nbsp;&nbsp;&nbsp;<a href="#" onclick="window.external.RCCoralShowLoginDlg();
                return false;">返回&gt;&gt;</a></dd></dl>
                    </form>
                </div>
            </div>
        </div>
    </body>
</html>