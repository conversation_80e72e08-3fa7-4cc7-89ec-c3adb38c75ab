<?php

use Octopus\Logger\Registry;

class AutoLoginController extends Controller
{

    public function actionIndex()
    {
        $beginTime = microtime(true);
        $passid = intval($_POST["passid"]);
        if ($passid && $_POST["access"])
        {
            $clientIp = get_client_ip();
            $enforceUseCache = false;
            $isUseRedis = Config::get('isUseRedis');
            if ($isUseRedis && defined('VIPFLN'))
            {
                $redis = RedisEx::getInstance();
                $keyname = "visitTimeLog:" . str_replace(".", "_", $clientIp);
                $visitTimeRange = $redis->lRange($keyname, 0, VIPFLN - 1);
                $visitLen = sizeof($visitTimeRange);
                if ($visitLen > (VIPFLN - 1))
                {
                    $timeRange = $visitTimeRange[0] - $visitTimeRange[VIPFLN - 1];
                    if ($timeRange == 0)
                    {
                        $enforceUseCache = true;
                    }
                }
            }
            $memberModel = loadModel('member');
            $token = $memberModel->getToken($passid, $enforceUseCache);
            if ($token && md5($token . TOKENKEY) == $_POST["access"])
            {
                $uid = $memberModel->getUid($passid);
                echo json_encode(array('sts' => 1, 'sec' => get_sec_browser($uid)));
                return;
            }
        }
        $endTime = microtime(true);
        $timeDifference = $endTime - $beginTime;
        if ($timeDifference > 1)
        {
            $info = array(
                "passid" => $_POST["passid"],
                "beginTime" => $beginTime,
                "endTime" => $endTime,
                "timeDifference" => $timeDifference
            );
            Registry::runtime()->info($_SERVER['REQUEST_URI'], $info);
        }
        echo json_encode(array('sts' => 0, 'msg' => mb_convert_encoding('自动登录帐号、密码验证失败，请重新输入！', 'UTF-8', 'GBK')));
    }

}
