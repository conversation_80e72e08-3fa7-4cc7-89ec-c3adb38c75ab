<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台-redis管理</title>
    <link rel="stylesheet" href="/style/css/global.css">
    <link href="/style/css/jquery.treetable.css" rel="stylesheet" type="text/css" />
    <link href="/style/css/jquery.treetable.theme.default.css" rel="stylesheet" type="text/css" />
</head>
<style type="text/css">
    table.treetable{font-size: 1.2em;line-height: 1.25;}
    table.treetable tr.selected{background-color: #7aafda;}
    .detailItem{background-color: #fffff2;}
    #tree{word-break:break-all;word-wrap:break-all;}
    .detailTbl{width:100%;border:black solid 1px;}
    .detailTbl th{width:50%;border:black solid 1px;text-align:center;}
    .detailTbl td{border:black solid 1px;}
    #goto{font-size:15px;}
</style>
<body>
<div class="mainWrap">
    <!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a> > <span>redis管理</span></div>
    <!--面包屑导航 结束-->

    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a class="currTabMenu" href="javascript:">redis查询</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->

    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
        <form action="" method="post" id="submitFormId">
            <div class="dataWrap ml10 clearfix">
                <span>Redis Pattern：</span>
            </div><!--dataWrap end-->
            <span class="input-sp" style="width:300px;"><input type="text" autocomplete="off" id="goto" value="{{$pageArray.lastKey}}"/></span>
            <a id="goto_btn" class="styleBtn_s styleBtnBlue left ml10 searachQuery" onclick="location.href='/Admin/KeyManage/Index?key='+jQuery('#goto').val();">查询</a>
            <a class="styleBtn_s styleBtnBlue ml10 left expandAll" onclick="is_expandAll=true;jQuery('#tree').treetable('expandAll');is_expandAll=false;return false;">全部展开</a>
            <a class="styleBtn_s styleBtnBlue ml10 left expandAll" onclick="jQuery('#tree').treetable('collapseAll'); return false;">全部折叠</a>
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->

    <!--主体内容 开始-->
    <div class="contentWrap">
        <table id="tree" class="treetable">
            <thead>
            <tr>
                <th width="50%">Key</th>
                <th width="10%">Type</th>
                <th width="10%">TTL</th>
                <th width="10%">Action</th>
            </tr>
            </thead>
            <tbody>
            {{foreach from=$pageArray.tree item=item key=key}}
            <tr data-tt-id="{{$item.index}}"
                class="{{if $item.folder}}folderItem{{else}}fileItem{{/if}}"
                {{if !$item.folder}}title="{{$item.fullKey}}" {{/if}}
            {{if $item.parent neq 0}}data-tt-parent-id="{{$item.parent}}"{{/if}}>
            <td {{if $item.folder eq 2}}colspan="4" {{/if}}><span class="{{if $item.folder eq 1}}folder{{else}}file{{/if}}">{{$item.key}}</td>
            {{if $item.folder neq 2}}
            <td>{{$item.type}}</td>
            <td>{{$item.ttl}}</td>
            <td></td>
            {{/if}}
            </tr>
            {{if !$item.folder}}
        {{/if}}
        {{/foreach}}
            </tbody>
        </table>
    </div>
    <script src="/style/js/jquery-1.8.3.min.js"></script>
    <script src="/style/js/jquery.treetable.js"></script>
    <script>
        var is_expandAll = false;
        $("#tree").treetable({
            expandable: true,
            onNodeCollapse: function() {
                var node = this;
                if($(node.row.context).hasClass('fileItem')) {
                    $("#tree").treetable("unloadBranch", node);
                }
            },
            onNodeExpand: function() {
                var node = this;
                if($(node.row.context).hasClass('detailItem'))
                {
                    return;
                }
                if($(node.row.context).hasClass('fileItem'))
                {
                    if(is_expandAll)
                    {
                        return false;
                    }
                    $.ajax({
                        async: false,
                        url: "/Admin/KeyManage/QueryKeyValue?parent=" + node.id + "&key=" + node.row.context.title
                    }).done(function(html) {
                        var rows = $(html).filter("tr");
                        $("#tree").treetable("loadBranch", node, rows);
                    });
                }
                $(".detailItem td .indenter").hide();
            }
        });
        $("#tree tbody").on("click", "tr", function(e) {
            if(!$(this).hasClass("detailItem"))
            {
                if($(this).hasClass("selected") && $(this).hasClass("expanded"))
                {
                    $("#tree").treetable("collapseNode", $(this).attr('data-tt-id'));
                }
                else if($(this).hasClass("selected") && $(this).hasClass("collapsed"))
                {
                    $("#tree").treetable("expandNode", $(this).attr('data-tt-id'));
                }
            }
            $(".selected").not(this).removeClass("selected");
            $(this).addClass("selected");
        });
    </script>
</body>
</html>