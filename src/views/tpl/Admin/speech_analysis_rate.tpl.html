
<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>语音通道-到达率统计</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
  <script src="/style/js/global.js"></script>
  <script src="/style/js/tableFixed.js"></script>
  <script src="/style/date/WdatePicker.js"></script>
</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">语音通道</a>  >  <span>到达率统计</span></div>
    <!--面包屑导航 结束-->
    
    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="" method="post" id="queryFromId">
            <div class="dataWrap clearfix">
        	<em>日期：</em>
            <div class="dataList w130">
            	<div class="getTime"><input type="text" value="{{$pageArray.startTime}}" autocomplete="off" name="startTime" onclick="WdatePicker()"/><i>日历icon</i></div>
            </div>
            </div><!--dataWrap end-->
            <span class="dataTxt">至</span>
            <div class="dataWrap clearfix">
                <div class="dataList w130">
                	<div class="getTime"><input type="text" value="{{$pageArray.endTime}}" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                </div>
            </div><!--dataWrap end-->
        	<a href="/Admin/SpeechAnalysis/ArrivalRate?timeTag=1" class="styleBtn_s styleBtnBlue left ml10">近七天</a>
            <a href="/Admin/SpeechAnalysis/ArrivalRate?timeTag=2" class="styleBtn_s styleBtnBlue left ml10">本月</a>
            <a href="/Admin/SpeechAnalysis/ArrivalRate?timeTag=3" class="styleBtn_s styleBtnBlue left ml10">上月</a>
            <div class="dataWrap ml10 clearfix">
            	<em>请求来源：</em>
                <select class="selectMod w90 selectProject" name="from">
                    <option value="0">全部</option>
                    {{foreach from=$pageArray.projectList item=projectInfo}}
                        <option {{if $pageArray.from == $projectInfo.channel}} selected="selected"{{/if}} value="{{$projectInfo.channel}}">{{$projectInfo.desc}}</option>
                    {{/foreach}}
                </select>
            </div>
            
            
            <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 queryFromSubmit" >查询</a>
        
        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    <!--主体内容 开始-->
    <div class="contentWrap">        
        
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th>日期</th>
                        <th></th>
                        <th>总请求数</th>
                        <th>总成功数</th>
                        <th>总送达率%</th>
                        <th>语音请求数</th>
                        <th>语音成功数 </th>
                        <th>语音送达率% </th>
                        <th>短信请求数</th>
                        <th>短信成功数</th>
                        <th>短信送达率%</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th>日期</th>
                <th></th>
                <th>总请求数</th>
                <th>总成功数</th>
                <th>总送达率%</th>
                <th>语音请求数</th>
                <th>语音成功数 </th>
                <th>语音送达率% </th>
                <th>短信请求数</th>
                <th>短信成功数</th>
                <th>短信送达率%</th>
            </tr>
            {{foreach from=$pageArray.speecAanalData key=speecAanalKey item=speecAanalInfo}}
            <tr>
                <td rowspan="{{math x=$speecAanalInfo.projectData|@count equation='x+1'}}">{{$speecAanalKey}}</td>
            </tr>
            {{foreach from=$speecAanalInfo.projectData item=projectAnanl}}
            <tr>
                <td>{{$projectAnanl.projectName}}</td>
                <td>{{$projectAnanl.total}}</td>
                <td>{{$projectAnanl.total_success}}</td>
                <td style="{{if $projectAnanl.total_per < '90'}}color:red;{{/if}}">{{$projectAnanl.total_per}}%</td>
                <td>{{$projectAnanl.v_total}}</td>
                <td>{{$projectAnanl.v_total_success}}</td>
                <td style="{{if $projectAnanl.v_total_per < '90'}}color:red;{{/if}}">{{$projectAnanl.v_total_per}}%</td>
                <td>{{$projectAnanl.s_total}}</td>
                <td>{{$projectAnanl.s_total_success}}</td>
                <td style="{{if $projectAnanl.s_total_per < '90'}}color:red;{{/if}}">{{$projectAnanl.s_total_per}}%</td>
            </tr>
            
            {{/foreach}}
            <tr>
            	<td colspan="2">合计</td>
                <td>{{$speecAanalInfo.heji.total}}</td>
                <td>{{$speecAanalInfo.heji.total_success}}</td>
                <td>{{$speecAanalInfo.heji.total_per}}%</td>
                <td>{{$speecAanalInfo.heji.v_total}}</td>
                <td>{{$speecAanalInfo.heji.v_total_success}}</td>
                <td>{{$speecAanalInfo.heji.v_total_per}}%</td>
                <td>{{$speecAanalInfo.heji.s_total}}</td>
                <td>{{$speecAanalInfo.heji.s_total_success}}</td>
                <td>{{$speecAanalInfo.heji.s_total_per}}%</td>
            </tr>
            
            {{/foreach}}
            
            
        </table>
        <!--table数据列表 结束-->
        
    </div><!--contentWrap end-->
    <!--主体内容 结束-->
</div><!--mainWrap end-->
<script>
$('.queryFromSubmit').click(function (){
    $('#queryFromId').submit();
});
</script>
</body>
</html>