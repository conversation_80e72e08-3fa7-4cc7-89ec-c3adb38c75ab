<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：DefaultController.php
 * 摘    要：子系统（Demo）默认控制器（例子）
 * 作    者：张小虎
 * 修改日期：2013.10.12
 */

namespace Demo;

use Controller;

class DefaultController extends Controller
{

    public function actionIndex()
    {
        echo "demo hello";
    }

    /**
     * User: panj
     * for test online
     * @param string $auth key
     * @return null
     */
    public function actionTest($auth)
    {
        if ($auth != '65b90a86ce75d7e96067b44bd8b96d0b')
        {
            die('access denied!');
        }

        $s = 'hello work';
        $e = \SecurityAction::encrypt($s);
        $d = \SecurityAction::decrypt($e);
        printf("source:%s<br>encryptoed:%s<br>", $d, $e);
        echo $s === $d ? "success" : "failed";
    }

}
