@charset "gb2312";
/* main */
.left{ width:750px; float:left;}
.loginbox{ height:256px;}
.round .lt{ top:-1px;}
.round .rt {right: -1px; top: -1px;}
.loginbox .login_left{ width:390px; float:left;} 
.loginbox .inbox1,.loginbox .btn1, .login_right .enter a,.loginbox .btn2,.box .tit,.box .tit span,.titmore,.hot,.entergame,.tag a,.hotli li em,.cai h3 a.currt,.cai h3,.info_right li,.game2 li a.btn3{background:url(../images/mainbg.png) no-repeat;}

.login_left table{ table-layout:fixed;}
.login_left table th{ width:100px; text-align:right; font-weight:100; height:40px; line-height:40px; padding-right:10px;}
.login_left table td{ height:40px; line-height:40px;}
.login_left h6{ height:30px; display:block; padding:15px 0 0 20px;}
.loginbox .inbox1{ width:169px; padding-left:3px; height:26px; line-height:26px; border:1px solid #dbdbdb;}
.loginbox .wid80{ width:80px; float:left;}
.loginbox span.code img{ float:left; margin:5px 0 0 10px;}
.loginbox .btn1{ width:96px; height:34px; line-height:34px; background-position:0 -28px; font-weight:bold; color:#fff; font-size:14px; border:0; cursor:pointer;}
.loginbox .btn1_hov{ background-position:0 -64px;}
.loginbox .btn2{ border:0; width:90px; height:34px; line-height:34px; cursor:pointer; background-position:0 -100px; }
.loginbox .login_right{ width:310px; height:256px; float:right; background:url(../images/logbg.png) left no-repeat; padding:0 0 0 45px;}
.loginbox .login_right b{ display:block; font-size:14px; padding:20px 0 8px} 
.loginbox .login_right p{ line-height:25px;}
.loginbox .login_right p img{ margin:8px 15px 0 0}
.login_right .enter{ height:30px; line-height:36px;}
.login_right .enter a{ height:26px; line-height:26px; padding-left:30px; display:inline-block; width:90px; margin-left:5px;}
.login_right .enter a.qq{ background-position:0 -438px;}
.login_right .enter a.sina{ background-position:0 -410px; margin-left:1.5em;}

.mt10{ margin-top:10px;}
.box .tit{ height:32px; line-height:32px; background-position:0 -171px; background-repeat:repeat-x; position:relative; }
.box .tit span{ display:inline-block; height:32px; float:left;;}
.box .tit span.titleft{ background-position:0 -137px;width:15px; overflow:hidden;}
.box .tit span.titright{ background-position:right -137px; float:right; padding-right:10px;}
.box .tit span.titright .titmore{ display:inline-block; width:37px; height:21px; line-height:21px; background-position:-129px -113px; color:#eb6600; padding-left:7px; margin:6px 0 0;}
.box .tit h2{ font-weight:100; font-size:14px; color:#515151;}
.box .boxbd{ border:1px solid #d5dfe3; border-radius:0 0 5px 5px; overflow:hidden;}
.imgli{ margin:8px 0;}
.imgli li{ width:235px; padding:5px 0 0 13px; float:left; border-right:1px dotted #e0e0e0; color:#808080; height:178px; overflow:hidden;}
.imgli li.lastli{ border:none;}
.imgli li a{ position:relative; display:inline-block;}
.imgli li a i.tj{ display:inline-block; width:55px; height:53px; background:url(../images/tuijian.png) no-repeat; position:absolute; top:-2px; left:-2px; z-index:9; cursor:pointer;}
.imgli li strong{display:block; height:30px; line-height:30px} 
.imgli li strong a,.imgli li a strong{font-weight:bold; font-size:14px; color:#ff6a00;}
.imgli li strong a.entergame{ width:84px; height:28px; line-height:28px; font-weight:bold; float:right; font-size:12px; background-position:0 -206px; text-align:center; color:#515151; margin:2px 14px 0 0;}
.imgli li strong a:hover.entergame{ color:#f60;}
.imgli li p a{ font-weight:bold;}
.imgli li p em.hot{ display:inline-block; width:22px; height:14px; background-position:-150px -93px; overflow:hidden; margin-left:6px;}
.xiaoyx { padding:15px 0 15px 14px;}
.xiaoyx li{ display:inline;}
.xiaoyx li a{ width:84px; height:84px; overflow:hidden; margin:0 22px 18px 0; float:left; position:relative;}
.xiaoyx li a i.tj{ display:inline-block; width:55px; height:53px; background:url(../images/tuijian.png) no-repeat; position:absolute; top:-2px; left:-2px; z-index:9; cursor:pointer;}
.xiaoyx li.lastli a{ margin-right:0;}
.xiaoyx li a img{ border:1px solid #b5d2ed; padding:1px; margin-bottom:3px;}
.xiaoyx li a:hover img{ border:1px solid #f60;}
.tuanli{ padding:10px 0;}
.tuanli li{ display:inline; width:220px; float:left; padding:0 16px; text-align:center; border-right:1px dotted #e0e0e0; overflow:hidden;}
.tuanli li.lastli{ padding-right:0; border:0;}
.tuanli li a{ display:inline-block; color:#515151;}
.tuanli li a:hover{ color:#f60;}
.tuanli li a h5{ display:block; font-weight:100; font-size:12px; line-height:20px; text-align:left;}
.tuanli li a h5 b{ font-weight:bold;}
.tuanli li a img{ margin:0 auto; display:block; width:189px; height:119px; overflow:hidden;}
.tuanli li h6{ display:block; width:190px; margin:0 auto; height:30px; line-height:30px;color:#ff6a00; font-weight:bold; text-align:left; font-size:16px; text-align:right;} 
.tuanli li h6 span{color:#808080; float:left; font-size:12px;}
.tuanli li h6 span font{ font-size:16px;}

.right{ width:224px; float:right;}
.right .wea{ padding:7px 7px 0; line-height:20px;}
.right .wea .today{ background:#edf6fc; border:1px solid #e4e9ed; height:80px; margin-bottom:5px;}
.right .wea .today h4{ display:block; height:25px; line-height:25px; padding:0 8px; color:#507d9a; border-bottom:1px solid #e5eaee; margin-bottom:8px;}
.right .wea h5{ display:block; height:33px; line-height:33px; border-top:1px dotted #dfdfdf; font-weight:100; margin-top:5px;}
.right .wea h5 a.more{ float:right;}
.right .wea .today em{ width:70px; text-align:center; float:left;}
.right .wea p{ margin-left:5px;}
.fblue{ color:#0e7dcc;}
.fred{ color:#ff6a00;}
.tag{ height:33px; line-height:33px; position:relative; _margin-bottom:-1px; z-index:9;}
.tag a{ color:#515151; padding:0 12px; display:inline-block; background-position:0 -270px; border-right:1px solid #d5dfe3; height:33px; line-height:33px;}
.tag a.curr{ background-position:0 -236px; color:#5093ae; font-weight:bold;}
.hotli{ padding:8px;}
.hotli li{ height:26px; line-height:26px; overflow:hidden; border-bottom:1px dotted #e0e0e0;}
.hotli li.firstimg{ height:80px; line-height:22px;}
.hotli li.firstimg img{ float:left; margin:0 8px 0 0 }
.hotli li.firstimg a{ display:block; color:#0e7dcc; font-weight:bold;}
.hotli li.firstimg a:hover{ color:#f60}
.hotli li.firstimg font{ color:#ff6a00; font-weight:bold; font-size:12px;}
.hotli li a{ color:#333;}
.hotli li a:hover{ color:#f60;}
.hotli li span{ color:#808080; float:right;}
.hotli li em{ display:inline-block; width:22px; height:17px; line-height:17px; background-position:-133px -214px; color:#666;font-size:12px; margin:5px 8px 0 4px; text-align:center; float:left}
.hotli li em.hotbest{ background-position:-108px -214px; font-weight:bold; color:#fff;}
.hotli h6{ display:block; font-weight:100; font-size:12px; text-align:right; height:16px; line-height:16px; padding-top:5px;}
.cai h3{ height:30px; line-height:30px; display:block; background-position:0 -304px; background-repeat:repeat-x; padding-left:4px;}
.cai h3 a{ display:inline-block; width:71px; float:left;font-size:12px; font-weight:100; color:#666; text-align:center;}
.cai h3 a.currt{ color:#5093ae; font-weight:bold; text-decoration:none; background-position:0 -336px;}
.caipiao_r_c{ padding:8px 2px 0 6px;}
.caipiao,.caipiao a{color:#6b7074;}
.caipiao dd li{ clear:both}
.caipiao dd h6{ font-size:12px; font-weight:bold; text-align:center; height:33px; line-height:33px; color:#000; background:#fff}
.caipiao dl dd{ width:250px; height:280px; overflow:hidden; float:left; border-right:1px solid #fff; border-left:1px solid #e5e5e5;}
.caipiao dl dd.nobr{ border-right:none; padding-right:0;}
.kj_detail .kj_box{height:82px;overflow:hidden; background:#fff;line-height:24px;  }
.kj_detail .kj_box2{height:32px; line-height:32px;cursor:pointer}
.kj_box em {float:left; height:24px; width:53px; font-style:normal; display:inline-block;}
.kj_box a{ text-decoration:underline;}
.kj_box a.r_link{float:right; text-decoration:none; color:#f00;}
.kj_box a.r_link:hover{text-decoration:underline;}
.kj_box .name{color:#0e6dbc; font-size:12px; width:48px; font-weight:bold; text-align:left; display:inline-block; float:left;}
.kj_box .qh2,.kj_box .qh{height:32px;margin-right:5px; width:60px; display:inline-block; float:left;line-height:32px;}
.kj_box .qh{height:24px; width:90px; float:left; line-height:24px;}
.kj_box .qh2 span{padding-right:1px;}
.kj_box .qh3 span{padding-right:2px;}
.kj_box .qh4 span{padding-right:5px;}
.kj_box .kj_number {width:215px; height:34px; overflow:hidden; clear:both;}
.kj_box .kj_number .zcbball{width:12px; height:20px; text-align:center; display:inline-block; margin-right:2px; background:#fff; border:1px solid #E5DED4; color:#f00; font:bold 12px/22px Arial; margin-top:5px;}
.kj_box .msee {display:block; margin-right:8px; text-align:right;}
.kj_box .wkj {width:212px;}
.zj_tips{*padding:0 12px 7px 12px}
.r_box_m{padding:8px}
.scroll .aa{width:100px; display:inline-block; color:#0e6dbc;}
.kj_box .qh {width: 90px;}
.kj_box em {width:70px;}
.kj_box .kj_number .redball, .kj_box .kj_number .blueball {margin-top:0}
.red_ball,.blue_ball{display:inline-block; padding:6px 0;}
.redball {background:url(../images/red_ball.gif) no-repeat scroll 0 0 transparent;color: #FFFFFF;display: inline-block;font-size: 14px;font-style: normal;font-weight: bold;height: 21px;line-height: 21px;text-align: center;width: 21px; margin-right:5px;}
.blueball {background: url(../images/blue_ball.gif) no-repeat scroll 0 0 transparent;color: #FFFFFF;display: inline-block;font-size: 14px;font-style: normal;font-weight: bold;height: 21px;line-height: 21px;margin: 0 2px 0 0;text-align: center;width: 21px; margin-right:5px;}
.caipiao_r_c{ clear:both; display:block; height:216px; overflow:hidden}
.caipiao_r_c li{ clear:both; border-bottom:1px dotted #e0e0e0;}
.cp_nav li.last_li{border-right:0;}
.cp_nav li.cur{background:#fff; border-bottom: 0 none;font-weight: bold; margin-bottom:-2px; position:relative; height:31px}
.cp_nav{ border-top:1px solid #fff;}
.cp_nav li{color:#000; font-size:12px;cursor: pointer;float: left;height: 30px;line-height: 30px;text-align: center;width:72px;}
.caipiao_r.kj_box .kj_number {width:205px;}
.caipiao_r .kj_box .qh2,.caipiao_r .kj_box .qh{ width:55px;}
.caipiao_r .kj_box .qh{ width:90px; margin-right:0; }
.caipiao_r .kj_box em{ width:50px;}
.caipiao_r .kj_detail .kj_box{ padding:0 0 0 10px;}
.uinfo{ height:186px; padding:10px;}
.info_left{ width:140px; border-right:1px dotted #e6e5dc; height:180px; text-align:center; float:left;}
.info_left img{ width:84px; height:89px; padding:3px; background:#fff; border:1px solid #e7e7e7;box-shadow:2px 2px 4px #f1f1f1; margin-top:20px;}
.info_left p{ height:30px; line-height:30px;}
.info_mid{ width:290px; padding-left:20px; float:left; line-height:32px;}
.info_mid strong{ font-weight:bold; font-size:14px;}
.info_mid a{ text-decoration:underline}
.info_mid .lv img{ vertical-align:middle; margin:0 0 0 6px;}
.info_right{ width:240px; height:150px; background:#fffdf0; border:1px solid #eae1ad; border-radius:5px; float:left; margin:8px 0 0 3px; padding:10px;}
.info_right li{ height:26px; line-height:26px; padding-left:15px; background-position:-165px -208px;}
.info_right li.linet{ border-top:1px dotted #ebe9e1; }
.info_right li strong{ margin:0 3px; font-weight:bold;}

.gban{ margin-bottom:10px;}
.boxbd .gtip{ color:#808080; font-size:14px; height:80px; margin-top:55px; text-align:center;}
.boxbd .gtip img{ vertical-align:middle}
.boxbd .gtip a{ text-decoration:underline}
.game2 ul{ padding:0 8px 8px;}
.game2 li{ padding:15px 0 15px 18px; height:70px;color:#808080; border-bottom:1px dotted #e0e0e0; line-height:20px;}
.game2 li img{ float:left; margin-right:15px;}
.game2 li strong{ display:block; font-size:14px; font-weight:bold; color:#ff6a00; height:28px; line-height:28px;}
.game2 li strong font{ font-size:12px; color:#333; font-weight:100; margin-left:8px;}
.game2 li a.btn3{ display:inline-block; width:121px; height:39px; background-position:0 -369px;); float:right; margin:15px 30px 0 0;}
.game3{ padding:5px 10px;}
.game3 ul{ height:116px; border-bottom:1px dotted #e0e0e0;}
.game3 ul li{ display:inline; width:100px; float:left; text-align:center; margin-left:3px;}
.game3 ul li a{ display:inline-block; cursor:pointer;}
.game3 ul li a img{ margin-top:10px; display:block;}
.game3 ul li p{ height:20px; line-height:20px;}
.filter {opacity:0.25;filter:alpha(opacity=25); background:#000; height:1000px; width:100%; position:absolute; left:0; top:0; z-index:1000;}/* layer */

.layer{ border:2px solid #b6b6b6; background:#fff; margin:10px auto; width:420px;color:#6b7074; position:absolute; left:50%; top:30%; margin-left:-210px; z-index:9999}
.layer h5{  font-weight:bold; font-size:13px; padding-left:11px;height:29px; line-height:29px;}
.layer h5 b{ font-weight:bold;}
.layer h5 span{ float:right; display:block; height:29px; width:29px;}
.b_close{ background:transparent url(../images/clo.png) no-repeat; height:20px; width:20px; margin-top:4px; cursor:pointer; border:none;}
.layer p .b_close{ float:right; background-position:10px -35px;}
.layer_cnt{ background:#fafaf2; margin:0 3px 3px; font-size:12px; padding:5px;}
/* 20120511 */
#ui_dlc_opencodes{margin-right: 0;width: 94px}
.info_side{ width:210px; float:left;}
.info_side .face{ display:block; text-align:center;}
.info_side .face img{background:#fff;border: 1px solid #E7E7E7;box-shadow: 2px 2px 4px #F1F1F1;height: 89px;margin-top: 20px; padding: 3px; width: 84px;}
.info_side .face p{ height:30px; line-height:30px;}
.info_side ul{ margin:10px 0 0 25px;}
.info_side ul li{ height:32px; line-height:32px; overflow:hidden;}
.info_side ul li img{ vertical-align:middle;}
.info_cnt{ margin:8px 12px 8px 0; border-left:1px dotted #e6e5dc; width:648px; padding-left:10px; float:left;}
.info_cnt h4.bt{ font-size:14px; height:36px; line-height:36px; font-weight:bold; border-bottom:1px solid #e6e6e6; margin:10px 0;}
.info_cnt h4.bt i{ display:inline-block; float:left; width:16px; height:16px; background:url(../images/infobg.png) no-repeat; margin:10px 3px 0 10px;}
.info_cnt h4.bt i.bt02{ background-position:-20px 0;}

.infotable{ table-layout:fixed;}
.infotable th{ width:100px; font-weight:100; text-align:right; height:32px; line-height:32px; padding-right:10px;}
.infotable td{ height:42px; line-height:42px;}
.infotable td a{ text-decoration:underline;}
.radio01{ width:13px; height:13px; overflow:hidden; vertical-align:middle; margin:-2px 2px 0 2px;}
.inbox2{ width:145px; padding-left:5px; height:22px; line-height:22px; border:1px solid #ccc;}
.btn4,.btn4_hov{ border:0; background:url(../images/infobg.png) 0 -20px no-repeat; width:103px; height:36px; line-height:36px; font-size:14px; cursor:pointer; color:#333;}
.btn4_hov{ background-position:0 -59px;}
.box .tit h2 font{ font-size:12px;}
.inbox3{ width:270px; padding:8px 0 8px 10px; height:32px; line-height:32px; font-size:14px; background:url(../images/infobg.png) 0 -99px no-repeat; border:0;}
.codetab{ table-layout:fixed; margin:50px 0 ;}
.codetab th,.codetab td{padding:5px 0; font-size:14px;}
.codetab th{ text-align:right; font-weight:100;}
.codetab td span.tipfont{ font-size:12px; line-height:20px; color:#808080; }
.codetab td span.fred{ color:#ee0000;}
em.safe{background: url("../images/passwordbg.png") no-repeat scroll 0 0 transparent; display: inline-block;    height: 13px;    padding: 2px 3px 4px;    width: 106px; overflow:hidden; vertical-align:middle}
em.safe i{text-align:center;height:13px;line-height:13px;_padding-top:3px;_height:10px;_line-height:10px; display:inline-block; font-style:normal; color:#fff; vertical-align:top; overflow:hidden }
em.safe i.w1{width:26px; background:url(../images/passwordbg.png) no-repeat 0 -19px;}
em.safe i.w2{width:54px; background:url(../images/passwordbg.png) no-repeat 0 -33px;}
em.safe i.w3{width:78px; background:url(../images/passwordbg.png) no-repeat 0 -47px;}
em.safe i.w4{width:106px; background:url(../images/passwordbg.png) no-repeat 0 -61px;}
.set-cnt h2.infonav{ background-position:12px 5px;}
.set-cnt h2.jifennav{ background-position:12px -20px;}
.set-cnt h2.scjnav{ background-position:12px -145px;}
.set-cnt h2.jsbnav{background-position:12px -45px;}
.set-cnt h2.txlnav{background-position:12px -70px;}
.set-cnt h2{ border-bottom:1px solid #e0ebf4; height:26px; line-height:26px; background: url(../images/title_ icon.gif) no-repeat #F1F8FE; margin:1px auto 0; font-weight:bold; font-size:12px; color:#588bb2; background-color:#f1f8fe; clear:both;}
.set-cnt h2 b{color:#2992ec; font-size:13px; padding-right:2em; }
.h2l{ width:34px; height:26px; float:left;}
.h2bg{ height:26px; float:left; text-align:left}
.h2bg b{ font-weight:bold;}
.h2r{ width:14px; height:26px; float:right;}
.d-tx{ float:right;}


/* 20120617 */
.bdemail{ text-align:center; padding:70px 0 180px;}
.bdemail table{ margin:0 auto; table-layout:fixed}
.bdemail table td{ text-align:left;}
.bdemail table .btn5{ width:80px; height:23px; font-size:12px; cursor:pointer;}
.oldemail{text-align:right; padding-right:12px;}
.oldemail font{ font-size:12px; float:left}
.inbox4{ width:210px; padding-left:5px; height:22px; line-height:22px; border:1px solid #ccc;} 
.bdemail td span img{ vertical-align:middle; margin:-3px 3px 0 0}
.w_er{ color:#e92828;}
.inbox_er{ border:1px solid #f9c5c5; background:#fff7f7;}
.checkemail{ padding:70px 40px 100px 170px;}
.checkemailLeft{ width:350px; float:left;}
.checkemail b{ font-weight:bold; font-size:14px; display:block; height:40px; line-height:40px;}
.checkemail ul{ display:block; height:100%; overflow:hidden; background:#f9fafb; border:1px solid #e7ecef; padding:20px 6px 20px 15px; border-radius:7px; margin-bottom:15px;}
.checkemail ul li{ line-height:20px; padding:5px 0}
.checkemail ul li p{ margin-left:18px;}
.checkemail ul li input{ vertical-align:middle; margin:-3px 2px 0 0;}
.checkemailRight{ width:280px; float:left; margin:30px 0 0 50px; line-height:22px;}
.checkemailRight b{ color:#999;}
.jihuo{ font-size:14px; line-height:40px; text-align:center; padding-left:40px;}
/* 20121030 */
.bind{ margin:12px 0 0 39px; border-top:1px solid #e6e6e6; line-height:30px}
.bind b{ display:block; font-weight:bold; font-size:14px; height:20px; line-height:20px; padding-top:10px;}
.bindbox{ background:#fafafa; border:1px solid #eee; padding:0 3px; color:#515151; font-size:14px;} 
.bindbox span{ display:block; height:66px; line-height:66px; overflow:hidden; position:relative}
.bindbox span.dotted{ border-top:1px dotted #bbb;}
.bindbox span img{ float:left; margin:11px 15px 0 5px;}
.bindbox span a{ font-size:14px}
.btn_jiechu,.btn_bangding{ display:inline-block; position:absolute; background:url(../images/bindbgs.png) 0 0 no-repeat; width:73px; height:27px; line-height:27px; color:#fff; font-size:12px; text-align:center; top:22px; right:16px;}
.btn_bangding{ background-position:0 -30px;}
.btn_jiechu:hover,.btn_bangding:hover{ color:#fff;}
.bind_tip{ display:block; height:28px; line-height:28px; background:#fff1c2 url(../images/bindbgs.png) -50px -58px no-repeat; border:1px solid #f2b656; color:#b44f1d; padding-left:27px;}
.userinfo td,.idinfo th{ font-size:12px;}
.userinfo td{line-height:32px; padding:8px 0 0;}
.userinfo th{font-weight:100; font-size:14px; color:#666; text-align:right;height:40px; padding:8px 12px 0 0;}
.userinfo font{color:#e92828; font-family:Verdana, Arial, Helvetica, sans-serif; vertical-align:middle; margin-right:4px;}
.blue{ color:#1989d7;}
.gray{ color:#999; font-size:12px;}
.orange{ color:#ff7404;}
.ipt_01{ height:28px; line-height:28px; border:1px solid #cdcdcd; background:transparent url(../images/bindbgs.png) 0 -119px repeat-x; width:195px; padding-left:5px; font-size:14px;}
.ipt_code{ height:28px; line-height:28px; border:1px solid #cdcdcd; background:transparent url(../images/bindbgs.png) 0 -89px repeat-x; width:80px; padding-left:5px; font-size:14px;}
.no_link{ width:200px; min-height:80px;_height:80px; margin:40px auto;padding:10px 0 0 100px; line-height:24px; background:url(./txz_img.png) no-repeat -155px 0}
.no_link em{font-size:14px; font-weight:bold; font-style:normal}
.no_link span a{color:#1989d7; text-decoration:underline}
.no_link span a:hover{color:#f60; text-decoration:underline}

.user_ok{width:380px; min-height:60px;_height:60px; margin:0px auto;padding:20px 0 0 100px; line-height:24px; font-size:16px; font-weight:bold; background:url(./txz_img.png) no-repeat -155px -122px}
.user_ok span{color:#1989d7}

.password_safety{width:175px; height:19px; line-height:19px;font-size:12px; color:#666; display:inline-block; overflow:hidden;}
.password_safety em{padding:2px 3px 4px; width:106px; height:13px; display:inline-block; background:url(../images/passwordbg.png) no-repeat 0 0; vertical-align:middle}
.password_safety em i{text-align:center;height:13px;line-height:13px;_padding-top:3px;_height:10px;_line-height:10px; display:inline-block; font-style:normal; color:#fff; float:left }
.password_safety em i.w1{width:26px; background:url(../images/passwordbg.png) no-repeat 0 -19px;}
.password_safety em i.w2{width:54px; background:url(../images/passwordbg.png) no-repeat 0 -33px;}
.password_safety em i.w3{width:78px; background:url(../images/passwordbg.png) no-repeat 0 -47px;}
.password_safety em i.w4{width:106px; background:url(../images/passwordbg.png) no-repeat 0 -61px;}
.register .height1 td,.register .height1 th{ height:25px; line-height:25px; padding-top:0; }
.register{ margin-top:15px; clear:both;}
.register th{ font-weight:100; font-size:14px; color:#666; text-align:right;height:40px; padding:8px 12px 0 0;}
.register td{ line-height:32px; padding:8px 0 0;}
.register th font{color:#e92828; font-family:Verdana, Arial, Helvetica, sans-serif; vertical-align:middle; margin-right:4px;}
.register td p{ color:#999;}
.register .code td{ padding:0;}
.pwtip{display:block; color:#e92828; background:url(../images/bindbgs.png) -68px -148px no-repeat; padding-left:8px; height:32px; line-height:32px;}
.btn_cancel{ font-size:14px; margin-left:17px; color:#000;}
.btn_cancel:hover{color:#fd5151}
.info_cnt a{ font-weight:100; font-size:12px; text-decoration:underline}
/* 20121226 */
.fgray{color:#808080;}
.infotable td select{ height:22px; line-height:22px;}
.step{ height:36px; line-height:36px; width:595px; margin-bottom:15px; background:url(../images/stepbg.png) no-repeat; font-size:14px; font-weight:700;color:#666;}
.step .st_1,.step .st_2,.step .st_3,.step .st_4{ display:inline-block;padding-left:10px; float:left}
.step .st_1{ width:138px;}
.step .st_2{ width:150px;color:#fff;}
.step .st_3{ width:150px;}
.step .st_4{}
/* 20130815 */
.titicon_01{display:inline-block; width:16px; height:16px; overflow:hidden; background:url(../images/icon_0815.png) 0 0 no-repeat; margin:9px 3px 0 10px; float:left}
.rgstab01_2 th{font-weight:100; font-size:12px;text-align:right; padding:7px 12px 5px 0;line-height:30px; vertical-align:top;color:#666;}
.tip_bind{ display:block; width:560px; background:#fff1c2; border:1px solid #f2b656; height:28px; line-height:28px; color:#b44f1d; font-size:12px; margin:0 0 5px 15px;}
.tip_bind ins.icon{ display:inline-block; width:14px; height:14px; overflow:hidden; margin:6px 4px 0 9px; float:left; background:url(../images/icon_0815.png) no-repeat -21px 0;}
a.cancel{ color:#000;}
a:hover.cancel{color:#ff4800}
.tip_bind_2{ display:block; height:47px; line-height:47px; background:#fffcf4; border:1px solid #f9ecd5; color:#f00; font-size:14px; margin:10px 0;}
.tip_bind_2 a{ font-weight:700; color:#0e7dcc; text-decoration:underline; font-size:14px;}
.tip_bind_2 a:hover{ color:#f30;}
.tip_bind_2 ins.tip_32{ display:inline-block; width:32px; height:32px; overflow:hidden; background:url(../images/tip_32.png) no-repeat; float:left; margin:7px 7px 0 11px;}
