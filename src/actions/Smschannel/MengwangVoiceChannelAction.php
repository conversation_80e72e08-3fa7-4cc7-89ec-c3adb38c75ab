<?php
namespace Smschannel;
use Action;
use FuncAction;
class MengwangVoiceChannelAction extends SmsChannelAction
{
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }
    
    private static $apiData = array();
    private static $randomCode = '';
    
    public function setApiData($data)
    {
        self::$apiData = $data;
    }
    
    /**
     * 发送短信，适用
     * @param string $phone 手机号
     * @param string $msg 短信内容
     * @return RET_SUCC 发送成功    RET_ERR 发送失败
     */
    public function send($phone, $msg)
    {
        self::$randomCode = \SmsInterface\SmsChannelAction::_getRandom();
        $data = array(
            'userId' => $this->user,
            'password' => $this->pwd,
            'pszMobis' => $phone,
            'pszMsg' =>  self::$randomCode,
            'iMobiCount' => 1,
            'pszSubPort' => '4000002345',
            'MsgId' => microtime(true) * 10000 ,
            'PtTmplId' => '',
            'msgType' => 1
        );
        if (!empty(self::$apiData['voiceType']))
        {
            $data['msgType'] = self::$apiData['voiceType'];
        }
        if ( !empty(self::$apiData['voiceTpl']) )
        {
            $data['PtTmplId'] = self::$apiData['voiceTpl'];
        }
        $errorLogPath = APPPATH . '/logs/mengwang_' . date ('Y-m-d-H').'.log';
        //file_put_contents ($errorLogPath, $this->apiUrl . '?'.http_build_query($data) );
        $response = FuncAction::postTimeout($this->apiUrl ,$data );
        //file_put_contents ($errorLogPath, $response ,FILE_APPEND );
        $response = (array )simplexml_load_string($response);
        //低于15位的为异常
        $this->SetMsgArray($response[0]);
        if (strlen($response[0]) < 15)
        {
            return self::RET_ERR;
        }
        else
        {
            $this->SetMsgId($response[0]);
            return self::RET_SUCC;

        }

    }
    
    public function getRandomCode ()
    {
        return self::$randomCode;
    }
    
    private function SetMsgId($msgId)
    {
        $this->msgId = $msgId;
    }

    private function SetMsgArray($res)
    {
        $code = array(
            1 => '发送成功',
            'DELIVRD'=>'发送成功',
            'MW:0000'=>'上行语音验证码请求参数有空值',
            'MW:0001'=>'上行语音验证码请求参数数据格式错误',
            'MW:0002'=>'用户验证失败',
            'MW:0003'=>'服务器内部错误',
            'MW:0004'=>'网络连接超时',
            'MW:0005'=>'重发失败',
            'MW:0006'=>'一分钟内同一个号码同一个用户同一个验证码 重复订单',
            'MW:0007'=>'用户未绑定语音通道',
            'MW:0008'=>'用户余额不足',
            'MW:0009'=>'没找到用户费率',
            'MW:0010'=>'没找到用户扣费类型',
            'MW:0011'=>'当前不支持语音通知',
            'MW:0012'=>'短信过期',
            'MW:0013'=>'语音模板编号错误',
            'MW:0014'=>'系统正在维护',
            'MW:0015'=>'文本通知语音字数过多（目前限制120个字）',
            'MW:1000'=>'',
            'MW:1001'=>'被叫拒接',
            'MW:1002'=>'外呼失败',
            'MW:1003'=>'金额不为整数',
            'MW:1004'=>'余额不足',
            'MW:1005'=>'数字非法',
            'MW:1006'=>'不允许有空值',
            'MW:1007'=>'枚举类型取值错误',
            'MW:1008'=>'访问IP不合法',
            'MW:1009'=>'手机号不合法',
            'MW:1010'=>'号码不合法',
            'MW:1011'=>'HTTP状态码不等于200',
            'MW:1012'=>'查无数据',
            'MW:1013'=>'手机号码为空',
            'MW:1014'=>'手机号为受保护的号码',
            'MW:1015'=>'登录邮箱或手机号为空',
            'MW:1016'=>'邮箱不合法',
            'MW:1017'=>'密码不能为空',
            'MW:1018'=>'没有测试子账号',
            'MW:1019'=>'金额过大,不要超过12位数字',
            'MW:1020'=>'余额被冻结',
            'MW:1021'=>'余额已注销',
            'MW:1022'=>'通话时长需大于60秒',
            'MW:1023'=>'系统内部错误',
            'MW:1024'=>'应用余额不足',
            'MW:1025'=>'字符长度太长',
            'MW:1026'=>'callId不能为空',
            'MW:1027'=>'日期格式错误',
            'MW:1028'=>'取消回拨失败',
            'MW:1029'=>'请求包头Authorization参数为空',
            'MW:1030'=>'请求包头Authorization参数Base64解码失败',
            'MW:1031'=>'请求包头Authorization参数解码后账户ID为空',
            'MW:1032'=>'请求包头Authorization参数解码后时间戳为空',
            'MW:1033'=>'请求包头Authorization参数解码后格式有误',
            'MW:1034'=>'主账户ID存在非法字符',
            'MW:1035'=>'请求包头Authorization参数解码后时间戳过期',
            'MW:1036'=>'请求地址SoftVersion参数有误',
            'MW:1037'=>'主账户已关闭',
            'MW:1038'=>'主账户未激活',
            'MW:1039'=>'主账户已锁定',
            'MW:1040'=>'主账户不存在',
            'MW:1041'=>'主账户ID为空',
            'MW:1042'=>'请求包头Authorization参数中账户ID跟请求地址中的账户ID不一致',
            'MW:1043'=>'请求地址Sig参数为空',
            'MW:1044'=>'请求token校验失败',
            'MW:1045'=>'主账号sig加密串不匹配',
            'MW:1046'=>'主账号token不存在',
            'MW:1047'=>'应用ID为空',
            'MW:1048'=>'应用ID存在非法字符',
            'MW:1049'=>'应用不存在',
            'MW:1050'=>'应用未审核通过',
            'MW:1051'=>'测试应用不允许创建client',
            'MW:1052'=>'应用不属于该主账号',
            'MW:1053'=>'应用类型错误',
            'MW:1054'=>'应用类型为空',
            'MW:1055'=>'应用名为空',
            'MW:1056'=>'行业类型为空',
            'MW:1057'=>'行业信息错误',
            'MW:1058'=>'是否允许拨打国际填写错误',
            'MW:1059'=>'是否允许拨打国际不能为空',
            'MW:1060'=>'创建应用失败',
            'MW:1061'=>'应用名称已存在',
            'MW:1062'=>'子账户昵称为空',
            'MW:1063'=>'子账户名称存在非法字符',
            'MW:1064'=>'子账户昵称长度有误',
            'MW:1065'=>'子账户clientNumber为空',
            'MW:1066'=>'同一应用下，friendlyname重复',
            'MW:1067'=>'子账户friendlyname只能包含数字和字母和下划线',
            'MW:1068'=>'client_number长度有误',
            'MW:1069'=>'client_number不存在或不属于该主账号',
            'MW:1070'=>'client已经关闭',
            'MW:1071'=>'client充值失败',
            'MW:1072'=>'client计费类型为空',
            'MW:1073'=>'clientType只能取值0,1',
            'MW:1074'=>'clientType为1时，charge不能为空',
            'MW:1075'=>'clientNumber未绑定手机号',
            'MW:1076'=>'同一应用下同一手机号只能绑定一次',
            'MW:1077'=>'单次查询记录数不能超过100',
            'MW:1078'=>'绑定手机号失败',
            'MW:1079'=>'子账号是否显号(isplay)不能为空',
            'MW:1080'=>'子账号是否显号(display)取值只能是0(不显号)和1(显号)',
            'MW:1081'=>'应用下该子账号不存在',
            'MW:1082'=>'friendlyname不能为空',
            'MW:1083'=>'查询client参数不能为空',
            'MW:1084'=>'client不属于应用',
            'MW:1085'=>'未上线应用不能超过100个client',
            'MW:1086'=>'已经是开通状态',
            'MW:1087'=>'子账号余额不足',
            'MW:1088'=>'未上线应用或demo只能使用白名单中号码',
            'MW:1089'=>'测试demo不能创建子账号',
            'MW:1090'=>'校验码不能为空',
            'MW:1091'=>'校验码错误或失效',
            'MW:1092'=>'校验号码失败',
            'MW:1093'=>'解绑失败,信息错误或不存在绑定关系',
            'MW:1094'=>'主叫clientNumber为空',
            'MW:1095'=>'主叫clientNumber未绑定手机号',
            'MW:1096'=>'验证码为空',
            'MW:1097'=>'显示号码不合法',
            'MW:1098'=>'语音验证码位4-8位',
            'MW:1099'=>'语音验证码位4-8位',
            'MW:1100'=>'语音通知类型错误',
            'MW:1101'=>'语音通知内容为空',
            'MW:1102'=>'语音ID非法',
            'MW:1103'=>'文本内容存储失败',
            'MW:1104'=>'语音文件不存在或未审核',
            'MW:1105'=>'号码与绑定的号码不一致',
            'MW:1106'=>'开通或关闭呼转失败',
            'MW:1107'=>'不能同时呼叫同一被叫',
            'MW:1108'=>'内容包含敏感词',
            'MW:1109'=>'语音通知发送多语音ID不能超过5个',
            'MW:1110'=>'呼转模式只能取1,2,3,4',
            'MW:1111'=>'呼转模式为2,4则必须填写forwardPhone',
            'MW:1112'=>'呼转模式为2、4则前转号码与绑定手机号码不能相等',
            'MW:1113'=>'群聊列表格式不合法',
            'MW:1114'=>'群聊呼叫模式只能是1免费,2直拨,3智能拨打',
            'MW:1115'=>'群聊ID不能为空',
            'MW:1116'=>'群聊超过最大方数',
            'MW:1117'=>'群聊ID发送错误',
            'MW:1118'=>'群聊操作失败服务出错',
            'MW:1119'=>'呼转号码不存在',
            'MW:1120'=>'订单号不能为空',
            'MW:1121'=>'订单号不存在',
            'MW:1122'=>'号码释放失败或号码已经自动释放',
            'MW:1123'=>'显手机号必须是呼叫列表中的号码',
            'MW:1124'=>'主被叫不能相同',
            'MW:1125'=>'开通国际漫游禁止回拨呼叫',
            'MW:1126'=>'短信服务请求异常',
            'MW:1127'=>'url关键参数为空',
            'MW:1128'=>'号码不合法',
            'MW:1129'=>'没有通道类别',
            'MW:1130'=>'该类别为冻结状态',
            'MW:1131'=>'没有足够金额',
            'MW:1132'=>'不是国内手机号码并且不是国际电话',
            'MW:1133'=>'黑名单',
            'MW:1134'=>'含非法关键字',
            'MW:1135'=>'该通道类型没有第三方通道',
            'MW:1136'=>'短信模板ID不存在',
            'MW:1137'=>'短信模板未审核通过',
            'MW:1138'=>'短信模板替换个数与实际参数个数不匹配',
            'MW:1139'=>'短信模板ID为空',
            'MW:1140'=>'短信内容为空',
            'MW:1141'=>'短信类型长度应为1',
            'MW:1142'=>'同一天同一用户不能发超过3条相同的短信',
            'MW:1143'=>'模板ID含非法字符',
            'MW:1144'=>'短信模板有替换内容，但参数为空',
            'MW:1145'=>'短信模板替换内容过长，不能超过70个字符',
            'MW:1146'=>'手机号码不能超过100个',
            'MW:1147'=>'短信模板已删除',
            'MW:1148'=>'同一天同一用户不能发超过N条验证码(n为用户自己配置)',
            'MW:1149'=>'短信模板名称为空',
            'MW:1150'=>'短信模板内容为空',
            'MW:1151'=>'创建短信模板失败',
            'MW:1152'=>'短信模板名称错误',
            'MW:1153'=>'短信模板内容错误',
            'MW:1154'=>'短信模板id为空',
            'MW:1155'=>'短信模板id不存在',
            'MW:1156'=>'30秒内不能连续发同样的内容',
            'MW:1157'=>'30秒内不能给同一号码发送相同模板消息',
            'MW:1158'=>'验证码短信参数长度不能超过10位',
            'MW:1159'=>'应用id为空',
            'MW:1160'=>'应用不存在',
            'MW:1161'=>'应用未审核通过',
            'MW:1162'=>'应用不属于该主账户',
            'MW:1163'=>'主账户id为空',
            'MW:1164'=>'主账户不存在',
            'MW:1165'=>'主账户未激活',
            'MW:1166'=>'主账户权限不足',
            'MW:1167'=>'时间戳为空',
            'MW:1168'=>'时间戳格式错误，格式为：yyyyMMddHHmmssSSS',
            'MW:1169'=>'时间戳过期，过期时间为30分钟',
            'MW:1170'=>'验证信息为空',
            'MW:1171'=>'验证信息错误',
            'MW:1172'=>'请勿重复提交，【主账户id+应用id+时间戳】不能重复',
            'MW:1173'=>'系统内部错误',
        );
        if (strlen($res) >= 15)
        {
            $res = 1;
        }
        $this->OperatorMsg = array('codeStatus' => $res, 'codeDesc' => !empty($code[$res]) ?
                $code[$res] : '未知错误');
    }
        
    
    
}
?>