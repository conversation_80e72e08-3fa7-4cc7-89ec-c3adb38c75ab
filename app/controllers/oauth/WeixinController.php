<?php

includeBase('OauthController');

class WeixinController extends OauthController
{

    /**
     * WeixinController constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 跳转到微信二维码, 扫描, 获取参数 code + state
     *
     * @return void
     */
    public function actionIndex()
    {
        $oauthConfig = Config::get('oauth');
        $this->oauthType = 'weixin';
        if (isset($oauthConfig[$_GET['mid']])) {
           $this->oauthConfig = $oauthConfig[$_GET['mid']][$this->oauthType];
        } else {
            $this->oauthConfig = $oauthConfig[$this->oauthType];
        }
      
        $this->authUrl = 'https://open.weixin.qq.com/connect/qrconnect';

        $midParam = "";
        if (isset($_GET['mid']))
        {
            $midParam = "?mid=" . \Common\Utils\Url::getStringParam("mid");
        }
        if (isset($_GET['autoLogin']))
        {
            $midParam .= "&autoLogin=" . intval($_GET['autoLogin']);
        }
        $this->callbackUrl = PASSPORT_HOST . $_GET['callback'] . $midParam;
        if (dev::isDevelopment()) {
            $midParam .= (!empty($midParam) ? "&" : "?") . "rewrite=" . PASSPORT_HOST;
            $this->callbackUrl = PASSPORT_HOST . $_GET['callback'] . $midParam;
        }


        if (!in_array($_GET['callback'], $this->allowCallbacks))
        {
            redirect('/');
        }
        $state = md5(time() . MD5KEY);
        setcookie('redirect_uri', $this->callbackUrl, 0, '/');
        setcookie('oauth_state', $state, 0, '/');

        $params = array(
            "appid"         => $this->oauthConfig['appid'],
            "redirect_uri"  => $this->callbackUrl,
            "response_type" => "code",
            "scope"         => "snsapi_login",
            "state"         => $state,
        );
        redirect($this->authUrl . "?" . http_build_query($params));
    }

    /**
     * 跳转到微信页面获取code, 获取参数 code + state
     * @return void
     */
    public function actionMpOpenId()
    {
        // 仅需 openid, $scope = "snsapi_base";
        $scope = "snsapi_base";
        $this->actionMp($scope);
    }

    /**
     *  跳转到微信页面获取code, 获取参数 code + state
     *
     * @param string $scope scope
     *
     * @return void
     */
    public function actionMp($scope = "snsapi_userinfo")
    {
        $oauthConfig = Config::get('oauth');
        $oauthConfig = $oauthConfig["weixin"];

        $appid = preg_replace('/[^\w_\.\$]/', '', $_GET["appid"]);
        if ($appid)
        {
            $oauthConfig = isset($oauthConfig[$appid]) ? $oauthConfig[$appid] : $oauthConfig;
        }

        $authUrl = 'https://open.weixin.qq.com/connect/oauth2/authorize';

        $params = array();
        if (isset($_GET['mid']))
        {
            $params["mid"] = \Common\Utils\Url::getStringParam("mid");
        }
        if (isset($_GET['appid']))
        {
            $params["appid"] = \Common\Utils\Url::getStringParam("appid");
        }

        $callbackUrl = PASSPORT_HOST . $_GET['callback'];
        if (!empty($params))
        {
            $callbackUrl .= "?" . http_build_query($params);
        }
        if (dev::isDevelopment()) {
            $callbackUrl .= (!empty($callbackUrl) ? "&" : "?") . "rewrite=" . PASSPORT_HOST;
            $callbackUrl = (isHttps() ? 'https://' : 'http://') . 'passport.2345.com' . $_GET['callback'] . $callbackUrl;
        }

        if (!in_array($_GET['callback'], $this->allowCallbacks))
        {
            redirect('/');
        }
        $state = md5(time() . MD5KEY);
        setcookie('redirect_uri', $callbackUrl, 0, '/');
        setcookie('oauth_state', $state, 0, '/');
        $params = array(
            "appid"         => $oauthConfig["appid"],
            "redirect_uri"  => $callbackUrl,
            "response_type" => "code",
            "scope"         => $scope,
            "state"         => $state,
        );
        redirect($authUrl . "?" . http_build_query($params) . "#wechat_redirect");
    }

}
