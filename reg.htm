<script>
function GetRequest() {

   var url = location.search; //获取url中"?"符后的字串

   var theRequest = new Object();

   if (url.indexOf("?") != -1) {

      var str = url.substr(1);

      strs = str.split("&");

      for(var i = 0; i < strs.length; i ++) {

         theRequest[strs[i].split("=")[0]]=unescape(strs[i].split("=")[1]);

      }

   }

   return theRequest;

}

var Request = new Object();

Request = GetRequest();

var forward = null;

forward = Request['forward'];

var url = location.search
window.location = "http://login.2345.com/reg.php"+url;
</script>