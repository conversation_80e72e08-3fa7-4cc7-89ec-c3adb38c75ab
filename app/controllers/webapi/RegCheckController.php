<?php

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 摘    要:RegCheckController.php
 * 作    者:<EMAIL>
 * 修改日期: 2015/9/10
 */

/**
 * 客户端检查是否显示验证码借口
 * */
class RegCheckController extends Controller
{

    public function actionIndex()
    {
        $userClientIp = get_client_ip();
        $regAction = loadAction("reg");
        $DateAnalyzeAction = loadAction('DateAnalyze');
        $isShowCode = $regAction->regCheckShowCode($userClientIp);
        //如果ip不需要显示验证码,检查时间规则是否需要验证码
        if (!$isShowCode)
        {
            $isShowCode = \Service\Security\CaptchaRules::getShowCode('reg');
        }
        //如果需要显示验证码,那么就不在记录时间片,不显示验证码时,加上时间片
        
        if (!$isShowCode)
        {
            
            $DateAnalyzeAction->setDateSlice('reg','captcha');
            
        }
        session_start();
        $key = md5(mt_rand(100000, 9999999) . 'key' . time());
        $val = md5(mt_rand(100000, 9999999) . 'val' . time());
        $_SESSION['reg_token'] = $key;
        $_SESSION[$key] = $val;
        $_SESSION['reg_time'] = time();
        if (isset($_SESSION['reg_num']))
        {
            $_SESSION['reg_num'] += 1;
        }
        else
        {
            $_SESSION['reg_num'] = 1;
        }

        $hidden = "<input type='hidden' name='" . $key . "' value='" . $val . "'>";
        echo "reg.checkRegStatus(" . json_encode(
                array('display' => $isShowCode, $key => $val)
            ) . ");$('#myForm').append(\"" . $hidden . "\");";
        exit;
    }

    /**
     * 获取身份令牌
     * */
    public function actionToken()
    {
        $uuid = \Common\Utils\Cookie::getUuid();
        $securityAction = loadAction("Security");
        $regToken = $securityAction->setRegToken($uuid);

        echo "reg.checkRegToken(" . json_encode(array('token' => $regToken)) . ")";
        exit;
    }

}
