<?php

includeBase('CronController');

class ClearRedisController extends CronController
{

    public function actionIndex()
    {
        $redis = RedisEx::getInstance();
        //注册锁定IP持续时间
        define('RIPLCT', $redis->hGet('LimitSetting', 'RIPLCT'));
        //注册锁定IP段持续时间
        define('RIPDLCT', $redis->hGet('LimitSetting', 'RIPDLCT'));
        //注册禁止IP段持续时间
        define('RIPDFCT', $redis->hGet('LimitSetting', 'RIPDFCT'));
        $nowTime = time();
        $redis->zRemRangeByScore('regLockedIps', 0, $nowTime - RIPLCT);
        $redis->zRemRangeByScore('regLockedIpDuans', 0, $nowTime - RIPDLCT);
        $redis->zRemRangeByScore('regForbidenIpDuans', 0, $nowTime - RIPDFCT);
    }

}
