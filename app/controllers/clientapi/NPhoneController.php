<?php

use Common\Utils\Url;
use Service\Encryption\Aes\AesManager;
use Service\Encryption\Sign\SignManager;
use Service\AccountStat\Consts\AccountStat;
use WebLogger\Facade\LoggerFacade;

includeBase('NController');

class NPhoneController extends NController
{
    /**
     * 发送登录或注册的短信验证码
     * 显示图片验证码条件 - 云控中配置的短信验证码条数 ，按项目来配置
     * 基础配置：
     * 发短信基础配置：1分钟2次，1小时15次，一小时50次
     * 发短信限制：1小时15次，一小时50次
     * 短信验证码输错限制：3次输错即当次验证码无效(当前的验证码会被销毁)，需重新发送（这个调用发送短信功能，已实现）
     * 规则参考/clientapi/phone/SendLoginCode  ，主要修正就是登录和注册的发送短信合并在一起
     * 合并操作可参考  /external/Quick
     * @return void
     */
    public function actionSendLoginCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        $this->startSession();
        $necessaryField = self::getNeedField(['mid', 'phoneNumber', SignManager::$paramSignName,]);
        list($mid, $phone) = $this->getParamAndAutoDeny($necessaryField);
        $captchaCode = Common\Utils\Url::getStringParam('captchaCode');
        $phone = (new AesManager())->aes128cbcHexDecrypt($phone);
        $captchaId = Common\Utils\Url::getStringParam('captchaId');
        $warningAction = loadAction('Warning');
        $app_use_neCaptcha = APP_USE_NECAPTCHA != "false" && !empty($captchaId);
        $warningAction->setRequestInfo(
            [
                'dir_type'          => 'w_phone_code',
                'type'              => 'sendCode',
                'path'              => true,
                'source'            => $_REQUEST['mid'],
                'app_use_neCaptcha' => $app_use_neCaptcha,
                'phone'             => md5($phone),
            ]
        );
        if ($app_use_neCaptcha) {
            $errInfo = null;
            $validateAction = loadAction('NEValidate');
            if (!$validateAction->appCheck163Verify($_POST, $errInfo)) {
                $context = [
                    'level'   => 'error',
                    'type'    => "error_ne_net",
                    'path'    => "/clientapi/NPhone/SendLoginCode",
                    'context' => $errInfo,
                ];
                LoggerFacade::error("易盾校验失败", $context);
                if ($errInfo["error"] == "100105") {
                    $this->codeResponse(100105, "", "校验失败");
                }
                $this->codeResponse(4105, "", "校验失败");
            }
        } else {
            $ShowCaptchaAction = loadAction('ShowCaptcha');
            $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($mid, $phone, $captchaCode);
            if ($showCaptcha['code'] != 200) {
                $this->echoResponse($showCaptcha);
            }
        }

        $memberModel = loadModel('member');
        $phoneAction = loadAction("phone");
        $sessionVerify = true;
        $smsFilterConfig = [
            'mNum' => 15,
            'hNum' => 15,
            'dNum' => 50
        ];
        $smsType = $phoneAction->getSmsType($mid);
        if ($memberModel->checkPhone($phone, true) != 0) {// 手机已存在，发送登录验证码
            $retCode = $phoneAction->sendLoginCode($mid, $phone, $smsType, 453, $sessionVerify, $smsFilterConfig);
        } else {
            $retCode = $phoneAction->sendRegCode($mid, $phone, $smsType, 454, $sessionVerify, $smsFilterConfig);
        }
        switch ($retCode) {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 301:
                $this->showError301();
                break;
            case 302:
                $this->showError302();
                break;
            case 400:
                $this->codeResponse(4100, "", "发送频繁，请稍后再试", 2100009);
                break;
            case 404:
                $this->codeResponse(4104, "", "该手机号不存在", 2100010);
                break;
            case 500:
            default:
                $this->codeResponse(5000, "", "服务器忙，请稍后再试", 2000007);
                break;
        }
    }

    /**
     * 快捷登录：满足登录或注册
     * 如果手机号没有注册，则先进行注册，再进行登录操作
     * 注意并发的问题
     * @return void
     */
    public function actionLogin()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST") {
            return;
        }
        $warningAction = loadAction('Warning');
        $warningAction->setRequestInfo([
            'dir_type' => 'w_phone',
            'type' => 'login',
            'path' => true,
            'account_type' => 'phone',
            'source' => $_REQUEST['mid'],
            'passid' => '',
        ]);
        $uploadToKafkaAction = loadAction('uploadToKafka');
        $uploadToKafkaAction->setSwitch(true);//开启kafka上报
        $uploadToKafkaAction->addData([
            'event_type' => AccountStat::TP_LOGIN,
            'type' => AccountStat::AC_TP_PHONE,
        ]);
        $necessaryField = self::getNeedField([
            'mid',
            'phoneNumber',
            'verifyCode',
            SignManager::$paramSignName,
        ]);
        list($exId, $phone, $verifyCode) = $this->getParamAndAutoDeny($necessaryField);
        $phone = (new AesManager())->aes128cbcHexDecrypt($phone);
        $warningAction->setRequestInfo(['phone' => md5($phone)]);
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone)) {
            $this->codeResponse("300", "", "手机号错误，请重新输入", 1000006);
        }
        $QuickAction = loadAction("Quick");
        if ($QuickAction->isLockRepeatRequest($phone)) {
            $this->codeResponse("4117", "", "服务器忙，请稍后再试", 2100011);
        }

        $phoneAction = loadAction("phone");
        $memberModel = loadModel('member');
        if ($memberModel->checkPhone($phone, true) != 0) {// 手机已存在，直接登录
            if (in_array($phone, ['18200432762']) && $verifyCode == '123456') {
                //xq龙潇提交APP审核
            } else {
                if (!$QuickAction->checkCodeInvalid('login', $exId, $phone)) {
                    $this->codeResponse(4110, "", "验证码失效，请重新输入", 2000008);
                }
                //登录验证码校验
                if (!$phoneAction->checkLoginCode($exId, $phone, md5($phone . $verifyCode), true, true)) {
                    $this->codeResponse("303", "", "验证码错误，请重新输入", 2000009);
                }
            }

            $memberModel = loadModel('member');
            $passid = $memberModel->getPassidByPhone($phone);
            if (!$passid) {
                //手机号,未绑定手机号, 但存在用户名
                $passid = $memberModel->getPassidByUsername($phone);
            }
            $user = [];
            if (!empty($passid)) {
                $user = $memberModel->read($passid);
                if ($user['locked'] == 2) {
                    $this->codeResponse(400, "", "此账号已被冻结，请联系客服", ********);
                }
                $isProhibitLogin = $phoneAction->prohibitUsernameAsPhoneDiffBindPhoneLogin($user, $phone);
                if ($isProhibitLogin) {
                    $this->codeResponse(400, "", "登录失败，请重新再试", 2100012);
                }
            }

            //收集注册登录信息
            $source = Url::getStringParam("exId", "mobile");
            $st = !!$passid;
            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
            \Service\AccountStat\AccountStat::collect($type, $clientType, $phone, $st, $source);

            // 登录
            if ($passid) {
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $phoneAction = loadAction("phone");
                $newUserName = $phoneAction->repairUsernameWithPhoneNum($phone, $user, "external");
                if ($newUserName) {
                    $username = $newUserName;
                } else {
                    $username = $user['username'];
                }
                $user["m_uid"] = unserialize($user['m_uid']);
                $uid = $user['m_uid']['1'];
                $userMod = $user['gid'] % 100 == 0 ? $user['gid'] : 0;
                $cookieI = loadAction('login')->getLoginCookie($passid, $uid, $username, $userMod);
                $warningAction->setRequestInfo(['passid' => $passid, 'login_time' => time()]);
                if (in_array($_REQUEST['mid'], Config::get("loginSpecialMid"))) {
                        //删除青鸟修改密码的缓存
                    $redis = RedisEx::getInstance();
                    $redis->del("redis:cache:member:update:password:passid:$passid");
                }
                $this->codeResponse(
                    200,
                    [
                        'source' => encryptCookieIByAes128cbc($cookieI['I']),
                        'login_type' => 1
                    ],
                    "登录成功",
                    2000000
                );
            } else {
                $this->codeResponse(400, "", "登录失败，请重新再试", 2000011);
            }
        } else {
            $uploadToKafkaAction->addData(['event_type' => AccountStat::TP_REG]);
            if (!$phoneAction->checkRegCode($exId, $phone, md5($phone . $verifyCode), true, true)) {
                $this->codeResponse(303, "", "验证码错误，请重新输入", 2000010);
            }
            $userClientIp = get_client_ip();
            $msgs = array(
                300 => array(
                    0 => '请输入正确的手机号码',
                    1 => '密码最少6个字符',
                    2 => '密码最多16个字符',
                    3 => '此手机号已被注册',
                ),
                400 => array(
                    0 => 400, // 非法域名调用
                    1 => 401, // 非法IP调用
                    2 => 402, // 批量刷CHECK
                    3 => 403, // IP段被禁止
                    4 => 404, // IP被禁止
                    5 => 405 // 未验证通过（缺少isValidate）
                ),
            );
            $pwd = mt_rand() . time();
            $password = substr(md5($pwd . MD5KEY), 0, 16);

            $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
            $pwdScore = $Zxcvbn->passwordStrength($password);
            $pwdStrength = $pwdScore['score'];
            $regAction = loadAction('reg');

            $gid = 300;
            // 客户端 注册
            $clientType = \Service\AccountStat\Consts\AccountStat::CTP_APP;
            $source = \Common\Utils\Url::getStringParam("exId", "mobile");
            $result = $regAction->regPhone(
                $phone,
                $password,
                $pwdStrength,
                $userClientIp,
                "login.2345.com",
                $gid,
                $source,
                $clientType
            );
            $states = explode(".", $result[0]);
            if ($states[0] == 400) {
                $this->codeResponse(403, "", 'errorCode:' . $msgs[$states[0]][$states[1]]);
            } elseif ($states[0] == 300) {
                $this->codeResponse($states[0] + $states[1], "", $msgs[$states[0]][$states[1]]);
            } elseif ($states[0] == 200) {
                $passid = $result[1]['passid'];
                $uid = $result[1]['uid'];
                $username = $result[1]['username'];
                $gid = $result[1]['gid'];
                $userMod = $gid % 100 == 0 ? $gid : 0;
                $cookieI = loadAction('login')->getLoginCookie($passid, $uid, $username, $userMod);
                $warningAction->setRequestInfo(['passid' => $passid, 'reg_time' => time(), 'login_time' => time()]);
                $this->codeResponse(
                    200,
                    [
                        'source' => encryptCookieIByAes128cbc($cookieI['I']),
                        'login_type' => 2
                    ],
                    "登录成功",
                    2000000
                );
            } else {
                $this->codeResponse("500", "", "登录失败，请重新再试", 2000011);
            }
        }
    }

    /**
     * 发送绑定验证码
     * 里面包含绑定手机验证码、验证原手机验证码、修改手机验证码
     * @return void
     */
    public function actionSendBindCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        $this->startSession();
        $necessaryField = self::getNeedField(['mid', 'sendType', SignManager::$paramSignName,]);
        list($mid, $sendType) = $this->getParamAndAutoDeny($necessaryField);
        $verifyCode = Url::getStringParam('captchaCode');
        $phone = Url::getStringParam('phoneNumber');
        $phone && $phone = (new AesManager())->aes128cbcHexDecrypt($phone);
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid])) {
            $this->showError501();
        }

        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo) {
            $this->showError401();
        }
        $passid = $authInfo['i'];

        $phoneAction = loadAction("phone");
        if ('verifyCode' == $sendType) {
            $phoneInfo = $phoneAction->getComplex($passid);
            if (!$phoneInfo) {
                $this->showError404();
            }
            $phone = $phoneInfo['phone'];
        }
        $captchaId = Common\Utils\Url::getStringParam('captchaId');
        if (APP_USE_NECAPTCHA != "false" && !empty($captchaId)) {
            $validateAction = loadAction('NEValidate');
            $errInfo = null;
            if (!$validateAction->appCheck163Verify($_POST, $errInfo)) {
                $context = [
                    'level'   => 'error',
                    'type'    => "error_ne_net",
                    'path'    => "/clientapi/NPhone/SendBindCode",
                    'context' => $errInfo,
                ];
                LoggerFacade::error("易盾校验失败", $context);
                if ($errInfo["error"] == "100105") {
                    $this->codeResponse(100105, "", "校验失败");
                }
                $this->codeResponse(4105, "", "校验失败");
            }
        } else {
            $ShowCaptchaAction = loadAction('ShowCaptcha');
            $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($mid, $phone, $verifyCode);
            if ($showCaptcha['code'] != 200) {
                $this->echoResponse($showCaptcha);
            }
        }

        switch ($sendType) {
            case "verifyCode":
                $retCode = $phoneAction->sendVerifyCode($mid, $passid, 5, 157);
                break;
            case "bindCode":
                $retCode = $phoneAction->sendBindCode($mid, $passid, $phone, 5, 151);
                break;
            case "editCode":
                $retCode = $phoneAction->sendEditCodeNew($mid, $passid, $phone, 5, 160);
                break;
            default:
                $this->codeResponse("500", "", "不支持的sendType！", 1100000);
                $retCode = '';
        }
        switch ($retCode) {
            case 200:
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 301:
                $this->showError301();
                break;
            case 302:
                $this->showError302();
                break;
            case 303:
                $this->showError303();
                break;
            case 400:
                $this->showError400();
                break;
            case 402:
                $this->showError402();
                break;
            case 404:
                $this->showError404();
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 绑定手机号码
     * @return void
     */
    public function actionBind()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        $necessaryField = self::getNeedField(['mid', 'phoneNumber', 'verifyCode', SignManager::$paramSignName,]);
        list($mid, $phone, $verifyCode) = $this->getParamAndAutoDeny($necessaryField);
        $phone = (new AesManager())->aes128cbcHexDecrypt($phone);
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone)) {
            $this->showError300();
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid])) {
            $this->showError501();
        }

        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo) {
            $this->showError401();
        }
        $passid = $authInfo['i'];

        $phoneAction = loadAction("phone");
        if (!$phoneAction->checkBindCode($mid, $passid, md5($phone . $verifyCode))) {
            $this->showError303();
        }

        $memberModel = loadModel('member');
        $userInfo = $memberModel->read($passid);
        $userInfo['passid'] = $passid;

        $retCode = $phoneAction->bind($passid, $phone);
        switch ($retCode) {
            case '200.0':
                //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:PHONE;
                $objLogV2Action = loadAction('logV2');
                $objLogV2Action->report($mid, 'USER_INFO_ADD', 'PHONE', $userInfo, '', $phone);

                $this->log($passid, '', $phone);
                $this->show200('绑定成功！');
                break;
            case '300.0':
                $this->showError300();
                break;
            case '300.1':
                $this->showError301();
                break;
            case '300.2':
                $this->showError302();
                break;
            case '300.3':
                $this->showError402();
                break;
            case '500.0':
            default:
                $this->showError500();
                break;
        }
    }


    /**
     * 校验原手机验证码接口
     * @return void
     */
    public function actionCheckVerifyCode()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        $necessaryField = self::getNeedField(['mid', 'verifyCode', SignManager::$paramSignName,]);
        list($mid, $verifyCode) = $this->getParamAndAutoDeny($necessaryField);
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid])) {
            $this->showError501();
        }

        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo) {
            $this->showError401();
        }
        $passid = $authInfo['i'];

        $phoneAction = loadAction("phone");
        $phoneInfo = $phoneAction->getComplex($passid);
        if (!$phoneInfo) {
            $this->showError404();
        }
        $phone = $phoneInfo['phone'];
        if ($phoneAction->checkVerifyCode($mid, $passid, md5($phone . $verifyCode))) {
            $this->show200('验证成功！');
        } else {
            $this->codeResponseNew(400, "", '验证失败！', 2000013);
        }
    }

    /**
     * 修改手机绑定手机
     * @return void
     */
    public function actionEdit()
    {
        if ($_SERVER["REQUEST_METHOD"] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        $necessaryField = self::getNeedField(['mid', 'phoneNumber', 'verifyCode', SignManager::$paramSignName,]);
        list($mid, $phone, $verifyCode) = $this->getParamAndAutoDeny($necessaryField);
        $phone = (new AesManager())->aes128cbcHexDecrypt($phone);
        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone)) {
            $this->showError300();
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid])) {
            $this->showError501();
        }

        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo) {
            $this->showError401();
        }
        $passid = $authInfo['i'];

        $phoneAction = loadAction("phone");
        if (!$phoneAction->checkEditCode($mid, $passid, md5($phone . $verifyCode))) {
            $this->showError303();
        }
        $memberModel = loadModel("member");
        $userinfo = $memberModel->read($passid);
        $userinfo['passid'] = $passid;

        if ($userinfo["phone"]) {
            $phoneOld = $userinfo["phone"];
            $retCode = $phoneAction->edit($passid, $phone);
        } else {
            $phoneOld = $userinfo["phone_redundancy"];
            $retCode = $phoneAction->bindSimple($passid, $phone);
        }
        switch ($retCode) {
            case '200.0':
                //日志行为打点:用户信息添加或修改; type:USER_INFO_ADD; sub_type:PHONE;
                $objLogV2Action = loadAction('logV2');
                $objLogV2Action->report($mid, $userinfo["phone"] ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD', 'PHONE', $userinfo, $userinfo["phone"], $phone);

                $this->log($passid, $phoneOld, $phone);
                $this->show200('修改成功！');
                break;
            case '300.0':
                $this->showError300();
                break;
            case '300.1':
                $this->showError301();
                break;
            case '300.2':
                $this->showError302();
                break;
            case '500.0':
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 功  能：返回成功信息
     *
     * @param string $msg msg
     * @return void
     */
    private function show200($msg = '发送成功！')
    {
        $this->codeResponseNew(200, "", $msg, 2000000);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError300()
    {
        $this->codeResponseNew(300, "", '手机号错误，请重新输入', 2100006);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError301()
    {
        $this->codeResponseNew(301, "", '该手机号已被其他账号绑定，请更换手机号绑定', 2100007);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError302()
    {
        $this->codeResponseNew(302, "", '该手机号已被其他账号绑定，请更换手机号绑定', 2100008);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError303()
    {
        $this->codeResponseNew(303, "", '手机短信验证码错误！', 2000013);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError400()
    {
        $this->codeResponseNew(400, "", '发送频繁，请稍后再试！', 2000014);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError401()
    {
        $this->codeResponseNew(401, "", '身份验证失效，请重新登录！', 2000012);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError402()
    {
        $this->codeResponseNew(402, "", '已绑定过手机号！', 2100014);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError404()
    {
        $this->codeResponseNew(404, "", '你的帐号未绑定手机！', 2100013);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError500()
    {
        $this->codeResponseNew(500, "", '服务器忙，请稍后再试！', 2000007);
    }

    /**
     * 功  能：显示错误
     *
     * @return void
     */
    private function showError501()
    {
        $this->codeResponseNew(501, "", '项目标识不正确！', 1000007);
    }

    /**
     * 添加手机修改日志
     *
     * @param int $passid passid
     * @param string $phoneOld phoneOld
     * @param string $phone phone
     * @param string $from from
     * @return void
     */
    private function log($passid, $phoneOld, $phone, $from = 'phone')
    {
        $clientIp = get_client_ip();
        $domain = 'login.2345.com';
        $referDomain = getReferDomain();
        $phoneModel = loadModel('phone');
        $phoneModel->log($passid, array('phone_old' => $phoneOld, 'phone' => $phone, 'op_time' => time(), 'op_ip' => $clientIp, 'op_domain' => $domain, 'op_refer' => $referDomain, 'op_from' => $from));
    }
}
