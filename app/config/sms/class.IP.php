<?php
/**
 * Copyright (c) 2015，上海二三四五网络科技有限公司
 * 摘    要：各种IP列表统一维护保存
 * 作    者：wangyi <<EMAIL>>
 * 修改日期：2015.11.05
 * 
 * 包含所有发起请求的IP地址，包括虚ip，ip列表一般情况下第一个表示是源站服务器的ip
 */
class Config_IP
{
    /**
     * 公司的IP配置
     * @var array
     */
    private static $company = array(
        // 亮秀路
        '*************',
        '**************',
        // 以下是博霞路11号的ip们
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '***************',
        '***************',
        '***************',
        '***************',
        '***************',
        '***************',
        '***************',
        '***************',
        //环科路新ip
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '*************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**************',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',

    );
	
	/**
	 * 积分
	 * @var array
	 */
	private static $jifen = array(
		'***************', // 源站
		'***************',
		'***************',
		'***************',
		'***************', // 测试机
		'**************', // lvs
		'*************','*************','************','************',
		'*************','*************',
		'*************','*************', // lvs
	);

	/**
	 * 手机联盟
	 * @var array
	 */
	private static $shouji = array(
		'183.136.203.92', // 源站
		'183.136.203.156',
		'183.136.203.120',
		'183.136.203.23', // lvs
		'***************', // 测试机
		'101.71.76.201','101.71.94.201','************','************',
		'101.71.76.206','101.71.94.206',
		'101.71.76.249','101.71.94.249', // lvs
	);

	/**
	 * 论坛
	 * @var array
	 */
	private static $bbs = array(
		'183.136.203.35',   //电信外网
		'183.136.203.36',   //电信外网
		'101.71.76.141','101.71.94.141','************','************',    //网通外网
		'101.71.76.142','101.71.94.142',    //网通外网
		'101.71.76.70','101.71.94.70', //lvs
		'183.136.203.9', //lvs
	);
	
	/**
	 * 通行证
	 * @var array
	 */
	private static $login = array(
		'*************','*************','************','************',
		'*************','*************',
		'*************','*************',
		'**************',
		'***************',
		'***************',
		'***************',
	);

	/**
	 * 代理商系统
	 * @var array
	 */
	private static $agent = array(
		'**************5',
	);

	/**
	 * 推广页
	 * @var array
	 */
	private static $tuiguang = array(
		'183.136.203.46',
		'183.136.203.76',
	);
	
	/**
	 * 网络电话
	 * @var array
	 */
	private static $netphone = array(
		'**************7',
		'***************',
		'*************','*************','************','************',
		'*************','*************',
		'183.136.203.28',
		'101.71.76.252','************2',
		'**************',
		'***************','**************',
	);
	
	/**
	 * 网络电话PC版
	 * @var array
	 */
	private static $netphone_pc = array(
		'**************7',
		'***************',
		'**************',
		'***************',
		'183.136.203.28',
		'*************','*************','************','************',
		'*************','*************',
		'101.71.76.252','************2','**************',
	);
	
	/**
	 * 网络电话手机版
	 * @var array
	 */
	private static $netphone_mobile = array(
		'**************7',
		'***************',
		'**************',
		'***************',
		'183.136.203.28',
		'*************','*************','************','************',
		'*************','*************',
		'101.71.76.252','************2','**************',
	);
	
	/**
	 * 2345携手平安送保险活动
	 * @var array
	 */
	private static $pingan_insurance = array(
		'***************',
		'***************',
		'*************','*************','************','************',
	);
	
	/**
	 * 用户中心
	 * @var array
	 */
	private static $icon_poll = array(
		'183.136.203.18',
		'**************',
		'**************',
		'**************',
		'**************',
		'***************',
		'***************',
		'***************',
		'***************',
		'***************',
		'*************','*************','************','************',
		'*************','*************',
		'*************','*************',
		'*************','*************',
		'*************','*************',
		'*************','*************',
		'************',
	);
	
	/**
	 * 彩票
	 * @var array
	 */
	private static $caipiao = array(
		'**************',
		'**************',
		'***************',
		'**************',
	);
	
	/**
	 * 影视大全
	 * @var array
	 */
	private static $movie = array(
		'**************',
		'**************',
		'**************',
		'**************',
		'**************',
		'**************',
		'**************',
		'*************','*************','************','************',
		'*************','*************',
		'*************','*************',
		'*************','*************',
	    
	    // 影视的新IP, add at 2015-04-15
	    '**************',
	    '**************',
	    '**************',
	    '**************',
	    '**************',
	    '**************',
	    '**************',
	    '***************',
	    '***************',
	    '***************',
	    '***************',
	    
	    // 购物频道用的IP，影视和电商共用一个businessId
	    '*************','*************',
	    '*************','*************',
	    '*************','*************',
	    '**************',
	    '**************',
	    '**************',
	    '**************',
	    '**************3',
	    '**************4',
	);
	
	/**
	 * 小说
	 * @var array
	 */
	private static $xiaoshuo = array(
		'***************',
		'***************',
		'***************',
		'**************',
		'*************','*************','************','************',
		'*************','*************',
		'*************','*************',
		'*************','*************',
	);
	
	/**
	 * 王牌浏览器登陆注册
	 * @var array
	 */
	private static $browser = array(
		'**************7',
		'***************',
		'***************',
		'*************','*************','************','************',
		'*************','*************',
	);
	
	/**
	 * 测试组用短信接口发短信
	 * @var array
	 */
	private static $tester = array(
		'172.16.0.21',
	);
	
	/**
	 * 50bang
	 * @var array
	 */
	private static $bang = array(
		'42.62.60.236',
	);
	
	/**
	 * 手机浏览器
	 * @var array
	 */
	private static $android_browser = array(
	    '183.136.203.52',
	    '183.136.203.58',
	    '183.136.203.130',
	    '101.71.76.188','101.71.94.188','************','************',
	    '101.71.76.189','101.71.94.189',
	    '183.136.203.75',
	    '183.136.203.160',
	    '101.71.76.153','101.71.94.153',
	    '101.71.76.223','101.71.94.223',
		'183.136.203.124',
	);
	
	/**
	 * 阅读王
	 * @var array
	 */
	private static $reader = array(
	    '***************',
	    '**************',
	    '**************',
	    '*************','*************','************','************',
	    '*************','*************',
	    '*************','*************',
	);

	/**
	 * 游戏
	 * @var array
	 */
	private static $game = array(
	    '**************',
	    '**************',
	    '**************',
	    '**************',
	    '***************',
		'***************',
	    '***************',
	);
	
	/**
	 * 代理IP
	 * @var array
	 */
	private static $proxy = array(
		'*************',
		'*************',
		'*************'
	);
	
	private static $web_client = array(
		'**************',
	);
	
	/**
	 * 分类金融
	 * @var array
	 */
	private static $list_finance = array(
		'**************',
		'*************',
	);
	
	/**
	 * 数据开发
	 * @var array
	 */
	private static $data_dev = array(
		// 移动武林榜
		'***********',
		'**********',
		'**********',
		'************',
		'************',
		'************',
		'************',
		'************',
		
		// 客户端武林榜
		'***************',
		'***************',
		'***************',
		
		// web武林榜实时运算
		'************',
		'************',
		'************',
		'**********',
		'**********',
		'**********',
		'**********',
		'**********',
		'**********',
		'**********',
		'**********',
		
		// web武林榜
		'************',
		'**********',
		'**********',
		'**********',
		'**********',
		'************',
		'**********',
		'*********',
		'42.62.4.38',
		'42.62.4.9',
		'42.62.12.140',																
	);
	
	private static $money_super_market = array(
			'172.15.13.79',
			'221.228.75.196',
			'221.228.75.205',
			'117.121.135.73',
	);

	/**
	 * 语音验证码和短信验证码合并
	 * @var array
	 */
	private static $combine = array(
		'**************',
		'**************7',
		'***************',
		'***************',
		'*************','*************','************','************',
		'*************','*************',
		'*************',
		'*************','**************',
	);

    /**
     * 猜你喜欢
     * @var array
     */
    private static $guessLike = array(
        '**********',
    );

    /**
     * 客户端服务器
     * @var array
     */
    private static $client = array(
        '*************',
        '*************'
    );

    /**
     *  tools.2345.com
     *  @var array
     */
    private static $tools = array(
        '***************',
		'***************',
		'***************',
    );
    
    /**
     * 域名贷
     * @var unknown
     */
    private static $ymd = array(
    	'**************',
    );

	/**
	 * 接口方法获取IP配置，这个方法放在这里有点奇怪 -_-，先这样吧。 by wangyi <<EMAIL>> 2015-05-27
	 * @param string $key
	 */
	public static function get($key='')
	{
		if(!isset(self::$$key))
		{
			return array();
		}
		return self::$$key;
	}
}
