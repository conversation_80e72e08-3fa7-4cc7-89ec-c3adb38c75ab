<?php
/**
 * Copyright (c) 2022,上海二三四五网络科技有限公司
 * 摘    要：获取OA产品信息相关动作
 * 作    者：朱锋锦
 * 日    期：2022-01-24
 */
class OAProductAction extends Action
{

    const REDIS_KEY_PROJECT = 'yfzt:api:project'; //研发中台数据缓存redis key
    const API_URL_PROJECT = 'https://cloud-api.2345.cn/open/v2/appList'; //研发中台数据接口

    /**
     * OAProductAction constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 功    能: 根据产品获取项目信息
     * 作    者: 朱锋锦
     * 日    期: 2022-01-24
     *
     * @param int $intOAProductId 产品ID
     * @return array 项目产品信息：项目ID、项目名称、项目下属产品信息列表（产品ID、产品名称）
     */
    public function getProjectByProduct($intOAProductId)
    {
        $arrAllProjectProduct = $this->getAllProjectProduct();
        foreach ($arrAllProjectProduct as $arrProject) {
            if (isset($arrProject['product_list'][$intOAProductId])) {
                return $arrProject;
            }
        }
        return [];
    }

    /**
     * 功    能: 根据项目获取产品信息
     * 作    者: 朱锋锦
     * 日    期: 2022-01-24
     *
     * @param int $intOAProjectId 项目ID
     * @return array 项目产品信息：项目ID、项目名称、项目下属产品信息列表（产品ID、产品名称）
     */
    public function getProductByProject($intOAProjectId)
    {
        $arrAllProjectProduct = $this->getAllProjectProduct();
        return $arrAllProjectProduct[$intOAProjectId] ?? [];
    }

    /**
     * 功    能: 获取全部的项目产品信息
     * 作    者: 朱锋锦
     * 日    期: 2022-01-24
     *
     * @return array 项目馋哦信息列表：项目ID、项目名称、项目下属产品信息列表（产品ID、产品名称）
     */
    public function getAllProjectProduct()
    {
        //1、获取静态变量
        static $arrAllProjectProduct = null;
        if (is_array($arrAllProjectProduct)) {
            return $arrAllProjectProduct;
        }

        //2、静态变量中没有数据，从redis中获取数据
        $objRedis = \RedisAction::connect();
        $strRedisKey = self::REDIS_KEY_PROJECT;
        $strRedisContent = $objRedis->get($strRedisKey);
        $arrRedisContent = unserialize($strRedisContent);
        $intRedisTime = $arrRedisContent['time'] ?? 0;
        $arrRedisData = $arrRedisContent['data'] ?? [];
        if ($arrRedisData && time() - $intRedisTime <= 300) {
            $arrAllProjectProduct = $arrRedisData;
            return $arrAllProjectProduct;
        }

        //3、静态变量中没有数据、redis中没有数据或数据时间超过300秒，从研发中台的接口中获取数据
        $strApiUrl = self::API_URL_PROJECT;
        $strApiContent = FuncAction::request($strApiUrl)[0];
        $arrApiContent = json_decode($strApiContent, true);
        $arrApiData = $this->formartProjectProduct($arrApiContent);
        if ($arrApiData) {
            //将数据存入redis
            $arrRedisContent = ['time' => time(), 'data' => $arrApiData];
            $objRedis->setex($strRedisKey, 86400 * 30, serialize($arrRedisContent));

            $arrAllProjectProduct = $arrApiData;
            return $arrAllProjectProduct;
        }

        //4、如果接口也没有数据，则尝试返回redis中的数据，并记录日志报警
        \WebLogger\Facade\LoggerFacade::error('获取研发中台项目产品信息失败', ['url' => $strApiUrl]);
        return $arrRedisData ?: [];
    }

    /**
     * 功    能: 格式化研发中台接口返回数据
     * 作    者: 朱锋锦
     * 日    期: 2022-01-24
     *
     * @param array $arrApiContent 接口返回数据
     * @return array
     */
    private function formartProjectProduct($arrApiContent)
    {
        $arrApiData = [];
        if (!empty($arrApiContent['data'])) {
            //格式化接口数据
            foreach ($arrApiContent['data'] as $arrProject) {
                $arrApiData[$arrProject['id']] = [
                    'project_id' => $arrProject['id'],
                    'project_name' => mb_convert_encoding($arrProject['name'], 'GBK', 'UTF-8'),
                    'product_list' => [],
                ];
                foreach ($arrProject['products'] as $arrProduct) {
                    $arrApiData[$arrProject['id']]['product_list'][$arrProduct['id']] = [
                        'product_id' => $arrProduct['id'],
                        'product_name' => mb_convert_encoding($arrProduct['name'], 'GBK', 'UTF-8'),
                    ];
                }
            }

            //添加其他项目、其他产品
            $arrApiData[0] = [
                'project_id' => 0,
                'project_name' => '其他项目',
                'product_list' => [
                    0 => [
                        'product_id' => 0,
                        'product_name' => '其他产品',
                    ],
                ],
            ];
        }
        return $arrApiData;
    }
}
