var enableCheck = true;
var intervalHandle = false;
var intervalTimes = 60;
var usernamevalid = true;
var phoneCodeValid = true;
Zepto(function($) {
    $(".form-ipt").bind({
        focus: function() {
            $(this).closest(".form-item").removeClass("form-item-error");
            $(this).closest(".form-item").find(".form-tip-error").hide();
        }
    });
    $("#imgvalid_phone").bind({
        focus: function() {
            $("#msg_validate").hide();
            if ($(this).val() == '请输入算式结果')
            {
                $(this).val("");
            }
        },
        blur: function() {
            if ($(this).val() == '')
            {
                $(this).val("请输入算式结果");
            }
            $(this).removeClass("ipt-txt-current");
            checkValidate_sj("phone");
        }
    });
    $("#imgvalid_email").bind({
        focus: function() {
            $("#msg_validate").hide();
            if ($(this).val() == '请输入算式结果')
            {
                $(this).val("");
            }
        },
        blur: function() {
            if ($(this).val() == '')
            {
                $(this).val("请输入算式结果");
            }
            $(this).removeClass("ipt-txt-current");
            checkValidate_sj("email");
        }
    });
    $("#validate_email").bind({
        focus: function() {
            $("#msg_validate_eamil").hide();
            if ($(this).val() == '请输入算式结果')
            {
                $(this).val("");
            }
        },
        blur: function() {
            if ($(this).val() == '')
            {
                $(this).val("请输入算式结果");
            }
            else
            {
                $(this).css('color', 'black');
            }
            checkValidate_sj('email');
        }
    });
    $("#validate_phone").bind({
        focus: function() {
            $("#msg_validate_phone").hide();
            if ($(this).val() == '请输入验证码')
            {
                $(this).val("");
                $(this).css('color', 'black');
            }
        },
        blur: function() {
            if ($(this).val() == '')
            {
                $(this).val("请输入验证码");
                $(this).css('color', 'gray');
            }
            else
            {
                $(this).css('color', 'black');
            }
            checkValidatePhone_sj();
        }
    });
    $('#btn_yzm_phone_sms').bind({
        click: function() {
            $("#validate_phone").attr('action', '');
            var phone = $.trim($('#usernametxt').val());
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('请填写正确手机号！').show();
                return false;
            }
            val = $("#validate").val();
            if (val === '' || val == '请输入算式结果')
            {
                $("#validate").addClass("ipt-txt-error").next('.i-retok').hide();
                $("#msg_validate").addClass('lg_form-tips form-tips-error').html('请输入正确的计算结果').show();
                return false;
            }
            $.ajax({
                url: "/api/check",
                data: 'type=validate&val=' + val,
                type: 'POST',
                async: false,
                success: function(response) {
                    err = response;
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR.status);
                },
                timeout: 3000
            });
            $(".g-mask").css({"display": "none"});
            $("#demo-pop-wx").css({"display": "none"});
            $('#form-tips-voice-msg').html('系统已将短信发送到您的手机，请注意查收。');
            $('#btn_yzm_phone').attr('action', 'sms');
            if (err == 1)
            {
                intervalTimes = 60;
                clearInterval(intervalHandle);
                intervalHandle = setInterval(function() {
                    showSendMsg('sms');
                }, 1000);
                $.post("/reg/sendPhoneCode", {'phone': phone, 'validate': val}, function(data) {
                    if (data == '400.0')
                    {
                        showSendBtn('sms');
                        $("#validate_phone").attr('action', 'frequently');
                        $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('发送频繁 ，请稍后再试！').show();
                    }
                    else if (data == '500.0')
                    {
                        showSendBtn('sms');
                        $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('服务器繁忙，请稍后再试！').show();
                    }
                    else if (data == '300.0')
                    {
                        showSendBtn('sms');
                        $("#msg_validate_phone").css('visibility', 'visible').addClass('form-tips-error').html('手机号码格式错误，请稍后再试！').show();
                    }
                });
            }
            else
            {
                $("#Pic").attr('src', '/randCaptcha.php?J' + Math.random());
            }
        }
    });
    $('#btn_yzm_phone').bind({
        click: function() {
            var phone = $.trim($('#username_phone').val());
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                showErrorTips("#msg_validate_phone", '请填写正确手机号！');
                return false;
            }
            val = $("#imgvalid_phone").val();
            if (val === '' || val == '请输入算式结果')
            {
                showErrorTips("#msg_imgvalid_phone", '请输入正确的计算结果');
                return false;
            }
            $.ajax({
                url: "/api/check",
                data: 'type=validate&val=' + val,
                type: 'POST',
                async: false,
                success: function(response) {
                    err = response;
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(jqXHR.status);
                },
                timeout: 3000
            });
            if (err == 1)
            {
                if ($('#btn_yzm_phone').attr('action') == 'sms')
                {
                    intervalTimes = 60;
                    clearInterval(intervalHandle);
                    intervalHandle = setInterval(function() {
                        showSendMsg('sms');
                    }, 1000);
                    $.post("/reg/sendPhoneCode", {'phone': phone, 'validate': val}, function(data) {
                        if (data == '400.0')
                        {
                            showSendBtn('sms');
                            $("#validate_phone").attr('action', 'frequently');
                            showErrorTips("#msg_validate_phone", '验证太频繁，请稍后再试！');
                        }
                        else if (data == '500.0')
                        {
                            showSendBtn('sms');
                            showErrorTips("#msg_validate_phone", '服务器繁忙，请稍后再试！');
                        }
                        else if (data == '300.0')
                        {
                            showSendBtn('sms');
                            showErrorTips("#msg_validate_phone", '您输入手机号码格式错误！');
                        }
                        else if (data == '600.0')
                        {
                            showSendBtn('sms');
                            showErrorTips("#msg_validate_phone", '图片验证码错误，请重新输入！');
                        }
                    });
                }
            }
            else
            {
                $("#Pic").attr('src', '/randCaptcha.php?J' + Math.random());
            }
        }
    });

    $(".btn-submit").bind({
        mousedown: function(e) {
            checkAll();
            return false;
        },
        click: function() {
            return false;
        }
    });
    $("#demo-pop-wx .logreg-close").click(function() {
        $(".g-mask").css({"display": "none"});
        $("#demo-pop-wx").css({"display": "none"});
    });
    $("#demo-pop-reg .logreg-close").click(function() {
        $(".g-mask").css({"display": "none"});
        $("#demo-pop-reg").css({"display": "none"});
    });
});
function showPopWx() {
    $("#demo-pop-wx").css({"display": "block"});
    $(".g-mask").css({
        "display": "block",
        "height": $(document).height()
    });
}
;
function showSendMsg(from) {
    if (intervalTimes > 1) {
        --intervalTimes;
        $("#btn_yzm_phone").addClass("btn-disabled");
        $("#btn_yzm_phone").text('重新发送（' + intervalTimes + '）').show();
    } else {
        showSendBtn(from);
    }
}
function showSendBtn(from) {
    clearInterval(intervalHandle);
    intervalTimes = 60;
    if (from == 'sms') {
        $("#btn_yzm_phone").prop("className", "btn-getvcode");
        $("#btn_yzm_phone").text('发送短信验证').show();
    }
}
function checkPhone()
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    var username_phone = $.trim($("#username_phone").val());
    var msg_username_phone = '';
    if(username_phone == '')
    {
        showErrorTips("#msg_username_phone", '请输入手机号');
        return false;
    }
    if(username_phone.length != 11 || !/^1[0123456789]\d{9}$/.test(username_phone))
    {
        showErrorTips("#msg_username_phone", '请输入正确的手机号');
        return false;
    }
    else
    {
        type = 'phone';
        typeName = '手机号码';
    }
    $.ajax({
        url: "/api/check/jsonp",
        data: 'type=' + type + '&value=' + username_phone + '&region=1',
        async: false,
        dataType: 'jsonp',
        jsonp: 'callback',
        success: function(response) {
            if (response == -1) {
                $('#btn_yzm_phone').prop('action', 'sms');
                showTips('#msg_validate_phone', '系统已将短信发送到您的手机，请注意查收。');
                showSendBtn('sms');
                err = 0;
            }
            else
            {
                showSendBtn('voice');
                err = response;
            }
            if (typeof err === "undefined")
            {
                $("#msg_agree").html('<i></i>您的注册可能过于频繁，若有疑问请联系客服！');
                $("#msg_agree").show();
                return false;
            }
            if (err != 0) {
                usernamevalid = false;
                msg_username = '这个' + typeName + '已经被使用，换一个吧';
            }
            else
            {
                usernamevalid = true;
            }
            if (usernamevalid === true)
            {
                hideErrorTips("#msg_username_phone");
                if (/^1[0123456789]\d{9}$/.test(username_phone)) {
                    $('#reg_type_phone').val('phone');
                }
                return true;
            }
            else
            {
                showErrorTips("#msg_username_phone", msg_username);
                return false;
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    if (!usernamevalid)
    {
        showErrorTips("#msg_username_phone", msg_username_phone);
        return false;
    }
    else
    {
        return true;
    }

}
function checkEmail()
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    var username_email = $.trim($("#username_email").val());
    var msg_username_email = '';
    if(username_email == '')
    {
        showErrorTips("#msg_username_email", '请输入邮箱帐号');
        return false;
    }
    if(!/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username_email))
    {
        showErrorTips("#msg_username_email", '请输入正确的邮箱帐号');
        return false;
    }
    else
    {
        type = 'email';
        typeName = '邮箱';
    }
    $.ajax({
        url: "/api/check/jsonp",
        data: 'type=' + type + '&value=' + username_email + '&status=0',
        async: false,
        dataType: 'jsonp',
        jsonp: 'callback',
        success: function(response) {
            err = response;
            if (typeof err === "undefined")
            {
                showErrorTips("#msg_agree_email", '您的注册可能过于频繁，若有疑问请联系客服！');
                return false;
            }
            if (err != 0) {
                usernamevalid = false;
                msg_username = '这个' + typeName + '已经被使用，换一个吧';
            }
            else
            {
                usernamevalid = true;
            }
            if (usernamevalid === true)
            {
                hideErrorTips("#msg_username_email");
                if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username_email)) {
                    $("#yzm_phone").hide();
                    $('#reg_type_email').val('email');
                }
                return true;
            }
            else
            {
                showErrorTips("#msg_username_email", msg_username);
                return false;
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    if (!usernamevalid)
    {
        showErrorTips("#msg_username_email", msg_username_email);
        return false;
    }
    else
    {
        return true;
    }
}

function safe(eName, type)
{
    var isvalid = true;
    var msg_pwd = '';

    $("#pwd_strong_" + type).show();
    var pass = $("#password_" + type).val();
    if (pass.length > 16)
    {
        msg_pwd = '最多16个字符';
        isvalid = false;
    }
    var passscore = zxcvbn(pass);
    var score = passscore.score;
    if (score == 0)
    {
        $("#pwd_strong_" + type).find('span').prop('className', 'weak').html("<s></s><s></s><s></s><s></s>弱");
        $("#pwd_strength_" + type).val('1');

        pass == '' ? msg_pwd = '密码不能为空' : msg_pwd = '密码强度不能为弱';
        isvalid = false;
    }
    else if (score == 1)
    {
        $("#pwd_strong_" + type).find('span').prop('className', 'medium').html("<s></s><s></s><s></s><s></s>中");
        $("#pwd_strength_" + type).val('2');
        isvalid = true;
    }
    else if (score == 2)
    {
        $("#pwd_strong_" + type).find('span').prop('className', 'strong').html("<s></s><s></s><s></s><s></s>强");
        $("#pwd_strength_" + type).val('3');
        isvalid = true;
    }
    else if(score >= 3)
    {
        $("#pwd_strong_" + type).find('span').prop('className', 'stronger').html("<s></s><s></s><s></s><s></s>极强");
        $("#pwd_strength_" + type).val('4');
        isvalid = true;
    }
    if (eName === 'keyup')
    {
        hideErrorTips('#msg_pwd_' + type);
        $('#pwd_strong_' + type).show();
    }
    if (eName === 'blur')
    {
        if (isvalid === true)
        {
            hideErrorTips('#msg_pwd_' + type);
            $('#pwd_strong_' + type).show();
        }
        else
        {
            showErrorTips('#msg_pwd_' + type, msg_pwd);
            $('#pwd_strong_' + type).hide();
        }
    }
    return isvalid;
}

function showErrorTips(tags, msg)
{
    var dom = $(tags);
    dom.closest('.form-item').addClass('form-item-error');
    dom.closest('.form-item').find(tags).html('<i></i>'+msg).show();
}
function showTips(tags, msg)
{
    var dom = $(tags);
    dom.closest('.form-item').addClass('form-tip');
    dom.closest('.form-item').find(tags).html(msg).show();
}
function hideErrorTips(tags)
{
    var dom = $(tags);
    dom.closest('.form-item').removeClass('form-item-error');
    dom.closest('.form-item').find(tags).html('').hide();
}
function checkValidate_sj(type)
{
    if (!enableCheck) {
        enableCheck = true;
        return false;
    }
    val = $("#imgvalid_" + type ).val();
    if (val === '' || val == '请输入算式结果')
    {
        showErrorTips("#msg_imgvalid_" + type, '请输入正确的计算结果');
        return false;
    }
    $.ajax({
        url: "/api/check",
        data: 'type=validate&val=' + val,
        type: 'POST',
        async: false,
        success: function(response) {
            err = response;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
    if (err == 1)
    {
        hideErrorTips("#msg_imgvalid_" + type);
        return true;
    } else {
        showErrorTips("#msg_imgvalid_" + type, '请输入正确的计算结果');
        $("#Pic_" + type ).attr('src', '/randCaptcha.php?J' + Math.random());
        return false;
    }

}
function checkValidatePhone_sj()
{
    if (!enableCheck) {
        enableCheck = true;
        phoneCodeValid = false;
    }
    if ($("#validate_phone").attr('action') == 'frequently')
    {
        showErrorTips("#msg_validate_phone", '发送频繁 ，请稍后再试！');
        phoneCodeValid = false;
    }

    var code = $.trim($("#validate_phone").val());
    if (code === '' || code === '请输入验证码')
    {
        showErrorTips("#msg_validate_phone", '请输入手机验证码');
        phoneCodeValid = false;
    }
    $.ajax({
        url: "/reg/checkPhoneCode",
        data: 'code=' + code + '&phone=' + $.trim($("#username_phone").val()),
        async: false,
        type: 'POST',
        success: function(response) {
            response === '200.0' ? err = 0 : err = 1;
            if (err)
            {
                showErrorTips("#msg_validate_phone", '您输入的验证码错误，请重新输入');
                phoneCodeValid = false;
            }
            else
            {
                hideErrorTips("#msg_validate_phone");
                phoneCodeValid = true;
            }
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
}

function checkAgree(type)
{
    if(type == 'email')
    {
        var type = '_email';
    }
    if (!$("#agree" + type).is(':checked'))
    {
        showErrorTips("#msg_agree" + type, '请同意2345服务协议，隐私申明');
        return false;
    }
    else
    {
        hideErrorTips("#msg_agree" + type);
        return true;
    }
}

function checkAll(type)
{
    if(type == "phone")
    {
        if (!(checkPhone()))
        {
            return false;
        }
    }
    else if(type == "email")
    {
        if (!checkEmail())
        {
            return false;
        }
    }
    if (!checkValidate_sj(type))
    {
        return false;
    }

    if ($('#reg_type_'+type).val() === 'phone' && !checkValidate_sj(type) && !(phoneCodeValid == true))
    {
        return false;
    }
    if (!safe('blur', type))
    {
        return false;
    }
    if (type =='phone' && !checkAgree(''))
    {
        return false;
    }
    if (type =='email' && !checkAgree('email'))
    {
        return false;
    }

    enableCheck = false;
    var msgs = {
        "300": {
            'username': {
                '0': '2345帐号最少2个字符 @msg_username',
                '1': '2345帐号请不要超过24个字符 @msg_username',
                '2': '2345帐号请输入汉字，字母，数字，或邮箱地址 @msg_username',
                '3': '密码最少6个字符 @msg_pwd',
                '4': '密码最多16个字符 @msg_pwd',
                '5': '请输入正确的邮箱 @msg_username',
                '6': '此帐号已被注册，请修改2345帐号 @msg_username',
                '7': '此邮箱已被注册，请换一个 @msg_username'
            },
            'phone': {
                '0': '请输入正确的手机号码 @msg_username',
                '1': '密码最少6个字符 @msg_pwd',
                '2': '密码最多16个字符 @msg_pwd',
                '3': '此手机号已被注册 @msg_username'
            },
            'email': {
                '0': '请输入正确的邮箱地址 @msg_username',
                '1': '密码最少6个字符 @msg_pwd',
                '2': '密码最多16个字符 @msg_pwd',
                '3': '此邮箱号已被注册 @msg_username'
            }
        },
        "400": {
            '0': '非法域名调用',
            '1': '非法IP调用',
            '2': '批量刷CHECK',
            '3': 'IP段被禁止',
            '4': 'IP被禁止',
            '5': '未验证通过（缺少isValidate）'
        }
    };
    if(type == "phone")
    {
        var postData = $("#myFormPhone").serialize();
    }
    else
    {
        var postData = $("#myFormEmail").serialize();
    }
    $.ajax({
        url: "/reg.php",
        data: postData,
        type: 'POST',
        async: false,
        success: function(response) {
            res = eval('(' + response + ')');
            if (typeof res.msgCode !== "undefined" && res.msgCode !== '')
            {
                var regType = $('#reg_type_' + type).val();
                var codeArr = res.msgCode.split('.');
                if (codeArr[0] == '400')
                {
                    $("#msg_agree_" + type + " span").html(msgs['400'][codeArr[1]] + '，若有疑问请联系客服！');
                    $("#msg_agree_" + type + " span").show();

                }
                else if (codeArr[0] == '300')
                {
                    msgArr = msgs['300'][regType][codeArr[1]].split('@');
                    $("#" + msgArr[1] + "_" + type +" span").html(msgArr[0]);
                    $("#" + msgArr[1] + "_" + type).show().next('div').find('.iRight').hide();
                }
                return false;
            }

            if (typeof res.msg !== "undefined" && res.msg !== '')
            {
                msgArr = res.msg.split('@');
                if (typeof msgArr[1] === "undefined")
                {
                    console.log(msgArr[0]);
                }
                else
                {
                    showErrorTips("#" + msgArr[1] + "_" + type, msgArr[0]);
                }
                return false;
            }

            if (typeof res.forwardPage === "undefined" || res.forwardPage === '')
            {
                res.forwardPage = '/reg.php';
            }

            if (typeof res.loadPage !== "undefined" && res.loadPage !== '')
            {
                setTimeout(function() {
                    window.location = res.forwardPage;
                }, 1000);
                $.getScript(res.loadPage, function() {
                    window.location = res.forwardPage;
                });
            }
            else
            {
                window.location = res.forwardPage;
            }
            return false;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });
}

(function ($) {
    var getScript = function (url, callback, options) {
        var settings  = $.extend({
            'url': url,
            'success' : callback || function () {},
            'dataType' : 'script'
        }, options || {});
        $.ajax(settings);
    };

    $.getScript = getScript;
}($ || Zepto));

function login(n, username)
{
    if (n == 2)
    {
        window.location.href = "http://login.duote.com/login.php";
    }
    else if (n == 3)
    {
        window.location.href = "http://bbs.haozip.com/login.php";
    }
    else if (n == 4)
    {
        window.location.href = "http://bbs.shanhu99.com/logging.php?action=login";
    }
    else if (n == 5)
    {
        window.location.href = "http://ie.2345.com/bbs/logging.php?action=login";
    }
    else
    {
        if (username)
        {
            window.location.href = "http://login.2345.com/login.php?username=" + username;
        }
        else
        {
            window.location.href = "http://login.2345.com/login.php";
        }
    }
}

