<?php
use Octopus\Router;
use WebLogger\Facade\LoggerFacade;
/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 文件名称：FuncAction.php
 * 摘    要：一些公用的功能
 * 作    者：杜海明
 * 修改日期：2015.04.16
 * */
class FuncAction extends Action
{
    /**
     * @param string            $url    url
     * @param string|array|null $data   data
     * @param array             $config config
     * @return array
     */
    public static function request(string $url, $data = null, array $config = [])
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        if ($data !== null)
        {
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        if (isset($config['referer']))
        {
            curl_setopt($ch, CURLOPT_REFERER, $config['referer']);
        }
        if (isset($config['header']))
        {
            curl_setopt($ch, CURLOPT_HTTPHEADER, $config['header']);
        }
        if (isset($config['timeout']))
        {
            curl_setopt($ch, CURLOPT_TIMEOUT, $config['timeout']);
        }
        else
        {
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        }
        $sapi = PHP_SAPI === 'cli' ? 'cli_' : '';
        $startTimeStamp = microtime(true);
        $result = curl_exec($ch);
        $timeSpent = microtime(true) - $startTimeStamp;
        $error = null;
        if (isset($config['log']) || curl_errno($ch))
        {
            $error = [
                'httpCode'  => curl_getinfo($ch, CURLINFO_HTTP_CODE),
                'timeSpent' => $timeSpent,
                'errno'     => curl_errno($ch),
                'error'     => curl_error($ch),
            ];
            LoggerFacade::info("error curl {$error['httpCode']} {$error['timeSpent']} {$error['errno']}: {$error['error']} {$url}", (array)$data);
            // $logPath = APPPATH . '/logs/' . date('Ymd');
            // if (!is_dir($logPath))
            // {
            //     mkdir($logPath, 0777, true);
            // }
            // error_log(
            //     date("[Y-m-d H:i:s]\n") . "{$error['httpCode']}\t{$error['timeSpent']}\t{$error['errno']}: {$error['error']}\n$url\n" . var_export($data, true) . "\n",
            //     3,
            //     $logPath . "/curl_error_{$sapi}" . date('H') . '.log'
            // );
        }
        curl_close($ch);
        return [$result, $error];
    }

    /**
     * 作 者：tianj<<EMAIL>>
     * 功 能: 一个简单的post方法
     * 修改日期：2012-10-29
     * @param string $url     post地址
     * @param array  $params  post内容
     * @param int    $timeout 超时时间
     * @param bool   $isFile  判断是否是文件上传
     * @param bool   $isJson  判断是否json
     * @return mixed
     */
    public static function postTimeout($url, $params = array(), $timeout = 3, $isFile = false, $isJson = false)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        $curlPost = '';
        if (is_array($params) && sizeof($params) >= 1 && !$isFile)
        {
            $paramsArr = array();
            foreach ($params as $k => $val)
            {
                $paramsArr[] = $k . '=' . urlencode($val);
            }
            $curlPost .= implode('&', $paramsArr);
        }
        else
        {
            $curlPost = $params;
        }
        $isJson ? curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json;')) : null;
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        //curl_setopt($ch, CURLOPT_REFERER, 'http://' . JIFEN_DOMAIN . '/api/');
        curl_setopt($ch, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
        if (isset($params['flag']) && $params['flag'])
        {
            //curl_setopt($ch, CURLOPT_USERAGENT, JIFEN_DOMAIN );
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 6);
        }

        $_start_time = microtime(true);
        $data = curl_exec($ch);
        $_total_exec = microtime(true) - $_start_time;
        $sapi = PHP_SAPI === 'cli' ? 'cli_' : '';
        // 超时1s日志
        if ($_total_exec > 1)
        {
            // $logPath = APPPATH . '/logs/' . date('Ymd');
            // if (!is_dir($logPath))
            // {
            //     mkdir($logPath, 0777);
            // }
            // $params = is_array($params) ? http_build_query($params) : $params;

            LoggerFacade::info("curl over 1s  {$_total_exec} {$url}", (array)$params);

            // error_log(
            //     date('Y-m-d H:i:s') . "\t[]\t\t \r\n",
            //     3,
            //     $logPath . "/curl_timeout_{$sapi}" . date('H') . '.log'
            // );
        }
        // 错误日志
        if (!$data)
        {
            // $logPath = APPPATH . '/logs/' . date('Ymd');
            // if (!is_dir($logPath))
            // {
            //     mkdir($logPath, 0777);
            // }
            $errInfo = curl_getinfo($ch);
            LoggerFacade::error("error curl " . curl_error($ch) . json_encode($errInfo)  . "[$_total_exec] {$url}", (array)$params);
        }

        // 错误日志
        if (date('YmdHi') > 201710131700 && date('YmdHi') < 201710131759)
        {
            // $logPath = APPPATH . '/logs/' . date('Ymd');
            // if (!is_dir($logPath))
            // {
            //     mkdir($logPath, 0777);
            // }
            LoggerFacade::error("{$_total_exec} {$url}", ['params' => $params, 'res' => $data]);
        }

        curl_close($ch);

        return $data;
    }

    /**
     * 函数名称：get
     * 参    数：
     * 作    者：杜海明
     * 功    能：模拟get提交
     * 修改日期：2015-04-16
     */
    public static function get($url, $timeout = 5)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        $getResult = curl_exec($ch);
        curl_close($ch);
        return $getResult;
    }

    /**
     * 函数名称：checkIsIp
     * 参    数：
     * 作    者：杜海明
     * 功    能：验证IP
     * 修改日期：2015-06-16
     * */
    public static function checkIsIp($ips)
    {
        if (empty($ips))
        {
            return true;
        }
        if (is_string($ips))
        {
            $data = filter_var($ips, FILTER_VALIDATE_IP);
            if ($data === false)
            {
                return false;
            }
        }
        elseif (is_array($ips))
        {
            foreach ($ips as $ipsInfo)
            {
                if (empty($ipsInfo))
                {
                    continue;
                }
                $data = filter_var($ipsInfo, FILTER_VALIDATE_IP);
                if ($data === false)
                {
                    return false;
                }
            }
        }
        else
        {
            return false;
        }
        return true;
    }

    /**
     * @return string
     */
    public static function getIp()
    {
        $ip = "";

        if (!empty($_SERVER['HTTP_CLIENT_IP']))
        {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        }
        elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))
        {
            // to check ip is pass from proxy
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
            if (strpos($ip, ",") !== false)
            {
                $ips = explode(",", $ip);
                $ip = $ips[0];
            }
        }
        elseif (!empty($_SERVER['HTTP_X_FORWARDED']))
        {
            $ip = $_SERVER['HTTP_X_FORWARDED'];
        }
        elseif (!empty($_SERVER['HTTP_X_CLUSTER_CLIENT_IP']))
        {
            $ip = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
        }
        elseif (!empty($_SERVER['HTTP_FORWARDED_FOR']))
        {
            $ip = $_SERVER['HTTP_FORWARDED_FOR'];
        }
        elseif (!empty($_SERVER['HTTP_FORWARDED']))
        {
            $ip = $_SERVER['HTTP_FORWARDED'];
        }
        else
        {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        return $ip;
    }


    /**
     *参    数：无
     *作    者：shyl
     *功    能：是不是手机号码
     *修改时间：2014/5/12
     */
    static public function isTelNum($tel)
    {
        return preg_match("/^1[34587][0-9]{9}$/", $tel);
    }


    /**
     *参    数：
     *作    者：shyl
     *功    能：记录请求接口log
     *修改时间：2014/8/6
     */
    static public function writeApiLog($info)
    {
        $dir = 'log/';
        if (!is_dir($dir))
        {
            mkdir($dir);
        }
        $file = $dir . date('Y-m-d') . '.txt';
        $fp = fopen($file, 'a+');
        $content = $_SERVER['REMOTE_ADDR'] . ' - - ';
        $content .= '[' . date('Y/m/d H:i:s') . '] ';
        $content .= '"' . $info['from'] . '/' . $info['passid'] . '/' . $info['phone'] . '/' . '" ';
        $content .= $info['code'] . PHP_EOL;
        fwrite($fp, $content);
        fclose($fp);
    }

    /**
     * 判断是否移动手机号
     * @param string $phone phone
     * @return bool
     */
    public static function isMPhone($phone)
    {
        $patternChina = '%(^(134|135|136|137|138|139|148|150|151|152|157|158|159|165|172|182|183|184|187|188|178|147|1703|1705|1706|179|198|1440|197|195)[\d]{7,8})%';
        $patternGlobal = '%(^00[\d]*$)%';
        return (strlen($phone) === 11 && preg_match($patternChina, $phone)) || preg_match($patternGlobal, $phone);
    }

    /**
     * 判断是否是电信手机号  192为广电号码段暂时加在电信号段中
     * @param string $phone phone
     * @return bool
     */
    public static function isChinaTelecomPhone($phone)
    {
        $pattern = '%(^(133|153|180|149|181|189|1700|1701|1702|173|177|199|191|1410|1740|190|192|193|162)[\d]{7,8})%';
        return (strlen($phone) === 11 && preg_match($pattern, $phone));
    }

    /**
     * 判断是否是联通手机号
     * @param string $phone phone
     * @return bool
     */
    public static function isChinaUnicomPhone($phone)
    {
        $pattern = '%(^(130|131|132|155|156|185|186|175|176|145|1704|1707|1708|1709|171|166|146|167|196)[\d]{7,8})%';
        return (strlen($phone) === 11 && preg_match($pattern, $phone));
    }

    /**
     * 抓取接口500 错误
     * */
    public function setHttpError()
    {
        register_shutdown_function(function () {
            $error = error_get_last();
            $levenArr = array(1, 4, 16, 64, 256, 4096, E_ALL);
            if (!empty($error) && in_array($error['type'], $levenArr))
            {
                // $logPath = APPPATH . '/logs/' . date('Ymd');
                // if (!is_dir($logPath))
                // {
                //     mkdir($logPath, 0777);
                // }
                $errorLogPath = $logPath . '/shutdown_' . date('H') . '.log';
                $msg = date('Y-m-d H:i:s') . "\r\n" . var_export($error, true) . "\r\n" . var_export($_GET, true) . "\r\n" . var_export($_POST, true) . "\r\n" . str_repeat('-', 40) . "\r\n";
                LoggerFacade::info("error register settHttpError {$msg}");
            }
        });
    }

    /**
     * 请求写入
     * */
    public function setRequest()
    {
//        LoggerFacade::info('FunAction setRequest', (array)$_POST);
        $inputValue = $_POST;
        $className = substr(Router::fetchClass(), 0, - 10); //替换controller
        $methodName = substr(Router::fetchMethod(), 6, strlen(Router::fetchMethod())); //action;
        $pid =  $inputValue['pid'] ?? "ZT";
        $data = [
            'logUsage' => 'usageStatistics',
            'system'   => '短信平台',
            'product'  => $pid,
            'app'      => $pid,
            'module'   => $className,
            'function' => $methodName,
            'version'  => '',
            'count'    => 1,
        ];
        LoggerFacade::info("【统计】项目通知次数埋点", $data);
    }
}
