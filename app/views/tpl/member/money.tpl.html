{{include file = "public/mem_header.tpl.html"}}
{{include file = "public/left_nav.tpl.html}}

<!--主体 begin-->
<div class="main main_position clearfix">
    <!--主体左侧 开始-->
    <!--#include virtual="include/left_nav.htm"-->
    <!--主体左侧 结束-->


    <!--主体右侧 开始-->
    <div class="account_right" style=" height:500px;">

        <!--我的钱袋 开始-->
        <div class="myBag">
            <h2 class="pf">我的钱袋</h2>
            <ul>
                {{ if $pageArray.hasCode==1 }}
                <li {{ if $pageArray.txExpire }} class="overdue" {{/if}}>
                    <a class="txBg" href="https://recharge.8566.com/tencent/10002" target="_blank">
                        <p class="getCard">
                            <span href="javascript:;" class="btnCoupon">查看</span>
                        </p>
                    </a>
                </li>
                {{/if}}
                <li>
                    <img class="pt15" src="/images/v3/userindex_v3/mq.png" alt="秒钱">
                    <p>
                        <em class="f15">你负责赚钱  我帮你生钱</em>
                        <!-- <span> </span> -->
                    </p>
                <div class="getCard pt15">
                    <a href="https://www.miaoqian.com/?promof4c=MjAxODAxMTkxMTA0MDIwMDAwNTQ3%0D%0A" target="_blank" class="btnCoupon">查看</a>
                    <a href="javascript:;" class="btnUseLog" id="mqUseLog">使用说明</a>
                </div>
                </li>

            </ul>
        </div><!--changeEmail-A end-->
        <!--我的钱袋 结束-->

    </div><!--account_right end-->
    <!--主体右侧 结束-->

</div><!--main end-->
<!--主体 end-->

</div><!--main end-->
<!--主体 end-->

<div class="box_mark" style="display:none;"></div>
<!--弹窗 begin-->



<!--爱奇艺优惠券弹窗 begin-->
<div class="pop-layer couponAqy" style="display: none;">
    <div class="modal-pop m-shadow show">
        <div class="m-bg">
            <div class="modal-hd">
                <h3 class="p-h-txt">领取优惠券</h3>
                <span class="extend"><a href="javascript:;" class="close" title="点击关闭"></a></span>
            </div>
            <div class="modal-bd">
                <div class="con-center">
                    <p class="txt-h1 tcenter">优惠券领取成功！</p>
                    <p class="txt-h3 pt8">2345专享爱奇艺优惠券</p>
                    <div class="code-input mt10">
                        <div class="input-widget-center">
                            <input type="text" id="aqy_text" class="input-txt" value="" readonly='true'>
                        </div>
                    </div>
                    <p class="txt-h5">
                        <a href="javascript:;" id="copy-buttonAqy" class="c-blue zeroclipboard-is-hover" title="单击复制到剪贴板" data-clipboard-target="aqy_text" data-clipboard-text="">[复制激活码]</a>
                    </p>
                    <div class="p-btn-grp tleft mt25">
                        <a href="http://www.iqiyi.com/?f=passport2345" class="btn-m-orange" target="_blank" onclick="cc('D12')">立即兑换</a>
                        <a href="javascript:;" class="btn-m-blue ml20 close" onclick="cc('D13')">我的钱袋</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--爱奇艺优惠券弹窗 end-->

<!--爱奇艺优惠券使用说明弹窗 start-->
<div class="pop-layer userLogAqy" style="display: none;">
    <div class="modal-pop modal-popW m-shadow show">
        <div class="m-bg">
            <div class="modal-hd">
                <h3 class="p-h-txt">爱奇艺优惠券使用说明</h3>
                <span class="extend"><a href="javascript:;" class="close" title="点击关闭"></a></span>
            </div>
            <div class="modal-useLog">
                <div class="floatL">
                    <em>使用流程：</em>
                    <div class="numData">
                        <i class="bgtop"></i>
                        <span><i>1</i>点击“立即领取”</span>
                        <span><i>2</i>点击“复制激活码”</span>
                        <span><i>3</i>点击“立即兑换”</span>
                        <span><i>4</i>点击“开通VIP”</span>
                        <span><i>5</i>选择12个月/3个月/1个月会员</span>
                        <span><i>6</i>粘贴激活码至代金券</span>
                        <span><i>7</i>输入验证码</span>
                        <span><i>8</i>支付使用</span>
                        <i class="bgbot"></i>
                    </div>
                </div>
                <div class="floatL">
                    <em>活动Tips：</em>
                    <div class="activeTipsA">
                        <p><i>·</i><span>该优惠券适用于黄金VIP新会员；</span></p>
                        <p><i>·</i><span>该优惠券暂不支持IOS端；</span></p>
                        <p><i>·</i><span>该优惠券暂不能跟爱奇艺会员其他优惠活动一起使用；</span></p>
                        <p><i>·</i><span>该活动最终解释权归2345网络科技有限公司所有</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--爱奇艺优惠券使用说明弹窗 end-->


<!--秒钱优惠券使用说明 start-->
<div class="pop-layer userLogMq" style="display: none;">
    <div class="modal-pop modal-popW m-shadow show">
        <div class="m-bg">
            <div class="modal-hd">
                <h3 class="p-h-txt">秒钱优惠券使用说明</h3>
                <span class="extend"><a href="javascript:;" class="close" title="点击关闭"></a></span>
            </div>
            <div class="modal-useLog2">
                <div class="floatAll userLogW">
                    <div class="numData">
                        <i class="bgtop"></i>
                        <span><i>·</i>注册送988元/288元/88/18元红包，满足条件4个红包可分次使用，投资可抵现，到期即可提现。</span>
                        <span><i>·</i>新用户专享返现福利：新手标每加入1万送50元现金，多投多送。返现金额将在加入后24小时内返至账户余额；</span>
                        <span><i>·</i>用户完成首次投资，额外赠送一张2%加息卡；</span>
                        <span><i>·</i>红包，加息卡等其他优惠券不可同时使用；</span>
                        <span><i>·</i>红包，加息卡有效期、抵扣金额以券面显示为准，具体使用规则请进入个人账户中查看；</span>
                        <span><i>·</i>本活动最终解释权归秒钱所有。如有疑问请咨询在线客服或拨打400-888-9896</span>
                        <i class="bgbot"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--秒钱优惠券使用说明 end-->



<!--芒果金融弹窗 begin-->
<div class="pop-layer couponMgjr" style="display: none;">
    <div class="modal-pop m-shadow show">
        <div class="m-bg">
            <div class="modal-hd">
                <h3 class="p-h-txt">使用说明</h3>
                <span class="extend"><a href="javascript:;" class="close" title="点击关闭"></a></span>
            </div>
            <div class="modal-bd">
                <div class="con-center">
                    <p class="txt-h1 tcenter"><b>请详见芒果金融活动首页</b></p>

                    <div class="p-btn-grp mt25">
                        <a href="https://www.hnmgjr.com/activitylist/20160608.html?hmsr=2345&hmpl=LOGO&hmcu=&hmkw=&hmci=" target="_blank" class="btn-m-blueB ml20">前往芒果金融首页</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--芒果金融弹窗 end-->

{{include file = "public/qrcode.tpl.html"}}

<script type="text/javascript" src="/js/lib/ZeroClipboard.min.js"></script>


<script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>

<script type="text/javascript" src="/js/common.js"></script>


<script>
    //芒果金融弹窗
    $("#mgjrCoupon").click(function(){
        var w_h = $(window).height();
        var h = (w_h-311)/2;
        $(".couponMgjr .modal-pop").css('margin-top', h + 'px');
        $(".couponMgjr").height(w_h).show();
    })
    function copyCode(id){
        if (window.clipboardData) {
            id.live('click', function(){
                var val = $(this).attr('data-clipboard-text');
                window.clipboardData.setData("Text", val);
                alert('已经成功复制到剪贴板');
            });
        } else {
            ZeroClipboard.config( {
                swfPath: '/js/lib/ZeroClipboard.swf',
                moviePath: '/js/lib/ZeroClipboard.swf',
                forceHandCursor: true
            } );
            var clip = new ZeroClipboard(id);
            // console.log(clip)
            clip.on('load', function () {
                this.on('complete', function () {
                    alert('已经成功复制到剪贴板');
                });
            });
            clip.on( 'wrongflash', function() {
                ZeroClipboard.destroy();
                alert("您的flash播放器版本过低,请升级后重试。");
            });
            clip.on( 'noflash', function() {
                ZeroClipboard.destroy();
                $('#copy-buttonAqy').hide().after('<p style="color:#999;font-size:12px;line-height:18px;">您的系统没有安装或者禁用了flash player插件，请开启后重试或手动复制。</p>')
            });
            $(window).resize(function(){
                clip.reposition();
            });
        }
    }

    copyCode(document.getElementById("copy-buttonAqy"));

</script>
<script type="text/javascript">


    //领取爱奇艺优惠券弹窗
    $("#aqyCoupon").click(function(){

        var gameId=0
        var api = "/webapi/thirdParty/AiQiYIVoucher/";
        var posting = $.post(api, {gameId: gameId});
        posting.done(function (data) {

            if (data.hasOwnProperty("code")) {
                if (data.code.toString() === "1") {
                    var w_h = $(window).height();
                    var h = (w_h - 311) / 2;
                    $("#libao_game_name").html(data.data.name);

                    $("#aqy_text").val(data.data.code);
                    $("#copy-buttonAqy").attr("data-clipboard-text", data.data.libao);


                    $(".couponAqy .modal-pop").css('margin-top', h + 'px');
                    $(".couponAqy").height(w_h).show();
                } else if (data.code.toString() === "-1") {
                    alert(data.msg);
                    window.location.href = "/login";
                } else {
                    alert(data.msg);
                }
            }

        });
    });

    //贷款王优惠券使用说明弹窗
    $("#dkwUseLog").click(function(){
        var w_h = $(window).height();
        var h = (w_h-311)/2;
        $(".userLogDkw .modal-pop").css('margin-top', h + 'px');
        $(".userLogDkw").height(w_h).show();
    });

    //爱奇艺优惠券使用说明弹窗
    $("#aqyUseLog").click(function(){
        var w_h = $(window).height();

        var h = (w_h-311)/2;
        $(".userLogAqy .modal-pop").css('margin-top', h + 'px');
        $(".userLogAqy").height(w_h).show();
    });

    $(".close").click(function(){
        $(this).parents(".pop-layer").hide().height(0);;
    });


    //秒钱弹窗
    $("#mqUseLog").click(function(){
        var w_h = $(window).height();
        var h = (w_h-311)/2;
        $(".userLogMq .modal-pop").css('margin-top', h + 'px');
        $(".userLogMq").height(w_h).show();
    });

</script>




{{include file = "public/mem_footer.tpl.html"}}
