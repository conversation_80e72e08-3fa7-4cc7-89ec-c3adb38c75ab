{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "bbf36c3089efa1f77705b3120928d5d0", "packages": [{"name": "Octopus/Badwords", "version": "1.1.0", "source": {"type": "git", "url": "ssh://******************/octopus/Badwords.git", "reference": "4edbb540deaeec97b99ff157264e0328df2ba001"}, "dist": {"type": "zip", "url": "http://packagist.2345.com/repo/private/dists/octopus/badwords/*******/4edbb540deaeec97b99ff157264e0328df2ba001.zip", "reference": "4edbb540deaeec97b99ff157264e0328df2ba001", "mirrors": [{"url": "http://packagist.2345.com/repo/private/dists/%package%/%version%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"Octopus\\": "src/"}}, "time": "2016-09-18T07:41:15+00:00"}, {"name": "Octopus/Logger", "version": "1.1.0", "source": {"type": "git", "url": "ssh://******************/octopus/Logger.git", "reference": "f14298eed4a5273ab4f01bb0741b9b4d1dc7e1ca"}, "dist": {"type": "zip", "url": "http://packagist.2345.com/repo/private/dists/octopus/logger/*******/f14298eed4a5273ab4f01bb0741b9b4d1dc7e1ca.zip", "reference": "f14298eed4a5273ab4f01bb0741b9b4d1dc7e1ca", "mirrors": [{"url": "http://packagist.2345.com/repo/private/dists/%package%/%version%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "type": "library", "autoload": {"psr-4": {"Octopus\\": "src/"}}, "time": "2015-07-01T06:29:38+00:00"}, {"name": "Octopus/PdoEx", "version": "1.0.16", "source": {"type": "git", "url": "ssh://******************/octopus/PdoEx.git", "reference": "466a6194f8456311419073b74f9c57848cbc1919"}, "dist": {"type": "zip", "url": "http://packagist.2345.com/repo/private/dists/octopus/pdoex/********/466a6194f8456311419073b74f9c57848cbc1919.zip", "reference": "466a6194f8456311419073b74f9c57848cbc1919", "mirrors": [{"url": "http://packagist.2345.com/repo/private/dists/%package%/%version%/%reference%.%type%", "preferred": true}]}, "require": {"octopus/logger": "1.1.0", "php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"Octopus\\": "src/"}}, "time": "2016-05-10T06:39:22+00:00"}, {"name": "OctopusUtf8/Kafka", "version": "1.1.0", "source": {"type": "git", "url": "https://gitlab.2345.cn/packagist/octopus/utf8/kafka.git", "reference": "0867aeca971a80ca1f2d666cc89c07c97174873e"}, "dist": {"type": "zip", "url": "http://packagist.2345.com/repo/private/dists/octopusutf8/kafka/*******/0867aeca971a80ca1f2d666cc89c07c97174873e.zip", "reference": "0867aeca971a80ca1f2d666cc89c07c97174873e", "mirrors": [{"url": "http://packagist.2345.cn/repo/private/dists/%package%/%version%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.8"}, "type": "library", "autoload": {"psr-4": {"Octopus\\": "class/src/"}}, "license": ["MIT"], "authors": [{"name": "gaox", "email": "<EMAIL>"}], "description": "Rdkafka编辑器类提示、class类使用封装", "time": "2019-06-04T09:11:29+00:00"}, {"name": "OctopusUtf8/deploySource", "version": "v1.1.1", "source": {"type": "git", "url": "ssh://******************/support/zctools/deploySource.git", "reference": "60c1860a240124048c9b19d0d7024f344be7e265"}, "dist": {"type": "zip", "url": "http://packagist.2345.com/repo/private/dists/octopusutf8/deploysource/*******/60c1860a240124048c9b19d0d7024f344be7e265.zip", "reference": "60c1860a240124048c9b19d0d7024f344be7e265", "mirrors": [{"url": "http://packagist.2345.com/repo/private/dists/%package%/%version%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0"}, "type": "library", "autoload": {"psr-4": {"Octopus\\": "src/"}}, "time": "2020-02-25T03:41:01+00:00"}, {"name": "adbario/php-dot-notation", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/eee4fc81296531e6aafba4c2bbccfc5adab1676e", "reference": "eee4fc81296531e6aafba4c2bbccfc5adab1676e", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.0|^5.0|^6.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "time": "2019-01-01T23:59:15+00:00"}, {"name": "alextartan/guzzle-psr18-adapter", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/alextartan/guzzle-psr18-adapter.git", "reference": "d29d0c0423dae6cb8768f2153d0aeea384364ecb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alextartan/guzzle-psr18-adapter/zipball/d29d0c0423dae6cb8768f2153d0aeea384364ecb", "reference": "d29d0c0423dae6cb8768f2153d0aeea384364ecb", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3", "php": ">=7.4", "psr/http-client": "^1.0", "psr/http-message": "1.0.*"}, "require-dev": {"ext-mbstring": "*", "ext-xml": "*", "infection/infection": "^0.13.0", "php-coveralls/php-coveralls": "~2.2.0", "phpstan/phpstan": "~0.12.5", "phpstan/phpstan-phpunit": "~0.12.6", "phpstan/phpstan-strict-rules": "~0.12.1", "phpunit/phpunit": "~8.0", "roave/security-advisories": "dev-master", "squizlabs/php_codesniffer": "3.5.*"}, "type": "library", "autoload": {"psr-4": {"AlexTartan\\GuzzlePsr18Adapter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Lightweight wrapper over Guzzle to comply with PSR18", "keywords": ["Guzzle", "adapter", "psr18"], "support": {"issues": "https://github.com/alextartan/guzzle-psr18-adapter/issues", "source": "https://github.com/alextartan/guzzle-psr18-adapter/tree/3.0.1"}, "time": "2020-01-20T21:33:00+00:00"}, {"name": "alibabacloud/client", "version": "1.5.17", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php-client.git", "reference": "0bb50708178c086a754812ba739a4c4f22ef2ff4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php-client/zipball/0bb50708178c086a754812ba739a4c4f22ef2ff4", "reference": "0bb50708178c086a754812ba739a4c4f22ef2ff4", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.2", "clagiordano/weblibs-configmanager": "^1.0", "danielstjules/stringy": "^3.1", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3", "mtdowling/jmespath.php": "^2.4", "php": ">=5.5"}, "require-dev": {"composer/composer": "^1.8", "drupal/coder": "^8.3", "ext-dom": "*", "ext-pcre": "*", "ext-sockets": "*", "ext-spl": "*", "league/climate": "^3.2.4", "mikey179/vfsstream": "^1.6", "monolog/monolog": "^1.24", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Client\\": "src"}, "files": ["src/Functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud Client for PHP - Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "client", "cloud", "library", "sdk", "tool"], "time": "2019-09-16T12:01:10+00:00"}, {"name": "alibabacloud/sdk", "version": "1.7.112", "source": {"type": "git", "url": "https://github.com/aliyun/openapi-sdk-php.git", "reference": "4cbc0180bcc909bd1e9b0433c409c3bf6cf9576b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/openapi-sdk-php/zipball/4cbc0180bcc909bd1e9b0433c409c3bf6cf9576b", "reference": "4cbc0180bcc909bd1e9b0433c409c3bf6cf9576b", "shasum": ""}, "require": {"alibabacloud/client": "^1.5", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "php": ">=5.5"}, "replace": {"alibabacloud/aas": "self.version", "alibabacloud/actiontrail": "self.version", "alibabacloud/aegis": "self.version", "alibabacloud/afs": "self.version", "alibabacloud/airec": "self.version", "alibabacloud/alidns": "self.version", "alibabacloud/alikafka": "self.version", "alibabacloud/alimt": "self.version", "alibabacloud/aliprobe": "self.version", "alibabacloud/appmallsservice": "self.version", "alibabacloud/arms": "self.version", "alibabacloud/arms4finance": "self.version", "alibabacloud/baas": "self.version", "alibabacloud/batchcompute": "self.version", "alibabacloud/bss": "self.version", "alibabacloud/bssopenapi": "self.version", "alibabacloud/cas": "self.version", "alibabacloud/cbn": "self.version", "alibabacloud/ccc": "self.version", "alibabacloud/ccs": "self.version", "alibabacloud/cdn": "self.version", "alibabacloud/cds": "self.version", "alibabacloud/cf": "self.version", "alibabacloud/chatbot": "self.version", "alibabacloud/cloudapi": "self.version", "alibabacloud/cloudauth": "self.version", "alibabacloud/cloudesl": "self.version", "alibabacloud/cloudmarketing": "self.version", "alibabacloud/cloudphoto": "self.version", "alibabacloud/cloudwf": "self.version", "alibabacloud/cms": "self.version", "alibabacloud/commondriver": "self.version", "alibabacloud/companyreg": "self.version", "alibabacloud/cr": "self.version", "alibabacloud/crm": "self.version", "alibabacloud/cs": "self.version", "alibabacloud/csb": "self.version", "alibabacloud/cusanalyticsconline": "self.version", "alibabacloud/dataworkspublic": "self.version", "alibabacloud/dbs": "self.version", "alibabacloud/dcdn": "self.version", "alibabacloud/dds": "self.version", "alibabacloud/dm": "self.version", "alibabacloud/dmsenterprise": "self.version", "alibabacloud/domain": "self.version", "alibabacloud/domainintl": "self.version", "alibabacloud/drcloud": "self.version", "alibabacloud/drds": "self.version", "alibabacloud/dts": "self.version", "alibabacloud/dybaseapi": "self.version", "alibabacloud/dyplsapi": "self.version", "alibabacloud/dypnsapi": "self.version", "alibabacloud/dysmsapi": "self.version", "alibabacloud/dyvmsapi": "self.version", "alibabacloud/eci": "self.version", "alibabacloud/ecs": "self.version", "alibabacloud/ecsinc": "self.version", "alibabacloud/edas": "self.version", "alibabacloud/ehpc": "self.version", "alibabacloud/elasticsearch": "self.version", "alibabacloud/emr": "self.version", "alibabacloud/ess": "self.version", "alibabacloud/fnf": "self.version", "alibabacloud/foas": "self.version", "alibabacloud/ft": "self.version", "alibabacloud/gpdb": "self.version", "alibabacloud/green": "self.version", "alibabacloud/hpc": "self.version", "alibabacloud/hsm": "self.version", "alibabacloud/httpdns": "self.version", "alibabacloud/idst": "self.version", "alibabacloud/imagesearch": "self.version", "alibabacloud/imm": "self.version", "alibabacloud/industrybrain": "self.version", "alibabacloud/iot": "self.version", "alibabacloud/itaas": "self.version", "alibabacloud/ivision": "self.version", "alibabacloud/ivpd": "self.version", "alibabacloud/jaq": "self.version", "alibabacloud/jarvis": "self.version", "alibabacloud/jarvispublic": "self.version", "alibabacloud/kms": "self.version", "alibabacloud/linkedmall": "self.version", "alibabacloud/linkface": "self.version", "alibabacloud/linkwan": "self.version", "alibabacloud/live": "self.version", "alibabacloud/lubancloud": "self.version", "alibabacloud/lubanruler": "self.version", "alibabacloud/market": "self.version", "alibabacloud/mopen": "self.version", "alibabacloud/mts": "self.version", "alibabacloud/multimediaai": "self.version", "alibabacloud/nas": "self.version", "alibabacloud/netana": "self.version", "alibabacloud/nlp": "self.version", "alibabacloud/nlscloudmeta": "self.version", "alibabacloud/nlsfiletrans": "self.version", "alibabacloud/ocs": "self.version", "alibabacloud/oms": "self.version", "alibabacloud/ons": "self.version", "alibabacloud/openanalytics": "self.version", "alibabacloud/ossadmin": "self.version", "alibabacloud/ots": "self.version", "alibabacloud/petadata": "self.version", "alibabacloud/polardb": "self.version", "alibabacloud/productcatalog": "self.version", "alibabacloud/pts": "self.version", "alibabacloud/push": "self.version", "alibabacloud/pvtz": "self.version", "alibabacloud/qualitycheck": "self.version", "alibabacloud/ram": "self.version", "alibabacloud/rds": "self.version", "alibabacloud/retailcloud": "self.version", "alibabacloud/rkvstore": "self.version", "alibabacloud/ros": "self.version", "alibabacloud/rtc": "self.version", "alibabacloud/saf": "self.version", "alibabacloud/sas": "self.version", "alibabacloud/sasapi": "self.version", "alibabacloud/scdn": "self.version", "alibabacloud/schedulerx2": "self.version", "alibabacloud/skyeye": "self.version", "alibabacloud/slb": "self.version", "alibabacloud/smartag": "self.version", "alibabacloud/sms": "self.version", "alibabacloud/smsintl": "self.version", "alibabacloud/snsuapi": "self.version", "alibabacloud/sts": "self.version", "alibabacloud/taginner": "self.version", "alibabacloud/tesladam": "self.version", "alibabacloud/teslamaxcompute": "self.version", "alibabacloud/teslastream": "self.version", "alibabacloud/ubsms": "self.version", "alibabacloud/ubsmsinner": "self.version", "alibabacloud/uis": "self.version", "alibabacloud/vod": "self.version", "alibabacloud/vpc": "self.version", "alibabacloud/vs": "self.version", "alibabacloud/wafopenapi": "self.version", "alibabacloud/welfareinner": "self.version", "alibabacloud/xspace": "self.version", "alibabacloud/xtrace": "self.version", "alibabacloud/yqbridge": "self.version", "alibabacloud/yundun": "self.version"}, "require-dev": {"composer/composer": "^1.8", "league/climate": "^3.2.4", "phpunit/phpunit": "^4.8", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Alibaba Cloud SDK for PHP - Easier to Use Alibaba Cloud in your PHP project", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibaba", "alibabacloud", "<PERSON><PERSON><PERSON>", "cloud", "library", "sdk"], "time": "2019-10-08T08:50:20+00:00"}, {"name": "bjeavons/zxcvbn-php", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/bjeavons/zxcvbn-php.git", "reference": "dde9679ac7e906a241d74d32083fb03699d56b93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bjeavons/zxcvbn-php/zipball/dde9679ac7e906a241d74d32083fb03699d56b93", "reference": "dde9679ac7e906a241d74d32083fb03699d56b93", "shasum": ""}, "require": {"php": "^7.1", "symfony/polyfill-mbstring": ">=1.3.1"}, "require-dev": {"php-coveralls/php-coveralls": "*", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"ZxcvbnPhp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "See contributors", "homepage": "https://github.com/bjeavons/zxcvbn-php"}], "description": "Realistic password strength estimation PHP library based on Zxcvbn JS", "homepage": "https://github.com/bjeavons/zxcvbn-php", "keywords": ["password", "zxcvbn"], "time": "2020-04-29T17:39:34+00:00"}, {"name": "cache/adapter-common", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/php-cache/adapter-common.git", "reference": "8788309be72aa7be69b88cdc0687549c74a7d479"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-cache/adapter-common/zipball/8788309be72aa7be69b88cdc0687549c74a7d479", "reference": "8788309be72aa7be69b88cdc0687549c74a7d479", "shasum": ""}, "require": {"cache/tag-interop": "^1.0", "php": ">=7.4", "psr/cache": "^1.0 || ^2.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "psr/simple-cache": "^1.0"}, "require-dev": {"cache/integration-tests": "^0.17", "phpunit/phpunit": "^7.5.20 || ^9.5.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Cache\\Adapter\\Common\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/aequasi"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyholm"}], "description": "Common classes for PSR-6 adapters", "homepage": "http://www.php-cache.com/en/latest/", "keywords": ["cache", "psr-6", "tag"], "support": {"source": "https://github.com/php-cache/adapter-common/tree/1.3.0"}, "time": "2022-01-15T15:47:19+00:00"}, {"name": "cache/apcu-adapter", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/php-cache/apcu-adapter.git", "reference": "85992d2b8937788eacf0db02db89101c0dd9b111"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-cache/apcu-adapter/zipball/85992d2b8937788eacf0db02db89101c0dd9b111", "reference": "85992d2b8937788eacf0db02db89101c0dd9b111", "shasum": ""}, "require": {"cache/adapter-common": "^1.0", "php": ">=7.4", "psr/cache": "^1.0 || ^2.0", "psr/simple-cache": "^1.0"}, "provide": {"psr/cache-implementation": "^1.0", "psr/simple-cache-implementation": "^1.0"}, "require-dev": {"cache/integration-tests": "^0.17", "phpunit/phpunit": "^7.5.20 || ^9.5.10"}, "suggest": {"ext-apcu": "The extension required to use this pool."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Cache\\Adapter\\Apcu\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/aequasi"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyholm"}], "description": "A PSR-6 cache implementation using apcu. This implementation supports tags", "homepage": "http://www.php-cache.com/en/latest/", "keywords": ["apcu", "cache", "psr-6"], "support": {"source": "https://github.com/php-cache/apcu-adapter/tree/1.3.0"}, "time": "2022-08-15T08:04:52+00:00"}, {"name": "cache/tag-interop", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-cache/tag-interop.git", "reference": "b062b1d735357da50edf8387f7a8696f3027d328"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-cache/tag-interop/zipball/b062b1d735357da50edf8387f7a8696f3027d328", "reference": "b062b1d735357da50edf8387f7a8696f3027d328", "shasum": ""}, "require": {"php": "^5.5 || ^7.0 || ^8.0", "psr/cache": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Cache\\TagInterop\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nicolas-grekas"}], "description": "Framework interoperable interfaces for tags", "homepage": "https://www.php-cache.com/en/latest/", "keywords": ["cache", "psr", "psr6", "tag"], "support": {"issues": "https://github.com/php-cache/tag-interop/issues", "source": "https://github.com/php-cache/tag-interop/tree/1.1.0"}, "time": "2021-12-31T10:03:23+00:00"}, {"name": "clagiordano/weblibs-configmanager", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/clagiordano/weblibs-configmanager.git", "reference": "6ef4c27354368deb2f54b39bbe06601da8c873a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clagiordano/weblibs-configmanager/zipball/6ef4c27354368deb2f54b39bbe06601da8c873a0", "reference": "6ef4c27354368deb2f54b39bbe06601da8c873a0", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"clagiordano/phpunit-result-printer": "^1", "phpunit/phpunit": "^4.8"}, "type": "library", "autoload": {"psr-4": {"clagiordano\\weblibs\\configmanager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "weblibs-configmanager is a tool library for easily read and access to php config array file and direct read/write configuration file / object", "keywords": ["clag<PERSON><PERSON><PERSON>", "configuration", "manager", "tool", "weblibs"], "time": "2019-09-25T22:10:10+00:00"}, {"name": "confidential/vault-php", "version": "0.0.6", "source": {"type": "git", "url": "https://gitlab.2345.cn/server/confidencial/vault-php.git", "reference": "a9b983b869ede92e5a3b3b224879cd0d49a4ecc1"}, "dist": {"type": "zip", "url": "http://packagist.2345.cn/repo/private/dists/confidential/vault-php/*******/a9b983b869ede92e5a3b3b224879cd0d49a4ecc1.zip", "reference": "a9b983b869ede92e5a3b3b224879cd0d49a4ecc1", "mirrors": [{"url": "http://packagist.2345.cn/repo/private/dists/%package%/%version%/%reference%.%type%", "preferred": true}]}, "require": {"alextartan/guzzle-psr18-adapter": "^4.0 || ^3.0", "ext-json": "*", "laminas/laminas-diactoros": "^2.14 ||^2.24 || ^3.0", "php": "^7.2 || ^8.0", "psr/cache": "^1.0|^2.0|^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/log": "^1.0|^2.0|^3.0"}, "require-dev": {"cache/apcu-adapter": "^1.3", "cache/array-adapter": "^1.0", "codeception/codeception": "^4.1", "codeception/module-asserts": "^1.3", "monolog/monolog": "^3.9", "php-vcr/php-vcr": "^1.5", "symfony/event-dispatcher": "<5.0"}, "suggest": {"cache/array-adapter": "For usage with CachedClient class"}, "type": "library", "autoload": {"psr-4": {"Vault\\": "src/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "yuzl", "email": "<EMAIL>"}], "description": "Best Vault client for PHP that you can find, fork from csharpru/vault-php", "keywords": ["hashicorp", "secrets", "vault"], "time": "2025-07-29T01:35:48+00:00"}, {"name": "danielst<PERSON>les/stringy", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/danielstjules/Stringy.git", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/danielstjules/Stringy/zipball/df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "reference": "df24ab62d2d8213bbbe88cc36fc35a4503b4bd7e", "shasum": ""}, "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "~1.1"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-4": {"Stringy\\": "src/"}, "files": ["src/Create.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.danielstjules.com"}], "description": "A string manipulation library with multibyte support", "homepage": "https://github.com/danielstjules/Stringy", "keywords": ["UTF", "helpers", "manipulation", "methods", "multibyte", "string", "utf-8", "utility", "utils"], "time": "2017-06-12T01:10:27+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.3.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/407b0cb880ace85c9b63c5f9551db498cb2d50ba", "reference": "407b0cb880ace85c9b63c5f9551db498cb2d50ba", "shasum": ""}, "require": {"guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4", "php": ">=5.5"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.0"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.3-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "time": "2018-04-22T15:46:56+00:00"}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "time": "2016-12-20T10:07:11+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "time": "2019-07-01T23:21:34+00:00"}, {"name": "jichupingtai/phpdotenv", "version": "2.5.0", "source": {"type": "git", "url": "******************:2345/phpdotenv.git", "reference": "391d3c537b7cc82cc0f9f42db24a6f122cab62d3"}, "dist": {"type": "zip", "url": "http://packagist.2345.com/repo/private/dists/jichupingtai/phpdotenv/*******/391d3c537b7cc82cc0f9f42db24a6f122cab62d3.zip", "reference": "391d3c537b7cc82cc0f9f42db24a6f122cab62d3", "mirrors": [{"url": "http://packagist.2345.cn/repo/private/dists/%package%/%version%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.9"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.vancelucas.com"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "time": "2019-04-15T01:22:14+00:00"}, {"name": "laminas/laminas-diactoros", "version": "2.17.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diactoros.git", "reference": "5b32597aa46b83c8b85bb1cf9a6ed4fe7dd980c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diactoros/zipball/5b32597aa46b83c8b85bb1cf9a6ed4fe7dd980c5", "reference": "5b32597aa46b83c8b85bb1cf9a6ed4fe7dd980c5", "shasum": ""}, "require": {"php": "^7.4 || ~8.0.0 || ~8.1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0"}, "conflict": {"zendframework/zend-diactoros": "*"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "ext-gd": "*", "ext-libxml": "*", "http-interop/http-factory-tests": "^0.9.0", "laminas/laminas-coding-standard": "^2.4.0", "php-http/psr7-integration-tests": "^1.1.1", "phpunit/phpunit": "^9.5.23", "psalm/plugin-phpunit": "^0.17.0", "vimeo/psalm": "^4.24.0"}, "type": "library", "extra": {"laminas": {"module": "Laminas\\Diactoros", "config-provider": "Laminas\\Diactoros\\ConfigProvider"}}, "autoload": {"files": ["src/functions/create_uploaded_file.php", "src/functions/marshal_headers_from_sapi.php", "src/functions/marshal_method_from_sapi.php", "src/functions/marshal_protocol_version_from_sapi.php", "src/functions/marshal_uri_from_sapi.php", "src/functions/normalize_server.php", "src/functions/normalize_uploaded_files.php", "src/functions/parse_cookie_header.php", "src/functions/create_uploaded_file.legacy.php", "src/functions/marshal_headers_from_sapi.legacy.php", "src/functions/marshal_method_from_sapi.legacy.php", "src/functions/marshal_protocol_version_from_sapi.legacy.php", "src/functions/marshal_uri_from_sapi.legacy.php", "src/functions/normalize_server.legacy.php", "src/functions/normalize_uploaded_files.legacy.php", "src/functions/parse_cookie_header.legacy.php"], "psr-4": {"Laminas\\Diactoros\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "PSR HTTP Message implementations", "homepage": "https://laminas.dev", "keywords": ["http", "laminas", "psr", "psr-17", "psr-7"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-diactoros/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-diactoros/issues", "rss": "https://github.com/laminas/laminas-diactoros/releases.atom", "source": "https://github.com/laminas/laminas-diactoros"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2022-08-30T17:01:46+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "52168cb9472de06979613d365c7f1ab8798be895"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/52168cb9472de06979613d365c7f1ab8798be895", "reference": "52168cb9472de06979613d365c7f1ab8798be895", "shasum": ""}, "require": {"php": ">=5.4.0", "symfony/polyfill-mbstring": "^1.4"}, "require-dev": {"composer/xdebug-handler": "^1.2", "phpunit/phpunit": "^4.8.36|^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"psr-4": {"JmesPath\\": "src/"}, "files": ["src/JmesPath.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "time": "2019-12-30T18:03:34+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.4.1", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "9256f12d8fb0cd0500f93b19e18c356906cbed3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/9256f12d8fb0cd0500f93b19e18c356906cbed3d", "reference": "9256f12d8fb0cd0500f93b19e18c356906cbed3d", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.5.6", "yoast/phpunit-polyfills": "^0.2.0"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "stevenmaguire/oauth2-microsoft": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "time": "2021-04-29T12:25:04+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "67d65b165b064235534ca2bc37290bf1d7ff8e75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/67d65b165b064235534ca2bc37290bf1d7ff8e75", "reference": "67d65b165b064235534ca2bc37290bf1d7ff8e75", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "license": ["MIT"], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2019-10-08T06:15:11+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "qiniu/php-sdk", "version": "v7.2.10", "source": {"type": "git", "url": "https://github.com/qiniu/php-sdk.git", "reference": "d89987163f560ebf9dfa5bb25de9bd9b1a3b2bd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qiniu/php-sdk/zipball/d89987163f560ebf9dfa5bb25de9bd9b1a3b2bd8", "reference": "d89987163f560ebf9dfa5bb25de9bd9b1a3b2bd8", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.3"}, "type": "library", "autoload": {"psr-4": {"Qiniu\\": "src/<PERSON>iu"}, "files": ["src/Qiniu/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.qiniu.com"}], "description": "Qiniu Resource (Cloud) Storage SDK for PHP", "homepage": "http://developer.qiniu.com/", "keywords": ["cloud", "qiniu", "sdk", "storage"], "time": "2019-10-28T10:23:23+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.12.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "b42a2f66e8f1b15ccf25652c3424265923eb4f17"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/b42a2f66e8f1b15ccf25652c3424265923eb4f17", "reference": "b42a2f66e8f1b15ccf25652c3424265923eb4f17", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.12-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2019-08-06T08:03:45+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.0", "ext-json": "*", "ext-curl": "*", "ext-openssl": "*"}, "platform-dev": [], "plugin-api-version": "2.0.0"}