<?php

namespace Admin;
use Controller;
class SpeechProjectController extends Controller
{
    public function actionIndex()
    {
        $pageArray = array();
        
        $TelephoneProjectModel = TelephoneProjectModel::getInstance();
        $projectList = $TelephoneProjectModel->getTelProject();

        foreach ($projectList as $projectKey => $projectInfo)
        {
            $projectList[$projectKey]['operate_date'] = $projectInfo['operate_time'] ? date("Y-m-d H:i:s",
                $projectInfo['operate_time']) : "";
            $projectList[$projectKey]['status_desc'] = $projectInfo['status'] == '1' ? '开启' :
                '关闭';
        }
        $pageArray['projectList'] = $projectList;


        loadView('Admin/speech_project_index.tpl.html', $pageArray);
    }


    public function actionSetTelProject()
    {
        $pageArray['channel'] = $this->Filter->getPost('channel');
        $pageArray['desc'] = $this->Filter->getPost('desc');
        $pageArray['number'] = $this->Filter->getPost('number');
        $pageArray['status'] = $this->Filter->getPost('status');
        $pageArray['voice'] = $this->Filter->getPost('voice');
        $pageArray['sms'] = $this->Filter->getPost('sms');
        $TelephoneProjectModel = TelephoneProjectModel::getInstance();
        $data = array(
            'channel' => $pageArray['channel'],
            'desc' => \EncodingAction::transcoding($pageArray['desc']),
            'number' => $pageArray['number'],
            'status' => $pageArray['status'],
            'voice' => $pageArray['voice'],
            'sms' => $pageArray['sms'],
            'operator' => UserAction::getAdminUserName());
        $isTrue = $TelephoneProjectModel->setTelProject($data);
        if ($isTrue)
        {
            return \MsgAction::returnJsonData(array('status' => 'P00001', 'msg' => '添加成功'));
        }
        else
        {
            return \MsgAction::returnJsonData(array('status' => 'P00007', 'msg' => '添加失败'));
        }
    }
    
    public function actionGetTelProjectInfo()
    {
        $channel = $this->Filter->getPost('channel');
        if (empty($channel))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00002', 'msg' => '缺少参数'));
        }
        $TelephoneProjectModel = TelephoneProjectModel::getInstance();
        $channelInfo = $TelephoneProjectModel->getTelProject(array('channel' => $channel,'row'=>'true'));
        if (empty($channelInfo))
        {
            return \MsgAction::returnJsonData(array('status' => 'P00003', 'msg' => '不存在的来源'));
        }
        return \MsgAction::returnJsonData(array('status' => 'P00001', 'data' => $channelInfo)); 
    }
}
