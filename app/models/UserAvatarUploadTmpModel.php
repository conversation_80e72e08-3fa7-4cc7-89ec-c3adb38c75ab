<?php
/**
 * 用户头像上传临时表，所有数据先入tmp表，由脚本进行归档
 * User: fuq
 * Date: 2020/04/29
 */
class UserAvatarUploadTmpModel extends Model
{
    private $pdo;
    private $tableName = 'user_avatar_upload_tmp';

    /**
     * 初始化
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
    }

    /**
     * 插入数据
     *
     * @param array $data 待记录数据
     *
     * @return
     */
    public function insert(array $data)
    {
        return $this->pdo->insert($this->tableName, $data);
    }

    /**
     * 获取某种审核状态的数据
     *
     * @param int $status   状态码
     * @param int $type     审核类型
     * @param int $limitNum 条数
     * @param int $fromId   数据起始id
     *
     * @return mixed
     */
    public function findAuditStatusData(int $status, int $type, int $limitNum = 100, int $fromId = 0)
    {
        $sql = "select * from {$this->tableName} where `id`> :id AND `approve_status`= :approve_status AND `approve_type`= :approve_type limit {$limitNum}";
        return $this->pdo->findAll($sql, [
            ":approve_status" => $status,
            ":approve_type" => $type,
            ":id" => $fromId,
        ]);
    }

    /**
     * 将tmp表数据整理后移入log表
     *
     * @param int    $delId     tmp表待删除id
     * @param string $groupName 生态名
     * @param array  $data      待入log表数据
     *
     * @return bool 返回结果
     */
    public function moveTmpAuditInfoToLog(int $delId, string $groupName, array $data)
    {
        $this->pdo->beginTransaction();
        // 删除tmp表
        $condition = array(
            "where"  => 'id = :id',
            "params" => array(
                ':id' => $delId,
            ),
        );

        $delRet = $this->pdo->delete($this->tableName, $condition);

        // 插入log表
        $userAvatarUploadLogModel = loadModel('userAvatarUploadLog');
        $logRet = $userAvatarUploadLogModel->addLog($groupName, $data['passid'], $data);

        if ($logRet === false || $delRet === false) {
            $this->pdo->rollBack();
            return false;
        }
        $this->pdo->commit();
        return true;
    }

    /**
     * 数据更新
     *
     * @param array $data      更新数据
     * @param array $condition 更新条件
     *
     * @return bool|int
     */
    public function update(array $data, array $condition)
    {
        return $this->pdo->update($this->tableName, $data, $condition);
    }

    /**
     * 查找单条记录
     *
     * @param string $taskId 阿里返回任务id
     *
     * @return array|bool
     */
    public function findOneByTaskId(string $taskId)
    {
        $sql = "SELECT * FROM {$this->tableName} WHERE aliyun_taskid = :taskId";
        $condition = [':taskId' => $taskId];

        return $this->pdo->find($sql, $condition);
    }

    /**
     * 获取监控数量的数据
     *
     * @param int    $status   状态码
     * @param string $time     时间
     *
     * @return mixed
     */
    public function monitorAuditStatusCount(int $status, string $time = '')
    {
        $condition = [":approve_status" => $status];
        $sql = "select count(`id`) num from {$this->tableName} where `approve_status`= :approve_status";

        if (!empty($time)) {
            $sql = $sql . ' AND `create_time` <= :create_time';
            $condition[":create_time"] = $time;
        }

        $ret = $this->pdo->find($sql, $condition);
        $ret = (int) ($ret['num'] ?? 0);

        return $ret;
    }

    /**
     * 将数据重新审核，并删除归档数据
     *
     * @param string $groupName 生态名
     * @param array  $data      待入log表数据
     *
     * @return bool 返回结果
     */
    public function addTmpAuditAndDelLog(string $groupName, array $data)
    {
        $this->pdo->beginTransaction();

        // 重新审核
        $addRet = $this->pdo->insert($this->tableName, $data);
        // 删除log表
        $condition = array(
            "where"  => 'avatar_name = :avatar_name',
            "params" => array(
                ':avatar_name' => $data['avatar_name'],
            ),
        );
        /** @var UserAvatarUploadLogModel $userAvatarUploadLogModel */
        $userAvatarUploadLogModel = loadModel('userAvatarUploadLog');
        $delRet = $userAvatarUploadLogModel->delLog($groupName, $data['passid'], $condition);

        if ($addRet === false || $delRet === false) {
            $this->pdo->rollBack();
            return false;
        }
        $this->pdo->commit();
        return true;
    }
}
