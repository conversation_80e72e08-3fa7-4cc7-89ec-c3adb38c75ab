<?php

/**
 * 定时set key
 * 手机白名单  手机黑名单 IP白名单
 * /opt/case/smsp.2345.net/console/crontab.sh SyncBlackPhone
 * /opt/case/smsp.2345.net/console/crontab.sh SyncIpWriteList
 * 
 * */

namespace Crontab;
use Controller;

class SmsSetKeyController extends Controller
{
    /**
     * 后台写入手机黑名名单KEY
     * */
    public function actionSmsSyncBlackPhoneKey()
    {
        $SmsSetKeyAction = SmsSetKeyAction::getInstance();
        $SmsSetKeyAction->SmsSyncBlackPhoneKey();
    }

    /**
     * 设置IP白名单
     * */
    public function actionSmsSyncIpWriteList()
    {
        $SmsSetKeyAction = SmsSetKeyAction::getInstance();
        $SmsSetKeyAction->SmsSyncIpWriteList();
    }

}

?>