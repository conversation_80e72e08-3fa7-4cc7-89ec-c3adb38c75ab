<?php

namespace Admin;

use Controller;

class TagTotalController extends Controller
{
    /**
     * @param array $cronParams cronParams
     * @return void
     */
    public function actionIndex($cronParams = null)
    {
        $redis = \RedisAction::connect();
        $pageArray = array();
        if (!isset($cronParams))
        {
            $pageArray['startTime'] = $this->Filter->getPost('startTime');
            $pageArray['endTime'] = $this->Filter->getPost('endTime');
            $act = $this->Filter->getPost('act');
            $pageArray['useCache'] = $this->Filter->getPost('useCache');
            $fromClick = $this->Filter->getPost('fromClick');
        }
        else
        {
            $pageArray = $cronParams;
            $pageArray['useCache'] = 0;
        }
        if (empty($pageArray['startTime']) || empty($pageArray['endTime']))
        {
            $pageArray['startTime'] = date('Y-m-d', strtotime('-5 day'));
            $pageArray['endTime'] = date('Y-m-d', strtotime('-1 day'));
        }
        $startTimeStamp = strtotime($pageArray['startTime']);
        $endTimeStamp = strtotime($pageArray['endTime']);

        if ($pageArray['useCache'] === false && $fromClick === false)
        {
            $pageArray['useCache'] = '1';
        }
        elseif ($pageArray['useCache'] === false)
        {
            $pageArray['useCache'] = '0';
        }

        $TagListModel = TagListModel::getInstance();
        $tagArr = $TagListModel->getTagList();
        $tagList = array();

        foreach ($tagArr as $tagInfo)
        {
            $tagList[$tagInfo['mid']] = $tagInfo['tag_name'];
        }
        $pageArray['tagList'] = $tagList;
        $tagTotalList = [];
        $total = [];
        $totalCount = array('total' => 0);

        for ($i = $startTimeStamp; $i <= $endTimeStamp; $i += 86400)
        {
            $theDate = date('Y-m-d', $i);
            $redisKey = "sms_addup_cache:tag:{$theDate}";
            if ($pageArray['useCache'] === '1' && $redis->exists($redisKey))
            {
                $tagTotal = unserialize($redis->get($redisKey));
                $tagTotalList[$theDate]['useCache'] = 1;
            }
            else
            {
                $queryData = array('startTime' => date('Y-m-d 00:00:00', $i), 'endTime' => date('Y-m-d 23:59:59', $i));
                $tagTotal = $TagListModel->getTagLogTotal($queryData);
                $tagTotalList[$theDate]['useCache'] = 0;
                if ($theDate != date("Y-m-d"))
                {
                    //结果储存至redis，保存半年
                    $redis->setex($redisKey, 180 * 86400, serialize($tagTotal));
                }
            }
            $total[$theDate] = 0;
            if (!empty($tagTotal))
            {
                foreach ($tagTotal as $tagTotalInfo)
                {
                    if ($tagTotalInfo['add_time'] != $theDate)
                    {
                        continue;
                    }
                    $tagTotalList[$theDate][$tagTotalInfo['mid']] = $tagTotalInfo;
                    $total[$theDate] += $tagTotalInfo['total'];
                    if (!isset($totalCount[$tagTotalInfo['mid']]))
                    {
                        $totalCount[$tagTotalInfo['mid']] = 0;
                    }
                    $totalCount[$tagTotalInfo['mid']] += $tagTotalInfo['total'];
                    $totalCount['total'] += $tagTotalInfo['total'];
                }
            }
        }
        if ($cronParams != null)
        {
            return;
        }
        krsort($tagTotalList);
        $pageArray['tagTotalList'] = $tagTotalList;
        $pageArray['total'] = $total;
        $pageArray['totalCount'] = $totalCount;
        if (isset($act) && $act === 'print')
        {
            echo "=======<br/>";
            var_export($tagTotalList);
            exit;
        }
        if (isset($act) && $act === 'export')
        {
            $filename = \EncodingAction::transcoding('用户中心发送量_', 'utf-8') . date('Y-m-d');
            AutoMakeCsvAction::init($filename);
            $fieldArr = array('日期');
            foreach ($pageArray['tagList'] as $tagListInfo)
            {
                if (!empty($tagListInfo))
                {
                    $fieldArr[] = $tagListInfo;
                }
            }
            $fieldArr[] = '总计';
            AutoMakeCsvAction::setField($fieldArr);

            foreach ($tagTotalList as $tagTotalListKey => $tagTotalListInfo)
            {
                $writeArr['date'] = $tagTotalListKey;
                foreach ($pageArray['tagList'] as $tagListKey => $tagListInfo)
                {
                    $writeArr[$tagListInfo] = $tagTotalListInfo[$tagListKey]['total'];
                }
                $writeArr['total'] = $total[$tagTotalListKey];
                AutoMakeCsvAction::writeData($writeArr);
            }

            $totalField = array('');
            foreach ($pageArray['tagList'] as $tagListKey => $tagListInfo)
            {
                $totalField[$tagListInfo] = $totalCount[$tagListKey];
            }
            $totalField['total'] = $totalCount['total'];
            AutoMakeCsvAction::writeData($totalField);
            AutoMakeCsvAction::headerLocationCsv();
        }
        loadView('Admin/tag_total_index.tpl.html', $pageArray);
    }


    public function actionTagPostionTotal()
    {
        $pageArray = array();

        $pageArray['startTime'] = $this->Filter->getPost('startTime');
        $pageArray['endTime'] = $this->Filter->getPost('endTime');
        $act = $this->Filter->getPost('act');
        if (empty($pageArray['startTime']) || empty($pageArray['endTime']))
        {
            $pageArray['startTime'] = date('Y-m-d', strtotime('-1 day'));
            $pageArray['endTime'] = date('Y-m-d');
        }
        $TagListModel = TagListModel::getInstance();
        $tagArr = $TagListModel->getTagList();
        $tagList = array();

        foreach ($tagArr as $tagInfo)
        {
            $tagList[$tagInfo['mid']] = $tagInfo['tag_name'];
        }
        $pageArray['tagList'] = $tagList;
        $queryData = array('startTime' => $pageArray['startTime'] . ' 00:00:00', 'endTime' => $pageArray['endTime'] . ' 23:59:59', 'pos' => true);
        $tagTotal = $TagListModel->getTagLogTotal($queryData);
        $tagTotalList = array();
        $total = array();
        $totalCount = array();
        $postionList = array();
        foreach ($tagTotal as $tagTotalInfo)
        {
            if (empty($postionList[$tagTotalInfo['positionId']]))
            {
                $postionInfo = $TagListModel->getPositionInfo(array('pl_id' => $tagTotalInfo['positionId'], 'row' => true));
                $postionList[$tagTotalInfo['positionId']] = !empty($postionInfo['position_name']) ? $postionInfo['position_name'] : $tagTotalInfo['positionId'];
            }
            $tagTotalList[$tagTotalInfo['add_time']][$postionList[$tagTotalInfo['positionId']]][$tagTotalInfo['mid']] = $tagTotalInfo;
            $tagTotalList[$tagTotalInfo['add_time']][$postionList[$tagTotalInfo['positionId']]][$tagTotalInfo['mid']]['positionName'] = $postionList[$tagTotalInfo['positionId']];
            if (!isset($total[$tagTotalInfo['add_time']][$postionList[$tagTotalInfo['positionId']]]))
            {
                $total[$tagTotalInfo['add_time']][$postionList[$tagTotalInfo['positionId']]] = 0;
            }
            $total[$tagTotalInfo['add_time']][$postionList[$tagTotalInfo['positionId']]] += $tagTotalInfo['total'];
            if (!isset($totalCount[$tagTotalInfo['add_time']][$tagTotalInfo['mid']]))
            {
                $totalCount[$tagTotalInfo['add_time']][$tagTotalInfo['mid']] = 0;
            }
            $totalCount[$tagTotalInfo['add_time']][$tagTotalInfo['mid']] += $tagTotalInfo['total'];
            if (!isset($totalCount[$tagTotalInfo['add_time']]['total']))
            {
                $totalCount[$tagTotalInfo['add_time']]['total'] = 0;
            }
            $totalCount[$tagTotalInfo['add_time']]['total'] += $tagTotalInfo['total'];
        }
        krsort($tagTotalList);
        $pageArray['tagTotalList'] = $tagTotalList;
        $pageArray['total'] = $total;
        $pageArray['totalCount'] = $totalCount;

        if ($act === 'export')
        {
            $filename = \EncodingAction::transcoding('用户中心发送量细分_', 'utf-8') . date('Y-m-d');
            AutoMakeCsvAction::init($filename);
            $fieldArr = array('日期', '位置');
            foreach ($pageArray['tagList'] as $tagListInfo)
            {
                if (!empty($tagListInfo))
                {
                    $fieldArr[] = $tagListInfo;
                }
            }
            $fieldArr[] = '总计';
            AutoMakeCsvAction::setField($fieldArr);

            foreach ($tagTotalList as $tagTotalListKey => $tagTotalListInfo)
            {
                foreach ($tagTotalListInfo as $tagTotalPostionKey => $tagTotalInfo)
                {
                    $writeArr = array(
                        'date'      => $tagTotalListKey,
                        'postionId' => $tagTotalPostionKey,
                    );
                    foreach ($pageArray['tagList'] as $tagListKey => $tagListInfo)
                    {
                        $writeArr[$tagListKey] = $tagTotalInfo[$tagListKey]['total'];
                    }
                    $writeArr['total'] = $total[$tagTotalListKey][$tagTotalPostionKey];
                    AutoMakeCsvAction::writeData($writeArr);
                }

                $totalField = array('总计', '');
                foreach ($pageArray['tagList'] as $tagListKey => $tagListInfo)
                {
                    $totalField[$tagListInfo] = $totalCount[$tagTotalListKey][$tagListKey];
                }
                $totalField['total'] = $totalCount[$tagTotalListKey]['total'];
                AutoMakeCsvAction::writeData($totalField);
            }
            AutoMakeCsvAction::headerLocationCsv();
        }
        loadView('Admin/tag_postion_total.tpl.html', $pageArray);
    }
}
