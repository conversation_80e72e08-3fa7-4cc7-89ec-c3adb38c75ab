{{include file="header.tpl.html"}}
<div class="wrap">
	<div class="box clearfix">
		<div class="tit clearfix">
			<span class="titleft"></span>
			<span class="titright"></span>
			<h2>我的帐号</h2></div>
		<div class="boxbd clearfix">      
			<div class="info_side"><span class="face">
					<img src="/{{$pageArray.userimg}}" onerror="this.src='../pic/photo.jpg'"><p><a href="/member/avatar.php">修改头像</a></p></span>
				

			</div> 
			<form onsubmit="return checkAll();" method="post" action="http://login.2345.com/oauth/edit_user.php">
				{{if $pageArray.act=="setpwd"}}<input type="hidden" name="act" value="setpwd"/>{{/if}}
				{{if $pageArray.forward!=""}}<input type="hidden" name="forward" value="{{$pageArray.forward}}"/>{{/if}}
				<div class="info_cnt">
					<h4 class="bt"><i class="bt01"></i>帐号信息</h4>
					<table width="100%" cellspacing="0" cellpadding="0" border="0" class="infotable">
						<tbody>
							{{if $pageArray.act!="setpwd"}}
							<tr>
								<th><font>*</font>2345帐号</th>
								<td width="213"><input type="text" onkeyup="javascript:checkUserLength();" onfocus="javascript:$('msg_username').style.display='';if(this.value=='可用邮箱作为您的2345帐号' ){this.value='';this.style.color='#333'}" onblur="javascript:checkUser(1);if(this.value==''){this.value='可用邮箱作为您的2345帐号';this.style.color='#999'}" value="可用邮箱作为您的2345帐号" style="color:#999" id="username" name="username" class="ipt_01"></td>
								<td width="265">
									<div style="float:left; display:none;" id="msg_username">2~24个字符，汉字，字母，数字</div>
									<div id="reg_email" style="float:left;">&nbsp;<a target="_blank" href="http://zc.qq.com/iframe/2/reg.html" class="blue">没有邮箱?</a></div>
								</td>
							</tr>
							{{/if}}
							<tr>
								<th><font>*</font> 密 码</th>
								<td><input type="password" onblur="javascript:check_pw_safe();" onfocus="javascript:$('safe').style.display='';safe();$('msg_password').style.display='none';" onpaste="javascript:return false;" onkeyup="javascript:safe();" maxlength="16" id="password" name="password" class="ipt_01 ipt_02"></td>
								<td>
									<div style="display:none" id="msg_password">建议字母数字组合，6-16个字符</div><span style="" class="password_safety" id="safe">密码强度：<em><i id="w1" class="w1" style="">弱</i><i id="w2" style="display:none" class="w2">中</i><i id="w3" style="display:none" class="w3">强</i><i id="w4" style="display:none" class="w4">极强</i></em></span>
									<input type="hidden" value="1" id="pwd_strength" name="pwd_strength">
								</td>
							</tr>
							<tr>
								<th><font>*</font>重复密码</th>
								<td><input type="password" onfocus="javascript:$('msg_repassword').style.display='';" onpaste="javascript:return false;" onblur="javascript:checkRepass();" maxlength="16" id="repassword" name="repassword" class="ipt_01 ipt_02"></td>
								<td>
									<div style="" id="msg_repassword" class="pwtip">最少6个字符</div>
								</td>
							</tr>
							{{if $pageArray.act!="setpwd"}}
							<tr>
								<th><font>*</font>邮箱地址</th>
								<td><input type="text" class="ipt_01" name="email" id="email" onblur="javascript:checkEmail();" onfocus="javascript:$('msg_email').style.display='';"></td>
								<td>
									<div id="msg_email" style="display:;">找回密码用，请填写您的常用邮件</div>
								</td>
							</tr>
							{{/if}}
							<tr>
								<th></th>
								<td style="height:52px; line-height:52px;" colspan="2"><input type="submit" value="保存" class="btn4" onmouseover="this.className='btn4_hov'" onmouseout="this.className='btn4'">  <a class="btn_cancel" href="edit_info.php">取&nbsp;消</a>  </td>
							</tr>
						</tbody>
					</table>
					<br>
					<br>
					<br>
					<br>
					<br>
					<br>
					<br>
					<br>

				</div>
			</form>
		</div>         
	</div>
</div>
<script type="text/javascript" src="/js/ajax.js"></script>
<script type="text/javascript">
	var loadJs = function(_url,_callback){
		var callback = arguments[1] || function() {
		};
		var _script = document.createElement("SCRIPT");
		_script.setAttribute("type", "text/javascript");
		_script.setAttribute("src", _url);
		document.getElementsByTagName("head")[0].appendChild(_script);
		if (document.all) {
			_script.onreadystatechange = function() {
				if (/onload|loaded|complete/.test(_script.readyState)) {
					callback && callback();
				}
			};
		} else {
			_script.onload = function() {
				callback();
			};
		}
	}
	var err = 0;
	function $(id){
		return document.getElementById(id);
	}
	String.prototype.trim = function() 
	{ 
		return this.replace(/(^\s*)|(\s*$)/g, ""); 
	};
	//换算中文字长
	String.prototype.cnSize = function(){
		var arr = this.match(/[^\x00-\xff]/ig);
		return this.length + (arr == null ? 0 : arr.length);
	};
	function checkUser(n)
	{
		username = $("username").value.trim();
	
		if (username.length < 2)
		{
			$("msg_username").style.display='';
			$("msg_username").className = 'pwtip';
			$("msg_username").innerHTML = '最少2个字符';
			$("username").className = "ipt_01 ipt_02";
			return false;
		}
		if (username.length > 24)
		{
			$("msg_username").style.display='';
			$("msg_username").className = 'pwtip';
			$("msg_username").innerHTML = '请不要超过24个字符';
			$("username").className = "ipt_01 ipt_02";
			return false;
		}
		if(/[^\u4E00-\u9FA5\w_@\.\-]/.test(username))
		{
			$("msg_username").style.display='';
			$("msg_username").className = 'pwtip';
			$("msg_username").innerHTML = '请输入汉字，字母，数字或邮箱地址';
			$("username").className = "ipt_01 ipt_02";
			return false;
		}
		startrequest('/api/check.php','type=username&username='+username,1,function (response){
			if (response == 1)
			{
				err = 1;
			}
			else if (response == 2)
			{
				err = 2;
			}
			else
			{
				err = 0;
			}
		});
		if (err == 1)
		{
			$("msg_username").style.display='';
			$("msg_username").className = 'pwtip';
			$("msg_username").innerHTML = '此帐号已被注册，请重新输入';
			$("username").className = "ipt_01 ipt_02";
			return false;
		}
		else if (err == 2)
		{
			$("msg_username").style.display='';
			$("msg_username").className = 'pwtip';
			$("msg_username").innerHTML = '这个2345帐号不适合您，换一个吧';
			$("username").className = "ipt_01 ipt_02";
			return false;
		}
		if (username != username.toLowerCase())
		{
			$("msg_username").style.display='';
			$("msg_username").className = 'pwtip';
			$("msg_username").innerHTML = '登录区分大小写，请牢记您的2345帐号';
			$("username").className = "ipt_01 ipt_02";
		}
		else
		{
			$("msg_username").className = '';
			$("msg_username").innerHTML = '<img src="/images/check.jpg" style="margin: 8px 0 0;" />';
			$("username").className = "ipt_01";
		}
		if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username) && n == 1)
		{
			$("email").value = username;
		}
	
		return true;
	}
	function checkUserLength()
	{
		$("msg_username").style.display='';
		if ($("username").value.cnSize() > 24)
		{
			$("msg_username").className = 'pwtip';
			$("msg_username").innerHTML = '请不要超过24个字符';
			$("username").className = "ipt_01 ipt_02";
			return false;
		}
		$("msg_username").className = '';
		$("msg_username").innerHTML = '2~24个字符，汉字，字母，数字';
		$("username").className = "ipt_01";
		return true;
	}
	function checkPass()
	{
		var pass = $("password").value;
		if (pass.length < 6)
		{
			$("msg_password").style.display='';
			$("msg_password").className = 'pwtip';
			$("msg_password").innerHTML = '最少6个字符';
			$("password").className = "ipt_01 ipt_02";
			$("safe").style.display = "none";
			return false;
		}
		if (pass.length > 16)
		{
			$("msg_password").style.display='';
			$("msg_password").className = 'pwtip';
			$("msg_password").innerHTML = '最多16个字符';
			$("password").className = "ipt_01 ipt_02";
			$("safe").style.display = "none";
			return false;
		}
		if (pass == $('username').value)
		{
			$("msg_password").style.display='';
			$("msg_password").className = 'pwtip';
			$("msg_password").innerHTML = '密码不能与2345帐号一致，请重新输入';
			$("password").className = "ipt_01 ipt_02";
			$("safe").style.display = "none";
			return false;
		}
		if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
		{
			$("msg_password").style.display='';
			$("msg_password").className = 'pwtip';
			$("msg_password").innerHTML = '您的密码过于简单，请重新输入';
			$("password").className = "ipt_01 ipt_02";
			$("safe").style.display = "none";
			return false;
		}
	
		$("msg_password").className = '';
		$("msg_password").innerHTML = '<img src="/images/check.jpg" />';
		$("password").className = "ipt_01";
		return true;
	}

	function checkPassSame(pass)
	{
		var first = pass.substring(0,1);
		var exp = new RegExp('^'+first+'+$');
		if(exp.test(pass))
		{
			return false;
		}
	
		if (first == 'a' || first == 'A')
		{
			f = pass.charCodeAt(0);
			for(i = 1; i < pass.length; i++)
			{
				tmp = pass.charCodeAt(i);
				if (tmp - f != i)
				{
					return true;
				}
			}
			return false;
		}
		return true;
	}

	function checkRepass()
	{
                if ($('w1').style.display == '') {
                    $("msg_repassword").style.display = '';
                    $("msg_repassword").className = 'pwtip';
                    $("msg_repassword").innerHTML = '密码强度不能为弱';
                    $("repassword").className = "ipt_01 ipt_02";
                    return false;
                }
		var pass = $("repassword").value;
		if (pass.length < 6)
		{
			$("msg_repassword").style.display='';
			$("msg_repassword").className = 'pwtip';
			$("msg_repassword").innerHTML = '最少6个字符';
			$("repassword").className = "ipt_01 ipt_02";
			return false;
		}
		if (pass.length > 16)
		{
			$("msg_repassword").style.display='';
			$("msg_repassword").className = 'pwtip';
			$("msg_repassword").innerHTML = '最多16个字符';
			$("repassword").className = "ipt_01 ipt_02";
			return false;
		}
	
		if ($("repassword").value != $("password").value)
		{
			$("msg_repassword").style.display='';
			$("msg_repassword").className = 'pwtip';
			$("msg_repassword").innerHTML = '两次输入密码不一致';
			$("repassword").className = "ipt_01 ipt_02";
			return false;
		}
		$("msg_repassword").className = '';
		$("msg_repassword").innerHTML = '<img src="/images/check.jpg" />';
		$("repassword").className = "ipt_01";
		return true;
	}
	function checkEmail()
	{
		email = $("email").value.trim();
	
		if (email == "" || email == '输入邮箱作为您的2345帐号')
		{
			$("msg_email").style.display='';
			$("msg_email").className = 'pwtip';
			$("msg_email").innerHTML = '请输入邮箱';
			$("email").className = "ipt_01 ipt_02";
			return false;
		}
	
		if (!/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(email))
		{
			$("msg_email").style.display='';
			$("msg_email").className = 'pwtip';
			$("msg_email").innerHTML = '您输入的邮箱格式不正确';
			$("email").className = "ipt_01 ipt_02";
			return false;
		}
	
		startrequest('/api/check.php','type=email&email='+email,1,function(response){
			if (response == 1)
				err = 1;
			else
				err = 0;
		});
		if (err)
		{
			$("msg_email").style.display='';
			$("msg_email").className = 'pwtip';
			$("msg_email").innerHTML = '此邮箱已被注册，请换一个';
			$("email").className = "ipt_01 ipt_02";
			return false;
		}
		$("msg_email").className = '';
		$("msg_email").innerHTML = '<img src="/images/check.jpg" />';
		$("email").className = "ipt_01";
	
		return true;
	}

	function safe()
	{
		var pass = $("password").value;
		if (pass.length < 6)
		{
			var score = 0;
		}
		else if (pass == $('username').value)
		{
			var score = 0;
		}
		else if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
		{
			var score = 0;
		}
		else
		{
			var score = passwordGrade(pass);
		}
		//	$('safe').innerHTML = score;
		//	return;
		if (score <= 10)
		{
			$('w1').style.display = '';
			$('w2').style.display = 'none';
			$('w3').style.display = 'none';
			$('w4').style.display = 'none';
			$('pwd_strength').value = 1;
		}
		else if (score >= 11 && score <= 20)
		{
			$('w1').style.display = 'none';
			$('w2').style.display = '';
			$('w3').style.display = 'none';
			$('w4').style.display = 'none';
			$('pwd_strength').value = 2;
		}
		else if (score >= 21 && score <= 30)
		{
			$('w1').style.display = 'none';
			$('w2').style.display = 'none';
			$('w3').style.display = '';
			$('w4').style.display = 'none';
			$('pwd_strength').value = 3;
		}
		else
		{
			$('w1').style.display = 'none';
			$('w2').style.display = 'none';
			$('w3').style.display = 'none';
			$('w4').style.display = '';
			$('pwd_strength').value = 4;
		}
	}
	function check_pw_safe(){
		var pass = $("password").value;
		if (pass.length < 6)
		{
			var score = 0;
		}
		else if (pass == $('username').value)
		{
			var score = 0;
		}
		else if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
		{
			var score = 0;
		}
		else
		{
			var score = passwordGrade(pass);
		}
		if (score <= 10)
		{
			$("msg_repassword").style.display='';
			$("msg_repassword").className = 'pwtip';
			$("msg_repassword").innerHTML = '密码强度不能为弱';
			$("password").className = "ipt_01 ipt_02";
		}

	}

	function passwordGrade(pwd) {
		var score = 0;
		var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
		var repeatCount = 0;
		var prevChar = '';

		//check length
		var len = pwd.length;
		score += len > 18 ? 18 : len;

		//check type
		for (var i = 0, num = regexArr.length; i < num; i++) { if (eval('/' + regexArr[i] + '/').test(pwd)) score += 4; }

		//bonus point
		for (var i = 0, num = regexArr.length; i < num; i++) {
			if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2) score += 2;
			if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5) score += 2;
		}

		//deduction
		for (var i = 0, num = pwd.length; i < num; i++) {
			if (pwd.charAt(i) == prevChar) repeatCount++;
			else prevChar = pwd.charAt(i);
		}
		score -= repeatCount * 1;

		return score;
	}
	function checkAll(n)
	{
		{{if $pageArray.act!="setpwd"}}
		if (!checkEmail())
		{
			$("email").focus();
			return false;
		}
		if (!checkUser(2))
		{
			$("username").focus();
			return false;
		}
		{{/if}}
		if (!checkPass())
		{
			$("password").focus();
			return false;
		}
		if (!checkRepass())
		{
			$("repassword").focus();
			return false;
		}
		return true;
	}
	//点击统计函数
	document.onclick = function(e)//兼容IE,FF,OPERA
	{
		e = window.event || e;
		sE = e.srcElement || e.target;
		var isNotImg = true;
		if(sE.tagName=="IMG"||sE.tagName=="A"||sE.tagName=="AREA")
		{
			if(sE.tagName=="IMG" && sE.src != "")
			{
				sE = sE.parentNode;
				isNotImg = false;
			}
			if( (sE.tagName == "A"||sE.tagName == "AREA") && sE.href != "" )
			{
				cc(sE.href);
			}
		}
	}
 
	function cc(a) {
		var b = arguments,
		web = "ajax54",
		a2,
		i1 = document.cookie.indexOf("uUiD="),
		i2;
		if (b.length > 1) web = b[1];
		if (i1 != -1) {
			i2 = document.cookie.indexOf(";", i1);
			a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
		}
		if (!a2) {
			a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
			document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
		}
		if (a.length > 0) {
			var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
			loadJs(c)
		}    return true;
	}

</script>
{{include file="footer.tpl.html"}}