#!/bin/bash
action=$1
PHP=/opt/app/php7/bin/php
PHPCODE=/opt/case/smsp.2345.net/console/index.php
IP=${2}
PORT=${3}
if [ -z "${action}" ];then
    exit 1;
fi
if [ -z ${IP} ];then
    IP='0';
fi
if [ -z ${PORT} ];then
    PORT='0';
fi
case "${action}" in
   'start')
        ${PHP} ${PHPCODE} Crontab\\SmsServiceController actionStart ${IP} ${PORT} server;
        ;;
   'reload')
        ${PHP} ${PHPCODE} Crontab\\SmsServiceController actionReload ${IP} ${PORT} server;
        ;;
   'shutdown')
        ${PHP} ${PHPCODE} Crontab\\SmsServiceController actionShutdown ${IP} ${PORT} server;
        ;;
   'MonitorSwoole')
        ${PHP} ${PHPCODE} Crontab\\SmsServiceController actionMonitorSwoole;
        ;;
    * )
        ;;
esac;
