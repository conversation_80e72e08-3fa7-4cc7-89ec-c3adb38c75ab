<?php
/**
 * Class AvaterController
 */
includeBase('CronController');

class AvatarController extends CronController
{
    const ERROR_IMG = 'fee9458c29cdccf10af7ec01155dc7f0';  // 微信默认头像图片
    private $mid = '';
    private $index = 1;

    /**
     * AvaterController constructor.
     */
    public function __construct()
    {
        parent::__construct();

        global $argv;
        $this->mid = $argv[2];
        $this->index = str_pad($argv[3] - 1, 2, "0", STR_PAD_LEFT);
    }

    /**
     * 数据初始化入口
     */
    public function actionInit()
    {
        $handle = fopen('/opt/case/data/login_avatar_init/05/xq_init_' . $this->index, 'r');
        if (!$handle) {
            exit('file error');
        }
        $errCount = 0;
        $i = 1;
        $errorInfo = '';
        while(!feof($handle) && $errCount < 100){
            echo $i++ . "\n";
            $line = trim(fgets($handle, 1024));
            $line = explode(chr(17), $line);

            // 判断passID
            if (!$line[0]) {
                continue;
            }

            // 昵称不存在记录日志返回
            if (!$line[1]) {
                $this->write('nickname_error:' . serialize($line), 'avatar_error');
                continue;
            }

            // 图片不存在记录日志入库
            if (!$line[2]) {
                $this->write('img_error:' . serialize($line), 'avatar_error');
                if (!$this->insertInitInfo($line, $errorInfo)) {
                    $this->write('data:' . serialize($line) . '|errorInfo:' . $errorInfo, 'insert_error');
                }
                continue;
            }

            $content = $this->loadImage($line[2]);
            if (!$content) {
                $this->write('wx_net_error' . serialize($line), 'avatar_wx_error');
                continue;
            }
            $uri = $this->saveImage($content, $line[0], $errCount);

            if (!$uri) {
                $uri = '';
            }

            // 数据入库
            if (!$this->insertInitInfo($line,$errorInfo, $uri)) {
                $this->write('data:' . serialize($line) . '|errorInfo:' . $errorInfo, 'insert_error');
            }
        }

        if ($errCount >= 100) {
            $this->write('wx_net_error_exceed_100', 'avatar_wx_error');
        }
        fclose($handle);
    }

    /**
     * 图片下载
     * @param $url
     * @param int $retry
     * @return bool|returns|string
     */
    private function loadImage($url, $retry = 3)
    {
        if ($retry > 0) {
            $curlOption = [
                CURLOPT_CONNECTTIMEOUT => 1,
                CURLOPT_TIMEOUT        => 1,
            ];
            $res = http_get($url, $curlOption);
            if (!$res) {
                --$retry;
                return $this->loadImage($url, $retry);
            }
            return $res;
        }
        return false;
    }

    /**
     * 保存图片
     * @param string $data 图片信息
     * @param string $passId passid
     * @param int $errCount 错误计数
     * @return bool|string
     */
    private function saveImage($data, $passId, &$errCount)
    {
        if (md5($data) === self::ERROR_IMG) {
            ++$errCount;
            $this->write($passId, 'avatar_img_expire');
            return false;
        }
        $errCount = 0;
        $path ='/opt/case/data/login_avatar_init/img/';
        $this->makeDir($path);
        $newfile = $this->getImgName($passId) . '.jpg';

        if (file_put_contents($path . $newfile, $data)) {
            return $newfile;
        }

        $this->write('save_img_error:' . $passId, 'avatar_error');
        return false;
    }

    /**
     * 保存初始化数据
     * @param $data
     * @param string $imgName
     */
    private function insertInitInfo($data, &$errorInfo, $imgName = '')
    {
        $model = loadModel('UserBase');
        $errorInfo = '';
        $extraData = [];
        $extraData['approve_status'] = 0;

        if ($imgName) {
            $extraData['valid_avatar_source'] = 'init';
            $extraData['valid_avatar_url'] = $imgName;
            $extraData['approve_status'] = 3;
        }
        return $model->setNicknameInfo($this->mid, $data[0], $data[1], $errorInfo, $extraData);
    }

    /**
     * 图片名称按规则生成
     * @param $passId
     * @return string
     */
    private function getImgName($passId)
    {
        return 'init_' . $this->mid . '_' . $passId . '_' . date('md');
    }

    /**
     * 写入文件
     *
     * @param string $content $content
     * @param string $dirType $dirType
     * @param int $logType $logType
     * @return void
     */
    private function write($content, $dirType, $logType = FILE_APPEND)
    {
        $path = APPPATH . "/logs/" . date("Ymd") . "/AvatarInit/";
        $this->makeDir($path);
        file_put_contents($path . "/" . $dirType . '_' . $this->index . ".log", $content . PHP_EOL, $logType);
    }

    private function makeDir($path)
    {
        if (!is_dir($path)) {
            @mkdir($path, 0755, true);
        }
    }
}
