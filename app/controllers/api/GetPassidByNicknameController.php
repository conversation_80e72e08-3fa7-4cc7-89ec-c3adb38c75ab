<?php
/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 文件名称:GetPassidByNickname.php
 * 摘    要: 通过昵称查找passid
 * 作    者:<EMAIL>
 * 修改日期: 2015/12/11
 */
class GetPassidByNicknameController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }
        $nickname = trim($_POST['nickname']);
        $memberModel = loadModel('member');
        $info = $memberModel->getPassidByNickname($nickname);
        echo serialize($info);
    }

}