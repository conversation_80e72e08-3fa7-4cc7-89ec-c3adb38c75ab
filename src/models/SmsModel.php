<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：SmsModel.php
 * 摘    要：短信验证码
 * 修改日期：2016.01.08
 */
class SmsModel extends Model
{
    /**
     * 函数名称：__construct
     * 参    数：
     * 作    者：杜海明
     * 功    能：初始化数据库信息
     * 修改日期：2015-04-20
     */
    public function __construct()
    {
        //顺序不能错~
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    /**
     * 根据手机号 看是不是在黑名单中
     * @param string $phone phone
     * @return bool
     */
    public function isInBlackList($phone)
    {
        $sql = "SELECT phone FROM " . $this->smsBlacklistTable . " where phone = :phone and status = 1 limit 1";

        return $this->pdo->find($sql, array(':phone' => "'{$phone}'")) ? true : false;
    }

    /**
     * 设置手机黑名单
     * @param array $params phone
     * @return bool|int
     */
    public function setBlackList($params)
    {
        return $this->pdo->insert($this->smsBlacklistTable, $params) ? $this->pdo->lastInsertId() : false;
    }


    /**
     * 删除手机黑名单
     * @param int $id id
     * @return bool|int
     */
    public function deleteBlackList($id)
    {
        $condition = array('where' => ' id = :id', 'params' => array(':id' => $id));

        return $this->pdo->delete($this->smsBlacklistTable, $condition) ? true : false;
    }


    /**
     * 获取一个项目的短信类型sms config type
     * @param int $pid pid
     * @return array
     */
    public function getSmsConfigProjectTypeList($pid)
    {
        $sql = 'SELECT tid FROM project_type_list where pid=:pid and status=1';

        return $this->pdo->findAll($sql, array(':pid' => $pid));
    }


    /**
     * 获取sms config type
     * @return array
     */
    public function getSmsProviderId()
    {
        $sql = "SELECT id FROM my_sms_provider";

        return $this->pdo->findAll($sql);
    }


    /**
     * 按项目id phone和type获取某天内共发送的条数
     * @param string $phone phone
     * @param string $type  type
     * @param string $date  date
     * @return int
     */
    public function getSmsNumByPhoneType($phone, $type, $date)
    {
        $tableName = self::getTableName(date('Ym', strtotime($date)));
        $sql = "SELECT COUNT(*) AS total FROM `{$tableName}` WHERE phone=:phone AND FROM_UNIXTIME( UNIX_TIMESTAMP(send_time) ,'%Y-%m-%d') = :send_time AND `type`=:type";

        $where = array(
            ':phone'     => $phone,
            ':type'      => $type,
            ':send_time' => $date,
        );
        $smsNum = $this->pdo->find($sql, $where);

        return $smsNum ? $smsNum['total'] : 0;
    }


    /**
     * 参    数：
     * 功    能：查询是否移动MAS退订手机号
     */
    public function isTDPhone($phone)
    {
        $sql = "SELECT COUNT(*) FROM my_sms_td where phone = :phone limit 1";

        return $this->pdo->find($sql, array(':phone' => "'{$phone}'")) ? true : false;
    }


    /**
     * 参    数：
     * 功    能：看该type是否是 验证码类型
     */
    public function isCheckCodeSmsType($type)
    {
        $sql = "SELECT is_checkcode FROM my_sms_config where sms_type = :sms_type limit 1";
        $is_checkcode = $this->pdo->find($sql, array(':sms_type' => $type));
        if ($is_checkcode && $is_checkcode['is_checkcode'] == 1)
        {
            return true;
        }

        return false;
    }


    /**
     * 功    能：获得签名
     */
    public function getSigniture($providerId)
    {
        $sql = "SELECT signiture FROM my_sms_provider WHERE id=:id";
        $result = $this->pdo->find($sql, array(':id' => $providerId));

        return $result ? $result['signiture'] : '';
    }


    /**
     * 功    能：获取sms 配置
     */
    public function getSmsConfig($smsType)
    {
        $sql = "SELECT sms_type,mobile_provider_id,telecom_provider_id,unicom_provider_id,is_restrict,restrict_nums,percent FROM my_sms_config WHERE sms_type=:sms_type";
        $config = $this->pdo->findAll($sql, array(':sms_type' => $smsType));

        foreach ($config as &$configItem)
        {
            $providerFields = $this->getProviderFileds();
            $arrFields = explode(",", $providerFields);
            foreach (array("mobile", "telecom", "unicom") as $providername)
            {
                $sql = "SELECT " . $providerFields . " FROM my_sms_provider WHERE id=:id";
                $detail = $this->pdo->find($sql, array(':id' => $configItem[$providername . '_provider_id']));
                foreach ($arrFields as $field)
                {
                    $configItem[$providername . "_" . $field] = $detail[$field];
                }
            }
        }
        unset($configItem);

        return $config;
    }

    /**
     * 功    能：获取sms callback配置
     */
    public function getSmsCallbackConfig()
    {
        $sql = "SELECT * FROM `callback_config` WHERE `status` > :zero";
        $config = $this->pdo->findAll($sql, [':zero' => 0]);
        foreach ($config as &$configItem)
        {
            $configItem['data'] = json_decode($configItem['data'], true);
        }
        return $config;
    }


    /**
     * @return string
     */
    private function getProviderFileds()
    {
        return "id,status,api_url,username,password,sign,sign_type,signiture,support_mobile,support_telecom,support_unicom,action_name,appType,timeQuantum";
    }

    /**
     * 用于发送完成后往写入与logID关联的发送结果
     * @param array $data      data
     * @return bool
     */
    public function addSmsLog($data)
    {
        $this->pdo->beginTransaction();

        //写入月表
        $condition = array(
            'where'  => 'log_id = :log_id',
            'params' => array('log_id' => $data['logId'],),
        );
        $update = array(
            'channel'       => $data['channel'],
            'account'       => $data['account'],
            'msg_id'        => $data['msgId'],
            'code_time'     => $data['codeTime'],
            'code_status'   => $data['codeStatus'],
            'code_desc'     => $data['codeDesc'],
            'sms_count'     => $data['smsCount'],
            "send_status"   => $data['sendStatus'],
            "send_response" => $data['sendResponse'],
        );
        $isTrue = $this->pdo->update(self::getTableName(), $update, $condition);

        //写入cache表
        if ($isTrue)
        {
            $condition = array(
                'where'  => 'log_id = :log_id',
                'params' => array('log_id' => $data['logId'],),
            );
            $update = array(
                'send_status' => $data['sendStatus'],
            );
            $isTrue = $this->pdo->update('sms_logs_cache', $update, $condition);
        }

        if ($isTrue !== false && !empty($data['mid']))
        {
            $tagData = array(
                'log_id'     => $data['logId'],
                'positionId' => $data['businessId'],
                'add_time'   => date('Y-m-d'),
                'mid'        => $data['mid'],
            );
            $isTrue = $this->pdo->insert('sms_tag_log', $tagData);
        }
        if ($isTrue !== false && $data['appType'] == 2)
        {
            $voiceData = array('log_id' => $data['logId'], 'add_time' => date('Y-m-d'));
            $isTrue = $this->pdo->insert('voice_logs_list', $voiceData);
        }

        if ($isTrue !== false)
        {
            $this->pdo->commit();

            return true;
        }
        else
        {
            $this->pdo->rollBack();

            return false;
        }
    }

    /**
     * 功    能：根据日期时间获取最后一个log_id
     * 修改日期：2019年6月11日
     *
     * @param string $date 流水号
     * @param string $dateTime 流水号
     * @author: wangchenglong
     * @return null|int
     */
    public function getSmsLogIdByDate($date, $dateTime)
    {
        $tableName = self::getTableName($date);
        $sql = "SELECT log_id FROM $tableName WHERE `send_time` <= :send_time ORDER BY `log_id` DESC limit 1";
        $config = $this->pdo->find($sql, array(':send_time' => $dateTime));
        if (!empty($config)) {
            return $config['log_id'];
        }
        return false;
    }

    /**
     * 用于校验失败后按logID更新失败原因
     * @param array $data data
     * @return bool
     */
    public function upSmsLogs($data)
    {
        $logIdCondition = array();
        foreach ($data['logIds'] as $key => $logId)
        {
            $logIdCondition[":logId" . $key] = $logId;
        }

        //新数据库写入方式
        $condition = array(
            'where'  => ' log_id in (' . implode(",", array_keys($logIdCondition)) . ')',
            'params' => $logIdCondition,
        );
        $update = array(
            "send_status"   => $data['send_status'],
            "send_response" => $data['send_response'],
        );
        $isOK = $this->pdo->update(self::getTableName(), $update, $condition);

        if ($isOK !== false)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     * 用于新建短信，返回logID
     * @param array $data   插入数据，数组
     * @param array $column 插入数据，字段
     * @return bool|array
     */
    public function setSmsLogs($data, $column)
    {
        if (count($data) == 0)
        {
            return false;
        }

        $cacheColumn = array('phone', 'encrypt_phone');
        $cacheData = array();
        $phoneLogIds = array();
        foreach ($data as $phone => $value)
        {
            $cacheData[$phone] = [
                \SecurityAction::desensitize($phone),
                \SecurityAction::encrypt($phone),
            ];
        }
        $this->pdo->beginTransaction();
        $res = $this->pdo->batch('sms_logs_cache', $cacheColumn, $cacheData);

        if ($res)
        {
            $lastInsertId = (int)($this->pdo->lastInsertId());
            foreach ($data as $phone => $value)
            {
                $data[$phone][0] = \SecurityAction::desensitize($phone);
                $data[$phone][1] = \SecurityAction::encrypt($phone);
                $insertId = $lastInsertId++;
                $data[$phone][] = $insertId;
                $phoneLogIds[$phone] = $insertId;
            }
            $this->createSmsLogTable();
            $res = $this->pdo->batch(self::getTableName(), array_merge($column, array('log_id')), $data);
        }

        if ($res)
        {
            $this->pdo->commit();
            return $phoneLogIds;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * my_multi_sms_logs更新日志
     * @param array $data data
     * @return bool
     */
    public function upMultiSmsLogs($data)
    {
        $condition = array('where' => ' id = :log_id ', 'params' => array(':log_id' => $data['logId']));
        $updata = array(
            "send_status"   => $data['send_status'],
            "send_response" => $data['send_response'],
        );
        $isTrue = $this->pdo->update('my_multi_sms_logs', $updata, $condition);
        if ($isTrue !== false)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    /**
     *  my_multi_sms_logs写日志
     * @param array $data data
     * @return bool|int
     */
    public function addMultiSmsLogs($data)
    {
        $insert = array(
            "phone"         => $data['phone'],
            "illegal_phone" => "",
            "text"          => $data['text'],
            "type"          => $data['type'],
            "send_time"     => $data['send_time'],
            "send_status"   => $data['send_status'],
            "send_response" => $data['send_response'],
            "passid"        => $data['passid'],
            "pid"           => $data['pid'],
            'business_id'   => $data['business_id'],
            'client_ip'     => $data['client_ip'],
            'server_ip'     => $data['server_ip'],
        );
        $isTrue = $this->pdo->insert('my_multi_sms_logs', $insert);

        return $isTrue === false ? false : $this->pdo->lastInsertId();
    }

    /**
     * 往 my_sms_logs_channel写日志
     * @param array $data      data
     * @param int   $channelId channelId
     * @return bool
     */
    public function addMultiSmsChannelLog($data, $channelId)
    {
        $this->pdo->beginTransaction();
        $condition = array(
            'where'  => ' id = :log_id ',
            'params' => array(':log_id' => $data['logId']),
        );

        $updata = array(
            "phone"         => $data['phone'],
            "illegal_phone" => $data['illegal_phone'],
            "amount"        => $data['amount'],
            "send_status"   => $data['send_status'],
            "send_response" => $data['send_response'],
        );
        $res = $this->pdo->update('my_multi_sms_logs', $updata, $condition);
        if ($res)
        {
            $insert = array(
                'log_id'     => $data['logId'],
                'channel'    => $channelId,
                'codeStatus' => $data['codeStatus'],
                'msgId'      => $data['msgId'],
                'codeDesc'   => $data['codeDesc'],
            );
            $res = $this->pdo->insert('my_multi_sms_logs_channel', $insert);
        }
        if ($res !== false)
        {
            $this->pdo->commit();

            return true;
        }
        else
        {
            $this->pdo->rollBack();

            return false;
        }
    }

    /**
     * 获取ip白名单
     * @return array
     */
    public function getProjectIps()
    {
        $sql = " select ip from iptables_list";

        return $this->pdo->findAll($sql);
    }

    /**
     * 获取项目的位置列表
     * @param int $pid pid
     * @return array|bool
     */
    public function getProjectPositionsPid($pid)
    {
        $sql = " select pl_id from position_list where pid=:pid ";

        return $this->pdo->findAll($sql, array(':pid' => $pid));
    }

    /**
     * 获取sms配置 短信类型列表
     * @return array|bool
     */
    public function getSmsConfigTypeList()
    {
        $sql = "SELECT sms_type FROM my_sms_config";

        return $this->pdo->findAll($sql);
    }

    /**
     * @param array $data    data
     * @param array $columns columns
     * @return array
     */
    public function addSmsLog2Redis($data, $columns)
    {
        return;
        $length = count($data);
        $endId = $this->redis->incrBy('sms:logid:last', $length);
        $nowId = $first_id = $endId - $length + 1;
        $updateId = $this->redis->get('sms:logid:update');
        $brustId = $this->redis->get('sms:logid:brust');
        $isSync = $this->redis->get('sms:logid:sync');
        $phoneLogIds = [];
        if ($isSync && $endId > $brustId)
        {
            //超出分配范围，进入独立模式，不与数据库同步
            $this->redis->set('sms:logid:sync', 0);
            $isSync = 0;
        }
        if ($isSync && $endId >= $updateId && $first_id <= $updateId)
        {
            //执行数据库同步操作，更新redis的update和brust
            $this->syncLogId();
        }

        foreach ($data as $phone => $phoneData)
        {
            $kv = [];
            foreach ($columns as $columnIndex => $column)
            {
                $kv[$column] = $phoneData[$columnIndex];
            }
            $this->setSmsLog2Redis($nowId, $kv);
            $phoneLogIds[$phone] = $nowId;
            $nowId++;
        }
        return $phoneLogIds;
    }


    /**
     * @param int   $logId logId
     * @param array $data  data
     */
    public function setSmsLog2Redis($logId, $data)
    {
        //$nowTime = time();
        //数据本体先写入redis
        //$this->redis->hMSet("sms:db:data:{$logId}:{$nowTime}", $data);
        //再写入数据索引
        //$this->redis->lPush('sms:db:list', $logId);
    }

    /**
     * @return bool
     */
    public function syncLogId()
    {
        $isSync = $this->redis->get('sms:logid:sync');
        if (!$isSync)
        {
            return false;
        }
        $sql = 'SELECT `value` FROM `static_config` WHERE `id` = 1';
        $lastIdDB = $this->pdo->find($sql)['value'];
        $sql = 'SELECT `value` FROM `static_config` WHERE `id` = 2';
        $stepDB = max(500, $this->pdo->find($sql)['value']);
        $lastId = $this->redis->get('sms:logid:last');
        if ($lastIdDB < $lastId)
        {
            $this->redis->set('sms:logid:sync', 0);
            return false;
        }
        $updateIdNew = (int)($lastIdDB + 0.75 * $stepDB);
        $brustIdNew = $lastIdDB + $stepDB;
        $sql = 'UPDATE `static_config` SET `value` = :lastId WHERE id = 1';
        $dbResult = $this->pdo->query($sql, [':lastId' => $brustIdNew, 'id' => 1]);
        if (!$dbResult)
        {
            $this->redis->set('sms:logid:sync', 0);
            return false;
        }
        $this->redis->set('sms:logid:update', $updateIdNew);
        $this->redis->set('sms:logid:brust', $brustIdNew);
        return true;
    }

    /**
     * @return bool
     */
    public function repairLogId()
    {
        $isSync = $this->redis->get('sms:logid:sync');

        $sql = 'SELECT max(log_id) as maxId FROM ' . self::getTableName();
        $maxIdDB = $this->pdo->find($sql)['maxId'];
        echo "Table " . self::getTableName() . " has max id: {$maxIdDB}\n";
        $lastId = $this->redis->get('sms:logid:last');
        $updateId = $this->redis->get('sms:logid:update');
        $brustId = $this->redis->get('sms:logid:brust');
        echo "Now Redis --> last: {$lastId}, update: {$updateId}, brust: {$brustId}\n";
        $sql = 'SELECT `value` FROM `static_config` WHERE `id` = 1';
        $lastIdDB = $this->pdo->find($sql)['value'];
        $sql = 'SELECT `value` FROM `static_config` WHERE `id` = 2';
        $stepDB = max(500, $this->pdo->find($sql)['value']);
        echo "Now DB    --> last: {$lastIdDB}, step: {$stepDB}\n\n";
        if ($isSync)
        {
            echo "Sync flag works well, exit.";
            return false;
        }
        else
        {
            echo "Sync flag not working, fixing...\n\n";
        }
        $lastIdNew = (int)($maxIdDB + $stepDB);
        $updateIdNew = (int)($maxIdDB + 1.75 * $stepDB);
        $brustIdNew = $maxIdDB + 2 * $stepDB;
        echo "Fix Redis --> last: {$lastIdNew}, update: {$updateIdNew}, brust: {$brustIdNew}\n";
        echo "Fix DB    --> last: {$brustIdNew}\n\n";
        $sql = 'UPDATE `static_config` SET `value` = :lastId WHERE `id` = :id';
        $dbResult = $this->pdo->query($sql, ['lastId' => $brustIdNew, 'id' => 1]);
        echo "DB Result: {$dbResult}";
        if ($dbResult)
        {
            $this->redis->set('sms:logid:last', $lastIdNew);
            $this->redis->set('sms:logid:update', $updateIdNew);
            $this->redis->set('sms:logid:brust', $brustIdNew);
            $this->redis->set('sms:logid:sync', 1);
        }
    }
}
