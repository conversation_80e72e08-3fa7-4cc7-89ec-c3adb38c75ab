<?php

use Octopus\PdoEx;

class LoginConfigModel extends Model
{
    const PACKAGE_STATUS_DISABLE = 0;
    const PACKAGE_STATUS_ENABLE = 1;

    const PACKAGE_LIST_STATUS_DISABLE = 0;
    const PACKAGE_LIST_STATUS_ENABLE = 1;

    private $pdo;
    private $table = 'login_config';
    private $typeTable = 'login_type';
    private $detailTable = 'login_config_detail';
    private $encryptTable = 'login_encrypt_type';
    private $encryptDetailTable = 'login_encrypt_detail';


    /**
     * UnionLoginConfigModel constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = PdoEx::getInstance(DB_PASSPORT_MAIN, $dbConfig[DB_PASSPORT_MAIN]);
    }

    /**
     * 功    能：获取某个项目详情
     * 修改日期：2019年5月29日
     *
     * @param int $id 项目配置表的主键id
     * @param int $type 查询类型 是否只查询有效的值
     * @author: wangchenglong
     * @return null
     */
    public function getById($id, $type = true)
    {
        $where = "WHERE id=:id ";
        $condition = [':id' => $id];
        if ($type) {
            $where .= "AND status = :status ";
            $condition[':status'] = 1;
        }
        $sql = "SELECT id,mid,pname,package_name,is_need_shanyan,is_union_login,next_request_interval,show_captcha_num,status,create_at,update_at,is_quiet_login,shanyan_type,protocol_status,version_op,version,channel_op,channel,relation,toast  ".
            "FROM {$this->table} {$where} ".
            "LIMIT 1";
        return $this->pdo->find($sql, $condition);
    }

    /**
     * 功    能：获取某个项目详情
     * 修改日期：2019年5月29日
     *
     * @param array $params 筛选条件数组
     * @param array $midArr 允许查看的mid数组
     * @author: wangchenglong
     * @return null
     */
    public function getConfigList($params = array(), $midArr = array())
    {
        $sql = "select lc.id from {$this->table} lc left join {$this->detailTable} lcd 
                on lc.`id` =`lcd`.`pid` and `lcd`.status = :lcd_status  ";
        $group = ' group by lc.id ';
        $where = ' where lc.status = :status and lc.mode = :mode ';
        $mode = isset($params['mode']) ? $params['mode'] : 1;
        $bindParams = array();
        $bindParams[':status'] = 1;
        $bindParams[':mode'] = $mode;
        $bindParams[':lcd_status'] = 1;

        if (array_key_exists('type', $params) && !empty($params['type'])) {
            $where = $where . ' and `lcd`.lid = :lid ';
            $bindParams[':lid'] = $params['type'];
        }

        if (array_key_exists('mid', $params) && !empty($params['mid'])) {
            $where = $where . ' and `lc`.mid = :mid ';
            $bindParams[':mid'] = $params['mid'];
        }

        if (array_key_exists('pname', $params) && $params['pname'] != '') {
            $where = $where . ' and (`lc`.pname like :pname or `lc`.package_name like :package_name) ';
            $bindParams[':pname'] = '%' . $params['pname'] . '%';
            $bindParams[':package_name'] = '%' . $params['pname'] . '%';
        }

        if (!empty($midArr)) {
            $where = $where . ' and `lc`.mid in(';
            foreach ($midArr as $k => $v) {
                $where = $where . ':lc_id' . $k . ',';
                $bindParams[':lc_id' . $k] = $v;
            }
            $where = rtrim($where, ',') . ')';
        }

        $countSql = 'select count(*) as cnt from (' . $sql . $where . $group.') as t1 ';
        $row = $this->pdo->find($countSql, $bindParams);
        $PageAction = loadAction('Page');
        $pageList = $PageAction->returnPageConfig(20);
        $arr['listPage'] = $PageAction->showPage($row['cnt'], '/houtai_gl/UnionLogin/LoginConfigList');
        $limit = " limit {$pageList['page']}, {$pageList['limit']} ";
        $sql = $sql . $where . $group . $limit;
        $pidArr = $this->pdo->findAll($sql, $bindParams);
        $list = array();
        if (!empty($pidArr)) {
            $listSql = "select lc.id,lc.mid,lc.pname,lc.package_name,lc.is_need_shanyan,
            lc.is_union_login,lc.next_request_interval,lc.show_captcha_num,
            group_concat(lcd.lid order by lcd.rank asc ) as login_type from {$this->table} lc 
            left join {$this->detailTable} lcd on lc.`id` =`lcd`.`pid` and `lcd`.status = :lcd_status  ";
            $listGroup = ' group by lc.id ';
            $listWhere = ' where lc.status = :status and lc.mode = :mode and lc.id in(';
            $listBindParams = array();
            $listBindParams[':status'] = 1;
            $listBindParams[':mode'] = $mode;
            $listBindParams[':lcd_status'] = 1;
            foreach ($pidArr as $key => $value) {
                $listWhere = $listWhere . ':id' . $key . ',';
                $listBindParams[':id' . $key] = $value['id'];
            }
            $listWhere = rtrim($listWhere, ',') . ')';
            $listSql = $listSql . $listWhere . $listGroup;
            $list = $this->pdo->findAll($listSql, $listBindParams);
        }
        return array(
            'list' => $list,
            'listPage' => $arr['listPage']
            );
    }

    /**
     * 功    能：获取某个项目详情
     * 修改日期：2019年5月29日
     *
     * @param string $field 需要查询的字段
     * @author: wangchenglong
     * @return null
     */
    public function getLoginTypeList($field = '*')
    {
        $sql = "select $field from {$this->typeTable} where status = :status ";
        return $this->pdo->findAll($sql, array(':status' => 1));
    }

    /**
     * 功    能：新增项目配置
     * 修改日期：2019年5月29日
     *
     * @param array $params 需要新增的参数
     * @author: wangchenglong
     * @return null
     */
    public function insertConfig($params = array())
    {
        if (empty($params)) {
            return false;
        }

        return $this->pdo->insert($this->table, $params);
    }

    /**
     * 功    能：新增项目配置
     * 修改日期：2019年5月29日
     *
     * @param int $id 项目配置表主键id
     * @param array $data 需要修改的参数
     * @author: wangchenglong
     * @return null
     */
    public function updateConfig($id, $data)
    {
        $info = $this->getById($id, false);
        if (!empty($info)) {
            $oldStr = $newStr = '';
            $checkKey = [
                'mid',
                'pname',
                'package_name',
                'is_union_login',
                'show_captcha_num',
                'is_need_shanyan',
                'next_request_interval',
                'status',
                'is_quiet_login',
                'shanyan_type',
                'protocol_status',
                'version_op',
                'version',
                'channel_op',
                'channel',
                'relation',
                'toast',
            ];

            if ($info['toast'] != '{}') {
                $info['toast'] = json_decode(iconv("gbk", "UTF-8", $info['toast']), true);
                ksort($info['toast']);
                $info['toast'] = json_encode($info['toast']);
            }

            foreach ($checkKey as $key) {
                $oldStr .= $info[$key];
                $newStr .= $data[$key];
            }
            if (md5($oldStr) == md5($newStr)) {
                return true;
            }

            $condition = array(
                "where" => "id = :id",
                "params" => array(":id" => $id),
            );
            return $this->pdo->update($this->table, $data, $condition);
        } else {
            return false;
        }
    }

    /**
     * 功    能：修改项目详情配置
     * 修改日期：2019年5月29日
     *
     * @param int $id 项目配置表主键id
     * @param array $data 需要修改的参数
     * @author: wangchenglong
     * @return null
     */
    public function updateConfigDetail($id, $data)
    {
        $flag = true;
        $id_arr = array();
        foreach ($data as $key => $value) {
            $id_arr[] = $value['lid'];
            $sql = "select id,appinfo_str,login_astrict_detail,pid,lid,rank,status,appinfo_str,
                    compel_bind_phone,login_astrict_detail from {$this->detailTable} where 
                    lid = :lid and pid = :pid";
            $result = $this->pdo->find($sql, array(':lid' => $value['lid'], ':pid' => $id));
            if (!empty($result)) {
                $result['appinfo_str'] = json_decode(iconv("gbk", 'UTF-8', $result['appinfo_str']), true);
                $result['login_astrict_detail'] = json_decode(iconv("gbk", 'UTF-8', $result['login_astrict_detail']), true);
                if ($result['pid'] != $id
                    || $result['lid'] != $value['lid']
                    || $result['rank'] != $value['rank']
                    || $result['status'] != $value['status']
                    || $result['appinfo_str'] != $value['appinfo_str']
                    || $result['compel_bind_phone'] != $value['compel_bind_phone']
                    || $result['login_astrict_detail'] != $value['login_astrict_detail']) {
                    $value['appinfo_str'] = json_encode($value['appinfo_str']);
                    $value['login_astrict_detail'] = json_encode($value['login_astrict_detail']);
                    $condition = array(
                        "where" => "id = :id",
                        "params" => array(
                            ":id" => $result['id'],
                        ),
                    );
                    $updateResult = $this->pdo->update($this->detailTable, $value, $condition);
                    if (empty($updateResult)) {
                        $flag = false;
                    }
                }
            } else {
                $value['pid'] = $id;
                $value['appinfo_str'] = json_encode($value['appinfo_str']);
                $value['login_astrict_detail'] = json_encode($value['login_astrict_detail']);
                $insertResult = $this->pdo->insert($this->detailTable, $value);
                if (empty($insertResult)) {
                    $flag = false;
                }
            }
        }

        $sql = "select lid from {$this->detailTable} where status = :status and pid = :pid ";
        $res = $this->pdo->findAll($sql, array(':status' => 1, ':pid' => $id));
        $arr = array();
        if (!empty($res)) {
            foreach ($res as $k => $v) {
                $arr[] = $v['lid'];
            }
        }
        $shouldDelete = array_diff($arr, $id_arr);
        if (!empty($shouldDelete)) {
            foreach ($shouldDelete as $ke => $val) {
                $condition = array(
                    "where" => "lid = :id and pid = :pid",
                    "params" => array(":id" => $val,':pid' => $id)
                );
                $result = $this->pdo->update($this->detailTable, array('status' => 0), $condition);
                if (empty($result)) {
                    $flag = false;
                }
            }
        }
        return $flag;
    }

    /**
     * 功    能：获取项目详情
     * 修改日期：2019年5月29日
     *
     * @param int $id 项目配置表主键id
     * @author: wangchenglong
     * @return null
     */
    public function getDetailById($id)
    {
        $sql = "select lcd.id,lcd.appinfo_str,lcd.login_astrict_detail,lcd.pid,lcd.lid,lcd.rank,
                lcd.status,lcd.appinfo_str,lcd.compel_bind_phone,lcd.login_astrict_detail,
                lt.is_need_appid,lt.is_need_bind_phone from {$this->detailTable}
                lcd left join {$this->typeTable} lt on lcd.`lid`=`lt`.`id` where
                lcd.pid=:pid and lcd.status = :status order by lcd.rank asc";
        return $this->pdo->findAll($sql, array(':pid' => $id, ':status' => 1));
    }

    /**
     * 功    能：获取项目信息
     * 修改日期：2019年5月29日
     *
     * @param array $params 项目配置表信息
     * @param int $type 类型 1用mid查询，2用包名查询，3用项目名称查询
     * @author: wangchenglong
     * @return null
     */
    public function getInfoByMid($params, $type, $status = false)
    {
        switch ($type) {
            case 1:
                $where = ' mid = :params ';
                break;
            case 2:
                $where = ' package_name = :params ';
                break;
            case 3:
                $where = ' pname = :params ';
                break;
            default:
                $where = ' mid = :params ';
                break;
        }
        $where .= ' and mode = :mode';

        if ($status) {
            $sql = "select id from {$this->table} where " . $where . ' and status = :status ';
            return $this->pdo->find($sql, array(':params' => $params['params'], ':status' => 1, ':mode' => $params['mode']));
        } else {
            $sql = "select id from {$this->table} where " . $where;
            return $this->pdo->find($sql, array(':params' => $params['params'], ':mode' => $params['mode']));
        }
    }

    /**
     * 功    能：删除项目配置
     * 修改日期：2019年5月29日
     *
     * @param int $id 项目配置表主键id
     * @author: wangchenglong
     * @return null
     */
    public function deleteConfigById($id)
    {
        $flag = true;
        $condition = array(
            "where" => "pid = :pid",
            "params" => array(":pid" => $id)
        );
        $result = $this->pdo->update($this->detailTable, array('status' => 0), $condition);
        if (empty($result)) {
            $flag = false;
        }

        $ConfigCondition = array(
            "where" => "id = :id",
            "params" => array(":id" => $id)
        );
        $res = $this->pdo->update($this->table, array('status' => 0), $ConfigCondition);

        if (empty($res)) {
            $flag = false;
        }

        return $flag;
    }

    /**
     * 功    能：获取配置信息
     * 修改日期：2019年5月29日
     *
     * @param string $mid 项目配置表主键id
     * @param int $mode 登录模式
     * @author: wangchenglong
     * @return null
     */
    public function getConfigInfo($mid, $mode)
    {
        $sql = "select lt.short_name,lt.is_need_bind_phone,lc.is_union_login,lc.next_request_interval,
                lc.show_captcha_num,lc.is_need_shanyan,lcd.login_astrict_detail,lcd.compel_bind_phone, 
                lc.is_quiet_login,lc.shanyan_type,lc.protocol_status,lc.version_op,lc.version,lc.channel_op,lc.channel,lc.relation,lc.toast 
                from {$this->table} lc left join {$this->detailTable} lcd on 
                lc.`id` =`lcd`.`pid` left join {$this->typeTable} lt on lt.`id` =`lcd`.`lid` where lc.mid = :mid  and 
                `lcd`.status = :lcd_status and lc.status = :status and lc.mode = :mode order by lcd.rank asc";
        $bindParams = array();
        $bindParams[':lcd_status'] = 1;
        $bindParams[':mid'] = $mid;
        $bindParams[':status'] = 1;
        $bindParams[':mode'] = $mode;
        $result = $this->pdo->findAll($sql, $bindParams);
        $data = array();
        if (empty($result)) {
            return false;
        } else {
            foreach ($result as $key => $value) {
                $object = json_decode(iconv("gbk", 'UTF-8', $value['login_astrict_detail']));
                $data[$key] = array(
                    'channelWhite' => iconv("UTF-8", 'gbk', $object->channelWhite),
                    'channelBlack' => iconv("UTF-8", 'gbk', $object->channelBlack),
                    'versionWhite' => iconv("UTF-8", 'gbk', $object->versionWhite),
                    'versionBlack' => iconv("UTF-8", 'gbk', $object->versionBlack),
                    'compel_bind_phone' => $value['compel_bind_phone'],
                    'short_name' => $value['short_name'],
                );
            }
            if (empty($data)) {
                return false;
            } else {
                $resultData = array(
                    'is_union_login' => $result[0]['is_union_login'] == 1 ? true : false,
                    'next_request_interval' => $result[0]['next_request_interval'],
                    'show_captcha_num' => $result[0]['show_captcha_num'],
                    'is_need_shanyan' => $result[0]['is_need_shanyan'] == 1 ? true : false,
                    'is_quiet_login' => $result[0]['is_quiet_login'] == 1 ? true : false,
                    'protocol_status' => $result[0]['protocol_status'] == 1 ? true : false,
                    'shanyan_type' => $result[0]['shanyan_type'],
                    'detail_data' => $data,
                );
                if ($mode == 2) {
                    $resultData['version_op'] = $result[0]['version_op'];
                    $resultData['version'] = $result[0]['version'];
                    $resultData['channel_op'] = $result[0]['channel_op'];
                    $resultData['channel'] = $result[0]['channel'];
                    $resultData['relation'] = $result[0]['relation'];
                    $resultData['toast'] = json_decode(iconv("gbk", "UTF-8", $result[0]['toast']), true);
                    $resultData['toast']['old_account']['content'] = iconv('UTF-8', 'gbk', $resultData['toast']['old_account']['content']);
                    $resultData['toast']['new_account']['content'] = iconv('UTF-8', 'gbk', $resultData['toast']['new_account']['content']);
                }
                return $resultData;
            }
        }
    }

    /**
     * 功    能：获取所有已配置的mid
     * 修改日期：2019年5月29日
     *
     * @param int $mode 登录模式
     * @author: wangchenglong
     * @return array
     */
    public function getAllConfigMid($mode)
    {
        $sql = "select mid from {$this->table} where status = :status and mode = :mode group by mid";
        $list = $this->pdo->findAll($sql, array(':status' => 1, ':mode' => $mode));
        $arr = array();
        if (!empty($list)) {
            foreach ($list as $key => $value) {
                $arr[$value['mid']] = $key;
            }
        }
        return $arr;
    }

}
