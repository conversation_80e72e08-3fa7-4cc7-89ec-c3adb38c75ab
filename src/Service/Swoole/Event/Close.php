<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/17
 * Time: 16:15
 */

namespace Service\Swoole\Event;

use Service\Swoole\Tool\Log;

class Close
{
    /**
     * 关闭连接
     * -
     * @param object $service 服务
     * @param int $fd 文件描述符
     * @param bool $reset $reset设置为true会强制关闭连接，丢弃发送队列中的数据
     * @return void
     */
    public static function OnClose($service, $fd, $reset)
    {
//        $msg = 'master_pid:' .$service->master_pid . 'manager_pid:' . $service->manager_pid . 'worker_pid:' . $service->worker_pid . 'worker_id:' . $service->worker_id;
//        Log::setLogMsg($msg);
//        Log::setLogMsg('Close：' . $fd . '是否强制关闭：' . $reset, true);
    }
}
