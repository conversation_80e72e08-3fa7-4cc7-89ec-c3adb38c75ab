<!DOCTYPE HTML>
<html>
<head>
    <meta charset="gb2312" />
    <title>{{$pageArray.title}}</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,minimal-ui"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
    <meta name="apple-mobile-web-app-title" content="2345用户中心">
    <meta content="telephone=no" name="format-detection" />
    <meta content="email=no" name="format-detection" />
    <meta content="" name="Description" />
    <meta content="" name="Keywords" />
    <link rel="stylesheet" type="text/css"  href="//login.2345.com/css/m/g-user_20160407.css">
</head>
<body>
<style type="text/css">
    .g-user-title{display:block; width:100%;}
    .g-uesr-zx{line-height: 3;font-size: 0.925rem;color:#666;font-weight:bold; display:block; width:100%;}
    .g-user-item{line-height: 2.5;font-size: 0.875rem;color:#555;width: 100%; display:block; background:#f2f4f5}
    .g-user-ts{line-height: 3;font-size: 0.875rem;}
    .g-user-cp li {width:25%;position: relative;float: left;padding:0.9rem;}
    .g-user-cp li .product{ text-align: center;margin-bottom:0.8rem;}
    .g-user-cp li .product img{ width: 100%; display:block;}
    .g-user-cp li .product .tit{ font-size:0.88889rem; line-height:1.22222rem; height:1.22222rem; white-space: normal; display: -webkit-box;-moz-box-orient: vertical;-webkit-box-orient: vertical; -webkit-line-clamp: 2; text-align: center;margin-top:0.3rem; word-wrap: break-word;}
    .clear{clear: both;}
</style>


<script src="/js/jquery-1.9.1.min.js"></script>
<script src="/js/login.js"></script>

<header class="g-user-hd">
    <a href="javascript:;" class="g-user-back">返回</a>
    <h1 class="g-user-logo">2345用户中心</h1>
</header>
{{if $pageArray.step == 1}}
<article class="g-user-bd">
    <header>
        <h1 class="g-user-title">帐号注销</h1>
        <span class="g-uesr-zx">帐号注销将放弃以下权利</span>
    </header>
    <p class="g-user-item">1、帐号信息，第三方授权信息将清空且无法恢复</p>
    <p class="g-user-item">2、2345旗下产品的使用信息将清空且无法恢复</p>
    <p class="g-user-item">3、帐号内的充值记录，优惠券，会员权益等将清空且无法恢复</p>
    {{if $pageArray.accountType == 'phone'}}
        <p class="g-user-item">4、帐号所绑定的手机号将会在一定时间内限制再次注册2345帐号</p>
    {{/if}}
    <br />
    <span class="g-uesr-zx">2345帐号通用但不限于以下产品：</span>
    <div class="g-user-cp">
        <ul>
            <li>
                <div class="product">
                    <img src="/img/logoff/sjllq.png" alt="2345手机浏览器">
                    <span class="tit">2345浏览器</span>
                </div>
            </li>
            <li>
                <div class="product">
                    <img src="/img/logoff/sjzs.png" alt="2345手机助手">
                    <span class="tit">2345手机助手</span>
                </div>
            </li>
            <li>
                <div class="product">
                    <img src="/img/logoff/tqw.png" alt="2345天气王">
                    <span class="tit">2345天气王</span>
                </div>
            </li>
            <li>
                <div class="product">
                    <img src="/img/logoff/sjlm.png" alt="2345手机联盟">
                    <span class="tit">王牌手机联盟</span>
                </div>
            </li>
            <li>
                <div class="product">
                    <img src="/img/logoff/xqlm.png" alt="星球联盟">
                    <span class="tit">星球联盟</span>
                </div>
            </li>
        </ul>

    </div>

    <div class="clear"></div>
    <div class="g-user-form">
        <div class="btn-group">
            <a href="javascript:void(0)" class="btn-confirm locationStep">已清楚风险，确认继续</a>
        </div>
    </div>
</article>
{{/if}}
{{if $pageArray.step == 2}}
    {{if $pageArray.accountType == 'phone'}}
        <article class="g-user-bd step_2_class">
            <header>
                <h1 class="g-user-title">帐号注销需验证手机号</h1>
            </header>
            <p class="g-user-ts">帐号注销后将不能恢复，请谨慎操作！</p>
            <form action="" class="g-user-form">
                <div class="form-item">
                    <p class="form-txt">已绑手机：<em>{{$pageArray.hidePhone}}</em></p>
                </div>

                <div class="form-item">
                    <div class="form-vcode">
                        <input type="tel" class="form-ipt ipt_txt" placeholder="请输入验证码">
                        <a href="javascript:;" class="btn-getvcode confirmCode">获取校验码</a>
                    </div>
                    <p class="form-tip-error form-tips-error" style="display: none"><i></i>验证码错误</p>
                </div>
                <div style="margin-top: 10px; display: none" class="logoutMsgDivClass">
                    <p style="color: #E6A23C;line-height: 20px;font-size: 13px;">
                        <img src="/images/logout_icon.png" style="display:block;float: left; padding-right: 5px">
                        <b style="font-weight:bold">您近期已成功注销过。</b>
                    </p>
                    <p style="color: #E6A23C;line-height: 20px;font-size: 13px; padding-left: 25px;" class="logoutMsgClass">本次需要注销账号为x月x日 xx:xx新注册账号。</p>
                    <p style="color: #E6A23C;line-height: 20px;font-size: 13px; padding-left: 25px;">如需再次注销，请联系官方电话400-000-2345，我们会在15个工作日内处理。</p>
                </div>
                <div style="clear: both"></div>
                <div class="btn-group">
                    <a href="javascript:void(0);" class="btn-confirm logOffClass">确认注销</a>
                </div>
            </form>
            <footer class="g-user-help">
                <p>如果您的手机无法收到验证码，请致电客服解决：<br>400-000-2345</p>
            </footer>
        </article>
    {{elseif $pageArray.accountType == 'email'}}
        <article class="g-user-bd step_2_class_2">
            <header>
                <h1 class="g-user-title">帐号注销需验证邮箱</h1>
            </header>
            <p class="g-user-ts">帐号注销后将不能恢复，请谨慎操作！</p>
            <form action="" class="g-user-form">
                <div class="form-item">
                    <p class="form-txt">已绑邮箱：<em>{{$pageArray.hideEmail}}</em></p>
                </div>
                <div class="form-item">
                    <div class="form-vcode">
                        <input type="tel" class="form-ipt ipt_txt" placeholder="请输入验证码">
                        <a href="javascript:;" class="btn-getvcode confirmCode">获取校验码</a>
                    </div>
                    <p class="form-tip-error form-tips-error" style="display:none;"><i></i>验证码错误</p>
                </div>
                <div class="btn-group">
                    <a href="javascript:void(0);" class="btn-confirm logOffClass">确认注销</a>
                </div>
            </form>
        </article>
    {{elseif $pageArray.accountType == 'username'}}
        <article class="g-user-bd step_2_class_3">
            <header>
                <h1 class="g-user-title">帐号注销需验证帐号密码</h1>
            </header>
            <p class="g-user-ts">帐号注销后将不能恢复，请谨慎操作！</p>
            <form action="" class="g-user-form">
                <div class="form-item form-item-error">
                    <input type="password" class="form-ipt ipt_txt" placeholder="输入帐号密码">
                    <p class="form-tip-error form-tips-error" style="display: none"><i></i>密码错误</p>
                </div>
                <div class="btn-group">
                    <a href="javascript:void(0);" class="btn-confirm logOffClass">确认注销</a>
                </div>
            </form>
        </article>
    {{elseif $pageArray.accountType == 'thirdBind'}}
        <article class="g-user-bd step_2_class_4">
            <header>
                <h1 class="g-user-title">帐号注销确认</h1>
            </header>
            <p class="g-user-ts">帐号注销后将不能恢复，请谨慎操作！</p>
            <form action="" class="g-user-form">
                <div class="form-item">
                    <div class="form-vcode">
                        <input type="tel" class="form-ipt ipt_txt" placeholder="请输入图片验证码"maxlength="4">
                        <img class="btn-getvcode" onclick="this.src = '/Captcha.php?mid=loginReg&t=' + Math.random();"  id="pic" src="/Captcha.php?mid=loginReg">
                    </div>
                    <p class="form-tip-error form-tips-error" style="display: none"><i></i>验证码错误</p>
                </div>
                <div class="btn-group">
                    <a href="javascript:void(0);" class="btn-confirm logOffClass">确认注销</a>
                </div>
            </form>
        </article>
    {{/if}}
{{/if}}

<article class="g-user-bd step_3_class" style="display: none">
    <div class="g-user-result bind-sec">
        <h1><i></i>帐号已注销</h1>
    </div>
</article>

<footer class="g-user-ft">
    <p>Copyright &copy;2345网址导航 沪ICP备********号-1</p>
</footer>
<script type="text/javascript">
    document.body.addEventListener('touchstart', function () {});

    var accountType = '{{$pageArray.accountType}}';
    var intervalTimes = 60;
    var intervalHandle = false;
    var thirdShow = '{{$pageArray.thirdShow}}';
    $('.locationStep').click(function () {
        window.location.href = '/member/LogOff/index/{{$pageArray.aesStr}}?step=2&to={{$pageArray.token}}';
    });

    function showSendMsg() {
        if (intervalTimes > 1) {
            --intervalTimes;
            $(".confirmCode").attr('act', 1);
            $(".confirmCode").text('重新发送（' + intervalTimes + '）').addClass('btn-disabled').show();
        } else {
            $(".confirmCode").attr('act', '');
            $(".confirmCode").text('获取验证码').removeClass('btn-disabled').show();
        }
    }
    var isBindClick = false
    $('.confirmCode').bind({
        click:function(){
            if ($(".confirmCode").attr('act') == 1)
            {
                return;
            }
            intervalTimes = 60;
            clearInterval(intervalHandle);
            intervalHandle = setInterval(function() {
                showSendMsg();
            }, 1000);
            if (accountType == 'phone')
            {
                $.post('/member/LogOff/SendCode/{{$pageArray.aesStr}}', {"to" : "{{$pageArray.to}}" }, function(data) {
                    if (data.code == '400.0')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '500.0')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '300.0')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '600.0')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '200')
                    {
                        if (!isBindClick){
                            $('.logOffClass').click(function () {
                                logOffFunc();
                            });
                            isBindClick = true
                        }
                    }
                });
            }
            else if (accountType == 'email')
            {
                $.post('/member/LogOff/SendMailCode/{{$pageArray.aesStr}}', {"to" : "{{$pageArray.to}}" }, function(data) {
                    if (data.code == '400')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '404')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == '500')
                    {
                        $('.form-tips-error').html(data.msg).show();
                    }
                    else if (data.code == 200)
                    {
                        if (!isBindClick){
                            $('.logOffClass').click(function () {
                                logOffFunc();
                            });
                            isBindClick = true
                        }
                    }
                });
            }
        }
    });


    function logOffFunc()
    {
        var verify_code = $('.ipt_txt').val();
        if (verify_code.length ==0)
        {
            return false;
        }
        if (accountType == 'username')
        {
            verify_code = MD5(verify_code);
        }
        $('.logoutMsgDivClass').hide();
        $('.form-tips').hide();
        $.post('/member/LogOff/Sub/{{$pageArray.aesStr}}','verify_code='+verify_code + "&to={{$pageArray.to}}"  ,function (result) {
            if (result.code == '200')
            {
                history.pushState(null, null, document.URL);
                window.addEventListener('popstate', function () {
                    history.pushState(null, null, document.URL);
                });
                $('.step_2_class').hide();
                $('.step_2_class_2').hide();
                $('.step_2_class_3').hide();
                $('.step_2_class_4').hide();
                $('.step_3_class').show();
                try {
                    UserCenter.userLogout(result.data.passid);
                }
                catch(err)
                {
                    try {
                        var paramObj={status:1, type:'logout'};
                        WebViewBridge.send(JSON.stringify(paramObj));
                    }
                    catch (err2) {
                        setTimeout("window.location.href = '/login'", 8000 )
                    }
                }
            }
            else
            {
                if (result.code == '407')
                {
                    var src = '/Captcha.php?' + Math.random() + '&mid=loginReg';
                    $('#pic').attr('src', src)
                }
                if  (result.code == '500'){
                    $('.logoutMsgClass').html(result.msg)
                    $('.logoutMsgDivClass').show();
                    return
                }
                $('.form-tips-error').html('<i class="icon-error"></i>' + result.msg).show();

            }
        },'json')
    }

    if (accountType == 'thirdBind')
    {
        $('.logOffClass').click(function () {
            logOffFunc();
        });
    }
    if (accountType == 'username')
    {
        $('.logOffClass').click(function () {
            logOffFunc();
        });
    }

</script>
</body>
</html>