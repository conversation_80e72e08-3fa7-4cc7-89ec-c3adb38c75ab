<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2021/1/28
 * Time: 11:39
 */
$_BASE_PATH = realpath(dirname(__FILE__) . "/../") . "/";
define("APPPATH", $_BASE_PATH . "app/");
define('BASEPATH', $_BASE_PATH);
include APPPATH . '/core/Config.php';
include APPPATH . '/core/RedisEx.php';
include APPPATH . '/core/Common.php';
include APPPATH . '/vendor/autoload.php';
use Dotenv\Dotenv;

$envPath = '/opt/case/config/login.2345.com';
if (file_exists($envPath . '/.env')) {
    $dotEnv = new Dotenv($envPath, '.env');
    $dotEnv->load();
}
Config::load();


class testDbMonitor
{
    private $pdo = null;

    private $dbName = "";
    private $config = [];
    private $errorMsgList = [];
    private $redisMaxMemory = 10737418240;  //10G

    public function __construct($dbName, $config)
    {
        $this->dbName = $dbName;
        $this->config = $config;
    }

    private function setError($dbName, $errorMsg)
    {
        if (empty($this->errorMsgList)) {
            header("HTTP/1.1 404 Not Found");
        }
        $this->echoMsg($errorMsg, "error");
        $this->errorMsgList[$dbName][] = date("Y-m-d H:i:s") . "\t" . $errorMsg;

    }

    public function getErrorList($dbName = "")
    {
        return $this->errorMsgList;
    }

    private function echoMsg($msg, $level = "info")
    {
        if ($level == "info") {
            echo date("Y-m-d H:i:s") . "\t " . $msg . "<br />";
        } else {
            echo date("Y-m-d H:i:s") . "\t " . "<p style='color: red;display: inline;'>{$msg}</p>" . "<br />";
        }
    }

    /**
     * @param $host
     * @param $port
     * @param $dbname
     * @param $username
     * @param $password
     * @param $testMachine
     * @return bool|PDO|null
     */
    function testDb($host, $port, $dbname, $username, $password, $testMachine)
    {
        $testMsg = "{$dbname}\t{$host}\t";
        $this->echoMsg("{$testMsg}开始检测");
        if ($fp = @fsockopen($host, $port, $errno, $errStr, 1)) {
            @fclose($fp);
            $dsn = 'mysql: dbname=' . $dbname . '; host=' . $host . '; port=' . $port;
            try {
                $this->pdo = new \PDO($dsn, $username, $password);
                if (is_object($this->pdo)) {
                    $this->echoMsg("连接成功");

                    return $this->pdo;
                } else {
                    $this->setError($dbname, "数据库(IP:{$host})，{$testMachine}连接失败！");

                    return false;
                }
            }
            catch (Exception $exception) {
                $errStr = "<br /> Connection failed: " . $exception->getMessage();
                $this->setError($dbname, "数据库(IP:{$host})，{$testMachine}连接失败！，错误：{$errStr}");

                return false;
            }
        } else {
            @fclose($fp);
            $this->setError($dbname, "数据库(IP:{$host})，{$testMachine}连接失败！，错误：{$errStr}");

            return false;
        }
    }

    /**
     * db检测入口
     * @return bool
     */
    public function check()
    {
        $openDb = null;
        $username = $this->config["username"];
        $password = $this->config["password"];
        if (!empty($this->config["master"])) {
            $host = $this->config["master"]["host"];
            $port = $this->config["master"]["port"];
            $openDb = $this->testDb($host, $port, $this->dbName, $username, $password, "master");
        }
        if (!empty($this->config["slave"])) {
            $host = $this->config["slave"]["host"];
            $port = $this->config["slave"]["port"];
            $openDb = $this->testDb($host, $port, $this->dbName, $username, $password, "slave");
        }

        if (!empty($this->config["master"]) && !empty($this->config["slave"]) && $this->config["master"]["host"] != $this->config["slave"]["host"]) {
            $this->syncTime($this->config["slave"]["host"], $this->config["slave"]["port"], "rc_status", "rc_status");
        }
        if (!empty($this->getErrorList())) {
            return false;
        }

        return true;
    }

    /**
     * 数据库主从延迟
     * @param $host
     * @param $port
     * @param $username
     * @param $password
     * @return bool
     */
    public function syncTime($host,$port, $username, $password)
    {
        try {
            $dsn = 'mysql:host=' . $host . ';port=' . $port;
            $pdo = new \PDO($dsn, $username, $password);
            $sth = $pdo->prepare('show slave status;');
            $sth->execute();
            $row = $sth->fetch(PDO::FETCH_ASSOC);
            if (empty($row)) {
                $this->setError($this->dbName, "主从同步失败:show slave status 失败");

                return false;
            }
            if ($row['Seconds_Behind_Master'] > 15) {
                $this->setError($this->dbName, "从库同步数据时间大于15秒,为：" . $row['Seconds_Behind_Master'] . '__');

                return false;
            } elseif ($row["Slave_IO_Running"] != "Yes") {
                $this->setError($this->dbName, "Slave_IO_Running值不为Yes, 为：" . $row["Slave_IO_Running"] . "__");

                return false;
            } elseif ($row["Slave_SQL_Running"] != "Yes") {
                $this->setError($this->dbName, "Slave_SQL_Running值不为Yes, 为：" . $row["Slave_SQL_Running"] . "__");

                return false;
            }
            $this->echoMsg("主从检测完成");
        }
        catch (Exception $exception) {
            $this->setError($this->dbName, "主从同步查询失败：错误：" . $exception->getMessage());

            return false;
        }
    }

    /**
     * test redis
     */
    public function testRedis()
    {
        $strNodes = $this->config['nodes'];
        $strAuth = $this->config['auth'];
        $nodeList = explode(',', $strNodes);
        foreach ($nodeList as $nodeInfo) {
            try {
                $testMsg = "redis节点{$nodeInfo}\t";
                $this->echoMsg($testMsg . "开始检测");
                $redisCluster = new RedisCluster(null, [$nodeInfo], 1, 1, true, $strAuth);
                if (is_object($redisCluster)) {
                    $this->echoMsg("连接成功");
                    $redisInfo = $redisCluster->info("used_memory_human");
                    if ($redisInfo["used_memory"] >= $this->redisMaxMemory) {
                        $this->setError($this->dbName, "{$nodeInfo}节点内存超出10G");
                    }
                } else {
                    $this->setError($this->dbName, "{$testMsg}连接失败");
                }
            }
            catch (Exception $exception) {
                $this->setError($this->dbName, "{$testMsg}连接失败，错误:" . $exception->getTraceAsString());
            }
        }
    }
}

class testMonitor
{
    private static function echoMsg($callFunc, $msg, $level = "info")
    {
        if ($level == "info") {
            echo date("Y-m-d H:i:s") . "\t " . $callFunc . "\t" . $msg . "<br />";
        } else {
            header("HTTP/1.1 404 Not Found");
            echo date("Y-m-d H:i:s") . "\t " . $callFunc . "\t" . "<p style='color: red;display: inline; '>{$msg}</p>" . "<br />";
        }
    }

    /**
     * apcu监控
     * @param array $data
     */
    public static function apcu_cache($data = [])
    {
        $key = "IP:WHITE:LIST";
        $ipList = apcu_fetch($key);
        self::echoMsg("apcu_cache", "{$_SERVER["SERVER_ADDR"]} apcu内的值：");
        if (is_array($ipList)) {
            foreach ($ipList as $ip) {
                self::echoMsg("apcu_cache", $ip);
            }
        } else {
            self::echoMsg("apcu_cache", "empty 请检测", "error");
        }
    }

    /**
     * xLog长度监控
     * @param array $data
     */
    public static function xLogLen($data = [])
    {
        $redis = \RedisEx::getInstance();
        $key = 'LOG_REDIS_KEY';
        $len = $redis->lLen($key);
        $str = "xLog消息队列{$key} 长度:{$len} ";
        if ($len > 30000) {
            self::echoMsg("xLogLen", $str . "超过指定值 30000", "error");
        } else {
            self::echoMsg("xLogLen", $str);
        }
    }

    /**
     * ip2region
     * @param array $data
     * @return mixed
     */
    public static function ip2region($data = [])
    {
        $ip = "127.0.0.1";
        $config = [
            'host' => '127.0.0.1',
            'port' => '9990',
        ];
        // 创建 连接 发送消息 接收响应 关闭连接
        $socket = socket_create(AF_INET, SOCK_STREAM, 0);
        if ($socket == false) {
            self::echoMsg("ip2region", '连接失败 - socket_create 失败', "error");

            return false;
        }
        //超时1秒
        socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, ["sec" => 1, "usec" => 0]);
        socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, ["sec" => 1, "usec" => 0]);
        $connected = socket_connect($socket, $config['host'], $config['port']);
        if ($connected == false) {
            self::echoMsg("ip2region", '连接失败 - socket_connect 失败', "error");

            return false;
        }
        $send = socket_send($socket, $ip, strlen($ip), 0);
        if ($send == false) {
            self::echoMsg("ip2region", '连接失败 - socket_send 失败', "error");

            return false;
        }
        $response = socket_read($socket, 1024);
        self::echoMsg("ip2region", $response);

        return true;
    }

    /**
     * kafka
     * @param array $data
     */
    public static function kafka_len($data = [])
    {
        $redis = \RedisEx::getInstance();
        $key = 'BIG_DATA_REDIS_KEY';
        $len = $redis->lLen($key);
        $str = "消息队列 {$key} 长度:{$len} ";
        if ($len > 30000) {
            self::echoMsg("kafka_len", $str . "超过指定值 30000", "error");
        } else {
            self::echoMsg("kafka_len", $str);
        }
    }

    /**
     * 用户信息变更通知队列监控
     * @param array $data
     */
    public static function noticeLen($data = [])
    {
        $key = "Notice:change:list";
        $redis = \RedisEx::getInstance();
        $len = $redis->lLen($key);
        $str = "用户信息变更消息队列{$key} 长度:{$len} ";
        if ($len > 500) {
            self::echoMsg("noticeLen", $str . "超过指定值 500", "error");
        } else {
            self::echoMsg("noticeLen", $str);
        }
    }

    /**
     * 检测数据库连接
     * @param array $data
     */
    public static function mysql($data = [])
    {
        $config = config::get("database");
        foreach ($data as $item) {
            $dbMonitor = new testDbMonitor($item, $config[$item]);
            $dbMonitor->check();
        }
    }

    /**
     * 检测redis连接
     * @param array $data
     */
    public static function redis($data = [])
    {
        $redisConfig = config::get("redis");
        foreach ($data as $item) {
            $dbMonitor = new testDbMonitor($item, $redisConfig[$item]);
            $dbMonitor->testRedis();
        }
    }

    /**
     * 云控apcu
     * @param array $data
     */
    public static function cloud_apcu($data = [])
    {
        $cacheInfo = apcu_cache_info();
        self::echoMsg("cloud_apcu", "使用总内存内存：" . $cacheInfo["mem_size"], "info");
        if (!empty($cacheInfo["cache_list"]) && is_array($cacheInfo["cache_list"])) {
            foreach ($cacheInfo["cache_list"] as $info) {
                $str =  "\t --- 有效期：" . $info["ttl"] . "\t --- 占用内存：" . $info["mem_size"] . "<br />";
                $apcuInfo = apcu_fetch($info["info"]);
                if ($info["info"] == "union:login:sdk:enable:package:ids") {
                    $apcuInfo = unserialize($apcuInfo);
                    $str =  "获取联合登录项目" . $str . $info["info"] . "<pre>" . var_export($apcuInfo, true) . "</pre>";
                } elseif (preg_match("/^mid:config:cache:key:/", $info["info"])) {
                    $list= explode(":", $info["info"]);
                    $type = "正常登录";
                    if ($list[count($list) - 1] == 2){
                        $type = "游客";
                    }
                    $str = "获取联合登录配置信息-{$type}" . $str . $info["info"] . "<pre>" . var_export(unserialize($apcuInfo), true) . "</pre>";
                } elseif (preg_match("/^union:login:sdk:package:version:list/", $info["info"])) {
                    $str = "根据包名和版本获取联合登录信息" . $str . $info["info"] .  "<pre>" . var_export(unserialize($apcuInfo), true) . "</pre><br />";
                } elseif (preg_match("/^nicknameTips:/", $info["info"])) {
                    $str =  "项目昵称提示" . $str . $info["info"] .  "<pre>" . mb_convert_encoding($apcuInfo, "GBK", "UTF-8") . "</pre>";
                } elseif ($info["info"] =="union:login:sdk:allowed:mid") {
                    $str =  "获取允许联合登录项目" . $str . $info["info"] .  "<pre>" . var_export($apcuInfo, true) . "</pre><br />";
                }elseif (preg_match("/^mid:config:key/",$info["info"])){
                    $str =  "根据mid获取登录方式" . $str . $info["info"] .  "<pre>" . var_export($apcuInfo, true) . "</pre><br />";
                } else{
                    echo  $info["info"] . "999999999999<br/>";
                }
                self::echoMsg("cloud_apcu", $str, "info");
            }
        }
    }
}


$monitorList = [
    "mysql"      => [
        "passport_user",
        "passport_main",
        "passport_avatar",
        "passport_log",
        "passport_notice",
        "my_2345",
    ],
    "redis"      => [
        "cluster",
    ],
    "xLogLen"    => [],
    "apcu_cache" => [],
    "ip2region"  => [],
    "noticeLen"  => [],
    "kafka_len"  => [],
    "cloud_apcu" => [],
];

$cmd = !empty($_GET["cmd"]) ? $_GET["cmd"] : "all";
$cmdList = explode(",", $cmd);
foreach ($monitorList as $key => $itemList) {
    if ($cmd != "all" && !in_array($key, $cmdList)) {
        continue;
    }
    if (method_exists("testMonitor", $key)) {
        testMonitor::$key($itemList);
    }
}

