@charset "gb2312";
/**
* @name : uc
* @update : 2014.12.22
* <AUTHOR> liux
**/
.userInfoBox .username .trigSet i { background: url(../../images/v2/uc_icon.png) no-repeat; }

.flag-bmail, .flag-bphone, .flag-bqq, .flag-bmail-un, .flag-bphone-un, .flag-bqq-un, .typeSel .i-type { background: url(../../images/v2/uc_sprite.png) no-repeat; }

body { background-color: #f3f3f3; }

.flag-bmail, .flag-bphone, .flag-bqq, .flag-bmail-un, .flag-bphone-un, .flag-bqq-un { display: inline-block; vertical-align: middle; width: 50px; height: 50px; }

.flag-bmail { background-position: 0 -55px; }

.flag-bphone { background-position: -60px -55px; }

.flag-bqq { background-position: -120px -55px; }

.flag-bmail-un { background-position: 0 0; }

.flag-bphone-un { background-position: -60px 0; }

.flag-bqq-un { background-position: -120px 0; }

.ucfnBox .m-txtA { padding-top: 10px; }
.ucfnBox .btnNext { margin-top: 15px; }

.titTips { padding-top: 10px; color: #666666; font-size: 14px; font-weight: bold; }

.weiTips { padding-bottom: 10px; color: #999999; font-size: 12px; }

.typeSel { overflow: hidden; *zoom: 1; }
.typeSel .i-type { position: absolute; top: 30px; left: 22px; width: 34px; height: 40px; overflow: hidden; background-position: 0 -115px; }
.typeSel .i-type.i-type-mail { background-position: -44px -115px; }
.typeSel .tit { margin: 28px 0 6px; font-size: 14px; }
.typeSel .data { color: #666666; height: 18px; overflow: hidden; }
.typeSel li { position: relative; float: left; display: inline; margin: 12px 22px 0 0; padding-left: 64px; width: 122px; height: 102px; border: 1px solid #74b4e8; background-color: #ffffff; cursor: pointer; *zoom: 1; -webkit-border-radius: 5px; -moz-border-radius: 5px; border-radius: 5px; }
.typeSel li:hover, .typeSel li.hover { background-color: #eaf5ff; border-color: #74b4e8; }
.typeSel li.current { width: 121px; height: 100px; border-width: 2px; border-color: #74b4e8; padding-left: 63px; background: #ffffff url(../../images/v2/type_cur.png) no-repeat right bottom; }
.typeSel li.current .i-type { top: 29px; left: 21px; }
.typeSel li.current .tit { margin-top: 27px; }

.retShow { padding-top: 100px; }
.retShow table { width: 100%; }
.retShow table td { text-align: center; }
.retShow .icon-show { margin-right: 12px; }
.retShow .retCon { vertical-align: middle; display: inline-block; *display: inline; *zoom: 1; text-align: left; }
.retShow .retCon .name { font-family: \5FAE\8F6F\96C5\9ED1; font-size: 20px; }
.retShow .retCon .tips { color: #999999; }
.retShow .retCon a { color: #2371c8; }
.retShow .retCon a:hover { color: #ff3300; }

.m-stepA { margin: 0 0 10px; overflow: hidden; *zoom: 1; }
.m-stepA .step-1, .m-stepA.m-stepA-4 .step-4, .m-stepA.m-stepA-3 .step-3, .m-stepA.m-stepA-2 .step-2 { color: #ffffff; background-color: #338ad1; background-position: right -40px; }
.m-stepA.m-stepA-4 .step-3, .m-stepA.m-stepA-3 .step-2, .m-stepA.m-stepA-2 .step-1 { color: #666666; background-color: #d4ecff; background-position: right -80px; }
.m-stepA.m-stepA-4 .step-1, .m-stepA.m-stepA-4 .step-2, .m-stepA.m-stepA-3 .step-1 { color: #666666; background-color: #d4ecff; background-position: right -120px; }
.m-stepA li { float: left; width: 25%; height: 30px; line-height: 30px; font-size: 14px; text-align: center; color: #666666; font-weight: bold; background: #e4e4e4 url(../../images/v2/step.png) no-repeat right 0; }
.m-stepA .step-4 { background-image: none; }

.userInfoBox { position: relative; margin-bottom: 10px; height: 72px; background-color: #f7f7f7; border: 1px solid #f3f3f3; }
.userInfoBox .avatar { float: left; position: relative; width: 72px; height: 72px; cursor: pointer; }
.userInfoBox .avatar .hover_bg { position: absolute; top: 0; left: 0; width: 72px; height: 72px; background: url(../../images/v2/avatar_hover.png) no-repeat; filter: alpha(opacity=0); opacity: 0; cursor: pointer; }
.userInfoBox .avatar a { display: block; *zoom: 1; }
.userInfoBox .avatar a:hover .hover_bg { filter: alpha(opacity=100); opacity: 1; }
.userInfoBox .cutline { float: left; margin: 10px 18px 0 18px; width: 1px; height: 52px; overflow: hidden; border-left: 1px dashed #c9c9c9; }
.userInfoBox .username { float: left; }
.userInfoBox .username .nid { margin-top: 15px; color: #999999; }
.userInfoBox .username .trigSet { color: #2371c8; font-size: 22px; font-family: \5FAE\8F6F\96C5\9ED1; cursor: pointer; }
.userInfoBox .username .trigSet i { float: left; display: inline; margin: 8px 5px 0 0; width: 20px; height: 20px; overflow: hidden; background-position: -5px -159px; }
.userInfoBox .username .trigSet:hover { color: #ff3300; }
.userInfoBox .username .name { line-height: 72px; font-size: 24px; font-family: \5FAE\8F6F\96C5\9ED1; }
.userInfoBox .moreLink { float: right; margin: 27px 25px 0 0; color: #2371c8; }
.userInfoBox .moreLink:hover { color: #ff3300; }

.bindInfoBox { margin-bottom: 10px; border: 1px solid #e3e8ec; overflow: hidden; *zoom: 1; }
.bindInfoBox .flag { position: absolute; top: 11px; left: 36px; }
.bindInfoBox .field { color: #333333; margin-right: 12px; }
.bindInfoBox .cont { color: #333333; }
.bindInfoBox .golink { margin-left: 12px; font-size: 12px; color: #2371c8; }
.bindInfoBox .golink:hover { color: #ff3300; }
.bindInfoBox .fn-btn { position: absolute; right: 25px; top: 20px; }
.bindInfoBox .item { position: relative; margin-bottom: -1px; padding-left: 106px; height: 72px; line-height: 72px; border-bottom: 1px solid #eeeeee; font-size: 14px; color: #666666; *zoom: 1; }
.bindInfoBox .item.item-un { color: #999999; }
.bindInfoBox .item.item-un .field { color: #666666; }

.uc-index-part { padding-bottom: 24px; }
