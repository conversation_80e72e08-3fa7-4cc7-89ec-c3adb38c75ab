<?php
use Service\ThirdParty\Auth\NECaptchaVerifier;
use Service\ThirdParty\Auth\SecretPair;

class NEValidateAction
{
    const MODE_LOGIN = 1;
    const MODE_REG = 2;
    /**
     * 功    能：获取易盾校验配置数据
     * 作    者：yinmt
     *
     * @param string $key 获取参数键值名
     *
     * @return mixed
     */
    public function verifyConfig($type, $key = '') {
        if (empty($key)) {
            return \Config::get('NECaptchaConfig')[$type];
        }
        return \Config::get('NECaptchaConfig')[$type][$key];
    }

    /**
     * 功    能：易盾校验
     * 作    者：yinmt
     *
     * @param array $postData 页面提交参数
     * @param array $errInfo 错误信息
     *
     * @return bool
     */
    public function check163Verify($type, $postData, &$errInfo, $mode = self::MODE_LOGIN) {
        if (!isset($postData['NECaptchaValidate']) || !$postData['NECaptchaValidate']) {
            $errInfo['content'] = '网络正在开小差，请稍后再试';
            return false;
        }
        RedisEx::delInstance();
        $verifier = new NECaptchaVerifier(
            $this->verifyConfig($type, 'captchaId'),
            new SecretPair(YIDUN_CAPTCHA_SECRET_ID, YIDUN_CAPTCHA_SECRET_KEY)
        );

        $validate = $postData['NECaptchaValidate']; // 获得验证码二次校验数据
        $result = $verifier->verify($validate, []);

        if (!$result['result']) {
            $errInfo = $result;
            $errInfo['content'] = '验证码校验不通过，请重新' . ($mode == self::MODE_LOGIN ? '登录' : '注册');
        }
        return $result['result'];
    }

    public function appVerifyConfig($type, $key = '') {
        if (empty($key)) {
            return \Config::get('NECaptchaConfig')[$type];
        }
        return \Config::get('NECaptchaConfig')[$type][$key];
    }

    public function appCheck163Verify($postData, &$errInfo) {
        $captchaId = $postData['captchaId'];
        $captchaConfigId = $this->appVerifyConfig(APP_NE_ACTIVE_ID, 'captchaId');
        if (empty($captchaId) || $captchaId != $captchaConfigId) {
            $errInfo = [
                "error"   => 100105,
                "content" => "校验失败",
            ];

            return false;
        }
        if (!isset($postData['NECaptchaValidate']) || !$postData['NECaptchaValidate']) {
            $errInfo['content'] = '网络正在开小差，请稍后再试';
            return false;
        }
        RedisEx::delInstance();
        $verifier = new NECaptchaVerifier(
            $captchaConfigId,
            new SecretPair(YIDUN_CAPTCHA_SECRET_ID, YIDUN_CAPTCHA_SECRET_KEY)
        );

        $validate = $postData['NECaptchaValidate']; // 获得验证码二次校验数据
        $result = $verifier->verify($validate, []);

        if (!$result['result']) {
            $errInfo = $result;
            $errInfo['content'] = '验证码校验不通过，请重新登录';
        }
        return $result['result'];
    }
}