<?php

/**
 * 并发
 * */
namespace Admin;
class BindDataModel extends \Model
{
    /**
     * 功    能：初始化数据库信息
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    public function getBingFaData($data = array(), $limit = array())
    {

        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col} FROM voice_bingfa_data ";
        if (!empty($data['startTime']) && !empty($data['endTime']))
        {
            $where .= ' `date` >=:startTime AND `date` <=:endTime AND';
            $paramArr[':startTime'] = $data['startTime'];
            $paramArr[':endTime'] = $data['endTime'];
        }
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND') ;
        }

        $sql .= $where . ' order by id desc';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }
}
