<?php

/**
 * Created by Php<PERSON>torm.
 * User: duhm
 * Date: 2016-7-18
 * Time: 9:18
 */
class RegController extends Controller
{
    public function actionIndex ()
    {
        session_start();
        //只能是POST提交
        if ($_SERVER["REQUEST_METHOD"] == "POST")
        {

            if (!isset($_POST['agree']))
            {
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
                {
                    exit(json_encode(array(
                        "loadPage" => '',
                        "forwardPage" => '',
                        "msg" => mb_convert_encoding('请同意2345服务协议 @msg_agree', "utf8", "gbk")
                    )));
                }
                else
                {
                    showMessage('请同意2345服务协议', 'back');
                }
            }

            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                $_POST['username'] = mb_convert_encoding(trim($_POST['username']), "gbk", "utf8");
            }
            else
            {
                $_POST['username'] = trim($_POST['username']);
            }

            $regex = Config::get('regex');

            if (filter_var($_POST['username'], FILTER_VALIDATE_EMAIL))
            {
                $this->regEmail();
            }
            elseif (preg_match($regex['phone'], $_POST['username']))
            {
                $this->regPhone();
            }
            else
            {
                exit;
            }


        }
        else
        {
            $pageArray = array('flip' => "true");
            if (isset($_GET["flip"]) && $_GET["flip"] == 0)
            {
                $pageArray['flip'] = "false";
            }
            loadView('browser3/reg.tpl.html', $pageArray);
        }
    }

    public function regPhone()
    {
        if (!isset($_POST['validate_phone']) || $_POST['validate_phone'] == '' || !$this->actionCheckPhoneCode($_POST['username'], $_POST['validate_phone'], true))
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                )));
            }
            else
            {
                showMessage('验证码输入错误', 'back');
            }
        }

        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );

        $client_ip = get_client_ip();
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();

        $pwdScore = $Zxcvbn->passwordStrength($_POST['password']);
        $regAction = loadAction('reg');

        $source = \Service\AccountStat\Consts\AccountStat::SRC_IE_BROWSER3;
        $result = $regAction->regPhone($_POST['username'], $_POST['password'], $pwdScore['score'], $client_ip, 'login.2345.com', 200, $source);
        //$result[0] = '300.2';
        $states = explode('.', $result[0]);

        if ($states[0] == 400)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showError($msgs[$states[0]][$states[1]], 400);
            }
        }
        elseif ($states[0] == 300)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showMessage($msgs[$states[0]][$states[1]], 'back');
            }
        }
        elseif ($states[0] == 200)
        {
            if ($_COOKIE['referDomain'] != '')
            {
                $referDomain = $_COOKIE['referDomain'];
                $redis = RedisEx::getInstance();
                $redis->hIncrBy('referDomain:RSN', $referDomain, 1);
            }
            $this->doLogin($result[1]);
        }
    }


    /**
     * email注册
     */
    private function regEmail()
    {
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || $_POST['validate'] !== '' . $_SESSION['captcha_code'])
        {
            unset($_SESSION['captcha_code']);
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                )));
            }
            else
            {
                showMessage('验证码输入错误', 'back');
            }
        }
        unset($_SESSION['captcha_code']);

        $msgs = array(
            300 => array(
                0 => '请输入正确的邮箱地址',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此邮箱号已被注册',
                4 => '邮箱长度不能超过24个字符',
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $client_ip = get_client_ip();
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($_POST['password']);
        $regAction = loadAction('reg');

        $source = \Service\AccountStat\Consts\AccountStat::SRC_IE_BROWSER3;
        $result = $regAction->regEmail($_POST['username'], $_POST['password'], $pwdScore['score'], $client_ip, 'login.2345.com', $source);
        $states = explode('.', $result[0]);

        if ($states[0] == 400)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showError($msgs[$states[0]][$states[1]], 400);
            }
        }
        elseif ($states[0] == 300)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showMessage($msgs[$states[0]][$states[1]], 'back');
            }
        }
        elseif ($states[0] == 200)
        {
            if ($_COOKIE['referDomain'] != '')
            {
                $referDomain = $_COOKIE['referDomain'];
                $redis = RedisEx::getInstance();
                $redis->hIncrBy('referDomain:RSN', $referDomain, 1);
            }
            $this->doLogin($result[1]);
        }
    }


    /**
     * 检查手机验证码
     */
    public function actionCheckPhoneCode($phone = null, $code = null, $return = null)
    {
        $respCode = '200.0';
        $code = isset($_POST['code']) ? $_POST['code'] : $code;
        $phone = isset($_POST['phone']) ? $_POST['phone'] : $phone;
        $regex = Config::get('regex');
        if (preg_match($regex['phone'], $phone))
        {
            $redis = RedisEx::getInstance();

            $checkTime = $redis->get("regchecktime:" . $phone);
            if ($checkTime > 2)
            {
                $redis->del("regchecktime:" . $phone);
                $redis->del("regcode:" . $phone);
                $respCode = '400.0';
            }

            $checkcode = $redis->get("regcode:" . $phone);
            if (md5($phone . $code) == $checkcode && $checkcode)
            {
                $respCode = '200.0';
            }
            else
            {
                $redis->setex("regchecktime:" . $phone, 1800, ++$checkTime);
                $respCode = '400.0';
            }
        }
        else
        {
            $respCode = '300.0';
        }

        if ($return)
        {
            if (isset($redis))
            {
                $redis->del("regchecktime:" . $phone);
                $redis->del("regcode:" . $phone);
            }
            return $respCode == '200.0' ? TRUE : FALSE;
        }
        else
        {
            echo $respCode;
        }
    }


    /**
     * 登录（设置cookie并跳转）
     * @param  array $params 用户信息
     */
    private function doLogin($params)
    {
        $cookie = $params['cookie'];
        $refer = urldecode($params['refer']);
        // cookie过期时间
        $cTime = time() + 3600 * 24 * 30;
        $loginAction = loadAction('login');
        $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);
        // 处理回跳地址
        if (empty($refer))
        {
            if (isset($_COOKIE['qqforward']))
            {
                $refer = urldecode($_COOKIE['qqforward']);
            }
            else
            {
                $refer = 'http://www.' . DOMAIN;
            }
        }

        // 设置其他域名的登录cookie： 2345.cn等
        $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);

        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
        {
            $paramsEncodeKey = Config::get("paramsEncodeKey");
            $phone = \Common\Utils\Security::encodeString($paramsEncodeKey["default"], $params['phone']);
            exit(json_encode(array(
                "loadPage" => $thirdCallbackCookies,
                "forwardPage" => $refer,
                "msg" => '',
                'I' => $cookie['I'],
                'phone' => $phone,
            )));
        }
        else
        {
            $this->pageArray = array(
                "loadPage" => json_encode($thirdCallbackCookies),
                "forwardPage" => $refer
            );
            return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $this->pageArray);
        }
    }



    /**
     * 发送手机验证码
     */
    public function actionSendPhoneCode()
    {
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || $_POST['validate'] !== '' . $_SESSION['captcha_code'])
        {
            unset($_SESSION['captcha_code']);
            echo '600.0';
            return;
        }
        $phone = isset($_POST['phone']) ? $_POST['phone'] : null;
        if (preg_match('/^1[0123456789]\d{9}$/', $phone))
        {
            $memberModel = loadModel('member');
            $ret = $memberModel->checkPhone($_POST['phone'], 1);
            if ($ret != 0)
            {
                echo '400.0';
                return;
            }

            // 测试不发送手机注册验证码
            if (RUNMODE == 'development' || RUNMODE == 'testing')
            {
                $code = 123456;
                $redis = RedisEx::getInstance();
                $redis->setex("regchecktime:" . $phone, 1800, 0);
                $redis->setex("regcode:" . $phone, 1800, md5($phone . $code));
                echo '200.0';
                return;
            }

            $code = rand(100000, 999999);
            $result = sendCodeUsePhone($phone, $code, 8, 204 , 'pcbs');
            if ($result == 1)
            {
                $redis = RedisEx::getInstance();
                $redis->setex("regchecktime:" . $phone, 1800, 0);
                $redis->setex("regcode:" . $phone, 1800, md5($phone . $code));
                echo '200.0';
            }
            else if ($result == 2)
            {
                echo '400.0';
            }
            else
            {
                echo '500.0';
            }
        }
        else
        {
            echo '300.0';
        }
    }

}
