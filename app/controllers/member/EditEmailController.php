<?php

/**
 * 修改用户绑定邮箱
 */
class EditEmailController extends Controller
{

    private $pageArray, $email, $phone, $forward;

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        $memberModel = loadModel("member");
        $userinfo = $memberModel->read(PASSID);
        $this->pageArray['page'] = "email";
        $this->pageArray['nav'] = "editemail";
        $this->pageArray['phonetype'] = ($userinfo['phone']) ? "edit" : "bind";
        $this->pageArray['emailtype'] = ($userinfo['email'] && $userinfo['email_status']) ? "edit" : "bind";
        $this->pageArray['open'] = (isset($_GET['page']) && $_GET['page'] == "open") ? true : false;
        if ($userinfo['email_status'] == 0)
        {
            redirect('/member/bindEmail');
        }
        $this->email = $userinfo["email"];
        $emailPre = explode("@", $userinfo["email"]);
        if (strlen($emailPre[0]) > 7)
        {
            $emailPre[0] = substr($emailPre[0], 0, 3) . "****" . substr($emailPre[0], strlen($emailPre[0]) - 3, 3);
        }
        else
        {
            $emailPre[0] = substr($emailPre[0], 0, 3) . "****";
        }
        $this->pageArray['email'] = $emailPre[0] . "@" . $emailPre[1];
        if ($userinfo["phone"])
        {
            $this->phone = $userinfo["phone"];
            $this->pageArray['phone'] = substr($userinfo['phone'], 0, 3) . '****' . substr($userinfo['phone'], -4);
        }
        else
        {
            $this->phone = false;
            $this->pageArray['phone'] = false;
        }
        $this->forward = getForwardUrl();
        $this->pageArray['tabIndex'] = 5;
        $this->pageArray['forward'] = $this->forward;
        $this->pageArray['title'] = "修改邮箱-2345用户中心";
    }

    /**
     * 默认函数
     */
    public function actionIndex()
    {
        $mid = $_GET["mid"] ?? "";
        $midStr = ($mid == "" ?: "&mid=" . $mid);
        if (!$this->phone)
        {
            $url = "/member/editEmail/step2?from=email" . $midStr;
            $url .= ($this->forward) ? "&forward=" . urlencode($this->forward) : "";
            if ($this->pageArray['open'])
            {
                $url .= "&page=open";
            }
            redirect($url);
        }
        $this->pageArray['step'] = 1;
        if (IsMobile())
        {
            loadView('member/m/edit_email_wap.tpl.html', $this->pageArray);
        }
        else
        {
            loadView('member/edit_email.tpl.html', $this->pageArray);
        }
    }

    /**
     * 第二步
     */
    public function actionStep2()
    {
        $mid = $_GET["mid"] ?? "";
        $midStr = ($mid == "" ?: "&mid=" . $mid);
        $from = $_GET['from'] == 'phone' ? 'phone' : 'email';
        if ($from == 'phone')
        {
            if (!$this->phone)
            {
                if ($this->forward)
                {
                    redirect("/member/editEmail/step2?from=email&forward=" . urlencode($this->forward)) . $midStr;
                }
                else
                {
                    redirect("/member/editEmail/step2?from=email" . $midStr);
                }
            }
        }
        $this->pageArray['from'] = $from;
        $this->pageArray['step'] = 2;
        if (IsMobile())
        {
            loadView('member/m/edit_email_wap.tpl.html', $this->pageArray);
        }
        else
        {
            loadView('member/edit_email.tpl.html', $this->pageArray);
        }
    }

    /**
     * 第三步
     */
    public function actionStep3()
    {
        $from = $_POST['from'] == 'phone' ? 'phone' : 'email';
        $code = $_POST['code'];
        if ($from == "phone")
        {
            $phone = $this->phone;
            $verifycode = md5($phone . $code);
            $phoneAction = loadAction("phone");
            $status = $phoneAction->checkVerifyCode("login", PASSID, $verifycode);
        }
        else
        {
            $email = $this->email;
            $verifycode = md5($email . $code);
            $emailAction = loadAction("email");
            $status = $emailAction->checkVerifyCode("login", PASSID, $verifycode);
        }
        if (IsMobile())
        {
            if ($status)
            {
                $this->pageArray['from'] = $from;
                $this->pageArray['code'] = $code;
                $this->pageArray['step'] = 3;
                $this->pageArray['tabIndex'] = 2;
                loadView('member/m/edit_email_wap.tpl.html', $this->pageArray);
            }
            else
            {
                showMessage("验证码错误！", 'back');
            }
            return;
        }
        $flag = $status ? 200 : 400;
        echo $flag;
    }

    /**
     * 第四步
     */
    public function actionStep4()
    {
        $clientIp = get_client_ip();
        $encryptAction = loadAction('Encrypt');
        $_POST['email'] = $encryptAction::aes128cbcHexDecrypt($_POST['email']);
        $email = $_POST['email'];
        $code2 = $_POST['code2'];
        $checkcode = md5($email . $code2);
        $emailAction = loadAction("email");
        $status = $emailAction->checkEditCode("login", PASSID, $checkcode);
        if ($status)
        {
            $memberModel = loadModel('member');
            $userInfo = $memberModel->read(PASSID);
            $userInfo['passid'] = PASSID;

            $row = $memberModel->getEmailByPassid(PASSID);
            $emailOld = "{$row['email']}:{$row['email_status']}";
            $emailAction = loadAction('email');
            $ret = $emailAction->edit(PASSID, $email);
            if ($ret == '200.0')
            {
                //邮箱修改成功，添加日志
                //日志行为打点:用户信息添加或修改; type:USER_INFO_ADD; sub_type:EMAIL;
                $objLogV2Action = loadAction('logV2');
                $objLogV2Action->report('ALL', ($userInfo['email'] ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD'), 'EMAIL', $userInfo, $userInfo['email'], $email);

                xLog('changeUInfo', 'changeUInfo', PASSID . ' resetEmail');
                $memberModel->memberInfoLog(PASSID, array('email_old' => $emailOld, 'email' => $email, 'op_time' => time(), 'op_ip' => $clientIp, 'op_domain' => 'login.2345.com', 'op_from' => 'login.2345.com'));
            }
            if (IsMobile())
            {
                $this->pageArray['step'] = 4;
                $this->pageArray['tabIndex'] = 2;
                loadView('member/m/edit_email_wap.tpl.html', $this->pageArray);
                return;
            }
            echo substr($ret, 0, 3);
        }
        else
        {
            if (IsMobile())
            {
                showMessage("验证码错误！", 'back');
                return;
            }
            echo 400;
        }
    }

    /**
     * 发送验证码
     */
    public function actionSendCode()
    {
        $from = (isset($_POST['from']) && $_POST['from'] == 'phone') ? 'phone' : 'email';

        if (isset($_POST['email']))
        {
            $encryptAction = loadAction('Encrypt');
            $_POST['email'] = $encryptAction::aes128cbcHexDecrypt($_POST['email']);
            $email = $_POST['email'];
            $code = $_POST['verifycode'];
            $from = $_POST['from'];
            $emailAction = loadAction("email");
            $status = $emailAction->sendEditCode("login", PASSID, $email, $code, $from);
        }
        else
        {
            if ($from == 'phone')
            {
                $phoneAction = loadAction("phone");
                $status = $phoneAction->sendVerifyCode("login", PASSID, 5, 152);
                echo $status;
                return;
            }
            else
            {
                $emailAction = loadAction("email");
                $status = $emailAction->sendVerifyCode("login", PASSID);
            }
        }
        echo $status;
    }

    /**
     * 检查验证码
     */
    public function actionCheckCode()
    {
        $code = $_POST['code'];
        $from = (isset($_POST['from']) && $_POST['from'] == 'phone') ? 'phone' : 'email';
        if (isset($_POST['email']))
        {
            $encryptAction = loadAction('Encrypt');
            $_POST['email'] = $encryptAction::aes128cbcHexDecrypt($_POST['email']);
            $email = $_POST['email'];
            $checkcode = md5($email . $code);
            $emailAction = loadAction("email");
            $status = $emailAction->checkEditCode("login", PASSID, $checkcode);
        }
        else
        {
            if ($from == 'phone')
            {
                $phone = $this->phone;
                $checkcode = md5($phone . $code);
                $phoneAction = loadAction("phone");
                $status = $phoneAction->checkVerifyCode("login", PASSID, $checkcode);
            }
            else
            {
                $email = $this->email;
                $checkcode = md5($email . $code);
                $emailAction = loadAction("email");
                $status = $emailAction->checkVerifyCode("login", PASSID, $checkcode);
            }
        }
        $flag = $status ? "200" : "400";
        echo $flag;
    }

}
