<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：KeyProvider.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：27/08/2018 10:25
 */

namespace Service\Lib\Action;

use SmsServiceConfig;

class KeyProvider
{
    /**
     * 获取数据存储方式redis key type string
     * User: panj
     * @return string
     */
    public static function getSmsServiceStorageTypeKey()
    {
        return SmsServiceConfig::SERVICE_STORAGE_TYPE;
    }

    /**
     * User: panj
     * @return string
     */
    public static function getSmsServiceLastLogIdKey()
    {
        return SmsServiceConfig::SERVICE_LOG_ID_LAST;
    }

    /**
     * User: panj
     *
     * @param int $logId logId
     *
     * @return string
     */
    public static function getSmsServiceLogIdDataKey($logId)
    {
        return SmsServiceConfig::SERVICE_LOG_ID_HASH_DATA . ':' . $logId;
    }

    /**
     * User: panj
     * @return string
     */
    public static function getSmsServiceLogIdSetKey()
    {
        return SmsServiceConfig::SERVICE_LOG_ID_SET;
    }

    /**
     * User: panj
     *
     * @param int $logId logId
     *
     * @return string
     */
    public static function getSmsLogsCacheKey($logId)
    {
        return SmsServiceConfig::SERVICE_SMS_LOGS_CACHE . ':' . $logId;
    }

    /**
     * User: panj
     *
     * @param int $logId logId
     *
     * @return string
     */
    public static function getSmsTagLogKey($logId)
    {
        return SmsServiceConfig::SERVICE_SMS_TAG_LOG . ':' . $logId;
    }

    /**
     * User: panj
     *
     * @param int $logId logId
     *
     * @return string
     */
    public static function getSmsVoiceLogsList($logId)
    {
        return SmsServiceConfig::SERVICE_SMS_VOICE_LOGS_LIST . ':' . $logId;
    }

    /**
     * User: panj
     * -
     * @return string
     */
    public static function getCallBackLogIdTagKey()
    {
        return SmsServiceConfig::SERVICE_SMS_CACHE_LOG_ID_CALL_BACK;
    }
}
