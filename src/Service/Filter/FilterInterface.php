<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：FilterInterface.php
 * 摘   要：短信发送提交服务商发送之前的参数过滤接口
 * 作   者：panj
 * 修改日期：31/08/2018 09:34
 */

namespace Service\Filter;

interface FilterInterface
{
    /**
     * User: panj
     *
     * @param array $data data
     *
     * @return mixed
     */
    public function initData($data);

    /**
     * User: panj
     * 前置检查
     * @param array $data data
     * @return mixed
     */
    public function preCheck($data);

    /**
     * User: panj
     * 后置检查
     * @return mixed
     */
    public function strictCheck();

    /**
     * User: panj
     * LogId 生成
     * @return mixed
     */
    public function initLogId();

    /**
     * User: panj
     * run method
     * @param array $data data
     * @return mixed
     */
    public function run($data);

    /**
     * User: panj
     *
     * @param array $data data
     *
     * @return mixed
     */
    public function request($data);
}
