<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：YyCallbackModel.php
 */

namespace YyInterface;
class YyCallbackModel extends \Model
{

    /**
     * 初始数据库
     */
    public function __construct()
    {
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    public function addVoiceCode($inserData)
    {
        $isTrue = $this->pdo->insert('freecall_voice_code', $inserData);
        return $isTrue !== false ? true : false;
    }

    public function upVoiceCode($upData, $condition)
    {
        $isTrue = $this->pdo->update('freecall_voice_code', $upData, $condition);
        return $isTrue !== false ? true : false;
    }
}
