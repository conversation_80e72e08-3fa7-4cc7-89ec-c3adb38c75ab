<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/7/29
 * Time: 19:20
 */

namespace Smschannel;

use FuncAction;

class YunRongChannelAction extends SmsChannelAction
{

    /**
     * 错误列表
     * @param int $code 错误ocde
     * @return mixed|string
     */
    private static function getMsg($code)
    {
        $msgList = [
            0     => '失败，提交抛出异常',
            - 1   => '用户名或者密码不正确',
            - 2   => '必填选项为空或扣费条数小于0',
            - 3   => '短信内容0个字节',
            - 4   => '0个有效号码',
            - 5   => '余额不够',
            - 10  => '用户被禁用',
            - 11  => '短信内容超过500字',
            - 12  => '无扩展权限（ext字段需填空）',
            - 13  => 'IP校验错误',
            - 14  => '内容解析异常',
            - 24  => '手机号码超过限定个数',
            - 25  => '没有提交权限',
            - 990 => '未知错误',
        ];

        return isset($msgList[$code]) ? $msgList[$code] : '未知错误';
    }


    /**
     * YunrongChannelAction constructor.
     * @param string $user   用户名
     * @param string $pwd    密码
     * @param string $apiUrl 供应商URL
     */
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }

    /**
     * @param int    $phone 手机号码
     * @param string $msg   发送内容
     * @param string $sign  签名
     * @return mixed
     */
    public function send($phone, $msg, $sign)
    {
        $phoneStr = $phone;
        $multi = 0;
        if (is_array($phoneStr)) {
            $phoneStr = implode(",", $phoneStr);
            $multi = 1;
        }
        $msg_u8 = iconv('GBK', 'UTF-8', $sign . $msg);
        $params = [
            'username' => $this->user,
            'password' => md5($this->user . md5($this->pwd)),
            'content'  => $msg_u8,
            'mobile'   => $phoneStr,
            'dstime'   => '',   //可为空 定时时间，为空时表示立即发送格式：yyyy-MM-dd HH:mm:ss
            'ext'      => '',   //可为空用户自定义扩展。需要向客户经理申请开通扩展签名
            'msgid'    => 'yr' . time() . rand(100000, 999999),   //可为空 客户自定义消息id。如果客户不传，平台会自动生成消息id。
            'msgfmt'   => '',   //可为空
        ];
        $msgId = $params['msgid'];
        $response = FuncAction::postTimeout($this->apiUrl, $params);
        if ($response > 0) {
            if ($multi) {
                foreach ($phone as $phoneLine) {
                    $this->msgIdList[$phoneLine] = $msgId;
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_SUCC,
                        'codeStatus' => 0,
                        'codeDesc'   => '成功',
                    );
                }
            } else {
                $this->setMsgId($msgId);
                $this->OperatorMsg = array(
                    'result'     => self::RET_SUCC,
                    'codeStatus' => 0,
                    'codeDesc'   => '成功',
                );
            }

            return self::RET_SUCC;
        } else {
            if ($multi) {
                foreach ($phone as $phoneLine) {
                    $this->msgIdList[$phoneLine] = 0;
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_ERR,
                        'codeStatus' => - 1,
                        'codeDesc'   => mb_convert_encoding(self::getMsg($response), 'GBK', 'UTF-8'),
                    );
                }
            } else {
                $this->setMsgId(0);
                $this->OperatorMsg = array(
                    'result'     => self::RET_ERR,
                    'codeStatus' => - 1,
                    'codeDesc'   => mb_convert_encoding(self::getMsg($response), 'GBK', 'UTF-8'),
                );
            }

            return self::RET_ERR;
        }
    }

    /**
     * @param array  $phone 手机号码列表
     * @param string $msg   发送短信内容
     * @param string $sign  签名
     * @return mixed
     */
    public function multiPhoneSend($phone, $msg, $sign)
    {
        return $this->send($phone, $msg, $sign);
    }

    /**
     * set msgId
     * @param string $msgId msgId
     * @return null
     */
    private function setMsgId($msgId)
    {
        $this->msgId = $msgId;
    }
}
