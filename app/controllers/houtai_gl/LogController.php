<?php

use Octopus\PdoEx;
includeBase('AdminController');

class LogController extends AdminController
{

    protected $authName = '注册用户编辑';

    public function actionIndex()
    {
        $type = $_GET['type'];
        $dbConfig = Config::get("database");
        $pdo = PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
        $rows = array();
        if ($type == 'email')
        {
            $passid = intval($_GET['passid']);
            $sql = "select * from members_info_log where uid = :uid order by id desc limit 30";
            $rows = $pdo->findAll($sql, array(":uid" => $passid));
        }
        else if ($type == 'phone')
        {
            $passid = intval($_GET['passid']);
            $sql = "select * from members_phone_log where passid = :passid order by id desc limit 30";
            $rows = $pdo->findAll($sql, array(":passid" => $passid));
        }
        else if ($type == 'username')
        {
            $passid = intval($_GET['passid']);
            $sql = "select * from members_log where passid = :passid order by id desc limit 30";
            $rows = $pdo->findAll($sql, array(":passid" => $passid));
        }
        $pageArray['type'] = $type;
        $pageArray['rows'] = $rows;
        PdoEx::delInstance(DB_PASSPORT_LOG);
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']))
        {
            loadView('admin/log.tpl.html', $pageArray);
        }
        else
        {
            $str = '<tr>
                <td>序号</td>';
            if ($type == 'email')
            {
                $str .= '<td>旧EMAIL</td>
                <td>新EMAIL</td>';
                
            }
            elseif ($type == 'phone')
            {
                $str .= '<td>旧手机</td>
                <td>新手机</td>';
            }
            elseif ($type == 'username')
            {
                $str .= '<td>旧用户名</td>
                <td>新用户名</td>';
            }
            $str .= '<td>操作时间</td>
                <td>操作IP</td>
                <td>操作域名</td>';
                
            if ($type == 'phone')
            {
                $str .= '<td>操作来源</td>';
            }
            $str .= '</tr>';
            if ( empty ($rows) )
            {
                if ($type == 'phone')
                {
                    $colspan = 7;
                }
                else
                {
                    $colspan = 6;
                }
                $str .= '<tr>
                        <td colspan="'.$colspan.'" class="noMess">
                            暂无修改记录
                        </td>
                    </tr>';
            }
            else
            {
                foreach ($rows as $key=>$vitem)
                {
                    $valKey = $key+1;
                    $str .= '<tr>';
                    $str .= '<td>' . $valKey .'</td>';
                    
                    if ($type == 'email')
                    {
                        $str .= '<td>'.$vitem['email_old'] .'</td>';
                        $str .= '<td>'.$vitem['email'].'</td>';
                    }
                    elseif ($type == 'phone')
                    {
                        $str .= '<td>'.$vitem['phone_old'].'</td>';
                        $str .= '<td>'.$vitem['phone'].'</td>';
                    }
                    elseif ($type == 'username')
                    {
                        $str .= '<td>'.$vitem['username_old'].'</td>';
                        $str .= '<td>'.$vitem['username'].'</td>';
                    }
                    $str .= '<td>'. date('Y-m-d H:i:s',$vitem['op_time']) .'</td>';
                    $str .= '<td>'.$vitem['op_ip'].'</td>';
                    $str .= '<td>'.$vitem['op_domain'].'</td>';
                    if ($type == 'phone')
                    {
                        $str .= '<td>'.$vitem['op_refer'].'</td>';
                    }
                    $str .= '</tr>';
                
                }
            }
            echo json_encode ( array ( 'log' => iconv('gbk','utf-8',$str)));
        }
        
    }

}
