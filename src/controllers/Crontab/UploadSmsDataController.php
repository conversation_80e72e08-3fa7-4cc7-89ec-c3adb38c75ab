<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/9/6
 * Time: 17:29
 */
namespace Crontab;

use Controller;
use Octopus\RedisEx;
use Service\Lib\Action\KeyProvider;
use Service\Swoole\Tool\Encoding;
use Service\Swoole\Tool\Log;
use WebLogger\Facade\LoggerFacade;

class UploadSmsDataController extends Controller
{

    private $runTime = '';

    /**
     * 开启定时
     * -
     * @param int $time 时间戳
     * @return void
     */
    private function startTimer($time)
    {
        $this->runTime = time() + $time;
        LoggerFacade::info('开启定时执行,时间为：' . date('Y-m-d H:i:s', $this->runTime));
    }

    /**
     * 判断是否过期
     * -
     * @return bool
     */
    private function isExpire()
    {
        if ($this->runTime >= time())
        {
            return true;
        }
        else
        {
            LoggerFacade::info('脚本允许已达定时时间，退出脚本。');
            return false;
        }
    }

    /**
     * redis 落数据库
     * @param int $time 时间
     * -
     * @return void
     */
    public function actionIndex(int $time = 600)
    {
        $fileName = 'cronLog';
        $redis = \RedisAction::connect();
        $this->startTimer($time - 3);
        while ($this->isExpire())
        {
            $nowTime = time() - $time;
            $c = 0;
            while ($listData = $redis->zRangeByScore(KeyProvider::getSmsServiceLogIdSetKey(), 0, $nowTime, ['limit' => [0, 1000]]))
            {
                //执行结束
                if (!$this->isExpire())
                {
                    break;
                }
                foreach ($listData as $listInfo)
                {
                    ++$c;
                    $queryData = json_decode($listInfo, true);
                    $data = $redis->hGetAll(KeyProvider::getSmsServiceLogIdDataKey($queryData['logId']));
                    $isTrue = \ServiceSmsModel::getInstance()->uploadServiceData($data, $listInfo);
                    if ($isTrue)
                    {
                        $isDel = $redis->zRem(KeyProvider::getSmsServiceLogIdSetKey(), $listInfo);
                        if ($isDel !== false)
                        {
                            $redis->hDel(KeyProvider::getSmsServiceLogIdDataKey($queryData['logId']));
                        }
                        else
                        {
                            LoggerFacade::info('error upload sms：' . json_encode(Encoding::transcoding($listInfo, 'utf-8')));
                        }
                    }
                    else
                    {
                        $redis->zRem(KeyProvider::getSmsServiceLogIdSetKey(), $listInfo);
                        $redis->hDel(KeyProvider::getSmsServiceLogIdDataKey($queryData['logId']));
                        LoggerFacade::info('error upload sms：' . json_encode(Encoding::transcoding($listInfo, 'utf-8')) . json_encode(Encoding::transcoding($data, 'utf-8')));
                    }
                }
                if ($c >= 20000)
                {
                    RedisEx::delInstance('default');
                    \ServiceSmsModel::getInstance()->close();
                    \ServiceSmsModel::getInstance()->reopen();
                    $redis = \RedisAction::connect();
                    LoggerFacade::info('运行了:' . $c . '释放数据库连接');
                    $c = 0;
                }
            }

            sleep(1);
        }
        LoggerFacade::info('end');
    }
}
