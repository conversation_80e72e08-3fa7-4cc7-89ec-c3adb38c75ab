<!DOCTYPE html>
<html>
<head>
    <meta charset="gb2312">
    <title>短信服务平台</title>
    <link rel="stylesheet" href="/style/vue/element/element-ui.css">
</head>
<body>
<div id="app">
    <el-container>
        <el-header style="padding: 10px;margin:0px 20px; background-color: rgb(225,230,244);">
            <el-row>
                <el-col :span="16">
                    <el-breadcrumb separator-class="el-icon-arrow-right" style="margin-top: 12px;margin-left: 20px">
                        <el-breadcrumb-item>短信通道</el-breadcrumb-item>
                        <el-breadcrumb-item>发送记录</el-breadcrumb-item>
                    </el-breadcrumb>
                </el-col>
            </el-row>
        </el-header>
        <el-main style="padding-top: 5px">
            <el-form label-position="right" label-width="60px">
                <el-row :gutter="5">
                    <el-col :span="11">
                        <el-form-item label="时间段">
                            <el-date-picker
                                    :editable="false"
                                    format="yyyy-MM-dd HH:mm"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    style="width:100%"
                                    v-model="form.dateRange"
                                    type="datetimerange"
                                    range-separator="到"
                                    start-placeholder="开始日期时间"
                                    end-placeholder="结束日期时间"
                                    :default-time="['00:00:00', '23:59:59']">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="5">
                        <el-form-item label="手机号">
                            <el-input placeholder="11位手机号或前缀" v-model="form.phone" style="width: 100%" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="提交">
                            <el-select v-model="form.submitStatus" placeholder="全部状态" style="width: 100%" clearable>
                                <el-option v-for="item in options.submitStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="4">
                        <el-form-item label="回执">
                            <el-select v-model="form.callbackStatus" placeholder="全部状态" style="width: 100%" clearable>
                                <el-option v-for="item in options.callbackStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="5">
                    <el-col :span="6">
                        <el-form-item label="服务商">
                            <el-select v-model="form.operator" placeholder="全部" style="width: 100%" filterable clearable>
                                <el-option v-for="item in options.operator" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="项目">
                            <el-select v-model="form.project" @change="handleProjectChange" placeholder="全部" style="width: 100%" filterable clearable>
                                <el-option v-for="item in options.project" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="类型">
                            <el-select v-model="form.type" placeholder="全部" style="width: 100%" filterable clearable>
                                <el-option v-for="item in options.type" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="位置">
                            <el-select v-model="form.position" placeholder="全部" style="width: 100%" filterable clearable>
                                <el-option v-for="item in options.position" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <sticky class="sticky" :z-index="5" :sticky-top="0" :height="stickyHeight">
                    <el-row :gutter="5" style="margin: 10px 0">
                        <el-col :span="21">
                            <template>
                                <el-checkbox-group v-model="columnList" size="mini">
                                    <el-checkbox-button label="text" value="1">内容</el-checkbox-button>
                                    <el-checkbox-button label="count">计费</el-checkbox-button>
                                    <el-checkbox-button label="account">账号</el-checkbox-button>
                                    <el-checkbox-button label="operator">通道</el-checkbox-button>
                                    <el-checkbox-button label="type">类型</el-checkbox-button>
                                    <el-checkbox-button label="project">项目</el-checkbox-button>
                                    <el-checkbox-button label="position">位置</el-checkbox-button>
                                    <el-checkbox-button label="ip">IP</el-checkbox-button>
                                    <el-checkbox-button label="send">提交</el-checkbox-button>
                                    <el-checkbox-button label="submit">发送</el-checkbox-button>
                                    <el-checkbox-button label="callback">回执</el-checkbox-button>
                                    <el-checkbox-button label="page">页码</el-checkbox-button>
                                </el-checkbox-group>
                            </template>
                        </el-col>
                        <el-col :span="3">
                            <el-button @click="submit" type="primary" :loading="loading" size="mini" style="float: right">查询</el-button>
                        </el-col>
                    </el-row>
                    <el-pagination
                            v-loading="loading"
                            v-show="column.page"
                            background
                            @current-change="handlePageChange"
                            :current-page.sync="page.current"
                            :page-size="page.size"
                            layout="total, prev, pager, next, jumper"
                            :total="page.total">
                    </el-pagination>
                </sticky>
            </el-form>
            <template>
                <el-table :data="table" @cell-dblclick="cellClick" v-loading="loading" element-loading-text="加载中…" fit highlight-current-row style="width: 100%">
                    <el-table-column property="phone" label="手机号" width="100">
                        <template slot-scope="scope">
                            <el-tooltip :content="scope.row.logId" placement="right">
                                <span>{{ scope.row.phone}}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <el-table-column property="text" v-if="column.text" label="内容" min-width="220"></el-table-column>
                    <el-table-column property="smsCount" v-if="column.count" v-if="column.smsCount" label="计费" align="center" width="50"></el-table-column>
                    <el-table-column v-if="column.operator" label="服务商">
                        <template slot-scope="scope">
                            <span>{{ translateParam('operator',scope.row.operator) }}</span>
                            <span v-if="column.account"><br/>{{ scope.row.account }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.type" label="类型">
                        <template slot-scope="scope">
                            <span>{{ translateParam('type',scope.row.type) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.project" label="项目">
                        <template slot-scope="scope">
                            <span>{{ translateParam('project',scope.row.project) }}</span>
                            <span v-if="scope.row.mid"><br/>{{ scope.row.mid }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.position" label="位置">
                        <template slot-scope="scope">
                            <span>{{ translateParam('positionAll',scope.row.position) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.ip" label="IP(S/C)" align="center" min-width="100">
                        <template slot-scope="scope">
                            <span>{{ scope.row.serverIp }}</span><br/><span>{{ scope.row.clientIp }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.send" label="提交时间" align="center" width="85">
                        <template slot-scope="scope">
                            <span>{{ scope.row.sendTime|showDate }}</span><br/><span>{{ scope.row.sendTime|showTime }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.send" label="提交状态" align="center">
                        <template slot-scope="scope">
                            <span v-if="scope.row.sendStatus">{{ scope.row.sendResponse }}({{ scope.row.sendStatus }})</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.submit" label="发送时间" align="center" width="85">
                        <template slot-scope="scope">
                            <span>{{ scope.row.submitTime|showDate }}</span><br/><span>{{ scope.row.submitTime|showTime }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.submit" label="发送状态" align="center">
                        <template slot-scope="scope">
                            <span v-if="scope.row.submitStatus">{{ scope.row.submitResponse }}({{ scope.row.submitStatus }})</span>
                        </template>
                    </el-table-column>
                    <el-table-column v-if="column.callback" label="回执时间" align="center" width="85">
                        <template slot-scope="scope">
                            <span>{{ scope.row.callbackTime|showDate }}</span><br/><span>{{ scope.row.callbackTime|showTime }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column property="callbackStatus" v-if="column.callback" label="回执状态" align="center" width="80">
                        <template slot-scope="scope">
                            <el-tooltip placement="left">
                                <pre slot="content">{{ scope.row.callbackResponse }}</pre>
                                <span>{{ scope.row.callbackStatus|translateCallback }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
        </el-main>
    </el-container>
</div>
</body>
<style>
    .el-form-item {
        margin-bottom: 5px;
    }
    .el-table td {
        padding: 8px 0;
    }
    .el-table .cell {
        line-height: 16px;
        padding-left: 7px;
        padding-right: 7px;
    }
</style>
<script src="/style/vue/vue[|if $smarty.const.RUNMODE eq 'production'|].min[|/if|].js"></script>
<script src="/style/vue/components/vue-resource[|if $smarty.const.RUNMODE eq 'production'|].min[|/if|].js"></script>
<script src="/style/vue/element/element-ui.js"></script>
<script src="/style/vue/components/sticky.js"></script>
<!--<script src="/style/vue/global.js"></script>-->
<script>
    Date.prototype.Format = function (fmt) {
        var o = {
            "M+": this.getMonth() + 1, //月份
            "d+": this.getDate(), //日
            "h+": this.getHours(), //小时
            "m+": this.getMinutes(), //分
            "s+": this.getSeconds(), //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (const k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    };
</script>
<script>
    //默认application/json;改为x-www-form-urlencoded
    Vue.http.options.emulateJSON = true;
    let vm;
    vm = new Vue({
        el: '#app',
        components: {'sticky': Sticky},
        data: function () {
            return {
                form: {
                    logId: '',
                    dateRange: [],
                    phone: '',
                    submitStatus: '',
                    callbackStatus: '',
                    operator: '',
                    type: '',
                    project: '',
                    position: '',
                },
                page: {
                    current: 1,
                    size: 100,
                    total: 0,
                    show: false,
                },
                options: {
                    operator: undefined,
                    type: undefined,
                    project: undefined,
                    positionAll: undefined,
                    position: undefined,
                    submitStatus: [{label: '成功', value: 1}, {label: '失败', value: 2}, {label: '未知', value: 9}],
                    callbackStatus: [{label: '成功', value: 1}, {label: '失败', value: 2}, {label: '未知', value: 9}],
                },
                column: {
                    text: true,
                    account: false,
                    operator: true,
                    type: true,
                    project: true,
                    position: true,
                    ip: false,
                    send: true,
                    submit: false,
                    callback: true,
                    count: false,
                    page: false,
                },
                columnList: ['text', 'operator', 'type', 'project', 'position', 'send', 'callback'],
                table: null,
                loading: false,
            }
        },
        computed: {
            stickyHeight: function () {
                return 50 + 30 * this.column.page
            }
        },
        watch: {
            columnList: function (val) {
                for (const item in this.column) {
                    if (val.indexOf(item) !== -1) {
                        this.column[item] = true;
                        if (item === 'page') {
                            this.page.show = true;
                        }
                    } else {
                        this.column[item] = false;
                        if (item === 'page') {
                            this.page.show = false;
                        }
                    }
                }
            }
        },
        filters: {
            showDate: function (val) {
                return typeof val === 'string' ? val.substr(0, 10) : "";
            },
            showTime: function (val) {
                return typeof val === 'string' ? val.substr(11, 8) : "";
            },
            translateCallback: function (val) {
                switch (val) {
                    case '1':
                        return '成功';
                    case '0':
                        return '失败';
                    case null:
                        return '';
                }
                return '未知(' + val + ')';
            },
        },
        methods: {
            cellClick: function (row, column, cell, event) {
                if (column.property === 'phone') {
                    if (row.phone.indexOf("*") != -1)
                    {
                        //解密
                        this.loading = true;
                        this.fetch('/Admin/SmsQueryLog/Api?func=decrypt', {
                            log_id: row.logId,
                            data: this.form
                        }).then((d => {
                            row.phone = d.phone;
                            this.loading = false;
                        }), (err) => {
                            this.$message.error(err);
                            this.loading = false;
                        });
                    }
                    else
                    {
                        row.phone = row.phone.substr(0, 3) + '****' + row.phone.substr(7);
                    }
                }
            },
            handleProjectChange: function (e) {
                this.form.position = '';
                this.fetch('/Admin/SmsQueryLog/Api?func=positionList', {project: this.form.project}).then(
                    d => {
                        this.options.position = JSON.parse(d.data);
                    }, (err) => this.$message.error(err)
                );
            },
            handlePageChange: function () {
                //防止page.current不更新
                setTimeout(() => {
                    this.query()
                }, 200);
            },
            submit: function () {
                this.page.current = 1;
                this.page.total = 0;
                this.query();
            },
            query: function () {
                this.loading = true;
                this.fetch('/Admin/SmsQueryLog/Api?func=data', {
                    data: this.form,
                    page: this.page,
                }).then((d => {
                    this.table = JSON.parse(d.data);
                    this.page.total = parseInt(d.total);
                    this.loading = false;
                }), (err) => {
                    this.$message.error(err);
                    this.loading = false;
                });
            },
            fillSelect: function () {
                this.fetch('/Admin/SmsQueryLog/Api?func=operatorList', {}).then(
                    d => {
                        this.options.operator = JSON.parse(d.data);
                    }, (err) => this.$message.error(err)
                );
                this.fetch('/Admin/SmsQueryLog/Api?func=typeList', {}).then(
                    d => {
                        this.options.type = JSON.parse(d.data);
                    }, (err) => this.$message.error(err)
                );
                this.fetch('/Admin/SmsQueryLog/Api?func=projectList', {}).then(
                    d => {
                        this.options.project = JSON.parse(d.data);
                    }, (err) => this.$message.error(err)
                );
                this.fetch('/Admin/SmsQueryLog/Api?func=positionList', {}).then(
                    d => {
                        this.options.positionAll = JSON.parse(d.data);
                    }, (err) => this.$message.error(err)
                );
            },
            post: function ($url, $postArr) {
                return new Promise((resolve, reject) => {
                    this.$http.post($url, $postArr).then(res => resolve(res), res => reject(res));
                });
            },
            fetch: function ($url, $postArr) {
                return new Promise((resolve, reject) => {
                    this.post($url, $postArr).then(res => {
                            if (res.data.err === 0) {
                                resolve(res.data);
                            } else if (res.data.msg) {
                                reject(res.data.err + '-' + res.data.msg)
                            } else {
                                reject(res.data.err)
                            }
                        }, res => reject(res.status)
                    );
                });
            },
            translateParam: function (p, v) {
                for (const i of this.options[p]) {
                    if (parseInt(i.value) === parseInt(v)) {
                        return i.label
                    }
                }
                return v + '-未知';
            },
        },
        mounted: function () {
            let date = new Date();
            date.setDate(date.getDate());
            this.form.dateRange = [date.Format("yyyy-MM-dd 00:00:00"), date.Format("yyyy-MM-dd 23:59:59")]
            this.fillSelect();
            //默认不展示数据信息
//            this.submit();
        }
    });
</script>
</html>
