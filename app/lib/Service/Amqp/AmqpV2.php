<?php
/**
 * Copyright (c) 2022,上海二三四五网络科技有限公司
 * 摘    要: 用户中心-rabbitmq操作-V2版本
 * 作    者: 朱锋锦
 * 日    期: 2022-03-25
 */

namespace Service\Amqp;

class AmqpV2
{
    private static $arrInstance = []; // 连接单例的数组

    private $objConn = null; // 连接
    private $objChannel = null; // 通道
    private $arrExchange = []; // 交换机数组
    private $arrQueue = []; // 队列数组

    /**
     * Amqp constructor.
     * @param array $arrConfig 配置
     * @throws AMQPConnectionException
     */
    private function __construct(array $arrConfig = [])
    {
        if (class_exists('AMQPConnection')) {
            $arrConfig = $this->randConnect($arrConfig);
            $this->objConn = new \AMQPConnection($arrConfig);
            if ($this->objConn->connect()) {
                $this->objChannel = new \AMQPChannel($this->objConn);
                if (!$this->objChannel->isConnected()) {
                    throw new Exception('can not connect to amqp channel');
                }
            } else {
                throw new Exception('can not connect to amqp server');
            }
        } else {
            throw new Exception('class "AMQPConnection" not exists');
        }
    }

    /**
     * 功    能：判断是否有多个连接，有的话随机返回一个
     * 修改日期：2020/4/13
     * 作    者：杨文斌
     *
     * @param array $arrConfig 配置
     * @return array
     */
    protected function randConnect($arrConfig)
    {
        if (!isset($arrConfig['host'])) {
            return $arrConfig;
        }
        if (strpos($arrConfig['host'], ',') === false) {
            return $arrConfig;
        }
        $hostArr = explode(',', $arrConfig['host']);
        //如果web请求，说明是生产消息
        if (isWebRequest()) {
            $randKey = array_rand($hostArr);
            $arrConfig['host'] = trim($hostArr[$randKey]);
        } else {
            //消费就取第一个
            $arrConfig['host'] = trim(array_shift($hostArr));
        }
        return $arrConfig;
    }

    /**
     * 获取单例
     * @param string $source 配置
     * @return static
     * @throws AMQPConnectionException
     */
    public static function getInstance($source)
    {
        $arrConfig = \Config::get('rabbitMq')[$source];
        $strKey = $source ? : 'default';
        if (!isset(self::$arrInstance[$strKey])) {
            self::$arrInstance[$strKey] = new self($arrConfig);
        }
        return self::$arrInstance[$strKey];
    }

    /**
     * 关闭连接
     * @return bool
     */
    public function close()
    {
        if ($this->objChannel && $this->objChannel->isConnected()) {
            $this->objChannel->close();
        }
        if ($this->objConn && $this->objConn->isConnected()) {
            $this->objConn->disconnect();
        }
        return true;
    }

    /**
     * 删除单例实例
     * @param string $source $source
     * @return void
     */
    public static function delInstance($source)
    {
        $strKey = $source ? : 'default';
        if (self::$arrInstance[$strKey]) {
            self::$arrInstance[$strKey] = null;
        }
    }

    /**
     * 设置Exch
     * @param string $strExchangeName 交换器名称
     * @param string $strType AMQP_EX_TYPE_DIRECT（直接投递）、AMQP_EX_TYPE_FANOUT、AMQP_EX_TYPE_HEADERS、AMQP_EX_TYPE_TOPIC
     * @param int $intFlag AMQP_NOPARAM、AMQP_DURABLE（持久化）、AMQP_PASSIVE、AMQP_AUTODELETE，可以多项并用
     * @return boolean
     * @throws AMQPConnectionException
     * @throws AMQPExchangeException
     */
    private function setExchange(
        string $strExchangeName,
        string $strType = AMQP_EX_TYPE_DIRECT,
        int $intFlag = AMQP_DURABLE
    ) {
        $blnRe = false;
        if ($this->objChannel) {
            if (!isset($this->arrExchange[$strExchangeName])) {
                $this->arrExchange[$strExchangeName] = new \AMQPExchange($this->objChannel);
                $this->arrExchange[$strExchangeName]->setName($strExchangeName);
                $this->arrExchange[$strExchangeName]->setType($strType);
                $this->arrExchange[$strExchangeName]->setFlags($intFlag);
                $this->arrExchange[$strExchangeName]->declareExchange();
            }
            $blnRe = true;
        }
        return $blnRe;
    }

    /**
     * 设置队列，并绑定交换机
     * @param string $strQueueName 队列名
     * @param string $strBindRouteKey 为空时，与队列名一致
     * @param string $strExchangeName 交换器名称
     * @param string $strUnbindRouteKey 解除绑定的Routekey
     * @param int $intFlag AMQP_DURABLE（持久化）、AMQP_PASSIVE、AMQP_EXCLUSIVE、AMQP_AUTODELETE，可以多项并用
     * @return boolean
     */
    private function setQueue(
        string $strQueueName,
        string $strBindRouteKey = '',
        string $strExchangeName = '',
        string $strUnbindRouteKey = '',
        int $intFlag = AMQP_DURABLE
    ) {
        $blnRe = false;
        if ($this->objChannel) {
            if (!isset($this->arrQueue[$strQueueName])) {
                $this->arrQueue[$strQueueName] = new \AMQPQueue($this->objChannel);
                $this->arrQueue[$strQueueName]->setName($strQueueName);
                $this->arrQueue[$strQueueName]->setFlags($intFlag);
                $this->arrQueue[$strQueueName]->declareQueue();
                strlen($strBindRouteKey) || $strBindRouteKey = $strQueueName;
                $this->arrQueue[$strQueueName]->bind($strExchangeName, $strBindRouteKey);
            }
            strlen($strUnbindRouteKey) && $this->arrQueue[$strQueueName]->unbind($strExchangeName, $strUnbindRouteKey);
            $blnRe = true;
        }
        return $blnRe;
    }

    /**
     * 功    能: 取队列中未处理的消息个数
     * @param string $strQueueName $strQueueName
     * @param int $intFlag $intFlag
     * @return false or int
     * @throws AMQPConnectionException
     * @throws AMQPQueueException
     */
    public function getQueueLen(string $strQueueName, int $intFlag = AMQP_DURABLE)
    {
        $intRe = false;
        if ($this->objChannel) {
            if (!isset($this->arrQueue[$strQueueName])) {
                $this->arrQueue[$strQueueName] = new \AMQPQueue($this->objChannel);
                $this->arrQueue[$strQueueName]->setName($strQueueName);
                $this->arrQueue[$strQueueName]->setFlags($intFlag);
            }
            $intRe = $this->arrQueue[$strQueueName]->declareQueue();
        }
        return $intRe;
    }

    /**
     * 生产者发布一条消息 (此方法有个废弃字段，建议用putDirectMsg)
     * @param string $strMsg 消息体
     * @param string $strQueueName $strQueueName
     * @param string $strRouteKey 交换器投递的路由
     * @param string $strExchangeName 交换器
     * @param array $arrAttr 'delivery_mode' => 2 表示消息持久化
     * @param int $intFlag AMQP_NOPARAM、AMQP_MANDATORY、AMQP_IMMEDIATE，可以多项并用
     * @return bool 投递是否成功
     */
    public function putMsg(
        string $strMsg,
        string $strQueueName,
        string $strRouteKey,
        string $strExchangeName,
        array $arrAttr = [],
        int $intFlag = AMQP_NOPARAM
    ) {
        $blnRe = false;
        if (strlen($strMsg)) {
            if ($this->setExchange($strExchangeName)) {
                $arrAttr || $arrAttr = ['delivery_mode' => 2, 'content_type' => 'application/json'];
                $blnRe = $this->arrExchange[$strExchangeName]->publish($strMsg, $strRouteKey, $intFlag, $arrAttr);
            }
        }
        return $blnRe;
    }

    /**
     * 生产者发布一条消息
     * @param string $strMsg 消息体
     * @param string $strRouteKey 交换器投递的路由
     * @param string $strExchangeName 交换器，默认是xq
     * @param array $arrAttr 'delivery_mode' => 2 表示消息持久化
     * @param int $intFlag AMQP_NOPARAM、AMQP_MANDATORY、AMQP_IMMEDIATE，可以多项并用
     * @return bool 投递是否成功
     */
    public function putDirectMsg(
        string $strMsg,
        string $strRouteKey,
        string $strExchangeName,
        array $arrAttr = [],
        int $intFlag = AMQP_NOPARAM
    ) {
        $blnRe = false;
        if (strlen($strMsg)) {
            if ($this->setExchange($strExchangeName)) {
                $arrAttr || $arrAttr = ['delivery_mode' => 2, 'content_type' => 'application/json'];
                $blnRe = $this->arrExchange[$strExchangeName]->publish($strMsg, $strRouteKey, $intFlag, $arrAttr);
            }
        }
        return $blnRe;
    }

    /**
     * 功    能：使用topic模式发送MQ消息, 交换机不具备存储消息的能力，请确认队列先绑定后再开启调用生成数据
     * 修改日期：2020/1/10
     * @param string $strMsg 消息内容
     * @param string $strRouteKey 绑定的key名称
     * @param string $strExchangeName 交换机名称
     * @param array $arrAttr 'delivery_mode' => 2 表示消息持久化
     * @param int $intFlag AMQP_NOPARAM、AMQP_MANDATORY、AMQP_IMMEDIATE，可以多项并用
     * @return bool 投递是否成功
     * @throws AMQPConnectionException
     * @throws AMQPExchangeException
     */
    public function putTopicMsg(
        string $strMsg,
        string $strRouteKey,
        string $strExchangeName,
        array $arrAttr = [],
        int $intFlag = AMQP_NOPARAM
    ) {
        $blnRe = false;
        if (strlen($strMsg)) {
            if ($this->setExchange($strExchangeName, AMQP_EX_TYPE_TOPIC)) {
                $arrAttr || $arrAttr = ['delivery_mode' => 2, 'content_type' => 'application/json'];
                $blnRe = $this->arrExchange[$strExchangeName]->publish($strMsg, $strRouteKey, $intFlag, $arrAttr);
            }
        }
        return $blnRe;
    }

    /**
     * 从队列中获取一条消息
     * @param string $strQueueName $strQueueName
     * @param string $strRouteKey $strRouteKey
     * @param string $strExchangeName $strExchangeName
     * @return array $arrRe
     * @see AMQPQueue::get()
     */
    public function getMsg(
        string $strQueueName,
        string $strRouteKey = '',
        string $strExchangeName = 'xq'
    ) {
        $arrRe = [];
        if ($this->setQueue($strQueueName, $strRouteKey, $strExchangeName)) {
            $objAMQPEnvelope = $this->arrQueue[$strQueueName]->get(); // 不要传参AMQP_AUTOACK
            if ($objAMQPEnvelope) {
                $arrRe = [
                    'id' => $objAMQPEnvelope->getDeliveryTag(),
                    'msg' => $objAMQPEnvelope->getBody(),
                ];
            }
        }
        return $arrRe;
    }

    /**
     * 确认消息已处理
     * @param string $strQueueName $strQueueName
     * @param int $intMsgId $intMsgId
     * @param string $strRouteKey $strRouteKey
     * @param string $strExchangeName $strExchangeName
     * @param type $intFlag AMQP_NOPARAM，AMQP_MULTIPLE
     * @see AMQPQueue::ack()
     * @return bool $blnRe
     */
    public function ackMsg(
        string $strQueueName,
        int $intMsgId,
        string $strRouteKey = '',
        string $strExchangeName = 'xq',
        $intFlag = AMQP_NOPARAM
    ) {
        $blnRe = false;
        if ($this->setQueue($strQueueName, $strRouteKey, $strExchangeName)) {
            $blnRe = $this->arrQueue[$strQueueName]->ack($intMsgId, $intFlag);
        }
        return $blnRe;
    }

    /**
     * 回调方式消费队列
     * @param string $strQueueName $strQueueName
     * @param callable $callable 回调
     * @param string $strRouteKey $strRouteKey
     * @param string $strExchangeName  $strExchangeName
     * @param type $intFlag $intFlag
     * @see AMQPQueue::consume()
     * @return void
     */
    public function consumeMsg(
        string $strQueueName,
        callable $callable,
        string $strRouteKey = '',
        string $strExchangeName = 'xq',
        $intFlag = AMQP_NOPARAM
    ) {
        if ($this->setQueue($strQueueName, $strRouteKey, $strExchangeName)) {
            $this->arrQueue[$strQueueName]->consume($callable, $intFlag);
        }
    }

    /**
     * 作    者: zhub
     * 功    能: 保活连接
     * 修改日期: 2020-04-22
     *
     * @param int $startTime 开始时间
     * @param int $timeInterval 时间间隔
     *
     * @return bool
     */
    public function heartBeat(int &$startTime, int $timeInterval = 10)
    {
        $now = time();
        try {
            if ($now - $startTime > $timeInterval) {
                // 发布消息用
                if (!empty($this->arrExchange)) {
                    end($this->arrExchange)->declareExchange();
                } else {
                    $this->setExchange('xq');
                }
                // 消费消息用
                if (!empty($this->arrQueue)) {
                    end($this->arrQueue)->declareQueue();
                }
                $startTime = $now;
            }
            return true;
        } catch (Throwable $e) {
            $startTime = $now;
            return false;
        }
    }

    /**
     * 功    能: 绑定交换机
     * @param string $strQueueName 队列名
     * @param string $strBindRouteKey 为空时，与队列名一致
     * @param string $strExchangeName 交换器名称
     * @return boolean
     */
    public function bindQueue(string $strQueueName, string $strBindRouteKey, string $strExchangeName)
    {
        if ($this->setQueue($strQueueName, $strBindRouteKey, $strExchangeName)) {
            return true;
        }
        return false;
    }
}

