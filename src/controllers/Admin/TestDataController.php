<?php

namespace Admin;

use Controller;

class TestDataController extends Controller
{
    protected $pdo;

    /**
     * @return int
     */
    public function actionIndex()
    {
        $redisAction = \RedisAction::connect();

        echo "\n";

        $res = $redisAction->lLen(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW);
        echo "channelCallbackStatusNew:\n";
        var_export($res);
        echo "\n";

        $this->varEcho($redisAction, "SmsResponseTimeStart");
        $this->varEcho($redisAction, "SmsResponseTimeEnd");
        $this->varEcho($redisAction, "SmsResponseTimeEnd2");
        $this->varEcho($redisAction, "SmsResponseTimeEnd3");
        $this->varEcho($redisAction, "SmsResponseTimeEnd4");
        $this->varEcho($redisAction, "ProcessorResponseStart");
        $this->varEcho($redisAction, "ProcessorResponseEnd");
        $this->varEcho($redisAction, "mengwangResponseInfo");
        $this->varEcho($redisAction, "mengwangResponseInfoLength1");
        $this->varEcho($redisAction, "mengwangResponseInfoLength2");
        $this->varEcho($redisAction, "DataArchiveStatus");

        $redisAction = \RedisAction::connect();
        $res = $redisAction->get("test_callback");
        var_export($res);

        $res = $redisAction->get("test_callback_ok");
        var_export($res);

        echo "\ndone.\n";
    }

    /**
     * @return bool
     */
    public function actionSetRedis()
    {
        //Example: http://smsp.2345.net/Admin/TestData/SetRedis?cmd=get&val[0]=test
        $redisAction = \RedisAction::connect();
        $GetRedisAction = \GetRedisAction::getInstance();
        $ips = $GetRedisAction->isIpWhiteList(get_client_ip());
        if (!$ips)
        {
            echo 'IP verify Failed: ' . get_client_ip() . "\n";
            return false;
        }
        else
        {
            echo 'IP verify Pass: ' . get_client_ip() . "\n";
        }
        echo "==========";
        if (isset($_GET['cmd']))
        {
            $redisCmd = $_GET['cmd'];
            $redisVal = $_GET['val'];
            echo "\ncmd:$redisCmd";
            echo "\nval:";
            var_export($redisVal);
            echo "\n==========\n";
            var_export(call_user_func_array(array($redisAction, $redisCmd), $redisVal));
            echo "\n==========";
        }
        echo "\ndone.\n";
    }

    /**
     * @return bool
     */
    public function actionErrorLog()
    {
        $GetRedisAction = \GetRedisAction::getInstance();
        $ips = $GetRedisAction->isIpWhiteList(get_client_ip());
        if (!$ips)
        {
            echo 'IP verify Failed: ' . get_client_ip() . "\n";
            return false;
        }
        else
        {
            echo 'IP verify Pass: ' . get_client_ip() . "\n";
        }
        $filePath = ini_get('error_log');
        if (!$fp = fopen($filePath, 'rb'))
        {
            echo "Cannot open PHP Error Log, file path: {$filePath}\n";
            return false;
        }
        $fileSize = filesize($filePath);
        echo "==========\n";
        echo "File size: {$fileSize}\n";
        echo "==========\n";
        if (isset($_GET['tail']) && is_numeric($_GET['tail']))
        {
            $seekLength = -1 * $_GET['tail'];
            fseek($fp, $seekLength, SEEK_END);
            echo fread($fp, $_GET['tail']);
        }
        elseif (isset($_GET['from'], $_GET['to']) && is_numeric($_GET['from']) && is_numeric($_GET['to']) && $_GET['to'] - $_GET['from'] > 0)
        {
            fseek($fp, $_GET['from'], SEEK_SET);
            echo fread($fp, $_GET['to'] - $_GET['from']);
        }
        echo "\n==========\n";
        echo "done.\n";
    }

    /**
     * @return bool
     */
    public function actionRepairLogIdSync()
    {
        $GetRedisAction = \GetRedisAction::getInstance();
        $ips = $GetRedisAction->isIpWhiteList(get_client_ip());
        if (!$ips)
        {
            echo 'IP verify Failed: ' . get_client_ip() . "\n";
            return false;
        }
        else
        {
            echo 'IP verify Pass: ' . get_client_ip() . "\n";
        }
        echo "==========\n";
        $smsModel = \SmsModel::getInstance();
        $smsModel->repairLogId();
        echo "\n==========\n";
        echo "done.\n";
    }

    /**
     * @param mixed $redisAction redisAction
     * @param string $type type
     */
    private function varEcho(&$redisAction, $type)
    {
        $res = $redisAction->get($type);
        $ttl = $redisAction->ttl($type);
        var_export(array($type, $res, $ttl));
        echo "\n";
    }


}
