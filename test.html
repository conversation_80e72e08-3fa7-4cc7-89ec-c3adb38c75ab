<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
		<meta name=renderer  content=webkit>
		<meta name = "viewport" content="width=device-width,initial-scale=1.0 user-scalable = no , maximum-scale=1.0">
		<title>登录demo</title>
        <script src="http://passport-plugin.st1.2345.cn/loginPlugin/login.umd.js"></script>
	</head>
	<script src="http://libs.baidu.com/jquery/1.8.1/jquery.min.js"></script>
	<body>
		<div id="priview"></div>
	</body>
	<script>
		var host = '//' + window.location.host
		function clearCache(){
			demo1.dropOut();
		};
		function logout(){
			window.location.href= host +'/loginPlugin/logout.html?mid='+'login&logoffType=single'
		}
		var config = {
			mid: 'YY',	// mid
			el: document.getElementById('priview'),	//容器
			size: 'normal',	// 尺寸
			dialog:true,	// 是否弹窗
			isReg:false,	// 是否只是注册
			triangleCode:true, //是否开启角标微信扫码
			autoSort:true,	//是否开启登录方式自动排序，开启后会记录用户常用登录方式并优先展示
			wechartStyleHref:null, // 微信二维码内嵌css
			extra:'test=1&to=2', //用于业务埋点数据，字符串
			registeredButtonCallback: function (instance) {	// 注册回调
				render(true)
			},
			loginButtonCallback: function (instance) {	// 登录回调
				render(false)
			},
			getUserInfo:function(res){	//获取用户信息回调
				var el = $('<div>'+res.userInfo.username+'</div>');
				var button = $('<button onclick="clearCache()">点击清除token</button>');
				var button2 = $('<button onclick="logout()">注销操作</button>');
				$("body").append(el);
				$("body").append(button);
				$("body").append(button2);
			}
		}
		var demo;
		function render(isReg){
			config.isReg = isReg
			config.el.remove()
			// sdk 重复渲染需要先移除容器dom（后续版本修复）
			var el = $('<div id="priview"></div>');
			$("body").append(el);
			config.el = document.getElementById('priview')
			// end
			demo1 = new Login2345(config)
		}
		demo1 = new Login2345(config);
	</script>
</html>
