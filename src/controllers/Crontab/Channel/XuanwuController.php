<?php

namespace Crontab\Channel;
use Controller;

class XuanwuController extends Controller
{

    /**
     * 发短信回调接口状态同步
     * */
    public function actionCallbackRynsStatus()
    {

        if (class_exists('\SoapClient'))
        {
            $GetMoneyInfoAction = \Admin\GetMoneyInfoAction::getInstance();
            $OperatorInfo = $GetMoneyInfoAction->getOperatorInfo(8);
            $client = new \SoapClient($OperatorInfo['api_url'], array('encoding' => 'utf-8'));
            $params = array(
                'account' => $OperatorInfo['username'],
                'password' => $OperatorInfo['password'],
                'PageSize' => 1);
            $result = $client->GetReport($params);
            
            $reportList = $result->GetReportResult->MTReport;
            foreach ($reportList as $reportInfo)
            {
                $reportInfo->msgID;  //消息ID
                $reportInfo->originResult;
                
                $status = ($data->Status == 'DELIVRD') ? true : false;
                $channelData = array(
                    'log_id' => $getLog['log_id'],
                    'phone' => $data->Mobile,
                    'status' => $status,
                    'message' => var_export((array )$data, true));

                $isTrue = $SmsLogModel->addChannelCallbackStatus($channelData);
                
                
                
            }
            
        }
        else
        {
            return - 1;
        }

    }

    /**
     * 用户回复内容回调
     * */
    public function actionCallbackSmsUplinkData()
    {
    }
}

?>