<?php

/**
 * 邮箱控制器
 */
class EmailController extends Controller
{

    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct();
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }
    }

    /**
     * 修改绑定邮箱
     *
     * @return void
     */
    public function actionEdit()
    {
        xLog("checkapilog", "checkapilog", "Email.actionEdit");
        $passid = intval($_POST['passid']);
        $email = $_POST['email'];
        $email = strtolower($email);
        if (isset($_POST['ip']) && filter_var($_POST['ip'], FILTER_VALIDATE_IP))
        {
            $clientIp = $_POST['ip'];
        }
        else
        {
            $clientIp = '0.0.0.0';
        }
        $from = isset($_POST['from']) ? $_POST['from'] : '';
        $domain = '';
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis)
        {
            $serverIp = get_client_ip();
            $redis = RedisEx::getInstance();
            $domainServerIPs = Config::get("domainServerIPs");
            foreach ($domainServerIPs as $domainName => $serverIpKey)
            {
                if ($redis->sIsMember($serverIpKey, $serverIp))
                {
                    $domain = $domainName;
                    break;
                }
            }
        }
        if ($passid > 0 && $email != '' && filter_var($email, FILTER_VALIDATE_EMAIL))
        {
            $memberModel = loadModel('member');
            $row = $memberModel->getEmailByPassid($passid);
            $emailOld = "{$row['email']}:{$row['email_status']}";
            $emailAction = loadAction('email');
            $ret = $emailAction->edit($passid, $email);
            if ($ret != '200.0')
            {
                echo $ret;
            }
            else
            {
                $memberModel->memberInfoLog($passid, array('email_old' => $emailOld, 'email' => $email, 'op_time' => time(), 'op_ip' => $clientIp, 'op_domain' => $domain, 'op_from' => $from));
                echo '200.0';
            }
        }
        else
        {
            echo '300.0';
        }
    }

}
