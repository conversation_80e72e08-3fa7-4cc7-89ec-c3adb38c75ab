<?php

include APPPATH.'/../metrics/vendor/autoload.php';
class MetricsController extends Controller
{
    public function actionIndex()
    {
        header("HTTP/1.1 404");
        exit;
        $registry = new \Prometheus\CollectorRegistry(new \Prometheus\Storage\APC(), true);
        $renderer = new \Prometheus\RenderTextFormat();
        $result = $renderer->render($registry->getMetricFamilySamples());

        header('Content-type: ' . \Prometheus\RenderTextFormat::MIME_TYPE);
        echo $result;
    }
}