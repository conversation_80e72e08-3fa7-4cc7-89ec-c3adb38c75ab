<?php

/**
 * Copyright (c) 2013,上海二三四五网络科技股份有限公司
 * 文件名称：DataStatisticController.php
 * 摘   要：
 * 作   者：panj
 * 修改日期：23/08/2018 10:05
 */

namespace Crontab;

use Controller;
use WebLogger\Facade\LoggerFacade;

class DataStatisticController extends Controller
{
    //业务方接口定义
    const BUSINESS_TYPE_SMS = 1;
    const BUSINESS_TYPE_SMS_NAME = '短信服务';

    const PRODUCT_ID_LJD = 10002;
    const PRODUCT_ID_KDW = 101;
    const PRODUCT_ID_SXJ = 102;

    const SMS_FEE_COUNT = 2;
    const SMS_RETURN_SUCC = 1;

    const SMS_PROVIDER_GUO_DU = 'GuoDu';
    const SMS_PROVIDER_ZHEN_GAO = 'Zhengao';

    const DATE_TYPE_DAY = 1;
    const DATE_TYPE_WEEK = 2;
    const DATE_TYPE_MONTH = 3;

    const CHARGE_UNIT = '成功条数';

    public static $productText = [
        self::PRODUCT_ID_LJD => '立即贷',
        self::PRODUCT_ID_KDW => '卡贷王',
        self::PRODUCT_ID_SXJ => '随心借',
    ];

    public static $smsProviderName = [
        self::SMS_PROVIDER_GUO_DU   => '国都',
        self::SMS_PROVIDER_ZHEN_GAO => '点集',
    ];

    protected $redis;
    protected $projectSmsProvider = [];
    protected $dailyData = [];
    protected $log;
    protected $startTime;
    protected $endTime;

    /**
     * DataStatisticController constructor.
     */
    public function __construct()
    {
        $this->startTime = microtime(true);
        // $this->log = new \ComLog();
        // $this->log->setPath('crontab/dataStatistic')->setName('day');
        $this->redis = \RedisAction::connect();
        $this->projectSmsProvider = $this->getSmsProvider();
        $time = microtime(true);
        LoggerFacade::info('construct time:' . ($time - $this->startTime));
    }

    /**
     * 获取api url
     * User: panj
     * @return string
     */
    protected static function getApiUrl()
    {
        if ('development' == RUNMODE)
        {
            return 'http://t1-managerdaikuan.2345.com/bi-server/api/expend/saveExpend';
        }
        elseif ('test' == RUNMODE)
        {
            return 'http://t1-managerdaikuan.2345.com/bi-server/api/expend/saveExpend';
        }
        else
        {
            return 'https://managerdaikuan.2345.com/bi-server/api/expend/saveExpend';
        }
    }

    /**
     * User: panj
     *
     * @param int $productCode 项目方产品id
     *
     * @return mixed
     */
    public static function getProjectId($productCode)
    {
        if ('production' == RUNMODE)
        {
            $project = [
                self::PRODUCT_ID_LJD => 113,
                self::PRODUCT_ID_KDW => 110,
                self::PRODUCT_ID_SXJ => 116,
            ];
        }
        else
        {
            //便于验证，将开发和测试环境的itemCode改为跟线上一致
            $project = [
                self::PRODUCT_ID_LJD => 113,
                self::PRODUCT_ID_KDW => 110,
                self::PRODUCT_ID_SXJ => 116,
            ];
        }

        return $project[$productCode];
    }

    /**
     * User: panj
     *
     * @param string $date date
     *
     * @return false|string
     */
    protected function getCalDate($date)
    {
        $lastDay = date('Y-m-d', strtotime(' -2 days'));
        if (!is_null($date))
        {
            $date = date('Y-m-d', strtotime($date));
            if ($date <= $lastDay)
            {
                return $date;
            }
        }

        return $lastDay;
    }

    /**
     * User: panj
     * @return mixed
     */
    protected function getSmsProvider()
    {
        $projectSign = array_map(
            function ($row) {
                return '【' . trim($row) . '】';
            },
            self::$productText
        );
        //改服务商配置的时候需要更新这个缓存
        $redisKey = \Config_CacheKey::BI_DATA_SMS_PROVIDER_ACCOUNT;
        $cacheData = $this->redis->get($redisKey);
        if (!$cacheData)
        {
            $provider = \Crontab\DataStatisticModel::getInstance()->getSmsProvider($projectSign);
            $projectSms = [];
            //一个项目下的所有账号;
            foreach ($provider as $item)
            {
                if (false !== strpos($item['action_name'], self::SMS_PROVIDER_GUO_DU))
                {
                    $projectSms[array_search($item['sign'], $projectSign)][self::SMS_PROVIDER_GUO_DU][] = $item;
                }
                elseif (false !== strpos($item['action_name'], self::SMS_PROVIDER_ZHEN_GAO))
                {
                    $projectSms[array_search($item['sign'], $projectSign)][self::SMS_PROVIDER_ZHEN_GAO][] = $item;
                }
            }
            $cacheData = serialize($projectSms);
            $this->redis->set($redisKey, $cacheData, 7 * 86400);
        }

        return unserialize($cacheData);
    }

    /**
     * User: panj
     * 生成 recordId
     * @return string
     */
    protected function getRecordId()
    {
        $redisKey = \Config_CacheKey::BI_DATA_RECODE_ID_LAST . date('Y-m-d');
        $recordId = $this->redis->get($redisKey);
        return $recordId ? $recordId : date('Ymd') . '00000';
    }

    /**
     * User: panj
     * 记录上次的recordId
     * @param string $recordId record Id
     *
     * @return mixed
     */
    protected function setRecordId($recordId)
    {
        $redisKey = \Config_CacheKey::BI_DATA_RECODE_ID_LAST . date('Y-m-d');

        return $this->redis->set($redisKey, $recordId, 7 * 86400);
    }

    /**
     * User: panj
     *
     * @param null $date date
     * @return null
     */
    public function actionRun($date = null)
    {
        $date = $this->getCalDate($date);
        $this->calculate($date);
        $recordId = $this->getRecordId();
        $biData = [];
        foreach ($this->projectSmsProvider as $productId => $productArray)
        {
            foreach ($productArray as $smsProvider => $accounts)
            {
                //服务商发送总数量
                $quantity = 0;
                //一个服务商的短信平均单价=(服务商下的每个账号的数量*该账号的单价)/这个服务商发送的所有短信数量,
                $charge = 0;
                foreach ($accounts as $account)
                {
                    if (isset($account['dataStatistic']) && !empty($account['dataStatistic']))
                    {
                        $statistic = $account['dataStatistic'];
                        $quantity += $statistic['itemCount'];
                        $charge += $statistic['itemCount'] * $account['unit_price'];
                    }
                    else
                    {
                        LoggerFacade::info('no dataStatistic!!!!', (array)$account);
                    }
                }
                $unitPrice = 0;
                if ($quantity > 0)
                {
                    $unitPrice = number_format($charge / $quantity, 4, '.', '');
                }

                $charge = number_format($charge, 4, '.', '');

                $projectSmsProviderData = [
                    'recordId'      => ++$recordId,
                    'productId'     => $productId,
                    'statisticDate' => $date,
                    'dateType'      => self::DATE_TYPE_DAY,
                    'itemCode'      => self::getProjectId($productId),
                    'itemName'      => self::$productText[$productId],
                    'supplierCode'  => $smsProvider,
                    'supplierName'  => self::$smsProviderName[$smsProvider],
                    'chargeUnit'    => self::CHARGE_UNIT,
                    'unitPrice'     => $unitPrice,
                    'quantity'      => $quantity,
                    'charge'        => $charge,
                ];

                $biData[] = $projectSmsProviderData;
            }
        }

        $statisticData = [
            'businessType' => self::BUSINESS_TYPE_SMS,
            'businessName' => self::BUSINESS_TYPE_SMS_NAME,
            'data'         => $biData,
        ];
        $this->setRecordId($recordId);
        $this->saveDailyData($statisticData, $date);
        $this->postData($statisticData);
        LoggerFacade::info('statisticData: ', (array)$statisticData);
        $time = microtime(true);
        LoggerFacade::info('actionRun time:' . ($time - $this->startTime));
    }

    /**
     * User: panj
     *
     * @param array $data data
     * @return null
     */
    protected function postData($data)
    {
        $data = \EncodingAction::iteratorArray($data, 'utf-8', 'gbk');
        $apiUrl = self::getApiUrl();
        $res = \FuncAction::postTimeout($apiUrl, json_encode($data), 30, false, true);
        LoggerFacade::info("apiUrl:{$apiUrl} postData return", (array)$res);
        $res = json_decode($res, true);
        if (isset($res['code']) && $res['code'] === '000')
        {
            echo 'post successfully! ';
        }
        else
        {
            echo 'post data failed, return :';
        }
    }

    /**
     * User: panj
     *
     * @param array $data data
     * @param string $date date
     *
     * @return mixed
     */
    protected function saveDailyData($data, $date)
    {
        $calDate = $this->getCalDate($date);
        $redisKey = \KeyAction::getBIDataStatisticKey($calDate);

        return $this->redis->set($redisKey, serialize($data), 7 * 86400);
    }

    /**
     * User: panj
     * @param string $date date
     * @return null
     */
    protected function calculate($date)
    {
        $dataTotal = $this->getSmsCountData($date);
        $data = [];
        foreach ($dataTotal as $key => $row)
        {
            $data[$row['titleId']] = $row;
        }
        //一个sign（项目） 从dataTotal里拿多个账号（账号里的同一个action）是同一个服务商
        foreach ($this->projectSmsProvider as $productId => $providerClass)
        {
            foreach ($providerClass as $k => $accounts)
            {
                foreach ($accounts as $kk => $account)
                {
                    if (array_key_exists($account['id'], $data))
                    {
                        $this->projectSmsProvider[$productId][$k][$kk]['dataStatistic'] = $data[$account['id']];
                    }
                }
            }
        }
    }

    /**
     * User: panj
     * @param string $date date
     * @return array|bool|mixed
     */
    public function getSmsCountData($date)
    {
        $startDate = $this->getCalDate($date);
        $redisKey = \Config_CacheKey::SMS_ADDUP_CACHE . 'operator:' . self::SMS_RETURN_SUCC . ':' . $startDate;
        if ($this->redis->exists($redisKey))
        {
            return unserialize($this->redis->get($redisKey));
        }
        else
        {
            $info = 'redis 无缓存数据';
            LoggerFacade::info($info);
            if ('production' !== RUNMODE)
            {
                return $this->mockData($date);
            }
        }
    }

    /**
     * User: panj
     * @param string $date date
     * @return array|bool
     */
    public function mockData($date)
    {
        if ('production' == RUNMODE)
        {
            return false;
        }
        $mockData = [];
        $startDate = $this->getCalDate($date);
        foreach ($this->projectSmsProvider as $productId => $providerClass)
        {
            foreach ($providerClass as $k => $accounts)
            {
                foreach ($accounts as $kk => $account)
                {
                    $mockData[] = array(
                        'itemCount' => mt_rand(10000, 90000),
                        'smsCount'  => mt_rand(10000, 90000),
                        'titleId'   => $account['id'],
                        'ctime'     => $startDate,
                    );
                }
            }
        }

        return $mockData;
    }

    /**
     * User: panj
     *
     * @param string $start start
     * @param string $end end
     * @return null
     */
    public function actionTmpDatePush($start, $end)
    {
        $begin = new \DateTime(date('Y-m-d', strtotime($start)));
        $end = new \DateTime(date('Y-m-d', strtotime($end)));
        $interval = \DateInterval::createFromDateString('1 day');
        $period = new \DatePeriod($begin, $interval, $end);

        foreach ($period as $dt)
        {
            $date = $dt->format("Y-m-d");
            $this->actionRun($date);
            sleep(1);
        }
    }
}
