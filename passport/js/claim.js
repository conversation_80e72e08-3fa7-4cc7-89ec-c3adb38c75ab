function claim(p)
{
	$('pass'+p).value = '';
	$('err'+p).innerHTML = '';
	$('pass'+p).className = 'ipt_03';
	$('p'+p).style.display = '';
	$('is'+p).className = 'btn_05 unable05';
	$('is'+p).disabled = 'disabled';
	$('no'+p).className = 'btn_06';
	$('no'+p).disabled = '';
}

function not(p)
{
	$('p'+p).style.display = 'none';
	$('is'+p).className = 'btn_05 ';
	$('is'+p).disabled = '';
	$('no'+p).className = 'btn_06 unable06';
	$('no'+p).disabled = 'disabled';
	$('is'+p).value = '此帐号是我的，我要认领';
	$('pass'+p).value = '';
}

function validate(username, p)
{
	var pass = $('pass'+p).value;
	startrequest('api/check.php','type=claim&username='+username+'&password='+pass+'&domain='+p,0,function(response){
		if (response > 0)
		{
			$('err'+p).innerHTML = '验证成功';
			$('pass'+p).className = 'ipt_03';
			$('p'+p).style.display = 'none';
			$('is'+p).value = '已认领';
		}
		else
		{
			$('err'+p).innerHTML = '密码错误';
			$('pass'+p).className = 'ipt_03 ipt_02';
		}			
	});
}