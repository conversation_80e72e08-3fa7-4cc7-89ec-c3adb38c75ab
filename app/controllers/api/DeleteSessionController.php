<?php

use Service\Report\LSessionReport;

class DeleteSessionController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        $allowSeverDomains = Config::get("domainServerIPs");
        $serverDomain = $_POST['domain'];
        if (empty($serverDomain) || !isset($allowSeverDomains[$serverDomain]))
        {
            die('0');
        }
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('0');
        }

        $sessionId = $_POST['sid'];
        $passid = $_POST['passid'];
        $version = $_POST['ver'];
        $redis = RedisEx::getInstance();
        if ($passid)
        {
            $key = "DELETE_SESSION" . date("Ymd");
            $redis->hIncrBy($key, $serverDomain . "_passid", 1);
            $redis->expire($key, 1728000);//  过期20天

            $redis->zRem('LoginSessions', $sessionId);
            echo $redis->zRem('LoginSession:' . $passid, $sessionId);
//            LSessionReport::Log('zRem', "LoginSession:$passid", $sessionId, time());
        }
        else
        {
            $key = "DELETE_SESSION" . date("Ymd");
            $redis->hIncrBy($key, $serverDomain, 1);
            $redis->expire($key, 1728000);//  过期20天

            echo $redis->zRem('LoginSessions', $sessionId);
        }
    }

}
