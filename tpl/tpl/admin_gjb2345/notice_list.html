{{include file="admin_gjb2345/header.html"}}
<style>
*,body{ font-size:12px; }
.mytable{ background-color:#2BA4E5; padding:0 0 0 0; text-align:left;}
.mytable td {background-color:#FFF; padding:1px 1px 1px 1px;}
</style>
    <h4>提醒列表</h4>
    <br />

	<div style="width:95%; margin-left:30px; border:1px solid #CCC;">
		<p style="color:red">注意: 时间的区间 为 00:00:00 - 23:59:59<br />
		开始时间 为 >=; 结束时间为 <;<br />
		条目被添加或修改后状态为禁用,确认无误后请手动调回正常.
		</p>
		<table class="mytable" border="0" cellpadding="0" cellspacing="1">
          <tr>
            <th>开始(>=)</th>
            <th>结束(<)</th>
            <th>提示</th>
            <th>URL</th>
            <th>操作</th>
          </tr>
{{foreach key=key item=item from=$pageArray.list}}
		  {{if $item.a_type eq 1}}
          <tr style="color:green">
          {{else}}
          <tr>
          {{/if}}
            <td>{{$item.start_time}}</td>
            <td>{{$item.end_time}}</td>
            <td>{{$item.content}}</td>
            <td><a href="{{$item.url}}" target="_blank">{{$item.url}}</a></td>
            <td><a href="javascript:void(0);" onclick="show_edit({{$item.id}});">编辑</a> | <a href="javascript:void(0);" onclick="if(confirm('确定要修改状态吗?')){location.href='notice_list.php?act=status&id={{$item.id}}&status={{if $item.enable eq 1}}0{{else}}1{{/if}}'}else{return false;}">{{if $item.enable eq 1}}正常{{else}}已禁用{{/if}}</a> | <a href="javascript:void(0);" onclick="if(confirm('确定要删除吗?操作不可恢复.')){location.href='notice_list.php?act=del&id={{$item.id}}'}else{return false;}">删除</a></td>
          </tr>
{{/foreach}}
		  <tr>
            <td colspan="5"><input type="button" onclick="show_add()" value="添加一行" /></td>
          </tr>
		  <form method="post" id="notice_add">
		  <input type="hidden" name="act" value="add" />
		  <tr id="notice_add_area" style="display:none;" >
            <td><input type="text" name="start_time" id="add_start_time" required="required" /></td>
            <td><input type="text" name="end_time" id="add_end_time" required="required" /></td>
            <td><input type="text" name="content" id="add_content" required="required" /></td>
            <td><input type="url" name="url" id="add_url" /></td>
            <td><label><input type="checkbox" name="a_type" value="1">每天一乐</label> | <input type="submit" value="确认添加" /> | <input type="button" onclick="hide_add()" value="取消" /></td>
          </tr>
          </form>
{{foreach key=key item=item from=$pageArray.list}}		  
		  <form method="post" id="notice_edit_{{$item.id}}">
		  <input type="hidden" name="act" value="edit" />
		  <input type="hidden" name="id" value="{{$item.id}}" />
		  <tr id="notice_edit_area_{{$item.id}}" style="display:none;" >
            <td><input type="text" name="start_time" required="required" value="{{$item.start_time}}" /></td>
            <td><input type="text" name="end_time" required="required" value="{{$item.end_time}}" /></td>
            <td><input type="text" name="content" required="required" value="{{$item.content}}" /></td>
            <td><input type="url" name="url" value="{{$item.url}}" /></td>
            <td><label><input type="checkbox" name="a_type" value="1" {{if $item.a_type eq 1}}checked{{/if}}>每天一乐</label> | <input type="submit" value="确认修改" /> | <input type="button" onclick="hide_edit({{$item.id}})" value="取消" /></td>
          </tr>
          </form>
{{/foreach}}		  
		  <!--input type="text" name="start_time{{$item.id}}" value="{{$item.start_time}}" required="required" /-->
        </table>
        <form method="post">
		前台轮播条数：
		<input type="text" name="max" size="5" value="{{$pageArray.max}}">
		<input type="hidden" name="act" value="max">
		<input type="submit" value="保存">
		</form>
	</div>
	<script>
		function show_add(){
			_g('notice_add_area').style.display="";
		}
		function hide_add(){
			_g('notice_add_area').style.display="none";
		}
		function show_edit(id){
			_g('notice_edit_area_' + id).style.display="";
		}
		function hide_edit(id){
			_g('notice_edit_area_' + id).style.display="none";
		}
		function _g(id){
			return document.getElementById(id);
		}
	</script>
{{include file="admin_gjb2345/footer.html"}}