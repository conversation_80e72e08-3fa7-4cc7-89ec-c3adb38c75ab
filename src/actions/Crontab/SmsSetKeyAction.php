<?php

namespace Crontab;

class SmsSetKeyAction extends \Action
{
    private static $blackError = array();
    private static $ipWriteError = array();

    /**
     * 同步手机黑白名单
     * */
    public function SmsSyncBlackPhoneKey()
    {
        $BlackListModel = \Admin\BlackListModel::getInstance();
        $GetRedisAction = \GetRedisAction::getInstance();
        $BlackList = $BlackListModel->getUserBlackList();
        $upErrorArr = array();
        foreach ($BlackList as $BlackInfo)
        {
            //1黑名单
            if ($BlackInfo['status'] == 1)
            {
                $getPhoneBlack = $GetRedisAction->isInBlackList($BlackInfo['phone']);
                if ($getPhoneBlack == false)
                {
                    $isTrue = $GetRedisAction->setBlackPhoneList($BlackInfo['phone']);
                }
            }
            //2白名单
            if ($BlackInfo['status'] == 2)
            {
                $getPhoneWrite = $GetRedisAction->isIpWhiteList($BlackInfo['phone']);
                if ($getPhoneWrite == false)
                {
                    $isTrue = $GetRedisAction->setWritePhoneList($BlackInfo['phone']);
                }
            }
            if ($isTrue === false)
            {
                self::$blackError[$BlackInfo['status']][] = $BlackInfo['phone'];
            }

        }
        return empty(self::$blackError) ? true : false;
    }

    /**
     * 同步IP白名单
     * */
    public function SmsSyncIpWriteList()
    {
        $BlackListModel = \Admin\BlackListModel::getInstance();
        $GetRedisAction = \GetRedisAction::getInstance();
        $IpList = $BlackListModel->getIpWriteList();
        foreach ($IpList as $IpInfo)
        {
            $isTrue = null;
            $getIp = $GetRedisAction->isIpWhiteList($IpInfo['ip']);
            //白名单里面没找到则写入
            if ($getIp === false)
            {
                $isTrue = $GetRedisAction->setIpWriteList($IpInfo['ip']);
            }
            if ($isTrue === false)
            {
                self::$ipWriteError[] = $IpInfo['ip'];
            }
        }
        return empty(self::$ipWriteError) ? true : false;
    }

    /**
     * 获取黑白名单设置错误信息
     * */
    public function getBlackError()
    {
        return self::$blackError;
    }

    /**
     * 获取IP白名单设置错误信息
     * */
    public function getIpWriteError()
    {
        return self::$ipWriteError;
    }
}