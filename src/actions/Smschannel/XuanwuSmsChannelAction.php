<?php

/**
 * Copyright (c) 2018,上海二三四五网络科技股份有限公司
 * 摘    要：玄武科技通道发送接口
 * MOS wsdl地址：http://211.147.239.62/Service/WebService.asmx?wsdl
 * 400 wsdl地址：http://211.147.239.62:8500/Service/WebService.asmx?wsdl
 * 作    者：zhangchao
 * 修改日期：2016.01.25
 */

namespace Smschannel;

use FuncAction;

class XuanwuSmsChannelAction extends SmsChannelAction
{
    /**
     * XuanwuSmsChannelAction constructor.
     * @param string $user   user
     * @param string $pwd    password
     * @param string $apiUrl url
     */
    public function __construct($user, $pwd, $apiUrl)
    {
        parent::__construct($user, $pwd, $apiUrl);
    }

    /**
     * 发送短信，适用
     * @param string|array $phone 手机号
     * @param string       $msg   短信内容
     * @param string       $sign  短信签名，可能不用
     * @return int
     */
    public function send($phone, string $msg, string $sign)
    {
        $multi = 0;
        if (is_array($phone))
        {
            if (count($phone) > 100)
            {
                //超过最多号码限制
                foreach ($phone as $value)
                {
                    $this->msgIdList[$value] = 0;
                    $this->operatorMsgList[$value] = array(
                        'codeStatus' => 0,
                        'codeDesc'   => '批次内数量超限',
                        'result'     => self::RET_ERR,
                    );
                }
                return self::RET_ERR;
            }
            $multi = 1;
        }
        $msgUTF8 = mb_convert_encoding($msg, 'UTF-8', 'GBK');
        $postArray = [
            'items'   => [],
            'content' => $msgUTF8,
            'msgType' => 'sms',
        ];
        if (!$multi)
        {
            $postArray['items'][] = ['to' => $phone];
        }
        else
        {
            foreach ($phone as $v)
            {
                $postArray['items'][] = ['to' => $v];
            }
        }
        $auth = base64_encode($this->user . ":" . md5($this->pwd));
        $header = [
            'Content-Type:application/json;charset=utf-8',
            'Accept:application/json',
            "Authorization:{$auth}",
        ];
        $url = "http://{$this->apiUrl}:9051/api/v1.0.0/message/mass/send";
        $resultArr = FuncAction::request($url, json_encode($postArray), ['header' => $header]);
        $resultJson = json_decode($resultArr[0], true);
        $resultJson['msg'] = mb_convert_encoding($resultJson['msg'], 'GBK', 'UTF-8');
        if ($resultArr[1] !== null && $resultJson === false)
        {
            $resultJson = ['code' => '-999', 'msg' => '通信错误', 'uuid' => 0];
        }
        if (!$multi)
        {
            $this->msgId = $resultJson['uuid'] === 0 ? 0 : $resultJson['uuid'] . '-' . substr($phone, -4, 4);
            $this->OperatorMsg = array(
                'codeStatus' => $resultJson['code'],
                'codeDesc'   => $resultJson['msg'],
            );
        }
        else
        {
            foreach ($phone as $v)
            {
                $this->msgIdList[$v] = $resultJson['uuid'] === 0 ? 0 : $resultJson['uuid'] . '-' . substr($v, -4, 4);
                $this->operatorMsgList[$v] = array(
                    'codeStatus' => $resultJson['code'],
                    'codeDesc'   => $resultJson['msg'],
                    'result'     => $resultJson['code'] === '0' ? self::RET_SUCC : self::RET_ERR,
                );
            }
        }
        return $resultJson['code'] === '0' ? self::RET_SUCC : self::RET_ERR;
    }


    /**
     * 相同内容多个手机号群发
     * @param array  $phone [phone1,phone2,phone3]
     * @param string $msg   message
     * @param string $sign  签名
     * @return int
     */
    public function multiPhoneSend($phone, $msg, $sign)
    {
        $this->send($phone, $msg, $sign);
    }

    /**
     * 不同手机号不同内容无变量群发
     * @param array $multiContent multiContent
     * @return int
     */
    public function batchSend($multiContent)
    {
        if (count($multiContent) > 100)
        {
            foreach ($multiContent as $key => $value)
            {
                $this->msgIdList[$value[0]] = 0;
                $this->operatorMsgList[$value[0]] = array(
                    'codeStatus' => 0,
                    'codeDesc'   => '批次内数量超限',
                    'result'     => self::RET_ERR,
                );
            }
            return self::RET_ERR;
        }
        $postArray = [
            'items'   => [],
            'msgType' => 'sms',
        ];
        $auth = base64_encode($this->user . ":" . md5($this->pwd));
        $header = [
            'Content-Type:application/json;charset=utf-8',
            'Accept:application/json',
            "Authorization:{$auth}",
        ];
        $url = "http://{$this->apiUrl}:9051/api/v1.0.0/message/group/send";
        foreach ($multiContent as $k => $v)
        {
            $msgUTF8 = mb_convert_encoding($v[1], 'UTF-8', 'GBK');
            $postArray['items'][] = ['to' => $v[0], 'content' => $msgUTF8];
        }
        $resultArr = FuncAction::request($url, json_encode($postArray), ['header' => $header]);
        $resultJson = json_decode($resultArr[0], true);
        $resultJson['msg'] = mb_convert_encoding($resultJson['msg'], 'GBK', 'UTF-8');
        if ($resultArr[1] !== null && $resultJson === false)
        {
            $resultJson = ['code' => '-999', 'msg' => '通信错误', 'uuid' => 0];
        }
        foreach ($multiContent as $k => $v)
        {
            $this->msgIdList[$v[0]] = $resultJson['uuid'] === 0 ? 0 : $resultJson['uuid'] . '-' . substr($v[0], -4, 4);
            $this->operatorMsgList[$v[0]] = array(
                'codeStatus' => $resultJson['code'],
                'codeDesc'   => $resultJson['msg'],
                'result'     => $resultJson['code'] === '0' ? self::RET_SUCC : self::RET_ERR,
            );
        }
        return $resultJson['code'] === '0' ? self::RET_SUCC : self::RET_ERR;
    }


    /**
     * 获取 callback status 需要的配置文件, 和设定回调方法
     * @param array $channelConfig channelconfig
     * @return array
     */
    public static function callbackStatusConfig($channelConfig)
    {
        $params = array(
            'account'  => $channelConfig['username'],
            'password' => $channelConfig['password'],
            'pagesize' => 100,
        );
        //直接使用http_build_query会导致@符号转译为%40
        return array(
            'url'           => "http://{$channelConfig['api_url']}:18088/cgi-bin/getreport?account={$params['account']}&password={$params['password']}&pagesize={$params['pagesize']}",
            'action'        => 'get',
            'callbackClass' => '\\Smschannel\\XuanwuSmsChannelAction',
            'callbackFunc'  => 'callbackStatus',
            'callback'      => '',
        );
    }

    /**
     * 获取 callback status, 是回调方法
     * @param array $data data
     * @return boolean
     */
    public static function callbackStatus($data)
    {
        $data = json_decode($data, true);
        if (is_array($data) === false)
        {
            return false;
        }
        $res = false;
        $RedisAction = \RedisAction::connect();
        foreach ($data as $report)
        {
            $dataInfo = [
                'Mobile'   => $report['phone'],
                'Msg_Id'   => $report['batchID'] . '-' . substr($report['phone'], -4, 4),
                'Status'   => $report['originResult'],
                'SendTime' => $report['submitTime'],//发送时间
                'RecvTime' => $report['doneTime'],//到达时间
                'orderID'  => $report['msgID'],//订单号
                'batchID'  => $report['batchID'],//订单号
                'state'    => $report['state'],//状态转译码
                'total'    => $report['total'],//分段数
            ];
            $res = $RedisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($dataInfo));
        }
        return true;
    }
}
