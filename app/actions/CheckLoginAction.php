<?php

class CheckLoginAction extends Action
{
    public $showCodeLimitCount;
    public $IP;

    /**
     * CheckLoginAction constructor.
     */
    public function __construct()
    {
        //$this->showCodeLimitCount = Config::get("showCodeLimitCount");
        $this->showCodeLimitCount = 1;
        $this->IP = get_client_ip();
    }

    /**
     * initFLoginInfo
     * -
     * @param bool $isShowCaptcha isShowCaptcha
     * @return mixed
     * <AUTHOR>
     */
    public function initFLoginInfo(&$isShowCaptcha)
    {
        $now = time();
        $token = md5(rand(1, 999999) . $now);
        $userNameField = $this->getRandUserName();

        $fLoginInfo['token'] = $token;
        $fLoginInfo['loginInit'] = $now;
        $fLoginInfo['userNameField'] = $userNameField;

        loadAction('checkIP');
        if (!CheckIPAction::checkISChinaIP($this->IP))
        {
            $isShowCaptcha = 1;
            $fLoginInfo['showCaptcha'] = 1;
        }
        else
        {
            $loginAction = loadAction('login');
            $expire = intval($_SESSION['expire']);
            if ($expire >= $this->showCodeLimitCount || $loginAction->decideCode($this->IP))
            {
                $isShowCaptcha = 1;
                $fLoginInfo['showCaptcha'] = 1;
            }
            else
            {
                $isShowCaptcha = 0;
                $fLoginInfo['showCaptcha'] = 0;
            }
        }

        if ($isShowCaptcha == 0)
        {
            $ua = trim($_SERVER['HTTP_USER_AGENT']);
            if ($ua == 'Mozilla/5.0 (iPhone; U; CPU iPhone OS 3_0 like Mac OS X; en-us) AppleWebKit/528.18 (KHTML, like Gecko) Version/4.0 Mobile/7A341 Safari/528.16')
            {
                $isShowCaptcha = 1;
                $fLoginInfo['showCaptcha'] = 1;
            }
        }
        return $fLoginInfo;
    }

    /**
     * getRandUserName
     * -
     * @return string
     * <AUTHOR>
     */
    private function getRandUserName()
    {
        $result = '';
        $length = mt_rand(5, 9);
        $charStr = '123546798abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charLength = strlen($charStr);
        for ($i = 0; $i < $length; $i++)
        {
            $result .= $charStr[mt_rand(0, $charLength - 1)];
        }
        return $result;
    }

    /**
     * getFLoginSessionInfo
     * -
     * @return array
     * <AUTHOR>
     */
    public function getFLoginSessionInfo()
    {
        $fLoginSessionInfo = array();
        if (isset($_SESSION[FLOGIN_SESSION_KEY]))
        {
            $fLoginSessionInfo = unserialize($_SESSION[FLOGIN_SESSION_KEY]);
        }
        return $fLoginSessionInfo;
    }


    /**
     * isNeedCheckCaptcha
     * -
     * @return bool
     * <AUTHOR>
     */
    public function isNeedCheckCaptcha()
    {
        $checkResult = false;
        $fLoginSessionInfo = $this->getFLoginSessionInfo();
        if ($fLoginSessionInfo['showCaptcha'] > 0)
        {
            $checkResult = true;
        }
        else
        {
            $loginAction = loadAction('login');
            $expire = intval($_SESSION['expire']);
            if ($expire >= $this->showCodeLimitCount || $loginAction->decideCode(get_client_ip()))
            {
                $checkResult = true;
            }
            else
            {
                loadAction('checkIP');
                if (!CheckIPAction::checkISChinaIP($this->IP))
                {
                    $checkResult = true;
                }
            }
        }

        return $checkResult;
    }

    /**
     * 将fLoginInfo和post中的相关数据合并成字符串用于记录日志用
     * -
     * @return string
     * <AUTHOR>
     */
    public function fLoginInfoToStr()
    {
        $result = 'fLoginInfo:';
        $userNameField = '';
        $fLoginSessionInfo = $this->getFLoginSessionInfo();
        if (count($fLoginSessionInfo) > 0)
        {
            $result .= implode(',', $fLoginSessionInfo) . ',';
            $userNameField = isset($fLoginSessionInfo['userNameField']) ? $fLoginSessionInfo['userNameField'] : '';
            if (!empty($userNameField))
            {
                $result .= 'userNameField:' . $userNameField;
            }
            else
            {
                $result .= 'userNameField:n';
            }
        }
        $result .= '; postData:';
        $result .= 'check_code:' . (isset($_POST['check_code']) ? $_POST['check_code'] : 'n') . ',';
        $result .= 'flToken:' . (isset($_POST['flToken']) ? $_POST['flToken'] : 'n') . ',';
        if (!empty($userNameField))
        {
            $result .= 'userNameField:' . $userNameField . ';';
        }
        else
        {
            $result .= 'userNameField:n';
        }
        return $result;
    }

    /**
     * checkFLoginInfo
     * -
     * @param array $postData PostData
     * @param string $errInfoStr ErrInfoStr
     * @return bool
     * <AUTHOR>
     */
    public function checkFLoginInfo($postData, &$errInfoStr)
    {
        $errInfoStr = '';
        $checkResult = true;
        $fLoginSessionInfo = $this->getFLoginSessionInfo();
        $fieldArray = array(
            'token',
            'loginInit',
            'userNameField',
        );
        foreach ($fieldArray as $key => $fName)
        {
            if (!isset($fLoginSessionInfo[$fName]) || empty($fLoginSessionInfo[$fName]))
            {
                $errInfoStr = $fName . '没有-服务端';
                $checkResult = false;
                break;
            }
        }
        if ($checkResult)
        {
            if ($this->isNeedCheckCaptcha())
            {
                if (!isset($postData['check_code']) || empty($postData['check_code']))
                {
                    $errInfoStr = '缺少验证码';
                    $checkResult = false;
                }
                elseif (!isset($_SESSION['captcha_code']) && !isset($_SESSION['checkIMGCode_new']))
                {
                    $errInfoStr = '缺少验证码数据-服务端';
                    $checkResult = false;
                }
            }
        }


        if ($checkResult)
        {
            $userNameField = $fLoginSessionInfo['userNameField'];
            if (!isset($postData['flToken']) || empty($postData['flToken']) || $postData['flToken'] != md5($fLoginSessionInfo['token']))
            {
                if (!isset($postData['flToken']) || empty($postData['flToken']))
                {
                    $errInfoStr = '缺少token参数或为空';
                }
                elseif ($postData['flToken'] != md5($fLoginSessionInfo['token']))
                {
                    $errInfoStr = 'token不一致';
                }
                $checkResult = false;
            }
            elseif (!isset($postData[$userNameField]) || empty($postData[$userNameField]))
            {
                $errInfoStr = '缺少用户名字段参数';
                $checkResult = false;
            }
        }

        return $checkResult;
    }

    /**
     * clearFInfo
     * -
     * @return void
     * <AUTHOR>
     */
    public function clearFInfo()
    {
        unset($_SESSION[FLOGIN_SESSION_KEY]);
    }

    /**
     * getUserNameField
     * -
     * @return mixed
     * <AUTHOR>
     */
    public function getUserNameField()
    {
        $result = "";
        if (isset($_SESSION[FLOGIN_SESSION_KEY]) && !empty($_SESSION[FLOGIN_SESSION_KEY]))
        {
            $fLoginSessionInfo = unserialize($_SESSION[FLOGIN_SESSION_KEY]);
            return isset($fLoginSessionInfo['userNameField']) ? $fLoginSessionInfo['userNameField'] : "";
        }
        return $result;
    }

    /**
     * clearCaptcha
     * -
     * @return void
     * <AUTHOR>
     */
    public function clearCaptcha()
    {
        unset($_SESSION['checkIMGCode_new']);
        unset($_SESSION['captcha_code']);
    }
}
