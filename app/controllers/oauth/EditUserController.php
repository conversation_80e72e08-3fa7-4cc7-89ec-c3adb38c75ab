<?php

use Octopus\Badwords;
use Common\Utils\Url;

class EditUserController extends Controller
{

    public function actionIndex()
    {
        $loginAction = loadAction("login");
        $regex = Config::get('regex');
        if ($loginInfo = $loginAction->checkAuthCookie())
        {
            if (strlen($_POST['password']) < 6)
            {
                showMessage('密码最少6个字符', 'back');
            }
            if (strlen($_POST['password']) > 16)
            {
                showMessage('密码最多16个字符', 'back');
            }
            $passid = intval($loginInfo['i']);
            $username = $_POST["username"];
            $password = $_POST["password"];
            $email = strtolower($_POST['email']);
            if (!isset($_POST['pwd_strength']))
            {
                $pwd_strength = 2;
            }
            else
            {
                $pwd_strength = $_POST['pwd_strength'];
            }
            $memberModel = loadModel('member');
            if (isset($_POST["act"]) && $_POST["act"] == "setpwd")
            {
                $memberModel->setPassword($passid, $password, $pwd_strength, get_client_ip());
            }
            else
            {
                if ($email)
                {
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL))
                    {
                        showMessage('请输入正确的邮箱', 'back');
                    }
                    if ($memberModel->checkEmail($email))
                    {
                        showMessage('此邮箱已被注册，请换一个', 'back');
                    }
                }
                if (preg_match($regex['username'], $username))
                {
                    showMessage('请输入汉字，字母，数字或邮箱地址', 'back');
                }
                if (strlen($username) < 3)
                {
                    showMessage('2345帐号最少2个字符', 'back');
                }
                if (strlen($username) > 36)
                {
                    showMessage('2345帐号请不要超过24个字符', 'back');
                }
//                $badwords = new Badwords(10);
//                if ($badwords->filter($username))
//                {
//                    showMessage('此帐号已被注册，请修改2345帐号', 'back');
//                }
                if ($memberModel->checkUser($username))
                {
                    showMessage('此帐号已被注册，请修改2345帐号', 'back');
                }
                if ($memberModel->setUser($passid, $username, $password, $pwd_strength, $email))
                {
                    //调用积分改名接口
                    $ret = http_post("http://jifen.yl234.com/api/passportChangeUsername.php", array("passid" => $passid, "username" => $username));
                    if (!$ret || $ret == "fail")
                    {
                        http_post("http://jifen.yl234.com/api/passportChangeUsername.php", array("passid" => $passid, "username" => $username));
                    }
                    $result = $memberModel->read($passid);
                    $result["m_uid"] = unserialize($result['m_uid']);
                    $uid = $result['m_uid']['1'];
                    $username = $result['username'];
                    $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
                    $cTime = time() + 3600 * 24 * 30;
                    $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                    $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);
                }
            }
        }
        $forward = getForwardUrl();
        if ($forward && isset($cookie['I']))
        {
            // 设置其他域名的登录cookie： 2345.cn等 todo cookie ctime 可能不存在
            $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
            $pageArray = array(
                "loadPage" => json_encode($thirdCallbackCookies),
                "forwardPage" => $forward
            );
            return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $pageArray);
        }
        else
        {
            redirect("/member/edit_info.php");
        }
    }

    public function actionPatchAccount($cssType = "main")
    {
        $openid = $_COOKIE['openid'];
        $accessToken = $_COOKIE['access_token'];
        $oauthType = $_COOKIE['oauth_type'];

        if (!$openid || !$accessToken || !in_array($oauthType, array('weibo')))
        {
            redirect('/');
        }

        $oauthAction = loadAction('oauth');
        $oauthUser = $oauthAction->oauthUserInfo($oauthType, $accessToken, $openid);
        if (!$oauthUser)
        {
            redirect('/');
        }

        $oauthModel = loadModel('oauth');
        $bind = $oauthModel->getBind($openid, $oauthType);
        if (!$bind)
        {
            redirect('/');
        }
        else if ($bind["passid"] > 0)
        {
            $passid = $bind["passid"];
            $memberModel = loadModel("member");
            $userInfo = $memberModel->read($passid);

            $userInfo["m_uid"] = unserialize($userInfo['m_uid']);
            $username = $userInfo['username'];
            $userMod = $userInfo['gid'] % 100 == 0 ? $userInfo['gid'] : 0;

            // 未绑定可登录账号
            if (!empty($username) && !$userMod)
            {
                redirect("/login");
                exit;
            }
            else
            {
                $pageArray = array("patched" => 0);
                if ($_SERVER['REQUEST_METHOD'] == 'POST')
                {
                    $this->doPatchAccount($passid);
                    $pageArray = array("patched" => 1);
                }

                // 王牌浏览器, 不包含header 倒计时关闭弹窗
                if ($cssType == "client")
                {
                    loadView('oauth/patch_account_client.tpl.html', $pageArray);
                }
                // 智能浏览器, 倒计时关闭页面
                else if ($cssType == "smartBrowser")
                {
                    loadView('oauth/patch_account_smart_browser.tpl.html', $pageArray);
                }
                else
                {
                    loadView('patch_account.tpl.html', $pageArray);
                }
            }
        }
    }

    private function doPatchAccount($passid)
    {
        $username = $_POST['username'];
        $password = Url::getStringParam('password', "", Url::POST);
        $pwdStrength = Url::getStringParam('pwd_strength', "", Url::POST);

        $msgs = array(
            300 => array(
                0 => '2345帐号最少2个字符',
                1 => '2345帐号请不要超过24个字符',
                2 => '2345帐号请输入汉字，字母，数字',
                3 => '密码最少6个字符',
                4 => '密码最多16个字符',
                6 => '此帐号已被注册，请修改2345帐号'
            )
        );
        if (!$pwdStrength)
        {
            $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
            $pwdObj = $Zxcvbn->passwordStrength($password);
            $pwdStrength = $pwdObj['score'];
        }

        $email = "";
        $regAction = loadAction('reg');
        $result = $regAction->patchUser($passid, $username, $password, $pwdStrength, $email);
        $status = $result[0];
        $status = explode(".", $status);
        if ($status[0] == 300)
        {
            showMessage($msgs[$status[0]][$status[1]], 'back');
        }
        \Common\Utils\Cookie::delOauthBindCookie();
    }

}
