ALTER TABLE passport_log.members_phone_log	MODIFY COLUMN phone_old varchar(50) DEFAULT '' COMMENT '旧手机号码';
ALTER TABLE passport_log.members_phone_log	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_log.logoff_log	        MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';


ALTER TABLE passport_user.members	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_2	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_3	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_4	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_5	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_6	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_7	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_8	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_9	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_10	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_11	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_12	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_13	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_14	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_15	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_16	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_17	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_18	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_19	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_20	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_21	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_22	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_23	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_24	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_25	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_26	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_27	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_28	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_29	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_30	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_31	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_32	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_33	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_34	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_35	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_36	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_37	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_38	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_39	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_40	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_41	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_42	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_43	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_44	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_45	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_46	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_47	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_48	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_49	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_50	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_51	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_52	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_53	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_54	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_55	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_56	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_57	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_58	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_59	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_60	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_61	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_62	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_63	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_64	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_65	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_66	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_67	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_68	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_69	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_70	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_71	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_72	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_73	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_74	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_75	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_76	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_77	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_78	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_79	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_80	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_81	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_82	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_83	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_84	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_85	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_86	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_87	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_88	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_89	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_90	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_91	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_92	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_93	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_94	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_95	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_96	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_97	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_98	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_99	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_100	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_101	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_102	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_103	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_104	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_105	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_106	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_107	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_108	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_109	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_110	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_111	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_112	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_113	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_114	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_115	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_116	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_117	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_118	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_119	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_120	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_121	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_122	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_123	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_124	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_125	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_126	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_127	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_128	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';



ALTER TABLE passport_user.members_phone	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_2	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_3	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_4	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_5	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_6	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_7	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_8	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_9	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_10	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_11	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_12	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_13	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_14	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_15	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_16	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_17	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_18	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_19	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_20	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_21	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_22	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_23	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_24	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_25	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_26	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_27	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_28	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_29	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_30	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_31	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_32	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_33	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_34	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_35	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_36	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_37	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_38	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_39	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_40	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_41	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_42	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_43	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_44	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_45	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_46	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_47	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_48	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_49	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_50	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_51	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_52	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_53	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_54	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_55	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_56	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_57	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_58	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_59	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_60	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_61	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_62	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_63	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_64	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_65	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_66	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_67	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_68	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_69	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_70	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_71	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_72	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_73	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_74	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_75	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_76	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_77	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_78	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_79	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_80	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_81	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_82	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_83	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_84	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_85	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_86	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_87	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_88	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_89	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_90	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_91	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_92	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_93	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_94	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_95	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_96	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_97	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_98	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_99	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_100	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_101	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_102	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_103	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_104	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_105	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_106	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_107	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_108	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_109	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_110	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_111	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_112	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_113	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_114	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_115	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_116	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_117	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_118	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_119	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_120	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_121	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_122	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_123	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_124	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_125	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_126	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_127	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_phone_128	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';


ALTER TABLE passport_user.members_info	    MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_2	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_3	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_4	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_5	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_6	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_7	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_8	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_9	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_10	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_11	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_12	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_13	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_14	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_15	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_16	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_17	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_18	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_19	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_20	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_21	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_22	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_23	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_24	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_25	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_26	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_27	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_28	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_29	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_30	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_31	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_32	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_33	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_34	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_35	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_36	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_37	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_38	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_39	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_40	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_41	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_42	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_43	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_44	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_45	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_46	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_47	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_48	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_49	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_50	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_51	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_52	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_53	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_54	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_55	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_56	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_57	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_58	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_59	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_60	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_61	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_62	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_63	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_64	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_65	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_66	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_67	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_68	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_69	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_70	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_71	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_72	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_73	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_74	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_75	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_76	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_77	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_78	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_79	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_80	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_81	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_82	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_83	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_84	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_85	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_86	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_87	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_88	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_89	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_90	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_91	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_92	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_93	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_94	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_95	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_96	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_97	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_98	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_99	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_100	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_101	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_102	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_103	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_104	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_105	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_106	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_107	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_108	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_109	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_110	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_111	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_112	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_113	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_114	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_115	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_116	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_117	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_118	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_119	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_120	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_121	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_122	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_123	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_124	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_125	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_126	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_127	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';
ALTER TABLE passport_user.members_info_128	MODIFY COLUMN phone varchar(50) DEFAULT '' COMMENT '手机号码';



ALTER TABLE passport_user.members_info	    MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_2	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_3	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_4	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_5	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_6	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_7	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_8	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_9	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_10	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_11	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_12	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_13	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_14	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_15	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_16	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_17	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_18	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_19	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_20	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_21	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_22	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_23	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_24	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_25	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_26	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_27	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_28	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_29	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_30	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_31	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_32	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_33	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_34	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_35	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_36	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_37	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_38	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_39	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_40	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_41	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_42	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_43	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_44	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_45	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_46	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_47	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_48	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_49	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_50	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_51	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_52	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_53	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_54	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_55	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_56	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_57	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_58	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_59	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_60	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_61	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_62	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_63	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_64	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_65	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_66	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_67	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_68	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_69	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_70	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_71	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_72	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_73	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_74	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_75	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_76	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_77	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_78	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_79	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_80	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_81	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_82	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_83	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_84	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_85	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_86	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_87	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_88	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_89	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_90	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_91	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_92	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_93	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_94	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_95	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_96	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_97	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_98	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_99	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_100	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_101	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_102	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_103	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_104	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_105	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_106	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_107	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_108	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_109	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_110	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_111	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_112	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_113	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_114	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_115	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_116	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_117	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_118	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_119	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_120	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_121	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_122	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_123	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_124	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_125	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_126	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_127	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';
ALTER TABLE passport_user.members_info_128	MODIFY COLUMN tel1 varchar(20) DEFAULT '' COMMENT '区号';



ALTER TABLE passport_user.members_info	    MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_2	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_3	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_4	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_5	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_6	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_7	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_8	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_9	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_10	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_11	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_12	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_13	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_14	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_15	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_16	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_17	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_18	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_19	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_20	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_21	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_22	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_23	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_24	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_25	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_26	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_27	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_28	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_29	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_30	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_31	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_32	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_33	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_34	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_35	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_36	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_37	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_38	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_39	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_40	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_41	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_42	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_43	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_44	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_45	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_46	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_47	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_48	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_49	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_50	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_51	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_52	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_53	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_54	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_55	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_56	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_57	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_58	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_59	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_60	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_61	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_62	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_63	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_64	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_65	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_66	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_67	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_68	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_69	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_70	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_71	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_72	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_73	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_74	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_75	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_76	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_77	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_78	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_79	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_80	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_81	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_82	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_83	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_84	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_85	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_86	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_87	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_88	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_89	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_90	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_91	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_92	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_93	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_94	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_95	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_96	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_97	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_98	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_99	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_100	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_101	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_102	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_103	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_104	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_105	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_106	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_107	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_108	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_109	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_110	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_111	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_112	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_113	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_114	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_115	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_116	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_117	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_118	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_119	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_120	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_121	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_122	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_123	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_124	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_125	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_126	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_127	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';
ALTER TABLE passport_user.members_info_128	MODIFY COLUMN tel2 varchar(32) DEFAULT '' COMMENT '电话号码';


ALTER TABLE passport_user.members_info	    MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_2	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_3	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_4	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_5	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_6	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_7	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_8	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_9	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_10	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_11	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_12	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_13	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_14	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_15	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_16	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_17	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_18	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_19	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_20	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_21	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_22	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_23	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_24	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_25	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_26	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_27	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_28	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_29	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_30	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_31	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_32	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_33	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_34	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_35	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_36	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_37	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_38	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_39	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_40	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_41	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_42	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_43	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_44	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_45	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_46	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_47	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_48	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_49	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_50	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_51	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_52	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_53	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_54	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_55	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_56	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_57	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_58	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_59	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_60	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_61	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_62	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_63	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_64	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_65	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_66	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_67	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_68	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_69	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_70	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_71	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_72	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_73	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_74	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_75	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_76	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_77	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_78	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_79	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_80	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_81	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_82	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_83	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_84	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_85	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_86	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_87	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_88	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_89	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_90	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_91	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_92	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_93	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_94	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_95	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_96	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_97	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_98	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_99	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_100	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_101	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_102	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_103	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_104	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_105	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_106	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_107	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_108	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_109	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_110	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_111	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_112	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_113	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_114	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_115	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_116	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_117	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_118	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_119	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_120	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_121	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_122	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_123	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_124	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_125	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_126	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_127	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';
ALTER TABLE passport_user.members_info_128	MODIFY COLUMN tel3 varchar(20) DEFAULT '' COMMENT '分机';


