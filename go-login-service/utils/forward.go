package utils

import (
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strings"
)

// GetForwardUrl gets and validates the forward URL from request
// 获取过滤后的跳转地址 - Direct Go translation of PHP getForwardUrl() function
func GetForwardUrl(r *http.Request) string {
	// $inputForward = !empty($_POST['forward']) ? $_POST['forward'] : (!empty($_GET['forward']) ? $_GET['forward'] : '');
	inputForward := getForwardFromRequest(r)

	// if (preg_match("/\s/", $inputForward)) {
	//     $inputForward = str_replace(" ", "%20", $inputForward);
	// }
	if hasWhitespace(inputForward) {
		inputForward = strings.ReplaceAll(inputForward, " ", "%20")
	}

	// $inputForward = filter_var($inputForward, FILTER_VALIDATE_URL);
	inputForward = validateURL(inputForward)

	var forward string
	// if (!empty($inputForward)) {
	//     $forward = str_replace('%20', ' ', filter_var($inputForward, FILTER_VALIDATE_URL));
	// } else {
	//     $forward = "";
	// }
	if inputForward != "" {
		forward = strings.ReplaceAll(validateURL(inputForward), "%20", " ")
	} else {
		forward = ""
	}

	// $forward = str_replace(array("\"", "'"), "", $forward);
	forward = strings.ReplaceAll(forward, "\"", "")
	forward = strings.ReplaceAll(forward, "'", "")

	// if ($forward != '' &&
	//     !preg_match("/^(http|https):\/\/[^\/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|hao774\.com|7255\.com|km\.com|2345\.cc|2345\.com\.cn)$/", $forward) &&
	//     !preg_match("/^(http|https):\/\/[^\/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|hao774\.com|7255\.com|km\.com|2345\.cc|2345\.com\.cn)(\/|\?)/", $forward))
	// {
	//     $forward = "http://www." . DOMAIN;
	// }
	if forward != "" && !isAllowedDomain(forward) {
		forward = "http://www." + getDomainConstant()
	}

	// Note: The commented PHP code for cookie redirect is not implemented:
	// if ($_COOKIE['is_need_redirect'] == 1)
	// {
	//     $forward = $_COOKIE['redirect_url'];
	// }

	return forward
}

// getForwardFromRequest extracts forward parameter from POST or GET
func getForwardFromRequest(r *http.Request) string {
	// Try POST first, then GET (equivalent to PHP's $_POST['forward'] ?: $_GET['forward'])
	if r.Method == "POST" {
		if err := r.ParseForm(); err == nil {
			if forward := r.PostForm.Get("forward"); forward != "" {
				return forward
			}
		}
	}
	
	// Try GET parameter
	return r.URL.Query().Get("forward")
}

// hasWhitespace checks if string contains whitespace (equivalent to PHP preg_match("/\s/", $str))
func hasWhitespace(s string) bool {
	matched, _ := regexp.MatchString(`\s`, s)
	return matched
}

// validateURL validates URL (equivalent to PHP filter_var($url, FILTER_VALIDATE_URL))
func validateURL(urlStr string) string {
	if urlStr == "" {
		return ""
	}
	
	// Parse the URL to validate it
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return ""
	}
	
	// Check if it's a valid HTTP/HTTPS URL
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return ""
	}
	
	// Check if host is present
	if parsedURL.Host == "" {
		return ""
	}
	
	return urlStr
}

// isAllowedDomain checks if the forward URL is from an allowed domain
func isAllowedDomain(forward string) bool {
	if forward == "" {
		return false
	}
	
	// Define allowed domain patterns (equivalent to PHP regex patterns)
	// Pattern 1: ^(http|https):\/\/[^\/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|hao774\.com|7255\.com|km\.com|2345\.cc|2345\.com\.cn)$
	pattern1 := `^(http|https)://[^/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|hao774\.com|7255\.com|km\.com|2345\.cc|2345\.com\.cn)$`
	
	// Pattern 2: ^(http|https):\/\/[^\/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|hao774\.com|7255\.com|km\.com|2345\.cc|2345\.com\.cn)(\/|\?)
	pattern2 := `^(http|https)://[^/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|hao774\.com|7255\.com|km\.com|2345\.cc|2345\.com\.cn)(/|\?)`
	
	// Check if URL matches either pattern
	matched1, _ := regexp.MatchString(pattern1, forward)
	matched2, _ := regexp.MatchString(pattern2, forward)
	
	return matched1 || matched2
}

// getDomainConstant returns the domain constant (equivalent to PHP DOMAIN constant)
func getDomainConstant() string {
	// PHP: define('DOMAIN', str_replace(array("passport.", "login.", "my."), "", $_SERVER['HTTP_HOST']));
	host := os.Getenv("HTTP_HOST")
	if host == "" {
		return "2345.com"
	}
	
	// Remove prefixes like passport., login., my.
	domain := host
	prefixes := []string{"passport.", "login.", "my."}
	for _, prefix := range prefixes {
		domain = strings.TrimPrefix(domain, prefix)
	}
	
	// If no host or empty after trimming, default to 2345.com
	if domain == "" {
		return "2345.com"
	}
	
	return domain
}
