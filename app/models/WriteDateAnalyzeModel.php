<?php

use Octopus\PdoEx;
class WriteDateAnalyzeModel extends Model
{

    private $pdo;
    
    /**
     * 初始化数据库
     * */
    public function __construct()
    {
        parent::__construct();
        $dbConfig = Config::get("database");
        $this->pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
    }
    
    
    /**
     * 插入时间记录
     * */
    public function insertDateAnalyze ($data)
    {
        $arr = array(
            'passid' => $data['passid'],
            'ip' => $data['clientIp'],
            'uuid' => $data['uuid'],
            'token' => $data['token'],
            'captcha' => $data['captcha'],
            'rename' => $data['rename'],
            'submit' => $data['submit'],
            'date' => date('Y-m-d H:i:s'),
            'isShowCap' => $data['isShowCap']
        );
        $isTrue = $this->pdo->insert('date_analyze_data', $arr, false);
        if ( $isTrue !== false )
        {
            return true;
        }
        else
        {
            return false;
        }
        
    }
    
    /**
     * 获取时间随便记录
     * */
    public function DateAnalyzeList($data,$limit = array())
    {
        $where = '';
        $paramArr = array();
        $col = ' * ';
        if (!empty($data['count']))
        {
            $col = ' count(*) as total ';
        }
        $sql = "SELECT {$col} FROM `date_analyze_data` ";

        
        if (!empty($where))
        {
            $where = ' WHERE ' . trim($where, 'AND');
        }

        $sql .= $where . ' ORDER BY dadid DESC  ';
        if (isset($limit['page']) && !empty($limit['limit']))
        {
            $sql .= " limit {$limit['page']},{$limit['limit']}";
        }
        if (!empty($data['row']))
        {
            return $this->pdo->find($sql, $paramArr);
        }
        else
        {
            return $this->pdo->findAll($sql, $paramArr);
        }
    }
    
}