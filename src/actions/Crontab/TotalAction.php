<?php

namespace Crontab;
class TotalAction extends \Action
{
    public function getProjectTotalList($data = array())
    {
        $BingFaModel = BingFaModel::getInstance();
        $project = $BingFaModel->getTelProject(array('status' => 1));
        $projectList = array();

        foreach ($project as $projectInfo)
        {
            $projectList[$projectInfo['channel']] = 0;
        }
        return $projectList;
    }
    /**
     *  函数名: summarizing
     *  功  能: 语音验证码请求汇总
     *  参  数: @date 日期
     *  作  者: chenmh
     *  日  期: 2015-08-25 13:54:30
     **/
    public function summarizing($date)
    {
        $rang = $answed = $data = $keyList = $this->getProjectTotalList();
        $YyRequestTotoalModel = YyRequestTotoalModel::getInstance();
        //请求总数
        $requestTotal = $YyRequestTotoalModel->getYyRequestTotal(array('date' => $date));
        $count = array();
        foreach ($requestTotal as $requestTotalInfo)
        {
            $count[$requestTotalInfo['from']] = $requestTotalInfo['total'];
        }
        // 成功请求数
        $requestSuccTotal = $YyRequestTotoalModel->getYyRequestTotal(array('date' => $date,
                'request_status' => 200));
        foreach ($requestSuccTotal as $requestSuccInfo)
        {
            $per = $requestSuccInfo['from'] . "_per";
            $data[$requestSuccInfo['from']] = $requestSuccInfo['total'];
            $data[$per] = round(($requestSuccInfo['total'] / $count[$requestSuccInfo['from']]) *
                100, 2);
        }


        $dateFormat = date("Y-m-d", strtotime($date));
        // 响铃数
        $yyRespondRangTotal = $YyRequestTotoalModel->getYyRespondTotal(array('date' => $dateFormat,
                'is_rang' => 1));

        foreach ($yyRespondRangTotal as $yyRespondRangInfo)
        {
            $per = $yyRespondRangInfo['f'] . "_per";
            $rang[$yyRespondRangInfo['f']] = $yyRespondRangInfo['c'];
            $rang[$per] = round(($yyRespondRangInfo['c'] / $data[$yyRespondRangInfo['f']]) *
                100, 2);
        }

        // 应答数
        $yyRespondAnawedTotal = $YyRequestTotoalModel->getYyRespondTotal(array('date' =>
                $dateFormat, 'is_answed' => 1));
        foreach ($yyRespondAnawedTotal as $yyRespondAnawedInfo)
        {
            $per = $yyRespondAnawedInfo['f'] . "_per";
            $answed[$yyRespondAnawedInfo['f']] = $yyRespondAnawedInfo['c'];
            $answed[$per] = round(($yyRespondAnawedInfo['c'] / $rang[$yyRespondAnawedInfo['f']]) *
                100, 2);
        }
        $YyRequestTotoalModel->delRequestTotal($date);
        
        foreach ($keyList as $keyInfo => $keyListInfo)
        {
            $per = $keyInfo . "_per";
            $requestData = array(
                'date' => $date,
                'type' => $keyInfo,
                'success_request' => !empty($data[$keyInfo]) ? $data[$keyInfo] : 0,
                'success_request_percent' => !empty($data[$per]) ? $data[$per] : 0,
                'success_rang' => !empty($rang[$keyInfo]) ? $rang[$keyInfo] : 0,
                'success_rang_percent' => !empty($rang[$per]) ? $rang[$per] : 0,
                'success_answed' => !empty($answed[$keyInfo]) ? $answed[$keyInfo] : 0,
                'success_answed_percent' => !empty($answed[$per]) ? $answed[$per] : 0);

            $YyRequestTotoalModel->addRequestTotal($requestData);
        }


    }

    public function requestTotal($date)
    {
        $keyList = $sTotalSuccess = $vTotalSuccess = $sTotal = $vTotal = $totalSuccess =
            $total = $this->getProjectTotalList();
        // 总请求数
        $YyRequestTotoalModel = YyRequestTotoalModel::getInstance();

        $dateFormat = date("Y-m-d", strtotime($date));
        $resTotal = $YyRequestTotoalModel->getYyRespondTotal(array('date' => $dateFormat));

        foreach ($resTotal as $resTotalInfo)
        {
            $total[$resTotalInfo['f']] = $resTotalInfo['c'];
        }
        // 总成功数
        $resSuccess = $YyRequestTotoalModel->getYyRespondTotal(array('date' => $dateFormat,
                'isRangOrSms' => 1));
        foreach ($resSuccess as $resSuccessInfo)
        {
            $per = $resSuccessInfo['f'] . "_per";
            $totalSuccess[$resSuccessInfo['f']] = $resSuccessInfo['c']; // 总成功数
            $totalPer[$per] = round(($resSuccessInfo['c'] / $total[$resSuccessInfo['f']]) *
                100, 2);
        }
        // 语言验证码请求数 另通过 总请求数 - 语言验证码请求数 = 短信验证码请求数
        $resV = $YyRequestTotoalModel->getYyRespondTotal(array('date' => $dateFormat,
                'is_sms' => 0));
        foreach ($resV as $resVInfo)
        {
            $vTotal[$resVInfo['f']] = $resVInfo['c']; // 语言验证码总请求数
            $sTotal[$resVInfo['f']] = $total[$resVInfo['f']] - $resVInfo['c']; // 短信验证码总请求数
        }
        // 语言验证码成功请求数 另通过 总成功请求数 - 语言验证码成功成功请求数 = 短信验证码成功请求数
        $resvSuccess = $YyRequestTotoalModel->getYyRespondTotal(array(
            'date' => $dateFormat,
            'is_sms' => 0,
            'is_rang' => 1));
        foreach ($resvSuccess as $resvSuccessInfo)
        {
            $per = $resvSuccessInfo['f'] . "_per";
            $vTotalSuccess[$resvSuccessInfo['f']] = $resvSuccessInfo['c']; // 语言验证码成功请求数
            $sTotalSuccess[$resvSuccessInfo['f']] = $totalSuccess[$resvSuccessInfo['f']] - $resvSuccessInfo['c']; // 短信验证码成功请求数
            $vPer[$per] = round(($resvSuccessInfo['c'] / $vTotal[$resvSuccessInfo['f']]) *
                100, 2);
            $sPer[$per] = round(($sTotalSuccess[$resvSuccessInfo['f']] / $sTotal[$resvSuccessInfo['f']]) *
                100, 2);
        }

        /*
        echo '总请求数' . '<pre>' . print_r($total, true) . '</pre>';
        echo '总成功数' . '<pre>' . print_r($total_success, true) . '</pre>';
        echo '总百分比' . '<pre>' . print_r($total_per, true) . '</pre>';
        echo '语言验证码总请求数' . '<pre>' . print_r($v_total, true) . '</pre>';
        echo '短信验证码总请求数' . '<pre>' . print_r($s_total, true) . '</pre>';
        echo '语言验证码成功请求数' . '<pre>' . print_r($v_total_success, true) . '</pre>';
        echo '短信验证码成功请求数' . '<pre>' . print_r($s_total_success, true) . '</pre>';
        echo '语言验证码百分比' . '<pre>' . print_r($v_per, true) . '</pre>';
        echo '短信验证码百分比' . '<pre>' . print_r($s_per, true) . '</pre>';
        */
        //$date = date('Ymd', strtotime($date));
        $YyRequestTotoalModel->delYyRequestTotal($date);
        foreach ($keyList as $keyInfo => $keyListInfo)
        {
            $addData = array(
                'date' => $date,
                'channel' => $keyInfo,
                'total' => $total[$keyInfo],
                'total_success' => $totalSuccess[$keyInfo],
                'v_total' => $vTotal[$keyInfo],
                's_total' => $sTotal[$keyInfo],
                'v_total_success' => $vTotalSuccess[$keyInfo],
                's_total_success' => $sTotalSuccess[$keyInfo]);
            $YyRequestTotoalModel->addYyRequestTotal($addData);
        }

    }
}

?>