
// 每三分钟, 定期删除数据
CREATE TABLE `passport`.`account_stat` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `type` TINYINT NOT NULL COMMENT "1:login, 2:reg, 3:待定, ..",
  `client_type` TINYINT NOT NULL COMMENT "1: web, 2: app",
  `account_type` TINYINT NOT NULL COMMENT "1:phone 2:email 3:username 4:oauth_qq 5:待定",
  `group_type` VARCHAR(32) NOT NULL COMMENT "group: login xiaoshuo",
  `source` VARCHAR(32) NOT NULL COMMENT "url: jf.2345.com/ mid:reader2345",
  `status` TINYINT NOT NULL COMMENT "0:fail, 1:success",
  `amount` BIGINT NOT NULL COMMENT "数量",
  `ctime` datetime NOT NULL DEFAULT "0000-00-00 00:00:00",
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;


// 每日汇总, 建议:定期备份数据并且归档旧数据
CREATE TABLE `passport`.`account_stat_summary` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `type` TINYINT NOT NULL COMMENT "1:login, 2:reg, 3:待定, ..",
  `client_type` TINYINT NOT NULL COMMENT "1: web, 2: app",
  `account_type` TINYINT NOT NULL COMMENT "1:phone 2:email 3:username 4:oauth_qq 5:待定",
  `group_type` VARCHAR(32) NOT NULL COMMENT "group: login xiaoshuo",
  `source` VARCHAR(32) NOT NULL COMMENT "url: jf.2345.com/ mid:reader2345",
  `status` TINYINT NOT NULL COMMENT "0:fail, 1:success",
  `amount` BIGINT NOT NULL COMMENT "数量",
  `ctime` datetime NOT NULL DEFAULT "0000-00-00 00:00:00",
  PRIMARY KEY (`id`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
