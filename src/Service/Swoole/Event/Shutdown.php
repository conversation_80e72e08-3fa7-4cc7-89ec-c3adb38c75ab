<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/15
 * Time: 17:30
 */
namespace Service\Swoole\Event;

use Service\Func\ServiceFunc;
use Service\Swoole\Tool\Log;

class Shutdown
{
    /**
     * 停服事件
     * -
     * @param object $service $service
     * @return void
     */
    public static function OnShutdown($service)
    {
        $removeStatus = ServiceFunc::regServiceIp($service->host, $service->port, 0);
        $msg = $service->host . ':' . $service->port . '关闭服务';
        if ($removeStatus === false)
        {
             $msg .= PHP_EOL . '注销ip失败';
        }
        ServiceFunc::sendWx($msg);
    }
}
