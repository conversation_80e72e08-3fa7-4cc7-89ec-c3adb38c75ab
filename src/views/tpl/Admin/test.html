
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
<title></title>
<script language="javascript" type="text/javascript" src="/style/js/jquery-1.7.1.min.js"></script>
<script language="javascript" type="text/javascript" src="/style/js/date/WdatePicker.js"></script>
<link href="/style/css/global.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div class="backtop"><a href="#">返回顶部</a></div>

<script src="/js/base.min.js" type="text/javascript"></script>
<script src="/js/ajax_tt.js"></script>
<script src="/jifenimg/js/pop_v1.js" type="text/javascript"></script>
<script src="js/jquery.fixedtableheader.min.js" type="text/javascript"></script>
<style type="text/css">
th {
	text-align: center;
}
table{margin-top:10px;}
table.hover{background:#6ab2e7; color:#000}
table img{margin:3px}
table td{padding:10px}
</style>

<div class="main">
	<div class="crumb">
		当前位置：<a href="welcome.php">积分后台首页</a> > <strong>短信供应商列表</strong>
	</div>

	<div class="pag mt10">
		<div class="tit">
			<a class="current" href="sms_manager.php?act=channel_list">短信供应商列表</a>
			<a href="sms_manager.php?act=config_list">短信配置列表</a>
			<a href="sms_manager.php?act=sound_config">语音验证码开关</a>
            <a href="sms_manager.php?act=balance_info">余额</a>
		</div>
	</div>
	<div>
		<p class="ml20 mt10" style="color:red">供应商的签名内容以合同约定为准，请勿随意更改，否则会导致短信无法发送。</p>
		<p class="ml20 mt10"><a href="javascript:;" onclick="showChannel(0)">新增供应商</a></p>
	</div>
	{{$pageArray.pagehtml_short}}<div style="margin-bottom:10px;"></div>
	<table width="100%" class="table mt10"">
	<tr class="th">
	<th width="3%">ID</th><th>供应商</th><th width="5%">通道状态</th><th width="5%">支持移动</th><th width="5%">支持电信</th><th width="5%">支持联通</th><th width="10%">签名</th><th>描述（资费等）</th><th>API地址</th><th width="5%">账户号</th><th width="5%">账户密码</th><th>创建时间</th>
	</tr>
	{{foreach item=item key=key from=$pageArray.channels}}
		<tr class="center">
		<td>{{$item.id}}</td>
		<td><a href="javascript:;" onclick="showChannel({{$item.id}})">{{$item.provider_name}}</a></td>
		<td id="status_{{$item.id}}"><a href="javascript:;" onclick="changeChannelStatus({{$item.id}},{{$item.raw_status}})"><span>{{$item.status}}</span></a></td>
		<td>{{$item.support_mobile}}</td>
		<td>{{$item.support_telecom}}</td>
		<td>{{$item.support_unicom}}</td>
		<td>{{$item.signiture}}</td>
		<td>{{$item.description}}</td>
		<td>{{$item.api_url}}</td>
		<td>{{$item.username}}</td>
		<td>{{$item.password}}</td>
		<td>{{$item.createdate}}</td>
		</tr>
	{{/foreach}}
	</table>
	{{$pageArray.pagehtml}}
	
	<div class="pop" style="margin-left: -450px; top: 240px;display:none;" id="pop">
	  <div class="pop_con" style="width:500px">
	    <div class="tit"><a title="关闭" class="ico_close_blue" href="javascript:void(0);" onclick="popConstructing.hideClient('pop');"></a>通道信息</div>
	    <div class="p20 tCenter" id="big-pic">
	   		<input type="hidden" id="id" value=''/>
	   		<table class="table" width="100%">
	   		<tr>
	   			<td width="20%">供应商</td><td><input type="text" size="30" id="provider_name"/></td>
	   		</tr>
	   		<tr>
	   			<td>支持移动</td><td><input type="checkbox" id="support_mobile" value="1"/></td>
	   		</tr>
	   		<tr>
	   			<td>支持电信</td><td><input type="checkbox" id="support_telecom" value="1"/></td>
	   		</tr>
	   		<tr>
	   			<td>支持联通</td><td><input type="checkbox" id="support_unicom" value="1"/></td>
	   		</tr>
	   		<tr>
	   			<td>签名</td><td><input type="text" size="30" id="signiture"/></td>
	   		</tr>
	   		<tr>	
	   			<td>描述</td><td><textarea rows="4" cols="33" id="description"></textarea></td>
	   		</tr>	
	   		<tr>	
	   			<td>API地址</td><td><textarea rows="3" cols="33" id="api_url"></textarea></td>
	   		</tr>	
	   		<tr>
	   			<td>账户号</td><td><input type="text" size="30" id="username"/></td>
	   		</tr>
	   		<tr>
	   			<td>账户密码</td><td><input type="text" size="30" id="password"/></td>
	   		</tr>
	   		<tr>
	   			<td colspan="2"><input type="button" size="30" value="确定" onclick="changeChannel()" />
	   			<input type="button" size="30" value="取消" onclick="popConstructing.hideClient('pop')" />
	   			</td>
	   		</tr>
	   		</table>
	  	</div>
	  </div>
</div>
<script type="text/javascript">
	function changeChannelStatus(id, status){
		var tips = '';
		var dest_status = 0;
		var text = '';
		if(status == 0) {
			tips = '确定打开通道？';
			text = 'open';
			dest_status = 1;
		} else {
			tips = '确定关闭通道？';
			dest_status = 0;
			text = 'close';
		}
		var flag = confirm(tips);
		if(flag){
			text ='<a href="javascript:;" onclick="changeChannelStatus('+id+','+dest_status+')"><span>'+text+'</span></a>';
			$.post('sms_manager.php?act=channel_status_change', {id: id, status: dest_status}, function(data){
				if(data == '1'){
					$('#status_' + id).html(text);
				}
			});
		}
	}
	function showChannel(id){
		if(id > 0) {
			$.get("sms_manager.php?act=channel_get", {id:id}, function(data){
				data = eval("(" + data + ")");
				reset();
				$("#id").val(data.id);
				$("#provider_name").val(data.provider_name);
				if(data.support_mobile == 1)
					$("#support_mobile").attr("checked", true);
				if(data.support_telecom == 1)
					$("#support_telecom").attr("checked", true);
				if(data.support_unicom == 1)
					$("#support_unicom").attr("checked", true);
				$("#api_url").val(data.api_url);
				$("#signiture").val(data.signiture);
				$("#description").val(data.description);
				$("#username").val(data.username);
				$("#password").val(data.password);
			});
		} else {
			reset();
		}
		popConstructing.client('pop');
	}
	function changeChannel(){
		var id = $("#id").val();
		$.post("sms_manager.php?act=channel_modify",{
			id: id,
			provider_name: $("#provider_name").val(),
			support_mobile: $("#support_mobile").attr("checked") ? 1 : 0,
			support_telecom: $("#support_telecom").attr("checked") ? 1 : 0,
			support_unicom: $("#support_unicom").attr("checked") ? 1 : 0,
			api_url: $("#api_url").val(),
			signiture: $("#signiture").val(),
			description: $("#description").val(),
			username: $("#username").val(),
			password: $("#password").val()
		}, function(data) {
			window.location.href = 'sms_manager.php?act=channel_list';
		});
	}
	
	function reset() {
		$("#id").val('');
		$("#provider_name").val('');
		$("#support_mobile").attr("checked", false);
		$("#support_telecom").attr("checked", false);
		$("#support_unicom").attr("checked", false);
		$("#api_url").val('');
		$("#signiture").val('');
		$("#description").val('');
		$("#username").val('');
		$("#password").val('');
	}
	
</script>
</body>
</html>