<?php

/**
 * Copyright (c) 2015,上海二三四五网络科技有限公司
 * 文件名称：UserController.php
 * 摘    要：用户信息接口
 * 作    者：张小虎
 * 修改日期：2015.11.19
 */

class UserController extends Controller
{

    public function actionInfo()
    {
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        if (!$authInfo)
        {
            die(json_encode(array(
                'code' => 401,
                'data' => '',
                'msg' => mb_convert_encoding('身份验证失效，请重新登录！', 'UTF-8', 'GBK')
            )));
        }

        //返回加密的cookie I
        if (isset($_GET['src']) && $_GET['src'] == 'loginSuccessCallback') {
            RedisEx::getInstance()->del(sprintf("redis:cache:member:update:password:passid:%s", $authInfo['i']));
            die(json_encode(array(
                'code' => 200,
                'data' => encryptCookieIByAes128cbc($_COOKIE['I']),
                'msg' => mb_convert_encoding('信息获取成功！', 'UTF-8', 'GBK')
            )));
        }

        $passid = $authInfo['i'];
        $memberModel = loadModel("member");
        /** @see MemberModel::read */
        $userinfo = $memberModel->read($passid);
        foreach ($userinfo as $key => $value)
        {
            $userinfo[$key] = mb_convert_encoding($value, 'UTF-8', 'GBK');
        }
        die(json_encode(array(
            'code' => 200,
            'data' => $userinfo,
            'msg' => mb_convert_encoding('用户信息获取成功！', 'UTF-8', 'GBK')
        )));
    }

}
