<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/15
 * Time: 16:09
 */

namespace Service\Swoole\Event;

use Service\Swoole\Tool\Format;
use Service\Swoole\Tool\Sign;

class Receive
{
    /**
     * 接受内容
     * -
     * @param object $service 服务
     * @param object $fd 文件描述
     * @param string $reactor_id $reactor_id
     * @param array $data 数据
     * @return void
     */
    public static function onReceive($service, $fd, $reactor_id, $data)
    {
        $data = Sign::getRequestData($data);
        $data = json_decode($data, true);
        if (empty($data) || !is_array($data))
        {
            $service->send($fd, Format::returnJson(-1, '数据格式不正确'));
            return;
        }
        if (Sign::checkSign($data))
        {
            $namespace = 'Service\Swoole\ReceiveHandle\\' . $data['ReceiveHandle'];
            if (class_exists($namespace))
            {
                if (method_exists($namespace, 'run'))
                {
                    (new $namespace())->run($service, $fd, $reactor_id, $data);
                }
                else
                {
                    $service->send($fd, Format::returnJson(-5, 'ReceiveHandle未实现'));
                }
            }
            else
            {
                $service->send($fd, Format::returnJson(-4, 'ReceiveHandle不存在'));
            }
        }
        else
        {
            $service->send($fd, Format::returnJson(-2, '数据校验失败'));
        }
    }
}
