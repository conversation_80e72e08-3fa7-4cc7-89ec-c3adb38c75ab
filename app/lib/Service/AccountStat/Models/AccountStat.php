<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：AccountStat.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：07-13, 2016
 */

namespace Service\AccountStat\Models;

class AccountStat
{
    private $pdo;

    const ACCOUNT_STAT = "account_stat";
    const ACCOUNT_STAT_SUMMARY = "account_stat_summary";
    const ACCOUNT_STAT_AUTO = "account_stat_auto";

    /**
     * AccountStat constructor.
     * -
     * @return void
     */
    public function __construct()
    {
        $dbConfig = \Config::get("database");
        $this->pdo = \Octopus\PdoEx::getInstance(DB_PASSPORT_LOG, $dbConfig[DB_PASSPORT_LOG]);
    }

    /**
     * 每三分钟 归档
     * -
     * @param array $columns 字段
     * @param array $data 数据
     * @return bool|int
     */
    public function storeCacheIntoDb($columns, $data)
    {
        $res = $this->pdo->batch(self::ACCOUNT_STAT, $columns, $data);
        return $res;
    }

    /**
     * 自动登录数据落库
     *
     * @param array $data 数据
     * @return bool|int
     */
    public function storeStatAutoData($data)
    {
        $res = $this->pdo->duplicate(self::ACCOUNT_STAT_AUTO, $data);
        return $res;
    }

    /**
     * 获取分组注册统计，
     *
     * @param  int   $groupType groupType
     * @param string $startTime startTime
     * @param string $endTime   endTime
     *
     * @return array|bool
     */
    public function getRegAccountStatSummaryByGroupType($groupType, $startTime = "", $endTime = "")
    {
        if (empty($endTime))
        {
            return array();
        }
        $sql = "SELECT ctime, group_type, SUM(amount) AS amount 
            FROM " . self::ACCOUNT_STAT_SUMMARY . "
            WHERE ctime >= :startTime AND ctime <= :endTime AND type = :type AND group_type=:groupType AND status=:status
            group by ctime";
        $params = array(
            ":startTime" => $startTime,
            ":endTime"   => $endTime,
            ":type"      => \Service\AccountStat\Consts\AccountStat::TP_REG,
            ":groupType" => $groupType,
            ":status"    => \Service\AccountStat\Consts\AccountStat::STATUS_SUCC,
        );
        $res = $this->pdo->findAll($sql, $params);

        return $res;
    }

    /**
     * 每日归档 数据获取
     * -
     * @param string $startTime 开始时间
     * @param string $endTime 结束时间
     * @return array|bool
     */
    public function getAccountStatForSummary($startTime = "", $endTime = "")
    {
        if (empty($endTime))
        {
            $startTime = date('Y-m-d H:i:s', strtotime('-2 hours'));
            $endTime = date('Y-m-d H:i:s');
        }
        $sql = "select type, client_type, account_type, group_type, source, status, sum(amount) as amount from " . self::ACCOUNT_STAT . "
         where ctime >= :startTime and ctime <= :endTime group by type, client_type, account_type, source, status";
        $params = array(
            ":startTime" => $startTime,
            ":endTime" => $endTime,
        );
        $res = $this->pdo->findAll($sql, $params);
        return $res;
    }

    /**
     * 每日归档
     * @param array $columns 字段
     * @param array $data 数据
     * @return bool|int
     */
    public function summary($columns, $data)
    {
        $res = $this->pdo->batch(self::ACCOUNT_STAT_SUMMARY, $columns, $data);
        return $res;
    }

    /**
     * delete old stat
     * @param string $endTime 删除时间
     * @return bool|int
     */
    public function deleteOldStat($endTime)
    {
        $condition = array(
            'where' => ' ctime < :endTime ',
            'params' => array(
                ':endTime' => $endTime,
            )
        );
        $res = $this->pdo->delete(self::ACCOUNT_STAT, $condition);
        return $res;
    }

    /**
     * 类型统计
     * @param string $type type
     * @param string $clientType clientType
     * @param string $startTime 起始时间
     * @param string $endTime 结束时间
     * @return array|bool
     */
    public function getAccountStatSummaryDay($type, $clientType, $startTime = "", $endTime = "")
    {
        if (empty($endTime))
        {
            $startTime = date('Y-m-d 00:00:00', strtotime('-30 days'));
            $endTime = date('Y-m-d 23:59:59', strtotime('-1 days'));
        }
        $sql = "select DATE_FORMAT(ctime,:format) as `day`, group_type, source, status, sum(amount) as amount from " . self::ACCOUNT_STAT_SUMMARY . "
         where type=:type and client_type=:clientType and ctime >= :startTime and ctime <= :endTime group by source, status, month(ctime), day(ctime)";
        $params = array(
            ":format" => "%Y-%m-%d",
            ":type" => $type,
            ":clientType" => $clientType,
            ":startTime" => $startTime,
            ":endTime" => $endTime,
        );
        $res = $this->pdo->findAll($sql, $params);
        return $res;
    }

    /**
     * 获取3分钟的数据
     * -
     * @param string $type type
     * @param string $clientType clientType
     * @param string $startTime 起始时间
     * @param string $endTime 结束时间
     * @return array|bool
     */
    public function getAccountStatForMinute($type, $clientType, $startTime = "", $endTime = "")
    {
        if (empty($endTime))
        {
            $startTime = date('Y-m-d H:i:s', strtotime('-2 hours'));
            $endTime = date('Y-m-d H:i:s');
        }
        $sql = "select DATE_FORMAT(ctime,:format) as `time1`, ( MINUTE(ctime)-MINUTE(ctime)%3) as `time2`, group_type, source, status, sum(amount) as amount from " . self::ACCOUNT_STAT . "
         where type=:type and client_type=:clientType and ctime >= :startTime and ctime <= :endTime group by source, status, HOUR(ctime), MINUTE(ctime)-MINUTE(ctime)%3";
        $params = array(
            ":format" => "%Y-%m-%d %H:",
            ":type" => $type,
            ":clientType" => $clientType,
            ":startTime" => $startTime,
            ":endTime" => $endTime,
        );
        $res = $this->pdo->findAll($sql, $params);
        return $res;
    }

    /**
     * 获取汇总数据
     * -
     * @param string $type type
     * @param string $clientType clientType
     * @param string $startTime 起始时间
     * @param string $endTime 结束时间
     * @return array|bool
     */
    public function getAccountStatSummaryDetail($type, $clientType, $startTime = "", $endTime = "")
    {
        if (empty($endTime))
        {
            $startTime = date('Y-m-d H:i:s', strtotime('-30 days'));
            $endTime = date('Y-m-d H:i:s');
        }
        $sql = "select DATE_FORMAT(ctime,:format) as `day`, account_type, group_type, source, status, sum(amount) as amount from " . self::ACCOUNT_STAT_SUMMARY . "
         where type=:type and client_type=:clientType and ctime >= :startTime and ctime <= :endTime group by account_type, source, status, month(ctime), day(ctime)";
        $params = array(
            ":format" => "%Y-%m-%d",
            ":type" => $type,
            ":clientType" => $clientType,
            ":startTime" => $startTime,
            ":endTime" => $endTime,
        );
        $res = $this->pdo->findAll($sql, $params);
        return $res;
    }

    /**
     * 获取一段时间的统计数 默认1个小时 20条数据
     * -
     * @param string $type $type
     * @param string $source $source
     * @param int $status $status
     * @param string $startTime 起始时间
     * @param string $endTime 结束时间
     * @return array|bool
     */
    public function getAmountForMinute($type, $source = "", $status = 1, $startTime = "", $endTime = "")
    {
        if (empty($endTime))
        {
            $startTime = date('Y-m-d H:i:s', strtotime('-1 hours'));
            $endTime = date('Y-m-d H:i:s');
        }

        $params = array(
            ":type" => $type,
            ":status" => $status,
            ":startTime" => $startTime,
            ":endTime" => $endTime,
        );
        if ($source)
        {
            $params[":source"] = $source;
            $where = " where type=:type and status=:status and source=:source and ctime >= :startTime and ctime <= :endTime ";
        }
        else
        {
            $params[":source"] = "game.2345.com";
            $where = " where type=:type and status=:status and source!=:source and ctime >= :startTime and ctime <= :endTime ";
        }
        $sql = "select HOUR (ctime) as `hour`, ( MINUTE(ctime)-MINUTE(ctime)%3) as `minute`, sum(amount) as amount from " . self::ACCOUNT_STAT . $where . "
         group by HOUR(ctime), MINUTE(ctime)-MINUTE(ctime)%3 order by MINUTE(ctime)-MINUTE(ctime)%3";
        $res = $this->pdo->findAll($sql, $params);
        return $res;
    }

}
