<?php

use Service\Encryption\Aes\AesManager;
use Service\Encryption\Sign\SignManager;
use WebLogger\Facade\LoggerFacade;

includeBase('NController');

class NFindController extends NController
{
    /**
     * 判断账号类型
     * /clientapi/find
     * @return void
     */
    public function actionType()
    {
        if ($_SERVER['REQUEST_METHOD'] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        session_start();
        $captchaId = Common\Utils\Url::getStringParam('captchaId');
        if (APP_USE_NECAPTCHA != "false" && !empty($captchaId)) {
            $necessaryField = self::getNeedField(['username', 'mid', SignManager::$paramSignName,]);
            list($username) = $this->getParamAndAutoDeny($necessaryField);
            $validateAction = loadAction('NEValidate');
            $errInfo = null;
            if (!$validateAction->appCheck163Verify($_POST, $errInfo)) {
                $context = [
                    'level'   => 'error',
                    'type'    => "error_ne_net",
                    'path'    => "/clientapi/NFind/SendCode",
                    'context' => $errInfo,
                ];
                LoggerFacade::error("易盾校验失败", $context);
                if ($errInfo["error"] == "100105") {
                    $this->codeResponse(100105, "", "校验失败");
                }
                $this->codeResponse(4105, "", "校验失败");
            }
        } else {
            $necessaryField = self::getNeedField(['username', 'captchaCode', 'mid', SignManager::$paramSignName,]);
            list($username, $checkCode) = $this->getParamAndAutoDeny($necessaryField);
            if ($checkCode != $_SESSION['captcha_code']) {
                $this->showError303();
            }
            //验证通过后清空验证码
            unset($_SESSION['captcha_code']);
        }
        $username = (new AesManager())->aes128cbcHexDecrypt($username);
        $username = mb_convert_encoding($username, "GBK", "UTF-8");
        $FindAction = loadAction('Find');
        $accoutTypeData = $FindAction->checkAccoutType($username);
        $ret = $accoutTypeData['passid'];
        $from = $accoutTypeData['type'];
        if ($from == "email") {
            if ($ret[0] == 0) {
                $this->showError304();
            } elseif ($ret[0] == 2) {
                $this->showError300();
            } else {
                $_SESSION['findPassid'] = $ret[1];
                $this->showUserInfo(array("phone" => "", "email" => $username));
            }
        } elseif ($from == 'phone') {
            if (!$ret) {
                $this->showError304();
            } else {
                $_SESSION['findPassid'] = $ret;
                $this->showUserInfo(array("phone" => $username, "email" => ""));
            }
        } else {
            if (!$ret) {
                $this->showError304();
            } else {
                $_SESSION['findPassid'] = $ret;
                $passid = $ret;
                $memModel = loadModel("member");
                $userinfo = $memModel->read($passid);
                $this->showUserInfo($userinfo);
            }
        }
    }

    /**
     * 发送短信验证码或邮箱验证码，短信验证码需要加图片验证码验证（图片验证码显示的策略是按云控配置来配置）
     * @return mixed
     */
    public function actionSendCode()
    {
        if ($_SERVER['REQUEST_METHOD'] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        session_start();
        $passid = $_SESSION['findPassid'];
        if (empty($passid)) {
            $this->showError405();
        }
        $necessaryField = self::getNeedField(['mid', 'from', SignManager::$paramSignName,]);
        list($mid, $from) = $this->getParamAndAutoDeny($necessaryField);
        $from = ($from == 'phone') ? 'phone' : 'email';
        $captchaCode = $_POST['captchaCode'];

        if ($from == 'phone') {
            $phoneAction = loadAction("phone");
            $phoneInfo = $phoneAction->get($passid);
            $captchaId = Common\Utils\Url::getStringParam('captchaId');
            if (APP_USE_NECAPTCHA != "false" && !empty($captchaId)) {
                $validateAction = loadAction('NEValidate');
                $errInfo = null;
                if (!$validateAction->appCheck163Verify($_POST, $errInfo)) {
                    $context = [
                        'level'   => 'error',
                        'type'    => "error_ne_net",
                        'path'    => "/clientapi/NFind/SendCode",
                        'context' => $errInfo,
                    ];
                    LoggerFacade::error("易盾校验失败", $context);
                    if ($errInfo["error"] == "100105") {
                        $this->codeResponse(100105, "", "校验失败");
                    }
                    $this->codeResponse(4105, "", "校验失败");
                }
            } else {
                $ShowCaptchaAction = loadAction('ShowCaptcha');
                $showCaptcha = $ShowCaptchaAction->isShowLoginRegCaptcha($mid, $phoneInfo['phone'], $captchaCode);
                if ($showCaptcha['code'] != 200) {
                    $this->echoResponse($showCaptcha);
                }
            }


            $status = $phoneAction->sendVerifyCode($mid, $passid, 8, 156);
        } else {
            $memberModel = loadModel("member");
            $emailInfo = $memberModel->getEmailByPassid($passid);
            if (!$emailInfo['email']) {
                $this->showError404();
            }
            $email = $emailInfo['email'];
            $timeKey = "EV:time:$mid:$passid";
            $codeKey = "EV:code:$mid:$passid";
            $code = rand(100000, 999999);
            $result = sendCodeFromEmail($email, $code);
            if ($result == 1) {
                $redis = RedisEx::getInstance();
                $redis->setex($timeKey, 1800, 0);
                $redis->setex($codeKey, 1800, md5($email . $code));
                $status = 200;
            } elseif ($result == 2) {
                //发送频繁
                $status = 400;
            } else {
                //服务器忙
                $status = 500;
            }
        }
        switch ($status) {
            case 200:
                $this->showSend200();
                break;
            case 400:
                $this->showSend400();
                break;
            case 404:
                $this->showError404();
                break;
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 校验邮箱验证码或者是手机验证码
     * @return void
     */
    public function actionCheckCode()
    {
        if ($_SERVER['REQUEST_METHOD'] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        session_start();
        $passid = $_SESSION['findPassid'];
        if (!$passid) {
            $this->showError405();
        }

        $necessaryField = self::getNeedField(['mid', 'code', 'from', SignManager::$paramSignName,]);
        list($mid, $code, $from) = $this->getParamAndAutoDeny($necessaryField);
        $from = ($from == 'phone') ? 'phone' : 'email';
        $memberModel = loadModel("member");
        if ($from == 'phone') {
            $userinfo = $memberModel->read($passid);
            if ($userinfo['phone']) {
                $phone = $userinfo['phone'];
            } else {
                $this->showError404();
                exit;
            }
            $phoneAction = loadAction("phone");
            $checkcode = md5($phone . $code);
            $status = $phoneAction->checkVerifyCode($mid, $passid, $checkcode);
        } else {
            $userinfo = $memberModel->read($passid);
            if ($userinfo['email']) {
                $email = $userinfo['email'];
            } else {
                $this->showError404();
                exit;
            }
            $emailAction = loadAction("email");
            $checkcode = md5($email . $code);
            $status = $emailAction->checkVerifyCode($mid, $passid, $checkcode);
        }
        if ($status == 200) {
            $this->showCheck200();
        } else {
            $this->showCheck400();
        }
    }

    /**
     * 找回密码，校验邮箱或短信验证码后进行修改密码
     * @return void
     */
    public function actionFind()
    {
        if ($_SERVER['REQUEST_METHOD'] != "POST") {
            return;
        }
        loadAction('uploadToKafka')->setSwitch(true);//开启kafka上报
        session_start();
        $passid = $_SESSION['findPassid'];
        if (!$passid) {
            $this->showError405();
        }

        $necessaryField = self::getNeedField([
            'mid',
            'from',
            'verifyCode',
            'password',
            SignManager::$paramSignName,
        ]);
        list($mid, $from, $code, $password) = $this->getParamAndAutoDeny($necessaryField);
        $password = (new AesManager())->aes128cbcHexDecrypt($password);
        $from = ($from == 'phone') ? 'phone' : 'email';
        //   * error:密码最少6个字符
        if (strlen($password) < 6) {
            $this->codeResponseNew(300, "", '密码最少6个字符！', 1100001);
        }
        //   * error:密码最多16个字符
        if (strlen($password) > 16) {
            $this->codeResponseNew(300, "", '密码最多16个字符！', 1100002);
        }
        // 密码强度
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        $memberModel = loadModel("member");
        if ($from == 'phone') {
            $userinfo = $memberModel->read($passid);
            if ($userinfo['phone']) {
                $phone = $userinfo['phone'];
            } else {
                $this->showError404();
                exit;
            }
            $phoneAction = loadAction("phone");
            $checkcode = md5($phone . $code);
            $status = $phoneAction->checkVerifyCode($mid, $passid, $checkcode);
            $fromValue = $phone;
        } else {
            $userinfo = $memberModel->read($passid);
            if ($userinfo['email']) {
                $email = $userinfo['email'];
            } else {
                $this->showError404();
                exit;
            }
            $emailAction = loadAction("email");
            $checkcode = md5($email . $code);
            $status = $emailAction->checkVerifyCode($mid, $passid, $checkcode);
            $fromValue = $email;
        }
        if ($status == 200) {
            $retStatud = $memberModel->updatePassword($passid, $password, $pwdStrength, get_client_ip());
            $memberModel->logFind(get_client_ip(), $_SERVER['HTTP_USER_AGENT'], $passid, $from, $fromValue, $code);
            if ($from == "phone") {
                $timeKey = "PV:time:" . $mid . ":" . $passid;
                $codeKey = "PV:code:" . $mid . ":" . $passid;
            } else {
                $timeKey = "EV:time:" . $mid . ":" . $passid;
                $codeKey = "EV:code:" . $mid . ":" . $passid;
            }
            if ($retStatud) {

                //日志行为打点:用户信息修改; type:USER_INFO_MODIFY; sub_type:PASSWORD;
                $userinfo['passid'] = $passid;
                $objLogV2Action = loadAction('logV2');
                $objLogV2Action->report($mid, 'USER_INFO_MODIFY', 'PASSWORD', $userinfo, '', '');

                $redis = RedisEx::getInstance();
                $redis->del($timeKey);
                $redis->del($codeKey);
            }
            unset($_SESSION['findPassid']);
            session_destroy();
            $this->showSuccess200();
        } else {
            $this->showError402();
        }
    }

    /**
     * 返回用户手机号和邮箱
     *
     * @param array $userinfo 用户信息
     * @return void
     */
    private function showUserInfo($userinfo)
    {
        $this->codeResponseNew(
            200,
            [
                'phone' => $userinfo['phone'] ? $userinfo['phone'] : '',
                'email' => $userinfo['email'] ? $userinfo['email'] : '',
            ],
            '用户手机号和邮箱！',
            2000000
        );
    }

    /**
     * 成功
     *
     * @return void
     */
    private function showSuccess200()
    {
        $this->codeResponseNew(200, "", '密码重置成功！', 2000000);
    }

    /**
     * 发送验证码成功
     *
     * @return void
     */
    private function showSend200()
    {
        $this->codeResponseNew(200, "", '发送验证码成功！', 2000000);
    }

    /**
     * 验证成功
     *
     * @return void
     */
    private function showCheck200()
    {
        $this->codeResponseNew(200, "", '验证成功！', 2000000);
    }

    /**
     * 功  能：
     *
     * @return void
     */
    private function showCheck400()
    {
        $this->codeResponseNew(400, "", '验证失败！', 2100071);
    }

    /**
     * 发送验证码失败
     *
     * @return void
     */
    private function showSend400()
    {
        $this->codeResponseNew(400, "", '发送验证码失败！', 2100070);
    }

    /**
     * 显示303验证码错误
     *
     * @return void
     */
    private function showError303()
    {
        $this->codeResponseNew(303, "", '验证码错误，请刷新后重新输入', 2100064);
    }

    /**
     * 输入的账号不存在
     *
     * @return void
     */
    private function showError304()
    {
        $this->codeResponseNew(304, "", '此账号不存在！', 2100065);
    }

    /**
     * 邮箱重复需要再输入用户名
     *
     * @return void
     */
    private function showError300()
    {
        $this->codeResponseNew(300, "", '此邮箱未激活，请使用用户名找回！', 2100066);
    }

    /**
     * 功  能：
     *
     * @return void
     */
    private function showError402()
    {
        $this->codeResponseNew(402, "", '请输入正确的验证码！', 2100072);
    }

    /**
     * 未绑定邮箱或手机
     *
     * @return void
     */
    private function showError404()
    {
        $this->codeResponseNew(404, "", '未绑定邮箱或手机！', 2100069);
    }

    /**
     * session不存在
     *
     * @return void
     */
    private function showError405()
    {
        $this->codeResponseNew(405, "", '请求超时，请重新找回！', 2100067);
    }

    /**
     * session不存在
     *
     * @return void
     */
    private function showError500()
    {
        $this->codeResponseNew(500, "", '服务器忙！', 2000007);
    }
}
