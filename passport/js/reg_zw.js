function checkUser(n)
{
	username = $("username").value.trim();
	
	if (username.length < 2)
	{
		$("msg_username").style.display='';
		$("msg_username").className = 'pwtip';
		$("msg_username").innerHTML = '最少2个字符';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}

	if (username.length > 24)
	{
		$("msg_username").style.display='';
		$("msg_username").className = 'pwtip';
		$("msg_username").innerHTML = '请不要超过24个字符';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}
	
	if(/[^\u4E00-\u9FA5\w_@\.]/.test(username))
	{
		$("msg_username").style.display='';
		$("msg_username").className = 'pwtip';
		$("msg_username").innerHTML = '请输入汉字，字母，数字，或邮箱地址';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}

	startrequest('/passport/api/check.php','type=username&username='+username,1,function (response){
		if (response == 1)
		{
			err = 1;
		}
		else if (response == 2)
		{
			err = 2;
		}
		else
		{
			err = 0;
		}
	});
	if (err == 1)
	{
		$("msg_username").style.display='';
		$("msg_username").className = 'pwtip';
		$("msg_username").innerHTML = '此帐号已被注册，请<a href="javascript:void(0);" class="blue" onclick="login('+n+',\''+username+'\')">登录</a>或重新输入';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}
	else if (err == 2)
	{
		$("msg_username").style.display='';
		$("msg_username").className = 'pwtip';
		$("msg_username").innerHTML = '这个2345帐号不适合您，换一个吧';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}
	
	if (username != username.toLowerCase())
	{
		$("msg_username").style.display='';
		$("msg_username").className = 'pwtip';
		$("msg_username").innerHTML = '登录区分大小写，请牢记您的2345帐号';
		$("username").className = "ipt_01 ipt_02";
	}
	else
	{
		$("msg_username").className = '';
		$("msg_username").innerHTML = '<img src="images/check.jpg" style="margin: 8px 0 0;" />';
		$("username").className = "ipt_01";
	}

	if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username) && n == 1)
	{
		$("email").value = username;
	}
	
	return true;
}
function checkUserLength()
{
	$("msg_username").style.display='';
	if ($("username").value.cnSize() > 24)
	{
		$("msg_username").className = 'pwtip';
		$("msg_username").innerHTML = '请不要超过24个字符';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}
	$("msg_username").className = '';
	$("msg_username").innerHTML = '2~24个字符，汉字，字母，数字';
	$("username").className = "ipt_01";
	return true;
}
function checkPass()
{
	var pass = $("password").value;
	if (pass.length < 6)
	{
		$("msg_password").style.display='';
		$("msg_password").className = 'pwtip';
		$("msg_password").innerHTML = '最少6个字符';
		$("password").className = "ipt_01 ipt_02";
		$("safe").style.display = "none";
		return false;
	}
	if (pass.length > 16)
	{
		$("msg_password").style.display='';
		$("msg_password").className = 'pwtip';
		$("msg_password").innerHTML = '最多16个字符';
		$("password").className = "ipt_01 ipt_02";
		$("safe").style.display = "none";
		return false;
	}
	if (pass == $('username').value)
	{
		$("msg_password").style.display='';
		$("msg_password").className = 'pwtip';
		$("msg_password").innerHTML = '密码不能与2345帐号一致，请重新输入';
		$("password").className = "ipt_01 ipt_02";
		$("safe").style.display = "none";
		return false;
	}
	if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
	{
		$("msg_password").style.display='';
		$("msg_password").className = 'pwtip';
		$("msg_password").innerHTML = '您的密码过于简单，请重新输入';
		$("password").className = "ipt_01 ipt_02";
		$("safe").style.display = "none";
		return false;
	}
	
	$("msg_password").className = '';
	$("msg_password").innerHTML = '<img src="images/check.jpg" />';
	$("password").className = "ipt_01";
	return true;
}

function checkPassSame(pass)
{
	var first = pass.substring(0,1);
	var exp = new RegExp('^'+first+'+$');
	if(exp.test(pass))
	{
		return false;
	}
	
	if (first == 'a' || first == 'A')
	{
		f = pass.charCodeAt(0);
		for(i = 1; i < pass.length; i++)
		{
			tmp = pass.charCodeAt(i);
			if (tmp - f != i)
			{
				return true;
			}
		}
		return false;
	}
	return true;
}

function checkRepass()
{
	var pass = $("repassword").value;
	if (pass.length < 6)
	{
		$("msg_repassword").style.display='';
		$("msg_repassword").className = 'pwtip';
		$("msg_repassword").innerHTML = '最少6个字符';
		$("repassword").className = "ipt_01 ipt_02";
		return false;
	}
	if (pass.length > 16)
	{
		$("msg_repassword").style.display='';
		$("msg_repassword").className = 'pwtip';
		$("msg_repassword").innerHTML = '最多16个字符';
		$("repassword").className = "ipt_01 ipt_02";
		return false;
	}
	
	if ($("repassword").value != $("password").value)
	{
		$("msg_repassword").style.display='';
		$("msg_repassword").className = 'pwtip';
		$("msg_repassword").innerHTML = '两次输入密码不一致';
		$("repassword").className = "ipt_01 ipt_02";
		return false;
	}
	$("msg_repassword").className = '';
	$("msg_repassword").innerHTML = '<img src="images/check.jpg" />';
	$("repassword").className = "ipt_01";
	return true;
}
function checkEmail()
{
	email = $("email").value.trim();
	
	if (email == "" || email == '输入邮箱作为您的2345帐号')
	{
		$("msg_email").style.display='';
		$("msg_email").className = 'pwtip';
		$("msg_email").innerHTML = '请输入邮箱';
		$("email").className = "ipt_01 ipt_02";
		return false;
	}
	
	if (!/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(email))
	{
		$("msg_email").style.display='';
		$("msg_email").className = 'pwtip';
		$("msg_email").innerHTML = '您输入的邮箱格式不正确';
		$("email").className = "ipt_01 ipt_02";
		return false;
	}
	
	startrequest('api/check.php','type=email&email='+email,1,function(response){
		if (response == 1)
			err = 1;
		else
			err = 0;
	});
	if (err)
	{
		$("msg_email").style.display='';
		$("msg_email").className = 'pwtip';
		$("msg_email").innerHTML = '此邮箱已被注册，请换一个';
		$("email").className = "ipt_01 ipt_02";
		return false;
	}
	$("msg_email").className = '';
	$("msg_email").innerHTML = '<img src="images/check.jpg" />';
	$("email").className = "ipt_01";
	
	return true;
}
function checkValidate()
{
	val = $("validate").value.trim();
	if (val == '')
	{
		$("msg_validate").style.display='';
		$("msg_validate").className = 'pwtip';
		$("msg_validate").innerHTML = '请输入验证码';
		$("validate").className = 'ipt_01 ipt_02';
		return false;
	}
	
	startrequest('api/check.php','type=validate&val='+val,1,function(response){
		if (response == 0)
			err = 1;
		else
			err = 0;
	});
	if(err)
	{
		$("msg_validate").style.display='';
		$("msg_validate").className = 'pwtip';
		$("msg_validate").innerHTML = '您输入的验证码错误，请重新输入';
		$("validate").className = 'ipt_01 ipt_02';
		$('pic').src='/check_code.php?'+Math.random();
		return false;
	}
	$("msg_validate").className = '';
	$("msg_validate").innerHTML = '<img src="images/check.jpg" />';
	$("validate").className = "ipt_01";
	
	return true;
}
function checkValidate_new()
{
	val = $("validate").value.trim();
	if (val == '')
	{
		$("msg_validate").style.display='';
		$("msg_validate").className = 'pwtip';
		$("msg_validate").innerHTML = '请输入验证码';
		$("validate").className = 'ipt_01 ipt_02';
		return false;
	}
	
	startrequest('api/check.php','type=validate&val='+val,1,function(response){
		if (response == 0)
			err = 1;
		else
			err = 0;
	});
	if(err)
	{
		$("msg_validate").style.display='';
		$("msg_validate").className = 'pwtip';
		$("msg_validate").innerHTML = '您输入的验证码错误，请重新输入';
		$("validate").className = 'ipt_01 ipt_02';
		$('pic').src='/captcha/captcha.php?'+Math.random();
		return false;
	}
	$("msg_validate").className = '';
	$("msg_validate").innerHTML = '<img src="images/check.jpg" />';
	$("validate").className = "ipt_01";
	
	return true;
}
function checkValidate_zw()
{
	val = $("validate").value.trim();
	if (val == '')
	{
		$("msg_validate").style.display='';
		$("msg_validate").className = 'pwtip';
		$("msg_validate").innerHTML = '请输入验证码';
		$("validate").className = 'ipt_01 ipt_02';
		return false;
	}
	
	startrequest('api/check_zw.php','type=validate&val='+val,1,function(response){
		if (response == 0)
			err = 1;
		else
			err = 0;
	});
	if(err)
	{
		$("msg_validate").style.display='';
		$("msg_validate").className = 'pwtip';
		$("msg_validate").innerHTML = '您输入的验证码错误，请重新输入';
		$("validate").className = 'ipt_01 ipt_02';
		$('pic').src='/zwcaptcha.php?'+Math.random();
		return false;
	}
	$("msg_validate").className = '';
	$("msg_validate").innerHTML = '<img src="images/check.jpg" />';
	$("validate").className = "ipt_01";
	
	return true;
}

function checkAll(n)
{
	var log = "";
	if (!checkEmail())
	{
		$("email").focus();
		//log = "email error";
		//reg_log(log);
		return false;
	}
	if (!checkUser(2))
	{
		$("username").focus();
		//log = "username error";
		//reg_log(log);
		return false;
	}
	if (!checkPass())
	{
		$("password").focus();
		//log = "password error";
		//reg_log(log);
		return false;
	}
	if (!checkRepass())
	{
		$("repassword").focus();
		//log = "repassword error";
		//reg_log(log);
		return false;
	}
	if (!checkValidate_zw())
	{
		$("validate").focus();
		//log = "verification code error";
		//reg_log(log);
		return false;
	}
	if (!$("agree").checked)
	{
		alert("请同意2345服务协议");
		//log = "agreement error";
		//reg_log(log);
		return false;
	}
	
	//log = "registration is complete";
	//reg_log(log);
	return true;
}

function changeValidate()
{
	$("pic").src = "../../check_code.php?"+Math.random();
}

function login(n, username)
{
	if (n == 2)
	{
		window.location.href="http://login.duote.com/login.php";
	}
	else if (n == 3)
	{
		window.location.href="http://bbs.haozip.com/login.php";
	}
	else if (n == 4)
	{
		window.location.href="http://bbs.shanhu99.com/logging.php?action=login";
	}
	else if (n == 5)
	{
		window.location.href="http://ie.2345.com/bbs/logging.php?action=login";
	}
	else
	{
		if (username)
		{
			window.location.href="http://login.2345.com/login.php?username="+username;
		}
		else
		{
			window.location.href="http://login.2345.com/login.php";
		}
	}
}

function safe()
{
	var pass = $("password").value;
	if (pass.length < 6)
	{
		var score = 0;
	}
	else if (pass == $('username').value)
	{
		var score = 0;
	}
	else if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
	{
		var score = 0;
	}
	else
	{
		var score = passwordGrade(pass);
	}
//	$('safe').innerHTML = score;
//	return;
	if (score <= 10)
	{
		$('w1').style.display = '';
		$('w2').style.display = 'none';
		$('w3').style.display = 'none';
		$('w4').style.display = 'none';
		$('pwd_strength').value = 1;
	}
	else if (score >= 11 && score <= 20)
	{
		$('w1').style.display = 'none';
		$('w2').style.display = '';
		$('w3').style.display = 'none';
		$('w4').style.display = 'none';
		$('pwd_strength').value = 2;
	}
	else if (score >= 21 && score <= 30)
	{
		$('w1').style.display = 'none';
		$('w2').style.display = 'none';
		$('w3').style.display = '';
		$('w4').style.display = 'none';
		$('pwd_strength').value = 3;
	}
	else
	{
		$('w1').style.display = 'none';
		$('w2').style.display = 'none';
		$('w3').style.display = 'none';
		$('w4').style.display = '';
		$('pwd_strength').value = 4;
	}
}

function passwordGrade(pwd) {
    var score = 0;
    var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
    var repeatCount = 0;
    var prevChar = '';

    //check length
    var len = pwd.length;
    score += len > 18 ? 18 : len;

    //check type
    for (var i = 0, num = regexArr.length; i < num; i++) { if (eval('/' + regexArr[i] + '/').test(pwd)) score += 4; }

    //bonus point
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2) score += 2;
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5) score += 2;
    }

    //deduction
    for (var i = 0, num = pwd.length; i < num; i++) {
        if (pwd.charAt(i) == prevChar) repeatCount++;
        else prevChar = pwd.charAt(i);
    }
    score -= repeatCount * 1;

    return score;
}


//function reg_log(log) {
	//startrequest('reg_log.php','log='+log+'&d='+new Date(),1,function (response){});
//}

function checkUserOther(n)
{
	username = $("username").value.trim();

    if (!/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username))
	{
		$("msg_email").style.display='';
		$("msg_email").innerHTML = '请输入正确的邮箱地址';
		$("email").className = "ipt_01 ipt_02";
		return false;
	}	

	if (username.length > 36)
	{
		$("msg_username").style.display='';
		$("msg_username").innerHTML = '请不要超过36个字符';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}
	
	startrequest('/passport/api/check.php','type=username&username='+username,1,function (response){
		if (response == 1)
		{
			err = 1;
		}
		else if (response == 2)
		{
			err = 2;
		}
		else
		{
			err = 0;
		}
	});
	if (err == 1)
	{
		$("msg_username").style.display='';
		$("msg_username").innerHTML = '此帐号已被注册，请<a href="javascript:void(0);" class="blue" onclick="login('+n+',\''+username+'\')">登录</a>或重新输入';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}
	else if (err == 2)
	{
		$("msg_username").style.display='';
		$("msg_username").innerHTML = '这个2345帐号不适合您，换一个吧';
		$("username").className = "ipt_01 ipt_02";
		return false;
	}
	
	if (username != username.toLowerCase())
	{
		$("msg_username").style.display='';
		$("msg_username").className = 'pwtip';
		$("msg_username").innerHTML = '登录区分大小写，请牢记您的2345帐号';
		$("username").className = "ipt_01 ipt_02";
	}
	else
	{
		$("msg_username").className = '';
		$("msg_username").innerHTML = '';
		$("username").className = "ipt_01";
	}
        	if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username))
	{
		$("email").value = username;
	}
              
	return true;
}