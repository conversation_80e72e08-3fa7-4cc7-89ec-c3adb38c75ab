document.write('<script type="text/javascript" src="//passport.2345.com/js/client/base.js"><\/script>');document.write('<script type="text/javascript" src="//passport.2345.com/js/client/jquery.md5.js"><\/script>');if(console===undefined){var console={log:function(a){},debug:function(a){}}}function responseszgyym(e,b,c,a,f){login.szgyym(e,b,c,a,f)}function isWeiXinBrowser(){var a=window.navigator.userAgent.toLowerCase();return a.match(/MicroMessenger/i)=="micromessenger"}function isH5GameAppBrowser(){var a=window.navigator.userAgent.toLowerCase();return a.match(/2345h5game_android_app/i)=="2345h5game_android_app"}function isMobileBrowser(){var b=window.navigator.userAgent.toLowerCase();var e=b.match(/android/i)=="android";var a=window.navigator.userAgent;var c=!!a.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);return e||c}var login={proxyUrl:"",errorLog:"",isShowCode:false,isGame:false,isMicro:false,isQqShow:true,isWeixinShow:false,commitParam:new Object(),locationUrl:"",wxAppid:"wx9858d355cbe24ab4",isAjax:false,isThirdAjax:false,jqueryObj:$,oldDomain:document.domain,unf:"username",showCodeFirst:false,logining:false,endCallLfaction:"",init:function(a){this.loadFiles(a);login.jqueryObj(".g-login .changeCode,.codeImg").click(function(){login.showCodeImg()});login.jqueryObj(".g-login .g-inputTxt input").focus(function(){login.jqueryObj(this).parent().addClass("g-inputTxt-focus");login.jqueryObj(this).siblings(".sDes").css("display","none")});login.jqueryObj(".g-login .g-inputTxt input").blur(function(){login.jqueryObj(this).parent().removeClass("g-inputTxt-focus g-inputTxt-error");if(login.jqueryObj(this).val()==""){login.jqueryObj(this).siblings(".sDes").css("display","block")}});if(!login.isPlaceholder()){login.jqueryObj(function(){setTimeout(function(){login.jqueryObj(".g-login .g-inputTxt .judgmentValue").each(function(){if($(this).val()!=""){$(this).parent().find(".sDes").hide()}})},200)})}login.jqueryObj(".g-login .g-inputTxt input").keypress(function(g){var f=g.which;if(f==13){if(login.isMicro&&login.jqueryObj("#wd_code").is(":hidden")&&login.isShowCode==true){if(login.checkUserAccount()&&login.checkUserPass()){login.checkLoginStatusBack({"display":true})}}else{login.submitForm()}}});var b=this.exeFuncPopupName;login.jqueryObj(".g-login .loginClose").click(function(){if(typeof b!="undefined"){b()}login.jqueryObj("#jsLoginDiv").hide()});if(typeof this.locationUrl=="undefined"){this.locationUrl="http://"+document.domain}var e="&callback=";switch(typeof(this.setCookieUrl)){case"string":e+=this.setCookieUrl;break;case"object":for(var c in this.setCookieUrl){e+=this.setCookieUrl[c]+","}break;default:e=""}login.jqueryObj(".g-login .thirdPartyLogin").click(function(){var f=login.getThirdLocationUrl("qq");var i=login.getThirdLocationUrl("weixin");var g=login.jqueryObj(this).attr("type");var h="";switch(g){case"qq":h="//login.2345.com/qq?forward="+f+e;break;case"weixin":if(isWeiXinBrowser()){h="//passport.2345.com/weixin/mp/"+login.wxAppid+"?forward="+i+e}else{if(isH5GameAppBrowser()){login.launchWeixinClient(login.wxAppid,i+e);return}else{h="//passport.2345.com/weixin?forward="+i+e}}break;default:h="";break}if(h==""){return false}if(login.isThirdAjax){login.thirdExeName(h)}else{login.jqueryObj(this).attr("href",h);window.location=h}});login.lFaction("","")},lFaction:function(loginError,data){var lei=loginError,bd=data,isInit=loginError=="loginError"?0:1,rvn=Math.floor(Math.random()*1000),protocal=document.location.protocol;$.ajax({url:protocal+"//passport.2345.com/webapi/Login/faction?init="+isInit+"&v="+rvn,data:"",async:false,dataType:"jsonp",success:function(lfData){var JlfData=eval("("+lfData+")");var isShowedCaptcha=false;if(isInit>0&&JlfData.isShow){if(login.isMicro){login.isShowCode=true;}else{isShowedCaptcha=true}}responseszgyym(JlfData.status,JlfData.unf,JlfData.token,isShowedCaptcha,isInit);if(lei!=""&&bd){if(!isShowedCaptcha&&bd.display){isShowedCaptcha=true}login.refreshCodeImg(bd)}if(!isShowedCaptcha){var requireCaptcha=login.jqueryObj("input[name=requiredCaptcha]").val();if(requireCaptcha){login.showCodeImg()}}setTimeout(login.removeSubmitDisabled,500)},error:function(jqXHR,textStatus,errorThrown){login.removeSubmitDisabled();console.log(jqXHR.status)},timeout:3000})},getThirdLocationUrl:function(b){var a="";switch(typeof(this.thirdLocationUrl)){case"string":a=this.thirdLocationUrl;break;case"object":if(b==="qq"){a=(typeof this.thirdLocationUrl["qq"])!=="undefined"?this.thirdLocationUrl["qq"]:this.locationUrl}else{if(b==="weixin"){a=(typeof this.thirdLocationUrl["weixin"])!=="undefined"?this.thirdLocationUrl["weixin"]:this.locationUrl}}break;default:a=this.locationUrl}return a},launchWeixinClient:function(b,a){if(typeof(WXCallback)=="object"){$.ajax({url:"//passport.2345.com/weixin/h5App/"+b,data:"forward="+a,async:false,dataType:"jsonp",success:function(c){WXCallback.launchWX(c)},error:function(c,f,e){console.log(c.status)},timeout:3000})}},setWeixinQrImg:function(c,b,a){!function(f,e){function g(i){var k,m=e.createElement("iframe"),l="https://open.weixin.qq.com/connect/qrconnect?appid="+i.appid+"&scope="+i.scope+"&redirect_uri="+i.redirect_uri+"&state="+i.state+"&login_type=jssdk";l+=i.style?"&style="+i.style:"";l+=i.href?"&href="+i.href:"";m.src=l;m.frameBorder="0";m.allowTransparency="true";m.scrolling="no";k=e.getElementById(i.id);var j=k.getAttribute("data-width"),h=k.getAttribute("data-height");m.width=j?j:"150px",m.height=h?h:"140px";k.innerHTML="",k.appendChild(m)}f.WxLogin=g}(window,document);if(!b){b=""}$.ajax({url:"//passport.2345.com/weixin/jsLoginQr/",data:"forward="+this.getThirdLocationUrl("weixin"),async:false,dataType:"jsonp",success:function(e){if(e){var f=new WxLogin({id:c,appid:e.data.appid,scope:"snsapi_login",redirect_uri:e.data.redirectUri,state:e.data.state,style:a,href:b})}},error:function(e,g,f){console.log(e.status)},timeout:3000})},loadFiles:function(a){if(typeof(a)!="object"||a==null){var a={"isPop":false}}if(typeof(a["isPop"])=="undefined"?false:!a["isPop"]){login.jqueryObj(".g-login .loginClose").hide()}},checkLoginStatus:function(a){if(a.display){login.isShowCode=a.display;if(!login.isMicro){login.showCodeImg()}}},setRequiredCaptcha:function(a){$("input[name=requiredCaptcha]").val(a);login.isShowCode=true;login.showCaptchaInput()},checkLoginStatusBack:function(a){if(a.display&&login.jqueryObj("#wd_code").is(":hidden")){login.isShowCode=a.display;login.showCodeImg();login.hideErrorTips("#g-error-tips");login.jqueryObj("#wd_name").hide();login.jqueryObj("#wd_code").show()}else{login.hideCodeImg();login.hideErrorTips("#g-error-tips");login.jqueryObj("#wd_name").show();login.jqueryObj("#wd_code").hide()}},setWxAppid:function(a){this.wxAppid=a},setLocation:function(a){this.locationUrl=a},setGameProject:function(){this.isGame=true},setMicro:function(a){this.isMicro=a},setQqShow:function(a){this.isQqShow=a},setWeixinShow:function(a){this.isWeixinShow=a},setThirdLocation:function(a){this.thirdLocationUrl=a},setThirdExeFunc:function(a){this.thirdExeName=a},setError:function(a){this.errorLog=a},getLastError:function(){return this.errorLog},exeFunc:function(a){this.exeFuncName=a},exePopup:function(a){this.exeFuncPopupName=a},setCommitParam:function(a){this.commitParam=a},getOtherParamHtml:function(){var b="";for(var a in this.commitParam){b+='<input type="hidden" value="'+this.commitParam[a]+'" name="'+a+'">'}return b},setLoginConfig:function(){},setDomain:function(a){this.getDomainName=a},setProxy:function(a){this.proxyUrl=a},setCookieUrl:function(a){this.setCookieUrl=a},isHtml5:function(){if(window.applicationCache){return true}else{return false}},getLoginHtml:function(){var a=this.showCodeFirst?"block":"none";return'<div class="g-login" id="jsLoginDiv">'+'<div class="g-login-th">'+'<span class="sMark g-left">2345\u5e10\u53f7\u767b\u5f55</span>'+'<a href="javascript:void(0);" class="g-login-closeBtn g-right loginClose"></a>'+"</div>"+'<div class="g-login-tb clearfix">'+' <form id="myFormLogin">'+this.getOtherParamHtml()+'<input type="hidden" name="cmd" value="login">'+'<input type="hidden"  name="currtime"/>'+'<input type="hidden"  name="flToken"/>'+'<div class="g-login-form">'+'<div class="g-inputTxt">'+'<i class="g-icon-name"></i>'+'<span class="sDes">\u624B\u673A/\u5DF2\u9A8C\u8BC1\u90AE\u7BB1/\u7528\u6237\u540D</span>'+'<input class="judgmentValue" type="text" name="username" value="">'+"</div>"+'<div class="g-inputTxt">'+'<i class="g-icon-password"></i>'+'<span class="sDes">\u5bc6\u7801</span>'+'<input type="hidden" value="" name="password">'+'<input type="hidden" value="" name="requiredCaptcha">'+'<input class="judgmentValue" type="password" value="" id="expassword">'+"</div>"+'<div class="g-login-code" style="display:'+a+'">'+'<div class="g-inputTxt">'+'<span class="sDes">\u9a8c\u8bc1\u7801</span>'+'<input type="text" value="" name="check_code">'+"</div>"+'<div class="codePic"><span class="sPic g-left">'+'<img class="codeImg" width="88" height="40">'+'</span><a href="#"  class="g-right changeCode">\u6362\u4e00\u6362</a></div>'+"</div>"+'<div class="g-error" id="g-error-tips" style="display:none"></div>'+'<div class="g-txt"><span class="g-sCheck g-left"><input type="checkbox" checked class="g-checkbox" name="autoLogin">\u4e0b\u6b21\u81ea\u52a8\u767b\u5f55</span><a href="//passport.2345.com/find?type=password" target="_blank" class="g-right forgetPassClass">\u627E\u56DE\u5BC6\u7801</a></div>'+'<input  type="button" class="g-btn btn-submit" value="\u767b&nbsp;&nbsp;&nbsp;&nbsp;\u5f55">'+(login.isMicro?"":'<div class="g-txt g-txt-registration"><a href="//passport.2345.com/reg.php?forward='+this.locationUrl+'" target="_blank" class="g-txt-login-btn"><em>\u7acb\u523b</em>\u6ce8\u518c</a></div>')+'<div class="g-other-login">'+'<span class="sTit">\u4f60\u4e5f\u53ef\u4ee5\u7528\u4ee5\u4e0b\u65b9\u5f0f\u767b\u5f55</span>'+'<div class="otherStyle">'+(login.isQqShow?'<a href="javascript:void(0);" class="blueBtn thirdPartyLogin" type="qq"><i class="g-icon-qq"></i><em class="qqTxthide">QQ登录</em></a>':"")+(login.isWeixinShow?'<a href="javascript:void(0);" class="blueBtn thirdPartyLogin login-weixin" type="weixinJs"><i class="g-icon-weixin"></i><em class="qqTxthide">微信登录</em></a>':"")+"</div>"+(isWeiXinBrowser()||isH5GameAppBrowser()?'<div class="otherStyle">'+'<a href="javascript:void(0);" class="blueBtn thirdPartyLogin" type="weixin"><i class="g-icon-weixin"></i><em class="qqTxthide">微信登录</em></a>'+"</div>":"")+"</div>"+"</div>"+"</form>"+"</div>"+"</div>"},showCodeImg:function(){d=new Date();login.jqueryObj(".g-login .codeImg").prop("src","//passport.2345.com/captcha?mid=SRF&d="+d.getTime());login.jqueryObj(".g-login .g-login-code").show();if(this.isGame==true){this.showCodeImgForGameInit()}return},showCodeImgForGameInit:function(){login.jqueryObj(".g-login .g-login-form").addClass("showcode");if(this.isMicro==true){login.jqueryObj(".g-login .g-login-code").prevAll(".g-inputTxt").hide();login.jqueryObj(".g-login .g-txt").hide()}},showCaptchaInput:function(){login.jqueryObj(".g-login .g-login-code").show();login.jqueryObj(".g-login .g-login-form").addClass("showcode")},hideCodeImg:function(){d=new Date();login.jqueryObj(".g-login .g-login-code").hide();if(this.isGame==true){login.jqueryObj(".g-login .g-login-form").removeClass("showcode");if(this.isMicro==true){login.jqueryObj(".g-login .g-login-code").prevAll(".g-inputTxt").show();login.jqueryObj(".g-login .g-txt").show()}}return},showErrorTips:function(a,c){var b=login.jqueryObj(a);b.html(c);b.show()},hideErrorTips:function(a){var b=login.jqueryObj(a);b.html("");b.hide()},checkUserAccount:function(){var b=false;var a='.g-login .g-inputTxt input[name="'+login.unf+'"]';var c=login.jqueryObj(a);var e=c.val();if(e==""){login.showErrorTips("#g-error-tips","\u7528\u6237\u540d\u4e0d\u80fd\u4e3a\u7a7a");c.parent().addClass("g-inputTxt-error")}else{if(e.length<2){login.showErrorTips("#g-error-tips","\u6700\u5c112\u4e2a\u5b57\u7b26!");c.parent().addClass("g-inputTxt-error")}else{if(e.replace(/[^\x00-\xff]/g,"**").length>24){login.showErrorTips("#g-error-tips","\u8bf7\u4e0d\u8981\u8d85\u8fc724\u4e2a\u5b57\u7b26!");c.parent().addClass("g-inputTxt-error")}else{b=true}}}return b},checkUserPass:function(){var a=false;var b=login.jqueryObj("#expassword").val();if(b==""){login.showErrorTips("#g-error-tips","\u8bf7\u5148\u8f93\u5165\u5bc6\u7801!");login.jqueryObj("#expassword").parent().addClass("g-inputTxt-error")}else{b=MD5(b);var c=b.split("").reverse().join("");login.jqueryObj('.g-login input[name="password"]').val(c);a=true}return a},showLogin:function(a,b){if(this.proxyUrl==""){login.showErrorTips("body","\u4ee3\u7406\u9875\u9762\u4e0d\u80fd\u4e3a\u7a7a");return false}login.jqueryObj(a).append(this.getLoginHtml());this.init(b);if(this.isHtml5()){this.createProxy();window.addEventListener("message",function(c){login.response(c.data)},false)}login.jqueryObj(".g-login .btn-submit").click(function(){if(login.isMicro&&login.jqueryObj("#wd_code").is(":hidden")&&login.isShowCode==true){if(login.checkUserAccount()&&login.checkUserPass()){login.checkLoginStatusBack({"display":true})}}else{login.submitForm()}})},submitForm:function(){if(login.jqueryObj.browser.msie&&(login.jqueryObj.browser.version=="6.0")&&!login.jqueryObj.support.style){login.jqueryObj(".g-login a.btn-submit").attr("href","###")}if(!login.checkUserAccount()){return false}if(!login.checkUserPass()){return false}if(login.isShowCode==true){var a=login.jqueryObj('.g-login input[name="check_code"]').val();if(a==""){login.showErrorTips("#g-error-tips","\u8BF7\u8F93\u5165\u9A8C\u8BC1\u7801");return false}}login.jqueryObj('.g-login input[name="currtime"]').val(parseInt(new Date().getTime()/1000));var h=window.location.host;var f=window.location;var b=login.jqueryObj('input[name="flToken"]').val();login.jqueryObj('input[name="flToken"]').attr("value",MD5(b));var e=login.jqueryObj("#myFormLogin").serialize()+"&domain="+h+"&currentUrl="+f;window.submitData=e;if(login.logining){return}login.logining=true;login.jqueryObj(".btn-submit").attr("disabled","true");if(login.isHtml5()){var g=document.getElementById("loginProxy").contentWindow;var c=document.location.protocol;g.postMessage(e,c+"//passport.2345.com")}else{login.createProxy()}},createProxy:function(){var b=new Base64();if(this.isHtml5()){login.jqueryObj("body").append('<iframe id="loginProxy" style="display:none" src="//passport.2345.com/proxy.html#'+b.encode(this.proxyUrl)+'"></iframe>')}else{var a=b.encode(this.proxyUrl);var c="";if(this.oldDomain!=document.domain){this.getDomainName=document.domain}if(typeof this.getDomainName!="undefined"){a=b.fencode(this.proxyUrl+"?domain="+this.getDomainName);c="?domain="+this.getDomainName}login.jqueryObj("body").append('<iframe id="loginProxy" style="display:none" src="'+this.proxyUrl+c+"#"+a+'"></iframe>')}},szgyym:function(f,b,e,a,g){if(f=="P10086"){login.unf=b;var c=login.jqueryObj(".judgmentValue");login.jqueryObj(c[0]).attr("name",login.unf);login.jqueryObj('input[name="flToken"]').attr("value",e);if(a){login.isShowCode=true;login.showCodeImg()}}},removeSubmitDisabled:function(){login.logining=false;login.jqueryObj(".btn-submit").removeAttr("disabled")},response:function(retdata){data=eval("("+retdata+")");try{data.status}catch(error){login.removeSubmitDisabled();retdata;return false}if(data.status!="P00001"){login.lFaction("loginError",data)}else{if(typeof(data.loadPage)=="undefined"){data.loadPage=new Array()}if(document.domain.slice(-8)!="2345.com"){var cookieI=eval("("+data.setCookie+")");var param="?"+cookieI.I;switch(typeof(this.setCookieUrl)){case"string":data.loadPage[data.loadPage.length]=this.setCookieUrl+param;break;case"object":for(var surl in this.setCookieUrl){data.loadPage[data.loadPage.length]=this.setCookieUrl[surl]+param}break;default:data.loadPage[data.loadPage.length]="/user/SetLoginCallBack?"+param}}if(data.loadPage.length==0){login.endRunFunc(data)}else{var len=data.loadPage.length;var j=0;for(var i in data.loadPage){login.jqueryObj.getScript(data.loadPage[i],function(){++j;if(j==len){login.endRunFunc(data)}})}}}},refreshCodeImg:function(a){if(typeof(a.location)!="undefined"){window.location=a.location;return false}if(a.display==false){login.isShowCode=false}else{if(a.display==true||login.isShowCode){login.isShowCode=true;if(!login.isMicro){login.showCodeImg()}}}if(login.isMicro){if(a.status=="P00004"){login.showCodeImg()}else{login.checkLoginStatusBack({"display":false})}}login.jqueryObj('.g-login input[name="check_code"]').val("");if(!login.isMicro||a.status!="P00004"){login.jqueryObj('.g-login input[name="password"]').val("");login.jqueryObj("#expassword").val("")}login.showErrorTips("#g-error-tips",a.msg)},endRunFunc:function(a){setTimeout(login.removeSubmitDisabled,500);if(typeof this.exeFuncName!="undefined"){this.exeFuncName()}if(!this.isAjax){login.locationPage(a)}},locationPage:function(a){if(typeof(a.location)!="undefined"){window.location=a.location}else{if(this.locationUrl!=""){window.location=this.locationUrl}else{window.location.reload()}}},chgWeiboText:function(a){},isPlaceholder:function(){var a=login.jqueryObj(".g-login input").get(0);return"placeholder" in a}};