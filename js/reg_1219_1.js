function checkUser(n)
{
    var username = $("#username").val().trim();
    var isvalid = true;
    var msg_username = '';

    if (username.length < 2)
    {
        isvalid = false;
        msg_username = '最少2个字符';
    }
    if (username.replace(/[^\x00-\xff]/g, "**").length > 24)
    {
        isvalid = false;
        msg_username = '请不要超过24个字符';
    }

    if (/[^\u4E00-\u9FA5\w_@\.\-]/.test(username))
    {
        isvalid = false;
        msg_username = '请输入汉字，字母，数字';
    }

    if (!isvalid)
    {
        $("#msg_username").addClass('form-tips form-tips-error').html(msg_username).show();
        $("#username").addClass("ipt-txt-error").next('.i-retok').hide();
        return false;
    }

    var type = 'username';
    var typeName = '用户名';
    if (/^1[0123456789]\d{9}$/.test(username)) {
        type = 'phone';
        typeName = '手机号码';
    }
    if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username)) {
        type = 'email';
        typeName = '邮箱';
    }

    jQuery.ajax({
        url: "/api/check/jsonp",
        data: 'type=' + type + '&value=' + username,
        async: false,
        dataType: 'jsonp',
        jsonp: 'callback',
        success: function(response) {
            err = response;
        },
        timeout: 3000
    });
    if (type == 'username')
    {
        if (err == 1)
        {
            isvalid = false;
            msg_username = '这个' + typeName + '已经被使用，换一个吧';

        }
        else if (err == 2)
        {
            isvalid = false;
            msg_username = '这个2345帐号不适合您，换一个吧';

        }
    }
    else
    {
        if (err != 0)
        {
            isvalid = false;
            msg_username = '这个' + typeName + '已经被使用，换一个吧';
        }

    }

    if (isvalid === true)
    {
        if (username !== username.toLowerCase())
        {
            msg_username = '登录区分大小写，请牢记您的帐号';
        }
        else
        {
            msg_username = '';
        }
        $("#msg_username").removeClass('form-tips-error').html('').hide();
        $("#username").removeClass("ipt-txt-error").next('.i-retok').show();

        if (/^1[0123456789]\d{9}$/.test(username)) {
            $("#yzm_pic").hide();
            $("#yzm_phone").show();
            $('#reg_type').val('phone');
        } else if (/^[_\.0-9a-zA-Z-]+@([0-9a-zA-Z][0-9a-zA-Z-]+\.)+[a-zA-Z]{2,4}$/.test(username)) {
            $("#yzm_pic").show();
            $("#yzm_phone").hide();
            $('#reg_type').val('email');
        } else {
            $("#yzm_pic").show();
            $("#yzm_phone").hide();
            $('#reg_type').val('username');
        }

        return true;
    }
    else
    {
        $("#yzm_phone").hide();
        $("#yzm_pic").hide();
        $("#msg_username").addClass('form-tips form-tips-error').html(msg_username).show();
        $("#username").addClass("ipt-txt-error").next('.i-retok').hide();
        return false;
    }

}
function checkUserLength()
{
    if ($("#username").val().cnSize() > 24)
    {
        $("#msg_username").addClass('form-tips form-tips-error').html('请不要超过24个字符').show();
        $("#username").addClass("ipt-txt-error");
        return false;
    }
    $("#msg_username").removeClass('form-tips-error').html('2~24个字符，汉字，字母，数字').show();
    $("#username").removeClass("ipt-txt-error");
    return true;
}

function safe(eName)
{
    var isvalid = true;
    var msg_pwd = '';

    $("#pwd_strong").show();
    var pass = $("#password").val();
    if (pass.length > 16)
    {
        msg_pwd = '最多16个字符';
        isvalid = false;
    }

    var score = getPwdScore(pass);
    if (score <= 10)
    {
        $('.form-pwd-tips').attr('class', 'form-pwd-tips').addClass('form-pwd-tips-1').find('i').each(function(index, ele) {
            if (index <= 0) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('弱');
        $("#pwd_strength").val('1');

        pass == '' ? msg_pwd = '密码不能为空' : msg_pwd = '密码强度不能为弱';
        isvalid = false;
    }
    else if (score >= 11 && score <= 20)
    {
        $('.form-pwd-tips').attr('class', 'form-pwd-tips').addClass('form-pwd-tips-2').find('i').each(function(index, ele) {
            if (index <= 1) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('中');
        $("#pwd_strength").val('2');
        isvalid = true;
    }
    else if (score >= 21 && score <= 30)
    {
        $('.form-pwd-tips').attr('class', 'form-pwd-tips').addClass('form-pwd-tips-3').find('i').each(function(index, ele) {
            if (index <= 2) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('强');
        $("#pwd_strength").val('3');
        isvalid = true;
    }
    else
    {
        $('.form-pwd-tips').attr('class', 'form-pwd-tips').addClass('form-pwd-tips-3').find('i').each(function(index, ele) {
            if (index <= 3) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('很强');
        $("#pwd_strength").val('4');
        isvalid = true;
    }

    if (eName === 'keyup')
    {
        $('#msg_pwd').hide().removeClass('form-tips-error').text('');
        $('#pwd_strong').show();
        $("#password").removeClass("ipt-txt-error");

    }

    if (eName === 'blur')
    {
        if (isvalid === true)
        {
            $('#msg_pwd').hide().removeClass('form-tips-error').text('');
            $('#pwd_strong').show();
            $("#password").removeClass("ipt-txt-error").next('.i-retok').show();

            if ($("#password").val() != $("#repassword").val() && $("#repassword").val() != '')
            {
                $("#repassword").addClass("ipt-txt-error").next('.i-retok').hide();
                $("#msg_repassword").addClass('form-tips form-tips-error').html('两次输入密码不一致').show();
            }

        }
        else
        {
            $('#msg_pwd').show().addClass('form-tips-error').html(msg_pwd);
            $('#pwd_strong').hide();
            $("#password").addClass("ipt-txt-error").next('.i-retok').hide();
            $("#repassword").next('.i-retok').hide();

            if (pass == '')
            {
                $("#repassword").removeClass("ipt-txt-error").val('').next('.i-retok').hide();
                $("#msg_repassword").removeClass('form-tips-error').html('').hide();

            }
        }


    }

    return isvalid;


}
function checkRepass(from)
{

    if (from == 'repassword')
    {
        if ($("#password").val() == '')
        {
            return;
        }

        if ($("#repassword").val() != $("#password").val())
        {
            $("#msg_repassword").addClass('form-tips form-tips-error').html('两次输入密码不一致').show();
            $("#repassword").addClass("ipt-txt-error").next('.i-retok').hide();
            return false;
        }
        else
        {
            $("#msg_repassword").removeClass('form-tips-error').html('').hide();
            $("#repassword").removeClass("ipt-txt-error").next('.i-retok').show();
            return true;
        }

    }

    if (from == 'password')
    {

    }

}


function checkValidate_sj()
{
    val = $("#validate").val();
    if (val == '')
    {
        $("#validate").addClass("ipt-txt-error").next('.i-retok').hide();
        $("#msg_validate").addClass('lg_form-tips form-tips-error').html('请输入正确的计算结果').show();
        return false;
    }
    jQuery.ajax({
        url: "/api/check",
        data: 'type=validate&val=' + val,
        type: 'POST',
        async: false,
        success: function(response) {
            err = response;
        },
        timeout: 3000
    });
    if (err == 1)
    {
        $("#msg_validate").removeClass('form-tips-error').html('').hide();
        $("#validate").removeClass("ipt-txt-error").next('.i-retok').show();
        return true;
    } else {
        $("#validate").addClass("ipt-txt-error").next('.i-retok').hide();
        $("#msg_validate").addClass('form-tips form-tips-error').html('请输入正确的计算结果').show();
        $("#pic").attr('src', '/randCaptcha.php?J' + Math.random());
        return false;
    }

}
function checkValidatePhone_sj()
{
    var code = $("#validate_phone").val().trim();
    if (code === '')
    {
        $("#msg_validate_phone").addClass('lg_form-tips form-tips-error').html('请输入手机验证码').show();
        return false;
    }
    jQuery.ajax({
        url: "/reg/checkPhoneCode",
        data: 'code=' + code + '&phone=' + $("#username").val().trim(),
        async: false,
        type: 'POST',
        success: function(response) {
            response === '200.0' ? err = 0 : err = 1;
        },
        timeout: 3000
    });
    if (err)
    {
        $("#msg_validate_phone").addClass('form-tips-error').html('您输入的验证码错误，请重新输入').show();
        $("#validate_phone").addClass("ipt-txt-error").next('.i-retok').hide();
        return false;
    }
    else
    {
        $("#msg_validate_phone").removeClass('form-tips-error').html('').hide();
        $("#validate_phone").removeClass("ipt-txt-error").next('.i-retok').show();
        return true;
    }

}


function checkAll()
{
    if (!checkUser(1))
    {
        return false;
    }
    if (!safe('blur'))
    {
        return false;
    }
    if (!checkRepass('repassword'))
    {
        return false;
    }

    if ($('#reg_type').val() !== 'phone' && !checkValidate_sj())
    {
        return false;
    }

    if ($('#reg_type').val() === 'phone' && !checkValidatePhone_sj())
    {
        return false;
    }

    if (!$("#agree").is(':checked'))
    {
        alert("请同意2345服务协议");
        return false;
    }

    $.ajax({
        url: "/reg.php",
        data: $("#myForm").serialize(),
        type: 'POST',
        async: false,
        success: function(response) {
            res = eval('(' + response + ')');
        },
        timeout: 3000
    });
    if (typeof res.msg !== "undefined" && res.msg !== '')
    {
        alert(res.msg);//提示错误
        return false;
    }
    else
    {
        //$("#form-tips-error").html('').hide();
    }

    if (typeof res.forwardPage === "undefined" || res.forwardPage === '')
    {
        res.forwardPage = '/reg.php';
    }

    if (typeof res.loadPage !== "undefined" && res.loadPage !== '')
    {	
		setTimeout(function(){
			window.location = res.forwardPage;
		},1000);
		$.getScript(res.loadPage,function(){
			window.location = res.forwardPage;
		});
    }
    else
    {
        window.location = res.forwardPage;
    }
    return false;


}


/*****************   until functoins   ****************/
function checkPassSame(pass)
{
    var first = pass.substring(0, 1);
    var exp = new RegExp('^' + first + '+$');
    if (exp.test(pass))
    {
        return false;
    }
    if (first == 'a' || first == 'A')
    {
        f = pass.charCodeAt(0);
        for (i = 1; i < pass.length; i++)
        {
            tmp = pass.charCodeAt(i);
            if (tmp - f != i)
            {
                return true;
            }
        }
        return false;
    }
    return true;
}
function passwordGrade(pwd)
{
    var score = 0;
    var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
    var repeatCount = 0;
    var prevChar = '';
    //check length
    var len = pwd.length;
    score += len > 18 ? 18 : len;
    //check type
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (eval('/' + regexArr[i] + '/').test(pwd))
            score += 4;
    }
    //bonus point
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
            score += 2;
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
            score += 2;
    }
    //deduction
    for (var i = 0, num = pwd.length; i < num; i++) {
        if (pwd.charAt(i) == prevChar)
            repeatCount++;
        else
            prevChar = pwd.charAt(i);
    }
    score -= repeatCount * 1;
    return score;
}
function login(n, username)
{
    if (n == 2)
    {
        window.location.href = "http://login.duote.com/login.php";
    }
    else if (n == 3)
    {
        window.location.href = "http://bbs.haozip.com/login.php";
    }
    else if (n == 4)
    {
        window.location.href = "http://bbs.shanhu99.com/logging.php?action=login";
    }
    else if (n == 5)
    {
        window.location.href = "http://ie.2345.com/bbs/logging.php?action=login";
    }
    else
    {
        if (username)
        {
            window.location.href = "http://login.2345.com/login.php?username=" + username;
        }
        else
        {
            window.location.href = "http://login.2345.com/login.php";
        }
    }
}
function getPwdScore(pass)
{
    var score = 0;
    if (pass.length < 6)
    {
        score = 0;
    }
    else if (pass == $('#username').val())
    {
        score = 0;
    }
    else if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
    {
        score = 0;
    }
    else
    {
        score = passwordGrade(pass);
    }
    return score;

}

String.prototype.trim = function()
{
    return this.replace(/(^\s*)|(\s*$)/g, "");
};

String.prototype.cnSize = function() {
    //换算中文字长
    var arr = this.match(/[^\x00-\xff]/ig);
    return this.length + (arr == null ? 0 : arr.length);
};