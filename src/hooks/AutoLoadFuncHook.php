<?php
use Octopus\Router;

class AutoLoadFuncHook extends Hook
{
    private static $autoObjArr = array();

    public function mark()
    {
        $className = Router::fetchClass();
        $class = $className::getInstance();
        $loadList = LoadClassConfig::getAutoLoadFunc();
        foreach ($loadList as $loadKey => $loadInfo)
        {
            //针对$class->$loadInfo['key']的行为不一致
            //php5: $class->($loadInfo['key'])
            //php7: ($class->$loadInfo)['key']
            $keyVar = $loadInfo['key'];
            if (!isset(self::$autoObjArr[$loadInfo['key']]) || is_object(self::$autoObjArr[$loadInfo['key']]))
            {
                $class->$keyVar = self::$autoObjArr[$loadInfo['key']] = new $loadKey();
            }
            else
            {
                $class->$keyVar = self::$autoObjArr[$loadInfo['key']];
            }
            if (!empty($loadInfo['runFunc']) && is_array($loadInfo['runFunc']))
            {
                foreach ($loadInfo['runFunc'] as $runFun)
                {
                    if (method_exists($class->$keyVar, $runFun))
                    {
                        $class->$keyVar->$runFun();
                    }
                }
            }
        }
    }
}
