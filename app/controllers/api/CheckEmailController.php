<?php

class CheckEmailController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }
        xLog("checkapilog", "checkapilog", "CheckEmail.actionIndex");
        $memberModel = loadModel('member');
        $email = htmlspecialchars($_POST['email'], ENT_QUOTES, 'ISO-8859-1');
        if (!filter_var($email, FILTER_VALIDATE_EMAIL))
        {
            die('200');
        }
        if ($member = $memberModel->getMemberFromEmail($email))
        {
            $username = $member['username'];
            $userLen = mb_strlen($username, 'GBK');
            if ($userLen <= 3)
            {
                if ($userLen > 1)
                {
                    $username = mb_substr($username, 0, $userLen - 1, "GBK") . "*";
                }
            }
            else
            {
                $username = mb_substr($username, 0, 3, "GBK") . str_repeat("*", $userLen - 3);
            }
            echo "201||$username";
        }
        else
        {
            echo "200";
        }
    }

}
