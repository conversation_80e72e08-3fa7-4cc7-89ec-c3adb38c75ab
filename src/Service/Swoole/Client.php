<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/8/17
 * Time: 11:45
 */
namespace Service\Swoole;

use Service\Func\ServiceFunc;
use Service\Swoole\Tool\Encoding;
use Service\Swoole\Tool\Format;
use Service\Swoole\Tool\Log;
use Service\Swoole\Tool\Sign;

class Client
{
    /**
     * 发送数据给服务
     * -
     * @param array $data 客户端发送的数据
     * @return bool
     */
    public static function sendTo(array $data)
    {
        $useSwoole = ServiceFunc::useSwooleGlobal();
        if ($useSwoole)
        {
            $client = new \swoole_client(SWOOLE_SOCK_TCP);
            try
            {
                $ipInfo = ServiceFunc::hashServiceIp(time());
                if (empty($ipInfo))
                {
                    $msg = gethostbyname($_SERVER['SERVER_NAME']) . ' 未开启swoole服务';
                    \WebLogger\Facade\LoggerFacade::info("error swoole process {$msg}");
                    return self::directSendSms($data);
                }
                list($ip, $port) = explode(':', $ipInfo);
                if (!$client->connect($ip, $port, 3))
                {
                    $msg = gethostbyname($_SERVER['SERVER_NAME']) . ' 未能连接swoole服务端：' . $ip . ':' . $port;
                    \WebLogger\Facade\LoggerFacade::info("error swoole process {$msg}");
                    return self::directSendSms($data);
                }
                $client->send(Sign::getSendData(Encoding::transcoding($data, 'utf-8')));
                $responseData = $client->recv();
                $client->close();
                return Encoding::transcoding(json_decode($responseData, true), 'gbk');
            }
            catch (\Exception $exception)
            {
                return Format::returnArray(-1, '接受数据超时', [$exception->getMessage()]);
            }
        }
        else
        {
            return self::directSendSms($data);
        }
    }

    /**
     * 直接发送短信，不走swoole
     * -
     * @param array $data data
     * @return array|mixed|string
     */
    private static function directSendSms($data)
    {
        $namespace = '\\Service\\Handle\\' . $data['Handle'];
        if (class_exists($namespace))
        {
            if (method_exists($namespace, 'run'))
            {
                $handleObj = new $namespace();
                $responseData = $handleObj->run($data);
                return Encoding::transcoding($responseData, 'gbk');
            }
            else
            {
                return Format::returnArray(-7, 'Handle未实现');
            }
        }
        else
        {
            return Format::returnArray(-6, 'Handle不存在');
        }
    }
}
