<?php
/**
 * 文件名称：GuoDuSmsChannelAction.php
 * @copyright Copyright (c) 2017，上海二三四五网络科技股份有限公司
 * <AUTHOR>
 * @group     新科技研究院/大数据
 * @date      2018/06/07
 */

namespace Smschannel;

use FuncAction;

class SiooSmsChannelAction extends SmsChannelAction
{
    /**
     * 发送短信，单条
     * @param array|string $phone 手机号
     * @param string       $msg   短信内容
     * @param string       $sign  签名
     * @return int
     */
    public function send($phone, $msg, $sign)
    {
        $phoneStr = $phone;
        $multi = 0;
        if (is_array($phoneStr))
        {
            $phoneStr = implode(",", $phoneStr);
            $multi = 1;
        }
        //该通道具有直接传输GBK编码的接口
        $params = array(
            'uid'    => $this->user,//用户名
            'auth'   => md5($this->pwd),//密码
            'mobile' => $phoneStr,//手机号
            'expid'  => 0,
            'msg'    => urlencode(\EncodingAction::convertEncoding($msg, 'utf-8')),
            'encode' => 'utf-8',
        );
        $finalUrl = "{$this->apiUrl}/hy/";
        $resultStr = FuncAction::request($finalUrl, http_build_query($params), [
            'header' => ['Content-Type:application/x-www-form-urlencoded'],
        ]);
        $resultArr = explode(',', $resultStr[0]);
        if ($resultArr[0] == '0')
        {
            if (!$multi)
            {
                $this->msgId = $resultArr[1];
                $this->OperatorMsg = array(
                    'result'     => self::RET_SUCC,
                    'codeStatus' => 0,
                    'codeDesc'   => '成功',
                );
                return self::RET_SUCC;
            }
            else
            {
                /**@var array $phone */
                foreach ($phone as $phoneLine)
                {
                    $this->msgIdList[$phoneLine] = $resultArr[1];
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_SUCC,
                        'codeStatus' => 0,
                        'codeDesc'   => '成功',
                    );
                }
                return self::RET_SUCC;
            }
        }
        else
        {
            if (!$multi)
            {
                $this->msgId = 0;
                $this->OperatorMsg = array(
                    'result'     => self::RET_ERR,
                    'codeStatus' => -1,
                    'codeDesc'   => "失败，代码{$resultArr[0]}",
                );
                return self::RET_ERR;
            }
            else
            {
                /**@var array $phone */
                foreach ($phone as $phoneLine)
                {
                    $this->msgIdList[$phoneLine] = 0;
                    $this->operatorMsgList[$phoneLine] = array(
                        'result'     => self::RET_ERR,
                        'codeStatus' => -1,
                        'codeDesc'   => "失败，代码{$resultArr[0]}",
                    );
                }
                return self::RET_ERR;
            }
        }
    }

    /**
     * 相同内容多个手机号群发
     * @param array  $phone [phone1,phone2,phone3]
     * @param string $msg   message
     * @param string $sign  签名
     * @return int
     */
    public function multiPhoneSend($phone, $msg, $sign)
    {
        $this->send($phone, $msg, $sign);
    }

    /**
     * @param array  $multiContent multiContent
     * @param string $sign         sign
     * @return int
     */
    public function batchSend($multiContent, $sign)
    {
        $infoArr = [];
        foreach ($multiContent as $key => $value)
        {
            list($phone, $msg) = $value;
            $msg = urlencode(\EncodingAction::convertEncoding($msg, 'utf-8'));
            $infoArr[] = "<Info><Mobile>{$phone}</Mobile><Msg><![CDATA[{$msg}]]></Msg></Info>";
        }
        $md5Auth = md5($this->pwd);
        $reqHead = "<Head><Auth>{$md5Auth}</Auth><Uid>{$this->user}</Uid><Encode>UTF-8</Encode><Expid>0</Expid></Head>";
        $reqBody = "<Detail>" . implode('', $infoArr) . "</Detail>";
        $reqAll = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Document>{$reqHead}{$reqBody}</Document>";
        $resultStr = FuncAction::request($this->apiUrl . '/hy/sendMCSms', $reqAll, [
            'header' => ['Content-Type:application/xml'],
        ])[0];
        $response = explode(',', $resultStr);

        if ($response[0] == '0')
        {
            foreach ($multiContent as $key => $value)
            {
                $this->msgIdList[$value[0]] = $response[1];
                $this->operatorMsgList[$value[0]] = array(
                    'codeStatus' => 1,
                    'codeDesc'   => "成功",
                    'result'     => self::RET_SUCC,
                );
            }
            return self::RET_SUCC;
        }
        else
        {
            foreach ($multiContent as $key => $value)
            {
                $this->operatorMsgList[$value[0]] = array(
                    'codeStatus' => 0,
                    'codeDesc'   => "失败，代码{$response[0]}",
                    'result'     => self::RET_ERR,
                );
            }
            return self::RET_ERR;
        }
    }

    /**
     * 获取 callback status 需要的配置文件, 和设定回调方法
     * @param array $channelConfig channelConfig
     * @return array
     */
    public static function callbackStatusConfig($channelConfig)
    {
        $params = array(
            'uid'  => $channelConfig['username'],//用户名
            'auth' => md5($channelConfig['password']),//密码
        );
        $api = $channelConfig['api_url'];
        $query = http_build_query($params);
        $finalUrl = "{$api}?{$query}";

        return array(
            'url'           => $finalUrl,
            'data'          => '',
            'account'       => $channelConfig['username'],
            'action'        => 'get',
            'callbackClass' => '\\Smschannel\\SiooSmsChannelAction',
            'callbackFunc'  => 'callbackStatus',
            'callback'      => '',
        );
    }

    /**
     * 获取 callback status, 是回调方法
     * @param string $data data
     * @return boolean
     */
    public static function callbackStatus($data)
    {
        $toPush = [];
        if ($data == '0')
        {
            return 0;
        }
        $redisAction = \RedisAction::connect();
        $arr = explode(';', $data);
        foreach ($arr as $itemStr)
        {
            $itemArr = explode(',', $itemStr);
            if (!isset($itemArr[3]))
            {
                continue;
            }
            $resInfo = array(
                'Mobile'    => $itemArr[2],//手机号码
                'Msg_Id'    => $itemArr[1],//订单号码
                'ProcTime'  => date('Y-m-d H:i:s'),//发送时间
                'TimeStamp' => $itemArr[0],//到达时间
                'Status'    => $itemArr[3],
            );
            $toPush[] = json_encode($resInfo);
        }
        $res = $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, ...$toPush);
        return $res;
    }
}
