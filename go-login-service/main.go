package main

import (
	"fmt"
	"log"
	"login-service/config"
	"login-service/handlers"
	"net/http"
	"os"

	"github.com/gorilla/mux"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using system environment variables")
	}

	// Initialize configuration
	config.Init()

	// Create router
	r := mux.NewRouter()

	// Create logout handler
	logoutHandler := handlers.NewLogoutHandler()

	// Routes
	r.HandleFunc("/login_for_index_0327.php", handleMainRequest).Methods("GET", "POST")
	r.<PERSON>("/logout", logoutHandler.HandleLogout).Methods("GET", "POST")

	// Get port from environment or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	fmt.Printf("Server starting on port %s\n", port)
	log.Fatal(http.ListenAndServe(":"+port, r))
}

// handleMainRequest handles the main request logic
func handleMainRequest(w http.ResponseWriter, r *http.Request) {
	action := r.URL.Query().Get("action")

	// if ($_GET['action'] == "logout") equivalent
	if action == "logout" {
		// Call the logout function directly (equivalent to PHP logout() call)
		logout(w, r)
		return
	}

	// For other actions, return a simple response
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.WriteHeader(http.StatusOK)

	html := `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Login Service</title>
</head>
<body>
    <h1>Login Service</h1>
    <p>This is the Go version of the login service.</p>
    <p>To logout, visit: <a href="?action=logout">?action=logout</a></p>
    <p>Original PHP equivalent: <code>if ($_GET['action'] == "logout") { logout(); }</code></p>
</body>
</html>`

	fmt.Fprint(w, html)
}
