<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="gbk">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <title>联合登录配置列表</title>
    <!-- Bootstrap -->
    <link href="/css/UnionLogin/bootstrap.min.css" rel="stylesheet">
    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="/js/houtai_gl/unionLogin/html5shiv.min.js"></script>
    <script src="/js/houtai_gl/unionLogin/respond.min.js"></script>
    <![endif]-->
</head>
<body>
<div class="container">
    <!--面包屑导航 开始-->
    <div class="location">位置：<a href="/houtai_gl/UnionLogin/config">联合登录设置管理</a> &gt; <span>联合登录配置列表</span></div>
    <!--面包屑导航 结束-->
    <div class="row">
        {{if $smarty.const.RUNMODE != 'production'}}
        <h1 class="text-center" style="background-color:#FFA67D;">联合登录配置列表({{$smarty.const.RUNMODE}})</h1>
        {{else}}
        <h1 class="text-center">联合登录配置列表</h1>
        {{/if}}
    </div>
    <div id="app" style="display: none;">
        <template id="list">
            <div class="row">
                <div class="col-md-3">
                    <div class="input-group" style="margin:10px">
                        <span class="input-group-addon">Mid</span>
                        <select class="form-control"  v-model="mid" v-on:change="changeMid">
                            <option>全部</option>
                            <option v-for="mid in mids "><{mid}></option>
                        </select>
                    </div>
                </div>
                <div class="col-md-9 text-right">
                    <button type="button" @click="add" class="btn btn-success" aria-label="Left Align"> 新增</button>
                    <br/>
                    <br/>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-hover">
                        <tr>
                            <th>序号</th>
                            <th>产品名称-包名-签名</th>
                            <th>最低支持版本号</th>
                            <th style="text-align: center">操作</th>
                        </tr>
                        <template v-for="item in list">
                            <tr>
                                <td><{item.id}></td>
                                <td><{item.pname}></td>
                                <td><{item.lowest_version}></td>
                                <td style="text-align: center">
                                    <a v-if="item.status==1" type="button" @click=edit(item) class="btn btn-link">编辑</a>
                                    <a v-if="item.status==1" type="button" @click="changeStatus(item.id, item.status, item.mid)"
                                       class="btn btn-link">禁用</a>
                                    <a v-if="item.status==0" type="button" @click="changeStatus(item.id, item.status, item.mid)"
                                       class="btn btn-link">启用</a>
                                    <a v-if="item.status==0" type="button" @click=edit(item) class="btn btn-link">查看</a>
                                </td>
                            </tr>
                        </template>
                    </table>
                </div>
            </div>
        </template>
        <template>
            <div id="model" style="display: none">
                <input type="hidden" v-model="id">
                <div class="input-group" style="margin:10px">
                    <span class="input-group-addon">名称</span>
                    <input id="pname" :disabled="noEdit" v-model="pname" type="text" class="form-control"
                           placeholder="请输入产品名称">
                </div>

                <div class="input-group" style="margin:10px">
                    <span class="input-group-addon">包名</span>
                    <input id="package_name" :disabled="noEdit" v-model="package_name" type="text" class="form-control"
                           placeholder="请输入产品包名">
                </div>

                <div class="input-group" style="margin:10px">
                    <span class="input-group-addon">签名</span>
                    <input id="sign" :disabled="noEdit" v-model="sign" type="text" class="form-control"
                           placeholder="请输入产品签名">
                </div>
            </div>
        </template>
    </div>

</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="/js/houtai_gl/unionLogin/jquery.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="/js/houtai_gl/unionLogin/bootstrap.min.js"></script>
<script src="/js/houtai_gl/layer/layer.js"></script>
<script src="/js/houtai_gl/vue/vue{{if $smarty.const.RUNMODE eq 'production'}}.min{{/if}}.js"></script>
<script>
    var mids = JSON.parse('{{$pageArray.mid}}');
    var app = new Vue({
        el: '#app',
        delimiters: ["<{", "}>"],
        data: {
            id: 0,
            list: [],
            mids: mids,
            pname: '',
            package_name: '',
            sign: '',
            modalTitle: '新增',
            noEdit: false,
            mid: '全部',
        },
        methods: {
            changeMid: function () {
                this.pullList();
            },
            pullList: function () {
                var me = this;
                var mid = me.mid;
                if (mid == '全部') {
                    mid = null;
                }
                $.ajax({
                    url: '/houtai_gl/UnionLoginApi/configList',
                    type: 'post',
                    data: {mid: mid},
                    success: function (response) {
                        var status = response.code;
                        if (status == 200) {
                            me.list = response.data;
                        }
                    }
                })
            },
            showModal: function () {
                var me = this;
                var modalConfig = {
                    type: 1,
                    title: me.modalTitle,
                    content: $('#model'),
                    btn: ['取消', '保存'],
                    btn2: function () {
                        var check = me.checkParam();
                        if (check != true) {
                            return false;
                        }
                        $.ajax({
                            url: '/houtai_gl/UnionLoginApi/save',
                            type: 'post',
                            data: {
                                'id': me.id,
                                'mid': me.mid,
                                'pname': me.pname,
                                'packageName': me.package_name,
                                'sign': me.sign
                            },
                            success: function (response) {
                                var status = response.code;
                                layer.msg(response.msg);
                                if (status == 200) {
                                    me.pullList();
                                }
                            }
                        })
                    }
                };

                layer.open(modalConfig);
            },
            add: function () {
                window.location.href = '/houtai_gl/UnionLogin/detail/';
            },
            edit: function (item) {
                window.location.href = '/houtai_gl/UnionLogin/detail/' + item.id;
            },
            checkParam: function () {
                var me = this;
                if (me.mid == '') {
                    layer.tips('请选择mid', $("#mid"));
                    return false;
                }
                if (me.pname == '') {
                    layer.tips('请输入产品名称', $("#pname"));
                    return false;
                }

                if (me.package_name == '') {
                    layer.tips('请输入产品包名', $("#package_name"));
                    return false;
                }
                if (me.sign == '') {
                    layer.tips('请输入产品签名', $("#sign"));
                    return false;
                }
                return true;
            },
            changeStatus: function (id, status, mid) {
                var me = this;
                if (status == 1) {
                    layer.confirm('禁用后会影响联合登录功能，您确认禁用吗？', {btn: ['取消', '确认禁用'], title: '确认禁用'},
                        function (index) {
                            layer.close(index)
                        }, function () {
                            $.ajax({
                                url: '/houtai_gl/UnionLoginApi/changeConfigStatus',
                                type: 'post',
                                data: {'id': id, 'status': status, 'mid': mid},
                                success: function (response) {
                                    var status = response.code;
                                    layer.msg(response.msg);
                                    if (status == 200) {
                                        me.pullList();
                                    }
                                }
                            })
                        })
                }
                else {
                    $.ajax({
                        url: '/houtai_gl/UnionLoginApi/changeConfigStatus',
                        type: 'post',
                        data: {'id': id, 'status': status, 'mid': mid},
                        success: function (response) {
                            var status = response.code;
                            layer.msg(response.msg);
                            if (status == 200) {
                                me.pullList();
                            }
                        }
                    })
                }
            }
        },
        mounted: function () {
            var me = this;
            $('#app').show();
            me.pullList();
        }
    })
</script>
</body>
</html>