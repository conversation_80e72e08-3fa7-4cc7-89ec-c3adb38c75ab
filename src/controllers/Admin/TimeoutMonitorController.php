<?php
/**
 * 发送超时页面
 * */
namespace Admin;
use Controller;

class TimeoutMonitorController extends Controller
{
    const LOGTIMELIST = 'logtimelist_';
    
    
    public function actionIndex()
    {
        
        $startTime = $this->Filter->getPost('startTime');
        $hourName = $this->Filter->getPost('hourName');
        $phone = $this->Filter->getPost('phone');
        if (empty($startTime) )
        {
            $startTime = date('Y-m-d');
        }
        if (empty($hourName))
        {
            $hourName = date('H');
        }
        
        $hour = range(00,24,01);
        $GetRedisAction = \GetRedisAction::getInstance();
        
        $OperatorConfigModel = OperatorConfigModel::getInstance();
        $OperatorList = $OperatorConfigModel->getOperatorList();
        foreach ($OperatorList as $OperatorInfo)
        {
            $operatorArr[$OperatorInfo['id']] = $OperatorInfo['provider_name'];
        }
        
        
        $SmsProjectModel = SmsProjectModel::getInstance();
        $projectList = $SmsProjectModel->getPojectList();
        $projectArr = array();
        foreach ($projectList as $projectInfo )
        {
            $projectArr[$projectInfo['pid']] = $projectInfo['project_name'];
        }
        
        
        $dataList = array();
        foreach ($hour as $hourInfo)
        {
            if ($hourInfo <= 9 )
            {
                $hourInfo = '0'.$hourInfo;
            }
            $pageArray['hour'][] = $hourInfo;
            $key = self::LOGTIMELIST . $startTime.'-' . $hourInfo;
            if ( $hourInfo != $hourName && $hourName != -1 )
            {
                continue;
            }
            $list = $GetRedisAction->redis->hGetAll($key); //print_r($list);
            if ( !empty ($list) )
            {
                if (is_array ($list) )
                {
                    foreach ($list as $listKey=>$listInfo)
                    {
                        //$listInfo = array($listInfo);
                        if (!empty($phone)  && $listKey  != $phone)
                        {
                            continue;
                        }
                        $listInfo = json_decode ($listInfo);
                        foreach ($listInfo as $dataKey=>$dataArr )
                        {
                            $dataList[$dataKey] = array(
                                'phone' => $listKey,
                                'logId' => $dataKey,
                                'diffDate' => $dataArr->diffDate,
                                'start' => $dataArr->start,
                                'end' => $dataArr->end,
                                'channelName' => !empty($operatorArr[$dataArr->channelId]) ? $operatorArr[$dataArr->channelId] : '',
                                'projectName' => !empty($projectArr[$dataArr->pid]) ? $projectArr[$dataArr->pid] : ''
                            );
                        }
                    }
                }
                else
                {
                    
                }
            }
           
        }
        krsort($dataList);
        $pageArray['dataList'] = $dataList;
        $pageArray['startTime'] = $startTime;
        $pageArray['hourName'] =  $hourName;
        $pageArray['phone'] =  $phone;
        loadView('Admin/timeout_monitor_index.tpl.html', $pageArray);
    }
}