<?php

class BindController extends Controller
{

    public function actionIndex()
    {
        $openid = $_COOKIE['openid'];
        $accessToken = $_COOKIE['access_token'];
        $oauthType = $_COOKIE['oauth_type'];

        $redirectUrl = "/";
        $host = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
        preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $host, $matches);
        if ($matches[0] == "2345.com" || $matches[0] == "2345.cn")
        {
            $redirectUrl = $_COOKIE['qqforward'];
        }

        if (!$openid || !$accessToken || !in_array($oauthType, array('qq', 'weibo', 'weixin')))
        {
            redirect($redirectUrl);
        }
        $oauthAction = loadAction('oauth');
        $oauthUser = $oauthAction->oauthUserInfo($oauthType, $accessToken, $openid);
        if (!$oauthUser)
        {
            redirect($redirectUrl);
        }
        if (isset($_COOKIE['qqforward']) && strpos($_COOKIE['qqforward'], "http://book.2345.com") === 0)
        {
            $this->createUserAndLogin($openid, $oauthType, $oauthUser, $redirectUrl);
        }
        elseif (isset($_COOKIE['qqforward']) && strpos($_COOKIE['qqforward'], "http://cps.2345.com") === 0)
        {
            $this->createUserAndLogin($openid, $oauthType, $oauthUser, $redirectUrl);
        }
        elseif (isset($_COOKIE['qqforward']) && strpos($_COOKIE['qqforward'], "://h5.2345.com") && strpos($_COOKIE['qqforward'], "://h5.2345.com") < 6)// https or http
        {
            $this->createUserAndLogin($openid, $oauthType, $oauthUser, $redirectUrl);
        }
        elseif (isset($_COOKIE['qqforward']) && strpos($_COOKIE['qqforward'], "://game.2345.com") &&
            strpos($_COOKIE['qqforward'], "://game.2345.com") < 6 &&
            strpos($_COOKIE['qqforward'], "gameBox"))// https or http gameBox 游戏盒子的链接
        {
            $this->createUserAndLogin($openid, $oauthType, $oauthUser, $redirectUrl);
        }
        elseif (isset($_COOKIE['qqforward']) && strpos($_COOKIE['qqforward'], "http://www.2345.com") === 0 && $_COOKIE['oAuthFocusLogin'])
        {
            $this->createUserAndLogin($openid, $oauthType, $oauthUser, $redirectUrl);
        }
        elseif (isset($_COOKIE['qqforward']) && preg_match("/^(https|http)\:\/\/\w+\.ym\.com/", $_COOKIE['qqforward']))
        {
            $this->createUserAndLogin($openid, $oauthType, $oauthUser, $redirectUrl);
        }
        else
        {
            $this->doBind($openid, $oauthType, $oauthUser, $redirectUrl);
        }
    }

    /**
     * 执行帐号绑定流程
     *
     * @param string $openid    openid
     * @param string $oauthType oauth type
     * @param array  $oauthUser oauth user info
     * @param string $forwardUrl forwardUrl
     *
     * @return mixed
     */
    private function doBind($openid, $oauthType, $oauthUser, $forwardUrl = '/')
    {
        $redirectUrl = PASSPORT_HOST . "/oauth/bind";
        if ($oauthType == "qq")
        {
            $redirectUrl = LOGIN_HOST . "/oauth/bind";
        }

        session_start();
        $loginAction = loadAction('login');
        if (!isset($_SESSION['expire']))
        {
            $_SESSION['expire'] = 0;
        }
        if ($_SERVER['REQUEST_METHOD'] == 'POST')
        {
            $gender = $oauthUser['gender'];
            $isUseRedis = Config::get("isUseRedis");
            if ($isUseRedis)
            {
                $redis = RedisEx::getInstance();
            }
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, $oauthType);
            if ($return)
            {
                redirect($forwardUrl);
            }
            if ($_POST['cmd'] == "bind")
            {
                if ($_SERVER['HTTP_USER_AGENT'] == ('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']) && !isset($_SERVER['HTTP_REFERER']))
                {
                    FlushData::set("alert", "绑定有误，错误代码1007，请联系客服");
                    redirect($redirectUrl);
                }
                else
                {
                    $isValidate = 0;
                    if ($_SESSION['expire'] > 3 || $loginAction->decideCode(get_client_ip()))
                    {
                        $checkCode = $_POST['check_code'];
                        if ($checkCode != "" && $_SESSION['captcha_code'] != "")
                        {
                            if ($checkCode != $_SESSION['captcha_code'])
                            {
                                $_SESSION['captcha_code'] = "";
                                FlushData::set("alert", "验证码输入错误！");
                                redirect($redirectUrl);
                            }
                        }
                        else
                        {
                            FlushData::set("alert", "验证码不能为空！");
                            redirect($redirectUrl);
                        }
                        unset($_SESSION['captcha_code']);
                        $isValidate = 1;
                    }
                    $username = trim($_POST['username']);
                    $password = trim($_POST['password']);

                    $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
                    if (!$source)
                    {
                        $source = "login.2345.com";
                    }
                    $result = $loginAction->login($username, $password, 'login.2345.com', get_client_ip(), $isValidate, $_SERVER['HTTP_USER_AGENT'], $source);
                    $state = $result[0];
                    if ($state == 6)
                    {
                        //限制ip6小时
                        FlushData::set("alert", "绑定有误，错误代码1006，请联系客服");
                        redirect($redirectUrl);
                    }
                    elseif ($state == 5)
                    {
                        //非法IP
                        FlushData::set("alert", "绑定有误，错误代码1005，请联系客服");
                        redirect($redirectUrl);
                    }
                    elseif ($state == 4)
                    {
                        //非法域名
                        FlushData::set("alert", "绑定有误，错误代码1004，请联系客服");
                        redirect($redirectUrl);
                    }
                    elseif ($state == 3)
                    {
                        $_SESSION['expire'] = 4;
                        FlushData::set("alert", "验证码输入错误！");
                        redirect($redirectUrl);
                    }
                    elseif ($state == 2)
                    {
                        setcookie('active_passid', $result['passId'], 0, '/');
                        setcookie('active_email', $result['email'], 0, '/');
                        redirect('/active_error.html');
                    }
                    elseif ($state === -1)
                    {
                        $_SESSION['expire'] = intval($_SESSION['expire']) + 1;
                        FlushData::set("alert", "2345帐号或密码错误，均区分大小写");
                        redirect($redirectUrl);
                    }
                    elseif ($state === 1)
                    {
                        unset($_SESSION['expire']);
                        $passid = $result['passid'];
                        $cookie = $result['cookie'];
                        $nickname = $oauthUser['nickname'];
                        $oauthBinds = $oauthModel->getBindByPassid($passid);
                        foreach ($oauthBinds as $oauthBind)
                        {
                            if ($oauthBind['type'] == $oauthType)
                            {
                                FlushData::set("alert", "此2345帐号已经绑定过其他帐号了");
                                redirect($redirectUrl);
                            }
                        }

                        $blnBind = $oauthModel->setBind($oauthType, $passid, $openid, $nickname, $username);
                        $memberInfo = $memberModel->read($passid);
                        $memberInfo['passid'] = $passid;
                        $objLogV2Action = loadAction('logV2');
                        if ($blnBind) {
                            //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:OPENID、NICKNAME;
                            $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'OPENID', $memberInfo, '', json_encode([$oauthType => $openid]));
                            $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'NICKNAME', $memberInfo, '', $nickname);
                        }

                        if ($memberInfo['gender'] == 0 && $gender != 0)
                        {
                            $memberModel->edit($passid, array('gender' => $gender));
                            $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'GENDER', $memberInfo, '', $gender);
                        }

                        if ($isUseRedis)
                        {
                            $redis->incr("regOAuthSuccNum");
                            $redis->incr("RSN:{$oauthType}_login_2345_com");
                            $redis->incr("LSN:{$oauthType}_login_2345_com");
                            $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
                            if (!$source)
                            {
                                $source = "login.2345.com";
                            }
                            $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;

                            $this->collectOauth($oauthType, $source, $type);
                        }

                        // 使用完毕，删除 cookie oauth_type openid access_token
                        \Common\Utils\Cookie::delOauthBindCookie();

                        setcookie('oauth_state', null, time() - 3600, '/');
                        setcookie('redirect_uri', null, time() - 3600, '/');

                        $cTime = time() + 3600 * 24 * 30;
                        $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);

                        // 设置其他域名的登录cookie： 2345.cn等
                        $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
                        if (!\Common\Utils\Http::isWeiXinBrowser() && !\Common\Utils\Http::isH5AppWebView())
                        {
                            setcookie('qqforward', '', 0, '/');
                        }

                        $pageArray = $loginAction->getRedirectParams($thirdCallbackCookies);

                        return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $pageArray);
                    }
                }
            }
            else
            {
                $nickname = $oauthUser['nickname'];
                $figureurl = $oauthUser['figureurl'];
                $clientIp = get_client_ip();
                $result = $memberModel->regOAuth($oauthType, $openid, $nickname, $clientIp, '', $gender);
                if (!$result)
                {
                    if ($isUseRedis) {
                        //收集注册登录信息
                        $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
                        !$source && $source = "login.2345.com";
                        $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
                        $this->collectOauth($oauthType, $source, $type, 0);
                    }
                    redirect($forwardUrl);
                }
                $passid = $result['passid'];
                $uid = $result['uid'];
                $username = $result['username'];
                $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;

                //todo 暂时注释, 不记录用户的第三方头像
                /*
                if ($figureurl)
                {
                    $userFolder = BASEPATH . "/member/avatar/" . ceil($passid / 3000);
                    if (!is_dir($userFolder))
                    {
                        mkdir($userFolder, 0777);
                    }
                    if (!file_exists("$userFolder/{$passid}_middle.jpg"))
                    {
                        $imgStr = curl_get_contents($figureurl);
                        $im = imagecreatefromstring($imgStr);
                        $im_x = imagesx($im);
                        $im_y = imagesy($im);
                        $im_big = imagecreatetruecolor(200, 200);
                        imagecopyresized($im_big, $im, 0, 0, 0, 0, 200, 200, $im_x, $im_y);
                        imagejpeg($im_big, "$userFolder/{$passid}_big.jpg");
                        $im_middle = imagecreatetruecolor(120, 120);
                        imagecopyresized($im_middle, $im, 0, 0, 0, 0, 120, 120, $im_x, $im_y);
                        imagejpeg($im_middle, "$userFolder/{$passid}_middle.jpg");
                        $im_small = imagecreatetruecolor(48, 48);
                        imagecopyresized($im_small, $im, 0, 0, 0, 0, 48, 48, $im_x, $im_y);
                        imagejpeg($im_small, "$userFolder/{$passid}_small.jpg");

                        \Service\Avatar\Avatar::storeUpdateTime($passid);
                    }
                }
                */
                if ($isUseRedis)
                {
                    $redis->incr("regOAuthSuccNum");
                    $redis->incr("RSN:{$oauthType}_login_2345_com");
                    $redis->incr("LSN:{$oauthType}_login_2345_com");
                    $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
                    if (!$source)
                    {
                        $source = "login.2345.com";
                    }
                    $type = \Service\AccountStat\Consts\AccountStat::TP_REG;

                    $this->collectOauth($oauthType, $source, $type);
                }
                $cTime = time() + 3600 * 24 * 30;
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                $cookie['need_modify_name'] = 1;
                $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);

                // 设置其他域名的登录cookie： 2345.cn等
                $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
                if (!\Common\Utils\Http::isWeiXinBrowser() && !\Common\Utils\Http::isH5AppWebView())
                {
                    setcookie('qqforward', '', 0, '/');
                }

                $pageArray = $loginAction->getRedirectParams($thirdCallbackCookies);

                return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $pageArray);
            }
        }
        else
        {
            $pageArray = array();
            if ($_SESSION['expire'] > 3 || $loginAction->decideCode(get_client_ip()))
            {
                $pageArray["display"] = '';
            }
            else
            {
                $pageArray["display"] = "none";
            }
            $pageArray['oauthUser'] = $oauthUser;
            if ($oauthType == 'qq')
            {
                $pageArray['oauthName'] = '腾讯QQ';
            }
            elseif ($oauthType == 'weibo')
            {
                $pageArray['oauthName'] = '新浪微博';
            }
            elseif ($oauthType == 'weixin')
            {
                $pageArray['oauthName'] = '微信';
            }
            $pageArray['alert'] = FlushData::get('alert');
            $wapBindTpl = 'm/bind_wap.tpl.html';
            if ($oauthType == "weixin")
            {
                $wapBindTpl = 'm/bind_wap_weixin.tpl.html';
            }
            loadCompatibleView('v2/bind.tpl.html', $wapBindTpl, $pageArray);
        }
    }

    /**
     * 创建用户并登录
     *
     * @param string $openid      openid
     * @param string $oauthType   oauthType
     * @param array  $oauthUser   oauth user
     * @param string $forwardUrl  forwardUrl
     * @param string $email       email
     *
     * @return mixed
     */
    private function createUserAndLogin($openid, $oauthType, $oauthUser, $forwardUrl = '/', $email = '')
    {
        // 使用完毕，删除 cookie oauth_type openid access_token
        \Common\Utils\Cookie::delOauthBindCookie();

        setcookie('oauth_state', null, time() - 3600, '/');
        setcookie('redirect_uri', null, time() - 3600, '/');

        $loginAction = loadAction('login');
        $isUseRedis = Config::get("isUseRedis");
        if ($isUseRedis)
        {
            $redis = RedisEx::getInstance();
        }
        $memberModel = loadModel('member');
        $oauthModel = loadModel('oauth');
        $return = $oauthModel->getBind($openid, $oauthType);
        if ($return)
        {
            redirect($forwardUrl);
        }
        if (!\Common\Utils\Http::isWeiXinBrowser() && !\Common\Utils\Http::isH5AppWebView())
        {
            setcookie('qqforward', '', 0, '/');
        }

        $nickname = $oauthUser['nickname'];
        $figureurl = $oauthUser['figureurl'];
        $gender = $oauthUser['gender'];
        $clientIp = get_client_ip();
        $result = $memberModel->regOAuth($oauthType, $openid, $nickname, $clientIp, $email, $gender);
        if (!$result)
        {
            if ($isUseRedis) {
                //收集注册登录信息
                $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
                !$source && $source = "login.2345.com";
                $type = \Service\AccountStat\Consts\AccountStat::TP_REG;
                $this->collectOauth($oauthType, $source, $type, 0);
            }
            redirect($forwardUrl);
        }
        $passid = $result['passid'];
        $uid = $result['uid'];
        $username = $result['username'];
        $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
        /*
        if ($figureurl)
        {
            $userFolder = BASEPATH . "/member/avatar/" . ceil($passid / 3000);
            if (!is_dir($userFolder))
            {
                mkdir($userFolder, 0777);
            }
            if (!file_exists("$userFolder/{$passid}_middle.jpg"))
            {
                $imgStr = curl_get_contents($figureurl);
                $im = imagecreatefromstring($imgStr);
                $im_x = imagesx($im);
                $im_y = imagesy($im);
                $im_big = imagecreatetruecolor(200, 200);
                imagecopyresized($im_big, $im, 0, 0, 0, 0, 200, 200, $im_x, $im_y);
                imagejpeg($im_big, "$userFolder/{$passid}_big.jpg");
                $im_middle = imagecreatetruecolor(120, 120);
                imagecopyresized($im_middle, $im, 0, 0, 0, 0, 120, 120, $im_x, $im_y);
                imagejpeg($im_middle, "$userFolder/{$passid}_middle.jpg");
                $im_small = imagecreatetruecolor(48, 48);
                imagecopyresized($im_small, $im, 0, 0, 0, 0, 48, 48, $im_x, $im_y);
                imagejpeg($im_small, "$userFolder/{$passid}_small.jpg");

                \Service\Avatar\Avatar::storeUpdateTime($passid);
            }
        }
        */
        if ($isUseRedis)
        {
            $redis->incr("regOAuthSuccNum");
            $redis->incr("RSN:{$oauthType}_login_2345_com");
            $redis->incr("LSN:{$oauthType}_login_2345_com");

            //收集注册登录信息
            $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
            if (!$source)
            {
                $source = "login.2345.com";
            }
            $type = \Service\AccountStat\Consts\AccountStat::TP_REG;

            $this->collectOauth($oauthType, $source, $type);
        }

        $cTime = time() + 3600 * 24 * 30;
        $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
        $cookie['need_modify_name'] = 1;
        $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);

        // 设置其他域名的登录cookie： 2345.cn等
        $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
        if (!\Common\Utils\Http::isWeiXinBrowser() && !\Common\Utils\Http::isH5AppWebView())
        {
            setcookie('qqforward', '', 0, '/');
        }

        $pageArray = $loginAction->getRedirectParams($thirdCallbackCookies);

        return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $pageArray);
    }

    public function actionCheck()
    {
        $passid = intval($_GET["passid"]);
        $timestamp = $_GET["timestamp"];
        $code = $_GET["code"];
        if (checkOAuthCheckUrl($passid, $timestamp, $code))
        {
            $data = array("weibo" => false, "qq" => false);
            $oauthModel = loadModel('oauth');
            $result = $oauthModel->getBindByPassid($passid);
            !$result && $result = [];
            foreach ($result as $row)
            {
                foreach ($row as $key => $value)
                {
                    $row[$key] = mb_convert_encoding($value, "UTF-8", "GBK");
                }
                $data[$row["type"]] = $row;
            }
            echo json_encode($data);
        }
    }

    public function actionCheckPwd()
    {
        $passid = intval($_GET["passid"]);
        $timestamp = $_GET["timestamp"];
        $code = $_GET["code"];
        $ret = 0;
        if (checkOAuthCheckUrl($passid, $timestamp, $code))
        {
            $memberModel = loadModel('member');
            $ret = $memberModel->checkWeakPassword($passid);
        }
        echo $ret;
    }

    public function actionQq()
    {
        $oauthModel = loadModel('oauth');
        if (isset($_GET["act"]) && $_GET["act"] == "do_unbind")
        {
            $passid = intval($_GET["id"]);
            $unbindcode = htmlspecialchars($_GET["checkCode"], ENT_QUOTES, 'ISO-8859-1');
            if ($unbindcode)
            {
                $memberModel = loadModel('member');
                $memberInfo = $memberModel->read($passid);
                $memberInfo['passid'] = $passid;
                $arrOpenid = json_decode($memberInfo['openid'] ?? '', true);
                $strOpenid = $arrOpenid['qq'] ?? '';

                $blnUnbind = $oauthModel->delBind('qq', $passid, $unbindcode);
                if ($blnUnbind) {
                    //日志行为打点:用户信息解绑; type:USER_INFO_UNBIND; sub_type:OPENID;
                    $objLogV2Action = loadAction('logV2');
                    $objLogV2Action->report('ALL', 'USER_INFO_UNBIND', 'OPENID', $memberInfo, json_encode(['qq' => $strOpenid]), '');
                }
            }
            //通知项目
            noticeToChange($passid, 'unbindQQ', array(
                'passid' => $passid,
                'uid' => '',
                'type' => 'unbindQQ',
                'value' => '',
            ));

            $location = isset($_COOKIE['qqforward']) ? urldecode($_COOKIE['qqforward']) : PASSPORT_HOST."/member/edit_info.php";
            setcookie("qqforward", "", time() - 3600, '/');

            redirect($location);
        }
        else
        {
            $loginAction = loadAction("login");
            if (!$loginInfo = $loginAction->checkAuthCookie())
            {
                redirect('/');
            }

            if (isset($_GET['forward']))
            {
                $forward = getForwardUrl();
                setcookie('qqforward', $forward, 0, '/');
            }
            else
            {
                setcookie('qqforward', "", time() - 3600, '/');
            }

            if (isset($_GET["act"]) && $_GET["act"] == "request_unbind")
            {
                $passid = intval($loginInfo["i"]);
                $memberModel = loadModel('member');
                $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
                $userinfo = $memberModel->getUsernameByPassid($passid);
                $gid = $memberModel->getGidByPassid($passid);
                if ($gid && $gid != '100')
                {
                    $checkCode = md5($userinfo['username'] . time() . $passid . "checkcode");
                    $oauthModel->setUnbindCode('qq', $passid, $checkCode);
                    $link = LOGIN_HOST . '/oauth/bind/qq?act=do_unbind&id=' . $passid . '&checkCode=' . $checkCode;
                    $urlJson = json_encode(array('url' => $link, 'code' => "200"));
                    echo $callback . '(' . $urlJson . ')';
                }
                else
                {
                    $urlJson = json_encode(array('url' => " ", 'code' => "400", 'passid' => $passid));
                    echo $callback . '(' . $urlJson . ')';
                }
            }
            else
            {
                redirect(LOGIN_HOST . "/oauth/qq?callback=/oauth/bind/qq_callback");
            }
        }
    }

    public function actionWeibo()
    {
        $oauthModel = loadModel('oauth');
        if (isset($_GET["act"]) && $_GET["act"] == "do_unbind")
        {
            $passid = intval($_GET["id"]);
            $unbindcode = htmlspecialchars($_GET["checkCode"], ENT_QUOTES, 'ISO-8859-1');
            if ($unbindcode)
            {
                $oauthModel->delBind('weibo', $passid, $unbindcode);
            }
            redirect(PASSPORT_HOST . '/member/edit_info.php');
        }
        else
        {
            $loginAction = loadAction("login");
            if (!$loginInfo = $loginAction->checkAuthCookie())
            {
                redirect("/");
            }
            if (isset($_GET["act"]) && $_GET["act"] == "request_unbind")
            {
                $passid = intval($loginInfo["i"]);
                $memberModel = loadModel('member');
                $row = $memberModel->read($passid);
                if ($row)
                {
                    if ($row["email_status"] == 1)
                    {
                        $checkCode = md5($row["username"] . time() . $row["email"] . $row["id"] . "checkcode");
                        $oauthModel->setUnbindCode('weibo', $passid, $checkCode);
                        $smtpConfig = Config::get('smtp');
                        $smtpemailto = $row['email']; //发送给谁
                        $mailsubject = "2345网址导航用户中心解除微博帐号绑定"; //邮件主题
                        $link = "<a href='" . PASSPORT_HOST . "/oauth/bind/weibo?act=do_unbind&id=" . $passid . "&checkCode=" . $checkCode . "' target='_blank'>" . PASSPORT_HOST . "/oauth/bind/weibo?act=do_unbind&id=" . $passid . "&checkCode=" . $checkCode . "</a>";
                        $linkTxt = PASSPORT_HOST . "/oauth/bind/weibo?act=do_unbind&id=" . $passid . "&checkCode=" . $checkCode;
                        $mailbody = "<pre>您好：" . $row['username'] . "<br><p><br>&nbsp;&nbsp;&nbsp;&nbsp;如果您没有请求解除微博帐号绑定，请不用理会此封邮件，请直接删除！<br></p><p><br>&nbsp;&nbsp;&nbsp;&nbsp;点击以下地址解除绑定<br></p><p>&nbsp;&nbsp;&nbsp;&nbsp;" . $link . "<br></p><p>&nbsp;&nbsp;&nbsp;&nbsp;如果通过点击以上链接无法访问，请将该网址复制并粘贴至新的浏览器窗口中。非常感谢您使用我们的服务。 <br></p><p><br>&nbsp;&nbsp;&nbsp;&nbsp;该邮件为系统自动发出,请不要回复,谢谢！<br></p><p><br>2345.com网址导航<br></p>http://www.2345.com/</pre>";
                        $mailbody_TXT = "您好：" . $row['username'] . ",请将该网址（" . $linkTxt . "）复制并粘贴至新的浏览器窗口中，非常感谢您使用我们的服务。";
                        $smtp = loadVendor('smtp');
                        $smtp->init($smtpConfig['server'], $smtpConfig['port'], $smtpConfig['username'], $smtpConfig['password']);
                        if (strpos($smtpemailto, '@21cn.com') !== false)
                        {
                            $mailtype = "TXT"; //邮件格式（HTML/TXT）,TXT为文本邮件
                            $smtp->sendmail($smtpemailto, $smtpConfig['email'], $mailsubject, $mailbody_TXT, $mailtype);
                        }
                        else
                        {
                            $mailtype = "HTML"; //邮件格式（HTML/TXT）,TXT为文本邮件
                            $smtp->sendmail($smtpemailto, $smtpConfig['email'], $mailsubject, $mailbody, $mailtype);
                        }
                    }
                }
            }
            else
            {
                redirect(PASSPORT_HOST . "/oauth/weibo?callback=/oauth/bind/weibo_callback");
            }
        }
    }

    /**
     * 绑定和解绑微信
     *
     * @return void
     */
    public function actionWeixin()
    {
        $oauthModel = loadModel('oauth');
        if (isset($_GET["act"]) && $_GET["act"] == "do_unbind")
        {
            $passid = intval($_GET["id"]);
            $unbindcode = htmlspecialchars($_GET["checkCode"], ENT_QUOTES, 'ISO-8859-1');
            if ($unbindcode)
            {
                $memberModel = loadModel('member');
                $memberInfo = $memberModel->read($passid);
                $memberInfo['passid'] = $passid;
                $arrOpenid = json_decode($memberInfo['openid'] ?? '', true);
                $strOpenid = $arrOpenid['weixin'] ?? '';

                $blnUnbind = $oauthModel->delBind('weixin', $passid, $unbindcode);
                if ($blnUnbind) {
                    //日志行为打点:用户信息解绑; type:USER_INFO_UNBIND; sub_type:OPENID;


                    $objLogV2Action = loadAction('logV2');
                    $objLogV2Action->report('ALL', 'USER_INFO_UNBIND', 'OPENID', $memberInfo, json_encode(['weixin' => $strOpenid]), '');
                }
            }
            $location = isset($_COOKIE['qqforward']) ? urldecode($_COOKIE['qqforward']) : PASSPORT_HOST."/member/edit_info.php";
            setcookie("qqforward", "", time() - 3600, '/');

            redirect($location);
        }
        else
        {
            $loginAction = loadAction("login");
            if (!$loginInfo = $loginAction->checkAuthCookie())
            {
                redirect('/');
            }

            if (isset($_GET['forward']))
            {
                $forward = getForwardUrl();
                setcookie('qqforward', $forward, 0, '/');
            }
            else
            {
                setcookie('qqforward', "", time() - 3600, '/');
            }
            if (isset($_GET["act"]) && $_GET["act"] == "request_unbind")
            {
                $passid = intval($loginInfo["i"]);
                $memberModel = loadModel('member');
                $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
                $userinfo = $memberModel->getUsernameByPassid($passid);
                $gid = $memberModel->getGidByPassid($passid);
                if ($gid && $gid != '100')
                {
                    //todo
                    $checkCode = md5($userinfo['username'] . time() . $passid . "checkcode");
                    $oauthModel->setUnbindCode('weixin', $passid, $checkCode);
                    $link = PASSPORT_HOST . '/oauth/bind/weixin?act=do_unbind&id=' . $passid . '&checkCode=' . $checkCode;
                    $urlJson = json_encode(array('url' => $link, 'code' => "200"));
                    echo $callback . '(' . $urlJson . ')';
                }
                else
                {
                    $urlJson = json_encode(array('url' => " ", 'code' => "400", 'passid' => $passid));
                    echo $callback . '(' . $urlJson . ')';
                }
            }
            else
            {
                redirect(PASSPORT_HOST . "/oauth/weixin?callback=/oauth/bind/weixin_callback");
            }
        }
    }

    /**
     * 微信公众号绑定和解绑
     *
     * @param string $appid appid
     *
     * @return void
     */
    public function actionWeixinMp($appid = "")
    {
        $oauthModel = loadModel('oauth');
        $appid = preg_replace('/[^\w_\.\$]/', '', $appid);


        if (isset($_GET['forward']))
        {
            $forward = getForwardUrl();
            setcookie('qqforward', $forward, 0, '/');
        }

        if (isset($_GET["act"]) && $_GET["act"] == "do_unbind")
        {
            $passid = intval($_GET["id"]);
            $unbindcode = htmlspecialchars($_GET["checkCode"], ENT_QUOTES, 'ISO-8859-1');
            if ($unbindcode)
            {
                $oauthModel->delBind('weixin', $passid, $unbindcode);
            }
            redirect(PASSPORT_HOST . '/member/edit_info.php');
        }
        else
        {
            $loginAction = loadAction("login");
            if (!$loginInfo = $loginAction->checkAuthCookie())
            {
                redirect('/');
            }
            if (isset($_GET["act"]) && $_GET["act"] == "request_unbind")
            {
                $passid = intval($loginInfo["i"]);
                $memberModel = loadModel('member');
                $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
                $userinfo = $memberModel->getUsernameByPassid($passid);
                $gid = $memberModel->getGidByPassid($passid);
                if ($gid && $gid != '100')
                {
                    //todo
                    $checkCode = md5($userinfo['username'] . time() . $passid . "checkcode");
                    $oauthModel->setUnbindCode('weixin', $passid, $checkCode);
                    $link = PASSPORT_HOST . '/oauth/bind/weixin?act=do_unbind&id=' . $passid . '&checkCode=' . $checkCode;
                    $urlJson = json_encode(array('url' => $link, 'code' => "200"));
                    echo $callback . '(' . $urlJson . ')';
                }
                else
                {
                    $urlJson = json_encode(array('url' => " ", 'code' => "400", 'passid' => $passid));
                    echo $callback . '(' . $urlJson . ')';
                }
            }
            else
            {
                redirect(
                    PASSPORT_HOST . "/oauth/weixin/mp?appid=" . $appid . "&callback=/oauth/bind/weixin_callback"
                );
            }
        }
    }

    /**
     * 微信 页游 H5页面包装APP 绑定和解绑
     *
     * @param string $appid appid
     *
     * @return void
     */
    public function actionWeixinH5App($appid = "")
    {
        $appid = preg_replace('/[^\w_\.\$]/', '', $appid);

        if (isset($_GET['forward']))
        {
            $forward = getForwardUrl();
            setcookie('qqforward', $forward, 0, '/');
        }

        if (isset($_GET["act"]) && $_GET["act"] == "do_unbind")
        {
            $passid = intval($_GET["id"]);
            // 暂不支持 weixin h5 app 解绑
            /*
            $oauthModel = loadModel('oauth');
            $unbindcode = htmlspecialchars($_GET["checkCode"], ENT_QUOTES, 'ISO-8859-1');
            if ($unbindcode)
            {
                $oauthModel->delBind('weixin', $passid, $unbindcode);
            }
            redirect(PASSPORT_HOST . '/member/edit_info.php');
            */
        }
        else
        {
            $loginAction = loadAction("login");
            if (!$loginInfo = $loginAction->checkAuthCookie())
            {
                redirect('/');
            }
            if (isset($_GET["act"]) && $_GET["act"] == "request_unbind")
            {
                $passid = intval($loginInfo["i"]);
                // 暂不支持 weixin h5 app 解绑
                /*
                $oauthModel = loadModel('oauth');
                $memberModel = loadModel('member');
                $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
                $userinfo = $memberModel->getUsernameByPassid($passid);
                $gid = $memberModel->getGidByPassid($passid);
                if ($gid && $gid != '100')
                {
                    //todo
                    $checkCode = md5($userinfo['username'] . time() . $passid . "checkcode");
                    $oauthModel->setUnbindCode('weixin', $passid, $checkCode);
                    $link = PASSPORT_HOST . '/oauth/bind/weixin?act=do_unbind&id=' . $passid . '&checkCode=' . $checkCode;
                    $urlJson = json_encode(array('url' => $link, 'code' => "200"));
                    echo $callback . '(' . $urlJson . ')';
                }
                else
                {
                    $urlJson = json_encode(array('url' => " ", 'code' => "400", 'passid' => $passid));
                    echo $callback . '(' . $urlJson . ')';
                }
                */
            }
            else
            {
                $oauthConfig = Config::get('oauth');
                $oauthConfig = $oauthConfig["weixin"];
                $oauthConfig = isset($oauthConfig[$appid]) ? $oauthConfig[$appid] : $oauthConfig;

                // for CSRF
                $state = md5(time() . MD5KEY);
                // setcookie('redirect_uri', $callbackUrl, 0, '/');
                setcookie('oauth_state', $state, 0, '/');
                $params = array(
                    "appid" => $oauthConfig["appid"],
                    "state" => $state,
                );

                // build callback url
                if (isset($_GET['mid']))
                {
                    $params["mid"] = \Common\Utils\Url::getStringParam("mid");
                }
                $callbackUrl = PASSPORT_HOST . '/oauth/bind/weixin_callback';
                $callbackUrl .= "?" . http_build_query($params);

                $this->echoResponse($callbackUrl);
                exit;
            }
        }
    }

    public function actionQqCallback()
    {
        if (!empty($_GET["rewrite"]) && !empty(dev::parasDomain($_GET["rewrite"]))) {
            $locationParam = $_GET;
            unset($locationParam["rewrite"]);
            $uri = parse_url($_SERVER['REQUEST_URI']);
            $locationUrl = $_GET["rewrite"] . "/" . $uri['path'] . "?" . http_build_query($locationParam);
            header("Location: {$locationUrl}");
            exit;
        }
        $loginAction = loadAction("login");
        if (!$loginInfo = $loginAction->checkAuthCookie())
        {
            redirect('/');
        }
        $passid = $loginInfo["i"];
        $username = $loginInfo["n"];
        setcookie('oauth_state', null, time() - 3600, '/');
        setcookie('redirect_uri', null, time() - 3600, '/');
        if (empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state'])
        {
            redirect('/');
        }
        $oauthAction = loadAction('oauth');
        $result = $oauthAction->qqCallback($_REQUEST['code'], $_COOKIE["redirect_uri"]);
        $location = isset($_COOKIE['qqforward']) ? urldecode($_COOKIE['qqforward']) : PASSPORT_HOST."/member/edit_info.php";
        setcookie("qqforward", "", time() - 3600, '/');
        if ($result)
        {
            $openid = $result['openid'];
            $nickname = $result['nickname'];
            $gender = $result['gender'];
            $oauthModel = loadModel('oauth');
            $row = $oauthModel->getBind($openid, 'qq');
            if ($row)
            {
                if ($row["passid"] != $passid)
                {
                    $location = PASSPORT_HOST . "/member/edit_info.php?msg=qqbind";
                }
            }
            else
            {
                $blnBind = $oauthModel->setBind('qq', $passid, $openid, $nickname, $username);
                $memberModel = loadModel('member');
                $memberInfo = $memberModel->read($passid);
                $memberInfo['passid'] = $passid;
                $objLogV2Action = loadAction('logV2');
                if ($blnBind) {
                    //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:OPENID、NICKNAME;
                    $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'OPENID', $memberInfo, '', json_encode(['qq' => $openid]));
                    $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'NICKNAME', $memberInfo, '', $nickname);
                }

                if ($memberInfo['gender'] == 0 && $gender != 0)
                {
                    $memberModel->edit($passid, array('gender' => $gender));
                    $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'GENDER', $memberInfo, '', $gender);
                }
            }
        }
        redirect($location);
    }

    public function actionWeiboCallback()
    {
        $loginAction = loadAction("login");
        if (!$loginInfo = $loginAction->checkAuthCookie())
        {
            redirect('/');
        }
        $passid = $loginInfo["i"];
        $username = $loginInfo["n"];
        setcookie('oauth_state', null, time() - 3600, '/');
        setcookie('redirect_uri', null, time() - 3600, '/');
        if (empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state'])
        {
            redirect('/');
        }
        $oauthAction = loadAction('oauth');
        $result = $oauthAction->weiboCallback($_REQUEST['code'], $_COOKIE["redirect_uri"]);

        $location = isset($_COOKIE['qqforward']) ? urldecode($_COOKIE['qqforward']) : PASSPORT_HOST."/member/edit_info.php";
        setcookie("qqforward", "", time() - 3600, '/');

        if ($result)
        {
            $openid = $result['openid'];
            $nickname = $result['nickname'];
            $gender = $result['gender'];
            $oauthModel = loadModel('oauth');
            $row = $oauthModel->getBind($openid, 'weibo');
            if ($row)
            {
                if ($row["passid"] != $passid)
                {
                    $location = PASSPORT_HOST . "/member/edit_info.php?msg=weibobind";
                }
            }
            else
            {
                $oauthModel->setBind('weibo', $passid, $openid, $nickname, $username);
                $memberModel = loadModel('member');
                $memberInfo = $memberModel->read($passid);
                if ($memberInfo['gender'] == 0 && $gender != 0)
                {
                    $memberModel->edit($passid, array('gender' => $gender));
                }
            }
        }
        redirect($location);
    }

    /**
     * 微信绑定callback
     *
     * @return void
     */
    public function actionWeixinCallback()
    {
        if (!empty($_GET["rewrite"]) && !empty(dev::parasDomain($_GET["rewrite"]))) {
            $locationParam = $_GET;
            unset($locationParam["rewrite"]);
            $uri = parse_url($_SERVER['REQUEST_URI']);
            $locationUrl = $_GET["rewrite"] . "/" . $uri['path'] . "?" . http_build_query($locationParam);
            header("Location: {$locationUrl}");
            exit;
        }
        $appid = preg_replace('/[^\w_\.\$]/', '', $_GET["appid"]);
        $loginAction = loadAction("login");
        if (!$loginInfo = $loginAction->checkAuthCookie())
        {
            redirect('/');
        }
        $passid = $loginInfo["i"];
        $username = $loginInfo["n"];
        setcookie('oauth_state', null, time() - 3600, '/');
        setcookie('redirect_uri', null, time() - 3600, '/');
        if (empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state'])
        {
            redirect('/');
        }
        $oauthAction = loadAction('oauth');
        /** @see OauthAction::weixinCallback() */
        $result = $oauthAction->weixinCallback($_REQUEST["code"], $appid);
        $location = PASSPORT_HOST . "/member/edit_info.php";
        if (isset($_COOKIE['qqforward']))
        {
            $location = $_COOKIE['qqforward'];
        }

        if ($result)
        {
            $openid = $result['unionid'];
            $nickname = $result['nickname'];
//            $gender = $result['gender'];
            $oauthModel = loadModel('oauth');
            $row = $oauthModel->getBind($openid, 'weixin');
            if ($row)
            {
                if ($row["passid"] != $passid)
                {
                    $location = PASSPORT_HOST . "/member/edit_info.php?msg=weixinbind";
                }
            }
            else
            {
                $blnBind = $oauthModel->setBind('weixin', $passid, $openid, $nickname, $username);
                if ($blnBind) {
                    //日志行为打点:用户信息添加; type:USER_INFO_ADD; sub_type:OPENID、NICKNAME;
                    $memberModel = loadModel('member');
                    $memberInfo = $memberModel->read($passid);
                    $memberInfo['passid'] = $passid;

                    $objLogV2Action = loadAction('logV2');
                    $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'OPENID', $memberInfo, '', json_encode(['weixin' => $openid]));
                    $objLogV2Action->report('ALL', 'USER_INFO_ADD', 'NICKNAME', $memberInfo, '', $nickname);
                }
            }
        }
        redirect($location);
    }

    /**
     * 收集注册登录信息,第三方登录
     *
     * @param string $oauthType $oauthType
     * @param string $source $source
     * @param string $type $type
     * @param int $status
     */
    private function collectOauth($oauthType, $source, $type, $status = 1)
    {
        $accountType = \Service\AccountStat\Consts\AccountStat::getOauth($oauthType);
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        \Service\AccountStat\AccountStat::collect($type, $clientType, $accountType, $status, $source);
    }
}

function checkOAuthCheckUrl($passid, $timestamp, $code)
{
    $api_access_code = TOKENKEY;
    return $code == md5($timestamp . $api_access_code . $passid);
}
