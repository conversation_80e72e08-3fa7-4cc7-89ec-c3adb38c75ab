<?php

use \Service\AccountStat\Consts\AccountStat;

class WeixinController extends Controller
{
    /**
     * WeixinController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->startSession();
    }

    /**
     * 微信登录链接, 设置 cookie
     *
     * @return void
     */
    public function actionIndex()
    {
        if (isset($_GET['forward']))
        {
            $forward = getForwardUrl();
            setcookie('qqforward', $forward, 0, '/');
            setcookie('oAuthFocusLogin', isset($_GET['focusLogin']) ? 1 : 0, 0, '/');
        }

        if (isset($_GET['callback']))
        {
            setcookie('oauthThirdCallback', $_GET['callback'], 0, '/');
        }
        else
        {
            if (isset($forward))
            {
                preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $forward, $matches);
                if ($matches[0] == '9991.com')
                {
                    setcookie('oauthThirdCallback', "http://login.9991.com/webapi/sso/login", 0, '/');
                }
                else
                {
                    setcookie('oauthThirdCallback', "", time() - 3600, '/');
                }
            }
        }
        $midParam = '';
        if (isset($_GET['mid']))
        {
            $midParam = '&mid=' . \Common\Utils\Url::getStringParam("mid");
        }

        redirect(PASSPORT_HOST . "/oauth/weixin/?callback=/weixin/callback{$midParam}");
    }

    /**
     * 微信公众号登录链接, 设置 cookie
     *
     * @param string $appid appid
     *
     * @return void
     */
    public function actionMp($appid = "")
    {
        $appid = preg_replace('/[^\w_\.\$]/', '', $appid);

        if (isset($_GET['forward']))
        {
            $forward = getForwardUrl();
            setcookie('qqforward', $forward, 0, '/');
            setcookie('oAuthFocusLogin', isset($_GET['focusLogin']) ? 1 : 0, 0, '/');
        }
        if (isset($_GET['callback']))
        {
            setcookie('oauthThirdCallback', $_GET['callback'], 0, '/');
        }
        else
        {
            if (isset($forward))
            {
                preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $forward, $matches);
                if ($matches[0] == '9991.com')
                {
                    setcookie('oauthThirdCallback', "http://login.9991.com/webapi/sso/login", 0, '/');
                }
                else
                {
                    setcookie('oauthThirdCallback', "", time() - 3600, '/');
                }
            }
        }
        redirect(PASSPORT_HOST . '/oauth/weixin/mp?callback=/weixin/callback&appid=' . $appid);
    }

    /**
     * 微信登录 for h5 app, 设置安全 state
     *
     * @param string $appid appid
     *
     * @return void
     */
    public function actionH5App($appid = "")
    {
        $appid = preg_replace('/[^\w_\.\$]/', '', $appid);
        if (isset($_GET['forward']))
        {
            $forward = getForwardUrl();
            setcookie('qqforward', $forward, 0, '/');
            setcookie('oAuthFocusLogin', isset($_GET['focusLogin']) ? 1 : 0, 0, '/');
        }
        if (isset($_GET['callback']))
        {
            setcookie('oauthThirdCallback', $_GET['callback'], 0, '/');
        }
        else
        {
            if (isset($forward))
            {
                preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $forward, $matches);
                if ($matches[0] == '9991.com')
                {
                    setcookie('oauthThirdCallback', "http://login.9991.com/webapi/sso/login", 0, '/');
                }
                else
                {
                    setcookie('oauthThirdCallback', "", time() - 3600, '/');
                }
            }
        }
        $oauthConfig = Config::get('oauth');
        $oauthConfig = $oauthConfig["weixin"];
        $oauthConfig = isset($oauthConfig[$appid]) ? $oauthConfig[$appid] : $oauthConfig;

        // for CSRF
        $state = md5(time() . MD5KEY);
//        setcookie('redirect_uri', $callbackUrl, 0, '/');
        setcookie('oauth_state', $state, 0, '/');
        $params = array(
            "appid"         => $oauthConfig["appid"],
            "state"         => $state,
        );

        // build callback url
        if (isset($_GET['mid']))
        {
            $params["mid"] = \Common\Utils\Url::getStringParam("mid");
        }
        $callbackUrl = PASSPORT_HOST . '/weixin/callback';
        $callbackUrl .= "?" . http_build_query($params);

        $this->echoResponse($callbackUrl);
        exit;
    }

    /**
     * 微信展示二维码登录, 设置安全 state
     *
     * @return void
     */
    public function actionJsLoginQr()
    {
        $allOauthConfig = Config::get('oauth');
        $oauthConfig = $allOauthConfig['weixin'];

        if (isset($_GET['forward']))
        {
            $forward = getForwardUrl();
            setcookie('qqforward', $forward, 0, '/');
            setcookie('oAuthFocusLogin', isset($_GET['focusLogin']) ? 1 : 0, 0, '/');
        }
        // 添加第三方登录... 2345.cn 类似 km.com ym.com 等等, 非 2345.com 域下的域名

        // for CSRF
        $state = md5(time() . MD5KEY);
        setcookie('oauth_state', $state, 0, '/');
        $params = array(
            "appid" => $oauthConfig["appid"],
            "state" => $state,
        );

        // build callback url
        if (isset($_GET['mid']))
        {
            $params["mid"] = \Common\Utils\Url::getStringParam("mid");
        }
        $redirectUri = (isHttps() ? "https" : "http") . '://passport.2345.com/weixin/callback';
        $redirectUri .= "?" . http_build_query($params);

        $this->codeResponse(200, array(
            "appid" => $oauthConfig["appid"],
            "state" => $state,
            "redirectUri" => $redirectUri,
        ));
        exit;
    }

    /**
     *
     * 微信公众号, 仅仅获取openid, 设置 cookie
     *
     * @param string $appid appid
     *
     * @return void
     */
    public function actionMpOpenId($appid = "")
    {
        $appid = preg_replace('/[^\w_\.\$]/', '', $appid);
        if (isset($_GET['forward']))
        {
            $forward = getForwardUrl();
            setcookie('qqforward', $forward, 0, '/');
        }
        if (isset($_GET['callback']))
        {
            setcookie('oauthThirdCallback', $_GET['callback'], 0, '/');
        }
        else
        {
            if (isset($forward))
            {
                preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $forward, $matches);
                if ($matches[0] == '9991.com')
                {
                    setcookie('oauthThirdCallback', "http://login.9991.com/webapi/sso/login", 0, '/');
                }
                else
                {
                    setcookie('oauthThirdCallback', "", time() - 3600, '/');
                }
            }
        }
        redirect(PASSPORT_HOST . '/oauth/weixin/mpOpenId?callback=/weixin/mpCallback&appid=' . $appid);
    }

    /**
     *
     * 微信公众号, 仅仅获取openid, 设置 cookie wxopenid 到根域名
     *
     * @return void
     */
    public function actionMpCallback()
    {
        $appid = preg_replace('/[^\w_\.\$]/', '', $_GET["appid"]);

        setcookie('oauth_state', null, time() - 3600, '/');
        setcookie('redirect_uri', null, time() - 3600, '/');
        if (isset($_COOKIE['qqforward']) && strpos($_COOKIE['qqforward'], "http://www.2345.com") === 0 &&
            $_COOKIE['oAuthFocusLogin']
        )
        {
            redirect($_COOKIE['qqforward']);
        }

        if (empty($_REQUEST['code']) || empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state'])
        {
            redirect('/');
        }
        setcookie('oauth_type', 'weixin', time() + 3600 * 24 * 30, '/', '2345.com');
        if (isset($_COOKIE['qqforward']))
        {
            setcookie('qqforward', '', 0, '/');
            $redirect = $_COOKIE['qqforward'];
        }
        else
        {
            $redirect = PASSPORT_HOST;
        }

        $oauthAction = loadAction('oauth');
        /** @see OauthAction::weixinCallback() */
        $result = $oauthAction->weixinCallback($_REQUEST["code"], $appid, false);

        if ($result && isset($result["openid"]))
        {
            setcookie('wxopenid', $result['openid'], 2522880000, '/', "2345.com");
        }
        //redirect($redirect . "?openid=".$result['openid']);
        redirect($redirect);
    }

    /**
     * 微信callback
     *
     * @return mixed
     */
    public function actionCallback()
    {
        if (!empty($_GET["rewrite"]) && !empty(dev::parasDomain($_GET["rewrite"]))) {
            $locationParam = $_GET;
            unset($locationParam["rewrite"]);
            $uri = parse_url($_SERVER['REQUEST_URI']);
            $locationUrl = $_GET["rewrite"] . "/" . $uri['path'] . "?" . http_build_query($locationParam);
            header("Location: {$locationUrl}");
            exit;
        }
        $appid = preg_replace('/[^\w_\.\$]/', '', $_GET["appid"]);
        $chrome = \Common\Utils\Http::isChrome();
        if ($chrome && isset($_COOKIE['qqforward'])) {
            $successHost = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
            switch ($successHost) {
                case 'www.hao774.com':
                    if ($_SERVER['HTTP_HOST'] != 'passport.hao774.com') {
                        redirect("http://passport.hao774.com{$_SERVER['REQUEST_URI']}&skip_oauth=1&qq_forward={$_COOKIE['qqforward']}");
                        exit;
                    }
                    break;
                // case 'passport.7255.com':
                //     if ($_SERVER['HTTP_HOST'] != 'passport.7255.com') {
                //         header("Location: http://passport.7255.com{$_SERVER['REQUEST_URI']}" );
                //     }
                //     break;
                // default:
                //     break;
            }
        }
        if (isset($_COOKIE['qqforward']) && strpos($_COOKIE['qqforward'], "http://www.2345.com") === 0 &&
            $_COOKIE['oAuthFocusLogin']
        )
        {
            setcookie('oauth_state', null, time() - 3600, '/');
            setcookie('redirect_uri', null, time() - 3600, '/');
            redirect($_COOKIE['qqforward']);
        }
        if ((empty($_COOKIE['oauth_state']) || $_REQUEST['state'] != $_COOKIE['oauth_state']) && empty($_GET['skip_oauth']))
        {
            setcookie('oauth_state', null, time() - 3600, '/');
            setcookie('redirect_uri', null, time() - 3600, '/');

            $redirectUrl = "/";
            $host = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
            preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $host, $matches);
            if ($matches[0] == "2345.com" || $matches[0] == "2345.cn")
            {
                $redirectUrl = $_COOKIE['qqforward'];
            }
            redirect($redirectUrl);
        }

        $oauthAction = loadAction('oauth');
        /** @see OauthAction::weixinCallback() */
        $result = $oauthAction->weixinCallback($_REQUEST["code"], $appid);
        if ($result)
        {
            //注意这里需要使用unionid, 同一个开发者账号旗下不同的app
            $openid = $result['unionid'];
            $accessToken = $result['access_token'];

            // 设置 wxopenid, 微信登录不同的账号会清理cookie
            if ($result && isset($result["openid"]))
            {
                setcookie('wxopenid', $result['openid'], 2522880000, '/', "2345.com");
            }

            $gender = $result['gender'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            $return = $oauthModel->getBind($openid, 'weixin');
            if ($return["passid"] > 0)
            {
                //收集注册登录信息
                $st = true;
                $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
                if (!$source)
                {
                    $source = "login.2345.com";
                }
                \Service\AccountStat\AccountStat::collect(
                    AccountStat::TP_LOGIN,
                    AccountStat::CTP_WEB,
                    AccountStat::AC_TP_OAUTH_WEIXIN,
                    $st,
                    $source
                );

                $passid = $return["passid"];
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                $result = $memberModel->read($passid);
                if ($result['locked'] == 2) {
                    //此账号已被冻结，请联系客服
                    $redirectUrl = isset($_GET["rewrite"]) ? $_GET["rewrite"] : PASSPORT_HOST;
                    redirect($redirectUrl);
                }
                if ($result['gender'] == 0 && $gender != 0)
                {
                    $memberModel->edit($passid, array('gender' => $gender));
                }
                $result["m_uid"] = unserialize($result['m_uid']);
                $uid = $result['m_uid']['1'];
                $username = $result['username'];
                $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
                // 修复QQ用户名 带 # 的
                $loginAction = loadAction('login');
                if (strpos($username, "#qq#") !== false)
                {
                    list($needModifyName, $username) = $loginAction->repairQQOldOAuthUser($passid, $username, $uid);
                    $userMod = 100;
                }
                else
                {
                    $needModifyName = 0;
                }

                // 设置登录cookie
                $cTime = time() + 3600 * 24 * 30;
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                $cookie['need_modify_name'] = $needModifyName;
                $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);

                // 设置其他域名的登录cookie： 2345.cn等
                $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
                setcookie('oauth_state', null, time() - 3600, '/');
                setcookie('redirect_uri', null, time() - 3600, '/');
                $pageArray = $loginAction->getRedirectParams($thirdCallbackCookies);
                return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $pageArray);
            }
            else
            {
                setcookie('oauth_type', 'weixin', time() + 3600 * 24 * 30, '/', DOMAIN);
                setcookie('openid', $openid, 0, '/');
                setcookie('access_token', $accessToken, 0, '/');

                $redirect = PASSPORT_HOST . '/oauth/bind';
            }
        }
        else
        {
            setcookie('oauth_state', null, time() - 3600, '/');
            setcookie('redirect_uri', null, time() - 3600, '/');

            //收集注册登录信息
            $st = false;
            $source = parse_url($_COOKIE['qqforward'], PHP_URL_HOST);
            if (!$source)
            {
                $source = "login.2345.com";
            }
            \Service\AccountStat\AccountStat::collect(
                AccountStat::TP_LOGIN,
                AccountStat::CTP_WEB,
                AccountStat::AC_TP_OAUTH_WEIXIN,
                $st,
                $source
            );

            if (isset($_COOKIE['qqforward']))
            {
                setcookie('qqforward', '', 0, '/');
                $redirect = $_COOKIE['qqforward'];
            }
            else
            {
                $redirect = PASSPORT_HOST;
            }
        }
        redirect($redirect);
    }


    /**
     * 微信app登录SDK
     *
     * @return mixed
     */
    public function actionLoginSDK()
    {
        if (RUNMODE != 'development' && !HTTPS)
        {
            $this->codeResponse(4000, "", '请求拒绝');
        }
        $necessaryField = array(
            'appid',
            'mid',
            'wxCode',
            'clientVer',
            'timestamp',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );

        list($appid, $mid, $wxCode) = $this->getParamAndAutoDeny($necessaryField);
        $checkLogin = loadAction('checkLoginForClient');
        //验证fToken
        /** @see CheckLoginForClientAction::checkFToken() */
        if (!$checkLogin->checkFToken())
        {
            $checkLogin->clearFInfo();
            $this->codeResponse(4004, "", "请求拒绝");
        }
        $oauthAction = loadAction('oauth');
        /** @see OauthAction::weixinCallback() */
        $result = $oauthAction->weixinCallback($wxCode, $appid);

        if ($result)
        {
            //注意这里需要使用unionid, 同一个开发者账号旗下不同的app
            $openid = $result['unionid'];
            $gender = $result['gender'];
            $nickname = $result['nickname'];
            $memberModel = loadModel('member');
            $oauthModel = loadModel('oauth');
            /** @see OauthModel::getBind() */
            $return = $oauthModel->getBind($openid, 'weixin');

            if ($return["passid"] > 0)
            {
                //收集登录信息
                $st = true;
                $source = $mid;
                \Service\AccountStat\AccountStat::collect(
                    AccountStat::TP_LOGIN,
                    AccountStat::CTP_APP,
                    AccountStat::AC_TP_OAUTH_WEIXIN,
                    $st,
                    $source
                );

                $passid = $return["passid"];
                $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                /** @see MemberModel::read */
                $result = $memberModel->read($passid);
                $userinfo = $result;
                $result["m_uid"] = unserialize($result['m_uid']);
                $uid = $result['m_uid']['1'];
                $username = $result['username'];
                $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
                $loginAction = loadAction('login');
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                foreach ($userinfo as $key => $value)
                {
                    $userinfo[$key] = mb_convert_encoding($value, 'UTF-8', 'GBK');
                }

                $data = array(
                    'I'        => $cookie['I'],
                    'userInfo' => $userinfo,
                );
                $this->codeResponse(200, $data, '登录成功');
            }
            else
            {
                $memberModel = loadModel('member');
                $clientIp = get_client_ip();

                /** @see MemberModel::regOAuth() */
                $result = $memberModel->regOAuth('weixin', $openid, $nickname, $clientIp, '', $gender);
                //防止变量被覆盖
                $passid = $result['passid'];
                if ($result)
                {
                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_REG,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_WEIXIN,
                        1,
                        $mid
                    );

                    $memberModel->setLogin($passid, date('Y-m-d H:i:s'), get_client_ip());
                    /** @see MemberModel::read */
                    $result = $memberModel->read($passid);
                    $userinfo = $result;
                    $result["m_uid"] = unserialize($result['m_uid']);
                    $uid = $result['m_uid']['1'];
                    $username = $result['username'];
                    $userMod = $result['gid'] % 100 == 0 ? $result['gid'] : 0;
                    $loginAction = loadAction('login');
                    $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
                    foreach ($userinfo as $key => $value)
                    {
                        $userinfo[$key] = mb_convert_encoding($value, 'UTF-8', 'GBK');
                    }

                    $data = array(
                        'I'        => $cookie['I'],
                        'userInfo' => $userinfo,
                    );
                    $this->codeResponse(200, $data, '登录成功');
                }
                else
                {
                    \Service\AccountStat\AccountStat::collect(
                        AccountStat::TP_REG,
                        AccountStat::CTP_APP,
                        AccountStat::AC_TP_OAUTH_WEIXIN,
                        0,
                        $mid
                    );
                    $this->codeResponse(202, $result, '微信绑定失败');
                }
            }
        }
        else
        {
            $this->codeResponse(201, $result, '获取微信授权信息失败');
        }
    }

}
