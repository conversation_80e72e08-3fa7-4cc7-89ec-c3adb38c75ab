
<!doctype html>
<html>
<head>
    <meta charset="gb2312">
    <meta name="author" content="weblol">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,minimal-ui"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
    <meta name="apple-mobile-web-app-title" content="2345用户中心">
    <meta content="telephone=no" name="format-detection" />
    <meta content="email=no" name="format-detection" />
    <title>爱奇艺vip会员代金券使用说明</title>
    <link rel="stylesheet" href="/css/wechat/iqiyiDiscount/public.css{{$pageArray.static}}">
    <style>
        .direction .lists .list1 .first, .direction .lists .list2 .first {
            height: 7.90625rem;
            background: url(/images/wechat/iqiyiDiscount/bg_011.png);
            background-size: 100%;
        }

    </style>
</head>
<body ontouchstart="">
<div class="direction clearfix">
    <div class="tab clearfix">
        <div class="tab1 item cur"><i class="phone-icon icon"></i>手机使用</div>
        <div class="tab2 item"><i class="pc-icon icon"></i>电脑使用</div>
    </div>
    <div class="lists">
        <div class="list1 list">
            <div class="box first">
                <p><b>进入爱奇艺官方：</b></p>
                <P>使用浏览器进入爱奇艺（www.iqiyi.com）,进入个人主页，点击开通VIP。</P>

            </div>
            <div class="box second">
                <p>
                    选择开通黄金VIP会员，勾选“到期后自动续费，可随时取消”，点击代金券↓
                </p>
                <p><img src="/images/wechat/iqiyiDiscount/img_02.jpg{{$pageArray.static}}" alt=""></p>
                <p>
                    输入确定兑换代金券码↓
                </p>
                <p><img style="margin: 5px auto" src="/images/wechat/iqiyiDiscount/img_03.png{{$pageArray.static}}" alt=""></p>
                <p >
                    如已绑定过代金券，则点击”选择代金券“，确认后选择支付方式完成支付。
                </p>
            </div>
            <div class="rule"  style="padding-top: 1.9rem">
                <p><b>注意事项： </b></p>
                <p>本优惠券不能跟爱奇艺会员其他优惠有活动一起使用，使用截止至2017年12月31日。</p>
                <p>本次微信领券活动最终解释权归上海二三四五网络科技有限公司所有</p>
            </div>
        </div>
        <div class="list2 list" style="display:none;">
            <div class="box first">
                <p><b>进入爱奇艺官方：</b></p>
                <P>使用浏览器进入爱奇艺（www.iqiyi.com）并登录。</P>

            </div>
            <div class="box second">
                <p>
                    登录后，选择开通黄金VIP会员，勾选某个时长的VIP会员，点击“兑换代金券”，输入“代金券码”，点击“使用，即可抵扣相应价格。
                </p>
                <p><img src="/images/wechat/iqiyiDiscount/img_01.jpg{{$pageArray.static}}" alt=""></p>
            </div>
            <div class="rule" style="padding-top: 1.9rem">
                <p>注意事项： </p>
                <p>本优惠券不能跟爱奇艺会员其他优惠有活动一起使用，使用截止至2017年12月31日。</p>
                <p>本次微信领券活动最终解释权归上海二三四五网络科技有限公司所有</p>
            </div>
        </div>
    </div>
</div>
<script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
<script type="text/javascript">
    var oItem = document.getElementsByClassName("item");
    var oList = document.getElementsByClassName("list");
    for(var i=0; i < oItem.length ; i++){
        oItem[i]["index"] = i;
        oItem[i].onclick = function(){
            for(var j=0; j < oItem.length ; j++){
                console.log(this.parentNode.children[j]);
                this.parentNode.children[j].className = "tab"+this["index"]+" item";
                oList[j].style.display = "none";
            }
            oList[this["index"]].style.display = "block";
            this.parentNode.children[this["index"]].className = "tab"+this["index"]+" item cur";
        }
    }

    wx.config({
        debug: false,
        appId: '{{$pageArray.appid}}',
        timestamp:{{$pageArray.signUse.timestamp}},
        nonceStr: '{{$pageArray.signUse.noncestr}}',
        signature: '{{$pageArray.signRes}}',
        jsApiList: ['onMenuShareTimeline','onMenuShareAppMessage','hideMenuItems']
    });
    wx.ready(function () {
        wx.onMenuShareTimeline({
            title: '您有一张未领取的爱奇艺vip会员代金券',
            link: '{{$pageArray.url}}',
            imgUrl: 'http://passport.2345.com/images/wechat/iqiyiDiscount/share.png',
            success: function () {
                $.ajax({
                    type: "GET",
                    url: "/wechat/iqiyiDiscount/share?type=timeline"
                });
            }
        });
        wx.hideMenuItems({
            menuList: ['menuItem:originPage','menuItem:copyUrl','menuItem:openWithSafari','menuItem:share:qq','menuItem:share:QZone']
        });
        wx.onMenuShareAppMessage({
            title: '您有一张未领取的爱奇艺vip会员代金券',
            link: '{{$pageArray.url}}',
            desc: '外面这么热，不在家好好看剧吗？2345用户中心送你爱奇艺vip会员代金券，海量大片随你看~\n',
            imgUrl: 'http://passport.2345.com/images/wechat/iqiyiDiscount/share.png',
            success: function () {
                $.ajax({
                    type: "GET",
                    url: "/wechat/iqiyiDiscount/share?type=friend"
                });
            }
        });
    });
</script>
</body>
</html>