{{include file="header.tpl.html"}}
<div class="wrap">
    <div class="box clearfix">
        <div class="tit clearfix">
            <span class="titleft"></span>
            <span class="titright"></span>
            <h2><a href="edit_info.php">个人资料</a> &gt; 修改头像</h2>
        </div>
        <div class="boxbd clearfix">
            <table width="100%">
                <tbody><tr align="center">
                        <td width="10%">&nbsp;</td>
                        <td valign="center" align="center">
                            <img width="81" height="81" onerror="this.src='avatar/default.gif';" src="{{$pageArray.avatar}}" id="photo">
                            <br>
                            当前头像
                        </td>
                        <td align="left">
                            <div id="altContent"></div>
                            <script type="text/javascript" src="/js/FaustCplus/swfobject.js"></script>
                            <script type="text/javascript">
                                var forward = '{{$pageArray.forward}}';
                                function uploadevent(status) {
                                    status += '';
                                    switch (status) {
                                        case '1':
                                            var time = new Date().getTime();
                                            document.getElementById('photo').src = "{{$pageArray.avatar}}?" + time;
                                            alert("头像修改成功！");
                                            try {
                                                if (window.external.RCCoralOnlineFavPage("DetailVersion") >= 3.0 && window.external.RCCoralOnlineFavPage("passid") && '{{$smarty.const.PASSID}}' == window.external.RCCoralOnlineFavPage("passid")) {
                                                    window.external.RCCoralOnUserHeadImageChanged();
                                                }
                                            } catch (e) {
                                                try {
                                                    if (chrome.sync.onlineFavPage("passid") && '{{$smarty.const.PASSID}}' == chrome.sync.onlineFavPage("passid")) {
                                                        chrome.sync.onUserHeadImageChanged();
                                                    }
                                                } catch (e) {
                                                }
                                            }
                                            if(forward!=''){
                                                window.location = forward;
                                            }else{
                                                window.location.reload();
                                            }
                                            break;
                                        case '-1':
                                            window.location = "/member/edit_info.php";
                                            break;
                                        case '-2':
                                            alert("上传失败！");
                                            window.location.reload();
                                            break;
                                        default:
                                            alert(typeof(status) + ' ' + status);
                                    }
                                }
                                function langFunc() {
                                    return {
                                        "CX0189": "您上传的头像会自动生成三种尺寸\n请注意中小尺寸的头像是否清晰",
                                        "CX0193": "仅支持JPG、GIF、PNG图片文件，且文件小于2M"
                                    };
                                }
                                var flashvars = {
                                    "jsfunc": "uploadevent",
                                    "jslang": "langFunc",
                                    "imgUrl": "{{$pageArray.imgUrl}}?" + new Date().getTime(),
                                    "pid": "75642723",
                                    "uploadSrc": false,
                                    "showBrow": true,
                                    "showCame": true,
                                    "uploadUrl": "{{$pageArray.uploadUrl}}",
                                    "uploadTmpUrl": "{{$pageArray.uploadTmpUrl}}",
                                    "pSize": "300|300|200|200|120|120|48|48"
                                };
                                var params = {
                                    menu: "false",
                                    scale: "noScale",
                                    allowFullscreen: "true",
                                    allowScriptAccess: "always",
                                    wmode: "transparent",
                                    bgcolor: "#FFFFFF"
                                };
                                var attributes = {
                                    id: "FaustCplus"
                                };
                                swfobject.embedSWF("/js/FaustCplus/FaustCplus.swf?v=20140417", "altContent", "750", "500", "9.0.0", "expressInstall.swf", flashvars, params, attributes);
                            </script>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{{include file="footer.tpl.html"}}