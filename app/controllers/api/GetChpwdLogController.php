<?php

class GetChpwdLogController extends Controller
{

    /**
     * @return void
     */
    public function actionIndex()
    {
        if (!\Service\Security\Server::checkServerRequestRight())
        {
            die('401');
        }
        $passid = $_POST['uid'];
        $type = $_POST['type'];
        if ($type == 'plog')
        {
            $memberModel = loadModel('member');
            echo serialize($memberModel->chgPwdLog($passid));
        }
    }

}