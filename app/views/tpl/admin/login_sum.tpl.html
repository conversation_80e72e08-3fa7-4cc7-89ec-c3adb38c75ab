<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=GBK">
        <script type="text/javascript" src="/js/jquery.min.js"></script>
        <script type="text/javascript" src="/js/datepicker/WdatePicker.js"></script>
        <script type="text/javascript" src="/js/highcharts/highcharts.js"></script>
        <script type="text/javascript" src="/js/highcharts/modules/exporting.js"></script>
        <title>登录用户统计</title>
        <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
        <link rel="stylesheet" href="/css/v3/top.css">
        <link rel="stylesheet" href="/css/v3/zhanghao.css">
  
        <script type="text/javascript">
            var chart;
                    function setSeries(data){
                    for (i = 0, len = chart.series.length; i < len; i++) {
                    chart.series[0].remove();
                    }
                    for (i in data){
                    chart.addSeries(data[i]);
                    }
                    }
            $(function() {
            chart = new Highcharts.Chart({
            chart: {
            renderTo: 'container',
                    type: 'line',
                    marginRight: 130,
                    marginBottom: 50
            },
                    title: {
            text: '{{$pageArray.title}}统计',
                    x: - 20 //center
            },
                    subtitle: {
            text: '',
                    x: - 20
            },
                    xAxis: {
            categories: [{{$pageArray.dates}}]
            },
                    yAxis: {
            title: {
            text: '登录次数'
            },
                    plotLines: [{
            value: 0,
                    width: 1,
                    color: '#808080'
            }]
            },
                    tooltip: {
            valueSuffix: '次'
            },
                    legend: {
            layout: 'vertical',
                    align: 'right',
                    verticalAlign: 'top',
                    x: - 10,
                    y: 100,
                    borderWidth: 0
            },
                    series: [{
            name: '{{$pageArray.domains.all}}登录成功',
                    color: '#0000FF',
                    data: [{{$pageArray.charts.all.succ_num}}]
            }, {
            name: '{{$pageArray.domains.all}}登录失败',
                    color: '#FF0000',
                    data: [{{$pageArray.charts.all.fail_num}}]
            }]
            });
            });        </script>
    </head>
    <body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
<div class="mainWrap">
    
		<!--面包屑导航 开始-->
        <div class="location">位置：<a href="#" target="_blank">登录用户统计</a>  &gt; <span>每日登录数表</span></div>
        <!--面包屑导航 结束-->
        
        <!--Tab菜单 开始-->
        <div class="TabMenu mt10">
        	<a href="/houtai_gl/login_sum" {{if $pageArray.title == '每日登录次数表'}} class="currTabMenu" {{/if}}>每日登录数表</a>
            <a href="/houtai_gl/login_sum/hour" {{if $pageArray.title == '每3分钟登录次数表'}} class="currTabMenu" {{/if}}>每3分钟登录数表</a>
            <a href="/houtai_gl/login_sum/detail">登录数详细表</a>
        </div>
        <!--Tab菜单 结束-->
        <div id="container" style="min-width: 400px; height: 400px; margin: 0 auto"></div>
        
        <div>
            {{$pageArray.title}}：<span style="color:blue;">蓝色</span>代表登录成功，<span style="color:red;">红色</span>代表登录失败
            {{if $pageArray.title == '每日登录次数表'}}
                <a target="_blank" href="/houtai_gl/login_sum/exportExcel">导出excel表</a>
            {{else}}
                <a target="_blank" href="/houtai_gl/login_sum/exportExcelHour">导出excel表</a>
            {{/if}}
        </div>
        
        <table cellspacing="0" cellpadding="0" style=" width:2000px" class="tabMod whiteTab forFixed">
            <tr style="text-align: center;">
                <th width="80" rowspan="2">日期</th>
                {{foreach item=domains key=groupKey from=$pageArray.domainGroups name=domainGroups}}
                <th{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:#d6e4f7"{{/if}} colspan="{{$domains|@count}}">{{$groupKey}}</th>
                {{/foreach}}
            </tr>
            <tr style="text-align: center;">
                {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
                {{foreach item=domain_name key=domain from=$domains}}
                <th{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:#d6e4f7"{{/if}}><span title="{{$domain}}">{{if $domain_name}}{{$domain_name}}{{else}}{{$domain}}{{/if}}</span><input type="button" onclick="setSeries([{name: '{{$pageArray.domains.$domain}}登录成功', color: '#0000FF', data: [{{$pageArray.charts.$domain.succ_num}}]}, {name: '{{$pageArray.domains.$domain}}登录失败', color: '#FF0000', data: [{{$pageArray.charts.$domain.fail_num}}]}]);" value="图表"/></th>
                {{/foreach}}
                {{/foreach}}
            </tr>
            {{foreach item=item key=date from=$pageArray.data}}
            <tr style="text-align: center">
                <td rowspan="2">{{$date}}</td>
                {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
                {{foreach item=domain_name key=domain from=$domains}}
                <td{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:#d6e4f7"{{/if}}><span style="color:blue;">{{$item.$domain.succ_num}}</span></td>
                {{/foreach}}
                {{/foreach}}
            </tr>
            <tr style="text-align: center">
                {{foreach item=domains from=$pageArray.domainGroups name=domainGroups}}
                {{foreach item=domain_name key=domain from=$domains}}
                <td{{if $smarty.foreach.domainGroups.index % 2 == 0}} style="background-color:#d6e4f7"{{/if}}><span style="color:red;">{{$item.$domain.fail_num}}</span></td>
                {{/foreach}}
                {{/foreach}}
            </tr>
            {{/foreach}}
        </table>
        
        
        
        
	</div>

<script>

$('.navList a').each(function (){
    if (  $(this).attr('href') == '/houtai_gl/login_sum' )
    {
        $(this).addClass('select');
    }
});
</script>
        
    </body>
</html>