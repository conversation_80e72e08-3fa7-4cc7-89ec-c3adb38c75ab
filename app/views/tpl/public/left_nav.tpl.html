<div class="main main_position clearfix">
    <div class="account_left">
        <ul class="account_nav">
            <li><a {{if $pageArray.nav=='userdata'}}class="curr_account"{{/if}} href="/member/editData"><i class="icon-b png24"></i>基本资料</a>
            <li><a {{if $pageArray.nav=='password'}}class="curr_account"{{/if}} href="/member/editPassword"><i class="icon-c png24"></i>修改密码</a></li>
            {{if $pageArray.phonetype == 'bind'}}
            <li><a {{if $pageArray.nav=='bindphone'}}class="curr_account"{{/if}} href="/member/bindPhone"><i class="icon-d png24"></i>绑定手机</a></li>
            {{else}}
            <li><a {{if $pageArray.nav=='editphone'}}class="curr_account"{{/if}} href="/member/editPhone"><i class="icon-d png24"></i>修改手机</a></li>
            {{/if}}
            {{if $pageArray.emailtype == 'bind'}}
            <li><a {{if $pageArray.nav=='bindemail'}}class="curr_account"{{/if}} href="/member/bindEmail"><i class="icon-e png24"></i>绑定邮箱</a></li>
            {{else}}
            <li><a {{if $pageArray.nav=='editemail'}}class="curr_account"{{/if}} href="/member/editEmail"><i class="icon-e png24"></i>修改邮箱</a></li>
            {{/if}}
            <li><a {{if $pageArray.nav=='avatar'}}class="curr_account"{{/if}} href="/member/avatar/index"><i class="icon-a png24"></i>我的头像</a></li>
            <li><a {{if $pageArray.nav=='editOauth'}}class="curr_account"{{/if}} href="/member/editOauth" onclick="cc('D1')"><i class="icon-g png24"></i>第三方授权</a></li>

<!--            <li><a {{if $pageArray.nav=='qd'}}class="curr_account"{{/if}} href="/member/moneyPocket/index" onclick="cc('D10')"><i class="icon-h png24"></i>我的钱袋</a></li>-->

        </ul>
    </div>
    <script>
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
            var r = window.location.search.substr(1).match(reg); //匹配目标参数
            if (r != null) return unescape(r[2]); return null; //返回参数值
        }

        var mid = getUrlParam("mid") ? getUrlParam("mid") : ""

        $(".account_left .account_nav li").each(function (i, n) {
            if (mid !== "") {
                var url = $(n).find("a").attr('href') + "?mid=" + mid
                $(n).find("a").attr('href', url)

                var aHtml = $(n).find("a").html()
                if (aHtml.indexOf("我的头像") !== -1) {
                    aHtml = aHtml.replace("我的头像", "头像昵称")
                    $(n).find("a").html(aHtml)
                }
            }
        })
    </script>