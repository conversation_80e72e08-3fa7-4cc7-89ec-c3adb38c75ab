# 一、目的及概述
`目的`：统一部门内各业务方使用的日志类，方便后期质量监测平台搜集使用。<br />
`概述`：该类库只需要静态调用。

# 二、使用说明

### 包安装 

```
1. 绑定公司gitlab仓库地址: 172.16.0.73 packagist.2345.com
2. 定义日志目录的环境变量：LOG4PHP_FILE_DIR，
    方式1：putenv('/the/file/path')
    方式2：在nginx或者php.ini中定义
    方式三：如果没有定义，默认使用APPPATH/logs做为日志目录
2. 执行如下命令: composer require octopusutf8/web/log4phpGBK 
```

### 使用步骤

> 支持的日志级别

```$xslt
class LogLevel
{
    const ERROR     = 'error';
    const WARNING   = 'warning';
    const INFO      = 'info';
    const DEBUG     = 'debug';
}
```

> 以写入info级别日志为例

* 调用示例<br>
```
LoggerFacade::info("This an info log !", ['age' => 18]);
```
* 单元测试代码

```
<?php
/**
 * Copyright (c) 2019,2345
 * 摘    要：
 * 作    者：liupeng
 * 修改日期：2019.04.10
 */
namespace WebLogger\Tests;

require_once __DIR__ . '/../../vendor/autoload.php';
define("ROOT_PATH", dirname(__DIR__) . "LoggerTest.php/");
define("APPPATH", dirname(dirname(__DIR__)) . '/app');

use PHPUnit\Framework\TestCase;
use WebLogger\Facade\LoggerFacade;

class LoggerTest extends TestCase
{
    /**
     * 单元测试用例
     * @return void
     */
    public function testDemo()
    {
        $resEmergency = LoggerFacade::emergency("This an emergency log !", array('age' => 18));
        $resAlert = LoggerFacade::alert("This an alert log !", array('age' => 18));
        $resCritical = LoggerFacade::critical("This an critical log !", array('age' => 18));
        $resError = LoggerFacade::error("This an error log !", array('age' => 18));
        $resWarning = LoggerFacade::warning("This an warning log !", array('age' => 18));
        $resNotice = LoggerFacade::notice("This an notice log !", array('age' => 18));
        $resInfo = LoggerFacade::info("This an info log !");
        $resDebug = LoggerFacade::debug("This an debug log !", array('age' => 18));

        $this->assertNotFalse($resEmergency);
        $this->assertNotFalse($resAlert);
        $this->assertNotFalse($resCritical);
        $this->assertNotFalse($resError);
        $this->assertNotFalse($resWarning);
        $this->assertNotFalse($resNotice);
        $this->assertNotFalse($resInfo);
        $this->assertNotFalse($resDebug);
    }
}
```

### 请求返回说明

> 返回结果为boolean(成功: true  失败: false)

### 日志记录说明

> 日志记录在项目根目录下的 app/logs/日期.log

### 自身异常日志说明
> 记录在php.ini文件中  error_log 选项配置的文件



### 单元测试使用说明

```$xslt
$ ./vendor/bin/phpunit src/tests/LoggerTest.php
```

#三、其他定制
请企业微信联系@牛广辉




