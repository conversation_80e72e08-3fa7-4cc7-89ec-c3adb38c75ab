<!DOCTYPE HTML>
<!--[if lt IE 7]><html class="ie6"><![endif]-->
<!--[if IE 7]><html class="ie7"><![endif]-->
<!--[if IE 8]><html class="ie8"><![endif]-->
<!--[if gte IE 9]><html class="ie9"><![endif]-->
<html xmlns="http://www.w3.org/1999/html">
<head>
<meta charset="gb2312" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
<title>游侠合作绑定/注册帐号-2345网页游戏</title>
<meta content="2345网页游戏" name="Description" />
<meta content="2345网页游戏" name="Keywords" />
<link rel="stylesheet" type="text/css" href="/css/bind-account.css" />
<style type="text/css">
#myRegister .inputTxtFocus {border:1px solid #b3e6ff; }
#myRegister .inputTxtError {border:1px solid #f30; }
.footer{margin-top:40px}
.footer{width:100%;height:150px;background-color:#fafafa}
.footer-c{padding-top:20px;color:#999}
.footer-c a{color:#999}
.footer-c a:hover{color:#69cbf7}
.footer-link a{margin:0 10px;color:#333;font-size:14px}
.footer-c p{text-align:center;font:12px/26px SimSun}
</style>
</head>
<body>
    <div class="wrapper">
        <!-- header -->
        <div class="header">
            <div class="inner">
                <span class="logo-area">
                    <a href="#" class="main-logo">
                        <img src="/images/logo-ucenter.png" alt="">
                    </a>
                    <i class="line"></i>
                    <a href="#" class="sub-logo">
                        <img src="/images/logo-s-youxia.png" alt="">
                    </a>
                </span>
            </div>            
        </div>
        <div class="main">
            <div class="inner">
                <div class="mod-bind">
                    <div class="mod-bind-hd">
                        <h2 class="txt">您可直接使用2345帐号登录游戏</h2>
                        <p class="summary">请关联您的帐号</p>
                    </div>
                    <div class="mod-bind-bd">
                        <div class="g-login tab">
                            <div class="g-login-th mt20">
                                <ul class="tab-nav">
                                    <li class="cur"><a href="javascript:;">我没有2345帐号<i></i></a></li>
                                    <li><a href="javascript:;">我有2345帐号<i></i></a></li>
                                </ul>                            
                            </div>
                            <div class="g-login-bd clearfix">
                                <div class="tab-com">
                                    <div class="tab-view" style="display: block;">  
                                        <!-- 注册 -->
                                        <form action="#" method="post" id="myRegister">
                                        <ul class="ulForm clearfix">
                                            <li class="item-input">
                                                <span class="sTit">用户名/邮箱</span>
                                                <div class="tips" id="msg_username_email" style="">
                                                    <span class="sError"></span>
                                                </div>
                                                <div class="formCon">
                                                    <div class="inputTxt">
                                                        <input onBlur="return checkUsername()" name="username" type="text" id="username_email" autocomplete="off">
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="item-input">
                                                <span class="sTit">密码</span>
                                                <div class="tips" id="msg_pwd_phone" style="display: none;">
                                                    <span class="sError"></span>
                                                </div>
                                                <div class="tips" id="pwd_strong_phone" style="display: none;">
                                                    <p class="pPasswordHard level4">
                                                        <em>密码强度</em>
                                                        <i class="on"></i>
                                                        <i class="on"></i>
                                                        <i class="on"></i>
                                                        <i class="on"></i>
                                                        <em class="emStyle">极强</em>
                                                    </p>
                                                    <input name="pwd_strength_phone" type="hidden" id="pwd_strength" value="1"></div>
                                                <div class="formCon">
                                                    <div class="inputTxt">
                                                        <input onblur="passsafe('blur', 'phone'); checkRepass('phone');" onkeyup="passsafe('keyup', 'phone');" name="password" id="password_phone" type="password" autocomplete="off">
                                                        <i class="iRight" style="display: none"></i>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="item-input">
                                                <span class="sTit">确认密码</span>
                                                <div class="tips" id="msg_repassword_phone" style="display: none">
                                                    <span class="sError"></span>
                                                </div>
                                                <div class="formCon">
                                                    <div class="inputTxt">
                                                        <input onfocus="jQuery('#msg_repassword_phone').hide();" onblur="checkRepass('phone');" name="repassword" id="repassword_phone" type="password" maxlength="16" autocomplete="off">
                                                        <i class="iRight" style="display: none"></i>
                                                    </div>
                                                </div>
                                            </li>
                                            <!--
                                            <li class="item-code">
                                                <span class="sTit">请输入验证码</span>
                                                <div class="tips" id="msg_validate" style="display: none;"><span class="sError">请输入验证码</span></div>
                                                <div class="formCon">
                                                    <div class="loginCode">
                                                        <div class="inputTxt">
                                                            <input onblur="checkValidateCode()" name="validate" id="validate" type="text" value="" autocomplete="off">
                                                            <i class="iRight" style="display: none"></i>
                                                        </div>
                                                        <div class="codePic">
                                                            <span class="sPic">
                                                                <img src="/Captcha" id="Pic" alt="图片验证码" onclick="jQuery('#Pic').attr('src', '/Captcha?I' + Math.random()); return false;" width="88" height="34">
                                                            </span>
                                                            <a onclick="jQuery('#Pic').attr('src', '/Captcha?I' + Math.random()); return false;" class="right" href="#">换一换</a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            -->
                                            <li class="item-checkTxt">
                                                <span class="sTit">&nbsp;</span>
                                                <div class="tips" id="msg_agree">
                                                    <span class="sError"></span>
                                                </div>
                                                <div class="formCon">
                                                    <span class="sCheck">
                                                        <input type="checkbox" onclick="checkAgree()" checked="" class="checkbox" id="agree" name="agree">
                                                        我同意
                                                        <a href="/licence.html" target="_blank">《服务协议》</a>
                                                        、
                                                        <a href="/declare.html" target="_blank">《隐私政策》</a>
                                                    </span>
                                                </div>
                                            </li>  
                                            <li class="item-submit">
                                                <span class="sTit">&nbsp;</span>
                                                <div class="formCon">
                                                    <a type="button" class="btnStyle" href="###" onclick="return checkAll('phone');return false;">注册2345帐号</a>
                                                </div>
                                            </li>
                                        </ul>
                                        </form>
                                        <!-- 注册 end -->
                                    </div>
                                    <div class="tab-view">
                                        <!-- 登录 -->
                                        <form id="myForm" action="#" method="post" onSubmit="return myFormSub();">
                                            <div class="g-login-form">
                                                <div class="g-inputTxt">
                                                    <i class="g-icon-name"></i>
                                                    <span class="sDes">用户名/已验证邮箱</span>
                                                    <input class="ipt-txt-user" name="username" id="username" type="text" maxlength="40" autocomplete="off">
                                                </div>
                                                <div class="g-inputTxt">
                                                    <i class="g-icon-password"></i>
                                                    <span class="sDes">密码</span>
                                                    <input id="pwd" name="password" type="password" autocomplete="off">
                                                </div>
                                                <!--
                                                <div class="g-login-code">
                                                    <div class="g-inputTxt">
                                                        <span class="sDes postDes">请输入验证码</span>
                                                        <input type="text" id="check_code" name="check_code" value="" autocomplete="off">
                                                        <i class="g-iRight"></i>
                                                    </div>
                                                    <div class="codePic">
                                                        <span class="sPic g-left">
                                                            <img onclick="this.src = '/Captcha.php?' + Math.random();" src="/Captcha.php" alt="" title="" width="88" height="36" id="pic">
                                                        </span>
                                                        <a href="javascript:void(0);" onclick="document.getElementById('pic').src = '/Captcha.php?' + Math.random();  return false;" class="g-right">换一换</a>
                                                    </div>
                                                </div>
                                                -->
                                                <div class="g-error" id="form-tips-error" style=""></div>
                                                <input onclick="return myFormSub();" type="button" class="g-btn" value="绑定到该帐号">
                                            </div>
                                        </form>
                                        <!-- 登录 end -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>                
            </div>
        </div>
        <div class="footer">
            <div class="c footer-c">
                <p class="footer-link">
                    <a data-position="{&quot;e2&quot;:&quot;sydb&quot;,&quot;e3&quot;:&quot;sydb-1&quot;}" title="游戏中心" href="http://wan.2345.com/game.html" target="_blank">游戏中心</a>|
                    <a data-position="{&quot;e2&quot;:&quot;sydb&quot;,&quot;e3&quot;:&quot;sydb-2&quot;}" title="帐号安全" href="http://wan.2345.com/user.html" target="_blank">帐号安全</a>|
                    <a data-position="{&quot;e2&quot;:&quot;sydb&quot;,&quot;e3&quot;:&quot;sydb-3&quot;}" title="客服中心" href="http://wan.2345.com/kefu.html" target="_blank">客服中心</a>|
                    <a data-position="{&quot;e2&quot;:&quot;sydb&quot;,&quot;e3&quot;:&quot;sydb-4&quot;}" title="家长监控" rel="nofollow" href="http://game.2345.com/jzjh.htm" target="_blank">家长监控</a>
                </p>
                <p><a target="_blank" title="沪网文2014073号" href="http://image.wan.2345.com/static/images/2345www.jpg">沪网文2014073号</a>&#12288;<a target="_blank" title="沪ICP备********号-1" href="http://image.wan.2345.com/static/images/2345icp.jpg">沪ICP备********号-1</a></p>
                <p>抵制不良游戏&#12288;拒绝盗版游戏&#12288;注意自我保护&#12288;谨防受骗上当&#12288;适度游戏益脑&#12288;沉迷游戏伤身&#12288;合理安排时间&#12288;享受健康生活</p>
            </div>
        </div>
    </div>
</body>
</html>
<script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
<script type="text/javascript" src="/js/ui-bind-account.js"></script>
<script type="text/javascript" src="/js/zxcvbn.js"></script>
<script type="text/javascript" src="/js/login.js"></script>
<script type="text/javascript">
document.onkeydown = function(evt)
{
  var evt = window.event?window.event:evt;
　if (evt.keyCode==13) {
     checkAll('phone');
     myFormSub();
　}
}


$(".g-inputTxt input").bind({
    focus: function () {
        $(this).parent().addClass("g-inputTxt-focus");
    },
    blur: function () {
        $(this).parent().removeClass("g-inputTxt-focus");
    },
    keydown: function () {
        $(this).parent().removeClass("g-inputTxt-focus");
    }
});


$("#myForm input").bind({
    focus: function () {
        $(this).prev('.sDes').hide();
    },
    blur: function () {
        if ($(this).val() == "") {
            $(this).prev('.sDes').show();
        }
    }
});


function myFormSub() {
    var username = $("#username").val();
    var pwd = $("#pwd").val();
    if (username == "" || username == "用户名/邮箱") {
        $("#form-tips-error").html("请输入2345帐号!").show();
        $('#username').parent().addClass("g-inputTxt-error");
        return false;
    }
    else if (username.length < 2) {
        $("#form-tips-error").html("最少2个字符!").show();
        $('#username').parent().addClass("g-inputTxt-error");
        return false;
    }
    else if (username.replace(/[^\x00-\xff]/g, "**").length > 24) {
        $("#form-tips-error").html("请不要超过24个字符!").show();
        $('#username').parent().addClass("g-inputTxt-error");
        return false;
    }
    else {
        $('#username').parent().removeClass("g-inputTxt-error");
    }

    /*
    if (( $("#check_code").val() == '' || $("#check_code").val() == '请输入验证码' ) && pwd != '') {
        $('#pwd').parent().removeClass("g-inputTxt-error");
        $("#form-tips-error").html("请先输入验证码!").show();
        $('#check_code').parent().addClass("g-inputTxt-error");
        return false;
    }
    else {
        $('#check_code').parent().removeClass("g-inputTxt-error");
    }
    */

    if (pwd == "" || pwd == "密码") {
        $("#form-tips-error").html("请先输入密码!").show();
        $('#pwd').parent().addClass("g-inputTxt-error");
        return false;
    }
    else {
        $('#pwd').parent().removeClass("g-inputTxt-error");
    }

    $("#pwd").val(MD5(pwd));
    // $("#currtime").val(parseInt(new Date().getTime() / 1000));

    $.ajax({
        url: "/oauth/yxbind/login",
        data: $("#myForm").serialize(),
        type: 'POST',
        async: false,
        dataType: "JSON",
        success: function (res) {
            if (res.err > 0) {
                jQuery("#pic").attr('src', '/Captcha.php?' + Math.random());
                //$("#check_code").val('');
                $("#pwd").val('');
				$('#username').parent().addClass("g-inputTxt-error");
                $("#form-tips-error").html(res.msg).show();
                return false;
            } else {
                location.href = res.forward;
            }
        },
        timeout: 3000
    });
}

// --------------------------------------------------------------------------- //
$(".inputTxt input").bind({
    focus: function () {
        $(this).parents('.item-input,.item-code').addClass("inputTxtFocus");
        $(this).parents('.item-input,.item-code').removeClass("inputTxtError");
        $(this).closest('li').find('.tips').hide();
    },
    blur: function () {
        $(this).parents('.item-input,.item-code').removeClass("inputTxtFocus");
    },
    keydown: function () {
        $(this).parents('.item-input,.item-code').removeClass("inputTxtFocus");
    }
});

$("#myRegister input").bind({
    focus: function () {
        $(this).parents('.item-input,.item-code').children('.sTit').hide();
    },
    blur: function () {
        if ($(this).val() == "") {
            $(this).parents('.item-input,.item-code').children('.sTit').show();
        }
    }
});


function checkUsername()
{
    var username_email = $.trim($("#username_email").val());
    var isvalid = true;
    var msg_username_email = '';
    if (username_email == '')
    {
        isvalid = false;
        msg_username_email = '请输入邮箱帐号';
    }

    if (username_email.length < 2)
    {
        isvalid = false;
        msg_username_email = '用户名不能少于两个字符';
    }

    if (/^1[35678]\d{9}$/g.test(username_email))
    {
        isvalid = false;
        msg_username_email = '用户名不能为手机号码';
    }

    if (!isvalid)
    {
        $("#msg_username_email span").html(msg_username_email);
        $("#msg_username_email").show();
        $("#username_email").parents('.item-input').addClass("inputTxtError");
        return false;
    }

    var msgs = {
        301 : '用户名不能为手机号',
        303 : '用户名重名, 请换一个吧!',
        302 : '用户名长度不能大于24位'
    };
    $.ajax({
        url: "/api/youxia?username="+username_email,
        data: {},
        type: 'POST',
        async: false,
        dataType: "JSON",
        success: function (res) {
            responseCode = res;
        },
        timeout: 3000
    });

    if (responseCode != 200)
    {
        isvalid = false;
    }

    if (isvalid === true)
    {
        $("#msg_username_email span").html('');
        $("#msg_username_email").hide();
        $("#username_email").parents('.item-input').removeClass('inputTxtError');
        return true;
    }
    else
    {
        $("#msg_username_email span").html(msgs[responseCode]);
        $("#msg_username_email").show();
        $("#username_email").parents('.item-input').addClass("inputTxtError");
        return false;
    }
}


function passsafe(eName, type)
{
    var isvalid = false;
    var msg_pwd = '';

    var pass = $("#password_" + type).val();
    if(pass != '')
    {
        $("#pwd_strong_" + type).show();
    }
    if (pass.length > 16)
    {
        msg_pwd = '最多16个字符';
        isvalid = false;
    }
    if ($.browser.msie && ($.browser.version == "6.0") && !$.support.style)
    {
        var longScore = getPwdScore(pass);
        if(longScore <= 10)
        {
            score = 0;
        }
        else if(longScore >= 11 && longScore <= 20)
        {
            score = 1;
        }
        else if(longScore >= 21 && longScore <= 30)
        {
            score = 2;
        }
        else if(longScore >= 31 && longScore <= 40)
        {
            score = 3;
        }
        else if(longScore >= 41)
        {
            score = 4;
        }
    }
    else
    {
        try{
            var passscore = zxcvbn(pass);
            var score = passscore.score;
        }
        catch(err)
        {
            var longScore = getPwdScore(pass);
            if(longScore <= 10)
            {
                score = 0;
            }
            else if(longScore >= 11 && longScore <= 20)
            {
                score = 1;
            }
            else if(longScore >= 21 && longScore <= 30)
            {
                score = 2;
            }
            else if(longScore >= 31 && longScore <= 40)
            {
                score = 3;
            }
            else if(longScore >= 41)
            {
                score = 4;
            }
        }
    }

    if (score <= 0)
    {
        $("#pwd_strong_" + type).find('.pPasswordHard').attr('class', 'pPasswordHard').addClass('level1').find('i').each(function(index, ele) {
            if (index <= 0) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('弱');
        $("#pwd_strength_" + type).val('1');

        pass == '' ? msg_pwd = '密码不能为空' : msg_pwd = '密码强度不能为弱';
        isvalid = false;
    }
    else if (score == 1)
    {
        $("#pwd_strong_" + type).find('.pPasswordHard').attr('class', 'pPasswordHard').addClass('level2').find('i').each(function(index, ele) {
            if (index <= 1) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('中');
        $("#pwd_strength_" + type).val('2');
        isvalid = true;
    }
    else if (score == 2)
    {
        $("#pwd_strong_" + type).find('.pPasswordHard').attr('class', 'pPasswordHard').addClass('level4').find('i').each(function(index, ele) {
            if (index <= 2) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('强');
        $("#pwd_strength_" + type).val('3');
        isvalid = true;
    }
    else if(score >= 3)
    {
        $("#pwd_strong_" + type).find('.pPasswordHard').attr('class', 'pPasswordHard').addClass('level4').find('i').each(function(index, ele) {
            if (index <= 3) {
                $(ele).addClass('on');
            } else {
                $(ele).attr('class', '');
            }
        }).next('em').text('极强');
        $("#pwd_strength_" + type).val('4');
        isvalid = true;
    }

    if (eName === 'keyup' && pass != '')
    {
        $('#msg_pwd_' + type).hide().find('span').text('');
        $('#pwd_strong_' + type).show();
        $("#password_" + type).parents('.item-input').removeClass("inputTxtError").find('.iRight').hide();
    }

    if (eName === 'blur')
    {
        if (isvalid === true)
        {
            $('#msg_pwd_' + type).hide().find('span').text('');
            $('#pwd_strong_' + type).show();
            $("#password_" + type).parents('.item-input').removeClass("inputTxtError").find('.iRight').show();

            if ($("#password").val() != $("#repassword_" + type).val() && $("#repassword_" + type).val() != '')
            {
                $("#repassword_" + type).parents('.item-input').addClass("inputTxtError").find('.iRight').hide();
                $("#msg_repassword_" + type +" span").html('两次输入密码不一致');
                $("#msg_repassword_" + type).show()
            }

        }
        else
        {
            $('#msg_pwd_' + type).show().find('span').html(msg_pwd);
            $('#pwd_strong_' + type).hide();
            $("#password_" + type).parents('.item-input').addClass("inputTxtError").find('.iRight').hide();
            $("#repassword_" + type).find('.iRight').hide();

            if (pass == '')
            {
                $("#repassword_" + type).val('').parents('.item-input').removeClass("inputTxtError").find('.iRight').hide();
                $("#msg_repassword_" + type + " span").html('');
                $("#msg_repassword_" + type).hide()

            }
        }
    }

    return isvalid;
}

function checkPassSame(pass)
{
    var first = pass.substring(0, 1);
    var exp = new RegExp('^' + first + '+$');
    if (exp.test(pass))
    {
        return false;
    }
    if (first == 'a' || first == 'A')
    {
        f = pass.charCodeAt(0);
        for (i = 1; i < pass.length; i++)
        {
            tmp = pass.charCodeAt(i);
            if (tmp - f != i)
            {
                return true;
            }
        }
        return false;
    }
    return true;
}

function passwordGrade(pwd)
{
    var score = 0;
    var regexArr = ['[0-9]', '[a-z]', '[A-Z]', '[\\W_]'];
    var repeatCount = 0;
    var prevChar = '';
    //check length
    var len = pwd.length;
    score += len > 18 ? 18 : len;
    //check type
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (eval('/' + regexArr[i] + '/').test(pwd))
            score += 4;
    }
    //bonus point
    for (var i = 0, num = regexArr.length; i < num; i++) {
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 2)
            score += 2;
        if (pwd.match(eval('/' + regexArr[i] + '/g')) && pwd.match(eval('/' + regexArr[i] + '/g')).length >= 5)
            score += 2;
    }
    //deduction
    for (var i = 0, num = pwd.length; i < num; i++) {
        if (pwd.charAt(i) == prevChar)
            repeatCount++;
        else
            prevChar = pwd.charAt(i);
    }
    score -= repeatCount * 1;
    return score;
}

function getPwdScore(pass)
{
    var score = 0;
    if (pass.length < 6)
    {
        score = 0;
    }
    else if (pass == '123456' || pass == '654321' || pass == '111222' || checkPassSame(pass) == false)
    {
        score = 0;
    }
    else
    {
        score = passwordGrade(pass);
    }
    return score;

}


function checkRepass(type)
{
    if ($("#password_" + type).val() == '')
    {
        return;
    }

    if ($("#repassword_" + type).val() != $("#password_" + type).val() && $("#repassword_" + type).val() != '')
    {
        $("#msg_repassword_" + type + " span").html('两次输入密码不一致');
        $("#msg_repassword_" + type).show();
        $("#repassword_" + type).parents('.item-input').addClass("inputTxtError").find('.iRight').hide();
        return false;
    }
    else if($("#repassword_" + type).val() == $("#password_" + type).val() && $("#repassword_" + type).val() != '')
    {
        $("#msg_repassword_" + type + " span").html('');
        $("#msg_repassword_" + type).hide();
        $("#repassword_" + type).parents('.item-input').removeClass("inputTxtError").find('.iRight').show();
        return true;
    }
}


function checkValidateCode()
{
    var val = $("#validate").val();
    if (val === '' || val == '请输入验证码')
    {
        $("#msg_validate").parent().addClass("inputTxtError");
        $("#msg_validate span").html('请输入验证码');
        $("#msg_validate").show();
        return false;
    }
    jQuery.ajax({
        url: "/api/youxia/checkCode?code=" + val,
        data: {},
        type: 'POST',
        async: false,
        dataType: 'JSON',
        success: function(response) {
            err = response;
        },
        error: function(jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });

    if (err == "200")
    {
        $("#msg_validate span").html('');
        $("#msg_validate").hide();
        $("#validate").parent().find('.icon').show();
        $(".mask,.security-code").hide();
        return true;
    } else {
        $("#validate").parent().find('.icon').hide();
        $("#msg_validate span").html('请输入正确的四位数字');
        $("#msg_validate").show();
        $("#Pic").attr('src', '/Captcha?J' + Math.random());
        return false;
    }
}


function checkAgree()
{
    if (!$("#agree").is(':checked'))
    {
        $("#msg_agree span").html('请同意2345服务协议、隐私政策');
        $("#msg_agree").show();
        return false;
    }
    else
    {
        $("#msg_agree span").html('');
        $("#msg_agree").hide();
        return true;
    }
}

function checkAll(type)
{
    // !checkValidateCode() ||
    if (!checkUsername() || !passsafe('blur', type)
            || !checkRepass(type) || !checkAgree())
    {
        return false;
    }

    var postData = $("#myRegister").serialize();
    $.ajax({
        url: "/oauth/yxbind/register",
        data: postData,
        type: 'POST',
        async: false,
        dataType: 'JSON',
        success: function (response) {
            res = response;
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(jqXHR.status);
        },
        timeout: 3000
    });

    if (res.err > 0)
    {
        alert(res.msg);
        return false;
    }

    window.location.href = res.refer;
}
</script>