<?php

namespace Admin;

use Controller;

class KeyManageController extends Controller
{
    private $redis;


    /**
     * KeyManageController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->redis = \RedisAction::connect();
    }

    /**
     * @return void
     */
    public function actionIndex()
    {
        $pattern = "";
        if (isset($_GET['key']))
        {
            $pattern = trim($_GET['key']);
        }
        if ($pattern != "" && $pattern != "*")
        {
            /*
            //如果服务器允许使用scan方法，把本段解除注释
            $it = null;
            $keys = array();
            //使用scan方法时，由于迭代器要求引用传值，仅适用于redis>2.8.0
            while ($arr_keys = $this->redis->scan($it, $pattern))
            {
                $keys = array_merge($keys, $arr_keys);
            }
            */
            //基类不支持scan，只能用keys
            $keys = $this->redis->keys($pattern);
        }
        else
        {
            $keys = array();
        }
        $keyTree = array();
        $this->showTree = array();
        $this->nodeCount = 0;
        foreach ($keys as $key)
        {
            $keyPath = explode(':', $key);
            $keyTemp = &$keyTree;
            for ($i = 0; $i < sizeof($keyPath); $i++)
            {
                if ($i != sizeof($keyPath) - 1)
                {
                    if (!isset($keyTemp[$keyPath[$i]]))
                    {
                        $keyTemp[$keyPath[$i]] = array();
                    }
                    $keyTemp = &$keyTemp[$keyPath[$i]];
                }
                else
                {
                    $keyTemp["_THISNODE_"][] = $keyPath[$i];
                }
            }
        }
        $serverInfo = $this->redis->info();
        $keyTree["Server Info Human"]["_SEVERLIST_"] = array(
            "版本号: " . $serverInfo['redis_version'],
            "数据库大小: " . $this->redis->dbSize(),
            "分配的内存总量: " . $serverInfo['used_memory_human'],
            "所用内存的峰值: " . $serverInfo['used_memory_peak_human'],
            "运行时间(秒): " . $serverInfo['uptime_in_seconds'],
            "最后一次成功保存: " . date("Y-m-d H:i:s", $serverInfo['rdb_last_save_time']),
            "命中key的次数: " . $serverInfo['keyspace_hits'],
            "没命中key次数: " . $serverInfo['keyspace_misses'],
            "运行以来执行命令总量: " . $serverInfo['total_commands_processed'],
        );
        $keyTree["Server Info Dump"]["_SEVERLIST_"] = array();
        foreach ($serverInfo as $k => $v)
        {
            $keyTree["Server Info Dump"]["_SEVERLIST_"][] = $k . " : " . $v;
        }
        $this->tree2Line($keyTree, $this->nodeCount);
        $pageInfo['tree'] = $this->showTree;
        $pageInfo['lastKey'] = $pattern;
        loadView('Admin/key_manage_index.tpl.html', $pageInfo);
    }


    /**
     * @param array  $tree       tree
     * @param array  $parentNode node
     * @param string $prefix     prefix
     *
     * @return void
     */
    private function tree2Line($tree, $parentNode, $prefix = "")
    {
        foreach ($tree as $k => $v)
        {
            if ($k == "_THISNODE_")
            {
                foreach ($v as $value)
                {
                    $thisNode = ++$this->nodeCount;
                    $this->showTree[] = array(
                        "index"   => $thisNode,
                        "parent"  => $parentNode,
                        "folder"  => 0,
                        "key"     => $value,
                        "fullKey" => $prefix . $value,
                        "type"    => $this->translateType($this->redis->type($prefix . $value)),
                        "ttl"     => $this->redis->ttl($prefix . $value),
                    );
                }
            }
            else
            {
                if ($k == "_SEVERLIST_")
                {
                    foreach ($v as $value)
                    {
                        $thisNode = ++$this->nodeCount;
                        $this->showTree[] = array(
                            "index"   => $thisNode,
                            "parent"  => $parentNode,
                            "folder"  => 2,
                            "key"     => $value,
                            "fullKey" => '',
                            "type"    => '',
                            "ttl"     => '',
                        );
                    }
                }
                else
                {
                    $thisNode = ++$this->nodeCount;
                    $this->showTree[] = array(
                        "index"   => $thisNode,
                        "parent"  => $parentNode,
                        "folder"  => 1,
                        "key"     => $k,
                        "fullKey" => '',
                        "type"    => '-',
                        "ttl"     => '-',
                    );
                    $this->tree2Line($v, $thisNode, $prefix . $k . ":");
                }
            }
        }
    }


    /**
     * @param  int $typeInt type
     *
     * @return string
     */
    private function translateType($typeInt)
    {
        switch ($typeInt)
        {
            case 0:
                $typeName = 'not exist';
                break;
            case 1:
                $typeName = 'String';
                break;
            case 3:
                $typeName = 'List';
                break;
            case 2:
                $typeName = 'Set';
                break;
            case 4:
                $typeName = 'zSet';
                break;
            case 5:
                $typeName = 'Hash';
                break;
            default:
                $typeName = 'unknown';
        }

        return $typeName;
    }

    /**
     * @param string $key key
     *
     * @return void
     */
    public function actionQueryKeyValue($key = '')
    {
        $key = "";
        $parentId = "";
        if (isset($_GET['key']))
        {
            $key = $_GET['key'];
        }
        if (isset($_GET['parent']))
        {
            $parentId = $_GET['parent'];
        }
        $html = "";
        $html .= "<tr data-tt-id='1000000{$parentId}' data-tt-parent-id='{$parentId}' class='detailItem'><td colspan='4'>";
        switch ($this->redis->type($key))
        {
            case 0:
                $html .= '[key does not exist]';
                break;
            case 1:
                $keys = $this->redis->get($key);
                $html .= "Type: String; Full Name: <strong>{$key}</strong>";
                $html .= "<table class='detailTbl'>";
                $html .= "<thead>";
                $html .= "<th style='width:100%'>Text</th>";
                $html .= "</thead>";
                $html .= "<tr>";
                $html .= "<td>{$keys}</td>";
                $html .= "</tr>";
                $html .= "</table>";
                break;
            case 2:
                $keys = $this->redis->sMembers($key);
                $html .= "Type: Set; Full Name: <strong>{$key}</strong>; Total Keys Count: " . sizeof($keys);
                $html .= "<table class='detailTbl'>";
                $html .= "<thead>";
                $html .= "<th>ID</th>";
                $html .= "<th>Member</th>";
                $html .= "</thead>";
                foreach ($keys as $k => $v)
                {
                    $html .= "<tr>";
                    $html .= "<td>{$k}</td>";
                    $html .= "<td>{$v}</td>";
                    $html .= "</tr>";
                }
                $html .= "</table>";
                break;
            case 3:
                $keys = $this->redis->lRange($key, 0, -1);
                $html .= "Type: List; Full Name: <strong>{$key}</strong>; Total Keys Count: " . $this->redis->lLen($key);
                $html .= "<table class='detailTbl'>";
                $html .= "<thead>";
                $html .= "<th>ID</th>";
                $html .= "<th>Value</th>";
                $html .= "</thead>";
                foreach ($keys as $k => $v)
                {
                    $html .= "<tr>";
                    $html .= "<td>{$k}</td>";
                    $html .= "<td>{$v}</td>";
                    $html .= "</tr>";
                }
                $html .= "</table>";
                break;
            case 4:
                $keys = $this->redis->zRange($key, 0, -1);
                $html .= "Type: zSet; Full Name: <strong>{$key}</strong>; Total Keys Count: " . $this->redis->zCount($key, "-inf", "+inf");
                $html .= "<table class='detailTbl'>";
                $html .= "<thead>";
                $html .= "<th style='width:33%'>ID</th>";
                $html .= "<th style='width:33%'>Score</th>";
                $html .= "<th style='width:34%'>Member</th>";
                $html .= "</thead>";
                foreach ($keys as $k => $v)
                {
                    $html .= "<tr>";
                    $html .= "<td>{$k}</td>";
                    $html .= "<td>" . $this->redis->zScore($key, $v) . "</td>";
                    $html .= "<td>{$v}</td>";
                    $html .= "</tr>";
                }
                $html .= "</table>";
                break;
            case 5:
                $keys = $this->redis->hGetAll($key);
                $html .= "Type: Hash; Full Name: <strong>{$key}</strong>; Total Keys Count: " . $this->redis->hLen($key);
                $html .= "<table class='detailTbl'>";
                $html .= "<thead>";
                $html .= "<th>HashKey</th>";
                $html .= "<th>Value</th>";
                $html .= "</thead>";
                foreach ($keys as $k => $v)
                {
                    $html .= "<tr>";
                    $html .= "<td>{$k}</td>";
                    $html .= "<td>{$v}</td>";
                    $html .= "</tr>";
                }
                $html .= "</table>";
                break;
            default:
                $html .= '[error]';
        }
        $html .= "</td></tr>";
        header("Content-Type:text/html; charset=GBK");
        echo $html;
    }

}
