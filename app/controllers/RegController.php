<?php

// 注册
class RegController extends Controller
{

    private $pageArray;
    private $title = "用户中心-注册";

    public function __construct()
    {
        parent::__construct();
        \Service\Monitor\ApiCallHookMonitor::Filter();
        $this->pageArray['title'] = $this->title;
    }

    // 注册入口，包括注册页面和注册提交
    public function actionIndex()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') // 注册提交
        {
            if (!isset($_POST['agree']))
            {
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
                {
                    exit(json_encode(array(
                        "loadPage" => '',
                        "forwardPage" => '',
                        "msg" => mb_convert_encoding('请同意2345服务协议 @msg_agree', "utf8", "gbk")
                    )));
                }
                else
                {
                    showMessage('请同意2345服务协议', 'back');
                }
            }

            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                $_POST['username'] = mb_convert_encoding(trim($_POST['username']), "gbk", "utf8");
            }
            else
            {
                $_POST['username'] = trim($_POST['username']);
            }

            /**
             * 敏感信息AES解密
             */
            if (isset($_POST['username']) && !empty($_POST['username']))
            {
                $encryptAction = loadAction('Encrypt');
                $_POST['username'] = $encryptAction::aes128cbcHexDecrypt($_POST['username']);
            }
            if (isset($_POST['password']) && !empty($_POST['password']))
            {
                $encryptAction = loadAction('Encrypt');
                $_POST['password'] = $encryptAction::aes128cbcHexDecrypt($_POST['password']);
            }

            $regex = Config::get('regex');

            if (filter_var($_POST['username'], FILTER_VALIDATE_EMAIL))
            {
                $this->regEmail();
            }
            elseif (preg_match($regex['phone'], $_POST['username']))
            {
                $this->regPhone();
            }
            else
            {
                $this->regUserName();
            }
        }
        else // 显示注册页面
        {
            if (isset($_SERVER['HTTP_REFERER']))
            {
                $referDomain = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
                preg_match('/\w+\.(com\.cn|net\.cn|org\.cn|gov\.cn|\w+)$/', $referDomain, $matches);
                if ($matches[0] != "2345.com" && $matches[0] != "2345.cn")
                {
                    $referDomain = '';
                }
            }
            else
            {
                $referDomain = '';
            }
            setcookie('referDomain', $referDomain, 0, '/', $_SERVER['HTTP_HOST']);
            $forward = getForwardUrl();
            if ($forward)
            {
                setcookie('qqforward', $forward, time() + 3600 * 24, '/');
                $urlInfo = parse_url($forward);
                switch ($urlInfo['host'])
                {
                    case "login.2345.com":
                        $productPic = "local.jpg";
                        break;
                    case "book.2345.com":
                        $productPic = "book.jpg";
                        break;
                    case "game.2345.com":
                    case "wan.2345.com":
                    case "xiaoyouxi.2345.com":
                    case "danji.2345.com":
                    case "wangyou.2345.com":
                    case "shouyou.2345.com":
                        $productPic = "game.jpg";
                        break;
                    case "buy.2345.com":
                        $productPic = "buy.jpg";
                        break;
                    case "v.2345.com":
                    case "kan.2345.com":
                    case "dianying.2345.com":
                    case "tv.2345.com":
                    case "dongman.2345.com":
                        $productPic = "yingshi.jpg";
                        break;
                    case "day.2345.com":
                        $productPic = "day.jpg";
                        break;
                    default:
                        $productPic = "local.jpg";
                }
            }
            else
            {
                $productPic = "local.jpg";
            }
            $this->pageArray['productPic'] = $productPic;
            $this->pageArray['domain'] = getDomainType($forward);
            $this->pageArray['refer'] = urlencode($forward);
            $this->pageArray['forwardDomain'] = empty($forward) ? "" : parse_url($forward, PHP_URL_HOST);
            if (IsMobile())
            {
                loadView('m/reg_wap.tpl.html', $this->pageArray);
            }
            else
            {
                loadView('v3/reg.tpl.html', $this->pageArray);
            }
        }
    }

    /**
     * 用户名注册
     */
    private function regUserName()
    {
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || $_POST['validate'] !== '' . $_SESSION['captcha_code'])
        {
            unset($_SESSION['captcha_code']);
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                )));
            }
            else
            {
                showMessage('验证码输入错误', 'back');
            }
        }
        unset($_SESSION['captcha_code']);

        $client_ip = get_client_ip();

        // 错误码
        $msgs = array(
            300 => array(
                0 => '2345帐号最少2个字符',
                1 => '2345帐号请不要超过24个字符',
                2 => '2345帐号请输入汉字，字母，数字，或邮箱地址',
                3 => '密码最少6个字符',
                4 => '密码最多16个字符',
                5 => '请输入正确的邮箱',
                6 => '此帐号已被注册，请修改2345帐号',
                7 => '此邮箱已被注册，请换一个'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );

        // 密码强度
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($_POST['password']);

        $regAction = loadAction('reg');

        $source = "login.2345.com";
        $refer = urldecode($_POST['refer']);
        if ($refer)
        {
            //path 只记录/m/
            $source = parse_url($refer, PHP_URL_HOST);
            $sourcePath = trim(parse_url($refer, PHP_URL_PATH), "/");
            if (in_array($sourcePath, array("m")))
            {
                $source .= "/" . $sourcePath;
            }
        }
        $result = $regAction->reg($_POST['username'], $_POST['password'], $_POST['email'], $pwdScore['score'], $_POST['refer'], 'login.2345.com', $client_ip, $source);
        $states = explode('.', $result[0]);

        if ($states[0] == 400)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showError($msgs[$states[0]][$states[1]], 400);
            }
        }
        elseif ($states[0] == 300)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showMessage($msgs[$states[0]][$states[1]], 'back');
            }
        }
        elseif ($states[0] == 200)
        {
            if ($_COOKIE['referDomain'] != '')
            {
                $referDomain = $_COOKIE['referDomain'];
                $redis = RedisEx::getInstance();
                $redis->hIncrBy('referDomain:RSN', $referDomain, 1);
            }

                $this->doLogin($result[1]);

        }
    }

    /**
     * 手机注册
     */
    private function regPhone()
    {
        if (!isset($_POST['validate_phone']) || $_POST['validate_phone'] == '' || !$this->actionCheckPhoneCode($_POST['username'], $_POST['validate_phone'], true))
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                )));
            }
            else
            {
                showMessage('验证码输入错误', 'back');
            }
        }

        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );

        $client_ip = get_client_ip();
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($_POST['password']);
        $regAction = loadAction('reg');

        $source = "login.2345.com";
        $refer = urldecode($_POST['refer']);
        if ($refer)
        {
            //path 只记录/m/
            $source = parse_url($refer, PHP_URL_HOST);
            $sourcePath = trim(parse_url($refer, PHP_URL_PATH), "/");
            if (in_array($sourcePath, array("m")))
            {
                $source .= "/" . $sourcePath;
            }
        }
        $result = $regAction->regPhone($_POST['username'], $_POST['password'], $pwdScore['score'], $client_ip, 'login.2345.com', 200,  $source);
        $states = explode('.', $result[0]);

        if ($states[0] == 400)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showError($msgs[$states[0]][$states[1]], 400);
            }
        }
        elseif ($states[0] == 300)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showMessage($msgs[$states[0]][$states[1]], 'back');
            }
        }
        elseif ($states[0] == 200)
        {
            if ($_COOKIE['referDomain'] != '')
            {
                $referDomain = $_COOKIE['referDomain'];
                $redis = RedisEx::getInstance();
                $redis->hIncrBy('referDomain:RSN', $referDomain, 1);
            }
                $this->doLogin($result[1]);
        }
    }

    /**
     * email注册
     */
    private function regEmail()
    {
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || $_POST['validate'] !== '' . $_SESSION['captcha_code'])
        {
            unset($_SESSION['captcha_code']);
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                )));
            }
            else
            {
                showMessage('验证码输入错误', 'back');
            }
        }
        unset($_SESSION['captcha_code']);

        $msgs = array(
            300 => array(
                0 => '请输入正确的邮箱地址',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此邮箱号已被注册',
                4 => '邮箱长度不能超过24个字符',
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $client_ip = get_client_ip();
        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($_POST['password']);
        $regAction = loadAction('reg');

        $source = "login.2345.com";
        $refer = urldecode($_POST['refer']);
        if ($refer)
        {
            //path 只记录/m/
            $source = parse_url($refer, PHP_URL_HOST);
            $sourcePath = trim(parse_url($refer, PHP_URL_PATH), "/");
            if (in_array($sourcePath, array("m")))
            {
                $source .= "/" . $sourcePath;
            }
        }
        $result = $regAction->regEmail($_POST['username'], $_POST['password'], $pwdScore['score'], $client_ip, 'login.2345.com', $source);
        $states = explode('.', $result[0]);

        if ($states[0] == 400)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showError($msgs[$states[0]][$states[1]], 400);
            }
        }
        elseif ($states[0] == 300)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit(json_encode(array(
                    "loadPage" => '',
                    "forwardPage" => '',
                    "msg" => '',
                    "msgCode" => $result[0]
                )));
            }
            else
            {
                showMessage($msgs[$states[0]][$states[1]], 'back');
            }
        }
        elseif ($states[0] == 200)
        {
            if ($_COOKIE['referDomain'] != '')
            {
                $referDomain = $_COOKIE['referDomain'];
                $redis = RedisEx::getInstance();
                $redis->hIncrBy('referDomain:RSN', $referDomain, 1);
            }
                $this->doLogin($result[1]);
        }
    }

    /**
     * 登录（设置cookie并跳转）
     * @param  array $params 用户信息
     */
    private function doLogin($params)
    {
        $cookie = $params['cookie'];
        $refer = urldecode($params['refer']);
        // cookie过期时间
        $cTime = time() + 3600 * 24 * 30;
        $loginAction = loadAction('login');
        $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);
        // 处理回跳地址
        if (empty($refer))
        {
            if (isset($_COOKIE['qqforward']))
            {
                $refer = urldecode($_COOKIE['qqforward']);
            }
            else
            {
                $refer = 'http://passport.' . DOMAIN;
            }
        }

        // 设置其他域名的登录cookie： 2345.cn等
        $_COOKIE['oauthThirdCallback'] = getForwardDomainSetThirdCallback($refer);
        $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
        unset($_COOKIE['oauthThirdCallback']);
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
        {
            exit(json_encode(array(
                "loadPage" => $thirdCallbackCookies,
                "forwardPage" => $refer,
                "msg" => ''
            )));
        }
        else
        {
            $this->pageArray = array(
                "loadPage" => json_encode($thirdCallbackCookies),
                "forwardPage" => $refer
            );
            return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $this->pageArray);
        }
    }

    /**
     * 发送手机验证码
     */
    public function actionSendPhoneCode()
    {
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || $_POST['validate'] !== '' . $_SESSION['captcha_code'])
        {
            unset($_SESSION['captcha_code']);
            echo '600.0';
            return;
        }
        $phone = isset($_POST['phone']) ? $_POST['phone'] : null;

        if (strpos($phone, '1719662') === 0 || strpos($phone, '1329141') === 0 || strpos($phone, '1306478') === 0)
        {
            //非法操作
            echo '500.0';

            return;
        }

        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        if (empty($referer) || strpos(strtolower($referer), '38264-0010') > 0)
        {
            if (is_numeric($_POST['validate']))
            {
                unset($_SESSION['captcha_code']);
                echo '600.0';
                return;
            }
        }

//        $checkIPAction = loadAction('CheckIP');
//        if (!$checkIPAction::checkISChinaIP(get_client_ip()))
//        {
//            echo '500.0';
//            return;
//        }
        //检查refer
        $host = $_SERVER['HTTP_HOST'];
        $scheme = isHttps() ? 'https://' : 'http://';
        $needRefer = strtolower($scheme . $host . '/reg');
        if (empty(strtolower($referer)) || strpos(strtolower($referer), $needRefer) === false)
        {
            xLog('regNoReferer', 'regNoReferer', serialize($_REQUEST).' refer:'.$referer);
            return;
        }
        else
        {
            xLog('regNormal', 'regNormal', serialize($_REQUEST) . ' refer:' . $referer . ' IP:' . get_client_ip());
        }

        if (strpos(strtolower($referer), '38264-0010') > 0)
        {
            echo '200.0';
            return;
        }

        if (preg_match('/^1[0123456789]\d{9}$/', $phone))
        {
            $memberModel = loadModel('member');
            $ret = $memberModel->checkPhone($_POST['phone'], 1);
            if ($ret != 0)
            {
                echo '400.0';
                return;
            }

            $code = rand(100000, 999999);
            // 测试手机验证码固定
            if (RUNMODE == 'development' || RUNMODE == 'testing') {
                $code = 123456;
            }
            // 开始发送短信
            /** @var PhoneAction $phoneAction */
            $phoneAction = loadAction("phone");
            $mid = "login";
            $smsMid = $phoneAction->getSmsCountMid(getForwardUrl(), $mid);
            $result = sendCodeUsePhone($phone, $code, 8, 167, $mid, [], $smsMid);
            if ($result == 1)
            {
                $redis = RedisEx::getInstance();
                $redis->setex("regchecktime:" . $phone, 1800, 0);
                $redis->setex("regcode:" . $phone, 1800, md5($phone . $code));
                echo '200.0';
            }
            else if ($result == 2)
            {
                echo '400.0';
            }
            else
            {
                echo '500.0';
            }
        }
        else
        {
            echo '300.0';
        }
    }

    /**
     * 发送手机语音验证码
     */
    public function actionSendPhoneVoiceCode()
    {
        /**
         * 防恶意攻击
         */
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || $_POST['validate'] !== '' . $_SESSION['captcha_code'])
        {
            unset($_SESSION['captcha_code']);
            echo '600.0';
            return;
        }
        if ($_SERVER['HTTP_REFERER'] == "http://www.baidu.com")
        {
            echo '200.0';
            return;
        }
        $phone = isset($_POST['phone']) ? $_POST['phone'] : null;
        if (preg_match('/^1[0123456789]\d{9}$/', $phone))
        {
            $memberModel = loadModel('member');
            $ret = $memberModel->checkPhone($_POST['phone'], 1);
            if ($ret != 0)
            {
                echo '400.0';
                return;
            }

            $result = sendCodeFromVoice($phone);
            if ($result[0] == 1)
            {
                $redis = RedisEx::getInstance();
                $redis->setex("regchecktime:" . $phone, 1800, 0);
                $redis->setex("regcode:" . $phone, 1800, md5($phone . $result[1]));
                echo '200.0';
            }
            else if ($result[0] == 2)
            {
                echo '400.0';
            }
            else
            {
                echo '500.0';
            }
        }
        else
        {
            echo '300.0';
        }
    }

    /**
     * 检查手机验证码
     */
    public function actionCheckPhoneCode($phone = null, $code = null, $return = null)
    {
        $respCode = '200.0';
        $code = isset($_POST['code']) ? $_POST['code'] : $code;
        $phone = isset($_POST['phone']) ? $_POST['phone'] : $phone;
        if (preg_match('/^1[0123456789]\d{9}$/', $phone))
        {
            $redis = RedisEx::getInstance();

            $checkTime = $redis->get("regchecktime:" . $phone);
            if ($checkTime > 2)
            {
                $redis->del("regchecktime:" . $phone);
                $redis->del("regcode:" . $phone);
                $respCode = '400.0';
            }

            $checkcode = $redis->get("regcode:" . $phone);
            if (md5($phone . $code) == $checkcode && $checkcode)
            {
                $respCode = '200.0';
            }
            else
            {
                $redis->setex("regchecktime:" . $phone, 1800, ++$checkTime);
                $respCode = '400.0';
            }
        }
        else
        {
            $respCode = '300.0';
        }

        if ($return)
        {
            if (isset($redis))
            {
                $redis->del("regchecktime:" . $phone);
                $redis->del("regcode:" . $phone);
            }
            return $respCode == '200.0' ? TRUE : FALSE;
        }
        else
        {
            echo $respCode;
        }
    }

}
