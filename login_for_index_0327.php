<?php
/*
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 摘    要：登录程序
 * 作    者：熊小明
 * 修改日期：2013.03.27
 */

define("BASE_PATH", dirname(__FILE__));
require_once BASE_PATH . "/include/init.php";

include_once BASE_PATH . "/config.inc.php";
include_once "class/tools.cls.php";

//退出
function logout()
{
    mySetCookie('u_sec', "", -1);
    mySetCookie('passid', "", -1);
    mySetCookie('name', "", -1);
    mySetCookie('uid', "", -1);
    mySetCookie('iden', "", -1);
    mySetCookie("name_ie", "", -1);
    mySetCookie("user_info", "", -1);
    mySetCookie("I", "", -1);
    $forward = $_REQUEST['forward'];
    $vDomain = get_domain();
    
    $url = 'https://passport.2345.com/v3/user/sso/clearToken';
    $cookies = "";
    foreach ($_COOKIE as $k => $v) {
        $cookies .= ($k . "=" . $v . ";");
    }
    $cookies = "cookie:" . rtrim($cookies, ";");
    curl_get_contents($url . '?mid=login', [CURLOPT_COOKIE => $cookies]);
    
    if ($_ENV['RUNMODE'] == "development") {
        dev::getInstance($_ENV['RUNMODE']);
        $headerUrl = "http://passport.2345.com/login_for_index_new.php?action=logout&forward=" . rawurlencode($forward);
        $devDomain = dev::getDevDomain($headerUrl);
        header("Location: {$devDomain}");
        exit;
    }
    if ($vDomain == '2345')
    {
        if (strpos($forward, "hao774.com") > 0)
        {
            header("Location: http://passport.hao774.com/webapi/sso/logoutCrossDomain?forward=" . rawurlencode($forward));
        }
        elseif (strpos($forward, "7255.com") > 0)
        {
            header("Location: http://passport.7255.com/webapi/sso/logoutCrossDomain?forward=" . rawurlencode($forward));
        }
        else
        {
            header("Location: http://bbs." . $vDomain . ".cn/api/passport.php?action=logout&forward=" . rawurlencode($forward));
        }
    }
    elseif ($forward)
    {
        header("Location: https://www.2345.com");
    }
    else
    {
        header("Location: http://my." . $vDomain . ".com/");
    }
    die;
}

if ($_GET['action'] == "logout")
{
    logout();
    die;
}

