<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2018/9/6
 * Time: 18:06
 */

class ServiceSmsModel extends Model
{
    /**
     * 函数名称：__construct
     * 参    数：
     * 作    者：杜海明
     * 功    能：初始化数据库信息
     * 修改日期：2015-04-20
     */
    public function __construct()
    {
        //顺序不能错~
        $this->dbName = DATABASE_SMS;
        parent::__construct();
    }

    /**
     * @param null|string $month 年月
     * @return bool
     */
    public function createSmsLogTable($month = null)
    {
        $tableName = self::getTableName($month);
//        if ($this->redis->get('smsp:table:' . $tableName))
//        {
//            return true;
//        }
//        else
        if ($this->checkTableExist($tableName))
        {
//            $this->redis->set('smsp:table:' . $tableName, true);
            return true;
        }
        $sql = "CREATE TABLE IF NOT EXISTS $tableName LIKE `sms_logs_archive`";
        $kkk = $this->pdo->query($sql);
        if ($kkk === 0)
        {
//            $this->redis->set('smsp:table:' . $tableName, true);
            return true;
        }
        return false;
    }

    /**
     * 用于新建短信，返回logID
     * @param array $data   插入数据，数组
     * @param array $column 插入数据，字段
     * @param bool $isGenerate 是否生成短信日志
     * @return bool|array
     */
    public function setSmsLogs($data, $column, $isGenerate = false)
    {
        if (count($data) == 0)
        {
            return false;
        }
        $cacheColumn = array('phone', 'encrypt_phone');
        $cacheData = array();
        $phoneLogIds = array();
        foreach ($data as $phone => $value)
        {
            $cacheData[$phone] = [
                \SecurityAction::desensitize($phone),
                \SecurityAction::encrypt($phone),
            ];
        }
        $this->pdo->beginTransaction();
        $res = $this->pdo->batch('sms_logs_cache', $cacheColumn, $cacheData);

        if ($res)
        {
            $lastInsertId = (int)($this->pdo->lastInsertId());
            foreach ($data as $phone => $value)
            {
                $data[$phone][0] = \SecurityAction::desensitize($phone);
                $data[$phone][1] = \SecurityAction::encrypt($phone);
                $insertId = $lastInsertId++;
                $data[$phone][] = $insertId;
                $phoneLogIds[$phone] = $insertId;
            }
            if ($isGenerate)
            {
                $this->createSmsLogTable();
                $res = $this->pdo->batch(self::getTableName(), array_merge($column, array('log_id')), $data);
            }
        }
        if ($res)
        {
            $this->pdo->commit();
            return $phoneLogIds;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * 短信内容从redis落入mysql
     * -
     * @param array $data 短信log
     * @return bool
     */
    public function uploadServiceData($data)
    {
        if (empty($data['log_id']))
        {
            return false;
        }
        if (isset($data['phone']) && !empty($data['phone']))
        {
            //pay attention to the order
            $data['encrypt_phone'] = SecurityAction::encrypt($data['phone']);
            $data['phone'] = SecurityAction::desensitize($data['phone']);
        }
        $this->pdo->beginTransaction();
        $addData = [
            'log_id' => $data['log_id'],
            'phone' => $data['phone'],
            'encrypt_phone' => $data['encrypt_phone'],
            'text' => $data['text'],
            'sessionid' => $data['sessionid'],
            'type' => $data['type'],
            'send_time' => $data['send_time'],
            'send_status' => $data['send_status'],
            'send_response' => $data['send_response'],
            'passid' => !empty($data['passid']) ? $data['passid'] : 0,
            'pid' => $data['pid'],
            'business_id' => $data['business_id'],
            'client_ip' => $data['client_ip'],
            'server_ip' => $data['server_ip'],
        ];
        if (!empty($data['msg_id']))
        {
            $addData['msg_id'] = $data['msg_id'];
        }
        if (isset($data['account']))
        {
            $addData['account'] = $data['account'];
        }
        if (isset($data['channel']))
        {
            $addData['channel'] = $data['channel'];
        }
        if (isset($data['code_time']))
        {
            $addData['code_time'] = $data['code_time'];
        }
        if (isset($data['code_status']))
        {
            $addData['code_status'] = (int)$data['code_status'];
        }
        if (isset($data['code_desc']))
        {
            $addData['code_desc'] = $data['code_desc'];
        }
        if (isset($data['sms_count']))
        {
            $addData['sms_count'] = $data['sms_count'];
        }
        if (isset($data['callback_time']))
        {
            $addData['callback_time'] = $data['callback_time'];
        }
        if (isset($data['callback_status']))
        {
            $addData['callback_status'] = $data['callback_status'];
        }
        if (isset($data['callback_message']))
        {
            $addData['callback_message'] = $data['callback_message'];
        }
        $res = $this->pdo->insert(self::getTableName(), $addData);
        if ($res !== false)
        {
            $sql = "INSERT INTO sms_logs_cache (log_id, send_time, send_status, callback_time, callback_status, phone, encrypt_phone) 
                  VALUES (:log_id, :send_time, :send_status, :callback_time, :callback_status, :phone, :encrypt_phone)
                  ON DUPLICATE KEY UPDATE send_time=VALUES(send_time), send_status=VALUES (send_status), callback_time=VALUES(callback_time), callback_status=VALUES(callback_status),phone=VALUES(phone),encrypt_phone=VALUES(encrypt_phone)";
            $params = [
                ':log_id'          => $data['log_id'],
                ':send_time'       => $data['send_time'],
                ':send_status'     => $data['send_status'],
                ':callback_time'   => $data['callback_time'] ?? null,
                ':callback_status' => $data['callback_status'] ?? null,
                ':phone'           => $data['phone'],
                ':encrypt_phone'   => $data['encrypt_phone'],
            ];
            $res = $this->pdo->query($sql, $params);
        }
        if ($res !== false)
        {
            if (!empty($data['mid']))
            {
                $tagLog = [
                    'mid' => $data['mid'],
                    'positionId' => $data['business_id'],
                    'add_time' => $data['send_time'],
                    'log_id' => $data['log_id']
                ];
                $res = $this->pdo->insert('sms_tag_log', $tagLog);
            }
        }

        if ($res)
        {
            $this->pdo->commit();
            return true;
        }
        else
        {
            $this->pdo->rollBack();
            return false;
        }
    }

    /**
     * 根据logid 更新msgid
     * -
     * @param int $logId logId
     * @param int $msgId  msgid
     * @return bool
     */
    public function updateMsgIdByLogId($logId, $msgId)
    {
        $condition = array(
            'where'  => 'log_id = :log_id',
            'params' => [
                'log_id' => $logId
            ]
        );
        $update = array(
            'msg_id' => $msgId,
        );
        return $this->pdo->update(self::getTableName(), $update, $condition);
    }
}
