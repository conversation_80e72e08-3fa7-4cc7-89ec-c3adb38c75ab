<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/7/29
 * Time: 19:48
 */

namespace SmsInterface;

use Controller;

class YunRongCallbackController extends Controller
{
    /**
     * 查看该渠道商是否具有访问权限
     * */
    public function __construct()
    {
        $isAccess = \ChannelIpWhiteList::isAccessAuthChannel('yunRong');
        if (!$isAccess) {
            header('HTTP/1.1 401 Unauthorized');
            exit;
        }
    }

    /**
     * 发短信回调接口状态同步
     * 注意其他文件 的这个方法名 拼写错误..
     * @return void
     */
    public function actionCallbackRsyncStatus()
    {
        $reports = $this->Filter->post('report');
        if (empty($reports)) {
            echo 1;
            exit;
        }
        $redisAction = \RedisAction::connect();
        $smsList = explode(';', trim($reports, ';'));
        foreach ($smsList as $smsInfo) {
            list($phone, $smsStatus, $msgId, $kzCode, $callbackTime) = explode('|', $smsInfo);
            $params = array(
                "Msg_Id"        => trim($msgId),
                "Mobile"        => trim($phone),
                "OriginStatus"  => trim($smsStatus),
                "Status"        => $smsStatus,
                "Callback_Time" => trim($callbackTime),
            );
            $isOK = $redisAction->lPush(\Config_CacheKey::CHANNEL_CALLBACK_STATUS_NEW, json_encode($params));
        }
        echo 0;
    }
}
