<?php

class CaptchaAction extends Action
{

    private $codeLength = 4; //默认显示四位验证码
    private $gifTimes = 4; //gif图验证码帧数
    private $randCode = array();
    private $randCodeStr = array();//验证码的字符
    private $checkImage = '';           //验证码的图片
    private $imageWidth = 100;                //验证码的图片宽度
    private $imageHeight = 40;               //验证码的图片宽度
    private $randString = '1234567890'; //默认字符串
    private $fontSize = 24;             //生成字体的大小
    private $lineNum = 3;                //干扰线的数目
    private $pointNum = 150;             //干扰点的数目
    private $backGround = '#F1F1F1';    //生成背景颜色

    public function setSize($width, $height)
    {
        $this->imageWidth = $width;
        $this->imageHeight = $height;
    }

    /**
     * 生成图片验证码
     * -
     * @param string $mid 唯一标识
     * @return array
     */
    public function generate($mid = '')
    {
        if (in_array($mid, array('SRF')))
        {
            $this->randString = '23456789abcdefghjkmnpstwx';
            $this->lineNum = 0;
            $this->pointNum = 0;
        }
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        $host = $_SERVER['HTTP_HOST'];
        $scheme = isHttps() ? 'https://' : 'http://';
        $needRefer = strtolower($scheme . $host . '/reg');

        if ($mid == 'loginReg' || strpos(strtolower($referer), $needRefer) > 0)
        {
            $this->randString = '23456789abcdefghjkmnpstwx';
            if (strpos(strtolower($referer), '38264-0010') > 0)
            {
                $this->randString = 'abcdefghjkmnpstwx';
                $this->lineNum = 8;
                $this->pointNum = 180;
            }
        }


        $randCode = array();
        $randCodeStr = '';
        for ($i = 0; $i < $this->codeLength; $i++)
        {
            $cutstrnum = mt_rand(0, strlen($this->randString) - 1);
            $randCode[] = trim(substr($this->randString, $cutstrnum, 1));
            $randCodeStr .= trim(substr($this->randString, $cutstrnum, 1));
        }
        $this->randCode = $randCode;
        $this->randCodeStr = $randCodeStr;

        if (RUNMODE == 'testing' || RUNMODE == 'development') {
            $randCode = [1,2,3,4];
            $this->randCode = $randCode;
            $this->randCodeStr = '1234';
        }
        return $randCode;
    }

    /**
     * show
     * -
     * @param string $mid Mid
     * @return void
     * <AUTHOR>
     */
    public function show($mid = '')
    {
        if ($mid == 'XCTEST' || $mid == 'XQ')
        {
            $this->lineNum = 0;
            $this->pointNum = 0;
        }
        header("Content-type: image/PNG");
        header('P3P: CP="CURa ADMa DEVa PSAo PSDo OUR BUS UNI PUR INT DEM STA PRE COM NAV OTC NOI DSP COR"');
        $this->checkImage = imagecreate($this->imageWidth, $this->imageHeight);
        imagecolorallocate($this->checkImage, 0xFF, 0xFF, 0xFF);
        $font = imagecolorallocate($this->checkImage, hexdec(substr($this->backGround, 1, 2)), hexdec(substr($this->backGround, 3, 2)), hexdec(substr($this->backGround, 5, 2)));
        imagerectangle($this->checkImage, 0, 0, $this->imageWidth - 1, $this->imageHeight - 1, $font);
        for ($i = 0; $i < $this->lineNum; $i++)
        {
            $lineColor = imagecolorallocate($this->checkImage, mt_rand(0, 255), mt_rand(0, 255), mt_rand(0, 255));
            $h1 = mt_rand(0, $this->imageHeight);
            $h2 = mt_rand(0, $this->imageHeight);
            imageline($this->checkImage, 0, $h1, $this->imageWidth, $h2, $lineColor);
            $h1++;
            $h2++;
            imageline($this->checkImage, 0, $h1, $this->imageWidth, $h2, $lineColor);
        }
        for ($i = 0; $i <= $this->pointNum; $i++)
        {
            $pointcolor = imagecolorallocate($this->checkImage, mt_rand(0, 255), mt_rand(0, 255), mt_rand(0, 255));
            imagesetpixel($this->checkImage, mt_rand(0, $this->imageWidth), mt_rand(0, $this->imageHeight), $pointcolor);
        }
        for ($i = 0; $i < $this->codeLength; $i++)
        {
            $randFontNum = mt_rand(1, 8);
            $fontFamily = APPPATH . "/fonts/" . $randFontNum . ".TTF";
            $fontcolor = imagecolorallocate($this->checkImage, mt_rand(0, 255), 0, mt_rand(0, 255));
            $x = $this->imageWidth / $this->codeLength;
            $y = $this->imageHeight / 1.4;
            imagettftext($this->checkImage, $this->fontSize, mt_rand(-10, 10), $x * $i, $y, $fontcolor, $fontFamily, $this->randCode[$i]);
        }
        imagepng($this->checkImage);
        imagedestroy($this->checkImage);
    }

    /**
     * 功    能：movementCaptchaShow 动态图片验证码
     *
     * <AUTHOR>
     *
     * @param string $mid Mid
     * @date   2018/8/2 13:36
     * @return  void
     */
    public function movementCaptchaShow($mid = '')
    {
        if ($mid == 'XCTEST' || $mid == 'XQ')
        {
            $this->lineNum = 0;
            $this->pointNum = 0;
        }
        echo \Common\Utils\SecurityCode::Draw($this->randCodeStr, $this->codeLength, $this->gifTimes, $this->imageWidth, $this->imageHeight, $this->lineNum, $this->pointNum);
    }

}
