<!DOCTYPE html>
<head>
    <title>PHPRedmin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=gb2312" />
	<link rel="stylesheet" media="all" type="text/css" href="/css/redmin/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" media="all" type="text/css" href="/css/redmin/bootstrap/bootstrap-responsive.min.css">
    <link rel="stylesheet" media="all" type="text/css" href="/css/redmin/font-awesome.min.css">
	<link rel="stylesheet" media="all" type="text/css" href="/css/redmin/nv.d3.css" />
	<link rel="stylesheet" media="all" type="text/css" href="/css/redmin/custom.css" />
	<link rel="stylesheet" media="all" type="text/css" href="/css/redmin/jquery-ui.min.css" />
    <script type="text/javascript" src="/js/jquery.min.js"></script>
    <script type="text/javascript" src="/js/redmin/bootstrap.min.js"></script>

    <script src="/js/redmin/actions.js" type=\"text/javascript\"></script>
    <script src="/js/redmin/hashes.js" type=\"text/javascript\"></script>
    <script src="/js/redmin/lists.js" type=\"text/javascript\"></script>
    <script src="/js/redmin/modal.js" type=\"text/javascript\"></script>
    <script src="/js/redmin/remlists.js" type=\"text/javascript\"></script>
    <script src="/js/redmin/sets.js" type=\"text/javascript\"></script>
    <script src="/js/redmin/strings.js" type=\"text/javascript\"></script>
    <script src="/js/redmin/zsets.js" type=\"text/javascript\"></script>

    <script type="text/javascript">
        $(document).ready(function() {
            $('.disabled').click(function(e) {
                e.preventDefault();
            });

            $('#reset_stats').click(function(e) {
                e.preventDefault();

                $('.modal-footer .save').unbind();
                $('.modal-footer .save').click(function() {
                    $.ajax({
                        url: '<?=$this->router->url?>/actions/reset',
                        dataType: 'json',

                        success: function(data) {
                            location.href = '<?=$this->router->url?>';
                        }
                    });
                });

                $('#confirmation').modal('show');
            });

            $('#flush_all').click(function(e) {
                e.preventDefault();

                $('.modal-footer .save').unbind();
                $('.modal-footer .save').click(function() {
                    $.ajax({
                        url: '<?=$this->router->url?>/actions/fall',
                        dataType: 'json',
                        success: function(data) {
                            location.href = '<?=$this->router->url?>';
                        }
                    });

                });

                $('#confirmation').modal('show');
            });

            $('#flush_db').click(function(e) {
                e.preventDefault();

                $('.modal-footer .save').unbind();
                $('.modal-footer .save').click(function() {
                    $.ajax({
                        url: '<?=$this->router->url?>/actions/fdb',
                        dataType: 'json',
                        success: function(data) {
                            location.href = '<?=$this->router->url?>';
                        }
                    });
                });

                $('#confirmation').modal('show');
            });

            $(".redisKey ul li").click(function(){
                $(".add-on + input").not('input[name=LOG_HANDLER]').val($(this).html());
            });
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="navbar span12 navbar-inverse">
                <div class="navbar-inner">
                    <div class="container">
                        <a class="btn btn-navbar" data-toggle="collapse" data-target=".navbar-responsive-collapse">
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </a>
                        <a class="brand" href="/houtai_gl/redis/">PHPRedmin</a>
                        <div class="nav-collapse collapse navbar-responsive-collapse">
                            <ul class="nav">
                                <li>
                                    <a href="/houtai_gl/redis/">
                                        <i class="icon-white icon-home"></i> Home
                                    </a>
                                </li>
                                <li>
                                    <a href="/houtai_gl/redis/info">
                                        <i class="icon-white icon-info-sign"></i> Info
                                    </a>
                                </li>
                                <li>
                                    <a href="/houtai_gl/redis/confirm">
                                        <i class="icon-white icon-bar-chart"></i> 实例&集群检测比对
                                    </a>
                                </li>
                                <!--<li>-->
                                    <!--<a href="/houtai_gl/redis//config">-->
                                        <!--<i class="icon-white icon-cogs"></i> Configurations-->
                                    <!--</a>-->
                                <!--</li>-->
                                <!--<li>-->
                                    <!--<a href="/houtai_gl/redis//stats">-->
                                        <!--<i class="icon-white icon-bar-chart"></i> Stats-->
                                    <!--</a>-->
                                <!--</li>-->
                                <!--<li>-->
                                    <!--<a href="/houtai_gl/redis//slowlog">-->
                                        <!--<i class="icon-white icon-warning-sign"></i> Slow Log-->
                                    <!--</a>-->
                                <!--</li>-->
                            </ul>
                            <!--
                            <ul class="nav pull-right">
                                <li class="divider-vertical"></li>
                                <li class="dropdown">
                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">Actions <b class="caret"></b></a>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a href="#" id="flush_db">
                                                <i class="icon-trash"></i> Flush Db
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" id="flush_all">
                                                <i class="icon-remove"></i> Flush All
                                            </a>
                                        </li>
                                        <li class="divider"></li>
                                        <li>
                                            <a href="<?=$this->router->url?>/welcome/save/1" target="_blank">
                                                <i class="icon-save"></i> Asynchronous Save
                                            </a>
                                        </li>
                                        <li>
                                            <a href="<?=$this->router->url?>/welcome/save" target="_blank">
                                                <i class="icon-save"></i> Synchronous Save
                                            </a>
                                        </li>
                                        <li class="divider"></li>
                                        <li>
                                            <a href="#" id="reset_stats">
                                                <i class="icon-refresh"></i> Reset Stats
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="span12">
                <div class="alert alert-success">
                    <a class="close" data-dismiss="alert" href="#">×</a>
                    redis://{{$pageArray.redisConfig.host}}:{{$pageArray.redisConfig.port}} Using Password: {{if $pageArray.redisConfig.password}}Yes{{else}}No{{/if}}
                </div>
                {{if $pageArray.redisKeys}}
                <div class="alert alert-success redisKey">
                    <a class="close" data-dismiss="alert" href="#">×</a>
                    {{foreach from=$pageArray.redisKeys key=kinds item=list}}
                        <br/><b>{{$kinds}}</b><ul>
                    {{foreach from=$list key = intro item = searchkey}}
                    <li>{{$searchkey}}</li>
                    {{/foreach}}
                    </ul>
                    {{/foreach}}
                    <br/>
                </div>
                {{/if}}
            </div>
        </div>
        <div class="row">