<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=GBK">
        <title>禁止规则设置</title>
            <link href="/css/v3/global_2016_06_23.css" rel="stylesheet">
        <link href="/css/v3/zhanghao.css" rel="stylesheet">
        <link rel="stylesheet" href="/css/v3/top.css">
        <script type="text/javascript" src="/js/jquery.min.js"></script>
    </head>
    <body>
{{$smarty.session.topStr}}
{{include file="admin/left.tpl.html"}}
    <div class="mainWrap">
    
		<!--面包屑导航 开始-->
        <div class="location">位置：<a href="#" target="_blank">查询限制信息</a>  &gt; <span>设置</span></div>
        <!--面包屑导航 结束-->
        
        <div class="selectTab mt10">
            <a href="/houtai_gl/limit_setting" class="cur">设置</a> 
            <a href="/houtai_gl/limit_query?type=regforbidenip">注册禁止IP列表</a> 
            <a href="/houtai_gl/limit_query?type=loginforbidenip">登录禁止IP列表</a> 
            <a href="/houtai_gl/limit_query?type=getpwdforbidenuser">找回密码禁止用户列表</a> 
            <a href="/houtai_gl/limit_query?type=checkforbidenip">Check禁止IP列表</a> 
            <a href="/houtai_gl/limit_query?type=visitforbidenip">访问禁止IP列表</a>
        </div>
    
        <div class="contentWrap">
            <!--table数据列表 开始-->        
            <form action="" method="post" id="settingFromID">    
            <table cellspacing="0" cellpadding="0" class="tabMod forFixed">
                <tbody>
                	<tr>
                        <th>类型</th>
                        <th>配置</th>
                        <th>值</th>
                    </tr>
                    <tr >
                    <td rowspan="3">注册锁定IP</td>
                    <td>规则次数</td>
                    <td><input name="RIPLLN" value="{{$pageArray.settings.RIPLLN}}"/>次</td>
                </tr>
                <tr >
                    <td>规则时间</td>
                    <td><input name="RIPLLT" value="{{$pageArray.settings.RIPLLT}}"/>秒</td>
                </tr>
                <tr >
                    <td>持续时间</td>
                    <td><input name="RIPLCT" value="{{$pageArray.settings.RIPLCT}}"/>秒</td>
                </tr>
                <tr >
                    <td rowspan="3">注册锁定IP段</td>
                    <td>规则次数</td>
                    <td><input name="RIPDLLN" value="{{$pageArray.settings.RIPDLLN}}"/>次</td>
                </tr>
                <tr >
                    <td>规则时间</td>
                    <td><input name="RIPDLLT" value="{{$pageArray.settings.RIPDLLT}}"/>秒</td>
                </tr>
                <tr >
                    <td>持续时间</td>
                    <td><input name="RIPDLCT" value="{{$pageArray.settings.RIPDLCT}}"/>秒</td>
                </tr>
                <tr >
                    <td rowspan="3">注册禁止IP段</td>
                    <td>规则次数</td>
                    <td><input name="RIPDFLN" value="{{$pageArray.settings.RIPDFLN}}"/>次</td>
                </tr>
                <tr >
                    <td>规则时间</td>
                    <td><input name="RIPDFLT" value="{{$pageArray.settings.RIPDFLT}}"/>秒</td>
                </tr>
                <tr >
                    <td>持续时间</td>
                    <td><input name="RIPDFCT" value="{{$pageArray.settings.RIPDFCT}}"/>秒</td>
                </tr>
                <tr >
                    <td rowspan="7">登陆禁止IP</td>
                    <td>规则次数</td>
                    <td><input name="LIPFLN" value="{{$pageArray.settings.LIPFLN}}"/>次</td>
                </tr>
                <tr >
                    <td>规则时间</td>
                    <td><input name="LIPFLT" value="{{$pageArray.settings.LIPFLT}}"/>秒</td>
                </tr>
                <tr >
                    <td>登录失败规则次数</td>
                    <td><input name="LFIPFLN" value="{{$pageArray.settings.LFIPFLN}}"/>次</td>
                </tr>
                <tr >
                    <td>登录失败规则时间</td>
                    <td><input name="LFIPFLT" value="{{$pageArray.settings.LFIPFLT}}"/>秒</td>
                </tr>
                <tr >
                    <td>登录空用户规则次数</td>
                    <td><input name="LNIPFLN" value="{{$pageArray.settings.LNIPFLN}}"/>次</td>
                </tr>
                <tr >
                    <td>登录空用户规则时间</td>
                    <td><input name="LNIPFLT" value="{{$pageArray.settings.LNIPFLT}}"/>秒</td>
                </tr>
                <tr >
                    <td>持续时间</td>
                    <td><input name="LIPFCT" value="{{$pageArray.settings.LIPFCT}}"/>秒</td>
                </tr>
                <tr >
                    <td rowspan="2">登陆IP验证码</td>
                    <td>规则次数</td>
                    <td><input name="LIPCLN" value="{{$pageArray.settings.LIPCLN}}"/>次</td>
                </tr>
                <tr >
                    <td>规则时间</td>
                    <td><input name="LIPCLT" value="{{$pageArray.settings.LIPCLT}}"/>秒</td>
                </tr>
                <tr >
                    <td rowspan="3">Check禁止IP</td>
                    <td>规则次数</td>
                    <td><input name="CIPFLN" value="{{$pageArray.settings.CIPFLN}}"/>次</td>
                </tr>
                <tr >
                    <td>规则时间</td>
                    <td><input name="CIPFLT" value="{{$pageArray.settings.CIPFLT}}"/>秒</td>
                </tr>
                <tr >
                    <td>持续时间</td>
                    <td><input name="CIPFCT" value="{{$pageArray.settings.CIPFCT}}"/>秒</td>
                </tr>
                <tr >
                    <td>访问禁止IP</td>
                    <td>规则次数：每秒最多允许访问</td>
                    <td><input name="VIPFLN" value="{{$pageArray.settings.VIPFLN}}"/>次</td>
                </tr>
                <tr >
                    <td colspan="3">
                        <a class="styleBtnBlue left ml10" href="javascript:" onclick="$('#settingFromID').submit();">保存</a>
                    </td>
                </tr>                   
            	</tbody>
            </table>
            </form>
            <!--table数据列表 结束-->        
        </div><!--contentWrap end-->
    
	</div>
    <script>
    
        $('.navList a').each(function (){
            if (  $(this).attr('href') == '/houtai_gl/limit_query' )
            {
                $(this).addClass('select');
            }
        });
    </script>
    </body>
</html>