<?php

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 摘    要:PhoneCodeController.php
 * 作    者:<EMAIL>
 * 修改日期: 2015/10/20
 */
class PhoneController extends Controller
{

    private $redis;

    public function __construct()
    {
        parent::__construct();
        $this->redis = RedisEx::getInstance();
    }

    /**
     * 供各项目组调用-发送手机验证码
     * @return json        发送状态
     */
    public function actionSendCode()
    {
        $callback = preg_replace('/[^\w_\.\$]/', '', $_GET['callback']);
        $mid = $_GET['mid'];
        $action = $_GET['action'];
        $phone = $_GET['phone'];
        $verifyCode = $_GET['code'];
        $loginAction = loadAction('login');
        $authInfo = $loginAction->checkAuthCookie('i');
        $passid = $authInfo['i'];
        if (empty($passid))
        {
            echo "$callback(" . json_encode(array("code" => "300.4", "msg" => iconv("GBK", "UTF-8//IGNORE", "请先登录"))) . ")";
            return;
        }
        $midArr = Config::get("mid");
        if (!array_key_exists($mid, $midArr))
        {
            echo "$callback(" . json_encode(array("code" => "300.2", "msg" => iconv("GBK", "UTF-8//IGNORE", "项目标识错误"))) . ")";
            return;
        }
        $phoneAction = loadAction('phone');
        switch ($action)
        {
            case 'verify':
                $retCode = $phoneAction->sendVerifyCode($mid, $passid, 5, 158);
                break;
            case 'bind':
                $retCode = $phoneAction->sendBindCode($mid, $passid, $phone, 5, 150);
                break;
            case 'edit':
                $retCode = $phoneAction->sendEditCode($mid, $passid, $phone, $verifyCode, 5, 161);
                break;
            default:
                $retCode = false;
                break;
        }
        switch ($retCode)
        {
            case 200:
                echo "$callback(" . json_encode(array("code" => "200.0", "msg" => iconv("GBK", "UTF-8//IGNORE", "发送成功"))) . ")";
                break;
            case 300:
                echo "$callback(" . json_encode(array("code" => "300.1", "msg" => iconv("GBK", "UTF-8//IGNORE", "请输入正确的手机号码"))) . ")";
                break;
            case 301:
            case 302:
                echo "$callback(" . json_encode(array("code" => "300.5", "msg" => iconv("GBK", "UTF-8//IGNORE", "该手机号已经被绑定"))) . ")";
                break;
            case 303:
                echo "$callback(" . json_encode(array("code" => "300.3", "msg" => iconv("GBK", "UTF-8//IGNORE", "原手机验证码错误"))) . ")";
                break;
            case 400:
                echo "$callback(" . json_encode(array("code" => "400.0", "msg" => iconv("GBK", "UTF-8//IGNORE", "验证频繁，请稍候再试"))) . ")";
                break;
            case 402:
                echo "$callback(" . json_encode(array("code" => "400.1", "msg" => iconv("GBK", "UTF-8//IGNORE", "该账号已经绑定手机号"))) . ")";
                break;
            case 404:
                echo "$callback(" . json_encode(array("code" => "300.5", "msg" => iconv("GBK", "UTF-8//IGNORE", "未绑定手机号码"))) . ")";
                break;
            case 500:
                echo "$callback(" . json_encode(array("code" => "500.0", "msg" => iconv("GBK", "UTF-8//IGNORE", "服务器忙，请稍候再试"))) . ")";
                break;
            default:
                break;
        }
    }

    /**
     * 发送快捷注册验证码
     */
    public function actionSendRegCode()
    {
        session_start();
        $mid = \Common\Utils\Url::getStringParam('mid');
        $phone = \Common\Utils\Url::getStringParam('phone');

        $midArr = Config::get("mid");
        $lockPro = Config::get("lockpro");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        if ($lockPro && array_key_exists($mid, $lockPro))
        {
            if (
                (empty($_SESSION['captcha_phone']) || $_SESSION['captcha_phone'] != $phone) &&
                (
                    empty($_GET['captcha_code']) ||
                    empty($_SESSION['captcha_code']) ||
                    strtoupper($_GET['captcha_code']) != strtoupper($_SESSION['captcha_code'])
                )
            ) {
                unset($_SESSION['captcha_phone']);
                unset($_SESSION['captcha_code']);
                $this->showError304();
            }
            unset($_SESSION['captcha_code']);
        }
        //注意:手机联盟站点启用后,再删掉下面的代码
        if ($mid == 'SJLM')
        {
            $this->show200();
        }
        $phoneAction = loadAction("phone");
        $positionId = 201;
        $sessionVerify = true;
        $retCode = $phoneAction->sendRegCode($mid, $phone, 8, $positionId, $sessionVerify);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 301:
                $this->showError301();
                break;
            case 302:
                $this->showError302();
                break;
            case 400:
                $this->showError400();
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 验证并快捷注册,
     */
    public function actionQuickReg()
    {
        $phone = \Common\Utils\Url::getStringParam("phone");
        $mid = \Common\Utils\Url::getStringParam("mid");
        $verifyCode = \Common\Utils\Url::getStringParam("verify_code");

        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->codeResponse(300, "", "请确认您的手机号是否填写正确!");
        }

        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->codeResponse(501, "", "项目标识不正确!");
        }

        $sessionVerify = true;

        $phoneAction = loadAction("phone");
        if (!$phoneAction->checkRegCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify, true))
        {
            $this->codeResponse(400, "", "短信验证失败!");
        }

        $userClientIp = get_client_ip();
        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $pwd = mt_rand() . time();
        $password = substr(md5($pwd . MD5KEY), 0, 16);

        $Zxcvbn = new \ZxcvbnPhp\Zxcvbn();
        $pwdScore = $Zxcvbn->passwordStrength($password);
        $pwdStrength = $pwdScore['score'];
        $regAction = loadAction('reg');

        $gid = 300;
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        $source = \Service\AccountStat\Consts\AccountStat::getSourceByMid($mid);
        if(!$source) {
            $source =  parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
            if(!$source)
            {
                $source = "webapi";
            }
        }
        $result = $regAction->regPhone($phone, $password, $pwdStrength, $userClientIp, 'login.2345.com', $gid, $source, $clientType);
        $states = explode(".", $result[0]);
        if ($states[0] == 400)
        {
            $this->codeResponse(403, "", 'error:' . $msgs[$states[0]][$states[1]]);
        }
        else if ($states[0] == 300)
        {
            $this->codeResponse($states[0] + $states[1], "", $msgs[$states[0]][$states[1]]);
        }
        else if ($states[0] == 200)
        {
            $cookie = $result[1]['cookie'];
            $forward = getForwardUrl();
            $setCookieArr = $this->doLogin($cookie, $forward);
            $data = array(
                "loadPage" => $setCookieArr
            );
            $this->codeResponse(200, $data, "注册成功！");
        }
        else
        {
            $this->codeResponse(500, "", "禁止注册！");
        }
    }

    /**
     * 发送快捷登录验证码
     */
    public function actionSendLoginCode()
    {
        session_start();
        $mid = \Common\Utils\Url::getStringParam('mid');
        $phone = \Common\Utils\Url::getStringParam('phone');
        $midArr = Config::get("mid");
        $lockPro = Config::get("lockpro");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        if ($lockPro && array_key_exists($mid, $lockPro))
        {
            if (
                (empty($_SESSION['captcha_phone']) || $_SESSION['captcha_phone'] != $phone) &&
                (
                    empty($_GET['captcha_code']) ||
                    empty($_SESSION['captcha_code']) ||
                    strtoupper($_GET['captcha_code']) != strtoupper($_SESSION['captcha_code'])
                )
            ) {
                unset($_SESSION['captcha_phone']);
                unset($_SESSION['captcha_code']);
                if (!isset($_REQUEST['wxGroup']) || $_REQUEST['wxGroup'] != 1) {
                    $this->showError304();
                }
            }
            unset($_SESSION['captcha_code']);
        }
        //注意:手机联盟站点启用后,再删掉下面的代码
//        if ($mid == 'SJLM')
//        {
//            $this->show200();
//        }
        $sessionVerify = true;
        $positionId = 200;
        $phoneAction = loadAction("phone");
        $retCode = $phoneAction->sendLoginCode($mid, $phone, 8, $positionId, $sessionVerify);
        switch ($retCode)
        {
            case 200:
                $_SESSION['captcha_phone'] = $phone;
                $this->show200();
                break;
            case 300:
                $this->showError300();
                break;
            case 400:
                $this->showError400();
                break;
            case 403:
                $this->showError403();
                break;
            case 404:
                $this->codeResponse(404, "", "该手机号不存在！");
                break;
            case 500:
            default:
                $this->showError500();
                break;
        }
    }

    /**
     * 手机号短信快捷登录
     */
    public function actionLogin()
    {
        $mid = \Common\Utils\Url::getStringParam('mid');
        $phone = \Common\Utils\Url::getStringParam('phone');
        $verifyCode = \Common\Utils\Url::getStringParam('verify_code');

        $regex = Config::get('regex');
        if (!preg_match($regex['phone'], $phone))
        {
            $this->showError300();
        }
        $midArr = Config::get("mid");
        if (!isset($midArr[$mid]))
        {
            $this->showError501();
        }
        $phoneAction = loadAction("phone");

        $sessionVerify = true;
        if (!$phoneAction->checkLoginCode($mid, $phone, md5($phone . $verifyCode), $sessionVerify, true))
        {
            $this->showError303();
        }
        $memberModel = loadModel('member');
        $passid = $memberModel->getPassidByPhone($phone);
        if (!$passid)
        {
            //手机号,未绑定手机号, 但存在用户名
            $passid = $memberModel->getPassidByUsername($phone);
        }
        $user = [];
        if (!empty($passid))
        {
            $user = $memberModel->read($passid);
            $isProhibitLogin = $phoneAction->prohibitUsernameAsPhoneDiffBindPhoneLogin($user, $phone);
            if ($isProhibitLogin)
            {
                $this->codeResponse(400, "", "登录失败!");
            }
        }

        //收集注册登录信息
        $st = !!$passid;
        $type = \Service\AccountStat\Consts\AccountStat::TP_LOGIN;
        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        $source = parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
        \Service\AccountStat\AccountStat::collect($type, $clientType, $phone, $st, $source);

        if ($passid)
        {
//            if ($mid == 'XQ')
//            {
//                $redisGetSuccess = true;
//                $memberRedis = loadAction('memberRedis');
//                $tmpResult = $memberRedis->hashGet($phone, REG_PHONE_INFO_REDIS_KEY, $phone);
//                $memberRedis->closeUserDataRedis();
//                if (empty($tmpResult) || $tmpResult == -1)
//                {
//                    $redisGetSuccess = false;
//                    //throw new Exception('redis中读取数据为空, passId:' . $passid);
//                }
//                $user = unserialize($tmpResult);
//                $necessaryFieldArray = array(
//                    'userName',
//                    'mUID',
//                    'gid',
//                );
//                foreach ($necessaryFieldArray as $field)
//                {
//                    if (!isset($user[$field]) || empty($user[$field]))
//                    {
//                        $redisGetSuccess = false;
//                        //throw new Exception('redis中读取数据为空, passId:' . $passid);
//                        break;
//                    }
//                    if ($field == 'mUID' && !is_array($user[$field]))
//                    {
//                        $redisGetSuccess = false;
//                        //throw new Exception('redis中读取数据为muid不是数组, passId:' . $passid);
//                        break;
//                    }
//                }
//
//                if (!$redisGetSuccess)
//                {
//                    $user = $memberModel->read($passid);
//                    $user["m_uid"] = unserialize($user['m_uid']);
//                    $uid = $user['m_uid']['1'];
//                    $userName = $user['username'];
//
//                    $saveInfo = $memberModel->getSaveInfoMemberRedis($passid, $user["m_uid"], $user['gid'], $user['username']);
//                    $memberRedis->hashSet($phone, REG_PHONE_INFO_REDIS_KEY, $phone, serialize($saveInfo));
//                    $memberRedis->closeUserDataRedis();
//                }
//                else
//                {
//                    $user["m_uid"] = $user['mUID'];
//                    $uid = $user['m_uid']['1'];
//                    $userName = $user['userName'];
//                }
//
//                //repair操作放入redis
//                $saveInfo['phone'] = $phone;
//                $saveInfo['passId'] = $passid;
//                $loginQueueRedis = loadAction('loginQueueRedis');
//                $pushResult = $loginQueueRedis->pushToLoginQueue(serialize($saveInfo));
//                $loginQueueRedis->closeLoginQueueRedis();
//
//                if (empty($pushResult) || $pushResult == -1)
//                {
//                    $phoneAction = loadAction("phone");
//                    $newUserName = $phoneAction->repairUsernameWithPhoneNum($phone, $user, "webapi");
//                    if ($newUserName)
//                    {
//                        $userName = $newUserName;
//                    }
//                    else
//                    {
//                        $userName = $user['username'];
//                    }
//                }
//                $userMod = $user['gid'] % 100 == 0 ? $user['gid'] : 0;
//                $loginAction = loadAction("login");
//                $cookie = $loginAction->getLoginCookie($passid, $uid, $userName, $userMod);
//            }
//            else
//            {
//                $user = $memberModel->read($passid);
                $phoneAction = loadAction("phone");
                $newUserName = $phoneAction->repairUsernameWithPhoneNum($phone, $user, "webapi");
                if ($newUserName)
                {
                    $username = $newUserName;
                }
                else
                {
                    $username = $user['username'];
                }

                $user["m_uid"] = unserialize($user['m_uid']);
                $uid = $user['m_uid']['1'];
                $userMod = $user['gid'] % 100 == 0 ? $user['gid'] : 0;
                $loginAction = loadAction("login");
                $cookie = $loginAction->getLoginCookie($passid, $uid, $username, $userMod);
//            }

            $forward = getForwardUrl();
            $setCookieArr = $this->doLogin($cookie, $forward);
            $data = array(
                "loadPage" => $setCookieArr
            );
            $this->codeResponse(200, $data, "登录成功!");
        }
        $this->codeResponse(400, "", "登录失败!");
    }

    private function doLogin($cookie, $forward = "")
    {
        $loginAction = loadAction("login");
        $vTime = \Common\Utils\Url::getIntParam("vTime");
        $autoLogin = \Common\Utils\Url::getStringParam("autoLogin");

        if ($vTime != 0)
        {
            $cTime = time() + $vTime;
        }
        else
        {
            $cTime = time() + 3600 * 24 * 30;
        }
        if ($autoLogin)
        {
            $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);
            setcookie('autoLogin', 1, $cTime, '/', $_SERVER['HTTP_HOST']);
        }
        else
        {
            $loginAction->setLoginCookie($cookie, 0, DOMAIN);
            setcookie('autoLogin', "", time() - 3600, '/', $_SERVER['HTTP_HOST']);
        }

        if (isset($_REQUEST['mid']) && trim($_REQUEST['mid']) === 'TGPT')
        {
            //TGPT无需此功能，因此为空
            $thirdCallbackCookies = array();
        }
        else
        {
            // 设置其他域名的登录cookie： 2345.cn等
            $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
        }
        return $thirdCallbackCookies;
    }

    /**
     * 返回成功信息
     */
    private function show200($msg = '发送成功！')
    {
        $this->codeResponse(200, "", $msg);
    }

    /**
     * 显示300错
     */
    private function showError300()
    {
        $this->codeResponse(300, "", "请输入正确的手机号码！");
    }

    /**
     * 显示301错
     */
    private function showError301()
    {
        $this->codeResponse(301, "", "该手机号已被其他用户占用！");
    }

    /**
     * 显示302错
     */
    private function showError302()
    {
        $this->codeResponse(302, "", "该手机号已被其他用户绑定！");
    }

    /**
     * 显示303错
     */
    private function showError303()
    {
        $this->codeResponse(303, "", "手机短信验证码错误！");
    }

    /**
     * 显示304错
     */
    private function showError304()
    {
        $this->codeResponse(304, "", "请输入正确的图片验证码！");
    }

    /**
     * 显示400错
     */
    private function showError400()
    {
        $this->codeResponse(400, "", "发送频繁，请稍后再试！！");
    }

    /**
     * 显示401错
     */
    private function showError401()
    {
        $this->codeResponse(401, "", "身份验证失效，请重新登录！");
    }

    /**
     * 显示402错
     */
    private function showError402()
    {
        $this->codeResponse(402, "", "已绑定过手机号！");
    }

    /**
     * 显示403错
     */
    private function showError403()
    {
        $this->codeResponse(403, "", "已该手机号禁止使用！");
    }

    /**
     * 显示404错
     */
    private function showError404()
    {
        $this->codeResponse(404, "", "你的帐号未绑定手机！");
    }

    /**
     * 显示500错
     */
    private function showError500()
    {
        $this->codeResponse(500, "", "服务器忙，请稍后再试！");
    }

    /**
     * 显示501错
     */
    private function showError501()
    {
        $this->codeResponse(501, "", '项目标识不正确！');
    }

}