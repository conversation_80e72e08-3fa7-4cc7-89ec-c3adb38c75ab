<?php

/**
 * Copyright (c) 2012,上海瑞创网络科技股份有限公司
 * 摘    要：日志记录类
 * 作    者：tianj<<EMAIL>>
 * 修改日期：2012.10.29
 */

/**
 * 说明:
 * 1.
 * 系统异常错误记录 请使用 静态方法 Com_Log::log() 直接记录
 * 2. 其他的日志记录请实例化后使用
 * 使用范例:
 * $log = new Com_Log();
 * 自动加载, 无需include
 * $log->setPath('cron/haozip')->setName('day');
 * 根目录位于/LOGS/ 下 'cron/haozip' 表示/LOGS/cron/haozip 目录
 * setName 为文件名 可选项: (day|month|year|custom)
 * $log->write('############');
 * 每句记录一行, 会自动在每行前加上时间
 *
 * <AUTHOR>
 * @datetime 2012-10-29
 */
class ComLog
{

    /**
     * 系统异常错误记录
     * User: panj
     * @param string $msg msg
     * @param string $level level
     * @return 无
     */
    public static function log($msg, $level = 'error')
    {
        $logDir = ROOT_PATH . '/app/logs/log';
        if (!is_dir($logDir))
        {
            mkdir($logDir, 0777, true);
        }

        $logName = ROOT_PATH . '/LOGS/log/' . date('Ymd') . '.log';
        file_put_contents($logName, date('Y-m-d H:i:s') . "\t" . "[$level]" . $msg . "\r\n", FILE_APPEND);
    }

    /**
     * CRON性能分析日志-记录cron脚本运行时间
     * User: panj
     * @param string $log log
     * @return 无
     */
    public static function cronProfileLog($log)
    {
        $logger = new self();
        $logger->setPath('cron/profile')->setName('day');
        $logger->write($log);
    }

    /**
     * 记录各种接口log
     * User: panj
     * @param string $path 路径
     * @param string $request_url url
     * @param string $request 请求
     * @param string $response 相应
     * @return 无返回
     */
    public static function logApi($path, $request_url, $request, $response)
    {
        $log = new self();
        $log->setPath($path)->setName('day');
        $content = array(
            "REQUEST_URL:" => $request_url,
            'REQUEST:' => $request,
            "RESPONSE:" => $response
        );
        $log->write(var_export($content, true));
    }

    private $path = null;

    private $logName = null;

    private $isPersistence = false;

    private $fileHandle;

    /**
     * 设置日志存储路径
     * User: panj
     * @param string $path 路径
     * @return $this
     */
    public function setPath($path)
    {
        $logRoot = ROOT_PATH . '/app/logs';
        if (substr($path, 0, 1) != '/')
        {
            $path = '/' . $path;
        }
        if (substr($path, strlen($path), 1) == '/')
        {
            $path = substr($path, 0, strlen($path) - 1);
        }
        $path = $logRoot . $path;
        if (!is_dir($path))
        {
            $this->mkdirs($path);
        }
        $path .= '/';

        $this->path = $path;
        return $this;
    }

    /**
     * 设置日志文件名
     * User: panj
     * @param string $fileName 文件名
     * @return $this
     */
    public function setName($fileName)
    {
        if ($fileName == 'day')
        {
            $this->logName = date('Y-m-d');
        }
        elseif ($fileName == 'month')
        {
            $this->logName = date('Y-m');
        }
        elseif ($fileName == 'year')
        {
            $this->logName = date('Y');
        }
        else
        {
            $this->logName = $fileName;
        }
        return $this;
    }

    /**
     * User: panj
     * @return string
     */
    public function getName()
    {
        return $this->path . $this->logName . '.log';
    }

    /**
     * 设置是否持久化打开文件
     * 注意: 使用持久化打开文件时, 必须手动使用close 方法关闭文件
     * User: panj
     * @param bool $isPersistence 是否持久化
     * @return $this
     */
    public function setPersistence($isPersistence)
    {
        $this->isPersistence = $isPersistence;
        return $this;
    }

    /**
     * 打开文件
     * User: panj
     * @return 无
     */
    private function open()
    {
        if (is_null($this->path))
        {
            $this->setPath('logs');
        }
        if (is_null($this->logName))
        {
            $this->setName(date('Y-m-d'));
        }
        $filePath = $this->path . $this->logName . '.log';
        $this->fileHandle = fopen($filePath, 'a');
    }

    /**
     * 记录一行日志
     * User: panj
     * @param string $log log
     * @param boolean $needDate $needDate
     * @return 无
     */
    public function write($log, $needDate = true)
    {
        if (is_null($this->fileHandle))
        {
            $this->open();
        }
        $logContent = $needDate ? date('Y-m-d H:i:s') . "\t$log\r\n" : "$log\r\n";
        fwrite($this->fileHandle, $logContent);

        if (!$this->isPersistence)
        {
            $this->close();
            $this->fileHandle = null;
        }
    }

    /**
     * 关闭文件
     * User: panj
     * @return 无
     */
    public function close()
    {
        fclose($this->fileHandle);
    }

    /**
     * 创建目录
     * User: panj
     * @param string $dir 路径
     * @return bool
     */
    public static function mkdirs($dir)
    {
        if (!is_dir($dir))
        {
            if (!ComLog::mkdirs(dirname($dir)))
            {
                return false;
            }
            if (!mkdir($dir, 0755))
            {
                return false;
            }
        }
        return true;
    }
}
