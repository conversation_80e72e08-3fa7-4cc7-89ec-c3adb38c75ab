<?php
/**
 * Copyright (c)  上海二三四五网络科技股份有限公司
 * 文件名称：DatabaseArchive.php
 * 摘    要：
 * 作    者：谢志新 <<EMAIL>>
 * 修改日期：02-09, 2017
 */

namespace Crontab;

use Controller;

class DatabaseArchiveController extends Controller
{

    /**
     * 归档 短信发送日志
     * @return null
     */
    public function actionIndex()
    {
        // 本脚本整点运行，保证只运行一个进程
        $redisAction = \RedisAction::connect();
        $archiveStatus = $redisAction->get("DataArchiveStatus");
        if ($archiveStatus && $archiveStatus == 1)
        {
            exit;
        }
        else
        {
            $redisAction->setex("DataArchiveStatus", 10800, 1);
        }

        // 3表归至存档表
        //$this->actionArchive();

        // 删除cache表中X天前数据，当前为3天
        $this->actionChaseCache();

        // 删除三表数据
        //$this->actionDeleteData();

        // 原先为主库+大归档表，以下命令为把大归档表转换为按月归档，现已没有意义
        // $this->actionTransfer();

        // 运行结束修改状态标记
        $redisAction->setex("DataArchiveStatus", 10800, 0);
    }

    /**
     * dealSql
     * @param \Octopus\PdoEx   $pdo         pdo
     * @param \Octopus\RedisEx $redisAction reidsAction
     * @param string           $sql         sql
     * @param array            $params      params
     * @return null
     */
    private function dealSql(&$pdo, &$redisAction, $sql, $params)
    {
        $res = $pdo->query($sql, $params);
        if ($res === false)
        {
            $pdo->rollBack();

            $this->sendEmail(
                "短信平台数据归档异常",
                "sql:<BR>$sql\n<BR>params:" . var_export($params, true) . "\n<BR>error:" . $pdo->lastError()
            );
            $redisAction->setex("DataArchiveStatus", 10800, 0);
            exit;
        }
    }


    /**
     * send email
     * @param string $mailSubject subject
     * @param string $mailBody    content
     * @return bool
     */
    private function sendEmail($mailSubject, $mailBody)
    {
        $mailSubject = "【短信平台报警】" . $mailSubject;
        if (RUNMODE === 'development')
        {
            $mailSubject = "【测试环境】" . $mailSubject;
        }

        //smtp配置
        $smtpConfig = array(
//            'server'   => '58.211.78.142',
            'server'   => 'smtp.exmail.qq.com',
            'port'     => 25,
            'username' => '<EMAIL>',
            'password' => 'PdC8afe9',
            'email'    => '<EMAIL>',
        );
        $smtpemailto = '<EMAIL>,<EMAIL>,<EMAIL>';
        $smtpemailcc = '';
        $mailtype = "HTML";
        require_once(SRCPATH . '/includes/mail/Smtp.php');
        $smtp = new \Smtp;

        $smtp->init($smtpConfig['server'], $smtpConfig['port'], $smtpConfig['username'], $smtpConfig['password']);
        $smtp->sendmail($smtpemailto, $smtpConfig['email'], $mailSubject, $mailBody, $mailtype, $smtpemailcc);

        return true;
    }

    /**
     * 删除cache表中X天前数据
     * @return void
     */
    public function actionChaseCache()
    {
        //$nowHour = date("G");
        //if ($nowHour !== 1)
        //{
        //    return;
        //}
        $pdo = null;
        if (!isset($pdo) || !$pdo)
        {
            $dbConfig = \Config::get("database");
            $pdo = \Octopus\PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);
        }
        $redisAction = \RedisAction::connect();
        $limitTime = date('Y-m-d H:i:s', time() - 86400 * 3);
        $lastSql = "SELECT `log_id` FROM `sms_logs_cache` WHERE `send_time` < :limitTime ORDER BY `log_id` DESC LIMIT 1";
        $lastRes = $pdo->find($lastSql, array(':limitTime' => $limitTime));
        if (!$lastRes)
        {
            return;
        }
        $lastId = $lastRes['log_id'];

        $pdo->beginTransaction();
        $sql = "DELETE FROM `sms_logs_cache` WHERE log_id <= :id";
        $this->dealSql($pdo, $redisAction, $sql, array(':id' => $lastId));
        $pdo->commit();
    }

    public function actionDeleteData()
    {
        if (time() > strtotime('2018-01-26 18:10:00'))
        {
            return;
        }
        $pdo = null;
        if (!isset($pdo) || !$pdo)
        {
            $dbConfig = \Config::get("database");
            $pdo = \Octopus\PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);
        }
        $sqlTwo = "DELETE FROM my_sms_logs WHERE id <= :lastInsertId";
        $sqlThree = "DELETE FROM my_sms_logs_channel WHERE log_id <= :lastInsertId";
        $sqlFour = "DELETE FROM sms_channel_status WHERE log_id <= :lastInsertId";
        $params = array(':lastInsertId' => '284737754');
        $pdo->query($sqlTwo, $params);
        $pdo->query($sqlThree, $params);
        $pdo->query($sqlFour, $params);
    }

    /**
     * 三表转移至存档表
     * @return void
     */
    public function actionArchive()
    {
        $nowHour = date("G");
        // 10点到18点不归档;
        if ($nowHour >= 10 && $nowHour <= 17)
        {
            return;
        }
        // 2点执行从库备份脚本，暂停
        if ($nowHour == 2)
        {
            return;
        }

        $pdo = "";
        $limit = 300;
        $index = 0;
        $redisAction = \RedisAction::connect();

        // 35天确保每个月的月底可以完整查询上个月的数据
        //$limitTime = strtotime("-35 days");
        // 定位到2017年11月30日
        $limitTime = strtotime("2017-11-30 23:59:59");
        $monthTableLast = "";

        $sqlOne = <<<SQLONE
INSERT INTO sms_logs_archive_[monthTable] (
    `log_id`, `sessionid`, `phone`, `text`, `send_time`, `send_status`, `send_response`,
    `send_id`, `uid`, `passid`, `type`, `business_id`, `client_ip`, `server_ip`,
    `pid`, `channel`, `msg_id`, `code_status`, `code_desc`, `sms_count`,
    `callback_time`, `callback_status`, `callback_message` )
(SELECT
    msl.id, msl.sessionid, msl.phone, msl.text, msl.send_time, msl.send_status, msl.send_response, 
    msl.send_id, msl.uid, msl.passid, msl.type, msl.business_id, msl.client_ip, msl.server_ip,
    msl.pid, mslc.channel, mslc.msgId, mslc.codeStatus, mslc.codeDesc, mslc.smsCount,
    scs.add_time, scs.status, scs.message
FROM my_sms_logs AS msl 
LEFT JOIN my_sms_logs_channel AS mslc ON `msl`.`id` = `mslc`.`log_id`
LEFT JOIN sms_channel_status AS scs ON `msl`.`id` = `scs`.`log_id`
WHERE `send_time` < '[nextMonthTime]' 
GROUP BY msl.id 
ORDER BY msl.id ASC
LIMIT $limit)
SQLONE;

        $sqlTwo = "DELETE FROM my_sms_logs WHERE id <= :lastInsertId LIMIT $limit";
        $sqlThree = "DELETE FROM my_sms_logs_channel WHERE log_id <= :lastInsertId LIMIT $limit";
        $sqlFour = "DELETE FROM sms_channel_status WHERE log_id <= :lastInsertId LIMIT $limit";
        $sqlTable = "CREATE TABLE IF NOT EXISTS `sms_logs_archive_[monthTable]` LIKE `sms_logs_archive`";

        while ($index++ < 1500)
        {
            if (!isset($pdo) || !$pdo)
            {
                $dbConfig = \Config::get("database");
                $pdo = \Octopus\PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);
            }

            $sql = "SELECT `send_time` FROM `my_sms_logs` ORDER BY `send_time` ASC LIMIT 1";
            $lastRes = $pdo->find($sql);

            if (!$lastRes)
            {
                $this->sendEmail(
                    "短信平台数据归档异常",
                    "sql:<BR>$sql<BR><BR>params:<BR>"
                );
                $redisAction->setex("DataArchiveStatus", 10800, 0);
                return;
            }
            else
            {
                // 90天内
                if ($limitTime < strtotime($lastRes["send_time"]))
                {
                    break;
                }
            }

            $nextMonthTime = date('Y-m-01 00:00:00', strtotime($lastRes["send_time"] . ' +1 month'));
            $monthTable = date('Ym', strtotime($lastRes["send_time"]));

            $sqlOneNew = str_replace(array('[monthTable]', '[nextMonthTime]'), array($monthTable, $nextMonthTime), $sqlOne);
            $sqlTableNew = str_replace(array('[monthTable]'), array($monthTable), $sqlTable);

            // 建表操作会导致事物自动提交并无法回滚，故放在事务之前
            if ($monthTableLast != $monthTable)
            {
                $resTable = $pdo->query($sqlTableNew);
                $monthTableLast = $monthTable;
            }

            $pdo->beginTransaction();
            try
            {
                $res1 = $pdo->query($sqlOneNew);
                if ($res1 === 0)
                {
                    //无数据
                    break;
                }
                elseif (!$res1)
                {
                    $pdo->rollBack();
                    $this->sendEmail(
                        "短信平台数据归档异常",
                        "sql:<BR>$sqlOneNew<BR><BR>params:<BR><BR>"
                    );
                    $redisAction->setex("DataArchiveStatus", 10800, 0);
                    break;
                }

                $paramsTwo = array(
                    ":lastInsertId" => $pdo->lastInsertId(),
                );
                $this->dealSql($pdo, $redisAction, $sqlTwo, $paramsTwo);
                $this->dealSql($pdo, $redisAction, $sqlThree, $paramsTwo);
                $this->dealSql($pdo, $redisAction, $sqlFour, $paramsTwo);

                $pdo->commit();
            }
            catch (\Exception $exception)
            {
                $this->sendEmail("短信平台数据归档异常", $exception->getMessage());
                $redisAction->setex("DataArchiveStatus", 10800, 0);
                return;
            }
            // 200*3 = 600
            if ($index % 4 === 0)
            {
                sleep(1);
            }
            // 200*100 = 2W
            if ($index % 100 === 0)
            {
                unset($pdo);
                \Octopus\PdoEx::delInstance(DATABASE_SMS);
            }
        }
    }

    /**
     * 转移已有数据表
     * @return void
     */
    public function actionTransfer()
    {
        $pdo = "";
        $limit = 500;
        $index = 0;
        $monthTableLast = "";
        $redisAction = \RedisAction::connect();
        $sqlOne = <<<SQLONE
INSERT INTO sms_logs_archive_[monthTable] (
    `log_id`, `sessionid`, `phone`, `text`, `send_time`, `send_status`, `send_response`,
    `send_id`, `uid`, `passid`, `type`, `business_id`, `client_ip`, `server_ip`,
    `pid`, `channel`, `msg_id`, `code_status`, `code_desc`, `sms_count`,
    `callback_time`, `callback_status`, `callback_message` )
(SELECT
    `log_id`, `sessionid`, `phone`, `text`, `send_time`, `send_status`, `send_response`,
    `send_id`, `uid`, `passid`, `type`, `business_id`, `client_ip`, `server_ip`,
    `pid`, `channel`, `msg_id`, `code_status`, `code_desc`, 0,
    `callback_time`, `callback_status`, `callback_message`
FROM sms_logs_archive  
WHERE `send_time` < '[nextMonthTime]' 
ORDER BY `log_id` ASC
LIMIT $limit)
SQLONE;

        $sqlTwo = "DELETE FROM sms_logs_archive WHERE log_id <= :lastInsertId LIMIT $limit";
        $sqlTable = "CREATE TABLE IF NOT EXISTS `sms_logs_archive_[monthTable]` LIKE `sms_logs_archive_201701`";

        while ($index++ < 1000)
        {
            if (!isset($pdo) || !$pdo)
            {
                $dbConfig = \Config::get("database");
                $pdo = \Octopus\PdoEx::getInstance(DATABASE_SMS, $dbConfig[DATABASE_SMS]);
            }

            $sql = "SELECT `send_time` FROM `sms_logs_archive` ORDER BY `log_id` ASC LIMIT 1";
            $lastRes = $pdo->find($sql);

            if ($lastRes == false)
            {
                //错误和没有数据都是false，此处不报警
                $redisAction->setex("DataArchiveStatus", 10800, 0);
                return;
            }

            $nextMonthTime = date('Y-m-01 00:00:00', strtotime($lastRes["send_time"] . ' +1 month'));
            if (date('Y', strtotime($lastRes["send_time"])) >= 2017)
            {
                $monthTable = date('Ym', strtotime($lastRes["send_time"]));
            }
            else
            {
                $monthTable = date('Y', strtotime($lastRes["send_time"]));
            }

            $sqlOneNew = str_replace(array('[monthTable]', '[nextMonthTime]'), array($monthTable, $nextMonthTime), $sqlOne);
            $sqlTableNew = str_replace(array('[monthTable]'), array($monthTable), $sqlTable);

            //建表操作会导致事物自动提交并无法回滚
            if ($monthTableLast != $monthTable)
            {
                $resTable = $pdo->query($sqlTableNew);
                $monthTableLast = $monthTable;
            }

            $pdo->beginTransaction();
            try
            {
                $res1 = $pdo->query($sqlOneNew);
                if ($res1 === 0)
                {
                    $redisAction->setex("DataArchiveStatus", 10800, 0);
                    return;
                }
                elseif (!$res1)
                {
                    $pdo->rollBack();
                    $this->sendEmail(
                        "短信平台历史表转移异常",
                        "sql:<BR>$sqlOne<BR><BR>params:<BR><BR>"
                    );
                    $redisAction->setex("DataArchiveStatus", 10800, 0);
                    return;
                }

                $paramsTwo = array(
                    ":lastInsertId" => $pdo->lastInsertId(),
                );
                $this->dealSql($pdo, $redisAction, $sqlTwo, $paramsTwo);
                $pdo->commit();
            }
            catch (\Exception $exception)
            {
                $this->sendEmail("短信平台历史表转移异常", $exception->getMessage());
                $redisAction->setex("DataArchiveStatus", 10800, 0);
                return;
            }
            if ($index % 3 == 0)
            {
                sleep(1);
            }
            if ($index % 100 == 0)
            {
                unset($pdo);
                \Octopus\PdoEx::delInstance(DATABASE_SMS);
            }
        }
    }

}
