<?php

use \Service\AccountStat\Consts\AccountStat;
use \Service\Encryption\Aes\AesManager;

/**
 * Copyright (c) 2012,上海二三四五网络科技股份有限公司
 * 摘    要:RegController.php
 * 作    者:<EMAIL>
 * 修改日期: 2015/9/17
 */
// 注册
class RegController extends Controller
{

    private function returnJson($data)
    {
        return json_encode($data);
    }

    // 注册入口，包括注册页面和注册提交
    public function actionIndex()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') // 注册提交
        {
            /* -----添加注册日志----- */
            $pwdHasMd5 = false;
            $logPath = APPPATH . "/logs/" . date("Ymd") . "/webapi_reg";
            deepMkdir($logPath);
            $logger = new Octopus\Logger("webapi_reg");
            $logger->pushHandler(new \Octopus\Logger\Handler\StreamHandler($logPath . "/" . date('H') . ".log"));

            // 加密包在此处解密
            if (isset($_POST['keyVersion']) && isset($_POST['encryptData']) && isset($_POST['encryptKey'])) {
                $flag = true;
                $priKeyA = Config::get('regPriKey');
                $sign = md5('keyVersion=' . $_POST['keyVersion'] . '&encryptKey=' . $_POST['encryptKey'] . '&encryptData=' . $_POST['encryptData']);
                // 数据签名验证防止数据丢失
                if (!isset($_POST['sign']) || $_POST['sign'] != $sign || !isset($priKeyA[intval($_POST['keyVersion'])])) {
                    $flag = false;
                    $logger->info("加密数据签名验证失败，或者加签key不匹配", $_POST);
                }
                // 私钥解密
                $priKey = openssl_pkey_get_private($priKeyA[intval($_POST['keyVersion'])]);
                if ($flag && $priKey !== false && openssl_private_decrypt(base64_decode($_POST['encryptKey']), $aesKey, $priKey)) {
                    if (strlen($aesKey) != 16) {
                        $flag = false;
                        $logger->warning("解密后的aesKey格式不正确", $_POST);
                    }
                } else {
                    $flag && $logger->warning("私钥读取失败，或者数据解密失败", $_POST);
                }
                // 数据解密
                if ($flag) {
                    $deData = (new AesManager($aesKey, '0123456789012345'))->aes128cbcBase64Decrypt($_POST['encryptData']);
                    if (false === $deData) {
                        $flag = false;
                        $logger->warning("post数据aes解密失败", $_POST);
                    } else {
                        $_POST = array_merge($_POST, json_decode($deData, true));
                        $pwdHasMd5 = true;
                    }
                }
                if (!$flag) {
                    exit($this->returnJson(array(
                        "loadPage" => '',
                        "forwardPage" => '',
                        "msg" => '',
                        "msgCode" => '400.6', // 数据加密问题，页面显示网络异常
                    )));
                }
            }

            if (!isset($_POST['agree']))
            {
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
                {
                    exit($this->returnJson(array(
                                "loadPage" => '',
                                "forwardPage" => '',
                                "msg" => mb_convert_encoding('请同意2345服务协议 @msg_agree', "utf8", "gbk")
                    )));
                }
                else
                {
                    showMessage('请同意2345服务协议', 'back');
                }
            }

            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                $_POST['username'] = mb_convert_encoding(trim($_POST['username']), "gbk", "utf8");
            }
            else
            {
                $_POST['username'] = trim($_POST['username']);
            }
            $regex = Config::get('regex');
            //载入时间片 
            $DateAnalyzeAction = loadAction('DateAnalyze');
            $isNormal = $DateAnalyzeAction->isNormalAccess('reg');
            $DateAnalyzeAction->regCount('regcountAccess');
            
            //载入令牌
            $SecurityAction = loadAction('Security');
            $uuid = \Common\Utils\Cookie::getUuid();
            $identityId = $_POST['identityId'];
            $isLegal = $SecurityAction->verifyRegToken($uuid , $identityId);

            $userClient_ip = get_client_ip();
            $refer = $_SERVER['HTTP_REFERER'];
            $servClient_domain = \Common\Utils\Url::getStringParam('domain');
            $clientType = \Common\Utils\Url::getStringParam('reg_type');
            $all = json_encode($_POST);
            $logger->info(
                "\"REQUEST_URI:{$_SERVER['REQUEST_URI']}\" \"username:{$_POST['username']}\" \"password:{$_POST['password']}\" \"ip:$userClient_ip\" \"refer:{$refer}\" \"domain:{$servClient_domain}\" \"reg_type：{$clientType}\" \"{$all}\""
            );
            //如果不合法  
            if (!$isLegal)
            {
                $isShowCode = \Service\Security\CaptchaRules::getShowCode('reg');
                if ( ! $isShowCode )
                {
                    $DateAnalyzeAction->setShowCode('reg');
                    exit($this->returnJson(array(
                                "loadPage" => '',
                                "forwardPage" => '',
                                "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                    )));
                }
                
            }
            else
            {
                $DateAnalyzeAction->setRegToken ($identityId);
            }
            
            //如果时间规则计算有问题
            if (! $isNormal)
            {
                $isShowCode = \Service\Security\CaptchaRules::getShowCode('reg');
                if ( ! $isShowCode )
                {
                    $DateAnalyzeAction->setShowCode('reg');
                    exit($this->returnJson(array(
                                "loadPage" => '',
                                "forwardPage" => '',
                                "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                    )));
                }
            }

            if (filter_var($_POST['username'], FILTER_VALIDATE_EMAIL))
            {
                $this->regEmail();
            }
            elseif (preg_match($regex['phone'], $_POST['username']))
            {
                $this->regPhone();
            }
            else
            {
                $this->regUserName($pwdHasMd5);
            }
        }
    }

    /**
     * 检查页面token
     *
     * @author：dongx
     * @return void
     */
    private function checkToken()
    {
        session_start();
        if (!isset($_SESSION['reg_token']))
        {
            exit($this->returnJson(array(
                "loadPage" => '',
                "forwardPage" => '',
                "msg" => mb_convert_encoding('错误代码3001，请刷新页面后重试 @msg_validate ', "utf8", "gbk")
            )));
        }
        $sessionKey = $_SESSION['reg_token'];
        if (!array_key_exists($sessionKey, $_POST))
        {
            exit($this->returnJson(array(
                "loadPage" => '',
                "forwardPage" => '',
                "msg" => mb_convert_encoding('错误代码3002，请刷新页面后重试  @msg_validate', "utf8", "gbk")
            )));
        }
        if ($_SESSION[$sessionKey] != $_POST[$sessionKey])
        {
            exit($this->returnJson(array(
                "loadPage" => '',
                "forwardPage" => '',
                "msg" => mb_convert_encoding('错误代码3003，请刷新页面后重试  @msg_validate', "utf8", "gbk")
            )));
        }

        if (time() - $_SESSION['reg_time'] < 3)
        {
            exit($this->returnJson(array(
                "loadPage" => '',
                "forwardPage" => '',
                "msg" => mb_convert_encoding('错误代码3004，请刷新页面后重试  @msg_validate', "utf8", "gbk")
            )));
        }

        if ($_SESSION['reg_num'] > 5)
        {
            //注册尝试次数超过10次
            exit($this->returnJson(array(
                "loadPage" => '',
                "forwardPage" => '',
                "msg" => mb_convert_encoding('错误代码3005，请刷新页面后重试  @msg_validate', "utf8", "gbk")
            )));
        }
    }

    /**
     * 用户名注册
     */
    private function regUserName($pwdHasMd5 = false)
    {
        session_start();
        $userClientIp = get_client_ip();
        /** @var RegAction $regAction */
        $regAction = loadAction('reg');
        $isShowCode = $regAction->regCheckShowCode($userClientIp);
        if ( ! $isShowCode )
        {
            $isShowCode = \Service\Security\CaptchaRules::getShowCode('reg');
        }
        if ($isShowCode && (!isset($_POST['validate']) || $_POST['validate'] == '' || ( $_POST['validate'] !== '' . $_SESSION['checkIMGCode_new'] && $_POST['validate'] !== '' . $_SESSION['captcha_code'])   ))
        {
            unset($_SESSION['checkIMGCode_new']);
            unset($_SESSION['captcha_code']);
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                )));
            }
            else
            {
                showMessage('验证码输入错误', 'back');
            }
        }
        unset($_SESSION['checkIMGCode_new']);
        unset($_SESSION['captcha_code']);
        $client_ip = get_client_ip();

        // 错误码
        $msgs = array(
            300 => array(
                0 => '2345帐号最少2个字符',
                1 => '2345帐号请不要超过24个字符',
                2 => '2345帐号请输入汉字，字母，数字，或邮箱地址',
                3 => '密码最少6个字符',
                4 => '密码最多16个字符',
                5 => '请输入正确的邮箱',
                6 => '此帐号已被注册，请修改2345帐号',
                7 => '此邮箱已被注册，请换一个'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );

        // 密码强度
        if (!isset($_POST['pwd_strength']))
        {
            $pwd_strength = 2;
        }
        else
        {
            $pwd_strength = $_POST['pwd_strength'];
        }
        $domain = $_POST['domain'];
        $result = $regAction->reg($_POST['username'], $_POST['password'], $_POST['email'], $pwd_strength, $_POST['refer'], $domain, $client_ip, $domain, AccountStat::CTP_WEB, $pwdHasMd5);
        if (isset($_POST['serverCall']))
        {
            if ($result[0] === '200.0')
            {
                //成功
                if (isset($_POST['regRest']))
                {
                    $result[1] = array_merge($result[1], array("callback" => $_POST['regRest']));
                }
                $result[1] = array_merge($result[1], array("ip" => get_client_ip()));
                $serverCall = parse_url($_POST['serverCall']);
                $serverPath = $serverCall['scheme'] . '://' . $serverCall['host'];
                if (preg_match("/^(http|https):\/\/[^\/]+\.(2345\.com|2345\.cn|9991\.com|2345jr\.com|ym\.com)$/", $serverPath))
                {
                    RedisEx::delInstance();
                    $curlOption = [
                        CURLOPT_CONNECTTIMEOUT => 1,
                        CURLOPT_TIMEOUT        => 1,
                    ];
                    http_post($_POST['serverCall'], array("game" => serialize($result)), $curlOption);
                }

            }
        }
        $states = explode('.', $result[0]);

        if ($states[0] == 400)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => '',
                            "msgCode" => $result[0]
                )));
            }
            else
            {
                showError($msgs[$states[0]][$states[1]], 400);
            }
        }
        elseif ($states[0] == 300)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => '',
                            "msgCode" => $result[0]
                )));
            }
            else
            {
                showMessage($msgs[$states[0]][$states[1]], 'back');
            }
        }
        elseif ($states[0] == 200)
        {
            if ($_COOKIE['referDomain'] != '')
            {
                $referDomain = $_COOKIE['referDomain'];
                $redis = RedisEx::getInstance();
                $redis->hIncrBy('referDomain:RSN', $referDomain, 1);
            }
                $this->doLogin($result[1]);
        }
    }

    /**
     * 手机注册
     */
    private function regPhone()
    {
        if (!isset($_POST['validate_phone']) || $_POST['validate_phone'] == '' || !$this->actionCheckPhoneCode($_POST['username'], $_POST['validate_phone'], true))
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                )));
            }
            else
            {
                showMessage('验证码输入错误', 'back');
            }
        }

        $msgs = array(
            300 => array(
                0 => '请输入正确的手机号码',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此手机号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );

        $client_ip = get_client_ip();
        if (!isset($_POST['pwd_strength']))
        {
            $pwd_strength = 2;
        }
        else
        {
            $pwd_strength = $_POST['pwd_strength'];
        }
        $regAction = loadAction('reg');

        $clientType = \Service\AccountStat\Consts\AccountStat::CTP_WEB;
        $source =  parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
        if(!$source)
        {
            $source = "webapi";
        }
        $result = $regAction->regPhone($_POST['username'], $_POST['password'], $pwd_strength, $client_ip, 'login.2345.com', 200, $source, $clientType);
        $states = explode('.', $result[0]);

        if ($states[0] == 400)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => '',
                            "msgCode" => $result[0]
                )));
            }
            else
            {
                showError($msgs[$states[0]][$states[1]], 400);
            }
        }
        elseif ($states[0] == 300)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => '',
                            "msgCode" => $result[0]
                )));
            }
            else
            {
                showMessage($msgs[$states[0]][$states[1]], 'back');
            }
        }
        elseif ($states[0] == 200)
        {
            if ($_COOKIE['referDomain'] != '')
            {
                $referDomain = $_COOKIE['referDomain'];
                $redis = RedisEx::getInstance();
                $redis->hIncrBy('referDomain:RSN', $referDomain, 1);
            }
                $this->doLogin($result[1]);
        }
    }

    /**
     * email注册
     */
    private function regEmail()
    {
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || ($_POST['validate'] !== '' . $_SESSION['checkIMGCode_new'] &&  $_POST['validate'] !== '' . $_SESSION['captcha_code']) )
        {
            unset($_SESSION['checkIMGCode_new']);
            unset($_SESSION['captcha_code']);
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => mb_convert_encoding('验证码输入错误 @msg_validate', "utf8", "gbk")
                )));
            }
            else
            {
                showMessage('验证码输入错误', 'back');
            }
        }
        unset($_SESSION['checkIMGCode_new']);
        unset($_SESSION['captcha_code']);

        $msgs = array(
            300 => array(
                0 => '请输入正确的邮箱地址',
                1 => '密码最少6个字符',
                2 => '密码最多16个字符',
                3 => '此邮箱号已被注册'
            ),
            400 => array(
                0 => 400, // 非法域名调用
                1 => 401, // 非法IP调用
                2 => 402, // 批量刷CHECK
                3 => 403, // IP段被禁止
                4 => 404, // IP被禁止
                5 => 405 // 未验证通过（缺少isValidate）
            )
        );
        $client_ip = get_client_ip();
        if (!isset($_POST['pwd_strength']))
        {
            $pwd_strength = 2;
        }
        else
        {
            $pwd_strength = $_POST['pwd_strength'];
        }
        $regAction = loadAction('reg');

        $source =  parse_url($_SERVER['HTTP_REFERER'], PHP_URL_HOST);
        if(!$source)
        {
            $source = "webapi";
        }
        $result = $regAction->regEmail($_POST['username'], $_POST['password'], $pwd_strength, $client_ip, 'login.2345.com', $source);
        $states = explode('.', $result[0]);

        if ($states[0] == 400)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => '',
                            "msgCode" => $result[0]
                )));
            }
            else
            {
                showError($msgs[$states[0]][$states[1]], 400);
            }
        }
        elseif ($states[0] == 300)
        {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
            {
                exit($this->returnJson(array(
                            "loadPage" => '',
                            "forwardPage" => '',
                            "msg" => '',
                            "msgCode" => $result[0]
                )));
            }
            else
            {
                showMessage($msgs[$states[0]][$states[1]], 'back');
            }
        }
        elseif ($states[0] == 200)
        {
            if ($_COOKIE['referDomain'] != '')
            {
                $referDomain = $_COOKIE['referDomain'];
                $redis = RedisEx::getInstance();
                $redis->hIncrBy('referDomain:RSN', $referDomain, 1);
            }
                $this->doLogin($result[1]);
        }
    }

    /**
     * 登录（设置cookie并跳转）
     * @param  array $params 用户信息
     */
    private function doLogin($params)
    {
        $DateAnalyzeAction = loadAction('DateAnalyze');
        $DateAnalyzeAction->writeRedisData('reg',$params);
        $cookie = $params['cookie'];
        $refer = urldecode($params['refer']);
        // cookie过期时间
        $cTime = time() + 3600 * 24 * 30;
        $loginAction = loadAction('login');
        $loginAction->setLoginCookie($cookie, $cTime, DOMAIN);
        // 处理回跳地址
        if (empty($refer))
        {
            if (isset($_COOKIE['qqforward']))
            {
                $refer = urldecode($_COOKIE['qqforward']);
            }
            else
            {
                $refer = 'http://www.' . DOMAIN;
            }
        }

        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']))
        {
            // 设置其他域名的登录cookie： 2345.cn等
            $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);
            exit($this->returnJson(array(
                "loadPage"    => $thirdCallbackCookies,
                "forwardPage" => $refer,
                "msg"         => '',
            )));
        }
        else
        {
            // 设置其他域名的登录cookie： 2345.cn等
            $thirdCallbackCookies = $loginAction->getThirdCallbackCookies($cookie, $cTime);

            $pageArray = array(
                "loadPage" => json_encode($thirdCallbackCookies),
                "forwardPage" => $refer
            );
            return loadCompatibleView("redirect.tpl.html", "m/redirect_wap.tpl.html", $pageArray);
        }
    }

    /**
     * 发送手机验证码
     */
    public function actionSendPhoneCode()
    {
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || $_POST['validate'] !== '' . $_SESSION['checkIMGCode_new'])
        {
            unset($_SESSION['checkIMGCode_new']);
            echo '600.0';
            return;
        }
        $phone = isset($_POST['phone']) ? $_POST['phone'] : null;
        $regex = Config::get('regex');
        if (preg_match($regex['phone'], $phone))
        {
            $memberModel = loadModel('member');
            $ret = $memberModel->checkPhone($_POST['phone'], 1);
            if ($ret != 0)
            {
                echo '400.0';
                return;
            }

            $code = rand(100000, 999999);
            $result = sendCodeUsePhone($phone, $code, 8, 165);
            if ($result == 1)
            {
                $redis = RedisEx::getInstance();
                $redis->setex("regchecktime:" . $phone, 1800, 0);
                $redis->setex("regcode:" . $phone, 1800, md5($phone . $code));
                echo '200.0';
            }
            else if ($result == 2)
            {
                echo '400.0';
            }
            else
            {
                echo '500.0';
            }
        }
        else
        {
            echo '300.0';
        }
    }

    /**
     * 发送手机语音验证码
     */
    public function actionSendPhoneVoiceCode()
    {
        /**
         * 防恶意攻击
         */
        session_start();
        if (!isset($_POST['validate']) || $_POST['validate'] == '' || $_POST['validate'] !== '' . $_SESSION['checkIMGCode_new'])
        {
            unset($_SESSION['checkIMGCode_new']);
            echo '600.0';
            return;
        }
        if ($_SERVER['HTTP_REFERER'] == "http://www.baidu.com")
        {
            echo '200.0';
            return;
        }
        $phone = isset($_POST['phone']) ? $_POST['phone'] : null;
        if (preg_match('/^1[0123456789]\d{9}$/', $phone))
        {
            $memberModel = loadModel('member');
            $ret = $memberModel->checkPhone($_POST['phone'], 1);
            if ($ret != 0)
            {
                echo '400.0';
                return;
            }

            $result = sendCodeFromVoice($phone);
            if ($result[0] == 1)
            {
                $redis = RedisEx::getInstance();
                $redis->setex("regchecktime:" . $phone, 1800, 0);
                $redis->setex("regcode:" . $phone, 1800, md5($phone . $result[1]));
                echo '200.0';
            }
            else if ($result[0] == 2)
            {
                echo '400.0';
            }
            else
            {
                echo '500.0';
            }
        }
        else
        {
            echo '300.0';
        }
    }

    /**
     * 检查手机验证码
     */
    public function actionCheckPhoneCode($phone = null, $code = null, $return = null)
    {
        $respCode = '200.0';
        $code = isset($_POST['code']) ? $_POST['code'] : $code;
        $phone = isset($_POST['phone']) ? $_POST['phone'] : $phone;
        if (preg_match('/^1[0123456789]\d{9}$/', $phone))
        {
            $redis = RedisEx::getInstance();

            $checkTime = $redis->get("regchecktime:" . $phone);
            if ($checkTime > 2)
            {
                $redis->del("regchecktime:" . $phone);
                $redis->del("regcode:" . $phone);
                $respCode = '400.0';
            }

            $checkcode = $redis->get("regcode:" . $phone);
            if (md5($phone . $code) == $checkcode && $checkcode)
            {
                $respCode = '200.0';
            }
            else
            {
                $redis->setex("regchecktime:" . $phone, 1800, ++$checkTime);
                $respCode = '400.0';
            }
        }
        else
        {
            $respCode = '300.0';
        }

        if ($return)
        {
            if (isset($redis))
            {
                $redis->del("regchecktime:" . $phone);
                $redis->del("regcode:" . $phone);
            }
            return $respCode == '200.0' ? TRUE : FALSE;
        }
        else
        {
            echo $respCode;
        }
    }

}
