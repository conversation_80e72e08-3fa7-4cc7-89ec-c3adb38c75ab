<!DOCTYPE html>
<html>
<head>
  <meta charset="gb2312">
  <title>短信服务平台-群发短信记录</title>
  <link rel="stylesheet" href="/style/css/global.css">
  <script src="/style/js/jquery-1.8.3.min.js"></script>
  <script src="/style/date/WdatePicker.js"></script>

    <style>
        .boxMod {
            left: 50%;
            top: 10%;
        }
        .scrollWrap {
            height: 400px;
            overflow-y: scroll;
        }
    </style>

</head>

<body>
<div class="mainWrap">
	<!--面包屑导航 开始-->
    <div class="location">位置：<a target="_blank" href="#">短信服务平台</a>  >  <span>群发短信记录</span></div>
    <!--面包屑导航 结束-->
    
    <!--Tab菜单 开始-->
    <div class="TabMenu mt10">
        <a href="/Admin/SmsQueryLog/Index">短信发送记录</a>
        <a class="currTabMenu" href="javascript:">群发短信记录</a>
    </div><!--TabMenu end-->
    <!--Tab菜单 结束-->
    
    <!--日期和下拉菜单 开始-->
    <div class="funList mt20 clearfix">
    	<form action="" method="post" id="queryFromId">
            <div class="clearfix">
                <div class="dataWrap clearfix">
                	<em>日期：</em>
                    <div class="dataList w130">
                    	<div class="getTime"><input type="text" value="{{$pageArray.startTime}}" autocomplete="off" name="startTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                    </div>
                    </div><!--dataWrap end-->
                    <span class="dataTxt">至</span>
                    <div class="dataWrap clearfix">
                        <div class="dataList w130">
                        	<div class="getTime"><input type="text" value="{{$pageArray.endTime}}" autocomplete="off" name="endTime" onclick="WdatePicker()"/><i>日历icon</i></div>
                        </div>
                    </div><!--dataWrap end-->
                    <span style=" margin-left:3;" class="dataTxt">供应商：</span>

                    <div class="selectWrap left w170">
                        <div class="selectInput"><input type="text" readonly="" value="全部" autocomplete="off" class="getListVal" id="selectedOperator"><i>下拉箭头</i></div>
                        <dl scroll="yes" class="selectList" style="display: none;" id="selectOperatorId">
                            <dd><a href="javascript:" value="0">全部</a></dd>
                            {{foreach from=$pageArray.OperatorList item=OperatorInfo}}
                                <dd><a href="javascript:" value="{{$OperatorInfo.id}}" >{{$OperatorInfo.provider_name}}</a></dd>
                            {{/foreach}}
                        </dl>
                        <input type="hidden" value="0" name="operatorId"/>
                    </div>
                    <span class="dataTxt" style="margin:0px">项目：</span>
                    <div class="selectWrap left w170">
                        <div class="selectInput"><input type="text" readonly="" value="全部" autocomplete="off" class="getListVal" id="projectSelected"><i>下拉箭头</i></div>
                        <dl scroll="yes" class="selectList addScroll" style="display: none;" id="selectPid">
                            <dd><a href="javascript:" value="0">全部</a></dd>
                            {{foreach from=$pageArray.projectList key=projectKey item=projectInfo}}
                            <dd><a href="javascript:" value="{{$projectKey}}">{{$projectInfo}}</a></dd>
                            {{/foreach}}
                        </dl>
                        <input type="hidden" name="projectId" value="0"/>
                    </div>
                    
                    <div class="showPostionDiv" >
                    	<span class="dataTxt">位置：</span>
                        <div class="selectWrap left" style="width: 160px;">
                            <div class="selectInput">
                            <input type="text" readonly="" value="全部" autocomplete="off" class="getListVal" id="selectedposid"><i>下拉箭头</i></div>
                            <dl scroll="yes" class="selectList addScroll" style="display: none;" id="selectPostionId">
                                
                            </dl>
                            <input type="hidden" name="plId" value="0"/>
                        </div>
                    </div>
                
                <input type="hidden" name="queryAction" value="1"/>
                <a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 queryFromSubmit">查询</a>
                <!--<a href="javascript:" class="styleBtn_s styleBtnBlue left ml10 exportProjectData">导出</a>-->

            </div>

        </form>
    </div><!--funList end-->
    <!--日期和下拉菜单 结束-->
    
    <!--主体内容 开始-->
    <div class="contentWrap">
        <!--table数据列表 开始-->
        <div class="tabFiedWrap">
            <div class="mrl30">
                <table class="tabMod topFied" cellpadding="0" cellspacing="0">
                    <tr>
                        <th width="60">logId</th>
                        <th width="100">手机</th>
                        <th width="140">发送时间</th>
                        <th>短信内容</th>
                        <th>发送状态</th>
                        <th>项目</th>
                        <th>类型</th>
                        <th>位置</th>
                        <th>供应商</th>
                        <th>提交状态描述</th>
                    </tr>
                </table>
            </div>
        </div><!--tabFiedWrap end-->
        
        <table class="tabMod forFixed mt10" cellpadding="0" cellspacing="0">
            <tr>
                <th width="80">logId</th>
                <th width="100">手机</th>
                <th>数量</th>
                <th width="140">发送时间</th>
                <th>短信内容</th>
                <th>发送状态</th>
                <th>项目</th>
                <th>类型</th>
                <th>位置</th>
                <th>供应商</th>
                <th>提交状态描述</th>
            </tr>
            {{foreach from=$pageArray.logList item=logInfo}}
            <tr>
                <td>{{$logInfo.log_id}}</td>
                <td class="phone" data-phone="{{$logInfo.phone}}">{{$logInfo.shortPhone}}</td>
                <td>{{$logInfo.amount}}</td>
                <td>{{$logInfo.send_time}}</td>
                <td>{{$logInfo.text}}</td>
                <td title="{{$logInfo.voiceMessage}}">{{$logInfo.voiceStatusDesc}}</td>
                <td>{{$logInfo.projectName}}</td>
                <td>{{$logInfo.smsName}}</td>
                <td>{{$logInfo.postionName}}</td>
                <td>{{$logInfo.channelName}}</td>
                <td>{{$logInfo.codeDesc}}</td>
            </tr>
            {{/foreach}}
        </table>
        <!--table数据列表 结束-->
        {{$pageArray.showPage}}
        
    </div><!--contentWrap end-->
    <!--主体内容 开始-->
</div><!--mainWrap end-->

<div class="boxMask" style="display:none">透明遮罩层</div>
<div class="boxMod w800 showAllPhoneDiv">
    <div class="boxTop"><h2 class="boxTit divSetUserClass"></h2><a class="BoxClose" href="javascript:">关闭按钮</a>
    </div>

    <div class="scrollWrap" id="modal">

    </div>
</div><!--boxMod end-->

<script src="/style/js/tableFixed.js"></script>
<script>
$('.queryFromSubmit').click(function (){
    $('#queryFromId').submit();
});
function showPostionFunc (projectId)
{
    if (projectId==0)
    {
        //$('.showPostionDiv').hide();
        $('#selectPostionId').html('');
        return false;
    }
    $.post('/Admin/SmsProject/GetProjectPostion','projectId='+projectId,function(data){
       // $('.showPostionDiv').show();
        $('#selectPostionId').html(data.data);
        if ( '{{$pageArray.plId}}' !='')
        {
            $('#selectPostionId').val('{{$pageArray.plId}}');   
        }
        $("#selectPostionId a").bind("click",function()
    	{
    		$(this).parents(".selectWrap").find(".getListVal").val($(this).text());
            var postionId = $(this).attr('value');
            $('input[name="plId"]').val(postionId)
    	});
        
        $("#selectPostionId a").each (function (){ 
            var posId =  $(this).attr('value');
            if ( posId == '{{$pageArray.plId}}')
            {
                $('input[name="plId"]').val(posId);
                $('#selectedposid').val( $(this).html() );
            }
        });
    })
}

if ('{{$pageArray.projectId}}' != '')
{
    showPostionFunc('{{$pageArray.projectId}}');
    
}
$('.exportProjectData').click(function (){
    var startTime = $('input[name="startTime"]').val();
    var endTime = $('input[name="endTime"]').val();
    if (startTime != endTime)
    {
        alert('只允许导出一天的数据');
        return false;
    }
    var link = '/Admin/SmsQueryLog/Index?act=export&' + $('#queryFromId').serialize();
    window.open(link);
})

$('#selectPid a').click(function (){
    var pId = $(this).attr('value');
    $('input[name="projectId"]').val(pId);
    showPostionFunc(pId);
})

$('#selectPid a').each (function (a,b){
    //
    var pid = $(this).attr('value');
    if( pid == '{{$pageArray.projectId}}' )
    {
        $('input[name="projectId"]').val(pid);
        $('#projectSelected').val( $(this).html() );
    }
})


$('#selectOperatorId a').click(function (){
     var operatorId = $(this).attr('value');
    $('input[name="operatorId"]').val(operatorId);
});
$('#selectOperatorId a').each (function (a,b){
     var operatorId = $(this).attr('value');
     if ( operatorId == '{{$pageArray.operatorId}}')
     {
        $('input[name="operatorId"]').val(operatorId);
        $('#selectedOperator').val( $(this).html() );
     }
})

$('#userTypeId a').click(function (){
    var typeId = $(this).attr('value');
    $('input[name="queryType"]').val(typeId);
})

$('#userTypeId a').each(function (a,b){
    
    var typeId = $(this).attr('value');
     if ( typeId == '{{$pageArray.queryType}}')
     {
        $('input[name="queryType"]').val(typeId);
        $('#selectedUserType').val( $(this).html() );
     }
})


$('.BoxClose').click(function (){
    $('.boxMask').hide();
    $('.boxMod').hide();
});

$('.phone').click(function (){
    $("#modal").html($(this).attr("data-phone"));
    $('.boxMask').show();
    $('.showAllPhoneDiv').show();
});


</script>
<script src="/style/js/global.js"></script>
</body>
</html>