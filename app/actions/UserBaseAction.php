<?php
use Common\Utils\Encoding;
use Service\UserBase\Rules;
use Service\UserBase\UserAvatarCache;

/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/4/27
 * Time: 9:35
 */
class UserBaseAction extends Action
{

    /**
     * 获取头像昵称
     * @param string $mid            项目标识
     * @param int    $passId         用户中心唯一用户ID
     * @param string $getType        空获取全部,avatar、nickname
     * @param string $size           图片大小 big、middle、small
     * @param int    $showType       1、对自己 2、对外展示
     * @param bool   $isCache        是否使用缓存
     * @param bool   $nicknameLoadDB 默认昵称载入数据库
     * @return mixed
     * @throws Exception
     */
    public function getAvatarNicknameInfo($mid, $passId, $getType = '', $size = 'big', $showType = 1, $isCache = true, $nicknameLoadDB = false)
    {
        $userBaseModel = loadModel('UserBase');
        $userInfo = $userBaseModel->getAvatarNicknameInfoByPassId($mid, $passId, '', $isCache);

        return Rules::getAvatarNicknameByRule($mid, $passId, $showType, $userInfo, $getType, $size, $nicknameLoadDB);
    }

    /**
     * @param string $mid            项目标识
     * @param array  $passIdsList    passid list
     * @param string $getType        空获取全部,avatar、nickname
     * @param string $size           图片大小 big、middle、small
     * @param int    $showType       批量获取头像默认为2,  1、对自己 2、对外展示  - 只对获取头像生效
     * @param string $selfPassId     自己的passid，对自己展现头像  - 只对获取头像生效
     * @param bool   $isCache        是否使用缓存
     * @param bool   $nicknameLoadDB 昵称是否落库
     * @return array
     * @throws Exception
     */
    public function getBatchAvatarNicknameInfo($mid, $passIdsList, $getType = '', $size = 'big', $showType = 2, $selfPassId = '', $isCache = true, $nicknameLoadDB = false)
    {
        $outData = $composeData = [];
        $userBaseModel = loadModel('UserBase');
        $resultData = $userBaseModel->getAvatarNicknameListByPassIds($mid, $passIdsList, '', $isCache);
        foreach ($resultData as $info) {
            unset($passIdsList[$info['passid']]);
            $composeData[$info['passid']] = $info;
        }
        foreach ($passIdsList as $passId) {
            $composeData[$passId] = [];
        }
        foreach ($composeData as $passId => $info) {
            if ($selfPassId == $passId) {
                $showType = 1;
            }
            $avatarNicknameInfo = Rules::getAvatarNicknameByRule($mid, $passId, $showType, $info, $getType, $size, $nicknameLoadDB);
            switch ($getType) {
                case 'avatar':
                    list($avatarImg, $isDefaultAvatar) = $avatarNicknameInfo;
                    $outData[$passId] = [
                        'avatarUrl' => $avatarImg,
                        'isDefaultAvatar' => $isDefaultAvatar
                    ];
                    break;
                case 'nickname':
                    list($nickname) = $avatarNicknameInfo;
                    $outData[$passId] = [
                        'nickname' => $nickname,
                    ];
                    break;
                default:
                    list($avatarImg, $nickname, $isDefaultAvatar) = $avatarNicknameInfo;
                    $outData[$passId] = [
                        'nickname'  => $nickname,
                        'avatarUrl' => $avatarImg,
                        'isDefaultAvatar' => $isDefaultAvatar
                    ];
            }
        }

        return $outData;
    }

    /**
     * 获取头像更新字段
     * @param string   $mid          项目标识
     * @param int      $passid       passid
     * @param resource $avatarSource 图片二进制流或者图片链接
     * @param string   $uploadSource 头像上传来源
     * @param string   $type         source、http、file
     * @return array
     * @throws Exception
     */
    public static function uploadAvatar($mid, $passid, $avatarSource, $uploadSource, $type = 'source')
    {
        $data = [];
        switch ($type) {
            case "http":
                $curlOption = [
                    CURLOPT_CONNECTTIMEOUT => 1,
                    CURLOPT_TIMEOUT        => 1,
                ];
                $avatarSource = http_get($avatarSource, [], $curlOption);
                break;
            case "file":
                $avatarSource = file_get_contents($avatarSource);
                break;
            default:
                break;
        }
        if (empty($avatarSource)) {
            return [];
        }
        $avatarImgTmpUrl = '/tmp/' . uniqid($passid);  //临时存放目录
        $isWrite = file_put_contents($avatarImgTmpUrl, $avatarSource, true);
        $warningAction = loadAction('Warning');
        if ($isWrite !== false) {
            $imageUploadAction = loadAction('ImageUpload');
            //写入临时成功
            list($avatarImgStatus, $avatarImgUrl) = ImageUploadAction::uploadAndAsyncAudit($mid, $passid, $avatarImgTmpUrl);
            $warningAction->setRequestInfo(['avatarUploadStatus' => $avatarImgStatus, 'uploadSource' => $uploadSource]);
            //            $avatarImgStatus = ImageUploadAction::IMG_AUDIT_PASS;   //  todo  测试强制默认成功， 后面正式对接去掉
            $data['approve_status'] = $avatarImgStatus;
            if ($avatarImgStatus == ImageUploadAction::IMG_AUDIT_PASS) {
                $data['valid_avatar_url'] = $avatarImgUrl;
                $data['valid_avatar_source'] = $uploadSource;
            } elseif ($avatarImgStatus == ImageUploadAction::IMG_AUDIT_WAIT) {
                $data['in_approve_avatar_url'] = $avatarImgUrl;
                $data['in_approve_avatar_source'] = $uploadSource;
            }
            if (empty($data['valid_avatar_url']) && empty($data['in_approve_avatar_url'])) {
                return [];
            }
            $data['approve_create_time'] = date('Y-m-d H:i:s');


            return $data;
        } else {
            //写入临时失败
            return [];
        }
    }

    /**
     * 三方登录保存头像和昵称
     * @param string $mid           项目标识
     * @param int    $passid        用户中心唯一用户ID
     * @param string $thirdUserInfo 第三方用户信息
     * @param string $uploadSource  上传头像来源
     * @return void
     */
    public function updateThirdAvatarNicknameInfo($mid, $passid, $thirdUserInfo, $uploadSource)
    {
        $warningAction = loadAction('Warning');
        try {
            $groupName = Rules::getGroupNameByMid($mid);
            if (!empty($groupName)) {
                $userBaseModel = loadModel('UserBase');
                $getAvatarStatus = $getNicknameStatus = true;
                if (empty($thirdUserInfo['figureurl'])) {
                    $getAvatarStatus = false;
                }
                if (empty($thirdUserInfo['nickname'])) {
                    $getNicknameStatus = false;
                }
                $setParams = [
                    'thirdSource'       => $uploadSource,
                    'getNicknameStatus' => $getNicknameStatus,
                    'getAvatarStatus'   => $getAvatarStatus,
                    'nickname'          => $thirdUserInfo['nicknameUtf8'],
                ];
                $warningAction->setRequestInfo($setParams);
                //获取头像规则
                $avatarRule = \Service\UserBase\Rules::getAvatarRule($mid);
                if (isset($avatarRule['fromTriplicate']) && $avatarRule['fromTriplicate'] == 1) {
                    $imageUploadAction = loadAction('ImageUpload');
                    list($avatarImg, $nickname, $isDefaultAvatar, $isDefaultNickname) = $this->getAvatarNicknameInfo($mid, $passid);
                    $avatarData = [];
                    //无可用头像时  使用第三方头像补全
                    if ($isDefaultAvatar) {
                        // 没有可用头像
                        $avatarData = self::uploadAvatar($mid, $passid, $thirdUserInfo['figureurl'], $uploadSource, 'http');
                        if (!empty($avatarData)) {
                            $validAvatarUrl = $avatarData['valid_avatar_url'] ?? '';
                            $inApproveAvatarUrl = $avatarData['in_approve_avatar_url'] ?? '';
                            $approveStatus = $avatarData['approve_status'];
                            $avatarData['approve_status'] = Rules::getAvatarStatus($validAvatarUrl, $inApproveAvatarUrl, $approveStatus);
                        }
                    }
                    //没有用的昵称时  使用第三方昵称
                    $newNickname = '';
                    $nicknameSource = $uploadSource;
                    if ($isDefaultNickname) {
                        //检查三方昵称是否符合规则
                        $nicknameMsg = [];
                        $isCheck = \Service\UserBase\Rules::checkNickname($mid, $thirdUserInfo['nicknameUtf8'], $nicknameMsg);
                        if ($isCheck) {
                            //符合规则 就更新昵称
                            $newNickname = $thirdUserInfo['nicknameUtf8'];
                        } else {
                            $newNickname = $nickname;
                            $nicknameSource = 'default';
                        }
                        $nicknameErrorMsg = json_encode(Encoding::transcoding($nicknameMsg, 'gbk', 'utf-8'));
                        $warningAction->setRequestInfo(['checkNicknameStatus' => $isCheck, 'nicknameSource' => $nicknameSource, 'checkNicknameError' => $nicknameErrorMsg]);
                    }
                    $errorMsg = [];
                    $isSet = true;
                    if (!empty($newNickname)) {
                        $isSet = $userBaseModel->setNicknameInfo($mid, $passid, $newNickname, $errorMsg, $avatarData, $nicknameSource);
                    } else {
                        if (isset($avatarData['valid_avatar_url']) || isset($avatarData['in_approve_avatar_url'])) {
                            $isSet = $userBaseModel->setAvatarInfo($mid, $passid, $avatarData, $errorMsg);
                        }
                    }
                    if ($isSet === false) {
                        throw new Exception(json_encode(Encoding::transcoding($errorMsg, 'gbk', 'utf-8')));
                    }
                }
            }
        }
        catch (Exception $exception) {
            $warningAction->setRequestInfo(['avatarErrorMsg' => $exception->getMessage()]);
        }
    }

    /**
     * 推送队列
     * @param string $mid       项目ID
     * @param string $groupName 生态名称
     * @param int    $passid    passid
     * @param string $nickname  昵称
     * @param array  $data      头像昵称数据
     * @return bool
     */
    public function pushAvatarNicknameExchange($mid, $groupName, $passid, $nickname, $data)
    {
        $groupName = trim($groupName, "_");
        $rabbitMqAction = loadAction('RabbitMq');
        if (!empty($nickname) && (!empty($data['valid_avatar_url']) || !empty($data['in_approve_avatar_url']))) {
            $isPush = $rabbitMqAction->broadcastMsg($passid, $mid, $groupName);
        } elseif (!empty($nickname)) {
            $isPush = $rabbitMqAction->broadcastMsg($passid, $mid, $groupName, ['nickName']);
        } else {
            $isPush = $rabbitMqAction->broadcastMsg($passid, $mid, $groupName, ['avatar']);
        }

        return $isPush;
    }

    /**
     * 自定义上传
     * @param string $mid
     * @param string $passId
     * @param string $avatarTmpFile
     * @param array $errorMsg
     * @return mixed
     * @throws Exception
     */
    public static function customUploadAvatar($mid, $passId, $avatarTmpFile, &$errorMsg = [], $type = 'file')
    {
        $avatarData = self::uploadAvatar($mid, $passId, $avatarTmpFile, 'upload', $type);
        $userBaseAction = loadAction('UserBase');
        $userBaseModel = loadModel('UserBase');
        list($avatarImg, $isDefaultAvatar) = $userBaseAction->getAvatarNicknameInfo($mid, $passId, 'avatar', 'big', 2);
        if ($isDefaultAvatar) {
            $validAvatarUrl = $avatarData['valid_avatar_url'] ?? '';
            $inApproveAvatarUrl = $avatarData['in_approve_avatar_url'] ?? '';
            $approveStatus = $avatarData['approve_status'];
        } else {
            $validAvatarUrl = $avatarImg;
            $inApproveAvatarUrl = $avatarData['in_approve_avatar_url'] ?? '';
            $approveStatus = $avatarData['approve_status'];
        }
        $avatarData['approve_status'] = Rules::getAvatarStatus($validAvatarUrl, $inApproveAvatarUrl, $approveStatus);

        $blnSet = $userBaseModel->setAvatarInfo($mid, $passId, $avatarData, $errorMsg);
        if ($blnSet) {
            //头像设置成功，添加日志
            $memberModel = loadModel('member');
            $userInfo = $memberModel->read($passId);
            $userInfo['passid'] = $passId;

            //日志行为打点:用户信息修改; type:USER_INFO_MODIFY; sub_type:AVATAR;
            $strNew = $avatarData['valid_avatar_url'] ?: $avatarData['in_approve_avatar_url'];
            $strNew = \ImageUploadAction::privateDownloadUrl($strNew, $mid, 'big', 86400);
            $strMid = $mid == 'login' ? 'ALL' : $mid;
            $objLogV2Action = loadAction('logV2');
            $objLogV2Action->report($strMid, $avatarImg ? 'USER_INFO_MODIFY' : 'USER_INFO_ADD', 'AVATAR', $userInfo, $avatarImg, $strNew);
        }

        return $blnSet;
    }
}
