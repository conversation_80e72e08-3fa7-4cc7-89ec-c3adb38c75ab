<!DOCTYPE HTML>
<html style=" overflow:hidden">
    <head>
        <meta charset="gb2312">
        <title>2345王牌浏览器登录</title>
        <link type="text/css" href="/browser2/css/p_login_20140421.css" rel="stylesheet">
        <script type="text/javascript" src="/browser2/js/login.js"></script>
        <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
        <script type="text/javascript" src="/js/jquery.autocomplete.min.js"></script>
        <script type="text/javascript">
            {{if $pageArray.display == 'none'}}
            window.external.RCCoralSetDlgSize(700, 380);
            {{else}}
            window.external.RCCoralSetDlgSize(700, 415);
            {{/if}}
            var user, users;
            function setAutoComplete() {
                $('#usernameHD').bind("input.autocomplete", function() {
                    $(this).trigger('keydown.autocomplete');
                });
                $("#usernameHD").autocomplete(users.sort(), {resultsClass: 'select_li', inputClass: '', highlight: false, matchCase: true, formatItem: function(row) {
                        return '<p title="' + row[0] + '">' + row[0] + '</p><span class="sele_close" onclick="delUser(\'' + row[0] + '\');return false;"></span>';
                    }, minChars: 0, scroll: true, width: 196});
                $("#usernameHD").result(function(data, value) {
                    $(this).val('');
                    $("#username").val(value.toString().replace(/<span[^>]*?>[^<]*?<\/span>/g, '').replace(/<[^>]*?>/g, ''));
                    var password = window.external.RCCoralGetLoginUserPwd($("#username").val());
                    if (password) {
                        $("#password").val(password);
                        $("#remember").attr("checked", "checked");
                    } else {
                        $("#password").val("");
                        $("#remember").removeAttr("checked");
                    }
                    setTimeout(function() {
                        $("#username").focus();
                    }, 50);
                });
            }
            function delUser(username) {
                window.external.RCCoralDelUser(username);
                if ($("#username").val() == username) {
                    $("#username").val("");
                    $("#password").val("");
                }
                for (i in users) {
                    if (users[i] == username) {
                        users.splice(i, 1);
                    }
                }
                $("#usernameHD").unautocomplete();
                setAutoComplete();
            }
            function showError(msg) {
                $("#errorMsg").html(msg);
                $("#errorMsg").show();
                $("#submitButton").removeAttr("disabled");
                $("#submitButton").val("登 录");
            }
            function hideError() {
                $("#errorMsg").html("");
                $("#errorMsg").hide();
            }
            function cc(a) {
                var b = arguments,
                        web = "ajax62",
                        a2,
                        i1 = document.cookie.indexOf("uUiD="),
                        i2;
                if (b.length > 1)
                    web = b[1];
                if (i1 != -1) {
                    i2 = document.cookie.indexOf(";", i1);
                    a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
                }
                if (!a2) {
                    a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
                    document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
                }
                if (a.length > 0) {
                    var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
                    $.getScript(c);
                }
                return true;
            }
            $(document).ready(function() {
                document.ondragstart = function() {
                    return false;
                };
                $(this).bind("contextmenu", function(event) {
                    return $(event.target).is("input") && ($(event.target).attr("type") == "text" || $(event.target).attr("type") == "password");
                });
                $("#loginForm").submit(function() {
                    if ($("#submitButton").attr("disabled") == "disabled") {
                        return false;
                    }
                    $("#submitButton").attr("disabled", "disabled");
                    $("#submitButton").val("登录中...");
                    cc("login_all");
                    if ($("#username").val() == "")
                    {
                        showError("请输入2345帐号");
                        $("#username").focus();
                        return false;
                    }
                    if ($("#password").val() == "")
                    {
                        showError("请先输入密码");
                        $("#password").focus();
                        return false;
                    }
                    hideError();
                    var username = $.trim($("#username").val());
                    window.external.RCCoralOnLogin(username, "", 1, "", "", "", "", "");
                    $.ajax({
                        type: "POST",
                        url: "/browser2/sign",
                        data: "username=" + username + "&password=" + MD5($("#password").val()) + "&check_code=" + $("#check_code").val(),
                        success: function(data) {
                            data = eval("(" + data + ")");
                            switch (data["sts"]) {
                                case -3:
                                    cc("login_fail");
                                    window.external.RCCoralOnLogin(username, "", 3, "帐号或密码不正确，请重新输入", "", "", "", "");
                                    showError("帐号或密码不正确，请重新输入");
                                    break;
                                case -1:
                                    cc("login_fail");
                                    window.external.RCCoralOnLogin(username, "", 3, "帐号未激活", "", "", "", "");
                                    window.external.RCCoralOpenUrl("/active_error.html", "");
                                    break;
                                case 0:
                                    cc("login_success");
                                    window.external.RCCoralOnLogin(username, "", 0, "", data["uid"], data["sec"], data["passid"], data["token"]);
                                    if ($("#remember").is(':checked')) {
                                        window.external.RCCoralSetLoginUserInfo(username, $("#password").val());
                                        window.external.RCCoralSetIsAutoLogin(true);
                                    } else {
                                        window.external.RCCoralSetLoginUserInfo(username, "");
                                        window.external.RCCoralSetIsAutoLogin(false);
                                    }
                                    break;
                                case 1:
                                    cc("login_fail");
                                    window.external.RCCoralOnLogin(username, "", 3, "帐号或密码不正确，请重新输入", "", "", "", "");
                                    $("#password").val("");
                                    $("#username").focus();
                                    $("#username").select();
                                    showError("帐号或密码不正确，请重新输入");
                                    break;
                                case 3:
                                    cc("login_fail");
                                    window.external.RCCoralOnLogin(username, "", 3, "验证码输入错误", "", "", "", "");
                                    showError("验证码输入错误");
                                    break;
                                case 4:
                                    cc("login_fail");
                                    window.external.RCCoralOnLogin(username, "", 3, "验证码不能为空", "", "", "", "");
                                    showError("验证码不能为空");
                                    break;
                                case 1004:
                                    cc("login_fail");
                                    window.external.RCCoralOnLogin(username, "", 3, "登录有误，错误代码1004，请联系客服", "", "", "", "");
                                    showError("登录有误，错误代码1004，请联系客服");
                                    break;
                                case 1005:
                                    cc("login_fail");
                                    window.external.RCCoralOnLogin(username, "", 3, "登录有误，错误代码1005，请联系客服", "", "", "", "");
                                    showError("登录有误，错误代码1005，请联系客服");
                                    break;
                            }
                            if (data["display"] === "") {
                                $("#check_code").attr({"tabindex": "3"});
                                $("#yzm").show();
                                $('#checkImg').attr({'src': '/check_code.php?' + new Date()});
                                window.external.RCCoralSetDlgSize(700, 415);
                            } else {
                                $("#check_code").attr({"tabindex": "-1"});
                                $("#yzm").hide();
                            }
                            if ($("#yzm").css("display") != "none") {
                                $("#check_code").val("");
                            } else {
                                $("#password").val("");
                            }
                        },
                        error: function(XMLHttpRequest, textStatus, errorThrown) {
                            if (XMLHttpRequest.readyState == 0) {
                                showError('您的网络情况异常，请检查网络配置');
                                window.external.RCCoralOnLogin(username, "", 3, "您的网络情况异常，请检查网络配置", "", "", "", "");
                            } else {
                                showError('无法连接到2345通行证，请稍后再试');
                                window.external.RCCoralOnLogin(username, "", 3, "无法连接到2345通行证，请稍后再试", "", "", "", "");
                            }
                        }
                    });
                    return false;
                });
                $("#usernameHD").keydown(function(event) {
                    if (event.keyCode == 13) {
                        return false;
                    }
                });
                $("#qqA").click(function() {
                    cc('login_qq');
                    window.external.RCCoralOpenUrl('{{$smarty.const.LOGIN_HOST}}/browser2/qq/', '');
                    return false;
                });
                $("#weiboA").click(function() {
                    cc('login_weibo');
                    window.external.RCCoralOpenUrl('/browser2/weibo/', '');
                    return false;
                });
                $('#dropbtn').click(function() {
                    if ($(".select_li").length && $(".select_li:first").css("display") != "none") {
                        $('#usernameHD').blur();
                    } else {
                        $('#usernameHD').trigger('input.autocomplete');
                        $('#usernameHD').focus();
                        $('#usernameHD').click();
                    }
                    return false;
                });
                $("#username").bind("input propertychange", function() {
                    var password = window.external.RCCoralGetLoginUserPwd($.trim($(this).val()));
                    if (password) {
                        $("#password").val(password);
                    } else {
                        $("#password").val("");
                    }
                });
                $("#username").keydown(function(event) {
                    if (event.keyCode == 40) {
                        $('#usernameHD').trigger('input.autocomplete');
                        $('#usernameHD').focus();
                        $('#usernameHD').click();
                        return false;
                    }
                }).focus(function() {
                    if ($(this).val() == user["username"]) {
                        this.focused = true;
                        this.select();
                    }
                }).mouseup(function() {
                    if ($(this).val() == user["username"]) {
                        if (this.focused) {
                            this.focused = false;
                            return false;
                        }
                    }
                });
                $("#password").focus(function() {
                    if ($(this).val() == "******") {
                        $(this).val("");
                        $(this).data("default", "******");
                    } else {
                        this.focused = true;
                        this.select();
                    }
                }).mouseup(function() {
                    if (this.focused) {
                        this.focused = false;
                        return false;
                    }
                }).blur(function() {
                    if ($(this).val() == "" && $(this).data("default")) {
                        $(this).val("******");
                        $(this).data("default", false);
                    }
                });
                $("#check_code").focus(function() {
                    this.focused = true;
                    this.select();
                }).mouseup(function() {
                    if (this.focused) {
                        this.focused = false;
                        return false;
                    }
                });
                $("#checkImg,#checkA").click(function() {
                    $('#checkImg').attr({src: '/check_code.php?' + new Date()});
                    return false;
                });
                $("#findA").click(function() {
                    window.external.RCCoralOpenUrl('/find?type=password', '');
                    return false;
                });
                $("#regA").click(function() {
                    window.external.RCCoralShowRegDlg();
                    return false;
                });
                user = window.external.RCCoralGetLastActiveUserInfo();
                if (user != "") {
                    user = eval("(" + user + ")");
                    $("#username").val(user["username"]);
                    $("#password").val(user["password"]);
                    if ($("#username").val() == "") {
                        $("#username").focus();
                    } else if ($("#password").val() != "") {
                        $("#remember").attr("checked", "checked");
                        $("#submitButton").focus();
                    } else {
                        $("#remember").removeAttr("checked");
                        $("#password").focus();
                    }
                } else {
                    $("#username").focus();
                }
                users = window.external.RCCoralGetLoginAllUserName();
                if (users != "") {
                    users = eval(users);
                } else {
                    users = [];
                }
                setAutoComplete();
            });
        </script>
    </head>
    <body style="overflow:hidden">
        <div class="wrap">
            <div class="lg_box lg_form">
                <div class="lg_t lg_t2">2345用户登录&nbsp;&nbsp;<span class="tmore fgray">收藏夹在线同步永不丢失!</span></div>
                <form id="loginForm" action="" method="post" onsubmit="return false;">
                    <div class="lg_bd">
                        <dl>
                            <dt>2345帐号</dt>
                            <dd>
                                <span class="input_wp iwp_1">
                                    <input id="usernameHD" class="ipt_01" type="text" autocomplete="off" tabindex="-1"/>
                                    <input id="username" class="ipt_01" type="text" style="position: absolute; left: 0px; top: 0px;">
                                    <ins id="dropbtn" tabindex="-1" class="select_arrow"></ins>
                                </span>
                            </dd>
                        </dl>
                        <dl>
                            <dt>密&nbsp;&nbsp;&nbsp;&nbsp;码</dt>
                            <dd>
                                <span class="input_wp" style="z-index:0">
                                    <input class="ipt_01" id="password" onfocus="select();" type="password">
                                </span>
                            </dd>
                        </dl>
                        <dl style="display:{{$pageArray.display}};" id="yzm">
                            <dt>验&nbsp;证&nbsp;码</dt>
                            <dd>
                                <span class="input_wp2" style="z-index:0">
                                    <input type="text" class="ipt_02" id="check_code"/>
                                </span>
                                <img id="checkImg" src="/check_code.php"/>
                                <a id="checkA" tabindex="-1" href="#">看不清，换一张</a>
                            </dd>
                        </dl>
                        <dl class="h25"><dt></dt><dd><p class="fred" id="errorMsg" style="display:none;"></p></dd></dl>
                        <dl class="h36">
                            <dt></dt>
                            <dd>
                                <input tabindex="-1" class="btn_01" onMouseOver="this.className = 'btn_01_hov'" onMouseOut="this.className = 'btn_01'" type="submit" value="登录">
                                <label><input id="remember" type="checkbox" class="checkbox_01" checked="checked">记住密码</label>
                                <a id="findA" tabindex="-1" href="#">忘记密码?</a>
                            </dd>
                        </dl>
                        <dl>           
                            <dd>
                                <span class="tmore fgray">还没有2345帐号?&nbsp;
                                    <a id="regA" href="#">立即注册&gt;&gt;</a>
                                </span>
                            </dd>
                        </dl>
                    </div>       
                </form>
                <div class="lg_qq lg_bd2">
                    <strong class="lg_qq_t">使用合作帐号快速登录</strong>
                    <div class="lg_hezuo_button">
                        <a id="qqA" href="#" title="用QQ帐号登录" class="qq"></a>
                        <a id="weiboA" href="#" title="用微博帐号登录" class="weibo" ></a>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
