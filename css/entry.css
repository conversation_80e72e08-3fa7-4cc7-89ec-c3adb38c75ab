@charset "gb2312";
/*
 * Created: 2014-03-07
 * Update: 2014-03-07
 * Author: zhuyi
*/
html, body, div, span,applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, dd, dl, dt, li, ol, ul, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {margin:0;padding:0;}
body{font:12px/1.5 Arial;color:#515151;word-wrap:break-word;word-break:break-all;}
html,body{background-color:#f6fafc;}
h1,h2,h3,h4,h5,h6{font-size:100%;}
em,b,i,strong,cite,sub,sup{font-style: normal;font-weight:normal;}
a{text-decoration: none;color:#0e7dcc;outline: none;}
a:hover{text-decoration:underline;color:#ff4800;}
a:active{star:expression(this.onFocus=this.blur());}
:focus{outline:0;}
li{list-style-type:none;}
img{border:0 none;vertical-align:top;}
fieldset{border-style: none }
label,button{cursor: pointer;}
select,input,textarea{font:12px/1.2em tahoma,arial,\5b8b\4f53;}
.txl-r input,.txl-r select{ font-size:12px; padding:0;}
.clearfix:after{content:".";height:0;visibility:hidden;display:block;clear:both;font-size:0;line-height:0;}
.clearfix{*zoom:1;}
.fix{word-break:keep-all;overflow:hidden;text-overflow:ellipsis;}
.clear{font-size:0;line-height:0;height:0;clear:both;overflow: hidden;display: block;}
.none{display:none;}
html{-webkit-text-size-adjust:none;}
*html{background-image:url(about:blank);background-attachment:fixed;}
.mt5{margin-top:5px;}
.mt10{margin-top:10px;}
.mr20{margin-right:20px;}
.pt5{padding-top:5px;}
.wrapper{ background:url(../images/top.jpg) 0 0 repeat-x #f6fafc;*zoom:1;}
.wrap{margin:0 auto;width:930px; padding:7px 50px 60px;background:url(../images/wrap_ry.jpg) 0 0 repeat-y;position:relative;}
.wrap_btm{width:1030px;height:20px;overflow:hidden;background:url(../images/wrap_btm.jpg) no-repeat;position:absolute;left:0;bottom:-1px;z-index:2;}
.inner{ width:1010px; margin:0 auto}

.head,.menu,.menu li,.menu li a.cur,.menu li a:hover,.menu li a.cur span,.menu li a:hover span{background:url(../images/comm.png) 0 0 no-repeat;}
/* head */
.head{ height:77px; overflow:hidden; background-position:0 0; background-repeat:repeat-x;min-width:1030px;}
.head .inner{position:relative;}
.logo img{ margin:15px 0 0 9px;}
.userinfo{position:absolute;right:0; top:12px;color:#dedede; line-height:20px;}
.userinfo p{ display:inline;color:#666;}
.userinfo a{color:#666; margin:0 13px; zoom:1;}
.userinfo a:hover{color:#ff4800;}

.acount{width:878px;padding:14px 25px 24px;margin:0px 0 10px;border:1px solid #d5ecff;background-color:#f2f9ff;zoom:1;overflow:hidden;}
.acount .th{height:18px;line-height:18px;color:#7e8285;}
.acount .tit{color:#0e7dcc;font-size:16px;font-family:\5FAE\8F6F\96C5\9ED1;margin-right:20px;}
.acount .tit .ico-user{margin:-2px 2px 0 0;*margin:0px 2px 0 0;}
/* menu */
.menu{ height:37px; min-width:1030px; background-position:0 -80px; background-repeat:repeat-x;}
.menu .inner{padding:0 10px;}
.menu li{background-position:0 -120px; float:left; height:37px; line-height:34px;}
.menu li.first{ background:none;}
.menu li.first a{margin-left:0;}
.menu li a{ display:inline-block;font-size:14px; font-weight:700;color:#fff;float:left;padding-right:18px; margin:0 5px 0 7px; vertical-align:middle;}
.menu li a span{ display:inline-block; height:37px; line-height:34px;  padding-left:18px;cursor:pointer; vertical-align:middle; float:left}
.menu li a.cur,.menu li a:hover{background-position:right -200px;color:#0f649d; }
.menu li a.cur span,.menu li a:hover span{ display:inline-block; background-position:left -160px;}
/* footer */
.footer{margin-top:10px;line-height:21px;text-align:center;}
.footer em{margin:0 8px;color:#e3e3e3;}
.footer a{color:#999;}
.footer a:hover{color:#f60;}

.cGray{color:#adadad;}
.cBlue,a.cBlue,a.cBlue:hover{color:#3283c0}
.cRed{color:#f70b19;}
.f14{font-size:14px;}

.selectA,.ico,.radio,.dis-ib,.btn,.btn-a,.btn-b,.btn-c,.inputTxtA,.inputTxtB{display: inline-block;vertical-align:middle;*display: inline;*zoom:1;}

.btn-a,.btn-b,.btn-c,.btn-d,
.ico-a,.ico-b,.ico-c,.ico-d,.ico-user,.ico-upload,.ico-mail,.ico-back,.ico-edit,.ico-upload,.ico-warn1,.ico-warn2,.ico-right,.logo_qq,.logo_wb,
.inputTxtA,.inputTxtB,.radio,.selectA,.selectA .arrow-btm,
.my-options .avatar{background:url(../images/spr.png) no-repeat;}

.btn-a,.btn-b,.btn-c{width:75px;height:28px;line-height:28px;font-size:12px;color:#fff;text-align:center;}
.btn-d{width:117px;height:35px;font-size:14px;color:#fff;border:0 none;cursor:pointer;overflow:hidden;text-align:center;}
.btn-a{background-position:0 0;}
a.btn-a:hover,a.btn-b:hover,a.btn-c:hover,a.btn-d:hover{text-decoration:none;color:#fff;}
a.btn-a:hover{background-position:0 -29px;}
.btn-b{background-position:0 -58px;}
a.btn-b:hover{background-position:0 -87px;}
.btn-c{background-position:0 -116px;color:#767676;width:77px;height:30px;line-height:30px;}
a.btn-c:hover{background-position:0 -147px;color:#767676;}
.btn-d{background-position:0 -178px;}
a.btn-d:hover,.btn-d:hover{background-position:0 -214px;}

.ico-a{width:22px;height:22px;background-position:-21px -250px;}
.ico-b{width:20px;height:24px;background-position:-44px -250px;}
.ico-c{width:18px;height:21px;background-position:-65px -250px;}
.ico-d{width:22px;height:16px;background-position:-84px -250px;}
.ico-user{width:19px;height:17px;background-position:0 -250px;}
.ico-upload{width:30px;height:30px;background-position:0 -496px;}
.ico-mail{width:16px;height:13px;background-position:-31px -496px;}
.ico-back{width:15px;height:14px;background-position:-48px -496px;}
.ico-edit{width:12px;height:12px;background-position:-65px -496px;}
.ico-warn1{width:14px;height:14px;background-position:0 -527px;}
.ico-warn2{width:14px;height:14px;background-position:-15px -527px;}
.ico-right{width:16px;height:16px;background-position:-30px -527px;}

.top-back{padding:15px 0 25px;}
.top-back a{color:#0f649d;}
.top-back a:hover{color:#ff4800;}
.top-back .ico-back{margin-right: 5px;}

.mod-a{*zoom:1;}
.mod-a .th-a{line-height:20px;height:22px;padding:9px 0;overflow:hidden;margin-bottom:-2px;}
.mod-a .th-a span{float:left;height:29px;border-bottom:2px solid #3aa9ce;padding-right:10px;color:#848484;font-size:14px;position:relative;}
.mod-a .th-a .ico{margin-right:8px;}
.mod-a .tb-a{border-top:2px solid #e7e7e7;padding-top:25px;}

.my-options{padding:10px 0 15px;float:left;}
.my-options .avatar{width:120px;height:120px;position:relative;float:left;display:inline;overflow:hidden;margin-left:-5px;padding:10px;background-position:0 -282px;}
.my-options .avatar img{width:120px;height:120px;display:block;}
.my-options .avatar .upload{width:120px;height:120px;display:block;margin:2px;padding:3px;position:absolute;top:5px;left:5px;z-index:2;text-indent: 9999px;overflow:hidden;}
.my-options .avatar a.upload:hover{border:2px solid #6eb6ec;margin:0;*zoom:1;}
.my-options .avatar .ico-upload{position:absolute;right:3px;bottom:3px;z-index:3;cursor:pointer;}
.my-options .others{margin-left:10px;width:780px;float:left;display: inline;}
.my-options .username{margin:5px 0 15px;padding:7px 0;height:35px;_padding:13px 0 0px;_height:40px;border-bottom:1px dashed #dedede;zoom:1;overflow:hidden;}
.my-options .username .name{font-size:24px;color:#6db92f;margin-right:25px;position:relative;top:3px;}

.tip-org{padding:6px 10px;border:1px solid #fedfad;background-color:#fff7db;}
.tip-org .con{color:#b44f1d;margin-left:7px;}

.ulBind{margin:10px -26px 0 0;*zoom:1;}
.ulBind li{float:left;margin-right:26px;width:398px;padding:15px 15px 20px 11px;border:1px dashed #ccc;background-color:#fff;}
.ulBind li .logo{float:left;width:120px;}
.ulBind li .p{float:left;line-height:38px;color:#858585;width:195px;}
.logo_qq{width:105px;height:37px;background-position:0 -423px;}
.logo_wb{width:107px;height:33px;background-position:0 -461px;}

.form-a{color:#848484;font-size:14px;*zoom:1;}
.form-a .fieldset{padding:10px 0 0 145px;*zoom:1;min-height: 32px;_height:32px;float:left;clear:left;}
.form-a .label{margin-left:-145px;width:135px;height:32px;line-height:32px;padding-right:10px;text-align:right;color:#767676;font-size:14px;float:left;display: inline;}
.inputTxtA,.inputTxtB,.selectA{padding:5px 12px;height:20px;line-height:20px;border:1px solid #d9d9d9;width:200px;background-position:0 -544px;background-repeat:repeat-x;font-size:14px;}
.inputTxtA{width:245px;}
.selectA{height:30px;line-height:30px;padding:0;margin-right:12px;color:#848484;position:relative;width:auto;min-width: 94px;z-index:3;font-size:12px;cursor:pointer;}
.selectA .text{min-width:40px;padding:0 6px;border-right:1px solid #ededed;float:left;text-align:center;cursor:pointer;}
.selectA .holder{width:40px;height:30px;border-left:1px solid #fff;overflow:hidden;position:relative;font-size:0;cursor:pointer;float:left;}
.selectA .option{position:absolute;width:100%;_width:expression(this.parentNode.offsetWidth-2);background-color:#fbfbfb;height:155px;border:1px solid #d9d9d9;margin:-1px 0 0 -1px;top:31px;left:0;overflow:auto;}
.selectA .option li{padding:0 6px;height:30px;line-height:30px;overflow:hidden;border-bottom:1px solid #eee;}
.selectA .option li a{color:#666;display: block;}
.selectA .option li a:hover{color:#ff4800;text-decoration:none;}
.selectA .arrow-btm{width:13px;height:7px;background-position:-114px -496px;position:absolute;top:12px;left:15px;}
.radio{width:17px;height:17px;margin-right:5px;background-position:-96px -496px;cursor:pointer;}
.radio-sel{background-position:-78px -496px;}
.none{display: none;}

/*瀹���璧�����绀�*/
.improve-profile-tips{padding:0 0 30px 107px;margin:170px 0 300px 307px;background: url(../images/profile-pic.png) no-repeat 0 0;}
.improve-profile-tips h2{color:#686868;font:normal 20px/1.5 'Microsoft YaHei';margin-bottom: 10px;}
.btn-profile{width:133px;display:inline-block;height:37px;padding-left: 29px;color:#5fa5d9;line-height: 37px;background: url(../images/profile-btn.png) no-repeat 0 0;}
.btn-profile:hover{color:#5492c5;text-decoration: none;background-position: 0 -38px;}

.inbox3{ width:270px; padding:8px 0 8px 10px; height:32px; line-height:32px; font-size:14px; background:url(../images/infobg2014.png) 0 -79px no-repeat; border:0;}
.codetab{ table-layout:fixed; margin:50px 0 ;}
.codetab th,.codetab td{padding:5px 0; font-size:14px;}
.codetab th{ text-align:right; font-weight:100;color:#666;}
.codetab td span.tipfont{ font-size:12px; line-height:20px; color:#808080; }
.codetab td span.fred{ color:#ee0000;}
em.safe{background:#dbdbdb; display: inline-block; height: 16px;width: 106px; overflow:hidden; vertical-align:middle}
em.safe i{text-align:center;height:16px;line-height:16px;display:inline-block; font-style:normal; color:#fff; vertical-align:top; overflow:hidden }
em.safe i.w1{width:26px; background:#f86e74;}
em.safe i.w2{width:54px; background:#ffb401;}
em.safe i.w3{width:78px; background:#6cc000;}
em.safe i.w4{width:106px; background:#2aa001;}
.pwtip{display:block; color:#e92828; background:url(../images/infobg2014.png) -274px -136px no-repeat; padding-left:8px; height:32px; line-height:32px;}

.txt-un{text-decoration: underline;}
.cRed{color:red;}
.cancel-text{color:#9d9d9d;font-size: 14px;margin-left: 28px;text-decoration: underline;}

.checkemail{ padding:70px 40px 100px 170px;}
.checkemailLeft{ width:350px; float:left;}
.checkemail b{ font-weight:bold; font-size:14px; display:block; height:40px; line-height:40px;}
.checkemail ul{ display:block; height:100%; overflow:hidden; background:#f9fafb; border:1px solid #e7ecef; padding:20px 6px 20px 15px; border-radius:7px; margin-bottom:15px;}
.checkemail ul li{ line-height:20px; padding:5px 0}
.checkemail ul li p{ margin-left:18px;}
.checkemail ul li input{ vertical-align:middle; margin:-3px 2px 0 0;}
.checkemailRight{ width:280px; float:left; margin:30px 0 0 50px; line-height:22px;}
.checkemailRight b{ color:#999;}
.jihuo{ font-size:14px; line-height:40px; text-align:center; padding-left:40px;}

.step{ height:36px; line-height:36px; width:595px; margin-bottom:15px; background:url(../images/stepbg.png) no-repeat; font-size:14px; font-weight:700;color:#666;}
.step .st_1,.step .st_2,.step .st_3,.step .st_4{ display:inline-block;padding-left:10px; float:left}
.step .st_1{ width:138px;}
.step .st_2{ width:150px;color:#fff;}
.step .st_3{ width:150px;}
.step .st_4{}
.btn4,.btn4_hov{ border:0; background:url(../images/infobg2014.png) 0 0px no-repeat; width:103px; height:36px; line-height:36px; font-size:14px; cursor:pointer; color:#333;}
.btn4_hov{ background-position:0 -39px;}


