<!doctype html>
<html>
<head>
    <meta charset="gb2312">
    <title>2345用户中心-绑定</title>
    <meta content="" name="Description"/>
    <meta content="" name="Keywords"/>
    <meta name="author" content="weblol">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no,minimal-ui"/>
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"/>
    <meta name="apple-mobile-web-app-title" content="2345用户中心">
    <meta content="telephone=no" name="format-detection"/>
    <meta content="email=no" name="format-detection"/>
    <link rel="stylesheet" href="/css/m/g-user_20160407.css">
    <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
    <script type="text/javascript" src="/js/login.js"></script>

</head>

<body>

<header class="g-user-hd">
    <a href="javascript:void(0);" class="g-user-back">返回</a>
    <h1 class="g-user-logo">2345用户中心</h1>
</header>
<article class="g-user-bd">
    <header>
        <h1 class="g-user-title">2345帐号注册</h1>
        <p class="g-user-tips">你好，<em>{{$pageArray.oauthUser.nickname}}</em>，您已经通过{{$pageArray.oauthName}}帐号成功登录2345</p>
    </header>
    <form action="" class="g-user-form" method="post">
        <fieldset>
            <legend class="form-title">我没有2345帐号：</legend>
            <a href="javascript:void(0)" class="btn-confirm" onclick="formSubmit('');
                                            return false;">自动创建一个2345帐号</a>
        </fieldset>
    </form>
    <form id="bind-form" action="" class="g-user-form bd-t" method="post">
        <input id="cmd" type="hidden" name="cmd" value="bind"/>
        <input id="realpwd" type="hidden" name="password"/>

        <fieldset>
            <legend class="form-title">我已有2345帐号：</legend>
            <div class="form-item form-item-username">
                <i class="icon-user"></i>
                <input id="username" name="username" type="text" class="form-ipt ipt-txt ipt-txt-defa ipt-txt-user"
                       placeholder="手机/已验证邮箱/用户名">
            </div>
            <div class="form-item form-item-pwd">
                <input id="pwd" type="password" class="form-ipt ipt-txt ipt-txt-pwd" placeholder="密码">
            </div>

            {{if $pageArray.display != 'none'}}
            <div class="form-item form-item-yzm " id="form-item-yzm">
                <div class="form-vcode">
                    <input type="tel" id="captcha-text" name="check_code"
                           class="form-ipt ipt-txt ipt-txt-defa ipt-txt-yzm" placeholder="请输入验证码">
                    <span class="form-vcode-num">
                        <img id="pic" width="100%" src="/Captcha.php" onclick="this.src = '/Captcha.php?' + Math.random();"
                             alt="看不清，换一张">
                    </span>
                    <a href="javascript:void(0);" class="form-lnk"
                       onclick="document.getElementById('pic').src = '/Captcha.php?' + Math.random(); return false;">换一换</a>
                </div>
            </div>

            {{/if}}

            <p id="error-msg" class="form-tip-error" {{if !$pageArray.alert }} style="display: none" {{/if}}><i></i>
            {{$pageArray.alert}} </p>
            <div class="btn-group">
                <a href="javascript:void(0)" class="btn-confirm" type="submit" onclick="formSubmit('bind');
                                            return false;">绑定到该帐号</a>
            </div>

            <div class="form-item-btn">
            </div>

        </fieldset>
    </form>
</article>

<footer class="g-user-ft">
    <p>Copyright &copy;2345.com 沪ICP备12023051号-1</p>
</footer>

<script type="text/javascript">
    document.body.addEventListener('touchstart', function () {
    });
</script>

<script type="text/javascript">
    $(function () {
        $(".ipt-txt").bind({
            focus: function () {
                if ($(this).val() == "手机/已验证邮箱/用户名" || $(this).val() == "请输入计算结果" || $(this).val() == '')
                    $(this).addClass("ipt-txt-current").css('color', '#999999');
                else
                    $(this).addClass("ipt-txt-current").css('color', 'black');
            },
            blur: function () {
                if ($(this).val() == "手机/已验证邮箱/用户名" || $(this).val() == "请输入计算结果" || $(this).val() == '') {
                    $(this).removeClass("ipt-txt-current").css('color', '#999999');
                }
                else {
                    $(this).removeClass("ipt-txt-current").css('color', 'black');
                }
            },
            keydown: function () {
                if ($(this).val() == "手机/已验证邮箱/用户名" || $(this).val() == "请输入计算结果" || $(this).val() == '') {
                    $(this).removeClass("ipt-txt-current").css('color', '#999999');
                }
                else {
                    $(this).removeClass("ipt-txt-current").css('color', 'black');
                }
            }
        });

        $(".ipt-txt-user").bind({
            focus: function () {
                if ($(this).val() == "手机/已验证邮箱/用户名") {
                    $(this).val("");
                }
            },
            keypress: function () {
                if ($(this).val() == "手机/已验证邮箱/用户名") {
                    $(this).val("");
                    $(this).removeClass("ipt-txt-defa");
                }
            },
            blur: function () {
                if ($(this).val() == "") {
                    $(this).val("手机/已验证邮箱/用户名");
                    $(this).addClass("ipt-txt-defa");
                }
            }
        });
        $(".ipt-txt-yzm").bind({
            keypress: function () {
                if ($(this).val() == '请输入计算结果') {
                    $(this).val("");
                    $(this).removeClass("ipt-txt-defa");
                }
            },
            blur: function () {
                if ($(this).val() == "") {
                    $(this).val("请输入计算结果");
                    $(this).addClass("ipt-txt-defa");
                }
            },
            focus: function () {
                if ($(this).val() == '请输入计算结果') {
                    $(this).val("");
                }
            }
        });
        $(".ipt-tips").bind({
            click: function () {
                $(".ipt-txt-pwd").focus();
            }
        });
        $(".ipt-txt-pwd").bind({
            keypress: function () {
                $(".ipt-tips").css({"display": "none"});
            },
            blur: function () {
                if ($(this).val() == "") {
                    $(".ipt-tips").css({"display": "block"});
                }
            },
            focus: function () {
                $('.ipt-tips').hide();
            }
        });
    });
    function formSubmit(cmd) {
        $('#cmd').val(cmd);
        if (cmd !== '') {
            cc('bindUser');
            if (check()) {
                $('#bind-form').submit();
            }
        } else {
            cc('creatUser');
            $('#bind-form').submit();
        }
    }
    function check() {
        if ($("#username").val() == "" || $("#username").val() == "手机/已验证邮箱/用户名") {
            $('.form-item-username').addClass("form-item-error");
            $("#error-msg").show();
            $("#error-msg").text("请先输入用户名！");
            $("#username").focus();
            return false;
        } else {
            $('.form-item-username').removeClass("form-item-error");
        }
        if ($('#pwd').val() == "") {
            $('.form-item-pwd').addClass("form-item-error");
            $("#error-msg").show();
            $("#error-msg").text("请先输入密码！");
            $('#pwd').focus();
            return false;
        } else {
            $('.form-item-pwd').removeClass("form-item-error");
        }
        if ($('#captcha-text') && $('#captcha-text').val() == "" || $('#captcha-text').val() == "请输入计算结果") {
            $('.form-item-yzm').addClass("form-item-error");
            $("#error-msg").show();
            $("#error-msg").text("请先输入验证码！");
            $('#pwd').focus();
            return false;
        } else {
            $('.form-item-yzm').removeClass("form-item-error");
        }
        $('#realpwd').val(MD5($('#pwd').val()));

        $("#error-msg").hide();
        return true;
    }
    function cc(a) {
        var b = arguments,
                web = "ajax54",
                a2,
                i1 = document.cookie.indexOf("uUiD="),
                i2;
        if (b.length > 1)
            web = b[1];
        if (i1 != -1) {
            i2 = document.cookie.indexOf(";", i1);
            a2 = i2 != -1 ? document.cookie.substring(i1 + 5, i2) : document.cookie.substr(i1 + 5)
        }
        if (!a2) {
            a2 = Math.floor(Math.random() * 100000) + "" + new Date().getTime() + Math.floor(Math.random() * 100000);
            document.cookie = "uUiD=" + a2 + ";expires=Thu, 21 Sep 2096 10:37:29 GMT; path=/"
        }
        if (a.length > 0) {
            var c = "//web.50bangzh.com/web/" + web + "?uId2=SPTNPQRLSX&uId=" + a2 + "&r=" + encodeURIComponent(location.href) + "&lO=" + encodeURIComponent(a);
            $.getScript(c);
        }
        return true;
    }
</script>

<div style="display: none">
    <script type="text/javascript" src="//web.50bangzh.com/js/userc2345"></script>
</div>

</body>

</html>
