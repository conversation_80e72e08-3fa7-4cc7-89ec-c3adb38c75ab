<?php
/**
 * Class StatAction
 * 2020-04-22 废弃
 */

class StatAction extends Action
{
    /**
     * 功  能：统计session每天被踢掉的次数
     *
     * @param redis $redis redis
     * @return null
     */
    public static function incSessionKicked(&$redis)
    {
        return;
        $key = "SESSION_KICKED:" . date("Ymd");
        $res = $redis->incr($key);
        $redis->expire($key, 1728000);// 过期20天
    }

    /**
     * 记录请求的相关信息
     *
     * @param int $code code
     * @param string $msg msg
     * @param int $codeReal $codeReal
     * @return void
     */
    public static function requestLog($code, $msg = '', $codeReal = 0)
    {
        return;
        if ($codeReal === 0) {
            return;// 本次只记录第一批url
        }
        $request_uri = $_SERVER['REQUEST_URI'];
        if (($pos = strpos($request_uri, '?')) !== false) {
            $request_uri = substr($request_uri, 0, $pos);
        }
        $request = [
            'url' => $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $request_uri,
            'param_get' => http_build_query($_GET),
            'param_post' => http_build_query($_POST),
            'param_cookie' => http_build_query($_COOKIE),//todo 需要确认下，不然太大
            'codeReal' => $codeReal,
            'code' => $code,
            'msg' => $msg,
        ];
        // $request 后续要做压缩
        xLog('requestLog', 'requestLog', serialize($request));
    }
}
