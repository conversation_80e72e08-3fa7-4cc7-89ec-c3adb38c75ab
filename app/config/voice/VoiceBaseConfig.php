<?php

/*
 * 发语音验证码基础配置
 */

class VoiceBaseConfig {

    //请求来源
    public static $request_from = array(
        'pc', 'yd', 'jifen', 'buy', 'cp', 'login', 'xs', 'game'
    );
    
    public static $number_from = array(
        'pc'        => 1, 
        'yd'        => 2, 
        'jifen'     => 0, 
        'buy'       => 3, 
        'cp'        => 5, 
        'login'     => 4, 
        'xs'        => 6, 
        'game'      => 7
    );
    
    
    /*********************************** sip服务器列表 ***********************************/
    //SIP内网IP
    public static $host_list =  array(
            '*************',
            '**********',
    );

    //SIP外网IP
    public static $sip_ip = array(
        '***************', // test
            '***************', // 电信
            '**********',       // 多线
    );

    //外网查内网
    public static $outer_find_in_sip_ip = array(
            '***************' => '*************',
            '***************' => '*************',
            '**********' => '**********',
    );
    
    
    
    
    

}








?>
