<?php

namespace Service\Report;

use WebLogger\Facade\LoggerFacade;

class ReportToBigData
{
    private $config;
    private $connect;
    private $template = [
        "common" => [
            'project_name' => '', //应用项目 必填 向大数据申请
            'sdk_version' => '0', //服务端SDK版本号 必填
            'app_version' => '0', //应用版本号 必填
            'channel_id' => '', //当前渠道号 非必填
            'ip' => '0', //客户端IP 必填
            'ip_city' => '0', //IP城市 必填
            'pass_id' => '0', //项目用户唯一标识 必填
            'host_channel_id' => '', //宿主渠道 非必填
            'host_version_name' => '', //宿主外部版本号 非必填
            'host_name' => '', //宿主名 非必填
            'host_version_code' => '', //宿主内部版本号 非必填
            'package_name' => '', //应用包名 非必填
            'device_id' => '', //设备标识 非必填
            'uid' => '', //App业务自身用户id 非必填
            'serial_id' => '', //项目名+（生成的随机唯一id） 非必填
            'imei' => '', //国际移动设备身份码（15位数字） 非必填
            'mac' => '', //WiFi MAC地址（12位16进制数字，去除冒号转小写 非必填
            'android_id' => '', //Android ID 非必填
            'access' => '', //网络类型 (WiFi、2G、3G、4G) 非必填
            'phone_num' => '', //手机号 非必填
            'nick_name' => '', //昵称 非必填
            'main_channel_id' => '', //首次使用渠道 非必填
        ],
        'data' => [
            'event_type' => '',// 事件类型 必填
            'timeMills' => 0,//事件发生时的时间戳（13位）必填
            'type' => '',//功能类型 非必填
            'extend' => [],//自定义扩展属性字段 非必填
        ],
        'trackinfo' => ['server_ip' => '',],//埋点服务端ip 必填
    ];
    private $kafkaStatus = true;//kafka运行状态

    /**
     * ReportToBigData constructor.
     * @param array $config $config
     */
    public function __construct($config)
    {
        $log_path = APPPATH . "/logs/" . date("Ymd") . "/kafka";
        deepMkdir($log_path);
        $config['log_path'] = $log_path;
        $this->config = $config;
        $this->template['common']['project_name'] = $config['project_name'];
    }

    /**
     * 创建链接
     *
     * @return \Octopus\Producer
     */
    private function connect()
    {
        if (!$this->connect) {
            $this->connect = new \Octopus\Producer([
                'brokers' => implode(',', $this->config['host']),
                'log_level' => LOG_ERR | LOG_WARNING,
                'log_path' => $this->config['log_path'],
                'socket.timeout.ms' => 3000,
                'error_cb' => [$this, 'errorCallback'],
                'dr_msg_cb' => [$this, 'drMsgCallback'],
            ]);
            $this->connect->getKafkaConf()->set('log.connection.close', "false");
            $this->connect->getKafkaConf()->set('socket.keepalive.enable', "true");
            $this->connect
                ->setBrokerServer()
                ->setProducerTopic($this->config['topic']);
        }
        return $this->connect;
    }

    /**
     * 格式化数据
     *
     * @param array $data $data
     * @return array
     */
    private function format($data)
    {
        $return = $this->template;
        foreach ($data as $item => $list) {
            foreach ($list as $key => $val) {
                if (isset($return[$item][$key])) {
                    $return[$item][$key] = $val;
                }
            }
        }
        if (empty($return['data']['extend'])) {
            $return['data']['extend'] = (object)[];
        }
        $return['data']['timeMills'] = (int)$return['data']['timeMills'];
        return $return;
    }

    /**
     * 推送数据
     *
     * @param array $data $data
     * @return bool
     */
    public function push($data)
    {
        $data = $this->format($data);
        $content = json_encode($data);
        if ($content === false) {
            $logPath = APPPATH . "/logs/" . date("Ymd") . "/cron/bigDataDebug/";
            !is_dir($logPath) && mkdir($logPath, true);
            file_put_contents($logPath . date('H') . ".log", time() . " " . bin2hex(serialize($data)) . "\n", FILE_APPEND);
            return false;
        }
        return $this->connect()->producerAsync($content);// 可能抛出 RdKafka\Exception
    }

    /**
     * 推送数据
     *
     * @param int $time 等待时间
     * @return bool
     */
    public function flush($time)
    {
        return $this->connect()->flush($time);
    }

    /**
     * 设置kafka运行状态
     *
     * @param bool $status $status
     * @return void
     */
    public function setKafkaStatus($status = false)
    {
        $this->kafkaStatus = $status;
    }

    /**
     * 获取kafka运行状态
     * @return bool
     */
    public function kafkaIsRunning()
    {
        return $this->kafkaStatus;
    }

    /**
     * 错误方法处理
     * @param  \RdKafka $kafka Kafka类
     * @param string $err 错误信息
     * @param string $reason 错误内容
     * @return void
     */
    public function errorCallback($kafka, $err, $reason)
    {
        $this->kafkaStatus = false;
        $msg = sprintf("Kafka error: %s (reason: %s)", rd_kafka_err2str($err), $reason) . PHP_EOL;
        file_put_contents($this->config['log_path'] . "/err_cb.log", $msg, FILE_APPEND);
        if (RUNMODE == 'production') {
            LoggerFacade::error("kafka异常", $msg);
        }
        if (method_exists($kafka, "purge")) {
            $kafka->purge(RD_KAFKA_PURGE_F_QUEUE);
        }
    }

    /**
     * 分发报告回调函数
     * @param  \RdKafka $kafka Kafka
     * @param string $message 错误信息
     * @return void
     */
    public function drMsgCallback($kafka, $message)
    {
        //do something
    }
}
