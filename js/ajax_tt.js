var xmlhttp;


function createxmlhttp()


{


	if( typeof( XMLHttpRequest ) != "undefined" )


	{


		xmlhttp = new XMLHttpRequest();


	}


	else


	{


		try


		{


			xmlhttp=new ActiveXObject("Msxml2.XMLHTTP");


		}


		catch(e)


		{


			try


			{


				xmlhttp=new ActiveXObject("Microsoft.XMLHTTP");


			}catch(e){}


		}


	}


}





function startrequest(arg,url,cation)


{


 


	createxmlhttp();


	xmlhttp.open("POST",url,true);


	xmlhttp.setRequestHeader("Content-Type","application/x-www-form-urlencoded;");

	
	xmlhttp.onreadystatechange=function ()


	{


		if(xmlhttp.readyState==4&&xmlhttp.status==200)


		{


			cation(xmlhttp.responseText);

			//alert('OK'+xmlhttp.responseText);
		}


	};


	xmlhttp.send(arg);


}