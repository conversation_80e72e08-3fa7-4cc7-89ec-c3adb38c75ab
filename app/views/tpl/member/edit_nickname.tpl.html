<!DOCTYPE HTML>
<html>
<head>
    <meta charset="gb2312" />
    <title>用户中心-完善用户名</title>
    <meta content="" name="Description" />
    <meta content="" name="Keywords" />
    <link rel="stylesheet" type="text/css"  href="/css/v2/common_20150922.css">
    <link rel="stylesheet" type="text/css"  href="/css/v2/uc_20150922.css">
    <script type="text/javascript" src="/js/jquery-1.8.3.min.js"></script>
</head>
<body>
<div class="wrapper">
    {{include file="public/header.tpl.html"}}
    <div class="main">
        <div class="m-boxA minHeight">
            <div class="m-boxA-hd">
                <h2 class="m-boxA-tit">设置昵称</h2>
                <a class="more" href="/member/editInfo">&lt; 返回个人中心</a>
            </div>
            <div class="m-boxA-bd">
                {{if $pageArray.success}}
                <div class="m-boxA-bd">
                    <div class="weiTips">昵称是您的个性身份展示，只可修改一次，不能用来登录。</div>
                    <div class="retShow">
                        <table>
                            <tbody>
                            <tr>
                                <td>
                                    <i class="icon-show icon-ok-big"></i>
                                    <div class="retCon">
                                        <div class="name">完善昵称成功！</div>
                                        <div class="tips" id="forwardTips">
                                            3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <script type="text/javascript">
                var times = 3;
                setInterval(function() {
                    if (times > 0) {
                        $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
                    } else {
                        window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
                    }
                    times--;
                }, 1000);
            </script>
            {{else}}
                <div class="ucfnBox">
                    <div class="m-form">
                        <span class="sTitTips">昵称是您的个性身份展示，<em>只可修改一次，不能用来登录。</em></span>
                        <form action="" method="post" id="nicknameForm">
                            <div class="form-item">
                                <span class="form-field">昵称：</span>
                                <input class="ipt_txt ipt_defa" name = "nickname" type="text" value=""/>
                                <span class="form-tips">请输入2-24个字符，支持中英文</span>
                            </div>
                            <div class="form-item">
                                <a class="btn-blueA" onclick="$('#nicknameForm').submit();" href="###">确定修改</a>
                            </div>
                        </form>
                    </div>
                </div>
            {{/if}}
            </div>
        </div>
    </div>
    {{include file="public/footer.tpl.html"}}