<?php
echo "ip：".get_client_ip() . "<br>";
echo "isHttps：".isHttps();

function get_client_ip($twiceProxy = true)
{
    $onlineIp = $_SERVER['REMOTE_ADDR'];
    if (filter_var($onlineIp, FILTER_VALIDATE_IP)) {
        return $onlineIp;
    } else {
        xLog('getClientIP', 'getClientIP', '??$_SERVER[REMOTE_ADDR]????л????IP');
        return '0.0.0.0';
    }
}

function isHttps()
{
    if (!empty($_SERVER['HTTPS']) && strtolower($_SERVER['HTTPS']) !== 'off')
    {
        return TRUE;
    }
    elseif (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')
    {
        return TRUE;
    }
    elseif (!empty($_SERVER['HTTP_FRONT_END_HTTPS']) && strtolower($_SERVER['HTTP_FRONT_END_HTTPS']) !== 'off')
    {
        return TRUE;
    }

    return FALSE;
}