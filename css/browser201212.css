@charset "gb2312";
/* index20120801.css
 * Created: 2012-08-01
 * Update: 2012-08-20
 * Author: amanda
 */
html, body, div, span,applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, dd, dl, dt, li, ol, ul, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td {margin:0;padding:0;}
body{font:12px/1.5 Arial;color:#515151;background-color:#fff;word-wrap:break-word;word-break:break-all;}
h1,h2,h3,h4,h5,h6{font-size:100%;}
em,b,i,strong,cite,sub,sup{font-style: normal;font-weight:normal;}
a{text-decoration: none;color:#0e7dcc;outline: none;}
a:hover{text-decoration:underline;color:#ff4800;}
a:active{star:expression(this.onFocus=this.blur());}
:focus{outline:0;}
li{list-style-type:none;}
img{border:0 none;vertical-align:top;}
fieldset{border-style: none }
label,button{cursor: pointer;}
select,input,textarea{font:12px/1.2em tahoma,arial,\5b8b\4f53;}
.txl-r input,.txl-r select{ font-size:12px;}
.clearfix:after{content:".";height:0;visibility:hidden;display:block;clear:both;font-size:0;line-height:0;}
.clearfix{*zoom:1;}
.fix{word-break:keep-all;overflow:hidden;text-overflow:ellipsis;}
.clear{font-size:0;line-height:0;height:0;clear:both;overflow: hidden;display: block;}
.none{display:none;}
html{-webkit-text-size-adjust:none;}
*html{background-image:url(about:blank);background-attachment:fixed;}

body{background:url(../images/bg.png) repeat-x 0 0 #fff;}
.wrapper{background:url(../images/wrapper.png) no-repeat center 104px;}
.header{background:url(../images/header.png) repeat-x 0 0;}
.top_bd li,.top_bd .logout,.round .tit,.round .r{background:url(../images/global.png) no-repeat 0 0;}
.fav_main .tit a i,.fav_main .content .hd,.fav_main .content .btn,.fav_main .aside .op_hd,.tree li i,.tree li s,.fav_main .article .inpbg,.fav_main .article .search_box .btn,.fav_main .article .data_tit,.pop_box .pop_tit,.pop_box .pop_tit span,.pop_box .pop_content .btn_area .btn,.pop_box .pop_content .sel_box,.pop_box .pop_content .icon span,.fav_main .article .hd .a_btn,.wrap_op .a_btn,.pop_backups .bd .btn{background:url(../images/fav2013.png) no-repeat 0 0;}

.pop_backups .hd .close{background:url(../images/close.png) no-repeat 0 0;}
.wrap{margin:0 auto;width:990px;background-color:#fff;}

/* header */
.header{height:116px;position:relative;z-index:100;}
.top_bd li a,.top_bd .item_bd span,.header_bd li a,.header_bd .item i{display:inline-block;*display:inline;*zoom:1;}
.header .top_bd{height:31px;line-height:28px;}
.top_bd{position:relative;z-index:100;}
.top_bd ul{float:right;}
.top_bd li{float:left;padding:0 10px;background-position:100% 10px;}
.top_bd .nobr{_padding-right:5px;background:none;}
.top_bd .logout{_margin-top:7px;padding-left:22px;height:16px;line-height:16px;background-position:-284px -30px;}

.header .header_bd{padding-top:10px;}
.header_bd .logo_box{float:left;}

/* footer */
.footer{margin-top:10px;line-height:21px;text-align:center;}
.footer em{margin:0 10px;color:#999;}
.footer a{color:#333;}
.footer a:hover{color:#f60;}

/* round */
.round{border:1px solid #d5dfe3;position:relative;}
.round .tit{position:relative;z-index:3;height:36px;background-position:0 -120px;background-repeat:repeat-x;border-bottom:1px solid #d5dfe3;}
.round .r{position:absolute;z-index:6;width:4px;height:4px;overflow:hidden;display:inline-block;*display:inline;*zoom:1;}
.round .lt{left:-1px;top:-1px;_top:0;background-position:0 -110px;}
.round .rt{right:-1px;top:-1px;_top:0;_right:0;background-position:-4px -110px;}
.round .lb{left:-1px;bottom:-1px;background-position:0 -114px;}
.round .rb{right:-1px;bottom:-1px;background-position:-4px -114px;}

/* fav_main */
.fav_main .tit a i,.fav_main .aside .op_hd{display:inline-block;*display:inline;*zoom:1;}
.fav_main .tit{padding:8px 0 0 15px;height:28px;}
.fav_main .tit h2{width:92px;height:20px;background-position:0 0;}
.fav_main .tit h3{font-weight:100; font-size:14px; color:#515151;} 
.fav_main .tit p{position:absolute;right:16px;top:8px;}
.fav_main .tit p a{margin-left:19px;color:#515151;}
.fav_main .tit p a:hover{color:#f60;}
.fav_main .tit a i{margin-right:5px;width:14px;height:15px;vertical-align:middle;overflow:hidden;}
.fav_main .tit .help i{background-position:-100px 0}
.fav_main .tit .feedback i{height:16px;background-position:-120px 0}
.fav_main .content .hd{padding:4px 0 0 10px;height:30px;background-position:0 -120px;background-repeat:repeat-x;}
.fav_main .content .btn{margin:0;padding:0;border:0 none;width:76px;height:25px;line-height:25px;text-align:center;color:#fff;font-weight:bold;cursor:pointer;background-position:0 -60px;overflow:hidden;}
.fav_main .aside,.fav_main .article{float:left;}
.fav_main .aside{width:197px;border-right:2px solid #e6ecf0;}
.fav_main .article{width:789px;}
.fav_main .aside .btn{float:left;margin-right:8px;}
.fav_main .aside .op{float:left;}
.fav_main .aside .op .op_inner{position:relative;}
.fav_main .aside .op_hd{padding-left:12px;width:70px;height:25px;overflow:hidden;line-height:25px;color:#085fb5;background-position:0 -30px;}
.fav_main .aside .op_hd:hover{color:#ff4800;}
.fav_main .aside .op .box .box_mask{position:absolute;top:25px;left:0;background-color:#fff;border:1px solid #b8c4cc;width:80px;height:68px;z-index:999;}
.fav_main .aside .op .box li{display:block;height:22px;line-height:22px;overflow:hidden; border-bottom:1px dotted #ccc; padding-left:12px;color:#ccc; cursor:default}
.fav_main .aside .op_bd{position:absolute;top:25px;left:0;border:1px solid #b8c4cc;width:80px;height:68px;background-color:#fff;overflow:hidden;}
.fav_main .aside .op_bd li a{display:block;height:22px;line-height:22px;overflow:hidden; border-bottom:1px dotted #dee4ee; padding-left:12px;color:#333;}
.fav_main .aside .op_bd li a:hover{color:#fff; background:#8b9ebc;text-decoration:none;}
.tree{width:197px;height:581px;overflow:auto; background:#f5f8fa;}
.tree .inner{width:100%;display:table;}
.tree li{padding-left:16px;line-height:32px;white-space:nowrap;font-size:14px;cursor:pointer;}
.tree li.cur{ background:#e6ecf0;}
.tree li.cur font{color:#333; font-weight:bold;}
.tree li:hover{ background:#fff;}
.tree i,.tree s,.tree a{vertical-align:middle;display:inline-block;*display:inline;*zoom:1;}
.tree li i{margin-right:7px;width:9px;height:9px;overflow:hidden;}
.tree li .none{background:none;}
.tree .open i{background-position:-140px 0;}
.tree .close i{background-position:-140px -11px;}
.tree li s{margin-right:5px;width:16px;height:16px;overflow:hidden;background-position:-180px 0;}
.tree li.import s{ background-position:-160px -1px;}
.tree li a{white-space:white-space;}
.tree li a span{color:#999}
.tree a font{display:inline-block;*display:inline;*zoom:1;}
.tree li:hover a{text-decoration:none;color:#0e7dcc;}
.tree li:hover a font{text-decoration:underline;}
.fav_main .article .hd{}
.fav_main .article .hd .btn{margin-right:10px; vertical-align:middle;}
.fav_main .article .hd a{vertical-align:middle;}
.fav_main .article .hd a:hover{color:#ff4800;}
.fav_main .article .hd a img{vertical-align:middle;}
.fav_main .article .hd em{margin:0 10px; vertical-align:middle;color:#808080}
.fav_main .article .hd .a_btn{padding:0;margin-right:8px;width:91px;height:25px;text-decoration:none;text-align:center;line-height:25px;overflow:hidden;background-position:-150px -30px;display:inline-block;*display:inline;*zoom:1;}
.fav_main .article .box{padding:8px 10px;height:24px;line-height:24px;}
.fav_main .article .op_box{color:#808080;_padding-top:8px;}
.fav_main .article .op_box input{margin-right:7px;width:14px;height:14px; vertical-align:middle;}
.fav_main .article .op_box label{margin-right:10px;color:#000;}
.fav_main .article .op_box em{margin:0 10px;}
.fav_main .article .search_box{float:right;}
.fav_main .article .inpbg{padding:3px 3px 2px 7px;width:151px;height:18px;line-height:18px;border:1px solid #b8c4cc;background-position:0 -200px;display:inline-block;*display:inline;*zoom:1; vertical-align:middle}
.fav_main .article .inpbg input{margin:0;padding:0;border:0 none;width:151px;height:18px;line-height:18px;background:transparent;}
.fav_main .article .search_box .btn{margin:0;padding:0;border:0 none;width:56px;height:25px;color:#333;background-position:-90px -30px;vertical-align:middle}
.fav_main .article .data_tit{height:30px;background-position:0 -160px;background-repeat:repeat-x;border-bottom:1px solid #e5e5e5;overflow:hidden;}
.fav_main .article .data_tit .title{padding-left:50px;width:154px;}
.fav_main .article .data_tit .url{padding-left:10px;width:214px;}
.fav_main .article .data_tit .memo{padding-left:10px;width:159px;}
.fav_main .article .data_tit .op{width:154px; text-align:center;}
.fav_main .article .data_tit li{float:left;line-height:30px;}
.fav_main .article .data{width:100%;color:#333; border-collapse:collapse; border-spacing:0; table-layout:fixed;}
.fav_main .article .data th,.fav_main .article .data td{padding:0 10px;line-height:29px;border:1px solid #e6e6e6;border-width:0 1px 1px 0; font-family:\5B8B\4F53;}
.fav_main .article .data th{ text-align:left;font-weight:normal;}

.fav_main .article .data .star{ display:inline-block; width:16px; height:16px;background:url(../images/comm.png) 0 -250px no-repeat; float:right; margin-top:6px;}

.fav_main .article .data th img{padding-right:5px;vertical-align:middle; float:left; margin-top:7px;}
.fav_main .article .data th input{margin-right:3px;width:13px;height:13px;vertical-align:middle; float:left; margin-top:8px;}
.fav_main .article .data .title{width:176px;}
.fav_main .article .data .url{width:197px;}
.fav_main .article .data .memo{width:169px;}
.fav_main .article .data .op{width:154px;}
.fav_main .article .cell_center{text-align:center;}
.fav_main .article .data_content{width:789px;height:422px;overflow:auto;}
.fav_main .article .data em{margin:0 10px;color:#b3b3b3}
.fav_main .article .line{margin:0 5px;padding:8px 5px;border-bottom:1px dashed #c9c9c9; clear:both}
.pagination{height:22px;font-family:Tahoma;padding:12px 0 13px;text-align:center;}
.pagination a,.pagination strong,.pagination span{ display:inline-block;height:20px;line-height:20px;overflow:hidden;min-width:10px;_width:10px;padding:0 5px;margin-right:5px;text-align:center;white-space:nowrap;vertical-align:middle;font-family:Arial,SimSun;}
.pagination a{border:solid 1px #dbdbdb;color:#515151;background-color:#fff;}
.pagination a:hover{color:#117dac;border:solid 1px #b1d8e0;background-color:#e8fbff;text-decoration:none;}

.pagination .prev{width:auto;}
.pagination .prev_none{color:#e4e4e4;}
.pagination .next{width:auto;}
.pagination .next_none{color:#e4e4e4;}
.pagination .page_cur{color:#fff;font-weight:bold;background-color:#17b2ce;border:1px solid #178da1}


/* pop_box */
.pop_box{position:absolute;padding:3px;width:384px;background-color:#a0a0a0;z-index:9999;}
.pop_box .inner{background-color:#fff;width:384px;}
.pop_box .pop_tit{padding:0 10px 0 11px;height:35px;line-height:35px;font-size:16px;color:#4c6370;background-position:0 -230px;background-repeat:repeat-x;}
.pop_box .pop_tit span{float:right;margin-top:10px;width:18px;height:18px;overflow:hidden;display:inline-block;*display:inline;*zoom:1;background-position:-140px -60px;cursor:pointer;}
.pop_box .pop_content{padding:7px 21px;background-color:#fff;position:relative;}
.pop_box .pop_content .field{margin-bottom:15px;}
.pop_box .pop_content .hd{width:314px;margin-bottom:5px;}
.pop_box .pop_content .hd label{font-weight:bold;}
.pop_box .pop_content .inp{padding:0 7px;width:300px;height:22px;font-size:14px;line-height:22px;color:#333;border:1px solid #b9c5cd;}
.pop_box .pop_content .spark_indeed{color:#ff5100}
.pop_box .pop_content .hd a,.pop_box .pop_content .btn_area a{ text-decoration:underline;}
.pop_box .pop_content .hd .help{float:right;}
.pop_box .pop_content .hd .help img{ vertical-align:middle}
.pop_box .pop_content .textarea{padding:3px 7px;width:300px;font-size:14px;line-height:22px; color:#333;border:1px solid #b9c5cd;resize:none;}
.pop_box .pop_content .nomargin{margin-bottom:0;}
.pop_box .pop_content .btn_area{padding:10px 0 4px;}
.pop_box .pop_content .btn_area .btn{margin:0 15px 0 0;padding:0;border:0 none;width:82px;height:25px;text-align:center;line-height:25px;background-position:0 -90px;color:#fff;font-weight:bold;cursor:pointer; vertical-align:middle}
.pop_box .pop_content .drop{position:absolute;left:21px;top:53px;_left:0;width:314px;border:1px solid #b8c4cc;background-color:#fff;z-index:99;}
.pop_box .pop_content .drop li{line-height:25px;height:25px;overflow:hidden;}
.pop_box .pop_content .drop a{padding-left:7px;color:#333;zoom:1;display:block;}
.pop_box .pop_content .drop a:hover{color:#333; text-decoration:none;background-color:#bfe0ed;}
.pop_box .pop_content .drop .keycode{color:#333; text-decoration:none;background-color:#bfe0ed;}
.pop_box .pop_content .sel_box{padding:0 0 0 7px;width:185px;height:22px;font-size:14px;line-height:22px;color:#333;border:1px solid #b9c5cd;background-position:0 -270px;}
.pop_box .pop_content .sel_list{position:absolute;left:21px;top:115px;_left:0;*top:117px;width:192px;border:1px solid #b8c4cc;background-color:#fff;z-index:99;}
.pop_box .move .sel_list{top:53px;}
.pop_box .pop_content .tree{height:225px;}
.pop_box .pop_content .result{padding:11px 0 0 50px;width:250px;}
.pop_box .pop_content .icon{float:left;width:60px;}
.pop_box .pop_content .hint{float:left;width:190px;line-height:22px;}
.pop_box .pop_content .hint h4{font-size:12px;}
.pop_box .pop_content .icon span{width:44px;height:44px;background-position:-90px -60px;display:inline-block;*display:inline;*zoom:1;}
.pop_box .pop_content .hint .btn_area{padding:19px 0 11px;}

.fav_main .tit .spec{color:#0e7dcc;font-size:14px;font-family:simsun; text-decoration:underline}
.fav_main .tit .spec:hover{color:#ff4800}
.fav_main .tit .desc{font-size:12px;color:#666;line-height:24px;}

/* backups */
.backups .tit{padding:8px 10px 0;}
.backups .tit .spec{float:right;}

/* data_backups */
.data_backups{margin-bottom:6px;width:100%; table-layout:fixed; border-collapse:collapse; border-spacing:0;font-size:14px;}
.data_backups th{padding-left:20px;height:32px;line-height:32px;background-color:#f2f5fa;font-weight:normal;text-align:left;color:#666;border-bottom:1px solid #e0e4eb}
.data_backups td{padding-left:20px;color:#333;line-height:32px;}
.data_backups .num{width:160px;}
.data_backups .time{width:215px;}
.data_backups .ip{width:250px;}
.data_backups .record{width:160px;}
.data_backups .cell_center{text-align:center;}
.data_backups .rec_center{padding:0 20px;text-align:center;}
.data_backups .bg{background-color:#f8f9fb;}
.data_backups a{ text-decoration:underline}
.data_backups .bold{font-weight:bold;}

.pop_mask{position: absolute; left: 0; top: 0; z-index: 888;width: 100%;height:100%;_height: expression(document.body.scrollHeight > document.body.offsetHeight ? document.body.scrollHeight : document.body.offsetHeight + 'px');overflow:hidden;background:#000;opacity:.3;-ms-filter:alpha(opacity=30);filter:alpha(opacity=30);}
.pop_backups{position:absolute;top:50%;left:50%;margin:-100px 0 0 -159px;width:318px;height:200px;z-index:900;}
.pop_backups .inner{padding:2px;font-size:14px;line-height:24px;background-color:#5ea8cf;border:1px solid #fff;}
.pop_backups .hd{position:relative;padding:0 8px;height:30px;color:#fff;line-height:30px;font-weight:bold}
.pop_backups .hd .close{position:absolute;top:8px;right:8px;width:10px;height:11px;overflow:hidden;display:block;cursor:pointer}
.pop_backups .bd{padding:25px 30px;background-color:#fff;}
.pop_backups .bd .btn_area{margin-top:15px; text-align:center}
.pop_backups .bd .btn{margin:0 7px;padding:0;border:0 none;width:56px;height:25px;text-align:center;line-height:25px;cursor:pointer;font-weight:normal;color:#333;background-position:-90px -30px;vertical-align:middle}

.pop_load{position:absolute;top:50%;left:50%;margin:-38px 0 0 -134px;width:268px;height:76px;z-index:900;}
.pop_load .bd{padding:25px 36px;width:190px;height:14px;overflow:hidden;}
.fav_main .content .btn_wide{background:url(../images/btn_wide.png) no-repeat 0 0; width:100px;}