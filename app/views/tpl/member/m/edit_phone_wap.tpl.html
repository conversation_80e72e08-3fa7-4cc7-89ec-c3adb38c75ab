{{include file = "member/m/head.tpl.html"}}
    {{if $pageArray.step == 2}}
    <article class="g-user-bd">
        <header>
            <h1 class="g-user-title">修改手机</h1>
        </header>
        <p class="g-user-tips">温馨提示:请输入您要绑定的手机号,可用该手机号登录所有2345产品，如2345网址导航、2345浏览器等</p>
        <form action="/member/editPhone/step3" id="phone-form" class="g-user-form" method="POST" onsubmit="return checkCode();">
            <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
            <div class="form-item">
                <p class="form-txt">原绑定手机:<em>{{$pageArray.phone}}</em></p>
            </div>
            <div class="form-item">
                <div class="form-vcode">
                    <input type="tel" id="code" name="code" class="form-ipt" placeholder="请输入验证码">
                    <a href="javascript:;" id="sendBtn" class="btn-getvcode">发送验证码</a>
                    <span id="sendingBtn" class="btn-getvcode btn-disabled" style = "display :none">重新发送(60)</span>
                </div>
                <p class="form-tip-error" id="checkMsg" style="display: none;"><i></i>验证码错误</p>
            </div>
            <div class="btn-group">
               <a href="#" class="btn-confirm" onclick="$('#phone-form').submit();return false;">下一步</a>
            </div>
        </form>
        <footer class="g-user-help">
            <p>如果您的手机无法收到验证码,请致电客服解决:<br>客服电话:************</p>
        </footer>
    </article>
    <script type="text/javascript">
    $(".form-ipt").bind({
        focus: function() {
            $(this).closest(".form-item").removeClass("form-item-error");
            $(this).closest(".form-item").find(".form-tip-error").hide();
        },
    });
    $(".btn-confirm").on("click", function(e)
        {
            $("#phone-form").submit();
            e.preventDefault();
    });
    $('#sendBtn').on('click',function(e){
        sendCode('phone');
        e.preventDefault();
    })
    function sendCode(from)
    {
        $.post("/member/editPhone/sendCode", {'from': from}, function(data)
        {
            if (data == '400') {
                $("#checkMsg").html('<i></i>验证的太频繁了，请稍后再试！');
                $("#checkMsg").parent().addClass('form-item-error');
                $("#checkMsg").show();
            } else if (data == '500') {
                $("#checkMsg").html('<i></i>服务器忙，请稍后再试！');
                $("#checkMsg").parent().addClass('form-item-error');
                $("#checkMsg").show();
            }
        });
        $("#sendBtn").text('重新发送');
        showSendMsg(60);
    }
    function showSendMsg(times)
    {
        $("#sendBtn").hide();
        $("#sendingBtn").text('重新发送(' + times + ')');
        $("#sendingBtn").show();
        if (times > 1)
        {
            times--;
            setTimeout(function() {
                showSendMsg(times);
            }, 1000);
        }
        else
        {
            $("#sendingBtn").hide();
            $("#sendBtn").show();
        }
    }
    function checkCode()
    {
        var codeErr = true;
        var code = $("#code").val();
        if (!code) {
            $("#checkMsg").html('<i></i>请输入验证码！');
            $("#checkMsg").parent().addClass('form-item-error');
            $("#checkMsg").show();
            return false;
        }
        $.ajax({
            type: "POST",
            url: "/member/editPhone/checkCode",
            async: false,
            data: "code=" + code,
            success: function(data) {
                if (data == '200') {
                    codeErr = false;
                } else if (data == '400') {
                    $("#checkMsg").html('<i></i>验证码错误！');
                    $("#checkMsg").parent().addClass('form-item-error');
                    $("#checkMsg").show();
                }
            }
        });
        return !codeErr;
    }
    </script>
    <script type="text/javascript">
            document.body.addEventListener('touchstart', function () {});
    </script>
    {{elseif $pageArray.step == 3}}
    <article class="g-user-bd">
        <header>
            <h1 class="g-user-title">修改手机</h1>
        </header>
        <p class="g-user-tips">温馨提示:请输入您要绑定的手机号,可用该手机号登录所有2345产品，如2345网址导航、2345浏览器等</p>
        <form action="/member/editPhone/step4" id="phone-form" class="g-user-form" method="post" onsubmit="return checkCode(true);">
            <input type="hidden" name="forward" value="{{$pageArray.forward}}"/>
            <input type="hidden" id="verifycode" name="code1" value="{{$pageArray.code}}"/>
            <input type="hidden" name="from" value="{{$pageArray.from}}"/>
            <div class="form-item">
                <input type="tel" id="phone"  class="form-ipt" placeholder="请输入手机号">
                <input type="hidden" id="phone1" name="phone" >
                <p class="form-tip-error" id="sendMsg" style="display: none;"><i></i>验证码错误</p>
            </div>
            <div class="form-item">
                <div class="form-vcode">
                    <input type="tel" id="code" name="code2" class="form-ipt" placeholder="请输入验证码">
                    <a href="javascript:;" id="sendBtn" class="btn-getvcode" >发送验证码</a>
                    <span id="sendingBtn" class="btn-getvcode btn-disabled" style = "display :none">重新发送(60)</span>
                </div>
                <p class="form-tip-error" id="checkMsg" style="display: none;"><i></i>验证码错误</p>
            </div>
            <div class="btn-group">
               <a href="#" class="btn-confirm">下一步</a>
            </div>
        </form>
        <footer class="g-user-help">
            <p>如果您的手机无法收到验证码,请致电客服解决:<br>客服电话:************</p>
        </footer>
    </article>
    <script type="text/javascript">
    $(".form-ipt").bind({
        focus: function() {
            $(this).closest(".form-item").removeClass("form-item-error");
            $(this).closest(".form-item").find(".form-tip-error").hide();
        },
    });
    $(".btn-confirm").on("click", function(e)
        {
            $("#phone-form").submit();
            e.preventDefault();
    });
    $('#sendBtn').on('click',function(e){
        checkAndSend();
        e.preventDefault();
    })
        var phoneErr = 1;
        function checkAndSend() {
            var phone = $("#phone").val();
            if (!phone) {
                phoneErr = 1;
                return false;
            }
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                phoneErr = 1;
                $("#sendMsg").html('<i></i>请输入正确的手机号！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            $.ajax({
                url: "/api/check/jsonp",
                data: 'type=phone&value=' + phone,
                async: false,
                dataType: 'jsonp',
                jsonp: 'callback',
                success: function(response) {
                    err = response;
                    if (err == 1)
                    {
                        phoneErr = 1;
                        $("#sendMsg").html('<i></i>请输入正确的手机号！');
                        $("#sendMsg").parent().addClass('form-item-error');
                        $("#sendMsg").show();
                        return false;
                    }
                    else if (err == 2)
                    {
                        phoneErr = 1;
                        $("#sendMsg").html('<i></i>此手机号已被绑定，请重新输入！');
                        $("#sendMsg").parent().addClass('form-item-error');
                        $("#sendMsg").show();
                        return false;
                    }
                    else if (err == 3)
                    {
                        phoneErr = 1;
                        $("#sendMsg").html('<i></i>此手机号已被绑定，请重新输入！');
                        $("#sendMsg").parent().addClass('form-item-error');
                        $("#sendMsg").show();
                        return false;
                    }
                    phoneErr = 0;
                    sendCode('phone');
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    phoneErr = 1;
                },
                timeout: 3000
            });
        }
        function sendCode() {
            var phone = $("#phone").val();
            var verifycode = $("#verifycode").val();
            if (phone == '') {
                $("#sendMsg").html('<i></i>请先输入手机号！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            if (phoneErr == 1) {
                return false;
            }
            $.post("/member/editPhone/sendCode", {'phone': AES.encrypt(phone), 'verifycode':verifycode}, function(data) {
                if (data == '300') {
                    $("#sendMsg").html('<i></i>请输入正确的手机号！');
                    $("#sendMsg").parent().addClass('form-item-error');
                    $("#sendMsg").show();
                } else if (data == '400') {
                    $("#sendMsg").html('<i></i>验证的太频繁了，请稍后再试！');
                    $("#sendMsg").parent().addClass('form-item-error');
                    $("#sendMsg").show();
                } else if (data == '500') {
                    $("#sendMsg").html('<i></i>服务器忙，请稍后再试！');
                    $("#sendMsg").parent().addClass('form-item-error');
                    $("#sendMsg").show();
                }
            });
            $("#sendBtn").text('重新发送');
            showSendMsg(60);
        }
        function showSendMsg(times)
        {
            $("#sendBtn").hide();
            $("#sendingBtn").text('重新发送(' + times + ')');
            $("#sendingBtn").show();
            if (times > 1) {
                times--;
                setTimeout(function() {
                    showSendMsg(times);
                }, 1000);
            } else {
                $("#sendingBtn").hide();
                $("#sendBtn").show();
            }
        }
        function checkCode(submit) {
            var phone = $("#phone").val();
            if (!phone) {
                phoneErr = 1;
                $("#sendMsg").html('<i></i>请先输入手机号！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            if (!/^1[0123456789]\d{9}$/.test(phone)) {
                phoneErr = 1;
                $("#sendMsg").html('<i></i>请输入正确的手机号！');
                $("#sendMsg").parent().addClass('form-item-error');
                $("#sendMsg").show();
                return false;
            }
            else
            {
                phoneErr = 0;
            }
            var codeErr = true;
            var phone = $("#phone").val();
            var code = $("#code").val();
            if (phoneErr == 1) {
                return false;
            }
            if (!code) {
                $("#checkMsg").html('<i class="icon-error"></i>请输入验证码！');
                $("#checkMsg").parent().addClass('form-item-error');
                $("#checkMsg").show();
                return false;
            }
            $.ajax({
                type: "POST",
                url: "/member/editPhone/checkCode",
                async: false,
                data: "phone=" + AES.encrypt(phone) + "&code=" + code,
                success: function(data) {
                    if (data == '200') {
                        codeErr = false;
                    } else if (data == '300') {
                        $("#checkMsg").html('<i></i>请输入正确的手机号！');
                        $("#checkMsg").parent().addClass('form-item-error');
                        $("#checkMsg").show();
                    } else if (data == '400') {
                        $("#checkMsg").html('<i></i>验证码错误！');
                        $("#checkMsg").parent().addClass('form-item-error');
                        $("#checkMsg").show();
                    }
                }
            });
            if (submit === true && codeErr === false)
            {
                $("#phone1").val(AES.encrypt($("#phone").val()));
            }
            return !codeErr;
        }
    </script>
    <script type="text/javascript">
        document.body.addEventListener('touchstart', function () {});
    </script>
    {{elseif $pageArray.step == 4}}
    <article class="g-user-bd">
        <div class="g-user-result bind-sec">
            <h1><i></i>修改手机成功</h1>
            <p id="forwardTips">3秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a></p>
        </div>
    </article>

    <script type="text/javascript">
        var times = 3;
        setInterval(function() {
            if (times > 0) {
                $("#forwardTips").html(times + '秒后<a href="{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}">返回上级页面</a>');
            } else {
                window.location = "{{if $pageArray.forward}}{{$pageArray.forward}}{{else}}/member/editInfo{{/if}}";
            }
            times--;
        }, 1000);
    </script>
    {{/if}}
    {{include file = "public/footer_m.tpl.html"}}
<script type="text/javascript" src="/js/client/aes.js"></script>
<script type="text/javascript" src="/js/client/encrypt.min.js"></script>
</body>
</html>