<?php

includeBase('AdminController');
use Service\Encryption\Aes\AesManager;

class RegQueryController extends AdminController
{

    protected $authName = '用户信息查询';

    /**
     * 功  能：
     * @return void
     */
    public function actionIndex()
    {
        $type = \Common\Utils\Url::getStringParam('type');
        $value = \Common\Utils\Url::getStringParam('value');
        $regIp = \Common\Utils\Url::getStringParam('regip');
        $startTime = !empty($_GET['start']) ? \Common\Utils\Url::getStringParam('start') . " 00:00:00" : "";
        $endTime = !empty($_GET['end']) ? \Common\Utils\Url::getStringParam('end') . " 23:59:59" : "";
        $page = (int)$_GET['page'] ? $_GET['page'] : 1;
        if ($type && !$value && !$regIp) {
            if (!$startTime && !$endTime || abs(strtotime($endTime) - strtotime($startTime)) > 86400 * 7) {
                showMessage('请至少指定一个注册ip，用户名手机号或者邮箱，时间间隔小于一星期', 'back');
            }
        }
        $showData = [
            "type" => $type,
            "value" => $value,
            "regip" => $regIp,
            "start" => \Common\Utils\Url::getStringParam('start'),
            "end" => \Common\Utils\Url::getStringParam('end'),
            "page" => $page
        ];
        if (!$type) {
            loadView('admin/reg_query.tpl.html', $showData);
            exit;
        }
        $user = [];
        $limit = 20;
        $totalRecord = 0;
        $memberModel = loadModel('member');
        $PageAction = loadAction('Page');
        $PageAction->returnPageConfig($limit);
        if (trim($value) != '') {// 精确搜索
            if ($type == 'username') {
                $user = $memberModel->getUserInfoByUsername($value);
            } elseif ($type == 'id') {
                $user = $memberModel->getBaseUserInfo($value);
            } elseif ($type == 'email') {
                // 只能搜索验证的
                $user = $memberModel->getUserInfoByEmail($value);
            } elseif ($type == 'phone') {
                $user = $memberModel->getUserInfoByPhone($value);
            }
            if ($user) {
                // 要做范围过滤
                if ($regIp) {
                    if ($user['reg_ip'] != $regIp) {
                        $user = [];
                    }
                }
                if ($startTime || $endTime) {
                    if (!(
                        strtotime($user['reg_time']) >= strtotime($startTime) &&
                        strtotime($user['reg_time']) <= strtotime($endTime))
                    ) {
                        $user = [];
                    }
                }
                if ($user) {
                    $totalRecord = 1;
                    $user = [$user];
                }
            } else {
                $user = [];
            }
        } else {// 范围搜索
            $user = $memberModel->getUserList(
                $page,
                $limit,
                $startTime,
                $endTime,
                $regIp
            );
            $totalRecord = $user['total'];
            $user = $user['list'];
        }
        foreach ($user as $key => $item) {
            $user[$key]['bind'] = [1 => $item['uid']];
            list($avatarUrl) = \Service\Avatar\Avatar::getTransferAvatar('login', $item['id'], 'middle');
            $user[$key]['avatar'] = $avatarUrl;
            $user[$key]['phoneEncrypt'] = (new AesManager())->aes128cbcEncrypt($item['phone']);
            $user[$key]['phone'] = empty($item['phone']) ? $item['phone'] : $this->userlogoHidden($item['phone'], 'phone');
            $user[$key]['emailEncrypt'] = (new AesManager())->aes128cbcEncrypt($item['email']);
            $user[$key]['email'] = empty($item['email']) ? $item['email'] : $this->userlogoHidden($item['email'], 'email');
        }
        $showData['listPage'] = $PageAction->showPage($totalRecord, '/houtai_gl/reg_query');
        $showData['user'] = $user;
        loadView('admin/reg_query.tpl.html', $showData);
    }

    /**
     * actionClearRegPhoneFromRedis
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionClearRegPhoneFromRedis()
    {
        $phone = intval($_GET['phone']);
        $memberRedis = loadAction('memberRedis');
        $result = $memberRedis->hashDel($phone, REG_PHONE_INFO_REDIS_KEY, $phone);
        if ($result === false) {
            $output = 'redis连接失败';
        } else {
            $output = '清理成功';
        }
        echo json_encode(array('result' => mb_convert_encoding($output, 'UTF-8', 'GBK')));
    }
}
