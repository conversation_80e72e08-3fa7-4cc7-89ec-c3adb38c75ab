<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/3/29
 * Time: 17:30
 */

class GrantAuthController extends Controller
{

    /**
     * UnionLoginController constructor.
     * -
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        if (RUNMODE != 'development' && !HTTPS) {
            $this->codeResponse(4000, "", '请求拒绝');
        }
    }

    /**
     * 游戏对外SDK  xq授权登录
     * -
     * @return void
     */
    public function actionIndex()
    {
        $this->startSession();
        $necessaryField = array(
            'mid',
            'source',
            'exId',
            'extendData',
            'timestamp',
            \Service\Encryption\Sign\SignManager::$paramSignName,
        );
        list($mid, $source, $exId, $extendData) = $this->getParamAndAutoDeny($necessaryField);  //字段不能为空+sign校验
        $checkLogin = loadAction('checkLoginForClient');
        //验证fToken
        if (!$checkLogin->checkFToken()) {
            $checkLogin->clearFInfo();
            $this->codeResponse(4004, "", "请求拒绝");
        }
        $extendData = base64_decode($extendData);
        $loginAction = loadAction('login');
        $loginAuth = $loginAction->checkAuth($source);  //校验登录授权
        if (!empty($loginAuth)) {
            $passid = $loginAuth['i'];
            $redis = RedisEx::getInstance();
            $unionLoginKey = 'grantAuth:exchange:' . $mid . ':' . intval($loginAuth['i']);
            $hourNum = $redis->get($unionLoginKey);
            //如果1小时里面连续调用5次,就不再兑换,1小时后再试
            if ($hourNum >= 6) {
                $checkLogin->clearFInfo();
                $this->codeResponse(4012, "", '请求失败');
            }
            $redis->incr($unionLoginKey);
            if (empty($hourNum)) {
                $redis->expire($unionLoginKey, 3600);
            }
            $LoginToken = loadAction("LoginToken");
            $getToken = $LoginToken->setLoginToken($exId, $passid);
            if ($getToken !== false) {
                $memberModel = loadModel('member');
                $user = $memberModel->read($passid);
                $user["m_uid"] = unserialize($user['m_uid']);
                $uid = $user['m_uid']['1'];
                $pushArr = [
                    'passid'     => $passid,
                    'phone'      => $user['phone'],
                    'uid'        => $uid,
                    'extendData' => $extendData,
                ];
                $responseData = [
                    "extend"   => $LoginToken->pushLoginInfo($exId, $pushArr),
                    "response" => $LoginToken->returnResponseData($exId, ['passid' => $passid, 'uid' => $uid], false),
                    "token"    => $getToken,
                ];
                $this->codeResponse(200, $responseData, "登录成功");
            } else {
                $this->codeResponse(401, "", "登录失败！");
            }
        } else {
            $checkLogin->clearFInfo();
            $this->codeResponse(500, '', '授权失败');
        }
    }
}
