<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=gbk" />
<title>管理中心-2345网址导航高级版</title>

<link href="css/gaoji.css" rel="stylesheet" type="text/css" />
</head>

<body>
<!-- wrap -->
<div class="wrap">
<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr>
    <td width="200" class="nav_border">
	<div class="left_nav">
    {{include file="admin_gjb2345/menu.html"}}
    </div></td>
    <td><div class="right_cnt">
<style>
*,body{ font-size:12px; }
.mytable{ background-color:#2BA4E5; padding:0 0 0 0; text-align:left; padding:1px 1px 1px 1px;}
.mytable td {background-color:#FFF; padding:1px 1px 1px 1px;}
</style>
<script language="javascript" type="text/javascript" src="js/date/WdatePicker.js"></script> 
    <h4>意见列表</h4>
    <br />

	<div style="width:95%; margin-left:30px; border:1px solid #CCC;">
	<form method="get" >
	<input type="hidden" name="act" value="search" />
	<table width="95%" border="0" cellspacing="0" cellpadding="0">
   <tr>
    <td width="100%">
	<table width="100%" border="0" cellspacing="0" cellpadding="0">
    <tr>
		<td>状态：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" name="s_status" id="radio4" value="" checked="checked" />
		<label for="radio4">全部</label>&nbsp;<input type="radio" name="s_status" id="radio" value="none" {{if $pageArray.s_status eq 'none'}} checked="checked"{{/if}} />
		<label for="radio">未处理</label>&nbsp;<input type="radio" name="s_status" id="radio2" value="reply" {{if $pageArray.s_status eq 'reply'}} checked="checked"{{/if}} />
		<label for="radio2">已回复</label></td>
     </tr>
	 <tr>
	    <td>时间：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input class="Wdate" type="text" onClick="WdatePicker()" name="s_time_start" value="{{$pageArray.time_start}}">  ---  <input class="Wdate" type="text" onClick="WdatePicker()" name="s_time_end" value="{{$pageArray.time_end}}"></td>
	 </tr>
	 <tr>
	    <td>用户名：&nbsp;&nbsp;&nbsp;&nbsp;<input type="text" name="s_username" value="{{$pageArray.s_username}}" id="s_username"></td>
	 </tr>
      
     <tr>
	    <td>回复方式：<input type="radio" name="s_reType" id="radio5" value="" checked="checked" />
      <label for="radio5">全部</label>{{foreach item=item key=key from=$pageArray.type_list name=s_reType}}  
  <input type="radio" name="s_reType" id="s_reType_{{$key}}" value="{{$key}}" {{if $pageArray.s_reType eq $key}} checked="checked"{{/if}} />
      <label for="s_reType_{{$key}}">{{$item}}</label>&nbsp;{{/foreach}}</td>
	 </tr>
	 <tr>
	    <td><input type="submit" value="搜索"></td>
	 </tr>
    </table>
	</td>
  </tr>
</table>
</form>
<hr />
		<table class="mytable" width="100%" border="0" cellspacing="1" cellpadding="0">
		  <tr>
			<th>ID</th>
			<th>提交时间</th>
			<th>UID</th>
			<th>用户名</th>
			<th width="50%">意见内容</th>
			<th>回复方式</th>
			<th>状态</th>
			<th>操作</th>
		  </tr>
{{foreach item=item key=key from=$pageArray.list name=advise}}
		  <tr>
			<td>{{$item.id}}</td>
			<td>{{$item.cdate}}</td>
			<td>{{$item.uid}}</td>
			<td>{{$item.username}}</td>
			<td><a href="suggestion_show.php?id={{$item.id}}">{{$item.content}}</a></td>
			<td>{{$item.show_reType}}{{if $item.mail}}({{$item.mail}}){{/if}}{{if $item.tel}}({{$item.tel}}){{/if}}</td>
			<td>{{$item.show_status}}{{if $item.show_status=='已回复'}}({{$item.re_count}}条){{/if}}</td>
			<td><a href="?action=back&id={{$item.id}}">恢复</a>&nbsp;|&nbsp;<a href="?action=del&id={{$item.id}}">彻底删除</a></td>
		  </tr>
{{/foreach}}
		</table>
		<p> 共{{$pageArray.page.total}}条记录 {{$pageArray.page.nowPage}}/{{$pageArray.page.totalPage}} <a href="{{$pageArray.page.startPage}}">第一页</a>|<a href="{{$pageArray.page.backPage}}">上一页</a>| <a href="{{$pageArray.page.nextPage}}">下一页</a>| <a href="{{$pageArray.page.endPage}}">最后一页</a> </p>
	</div>
{{include file="admin_gjb2345/footer.html"}}