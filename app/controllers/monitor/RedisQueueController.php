<?php

/**
 * Class RedisQueueController
 */
class RedisQueueController extends Controller
{
    private $config;

    private $upValue = 1000;

    //用户数据redis
    private $userDataHashKey = 'dlx:memberInfo:shard';
    //注册手机号码redis
    private $regPhoneHashKey = REG_PHONE_INFO_REDIS_KEY;

    /**
     * RedisQueueController constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->config = Config::get('redis');
        $ip = get_client_ip();
        if (!\Service\Security\Server::checkCompanyIp($ip))
        {
            header('HTTP/1.1 404 Not Found');
            exit();
        }
    }

    /**
     * 比对 数据库中 与 redis中 用户数据长度差
     * -
     * @return void
     * <AUTHOR>
     */
    public function actionIndex()
    {
        $userDataRedis = $this->initUserDataRedis();
        if (empty($userDataRedis) || count($userDataRedis) < 3)
        {
            header('HTTP/1.1 404 Not Found');
            echo '用户数据redis连接失败';
            exit();
        }
        $everyRedisCount = array();
        $totalRedisLength = 0;
        foreach ($userDataRedis as $key => $uRedis)
        {
            $tmpLength = $uRedis->hLen($this->regPhoneHashKey);
            if (!$tmpLength)
            {
                header('HTTP/1.1 404 Not Found');
                echo '手机注册redis:' . $key . ', 没有获取到长度';
                $this->closeConnection($userDataRedis);
                exit();
            }
            array_push($everyRedisCount, $key . ':' . $tmpLength);
            $totalRedisLength += $tmpLength;

            $memory = $uRedis->info("MEMORY");
            // 10737418240 10G
            if (isset($memory["used_memory"]) && isset($memory["used_memory_human"]))
            {
                array_push($everyRedisCount, $key . ' used_memory:' . $memory["used_memory"] . ', used_memory_human:' . $memory["used_memory_human"]);
            }
        }
        //$memberModel = loadModel('member');
        $totalDBLength = 1000;
//        $totalDBLength = $memberModel->getMembersPhoneCount('');
//        if (!$totalDBLength)
//        {
//            header('HTTP/1.1 404 Not Found');
//            echo '手机注册获取数据库中长度出错';
//            $this->closeConnection($userDataRedis);
//            exit();
//        }

        $everyRedisInfo = implode("<br/>", $everyRedisCount);
        $diff = $totalDBLength - $totalRedisLength;
        if (false && $diff >= $this->upValue)
        {
            header('HTTP/1.1 404 Not Found');
            echo '手机注册DB:' . $totalDBLength . '; redis:' . $totalRedisLength . ';<br/>' .  $everyRedisInfo;
            $this->closeConnection($userDataRedis);
            exit();
        }
        echo '手机注册DB:' . $totalDBLength . '; redis:' . $totalRedisLength . ';<br/>' . $everyRedisInfo;
        $this->closeConnection($userDataRedis);
    }

    /**
     * closeConnection
     * -
     * @param array $userDataRedis UserDataRedis
     * @return void
     * <AUTHOR>
     */
    private function closeConnection(&$userDataRedis)
    {
        foreach ($userDataRedis as $key => $redisConnection)
        {
            if ($redisConnection)
            {
                $redisConnection->close();
            }
            unset($userDataRedis[$key]);
        }
    }


    /**
     * initUserDataRedis
     * -
     * @return array
     * <AUTHOR>
     */
    private function initUserDataRedis()
    {
        $userDataRedis = array();
        $dlxRedis = $this->config['dlx_members'];
        foreach ($dlxRedis as $key => $redisInfo)
        {
            if (!empty($redisInfo['master']))
            {
                $tmpRedis = new Redis();
                $tmpRedis->connect($redisInfo['master']['host'], $redisInfo['master']['port'], 3);
                $tmpRedis->auth($redisInfo['master']['auth']);
                if ($tmpRedis)
                {
                    $userDataRedis[$key] = $tmpRedis;
                }
            }
        }
        return $userDataRedis;
    }
}
