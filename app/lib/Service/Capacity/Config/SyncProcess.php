<?php
/**
 * Created by PhpStorm.
 * User: duhm
 * Date: 2019/4/16
 * Time: 14:51
 */

namespace Service\Capacity\Config;

class SyncProcess
{
    private static $maxRecord = 140000000;   //最大记录ID 1.3亿
    private static $spaceLimit = 5000000;   //500w 一切分


    /**
     * 获取最大记录
     * @return int
     */
    public static function getMaxRecord()
    {
        return self::$maxRecord;
    }

    /**
     * 拆分大小
     * @return int
     */
    public static function getSpaceLimit()
    {
        return self::$spaceLimit;
    }

    /**
     * 进程分队
     * @param int $spaceLimit 切分大小
     * @return array
     */
    public static function getProcessList($spaceLimit = 0)
    {
        if (empty($spaceLimit) || $spaceLimit == 0) {
            $spaceLimit = self::$spaceLimit;
        }
        $processArr = [];
        $processLIst = range(0, self::$maxRecord, $spaceLimit);
        foreach ($processLIst as $item) {
            if (self::$maxRecord <= $item) {
                continue;
            }
            if ($item + self::$spaceLimit > self::$maxRecord) {
                $processArr[] = $item . ":" . self::$maxRecord;
            } else {
                $processArr[] = $item . ":" . ($item + self::$spaceLimit);
            }
        }

        return $processArr;
    }
}
