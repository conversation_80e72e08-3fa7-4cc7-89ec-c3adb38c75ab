<?php
/**
 * Created by : PhpStorm
 * User: duhm
 * Date: 2020/4/27
 * Time: 14:55
 */

namespace Service\UserBase;


class Rules
{
    private static $redis = null;
    private static $avatarConfigInfo = [];    //头像配置的规则信息
    private static $nicknameConfigInfo = [];   //昵称配置的规则信息
    protected static $groupMidMapKey = 'login:groupMidMap';  // mid和头像生态名称关系存放key
    protected static $groupMidMapList = [];   //mid和头像生态名称关系列表


    /**
     * 获取redis链接
     * @return \redis|null
     */
    protected static function getRedis()
    {
        if (!is_object(self::$redis)) {
            self::$redis = \RedisEx::getInstance();
        }

        return self::$redis;
    }

    /**
     * 根据mid获取头像生态
     * @param string $mid 用户中心项目唯一标识
     * @return mixed
     */
    public static function getGroupNameByMid($mid)
    {
        if (empty(self::$groupMidMapList[$mid])) {
            self::$groupMidMapList[$mid] = self::getRedis()->hGet(self::$groupMidMapKey, $mid);
        }
        if (empty(self::$groupMidMapList[$mid])){
            return 'default';
        }

        return self::$groupMidMapList[$mid];
    }

    /**
     * 获取昵称规则配置
     * @param string $groupName 头像生态标识
     * @return string
     */
    public static function getNicknameConfigKey($groupName)
    {
        return "login:{$groupName}:nicknameConfig";
    }

    /**
     * 获取昵称规则配置
     * @param string $groupName 头像生态标识
     * @return string
     */
    public static function getAvatarConfigKey($groupName)
    {
        return "login:{$groupName}:headConfig";
    }

    /**
     * 获取头像规则
     * @param string $mid 用户中心项目唯一标识
     * @param string $key 配置key值
     * @return array|bool|mixed|string
     */
    public static function getAvatarRule($mid, $key = '')
    {
        if (empty(self::$avatarConfigInfo[$mid])) {
            $groupName = self::getGroupNameByMid($mid);
            if (empty($groupName)) {
                return '';
            }
            $configInfo = self::getRedis()->get(self::getAvatarConfigKey($groupName));
            if ($configInfo !== false) {
                self::$avatarConfigInfo[$mid] = json_decode($configInfo, true);
            } else {
                return '';
            }
        }
        if (!empty($key)) {
            return self::$avatarConfigInfo[$mid][$key] ?? '';
        } else {
            return self::$avatarConfigInfo[$mid];
        }
    }


    /**
     * 获取昵称规则
     * @param string $mid 用户中心项目唯一标识
     * @param string $key 配置key值
     * @return array|bool|mixed|string
     */
    public static function getNicknameRule($mid, $key = '')
    {
        if (empty(self::$nicknameConfigInfo[$mid])) {
            $groupName = self::getGroupNameByMid($mid);
            if (empty($groupName)) {
                return '';
            }
            $configInfo = self::getRedis()->get(self::getNicknameConfigKey($groupName));
            if ($configInfo !== false) {
                self::$nicknameConfigInfo[$mid] = json_decode($configInfo, true);
            } else {
                return '';
            }
        }
        if (!empty($key)) {
            return self::$nicknameConfigInfo[$mid][$key] ?? '';
        } else {
            return self::$nicknameConfigInfo[$mid];
        }
    }

    /**
     * 昵称规则校验
     * @param string $mid      项目标识
     * @param string $nickname 昵称
     * @param array  $msg      描述
     * @return bool
     * @throws \Exception
     */
    public static function checkNickname($mid, $nickname, &$msg = [])
    {
        //默认昵称, 不允许使用
        if (self::getCustomRules($mid)::notAllowUseDefaultNickname($nickname)) {
            $msg = TipsMap::ERRORNICKNAMUSEREDEFAULT;

            return false;
        }
        $nicknameConfig = self::getNicknameRule($mid); //获取昵称规则
        if (empty($nicknameConfig)) {
            //如果没有取到规则就默认校验失败
            $msg = TipsMap::ERRORNETNOTUSE1;

            return false;
        }
        $needFiledList = [
            'duplication',
            'specialChar',
            'allowMidSpace',
            'length',
        ];
        foreach ($needFiledList as $item) {
            if (!isset($nicknameConfig[$item])) {
                $msg = TipsMap::ERRORNETNOTUSE2;

                return false;
            }
        }
        list($minLen, $maxLen) = $nicknameConfig['length'];
        if (empty($minLen) || empty($maxLen)) {
            $msg = TipsMap::ERRORNETNOTUSE3;

            return false;
        }
        $strLen = mb_strlen($nickname, 'utf8');
        if ($strLen < $minLen) {
            TipsMap::$errorNicknameLenFailed['msg'] = "昵称长度过短，仅支持{$minLen}~{$maxLen}位字符";
            $msg = TipsMap::$errorNicknameLenFailed;

            return false;
        }
        if ($strLen > $maxLen) {
            TipsMap::$errorNicknameLenFailed['msg'] = "昵称长度过长，仅支持{$minLen}~{$maxLen}位字符";
            $msg = TipsMap::$errorNicknameLenFailed;

            return false;
        }
        //昵称中间是否允许有空格
        if (!$nicknameConfig['allowMidSpace']) {
            if (preg_match("/\s/", $nickname)) {
                $msg = TipsMap::ERRORNICKNAMENOTEMPTY;

                return false;
            }
        }
        //是否允许特殊字符
        if (!$nicknameConfig['specialChar']) {
            //不允许特殊字符
            if (!preg_match("/^[a-zA-Z0-9_\-\x{4e00}-\x{9fa5}]+$/u", $nickname)) {
                $msg = TipsMap::ERRORASSIGNRULESFAILE;

                return false;
            }
        }
        //审核昵称
        loadAction('ContentAudit');
        $nicknameAuditStatus = \ContentAuditAction::textAudit($mid, $nickname);
        if (!in_array($nicknameAuditStatus, [\ContentAuditAction::CONT_AUDIT_PASS])) {
            $msg = TipsMap::ERRORNICKNAMEAUDITFAILE;

            return false;
        }

        return true;
    }

    /**
     * 老昵称检查
     * @param string $mid      项目mid
     * @param string $nickname 昵称
     * @param array  $msg      错误描述
     * @return bool
     * @throws \Exception
     */
    public static function checkOldNickname($mid, $nickname, &$msg = [])
    {
        $strLen = mb_strlen($nickname, 'utf8');
        if ($strLen < 2) {
            TipsMap::$errorNicknameLenFailed['msg'] = "昵称长度过短，仅支持2~16位字符";
            $msg = TipsMap::$errorNicknameLenFailed;

            return false;
        }
        if ($strLen > 16) {
            TipsMap::$errorNicknameLenFailed['msg'] = "昵称长度过长，仅支持2~16位字符";
            $msg = TipsMap::$errorNicknameLenFailed;

            return false;
        }
        $regex = \Config::get('regex');
        if (preg_match($regex['username'], $nickname) || preg_match($regex['phone'], $nickname) || filter_var($nickname, FILTER_VALIDATE_EMAIL)) {
            $msg = TipsMap::ERRORNICKNAMUSEREDEFAULT;

            return false;
        }
        //审核昵称
        loadAction('ContentAudit');
        $nicknameAuditStatus = \ContentAuditAction::textAudit($mid, $nickname);
        if (!in_array($nicknameAuditStatus, [\ContentAuditAction::CONT_AUDIT_PASS])) {
            $msg = TipsMap::ERRORNICKNAMEAUDITFAILE;

            return false;
        }

        return true;
    }

    /**
     * 根据规则获取头像和昵称
     * @param string $mid            项目标识
     * @param int    $passId         passid
     * @param int    $showType       1、对自己 2、对外展示
     * @param array  $userInfo       用户信息
     * @param string $getType        空获取全部,avatar、nickname
     * @param string $size           默认big  big、middle、small
     * @param bool   $nicknameLoadDB 昵称是否入库
     * @return void
     * @throws \Exception
     */
    public static function getAvatarNicknameByRule($mid, $passId, $showType, $userInfo, $getType = '', $size = 'big', $nicknameLoadDB = false)
    {
        $isDefaultAvatar = false;
        $isDefaultNickname = false;
        $isWriteNickname = false;
        if (!empty($userInfo)) {
            $auditFirst = self::getAvatarRule($mid, 'auditFirst');
            if ($auditFirst === 0) {
                //先发后审  状态1和4
                //有待审核的返回待审核、没有待审核的，返回已审核的、没有已审核的返回默认的
                $avatarImg = $userInfo['in_approve_avatar_url'];
                if (empty($avatarImg)) {
                    $avatarImg = $userInfo['valid_avatar_url'];
                }
            } else {
                //先审后发  状态3和5
                if ($showType == 2) {
                    //对外的返回审核通过的头像
                    $avatarImg = $userInfo['valid_avatar_url'];
                } else {
                    //对自己的 有待审核的返回统一「审核中」头像
                    $avatarImg = $userInfo['in_approve_avatar_url'];
                    if (empty($avatarImg)) {
                        $avatarImg = $userInfo['valid_avatar_url'];
                    } else {
                        $avatarImg = 'approving.png';
                    }
                    if ($_GET['mid'] == 'JSQNLLQ' && $avatarImg == 'approving.png') {
                        //青鸟浏览器-头像审核中图片
                        $avatarImg = 'DEV_jsqnllq_1656493934_a02035.png';
                    }
                }
            }
            $nickname = $userInfo['nickname'];
        }
        if (empty($avatarImg)) {
            $avatarImg = Rules::getAvatarRule($mid, 'defaultImg') ?: "default.jpg";
            $isDefaultAvatar = true;
        }
        if (empty($nickname)) {
            //没有昵称的时候默认一个
            $nickname = self::generateDefaultNickname($mid, $passId);
            $isDefaultNickname = true;
            $isWriteNickname = true;
        } else {
            if (empty($userInfo['nickname_source'])) {
                if (self::getCustomRules($mid)::notAllowUseDefaultNickname($nickname)) {
                    $isDefaultNickname = true;
                }
            } else {
                if ($userInfo['nickname_source'] == 'default') {
                    $isDefaultNickname = true;
                }
            }
        }
        loadAction('ImageUpload');
        $outData = [];
        $expire = self::getAvatarRule($mid, 'expire');
        $expire = !empty($expire) ? $expire : 86000;
        switch ($getType) {
            case 'avatar':
                $outData = [
                    \ImageUploadAction::privateDownloadUrl($avatarImg, $mid, $size, $expire),
                    $isDefaultAvatar,
                ];
                break;
            case 'nickname':
                $outData = [
                    $nickname,
                    $isDefaultNickname,
                ];
                break;
            default:
                $outData = [
                    \ImageUploadAction::privateDownloadUrl($avatarImg, $mid, $size, $expire),
                    $nickname,
                    $isDefaultAvatar,
                    $isDefaultNickname,
                ];
        }
        if ($isDefaultNickname && $isWriteNickname && $nicknameLoadDB) {
            UserAvatarCache::pushNicknameUpdateList($mid, $passId, $nickname);
        }

        return $outData;
    }

    /**
     * 生成默认昵称
     * @param string $mid 项目标识
     * @return mixed
     */
    public static function generateDefaultNickname($mid, $passid)
    {
        $newNickname = self::getCustomRules($mid)::generateNickname($passid);

        return $newNickname;
    }

    /**
     * 获取自定义昵称规则
     * @param string $mid 项目mid
     * @return string
     */
    public static function getCustomRules($mid)
    {
        //不符合规则 则默认一个昵称
        $groupName = \Service\UserBase\Rules::getGroupNameByMid($mid);
        if (empty($groupName)) {
            $groupName = 'Default';
        }
        $nicknameRuleNamespace = '\\Service\\UserBase\\CustomRules\\' . ucfirst($groupName) . 'NicknameRule';
        if (!class_exists($nicknameRuleNamespace)) {
            $nicknameRuleNamespace = '\\Service\\UserBase\\CustomRules\\DefaultNicknameRule';
        }

        return $nicknameRuleNamespace;
    }

    /**
     * 缓存生态项目映射
     * @param string $group 生态
     * @param string $mid   项目
     * @return bool|false|int
     */
    public static function setGroupMidMap($group, $mid)
    {
        if (empty($group)) {
            return self::getRedis()->hDel(self::$groupMidMapKey, $mid);
        }

        return self::getRedis()->hSet(self::$groupMidMapKey, $mid, $group);
    }

    /**
     * 缓存生态配置信息
     * @param string $group 生态
     * @param array  $rules 规则
     * @return bool
     */
    public static function setGroupConfig($group, $rules)
    {
        $avatarRet = self::getRedis()->set(
            self::getAvatarConfigKey($group),
            json_encode(
                [
                    'fromTriplicate' => $rules['fromTriplicate'],
                    'auditFirst'     => $rules['auditFirst'],
                    'defaultImg'     => $rules['defaultImg'],
                    'expire'         => (int)$rules['expire'],
                ]
            )
        );

        $nickNameRet = self::getRedis()->set(
            self::getNicknameConfigKey($group),
            json_encode(
                [
                    'tips'          => $rules['tips'],
                    'duplication'   => $rules['duplication'],
                    'specialChar'   => $rules['specialChar'],
                    'allowMidSpace' => $rules['allowMidSpace'],
                    'length'        => json_decode($rules['length'], true),
                ]
            )
        );

        return $avatarRet && $nickNameRet;
    }

    /**
     * @param string $validAvatarUrl     审核后的图片
     * @param string $inApproveAvatarUrl 审核中图片
     * @param string $approveStatus      当前状态
     * @return string
     */
    public static function getAvatarStatus($validAvatarUrl, $inApproveAvatarUrl, $approveStatus)
    {
        $data = [
            'valid_avatar_url'      => $validAvatarUrl,
            'in_approve_avatar_url' => $inApproveAvatarUrl,
            'approve_status'        => $approveStatus,
        ];

        return self::getAvatarStatusMap($data);
    }

    /**
     * 头像状态码映射
     * 1.tmp中的状态码说明（定义在ImageUploadAction）：0-请求失败 1-待审核 2-不通过 3-通过 4-人工审核 5-未审核 6-文件检查失败 7-mid检查失败 8-上传失败
     * 2.需求中的状态码说明：0-无头像 1-仅有待审核 2-无头像，且审核不过 3-仅有一张 4-有两张 5-有一张，另一张状态为不通过
     * @param $data
     * @return string
     */
    public static function getAvatarStatusMap($data)
    {
        if (!isset($data['valid_avatar_url']) || !isset($data['in_approve_avatar_url']) || !isset($data['approve_status'])) {
            return 999;
        }
        loadAction('ImageUpload');
        if (!empty($data['valid_avatar_url'])) {
            if (empty($data['in_approve_avatar_url'])) {
                if (\ImageUploadAction::IMG_AUDIT_BLOCK === (int)$data['approve_status']) {
                    return 5;
                } else {
                    return 3;
                }
            } else {
                if (!empty($data['in_approve_avatar_url'])) {
                    return 4;
                }
            }
        } else {
            if (empty($data['in_approve_avatar_url'])) {
                if (\ImageUploadAction::IMG_AUDIT_BLOCK === (int)$data['approve_status']) {
                    return 2;
                } else {
                    return 0;
                }
            } else {
                if (!empty($data['in_approve_avatar_url'])) {
                    return 1;
                } else {
                    return 0;
                }
            }
        }
    }
}