<div class="mainWrap">

    <div class="container-fluid">
        <div class="row">

            <div id="condition">
                <select id="type" class="form-control">
                    <option value="1">登录</option>
                    <option selected="selected" value="2">注册</option>
                </select>
                历史小时 <input id="hourAgo" value="0"/>
                小时间隔 <input id="hourOffset" value="1"/>
                3分钟间隔 <input id="minuteOffset" value="0"/>
                报警比例% <input id="rate" value="30"/>
                <button id="search">刷新</button>
                <br>
                验证码-重命名时间限制<input id="time12" value="0"/>
                验证码-提交时间限制<input id="time13" value="0"/>
            </div>

            <div class="col-md-offset-4 col-md-4">
                <div class="page-header text-center" id="no_data_msg" style="display:none">
                    <lable class="h1">暂无数据</lable>
                </div>
            </div>

            <div class="col-md-7">
                <div id="chart" style="height:600px;">

                </div>
            </div>

        </div>
    </div>

</div>

<script type="text/javascript" src="/js/lib/echartsV3/echarts.min.js"></script>

<script>
    var chart = echarts.init(document.getElementById('chart'));
    var option = {
        title: {
            text: '登录/注册每三分钟数报警',
            x: 'center',
            y: 0
        },
        grid: [
            {x: '7%', y: '10%', width: '38%', height: '34%'},
            {x2: '7%', y: '10%', width: '38%', height: '34%'},
            {x: '7%', y2: '10%', width: '38%', height: '34%'},
            {x2: '7%', y2: '10%', width: '38%', height: '34%'}
        ],
        tooltip: {
            formatter: 'Group {a}: ({c})'
        },
        xAxis: [
            {gridIndex: 0, min: 0, max: 20},
            {gridIndex: 1, min: 0, max: 20},
            {gridIndex: 2, min: 0, max: 20},
            {gridIndex: 3, min: 0, max: 20}
        ],
        yAxis: [
            {gridIndex: 0, min: 0, max: 15, name: 'game.2345.com'},
            {gridIndex: 1, min: 0, max: 15, name: '全部'},
            {gridIndex: 2, min: 0, max: 15, name: 'game.2345.com'},
            {gridIndex: 3, min: 0, max: 15, name: '全部'}
        ],
        series: [
            {
                name: '',
                type: 'scatter',
                xAxisIndex: 0,
                yAxisIndex: 0,
                data: [],
                markLine: {},
                markPoint: {
                    data: [{
                        name: "max",
                    }]
                }
            },
            {
                name: '',
                type: 'scatter',
                xAxisIndex: 1,
                yAxisIndex: 1,
                data: [],
                markLine: {},
                markPoint: {
                    data: [{
                        name: "max",
                    }]
                }
            },
            {
                name: '',
                type: 'scatter',
                xAxisIndex: 2,
                yAxisIndex: 2,
                data: [],
                markLine: {},
                markPoint: {
                    data: [{
                        name: "max",
                    }]
                }
            },
            {
                name: '',
                type: 'scatter',
                xAxisIndex: 3,
                yAxisIndex: 3,
                data: [],
                markLine: {},
                markPoint: {
                    data: [{
                        name: "max",
                    }]
                }
            }
        ]
    };

    draw();

    $("#search").click(function () {
        draw();
    });

    $("#type, #hourAgo, #hourOffset, #minuteOffset, #rate").change(function () {
        draw();
    });

    function draw() {
        var params = {
            "type": $("#type").val(),
            "hourAgo": $("#hourAgo").val(),
            "hourOffset": $("#hourOffset").val(),
            "minuteOffset": $("#minuteOffset").val(),
            "rate": $("#rate").val(),
            "time12": $("#time12").val(),
            "time13": $("#time13").val(),
        };
        var posting = $.post("/houtai_gl/account_stat_api/monitor/", params);
        posting.done(function (response) {
            if (response.code < 0) {
                alert(response.msg);
            } else {
                option.title.text = '登录/注册每三分钟数报警:' + response.data.info.startTime + " ~ " + response.data.info.endTime;
                for (var i = 0; i < response.data.data.length; i++) {
                    var data = response.data.data[i];
                    option.xAxis[i].min = data.xMin;
                    option.xAxis[i].max = data.xMax;
//                    option.xAxis[i].name = data.isWarning + "";

                    option.yAxis[i].min = data.yMin;
                    option.yAxis[i].max = data.yMax;
                    option.yAxis[i].name = data.source + " " + data.status;
                    option.series[i].name = i;
                    option.series[i].data = data.data;

                    option.series[i].markLine = {
                        animation: false,
                        label: {
                            normal: {
                                textStyle: {
                                    align: 'right'
                                }
                            }
                        },
                        tooltip: {},
                        lineStyle: {
                            normal: {
                                type: 'solid'
                            }
                        },
                        data: [[{
                            coord: [0, 30],
                            symbol: 'none'
                        }, {
                            coord: [19, 100],
                            symbol: 'none'
                        }]]
                    };
                    option.series[i].markLine.data[0][0].coord = [data.xMin, data.xMin * data.slope + data.offset];
                    option.series[i].markLine.data[0][1].coord = [data.xMax, data.xMax * data.slope + data.offset];
                    option.series[i].markLine.tooltip.formatter = "y = " + data.slope.toFixed(2) + " * x + " + data.offset.toFixed(2) + " " + data.isWarning;
                    option.series[i].markLine.label.normal.formatter = "y = " + data.slope.toFixed(2) + " * x + " + data.offset.toFixed(2) + " " + data.isWarning;
                }
                chart.setOption(option);
            }
        }).always(function () {

        });
    }


</script>


<script type="text/javascript">

    url = "/houtai_gl/account_stat_api/monitor/";
    $('.navList a').each(function () {
        if ($(this).attr('href') == '/houtai_gl/account_stat/monitor/') {
            $(this).addClass('select');
        }
    });

</script>
